package com.yyigou.dsrp.cdc.client.customer.response;

import com.yyigou.ddc.common.service.annotation.EntityField;
import com.yyigou.ddc.common.service.annotation.MethodType;
import lombok.Data;

import java.io.Serializable;

@Data
public class CustomerComponentResponse implements Serializable {
    /**
     * 本企业编号（由scs平台分配）
     */
    @EntityField(name = "本企业编号（由scs平台分配）", stringValueTypeLength = 32)
    private String enterpriseNo;

    @EntityField(name = "企业名称", stringValueTypeLength = 32)
    private String enterpriseName;
    /**
     * 公司/企业编码（basic_company表)
     */
    @EntityField(name = "公司/企业编码（basic_company表)", stringValueTypeLength = 32)
    private String companyNo;
    /**
     * 公司/企业编码（basic_company表)
     */
    @EntityField(name = "公司/企业编码（basic_company表)", stringValueTypeLength = 32)
    private String companyName;
    /**
     * 客户主键
     */
    @EntityField(name = "客户主键", stringValueTypeLength = 32)
    private String customerNo;
    /**
     * 客户编码
     */
    @EntityField(name = "客户编码", stringValueTypeLength = 50)
    private String customerCode;


    /**
     * 客户名称
     */
    @EntityField(name = "客户名称", stringValueTypeLength = 128)
    private String customerName;
    /**
     * 客户外语名称
     */
    @EntityField(name = "客户外语名称", stringValueTypeLength = 300)
    private String customerNameEn;

    /**
     * 统一社会信用代码。公立医院的事业单位法人证号
     */
    @EntityField(name = "统一社会信用代码。公立医院的事业单位法人证号", stringValueTypeLength = 100)
    private String unifiedSocialCode;

    /**
     * 客户基本分类
     */
    @EntityField(name = "客户基本分类", stringValueTypeLength = 32)
    private String customerCategoryNo;
    /**
     * 客户基本分类名称
     */
    @EntityField(name = "客户基本分类名称", stringValueTypeLength = 32)
    private String customerCategoryName;

    /**
     * 助记码
     */
    @EntityField(name = "助记码", stringValueTypeLength = 100)
    private String mnemonicCode;

    /**
     * 信用天数
     */
    @EntityField(name = "信用天数", requiredMetTypes = MethodType.ADD, stringValueTypeLength = 5)
    private Integer periodDays;


    /**
     * 管控状态 1：启用 2：停用
     */
    private String controlStatus;

    @EntityField(name = "纳税类别编码")
    private String taxCategory;
    /**
     * 价格分类编码
     */
    private String priceCategoryCode;

    /**
     * 价格分类名称
     */
    private String priceCategoryName;

    /**
     * 合作性质
     */
    @EntityField(name = "合作性质", stringValueTypeLength = 1, requiredMetTypes = {MethodType.UPDATE})
    private String cooperationMode;
    /**
     * 合作性质
     */
    @EntityField(name = "合作性质名称", stringValueTypeLength = 1, requiredMetTypes = {MethodType.UPDATE})
    private String cooperationModeName;

    /**
     * 业务性质
     */
    private String businessType;

    /**
     * 业务性质名称
     */
    private String businessTypeName;

    /**
     * 返回外部客户编号
     */
    private String omsCustomerNo;

    @EntityField(name = "联系人")
    private String linkMan;
    @EntityField(name = "联系电话")
    private String linkPhone;
    /**
     * 归属公司
     */
    private String ownerCompany;
    /**
     * 管控类型id
     */
    private Long controlId;

    /**
     * 管控类型名称
     */
    private String controlTypeName;


}
