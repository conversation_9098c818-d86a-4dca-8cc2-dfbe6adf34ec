package com.yyigou.dsrp.cdc.client.v2.customer.request;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class CustomerAssignDocExeRequest implements Serializable {
    private static final long serialVersionUID = 1L;
    
    @EntityField(name = "客户编码")
    private String customerCode;

    @EntityField(name = "使用组织编号列表")
    private List<String> useOrgNoList;
}
