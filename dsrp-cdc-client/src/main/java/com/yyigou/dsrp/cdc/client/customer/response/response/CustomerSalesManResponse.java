package com.yyigou.dsrp.cdc.client.customer.response.response;

import com.yyigou.ddc.common.service.annotation.EntityField;
import com.yyigou.ddc.common.service.annotation.MethodType;
import lombok.Data;

import java.io.Serializable;

@Data
public class CustomerSalesManResponse implements Serializable {
    @EntityField(name = "id")
    private Long id;
    /**
     * 部门编号
     */
    @EntityField(name = "部门编号", stringValueTypeLength = 32, requiredMetTypes = {MethodType.UPDATE})
    private String orgNo;
    /**
     * 部门名称
     */
    @EntityField(name = "部门名称", stringValueTypeLength = 100, requiredMetTypes = {MethodType.UPDATE})
    private String orgName;
    /**
     * 业务员编号
     */
    @EntityField(name = "业务员编号", stringValueTypeLength = 32, requiredMetTypes = {MethodType.UPDATE})
    private String salesManNo;
    /**
     *
     */
    @EntityField(name = "", stringValueTypeLength = 255, requiredMetTypes = {MethodType.UPDATE})
    private String salesManName;

    /**
     * 职位
     */
    @EntityField(name = "职位")
    private String post;


    /**
     * 默认订单专员  2:是 1：默认  0：否
     */
    @EntityField(name = "默认订单专员  2:是 1：默认  0：否 ", stringValueTypeLength = 255, requiredMetTypes = {MethodType.UPDATE})
    private Integer orderSpecialist;

    /**
     * 默认业务员  2:是 1：默认  0：否
     */
    @EntityField(name = "默认业务员  2:是 1：默认  0：否 ", stringValueTypeLength = 255, requiredMetTypes = {MethodType.UPDATE})
    private Integer isDefault;


    /**
     * 联系电话
     */
    @EntityField(name = "联系电话")
    private String mobile;

    /**
     * 部门编号
     */
    @EntityField(name = "部门编号", stringValueTypeLength = 32, requiredMetTypes = {MethodType.UPDATE})
    private String deptNo;
    /**
     * 部门名称
     */
    @EntityField(name = "部门名称", stringValueTypeLength = 100, requiredMetTypes = {MethodType.UPDATE})
    private String deptName;
}
