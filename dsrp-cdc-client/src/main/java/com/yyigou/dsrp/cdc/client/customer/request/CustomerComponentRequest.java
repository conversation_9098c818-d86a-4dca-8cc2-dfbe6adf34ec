package com.yyigou.dsrp.cdc.client.customer.request;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class CustomerComponentRequest implements Serializable {
    /**
     * 租户编号
     */
    private String enterpriseNo;

    /**
     * 客户编号
     */
    private String customerNo;
    /**
     * 客户编号精确查询
     */
    private List<String> customerNoList;

    /**
     * 客户编号精确查询
     */
    private List<String> notInCustomerNoList;
    /**
     * 客户编码
     */
    private String customerCode;
    /**
     * 客户编码集合
     */
    private List<String> customerCodeList;
    /**
     * 客户名称
     */
    private String customerName;
    /**
     * 客户名称集合
     */
    private List<String> customerNameList;
    /**
     * 模糊搜索关键字：客户名称
     */
    private String customerNameKeyword;
    /**
     * 模糊搜索关键字：客户编码/编号/名称模糊查询
     */
    private String customerKeywords;

    /**
     * 管控状态
     */
    private List<String> controlStatusList;

    /**
     * 统一社会信用代码。公立医院的事业单位法人证号
     */
    private String unifiedSocialCode;
    private String unifiedSocialCodeKeywords;
    private String ownerCompanyKeywords;
    private List<Long> controlIdList;
    private List<String> businessTypeList;
    private String linkManAndPhoneKeywords;
    /**
     * 公司编号
     */
    private List<String> companyNoList;
    /**
     * 合作关系
     */
    private List<String> cooperationModeList;
    /**
     * 价格体系
     */
    private List<String> priceCategoryCodeList;

    /**
     * 客户分类
     */
    private List<String> customerCategoryNoList;
    /**
     * oms客户编码
     */
    private List<String> omsCustomerNoList;

    @EntityField(
            name = "所在地区数组搜索"
    )
    private List<String> regionCodeList;
}
