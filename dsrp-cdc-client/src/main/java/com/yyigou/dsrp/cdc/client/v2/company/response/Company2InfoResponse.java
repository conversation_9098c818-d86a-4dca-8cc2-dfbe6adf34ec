package com.yyigou.dsrp.cdc.client.v2.company.response;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;

/**
 *
 **/
@Data
public class Company2InfoResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    @EntityField(name = "租户编号")
    private String enterpriseNo;

    @EntityField(name = "企业编号")
    private String companyNo;

    @EntityField(name = "企业编码，集团化统一编码")
    private String companyCode;

    @EntityField(name = "企业名称")
    private String companyName;

    @EntityField(name = "统一社会信用代码。公立医院的事业单位法人证号")
    private String unifiedSocialCode;

    @EntityField(name = "企业注册地域：1-境内，2-境外")
    private Integer factoryType;

    @EntityField(name = "企业注册地域名称")
    private String factoryTypeName;

    @EntityField(name = "国家/地区")
    private String country;

    @EntityField(name = "国家/地区")
    private String countryRegionId;

    @EntityField(name = "国家/地区")
    private String countryRegionName;

    @EntityField(name = "纳税类别编码")
    private String taxCategory;

    @EntityField(name = "纳税类别名称")
    private String taxCategoryName;

    @EntityField(name = "经济类型编码")
    private String economicType;

    @EntityField(name = "经济类型名称")
    private String economicTypeName;

    @EntityField(name = "是否关联企业：1-是，0-否")
    private Integer isAssociatedEnterprise;

    @EntityField(name = "是否关联企业描述,导出使用")
    private String isAssociatedEnterpriseName;

    @EntityField(name = "关联组织编号")
    private String associatedOrgNo;

    @EntityField(name = "关联组织编码")
    private String associatedOrgCode;

    @EntityField(name = "关联组织名称")
    private String associatedOrgName;

    @EntityField(name = "是否医疗机构")
    private Integer isMedicalInstitution;

    @EntityField(name = "是否医疗机构,导出使用")
    private String isMedicalInstitutionName;

    @EntityField(name = "机构类型：yy:医院,dsfsys:第三方实验室,dsftjzx:第三方体检中心,jkzx:疾控中心,xz:血站,zs:诊所,ylhly:养老护理院,jgqt:其他")
    private String institutionalType;

    @EntityField(name = "医院类型:yy：公立医院，mbyy:民营医院")
    private String hospitalType;

    @EntityField(name = "医院等级：0:无等级,1:一级甲等,2:一级乙等,3:一级丙等,4:二级甲等,5:二级乙等,6:二级丙等,7:三级特等,8:三级甲等,9:三级乙等,10:三级丙等")
    private Integer hospitalClass;

    @EntityField(name = "合作关系：customer-客户，supplier-供应商")
    private String partnership;

    @EntityField(name = "合作关系")
    private String partnershipText;

    @EntityField(name = "法人")
    private String legalPerson;

    @EntityField(name = "经营状态")
    private String businessStatus;

    @EntityField(name = "成立日期")
    private String establishmentDate;

    @EntityField(name = "营业期限开始时间")
    private String businessStartTime;

    @EntityField(name = "营业期限结束时间")
    private String businessEndTime;

    @EntityField(name = "是否长期,导出显示")
    private String businessEndTimeName;

    @EntityField(name = "是否长期：1-长期，0-否")
    private Integer businessLongTerm;

    @EntityField(name = "注册资金")
    private String registedCapital;

    @EntityField(name = "实缴资本")
    private String paidCapital;

    @EntityField(name = "类型")
    private String companyBusinessType;

    @EntityField(name = "所属行业")
    private String industry;

    @EntityField(name = "工商注册号")
    private String businessRegistNo;

    @EntityField(name = "组织机构代码")
    private String organizationNo;

    @EntityField(name = "纳税人识别号")
    private String taxpayerNo;

    @EntityField(name = "纳税人资质")
    private String taxpayerQualification;

    @EntityField(name = "是否上市 1：是 0 ：否")
    private Integer isListed;

    @EntityField(name = "核准日期")
    private String approvalDate;

    @EntityField(name = "登记机关")
    private String registrationAuthority;

    @EntityField(name = "所在区域编码")
    private String regionCode;

    @EntityField(name = "所在区域名称")
    private String regionName;

    @EntityField(name = "注册详细地址")
    private String address;

    @EntityField(name = "曾用名")
    private String lastName;

    @EntityField(name = "参保人数")
    private String insuredNumber;

    @EntityField(name = "web网站")
    private String webSite;

    @EntityField(name = "企业邮箱")
    private String email;

    @EntityField(name = "传真")
    private String fax;

    @EntityField(name = "经营范围")
    private String manageScope;


}
