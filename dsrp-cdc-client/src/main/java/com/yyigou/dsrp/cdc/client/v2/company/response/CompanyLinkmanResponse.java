package com.yyigou.dsrp.cdc.client.v2.company.response;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;

@Data
public class CompanyLinkmanResponse implements Serializable {
    private Long id;

    @EntityField(name = "联系人")
    private String linkman;

    @EntityField(name = "岗位/职务")
    private String position;

    @EntityField(name = "联系电话")
    private String mobilePhone;

    @EntityField(name = "固定电话")
    private String fixedPhone;

    @EntityField(name = "性别('male','secrecy','female')")
    private String sex;

    @EntityField(name = "邮箱")
    private String email;

    @EntityField(name = "qq")
    private String qq;

    @EntityField(name = "微信")
    private String wx;

    @EntityField(name = "状态：1-正常、0-作废")
    private Integer status;

    @EntityField(name = "是否默认 1：是 0：否")
    private Integer isDefault;
}
