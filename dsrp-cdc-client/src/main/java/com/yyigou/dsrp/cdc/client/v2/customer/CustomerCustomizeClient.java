package com.yyigou.dsrp.cdc.client.v2.customer;

import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.dsrp.cdc.client.v2.customer.request.CustomerCoordinationCreateRequest;
import com.yyigou.dsrp.cdc.client.v2.customer.response.CustomerCoordinationCreateResponse;

/**
 * @Description 客户档案定制化RPC接口
 * @Classname CustomerCustomizeClient
 * @Date 2025/3/18 18:12
 * @author: baoww
 */
public interface CustomerCustomizeClient {
    /**
     * 定制接口使用场景：1.客商关系建立时创建客户档案
     *
     * @param params
     * @return
     */
    CallResult<CustomerCoordinationCreateResponse> createCoordinationCustomer(CustomerCoordinationCreateRequest params);
}
