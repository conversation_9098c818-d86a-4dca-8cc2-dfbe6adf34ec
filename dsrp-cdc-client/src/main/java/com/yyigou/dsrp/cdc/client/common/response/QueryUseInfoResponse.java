package com.yyigou.dsrp.cdc.client.common.response;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;

/**
 * 详情页面的使用信息查询
 *
 * @author: fudj
 * @createTime: 2023-04-24  15:55
 * @version: 1.0
 */
@Data
public class QueryUseInfoResponse implements Serializable {
    @EntityField(name = "组织编码")
    private String orgNo;

    @EntityField(name = "组织编码")
    private String orgCode;

    @EntityField(name = "组织名称")
    private String orgName;

    @EntityField(name = "是否使用")
    private Integer useStatus;

    @EntityField(name = "绑定租户")
    private String bindingEnterpriseNo;

    @EntityField(name = "集团编码")
    private String groupEnterpriseNo;
}
