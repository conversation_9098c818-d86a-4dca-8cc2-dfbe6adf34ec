package com.yyigou.dsrp.cdc.client.company.response;

import lombok.Data;

import java.io.Serializable;

@Data
public class CompanyCertFindResponse implements Serializable {
    /**
     * 租户编号
     */
    private String enterpriseNo;

    /**
     * 公司编号
     */
    private String companyNo;
    /**
     * 证照类型
     */
    private String certType;

    /**
     * 证件代码
     */
    private String certCode;

    /**
     * 证件类型
     */
    private String certName;

    /**
     * 证照有效期始
     */
    private String startTime;


    /**
     * 证照有效期至
     */
    private String endTime;


    /**
     * 长期标记 1是长期 0非长期
     */
    private String longTerm;

    /**
     * 备注
     */
    private String remark;
}
