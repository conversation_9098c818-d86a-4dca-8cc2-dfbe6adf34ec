package com.yyigou.dsrp.cdc.client.v2.company;

import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.dsrp.cdc.client.company.response.CompanyLinkmanResponse;
import com.yyigou.dsrp.cdc.client.company.response.CompanyShippingAddressResponse;
import com.yyigou.dsrp.cdc.client.v2.company.request.*;
import com.yyigou.dsrp.cdc.client.v2.company.response.Company2InfoResponse;
import com.yyigou.dsrp.cdc.client.v2.company.response.CompanyAssociatedOrgResponse;
import com.yyigou.dsrp.cdc.client.v2.company.response.CompanyBasicResponse;
import com.yyigou.dsrp.cdc.client.v2.company.response.CompanyWithCertResponse;

import java.util.List;

public interface CompanyV2Client {


    CallResult<List<Company2InfoResponse>> findListByCompanyNoList(String enterpriseNo, List<String> companyNoList);


    CallResult<List<Company2InfoResponse>> findCompanyList(CompanyQueryListRequest params);


    CallResult<List<CompanyShippingAddressResponse>> getLinkAddressList(CompanyLinkRequest params);

    CallResult<List<CompanyLinkmanResponse>> getLinkmanList(CompanyLinkRequest params);

    /**
     * 通过组织查询企业相关信息
     *
     * @param params 组织请求参数
     * @return
     */
    CallResult<List<CompanyBasicResponse>> findCompanyBasicInfoByOrgList(CompanyAssociateOrgRequest params);

    CallResult<List<CompanyWithCertResponse>> findCompanyWithCertList(CompanyWithCertQueryRequest params);


    CallResult<List<CompanyAssociatedOrgResponse>> findCompanyAssociatedOrgList(CompanyAssociateOrgRequest params);

}
