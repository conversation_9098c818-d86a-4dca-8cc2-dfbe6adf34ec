package com.yyigou.dsrp.cdc.client.v2.supplier.request;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;

@Data
public class SupplierCodeOrgPairRequest implements Serializable {
    private static final long serialVersionUID = 5009195985397117121L;

    @EntityField(name = "使用组织编号")
    private String useOrgNo;

    @EntityField(name = "供应商编码")
    private String supplierCode;

}
