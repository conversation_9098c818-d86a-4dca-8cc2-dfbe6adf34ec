package com.yyigou.dsrp.cdc.client.v2.customer.request;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
public class CustomerAssignMdmExeRequest extends CustomerAssignExeRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    @EntityField(name = "使用组织编号-客户编码映射")
    private Map<String, String> orgNo2CustomerNoMap;
}
