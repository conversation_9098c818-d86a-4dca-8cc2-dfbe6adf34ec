package com.yyigou.dsrp.cdc.client.company;

import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.dsrp.cdc.client.company.response.CompanyLinkmanResponse;
import com.yyigou.dsrp.cdc.client.company.response.CompanyShippingAddressResponse;
import com.yyigou.dsrp.cdc.common.enums.LinkmanTypeEnum;

import java.util.List;

public interface CompanyExtendClient {

    CallResult<List<CompanyLinkmanResponse>> getLinkmanList(String enterpriseNo, LinkmanTypeEnum typeEnum, List<String> sourceList);

    CallResult<List<CompanyShippingAddressResponse>> getLinkAddressList(String enterpriseNo, LinkmanTypeEnum typeEnum, List<String> sourceList);
}
