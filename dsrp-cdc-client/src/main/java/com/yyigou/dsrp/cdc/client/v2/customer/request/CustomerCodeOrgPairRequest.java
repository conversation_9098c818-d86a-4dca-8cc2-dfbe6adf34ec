package com.yyigou.dsrp.cdc.client.v2.customer.request;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class CustomerCodeOrgPairRequest implements Serializable {
    private static final long serialVersionUID = 5009195985397117121L;

    @EntityField(name = "使用组织编号")
    private String useOrgNo;

    @EntityField(name = "客户编码")
    private String customerCode;

}
