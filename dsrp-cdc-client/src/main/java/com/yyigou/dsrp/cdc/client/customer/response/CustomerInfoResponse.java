package com.yyigou.dsrp.cdc.client.customer.response;

import com.yyigou.ddc.common.service.annotation.EntityField;
import com.yyigou.ddc.common.service.annotation.MethodType;
import com.yyigou.dsrp.cdc.client.company.response.CompanyBankResponse;
import com.yyigou.dsrp.cdc.client.company.response.CompanyLinkmanResponse;
import com.yyigou.dsrp.cdc.client.company.response.CompanyShippingAddressResponse;
import com.yyigou.dsrp.cdc.client.customer.response.response.CustomerInvoiceResponse;
import com.yyigou.dsrp.cdc.client.customer.response.response.CustomerSalesManResponse;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class CustomerInfoResponse implements Serializable {
    private static final long serialVersionUID = -3266519656702747946L;

    /**
     * 租户编号
     */
    private String enterpriseNo;
    /**
     * 客户编号
     */
    private String customerNo;
    /**
     * 客户编码
     */
    private String customerCode;

    /**
     * 客户名称
     */
    private String customerName;
    /**
     * 客户分类（基本信息）
     */
    private String customerCategoryNo;
    /**
     * 客户分类（基本信息）
     */
    private String customerCategoryName;


    /**
     * 公司/企业编码（basic_company表)
     */
    private String companyNo;


    /**
     * 公司/企业编码（basic_company表)
     */
    private String companyName;


    /**
     * 统一社会信用代码。公立医院的事业单位法人证号
     */
    private String unifiedSocialCode;
    /**
     * 简称（标准信息）
     */
    private String shortName;
    /**
     * 客户英文名称（标准信息）
     */
    private String customerNameEn;
    /**
     * 管控类型（标准信息）
     */
    private Long controlId;
    /**
     * 管控类型名称（标准信息）
     */
    private String controlTypeName;
    /**
     * 价格体系编码
     */
    private String priceCategoryCode;
    /**
     * 价格体系名称
     */
    private String priceCategoryName;


    /**
     * 助记码
     */
    @EntityField(name = "助记码", stringValueTypeLength = 100, requiredMetTypes = {MethodType.UPDATE})
    private String mnemonicCode;


    @EntityField(name = "归属公司")
    private String ownerCompany;

    /**
     * Gsp首营状态
     */
    @EntityField(name = "Gsp首营状态 0:无需首营，1：未首营 2：已首营")
    private Integer gspStatus;


    /**
     * 公司纳税类别
     */
    @EntityField(name = "纳税类别")
    private String taxCategory;
    private String taxCategoryName;


    @EntityField(name = "纳税类别")
    private List<String> enterpriseTypes;

    /**
     * 备注
     */
    @EntityField(name = "备注", stringValueTypeLength = 500, requiredMetTypes = {MethodType.UPDATE})
    private String remark;


    @EntityField(name = "管控状态")
    private String controlStatus;
    @EntityField(name = "管控状态名称")
    private String controlStatusName;

    /**
     * 合作性质
     */
    @EntityField(name = "合作性质", stringValueTypeLength = 1, requiredMetTypes = {MethodType.UPDATE})
    private String cooperationMode;

    /**
     * 合作性质
     */
    @EntityField(name = "合作性质名称", stringValueTypeLength = 1, requiredMetTypes = {MethodType.UPDATE})
    private String cooperationModeName;


    @EntityField(name = "客户性质")
    private String customerType;

    @EntityField(name = "客户性质名称")
    private String customerTypeName;

    @EntityField(name = "结算方式")
    private String settlementModes;

    @EntityField(name = "结算方式名称")
    private String settlementModesName;

    @EntityField(name = "信用额度")
    private String creditAmount;

    @EntityField(name = "信用期限")
    private String creditDates;

    @EntityField(name = "合作起始时间")
    private String coopStartTime;

    @EntityField(name = "合作结束时间")
    private String coopEndTime;

    /**
     * 机构类型：yy:医院,dsfsys:第三方实验室,dsftjzx:第三方体检中心,jkzx:疾控中心,xz:血站,zs:诊所,ylhly:养老护理院,jgqt:其他
     */
    @EntityField(name = "机构类型")
    private String institutionalType;

    /**
     * 医院类型：yy：公立医院，mbyy:民营医院
     */
    @EntityField(name = "医院类型")
    private String hospitalType;

    /**
     * 医院等级：0:无等级,1:一级甲等,2:一级乙等,3:一级丙等,4:二级甲等,5:二级乙等,6:二级丙等,7:三级特等,8:三级甲等,9:三级乙等,10:三级丙等
     */
    @EntityField(name = "医院等级")
    private Integer hospitalClass;

    @EntityField(name = "收款协议")
    private String paymentAgreement;
    @EntityField(name = "收款条件")
    private String paymentCondition;
    @EntityField(name = "交易币种")
    private String currency;
    @EntityField(name = "交易币种")
    private String currencyName;
    @EntityField(name = "国家/地区")
    private String country;
    @EntityField(name = "企业注册地域：1-境内，2-境外")
    private Integer factoryType;
    @EntityField(name = "经济类型")
    private String economicType;
    private String economicTypeName;

    /**
     * 业务性质
     */
    private String businessType;

    /**
     * 业务性质名称
     */
    private String businessTypeName;


    @EntityField(name = "是否关联企业：1-是，0-否")
    private Integer isAssociatedEnterprise;
    @EntityField(name = "关联组织编号,关联企业时选择")
    private String associatedOrgNo;
    @EntityField(name = "关联组织编码,关联企业时选择")
    private String associatedOrgCode;
    @EntityField(name = "关联组织名称,关联企业时选择")
    private String associatedOrgName;

    @EntityField(name = "是否医疗机构")
    private Integer isMedicalInstitution;

    @EntityField(name = "是否医疗机构")
    private String isMedicalInstitutionName;

    @EntityField(name = "是否散户")
    private Integer retailInvestors;
    @EntityField(name = "是否散户名称")
    private String retailInvestorsName;

    @EntityField(name = "交易类型(客户类型)")
    private String transactionType;
    @EntityField(name = "交易类型名称(客户类型)")
    private String transactionTypeName;


    @EntityField(name = "客户发票信息")
    private List<CustomerInvoiceResponse> invoiceList;
    @EntityField(name = "客户销售员信息")
    private List<CustomerSalesManResponse> salesManList;
    @EntityField(name = "客户联系人信息")
    private List<CompanyLinkmanResponse> linkmanList;
    @EntityField(name = "客户联系地址")
    private List<CompanyShippingAddressResponse> linkAddressList;
    @EntityField(name = "银行信息")
    private List<CompanyBankResponse> bankList;
    @EntityField(name = "业务区域集合")
    private List<AreaResponse> areaList;
}
