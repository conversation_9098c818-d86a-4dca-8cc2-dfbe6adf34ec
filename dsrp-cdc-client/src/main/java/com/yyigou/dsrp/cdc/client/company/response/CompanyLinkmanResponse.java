package com.yyigou.dsrp.cdc.client.company.response;

import com.yyigou.ddc.common.service.annotation.EntityField;
import com.yyigou.ddc.common.service.annotation.MethodType;
import lombok.Data;

import java.io.Serializable;

@Data
public class CompanyLinkmanResponse implements Serializable {
    private Long id;
    /**
     * 联系人
     */
    @EntityField(name = "联系人", requiredMetTypes = MethodType.ADD, stringValueTypeLength = 50)
    private String linkman;
    /**
     * 岗位/职务
     */
    @EntityField(name = "岗位/职务", requiredMetTypes = MethodType.ADD, stringValueTypeLength = 100)
    private String position;
    /**
     * 联系电话
     */
    @EntityField(name = "联系电话", requiredMetTypes = MethodType.ADD, stringValueTypeLength = 100)
    private String mobilePhone;
    /**
     * 固定电话
     */
    @EntityField(name = "固定电话", requiredMetTypes = MethodType.ADD, stringValueTypeLength = 100)
    private String fixedPhone;
    /**
     * 性别
     */
    @EntityField(name = "性别('male','secrecy','female')", requiredMetTypes = MethodType.ADD, stringValueTypeLength = 10)
    private String sex;
    /**
     * 邮箱
     */
    @EntityField(name = "邮箱", requiredMetTypes = MethodType.ADD, stringValueTypeLength = 100)
    private String email;
    /**
     *
     */
    @EntityField(name = "", requiredMetTypes = MethodType.ADD, stringValueTypeLength = 100)
    private String qq;
    /**
     *
     */
    @EntityField(name = "", requiredMetTypes = MethodType.ADD, stringValueTypeLength = 100)
    private String wx;

    /**
     * 状态：1-正常、0-作废
     */
    @EntityField(name = "状态：1-正常、0-作废", requiredMetTypes = MethodType.ADD, stringValueTypeLength = 0)
    private Integer status;
    /**
     * 是否默认
     */
    @EntityField(name = "是否默认 1：是 0：否")
    private Integer isDefault;


    @EntityField(name = "客户供应商no")
    private String sourceNo;
}
