package com.yyigou.dsrp.cdc.client.v2.supplier.response;

import com.yyigou.ddc.common.service.annotation.EntityField;
import com.yyigou.dsrp.cdc.client.v2.company.response.CompanyBasicResponse;
import lombok.Data;

import java.io.Serializable;

@Data
public class SupplierBaseResponse extends CompanyBasicResponse implements Serializable {
    private static final long serialVersionUID = -6413140533278133644L;

    @EntityField(name = "租户编号")
    private String enterpriseNo;

    @EntityField(name = "供应商编号")
    private String supplierNo;

    @EntityField(name = "供应商编码")
    private String supplierCode;

    @EntityField(name = "管理组织编号")
    private String manageOrgNo;

    @EntityField(name = "管理组织名称")
    private String manageOrgName;

    @EntityField(name = "使用组织编号")
    private String useOrgNo;

    @EntityField(name = "使用组织名称")
    private String useOrgName;

    @EntityField(name = "管控状态")
    private String controlStatus;

    @EntityField(name = "管控状态")
    private String controlStatusName;
}
