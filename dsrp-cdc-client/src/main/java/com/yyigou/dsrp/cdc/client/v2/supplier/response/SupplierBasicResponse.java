package com.yyigou.dsrp.cdc.client.v2.supplier.response;

import com.yyigou.ddc.common.service.annotation.EntityField;
import com.yyigou.dsrp.cdc.client.v2.company.response.CompanyBasicResponse;
import lombok.Data;

import java.io.Serializable;

/**
 * @Description 供应商基本信息响应类
 * @Classname SupplierBasicResponse
 * @Date 2025/3/18 16:39
 * @author: baoww
 */
@Data
public class SupplierBasicResponse extends CompanyBasicResponse implements Serializable {
    private static final long serialVersionUID = -6413140533278133644L;

    @EntityField(name = "管理组织编号")
    private String manageOrgNo;

    @EntityField(name = "管理组织名称")
    private String manageOrgName;

    @EntityField(name = "供应商编号")
    private String supplierNo;

    @EntityField(name = "供应商编码")
    private String supplierCode;

    @EntityField(name = "供应商名称")
    private String supplierName;

    @EntityField(name = "供应商英文名称")
    private String supplierNameEn;

    @EntityField(name = "供应商分类编号")
    private String supplierCategoryNo;

    @EntityField(name = "供应商分类名称")
    private String supplierCategoryName;

    @EntityField(name = "助记码")
    private String mnemonicCode;

    @EntityField(name = "供应商类型")
    private String transactionType;

    @EntityField(name = "供应商类型名称")
    private String transactionTypeName;

    @EntityField(name = "是否散户")
    private Integer retailInvestors;

    @EntityField(name = "是否散户名称")
    private String retailInvestorsName;

    @EntityField(name = "备注")
    private String remark;

    @EntityField(name = "业务状态：1:合格 2：草稿")
    private Integer businessFlag;
}
