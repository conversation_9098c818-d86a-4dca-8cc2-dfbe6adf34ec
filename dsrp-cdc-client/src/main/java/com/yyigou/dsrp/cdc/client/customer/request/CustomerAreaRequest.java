package com.yyigou.dsrp.cdc.client.customer.request;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class CustomerAreaRequest implements Serializable {
    /**
     * 租户编号 必填
     */
    private String enterpriseNo;

    /**
     * 客户编号精确查询 必填
     */
    private List<String> customerNoList;


    /**
     * 区域编号 非必填
     */
    @EntityField(name = "区域编号")
    private List<String> areaNoList;

    /**
     * 区域编码 非必填
     */
    @EntityField(name = "区域编码")
    private List<String> areaCodeList;


    /**
     * 是否默认 1：是 0：否 非必填
     */
    @EntityField(name = "否默认 1：是 0：否", stringValueTypeLength = 255)
    private Integer isDefault;
}
