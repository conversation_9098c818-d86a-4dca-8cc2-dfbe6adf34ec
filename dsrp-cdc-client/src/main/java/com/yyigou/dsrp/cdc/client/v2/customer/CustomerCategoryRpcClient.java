package com.yyigou.dsrp.cdc.client.v2.customer;

import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.dsrp.cdc.client.v2.customer.request.CustomerCategoryRequest;
import com.yyigou.dsrp.cdc.client.v2.customer.response.CustomerBizResponse;
import com.yyigou.dsrp.cdc.client.v2.customer.response.CustomerCategoryResponse;

import java.util.List;

public interface CustomerCategoryRpcClient {

    /**
     * 查询客户分类列表
     *
     * @param enterpriseNo
     * @param customerCategoryNoList
     * @return
     */
    CallResult<List<CustomerCategoryResponse>> selectCustomerCategoryListByNo(String enterpriseNo, List<String> customerCategoryNoList);

    /**
     * 查询客户分类列表
     *
     * @param request
     * @return
     */
    CallResult<List<CustomerCategoryResponse>> selectCustomerCategoryList(CustomerCategoryRequest request);

}
