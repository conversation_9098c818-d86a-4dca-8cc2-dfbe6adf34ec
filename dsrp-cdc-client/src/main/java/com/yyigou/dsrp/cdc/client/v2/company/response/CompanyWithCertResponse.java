package com.yyigou.dsrp.cdc.client.v2.company.response;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class CompanyWithCertResponse extends CompanyBasicResponse implements Serializable {
    private static final long serialVersionUID = -7480461646660397179L;

    private List<CompanyCertResponse> companyCertList;
}
