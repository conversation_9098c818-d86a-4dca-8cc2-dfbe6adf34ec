package com.yyigou.dsrp.cdc.client.v2.company.response;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Description 企业档案基本信息
 * @Classname CompanyBasicResponse
 * @Date 2025/3/18 16:43
 * @author: baoww
 */
@Data
public class CompanyBasicResponse implements Serializable {
    private static final long serialVersionUID = -7480461646660397179L;

    @EntityField(name = "租户编号")
    private String enterpriseNo;

    @EntityField(name = "企业编号")
    private String companyNo;

    @EntityField(name = "企业编码，集团化统一编码")
    private String companyCode;

    @EntityField(name = "企业名称")
    private String companyName;

    @EntityField(name = "统一社会信用代码。公立医院的事业单位法人证号")
    private String unifiedSocialCode;

    @EntityField(name = "企业注册地域：1-境内，2-境外")
    private Integer factoryType;

    @EntityField(name = "企业注册地域名称")
    private String factoryTypeName;

    @EntityField(name = "国家/地区")
    private String country;

    @EntityField(name = "国家/地区")
    private String countryRegionId;

    @EntityField(name = "国家/地区")
    private String countryRegionName;

    @EntityField(name = "纳税类别编码")
    private String taxCategory;

    @EntityField(name = "纳税类别名称")
    private String taxCategoryName;

    @EntityField(name = "经济类型")
    private String economicType;

    @EntityField(name = "经济类型名称")
    private String economicTypeName;

    @EntityField(name = "是否内部组织：1-是，0-否")
    private Integer isAssociatedEnterprise;

    @EntityField(name = "内部组织编号")
    private String associatedOrgNo;

    @EntityField(name = "内部组织编码")
    private String associatedOrgCode;

    @EntityField(name = "内部组织名称")
    private String associatedOrgName;



//---------------客户档案使用---------------
    @EntityField(name = "是否医疗机构")
    private Integer isMedicalInstitution;

    @EntityField(name = "是否医疗机构")
    private String isMedicalInstitutionName;

    @EntityField(name = "机构类型")
    private String institutionalType;

    @EntityField(name = "机构类型名称")
    private String institutionalTypeName;

    @EntityField(name = "医院类型")
    private String hospitalType;

    @EntityField(name = "医院等级")
    private Integer hospitalClass;



//---------------银行信息---------------
    private List<CompanyBankResponse> companyBankList;
}
