package com.yyigou.dsrp.cdc.client.v2.customer.request;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class CustomerCodeOrgPairListRequest implements Serializable {
    private static final long serialVersionUID = 5009195985397117121L;

    @EntityField(name = "租户编号")
    private String enterpriseNo;

    @EntityField(name = "客户编码")
    private List<CustomerCodeOrgPairRequest> customerCodeOrgPairList;

}
