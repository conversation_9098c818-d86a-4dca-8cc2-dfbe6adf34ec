package com.yyigou.dsrp.cdc.client.supplier.response;

import com.yyigou.ddc.common.service.annotation.EntityField;
import com.yyigou.ddc.common.service.annotation.MethodType;
import lombok.Data;

import java.io.Serializable;

@Data
public class SupplierComponentResponse implements Serializable {
    /**
     * 本企业编号（由scs平台分配）
     */
    @EntityField(name = "本企业编号（由scs平台分配）", stringValueTypeLength = 32)
    private String enterpriseNo;

    @EntityField(name = "企业名称", stringValueTypeLength = 32)
    private String enterpriseName;
    /**
     * 公司/企业编码（basic_company表)
     */
    @EntityField(name = "公司/企业编码（basic_company表)", stringValueTypeLength = 32)
    private String companyNo;
    /**
     * 公司/企业编码（basic_company表)
     */
    @EntityField(name = "公司/企业编码（basic_company表)", stringValueTypeLength = 32)
    private String companyName;
    /**
     * 供应商主键
     */
    @EntityField(name = "供应商主键", stringValueTypeLength = 32)
    private String supplierNo;
    /**
     * 供应商编码
     */
    @EntityField(name = "供应商编码", stringValueTypeLength = 50)
    private String supplierCode;


    /**
     * 供应商名称
     */
    @EntityField(name = "供应商名称", stringValueTypeLength = 128)
    private String supplierName;
    /**
     * 供应商外语名称
     */
    @EntityField(name = "供应商外语名称", stringValueTypeLength = 300)
    private String supplierNameEn;

    /**
     * 统一社会信用代码。公立医院的事业单位法人证号
     */
    @EntityField(name = "统一社会信用代码。公立医院的事业单位法人证号", stringValueTypeLength = 100)
    private String unifiedSocialCode;

    /**
     * 供应商基本分类
     */
    @EntityField(name = "供应商基本分类", stringValueTypeLength = 32)
    private String supplierCategoryNo;
    /**
     * 供应商基本分类名称
     */
    @EntityField(name = "供应商基本分类名称", stringValueTypeLength = 32)
    private String supplierCategoryName;

    /**
     * 助记码
     */
    @EntityField(name = "助记码", stringValueTypeLength = 100)
    private String mnemonicCode;

    /**
     * 信用天数
     */
    @EntityField(name = "信用天数", requiredMetTypes = MethodType.ADD, stringValueTypeLength = 5)
    private Integer periodDays;


    /**
     * 付款协议id(本地的id)
     */
    private Long paymentAgreementId;

    /**
     * 付款协议id(ys的id)
     */
    private String paymentAgreementYsId;

    /**
     * 付款协议编码
     */
    private String paymentAgreementCode;

    /**
     * 付款协议名称
     */
    private String paymentAgreementName;

    /**
     * 管控状态 1：启用 2：停用 3：冻结
     */
    private String controlStatus;
    @EntityField(name = "管控状态名称")
    private String controlStatusName;


    @EntityField(name = "纳税类别编码")
    private String taxCategory;
    /**
     * 信用额度
     */
    private String creditAmount;
    /**
     * 供应商类型
     */
    private String supplierType;

    /**
     * 供应商类型名称
     */
    private String supplierTypeName;

    /**
     * 合作性质
     */
    private String cooperationMode;
    private String cooperationModeName;
}
