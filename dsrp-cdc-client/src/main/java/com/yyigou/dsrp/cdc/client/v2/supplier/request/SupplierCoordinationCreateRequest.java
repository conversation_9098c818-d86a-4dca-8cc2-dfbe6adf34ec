package com.yyigou.dsrp.cdc.client.v2.supplier.request;

import lombok.Data;

import java.io.Serializable;

/**
 * @Description 客商关系建立新增供应商档案请求参数
 * @Classname SupplierCoordinationCreateRequest
 * @Date 2025/3/18 16:36
 * @author: baoww
 */
@Data
public class SupplierCoordinationCreateRequest implements Serializable {
    private static final long serialVersionUID = -3848494471943133188L;

    /**
     * 租户编号
     */
    private String enterpriseNo;

    /**
     * 组织
     */
    private String orgNo;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 统一社会信用代码
     */
    private String unifiedSocialCode;

    /**
     * oms供应商编号
     */
    private String omsSupplierNo;
}
