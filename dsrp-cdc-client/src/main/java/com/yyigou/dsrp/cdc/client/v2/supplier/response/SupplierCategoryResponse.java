package com.yyigou.dsrp.cdc.client.v2.supplier.response;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;

@Data
public class SupplierCategoryResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    @EntityField(name = "分类编码")
    private String no;

    @EntityField(name = "所属企业编号")
    private String enterpriseNo;

    @EntityField(name = "管理组织编号")
    private String manageOrgNo;

    @EntityField(name = "上级分类编号")
    private String parentNo;

    @EntityField(name = "助记码")
    private String mnemonicCode;

    @EntityField(name = "分类编码")
    private String categoryCode;

    @EntityField(name = "分类名称")
    private String categoryName;

    @EntityField(name = "层级")
    private Integer level;

    @EntityField(name = "顶级编号")
    private String topCategoryNo;

    @EntityField(name = "路径")
    private String path;

    @EntityField(name = "描述")
    private String remark;

    @EntityField(name = "状态：1正常 0无效 ")
    private String status;

    @EntityField(name = "排序")
    private Integer sortNum;

    @EntityField(name = "0:未删除，1已删除")
    private Integer deleted;


}
