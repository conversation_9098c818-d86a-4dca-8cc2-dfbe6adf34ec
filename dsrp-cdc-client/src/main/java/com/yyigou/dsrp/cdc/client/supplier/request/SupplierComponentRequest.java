package com.yyigou.dsrp.cdc.client.supplier.request;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class SupplierComponentRequest implements Serializable {

    /**
     * 租户编号
     */
    private String enterpriseNo;


    /**
     * 供应商编号
     */
    private String supplierNo;
    /**
     * 供应商编号精确查询
     */
    private List<String> supplierNoList;

    /**
     * 供应商编号精确查询
     */
    private List<String> notInSupplierNoList;
    /**
     * 供应商编码
     */
    private String supplierCode;
    /**
     * 供应商编码集合
     */
    private List<String> supplierCodeList;
    /**
     * 供应商编号
     */
    private String supplierName;
    /**
     * 模糊搜索关键字：供应商名称
     */
    private String supplierNameKeyword;
    /**
     * 模糊搜索关键字：供应商编码/编号/名称模糊查询
     */
    private String supplierKeywords;

    /**
     * 管控状态
     */
    private List<String> controlStatusList;
    /**
     * 企业编号
     */
    private List<String> companyNoList;
    /**
     * 企业编号
     */
    private String supplierCodeKeywords;
    /**
     * 供应商分类
     */
    private List<String> supplierCategoryNoList;

    /**
     * 统一社会信用代码。公立医院的事业单位法人证号
     */
    private String unifiedSocialCode;

    /**
     * 合作性质
     */
    private List<String> cooperationModeList;
}
