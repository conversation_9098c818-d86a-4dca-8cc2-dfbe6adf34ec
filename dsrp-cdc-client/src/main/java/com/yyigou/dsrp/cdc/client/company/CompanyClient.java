package com.yyigou.dsrp.cdc.client.company;

import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.dsrp.cdc.client.company.response.CompanyInfoResponse;

import java.util.List;

public interface CompanyClient {

    /**
     * 根据查询条件查询公司信息
     *
     * @param companyNoList
     */
    CallResult<List<CompanyInfoResponse>> findListByCompanyNoListIgnoringEnterpriseNo(List<String> companyNoList);


    CallResult<List<CompanyInfoResponse>> findListByCompanyNoList(String enterpriseNo, List<String> companyNoList);

    CallResult<List<CompanyInfoResponse>> findListByCompanyCodeList(String enterpriseNo, List<String> companyCodeList);


}
