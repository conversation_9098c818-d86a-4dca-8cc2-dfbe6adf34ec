package com.yyigou.dsrp.cdc.client.customer.response.response;

import com.yyigou.ddc.common.service.annotation.EntityField;
import com.yyigou.ddc.common.service.annotation.MethodType;
import lombok.Data;

import java.io.Serializable;

@Data
public class CustomerInvoiceResponse implements Serializable {
    @EntityField(name = "id")
    private Long id;
    @EntityField(name = "发票类型: 1增值税专用发票 2增值税普通发票 3.增值税电子专用发票 4.增值税电子普通发票", requiredMetTypes = MethodType.ADD, stringValueTypeLength = 1)
    private Integer type;
    private String typeName;

    /**
     * 纳税人识别号
     */
    @EntityField(name = "纳税人识别号", requiredMetTypes = MethodType.ADD, stringValueTypeLength = 100)
    private String taxNo;
    /**
     * 开票抬头(企业名称)
     */
    @EntityField(name = "开票抬头(企业名称)", requiredMetTypes = MethodType.ADD, stringValueTypeLength = 128)
    private String invoiceTitle;
    /**
     * 开户银行
     */
    @EntityField(name = "开户银行", requiredMetTypes = MethodType.ADD, stringValueTypeLength = 255)
    private String bankDeposit;
    /**
     * 银行账号
     */
    @EntityField(name = "银行账号", requiredMetTypes = MethodType.ADD, stringValueTypeLength = 100)
    private String bankAccount;
    /**
     * 注册地址
     */
    @EntityField(name = "注册地址", requiredMetTypes = MethodType.ADD, stringValueTypeLength = 300)
    private String address;


    /**
     * 注册电话
     */
    @EntityField(name = "注册电话", requiredMetTypes = MethodType.ADD, stringValueTypeLength = 100)
    private String phone;

    /**
     * 电子邮箱
     */
    @EntityField(name = "收票邮箱", requiredMetTypes = MethodType.ADD, stringValueTypeLength = 100)
    private String email;


    @EntityField(name = "收票手机号", requiredMetTypes = MethodType.ADD, stringValueTypeLength = 100)
    private String invoicePhone;
    /**
     * 默认发票 1默认 0非默认
     */
    @EntityField(name = "默认发票 1默认 0非默认", requiredMetTypes = MethodType.ADD, stringValueTypeLength = 1)
    private Integer isDefault;

    /**
     * 要求
     */
    @EntityField(name = "客户开票要求", stringValueTypeLength = 500)
    private String requirement;


    @EntityField(name = "客户开票要求", stringValueTypeLength = 500)
    private Integer status;
}
