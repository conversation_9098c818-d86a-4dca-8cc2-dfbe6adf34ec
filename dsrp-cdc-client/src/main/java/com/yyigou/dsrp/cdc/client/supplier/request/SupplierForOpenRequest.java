package com.yyigou.dsrp.cdc.client.supplier.request;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class SupplierForOpenRequest implements Serializable {
    @EntityField(name = "租户编码")
    private String enterpriseNo;
    @EntityField(name = "最后同步时间")
    private String lastSyncTime;
    @EntityField(name = "供应商编码集合")
    private List<String> supplierCodeList;
}
