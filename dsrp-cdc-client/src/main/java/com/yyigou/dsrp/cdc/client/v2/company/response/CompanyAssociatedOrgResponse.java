package com.yyigou.dsrp.cdc.client.v2.company.response;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;

@Data
public class CompanyAssociatedOrgResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    @EntityField(name = "id")
    private Long id;

    /**
     * 所属租户编号
     */
    @EntityField(name = "所属租户编号")
    private String enterpriseNo;

    /**
     * 企业编号
     */
    @EntityField(name = "企业编号")
    private String companyNo;

    /**
     * 来源类型：1-供应商，2-客户，3-组织
     */
    @EntityField(name = "来源类型：1-供应商，2-客户，3-组织")
    private Integer sourceType;

    /**
     * 来源编码
     */
    @EntityField(name = "来源编码")
    private String sourceCode;

    /**
     * 关联组织编号
     */
    @EntityField(name = "关联组织编号")
    private String associatedOrgNo;


    @EntityField(name = "关联组织编码")
    private String associatedOrgCode;

    @EntityField(name = "关联组织名称")
    private String associatedOrgName;


}
