package com.yyigou.dsrp.cdc.client.v2.customer;

import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.dsrp.cdc.client.v2.customer.response.CustomerApplyResponse;
import com.yyigou.dsrp.cdc.client.v2.customer.response.CustomerBizResponse;

public interface CustomerApplyRpcClient {

    /**
     * 查询申请单详情
     *
     * @param enterpriseNo
     * @param applyInstanceNo
     * @return
     */
    CallResult<CustomerApplyResponse> getCustomerApplyByNo(String enterpriseNo, String applyInstanceNo);


}
