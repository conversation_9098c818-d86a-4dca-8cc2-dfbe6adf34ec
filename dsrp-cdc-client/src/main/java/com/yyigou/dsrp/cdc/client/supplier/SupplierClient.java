package com.yyigou.dsrp.cdc.client.supplier;

import com.yyigou.ddc.common.page.PageDto;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.dsrp.cdc.client.common.response.QueryUseInfoResponse;
import com.yyigou.dsrp.cdc.client.supplier.request.SupplierComponentRequest;
import com.yyigou.dsrp.cdc.client.supplier.request.SupplierFindRequest;
import com.yyigou.dsrp.cdc.client.supplier.request.SupplierNameRequest;
import com.yyigou.dsrp.cdc.client.supplier.request.SupplierNoRequest;
import com.yyigou.dsrp.cdc.client.supplier.response.SupplierComponentResponse;
import com.yyigou.dsrp.cdc.client.supplier.response.SupplierInfoResponse;
import com.yyigou.dsrp.cdc.client.supplier.response.SupplierResponse;

import java.util.List;

public interface SupplierClient {
    /**
     * 根据供应商名称查询供应商信息
     *
     * @param request
     * @return: {@link CallResult <  List <  SupplierInfoResponse >>}
     */
    CallResult<List<SupplierInfoResponse>> findSupplierByName(SupplierNameRequest request);

    /**
     * 根据供应商编号集合查询供应商信息
     *
     * @param request
     * @return: {@link CallResult< List< SupplierInfoResponse>>}
     */
    CallResult<List<SupplierInfoResponse>> findSupplierByNo(SupplierNoRequest request);


    /**
     * 查询供应商信息
     *
     * @param enterpriseNo
     * @param request
     * @return
     */
    CallResult<List<SupplierResponse>> findSupplier(String enterpriseNo, SupplierFindRequest request);

    /**
     * 查询集团租户供应商引用信息
     *
     * @param enterpriseNo
     * @param supplierCode
     * @return: {@link CallResult< List<  String >>}
     */
    CallResult<List<String>> findSupplierUserList(String enterpriseNo, String supplierCode);


    /**
     * 处理供应商新增申请
     *
     * @param applyJson
     * @return
     */
    CallResult<String> handleSupplierApply(String applyJson);

    /**
     * 分页查询供应商
     *
     * @param params
     * @return
     */
    CallResult<PageVo<SupplierComponentResponse>> selectSupplierPageForCommonComponent(SupplierComponentRequest params, PageDto pageDto);

    /**
     * 按指定条件查询供应商
     *
     * @param params
     * @return
     */
    CallResult<List<SupplierComponentResponse>> selectSupplierListForCommonComponent(SupplierComponentRequest params);

    /**
     * 查询供应商详情
     *
     * @param enterpriseNo 租户编号
     * @param supplierNo   供应商no
     * @return
     */
    CallResult<SupplierInfoResponse> selectSupplierInfo(String enterpriseNo, String supplierNo);


    /**
     * 查询供应商使用信息
     *
     * @param enterpriseNo 租户编号
     * @param supplierCode 供应商no
     * @return
     */
    CallResult<List<QueryUseInfoResponse>> queryUseInfo(String enterpriseNo, String supplierCode);
}
