package com.yyigou.dsrp.cdc.client.company.response;

import com.yyigou.ddc.common.service.annotation.Entity;
import com.yyigou.ddc.common.service.annotation.EntityField;
import com.yyigou.ddc.common.service.annotation.MethodType;
import lombok.Data;

import java.io.Serializable;

/**
 * CompanyShippingAddress
 * <p>
 * 收货地址表
 */
@Data
@Entity(name = "联系地址")
public class CompanyShippingAddressResponse implements Serializable {
    /**
     * 主键
     */
    @EntityField(name = "主键", requiredMetTypes = MethodType.ADD, stringValueTypeLength = 20)
    private Long id;
    /**
     * 收货人
     */
    @EntityField(name = "收货人", requiredMetTypes = MethodType.ADD, stringValueTypeLength = 50)
    private String receiveUser;
    /**
     * 联系电话
     */
    @EntityField(name = "联系电话", requiredMetTypes = MethodType.ADD, stringValueTypeLength = 100)
    private String receivePhone;
    /**
     * 行政地区编码
     */
    @EntityField(name = "行政地区编码", requiredMetTypes = MethodType.ADD, stringValueTypeLength = 20)
    private String regionCode;
    /**
     * 行政地区名称
     */
    @EntityField(name = "行政地区名称", requiredMetTypes = MethodType.ADD, stringValueTypeLength = 100)
    private String regionName;

    @EntityField(name = "行政地区名全称", requiredMetTypes = MethodType.ADD, stringValueTypeLength = 100)
    private String regionFullName;
    /**
     * 收货详细地址
     */
    @EntityField(name = "收货详细地址", requiredMetTypes = MethodType.ADD, stringValueTypeLength = 300)
    private String receiveAddr;
    /**
     * 电子邮箱
     */
    @EntityField(name = "电子邮箱", requiredMetTypes = MethodType.ADD, stringValueTypeLength = 100)
    private String email;
    /**
     * 收货地址状态:1默认 0非默认
     */
    @EntityField(name = "收货地址状态:1默认 0非默认", requiredMetTypes = MethodType.ADD, stringValueTypeLength = 4)
    private Integer isDefault;
    /**
     * 地址类型：1联系地址 2：收票地址 3:收货地址
     */
    @EntityField(name = "地址类型：1-联系地址，2-收票地址，3-客户收货地址", requiredMetTypes = MethodType.ADD, stringValueTypeLength = 1)
    private String addressType;
    @EntityField(name = "地址描述", requiredMetTypes = MethodType.ADD, stringValueTypeLength = 1)
    private String addressDesc;
    @EntityField(name = "客户供应商no")
    private String sourceNo;
}