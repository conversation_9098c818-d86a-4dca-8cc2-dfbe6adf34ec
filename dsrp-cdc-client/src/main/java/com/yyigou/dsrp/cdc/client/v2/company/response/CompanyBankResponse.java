package com.yyigou.dsrp.cdc.client.v2.company.response;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;

@Data
public class CompanyBankResponse implements Serializable {
    @EntityField(name = "id")
    private Long id;

    @EntityField(name = "银行编码")
    private String bankCode;

    @EntityField(name = "银行类型")
    private String bankType;

    @EntityField(name = "银行类型名称")
    private String bankTypeName;

    @EntityField(name = "开户银行")
    private String openBank;

    @EntityField(name = "账号")
    private String accountNo;

    @EntityField(name = "账户名")
    private String accountName;

    @EntityField(name = "账户类型")
    private Integer accountType;

    @EntityField(name = "联系人")
    private String linkPerson;

    @EntityField(name = "联系电话")
    private String linkPhone;

    @EntityField(name = "状态")
    private Integer status;

    @EntityField(name = "是否默认")
    private Integer isDefault;

    @EntityField(name = "联系地址类型")
    private String linkaddType;

    @EntityField(name = "来源编号")
    private String sourceNo;

    @EntityField(name = "管理组织编号")
    private String manageOrgNo;

    @EntityField(name = "币种")
    private String currency;

    @EntityField(name = "币种名称")
    private String currencyName;
}
