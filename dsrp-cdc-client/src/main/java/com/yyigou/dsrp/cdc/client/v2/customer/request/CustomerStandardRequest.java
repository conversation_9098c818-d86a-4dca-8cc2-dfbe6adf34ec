package com.yyigou.dsrp.cdc.client.v2.customer.request;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.util.List;

/**
 * @Description 客户档案标准查询请求参数
 * @Classname CustomerStandardRequest
 * @Date 2025/3/18 16:42
 * @author: baoww
 */
@Data
public class CustomerStandardRequest extends CustomerBaseRequest {
    private static final long serialVersionUID = 5009195985397117121L;

    @EntityField(name = "客户编码/名称模糊查询")
    private String customerKeywords;

    @EntityField(name = "客户编号集合精确查询")
    private List<String> customerNoList;

    @EntityField(name = "客户编码集合精确查询")
    private List<String> customerCodeList;

    @EntityField(name = "客户名称")
    private String customerName;

    @EntityField(name = "客户名称集合")
    private List<String> customerNameList;

    @EntityField(name = "管控状态集合")
    private List<String> controlStatusList;

    @EntityField(name = "统一社会信用代码")
    private String unifiedSocialCode;

    @EntityField(name = "企业编号集合")
    private List<String> companyNoList;

    @EntityField(name = "客户联系人/联系电话模糊搜索")
    private String linkManAndPhoneKeywords;

    @EntityField(name = "合作性质编码集合")
    private List<String> cooperationModeList;

    @EntityField(name = "价格体系编码集合")
    private List<String> priceCategoryCodeList;

    @EntityField(name = "客户分类编码集合")
    private List<String> customerCategoryNoList;

    @EntityField(name = "对应内部组织列表")
    private List<String> associatedOrgNoList;
}
