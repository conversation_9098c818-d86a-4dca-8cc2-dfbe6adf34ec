package com.yyigou.dsrp.cdc.client.v2.supplier.response;

import lombok.Data;

import java.io.Serializable;

/**
 * @Description 客商关系建立新增供应商档案返回参数
 * @Classname SupplierCoordinationCreateResponse
 * @Date 2025/3/18 16:36
 * @author: baoww
 */
@Data
public class SupplierCoordinationCreateResponse implements Serializable {

    private static final long serialVersionUID = -5998722301019445037L;

    /**
     * 供应商编号
     */
    private String supplierNo;
    /**
     * 供应商编码
     */
    private String supplierCode;
    /**
     * 供应商名称
     */
    private String supplierName;
}
