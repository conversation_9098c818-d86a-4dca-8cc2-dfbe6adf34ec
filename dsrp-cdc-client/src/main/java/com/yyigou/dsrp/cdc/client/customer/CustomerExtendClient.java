package com.yyigou.dsrp.cdc.client.customer;

import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.dsrp.cdc.client.customer.request.CoordinationCustomerRequest;
import com.yyigou.dsrp.cdc.client.customer.request.CustomerAreaRequest;
import com.yyigou.dsrp.cdc.client.customer.response.AreaResponse;
import com.yyigou.dsrp.cdc.client.customer.response.CoordinationCustomerResponse;
import com.yyigou.dsrp.cdc.client.supplier.request.SupplierManQueryRequest;
import com.yyigou.dsrp.cdc.client.supplier.response.SupplierSalesManResponse;

import java.util.List;

/**
 * 客户扩展接口
 * 用于提供客户非标准接口
 */
public interface CustomerExtendClient {

    //废弃使用 querySupplierManList
    @Deprecated
    CallResult<List<SupplierSalesManResponse>> querySupplierMan(String enterpriseNo, String customerNo, String orgNo, String salesManNo, List<Integer> orderSpecialist);

    CallResult<List<SupplierSalesManResponse>> querySupplierManList(SupplierManQueryRequest params);


    /**
     * 查询协同客户
     *
     * @param params
     * @return
     */
    CallResult<List<CoordinationCustomerResponse>> queryCoordinationCustomer(CoordinationCustomerRequest params);


    CallResult<List<AreaResponse>> queryCustomerArea(CustomerAreaRequest params);
}
