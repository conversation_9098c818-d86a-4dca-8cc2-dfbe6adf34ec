package com.yyigou.dsrp.cdc.client.v2.supplier.request;

import lombok.Data;

import java.io.Serializable;

/**
 * @Description 供应商档案请求基类
 * @Classname SupplierBaseRequest
 * @Date 2025/3/18 16:36
 * @author: baoww
 */
@Data
public class SupplierBaseRequest implements Serializable {
    private static final long serialVersionUID = -3416700025452964676L;

    /**
     * 租户编号
     */
    private String enterpriseNo;
    /**
     * 管理组织编号
     */
    private String manageOrgNo;
    /**
     * 使用组织编号
     */
    private String useOrgNo;
    /**
     * 供应商编号
     */
    private String supplierNo;
    /**
     * 供应商编码
     */
    private String supplierCode;
}
