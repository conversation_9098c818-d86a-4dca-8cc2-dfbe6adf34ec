package com.yyigou.dsrp.cdc.client.v2.supplier;

import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.dsrp.cdc.client.v2.supplier.response.SupplierCategoryResponse;

import java.util.List;

public interface SupplierCategoryRpcClient {

    /**
     * 查询供应商分类列表
     *
     * @param enterpriseNo
     * @param supplierCategoryNoList
     * @return
     */
    CallResult<List<SupplierCategoryResponse>> selectSupplierCategoryListByNo(String enterpriseNo, List<String> supplierCategoryNoList);

}
