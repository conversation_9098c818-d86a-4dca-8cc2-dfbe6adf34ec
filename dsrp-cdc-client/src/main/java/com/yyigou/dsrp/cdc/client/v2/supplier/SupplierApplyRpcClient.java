package com.yyigou.dsrp.cdc.client.v2.supplier;

import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.dsrp.cdc.client.v2.supplier.response.SupplierApplyResponse;

public interface SupplierApplyRpcClient {

    /**
     * 查询申请单详情
     *
     * @param enterpriseNo
     * @param applyInstanceNo
     * @return
     */
    CallResult<SupplierApplyResponse> getSupplierApplyByNo(String enterpriseNo, String applyInstanceNo);


}
