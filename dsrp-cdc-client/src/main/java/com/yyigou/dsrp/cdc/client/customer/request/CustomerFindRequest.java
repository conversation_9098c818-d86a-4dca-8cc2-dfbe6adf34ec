package com.yyigou.dsrp.cdc.client.customer.request;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 描述：
 *
 * @author: myq
 * @date: 2023/8/29 16:18
 * @version: 1.0.0
 */
@Data
public class CustomerFindRequest implements Serializable {

    /**
     * 客户编号精确查询
     */
    private List<String> customerNoList;

    /**
     * 客户编号
     */
    private String customerName;
    /**
     * 模糊搜索关键字：客户名称
     */
    private String customerNameKeyword;
    /**
     * 模糊搜索关键字：客户编码/编号/名称模糊查询
     */
    private String customerKeywords;

    private String customerCode;
    private List<String> customerCodeList;


    @EntityField(name = "价格体系集合")
    private List<String> priceCategoryCodeList;
}
