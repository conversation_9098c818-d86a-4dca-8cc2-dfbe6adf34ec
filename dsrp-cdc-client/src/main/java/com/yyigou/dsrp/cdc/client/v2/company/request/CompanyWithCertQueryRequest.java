package com.yyigou.dsrp.cdc.client.v2.company.request;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class CompanyWithCertQueryRequest implements Serializable {
    private static final long serialVersionUID = -7567300956672119335L;

    @EntityField(name = "租户编号")
    private String enterpriseNo;

    @EntityField(name = "企业编号列表")
    private List<String> companyNoList;
}
