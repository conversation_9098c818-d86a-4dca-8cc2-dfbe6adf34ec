package com.yyigou.dsrp.cdc.client.v2.supplier.request;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;

@Data
public class SupplierGspStatusRequest implements Serializable {
    private static final long serialVersionUID = 7651163492063573672L;

    @EntityField(name = "企业编号")
    private String enterpriseNo;

    @EntityField(name = "使用组织编号")
    private String useOrgNo;

    @EntityField(name = "供应商编号")
    private String supplierNo;

    @EntityField(name = "供应商编码")
    private String supplierCode;
}
