package com.yyigou.dsrp.cdc.client.supplier.response;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class SupplierCertForOpenResponse implements Serializable {

    private String certType;//证件类型: 0三证合一 1营业执照 2生产许可证 3生产备案证(税务等级证) 4组织机构代码证

    private String certCode;//证书编号

    private String certName;//证书名称

    private String certBeginDate;//证书开始时间

    private String certEndDate;//证书结束时间

    private String longTerm;//是否长期  1长期 0无

    private String imgStrs;

    private List<String> certImages;//证书图片地址
}
