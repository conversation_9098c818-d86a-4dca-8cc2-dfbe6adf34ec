package com.yyigou.dsrp.cdc.client.v2.customer.request;

import lombok.Data;

import java.io.Serializable;

/**
 * @Description 客户档案请求基类
 * @Classname CustomerBaseRequest
 * @Date 2025/3/18 16:36
 * @author: baoww
 */
@Data
public class CustomerBaseRequest implements Serializable {

    private static final long serialVersionUID = 7651163492063573672L;
    /**
     * 租户编号
     */
    private String enterpriseNo;
    /**
     * 管理组织编号
     */
    private String manageOrgNo;
    /**
     * 使用组织编号
     */
    private String useOrgNo;
    /**
     * 客户编号
     */
    private String customerNo;
    /**
     * 客户编码
     */
    private String customerCode;
}
