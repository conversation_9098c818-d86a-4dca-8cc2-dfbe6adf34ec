package com.yyigou.dsrp.cdc.client.v2.customer.response;

import com.yyigou.ddc.common.service.annotation.EntityField;
import com.yyigou.dsrp.cdc.client.v2.company.response.CompanyLinkmanResponse;
import com.yyigou.dsrp.cdc.client.v2.company.response.CompanyShippingAddressResponse;
import lombok.Data;

import java.util.List;

/**
 * @Description 客户业务信息
 * @Classname CustomerBizResponse
 * @Date 2025/3/18 16:39
 * @author: baoww
 */
@Data
public class CustomerBizResponse extends CustomerBasicResponse {
    // ------------业务信息------------
    @EntityField(name = "bizId")
    private Long bizId;

    @EntityField(name = "使用组织编号")
    private String useOrgNo;

    @EntityField(name = "使用组织名称")
    private String useOrgName;

    @EntityField(name = "使用组织修改标志")
    private Integer useOrgModifyFlag; //使用组织修改标志 0：未修改 1：已修改

    @EntityField(name = "合作性质")
    private String cooperationMode;

    @EntityField(name = "合作性质名称")
    private String cooperationModeName;

    @EntityField(name = "客户性质")
    private String businessType;

    @EntityField(name = "客户性质名称")
    private String businessTypeName;

    @EntityField(name = "价格体系编码")
    private String priceCategoryCode;

    @EntityField(name = "价格体系名称")
    private String priceCategoryName;

    @EntityField(name = "币种")
    private String currency;

    @EntityField(name = "币种名称")
    private String currencyName;

    @EntityField(name = "结算方式")
    private String settlementModes;

    @EntityField(name = "结算方式名称")
    private String settlementModesName;

    @EntityField(name = "收款协议")
    private String receiveAgreement;

    @EntityField(name = "收款条件")
    private String receiveCondition;

    @EntityField(name = "信用额度")
    private String creditAmount;

    @EntityField(name = "信用期限")
    private String creditDates;

    @EntityField(name = "合作开始时间")
    private String coopStartTime;

    @EntityField(name = "合作结束时间")
    private String coopEndTime;

    @EntityField(name = "业务归属")
    private String ownerCompany;

    @EntityField(name="是否GSP")
    private Integer isGspControl;


    // ------------供应商首营信息------------
    @EntityField(name = "GSP首营状态")
    private Integer gspAuditStatus;


    // ------------客户联系人信息------------
    @EntityField(name = "客户联系人信息")
    private List<CompanyLinkmanResponse> companyLinkmanList;

    // ------------客户联系地址信息------------
    @EntityField(name = "客户联系地址信息")
    private List<CompanyShippingAddressResponse> companyShippingAddressList;

    // ------------客户负责人信息------------
    private List<CustomerSalesManResponse> customerSalesManList;
    
    // ------------客户开票信息------------
    private List<CustomerInvoiceResponse> customerInvoiceList;

    // 银行信息 @see com.yyigou.dsrp.cdc.client.v2.company.response.CompanyBasicResponse#companyBankList


    // ------------分派信息------------
    @EntityField(name = "管控启停状态")
    private String controlStatus;

    
}
