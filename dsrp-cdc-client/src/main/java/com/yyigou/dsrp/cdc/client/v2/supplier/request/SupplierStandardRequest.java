package com.yyigou.dsrp.cdc.client.v2.supplier.request;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.util.List;

/**
 * @Description 供应商标准查询请求参数
 * @Classname SupplierStandardRequest
 * @Date 2025/3/18 16:42
 * @author: baoww
 */
@Data
public class SupplierStandardRequest extends SupplierBaseRequest {
    private static final long serialVersionUID = 2415050931981227332L;

    @EntityField(name = "供应商编码/名称模糊查询")
    private String supplierKeywords;

    @EntityField(name = "供应商编号精确查询")
    private List<String> supplierNoList;

    @EntityField(name = "供应商编码精确查询")
    private List<String> supplierCodeList;

    @EntityField(name = "供应商名称")
    private String supplierName;

    @EntityField(name = "供应商名称集合")
    private List<String> supplierNameList;

    @EntityField(name = "管控状态集合")
    private List<String> controlStatusList;

    @EntityField(name = "企业编号集合")
    private List<String> companyNoList;

    @EntityField(name = "供应商分类编号集合")
    private List<String> supplierCategoryNoList;

    @EntityField(name = "统一社会信用代码")
    private String unifiedSocialCode;

    @EntityField(name = "合作性质编码集合")
    private List<String> cooperationModeList;
}
