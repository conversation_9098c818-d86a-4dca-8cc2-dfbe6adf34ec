package com.yyigou.dsrp.cdc.client.v2.customer.request;

import lombok.Data;

import java.io.Serializable;

/**
 * @Description 客商关系建立新增客户档案请求参数
 * @Classname CustomerCoordinationCreateRequest
 * @Date 2025/3/18 16:36
 * @author: baoww
 */
@Data
public class CustomerCoordinationCreateRequest implements Serializable {
    private static final long serialVersionUID = -8769013647174365671L;

    /**
     * 租户编号
     */
    private String enterpriseNo;

    /**
     * 组织
     */
    private String orgNo;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 统一社会信用代码
     */
    private String unifiedSocialCode;

    /**
     * oms客户编号
     */
    private String omsCustomerNo;
}
