package com.yyigou.dsrp.cdc.client.v2.customer;

import com.yyigou.ddc.common.page.PageDto;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.ddc.services.ddc.uap.domain.dto.grade.GradeDocAssignMessage;
import com.yyigou.ddc.services.ddc.uap.domain.vo.grade.GradeRpcAssignVO;
import com.yyigou.dsrp.cdc.client.customer.request.CustomerInvoiceQueryRequest;
import com.yyigou.dsrp.cdc.client.v2.customer.request.CustomerLinkManQueryRequest;
import com.yyigou.dsrp.cdc.client.customer.request.CustomerNameRequest;
import com.yyigou.dsrp.cdc.client.customer.request.CustomerNoRequest;
import com.yyigou.dsrp.cdc.client.v2.customer.request.CustomerCodeOrgPairListRequest;
import com.yyigou.dsrp.cdc.client.v2.customer.request.CustomerGspStatusRequest;
import com.yyigou.dsrp.cdc.client.v2.customer.request.CustomerStandardRequest;
import com.yyigou.dsrp.cdc.client.v2.customer.response.CompanyLinkManResponse;
import com.yyigou.dsrp.cdc.client.v2.customer.response.CustomerBasicResponse;
import com.yyigou.dsrp.cdc.client.v2.customer.response.CustomerBizResponse;
import com.yyigou.dsrp.cdc.client.v2.customer.response.CustomerInvoiceResponse;

import java.util.List;

/**
 * @Description 客户档案RPC接口
 * @Classname CustomerRpcClient
 * @Date 2025/3/18 17:43
 * @author: baoww
 */
public interface CustomerRpcClient {

    /**
     * 根据租户编号+组织编号+客户编号获取客户档案信息
     *
     * @param enterpriseNo
     * @param useOrgNo
     * @param customerNo
     * @return
     */
    CallResult<CustomerBizResponse> getCustomerBizInfoByCustomerNo(String enterpriseNo, String useOrgNo, String customerNo);

    /**
     * 根据租户编号+组织编号+客户编码获取客户档案信息
     *
     * @param enterpriseNo
     * @param useOrgNo
     * @param customerCode
     * @return
     */
    CallResult<CustomerBizResponse> getCustomerBizInfoByCustomerCode(String enterpriseNo, String useOrgNo, String customerCode);

    /**
     * 根据客户档案标准查询条件查询客户档案信息
     *
     * @param params
     * @return
     */
    CallResult<List<CustomerBizResponse>> selectCustomerBizInfoList(CustomerStandardRequest params);


    /**
     * 根据客户档案标准查询条件查询客户档案信息
     *
     * @param params
     * @return
     */
    CallResult<List<CustomerBizResponse>> selectCustomerBizByCodeOrgPair(CustomerCodeOrgPairListRequest params);

    /**
     * 根据客户档案标准查询条件分页查询客户档案信息
     *
     * @param params
     * @param pageDto
     * @return
     */
    CallResult<PageVo<CustomerBizResponse>> selectCustomerBizInfoListPage(CustomerStandardRequest params, PageDto pageDto);

    /**
     * 根据客户名称查询客户档案信息
     *
     * @param request
     * @return
     */
    CallResult<List<CustomerBizResponse>> selectCustomerBizInfoByName(CustomerNameRequest request);

    /**
     * 根据客户编号查询客户档案信息
     *
     * @param request
     * @return
     */
    CallResult<List<CustomerBizResponse>> selectCustomerBizInfoByNo(CustomerNoRequest request);


    /**
     * 分派客户档案
     * @param message
     * @return
     */
    CallResult<List<GradeRpcAssignVO>> assignCustomer(GradeDocAssignMessage message);

    /**
     * 客户档案首营状态变更
     * @param message
     * @return
     */
    CallResult<Boolean> changeGspStatus(CustomerGspStatusRequest message);

    /**
     * 根据客户编码查询开票信息列表
     * @param request
     * @return
     */
    CallResult<List<CustomerInvoiceResponse>> getInvoiceListByCustomerCode(CustomerInvoiceQueryRequest request);

    /**
     * 根据客户编码查询联系人信息列表
     * @param request
     * @return
     */
    CallResult<List<CompanyLinkManResponse>> getLinkManListByCustomerCode(CustomerLinkManQueryRequest request);

    /**
     * 根据租户编号+客户编码获取客户档案基本信息
     *
     * @param enterpriseNo
     * @param customerCode
     * @return
     */
    CallResult<CustomerBasicResponse> getCustomerBasicInfoByCustomerCode(String enterpriseNo, String customerCode);
}
