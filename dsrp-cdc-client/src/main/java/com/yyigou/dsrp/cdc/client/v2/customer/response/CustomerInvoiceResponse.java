package com.yyigou.dsrp.cdc.client.v2.customer.response;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;

@Data
public class CustomerInvoiceResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    @EntityField(name = "ID")
    private Long id;

    @EntityField(name = "发票种类")
    private Integer type;

    @EntityField(name = "发票种类名称")
    private String typeName;

    @EntityField(name = "纳税人识别号")
    private String taxNo;

    @EntityField(name = "开票抬头")
    private String invoiceTitle;

    @EntityField(name = "收票手机号")
    private String invoicePhone;

    @EntityField(name = "受票邮箱")
    private String email;

    @EntityField(name = "电话")
    private String phone;

    @EntityField(name = "地址")
    private String address;

    @EntityField(name = "开户行")
    private String bankDeposit;

    @EntityField(name = "开户账号")
    private String bankAccount;

    @EntityField(name = "是否默认")
    private Integer isDefault;

    @EntityField(name = "开票要求")
    private String requirement;
}