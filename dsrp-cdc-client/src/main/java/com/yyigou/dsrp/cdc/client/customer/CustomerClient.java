package com.yyigou.dsrp.cdc.client.customer;

import com.yyigou.ddc.common.page.PageDto;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.dsrp.cdc.client.common.response.QueryUseInfoResponse;
import com.yyigou.dsrp.cdc.client.customer.request.CustomerComponentRequest;
import com.yyigou.dsrp.cdc.client.customer.request.CustomerFindRequest;
import com.yyigou.dsrp.cdc.client.customer.request.CustomerNameRequest;
import com.yyigou.dsrp.cdc.client.customer.request.CustomerNoRequest;
import com.yyigou.dsrp.cdc.client.customer.request.CustomerPriceCateRequest;
import com.yyigou.dsrp.cdc.client.customer.response.CustomerComponentResponse;
import com.yyigou.dsrp.cdc.client.customer.response.CustomerInfoResponse;
import com.yyigou.dsrp.cdc.client.customer.response.CustomerResponse;
import com.yyigou.dsrp.cdc.client.customer.response.response.CompanyLinkmanResponse;
import com.yyigou.dsrp.cdc.client.customer.response.response.CustomerInvoiceResponse;

import java.util.List;

public interface CustomerClient {

    /**
     * 根据客户名称查询客户信息
     *
     * @param request
     * @return: {@link CallResult< List< CustomerInfoResponse>>}
     */
    CallResult<List<CustomerInfoResponse>> findCustomerByName(CustomerNameRequest request);

    /**
     * 根据客户编号集合查询客户信息
     *
     * @param request
     * @return: {@link CallResult< List< CustomerInfoResponse>>}
     */
    CallResult<List<CustomerInfoResponse>> findCustomerByNo(CustomerNoRequest request);


    /**
     * 根据价格体系查询客户信息
     *
     * @param request
     * @return: {@link CallResult< List< CustomerInfoResponse>>}
     */
    CallResult<List<CustomerInfoResponse>> findCustomerByPriceCate(CustomerPriceCateRequest request);


    /**
     * 条件查询
     *
     * @param request
     * @param enterpriseNo
     * @return
     */
    CallResult<List<CustomerResponse>> findCustomer(String enterpriseNo, CustomerFindRequest request);

    /**
     * 查询集团租户客户引用信息
     *
     * @param enterpriseNo
     * @param customerCode
     * @return: {@link CallResult< List<  String >>}
     */
    CallResult<List<String>> findCustomerUserList(String enterpriseNo, String customerCode);


    /**
     * 处理客户新增申请
     *
     * @param applyJson
     * @return
     */
    CallResult<String> handleCustomerApply(String applyJson);


    CallResult<List<CompanyLinkmanResponse>> getLinkmanListByCustomerNo(String enterpriseNo, String customerNo);

    CallResult<List<CompanyLinkmanResponse>> getLinkmanListByCustomerNoList(String enterpriseNo, List<String> customerNoList);


    /**
     * 分页查询客户
     *
     * @param params
     * @return
     */
    CallResult<PageVo<CustomerComponentResponse>> selectCustomerPageForCommonComponent(CustomerComponentRequest params, PageDto pageDto);

    /**
     * 按指定条件查询客户
     *
     * @param params
     * @return
     */
    CallResult<List<CustomerComponentResponse>> selectCustomerListForCommonComponent(CustomerComponentRequest params);

    /**
     * 查询客户详情
     *
     * @param enterpriseNo 租户编号
     * @param customerNo   客户no
     * @return
     */
    CallResult<CustomerInfoResponse> selectCustomerInfo(String enterpriseNo, String customerNo);


    /**
     * 查询供应商使用信息
     *
     * @param enterpriseNo 租户编号
     * @param customerCode 供应商no
     * @return
     */
    CallResult<List<QueryUseInfoResponse>> queryUseInfo(String enterpriseNo, String customerCode);


    /**
     * 根据客户编码查询税务信息列表
     * @param enterpriseNo
     * @param customerCode
     * @return
     */
    CallResult<List<CustomerInvoiceResponse>> getInvoiceListByCustomerCode(String enterpriseNo, String customerCode);

}
