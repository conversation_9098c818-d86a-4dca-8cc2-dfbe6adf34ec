package com.yyigou.dsrp.cdc.client.v2.customer.response;

import com.yyigou.ddc.common.service.annotation.EntityField;
import com.yyigou.dsrp.cdc.client.v2.company.response.CompanyBasicResponse;
import lombok.Data;

/**
 * @Description 客户基本信息
 * @Classname CustomerBasicResponse
 * @Date 2025/3/18 16:39
 * @author: baoww
 */
@Data
public class CustomerBasicResponse extends CompanyBasicResponse {
    private static final long serialVersionUID = -8814255689780028574L;

    @EntityField(name = "管理组织编号")
    private String manageOrgNo;

    @EntityField(name = "管理组织名称")
    private String manageOrgName;

    @EntityField(name = "客户编号")
    private String customerNo;

    @EntityField(name = "客户编码")
    private String customerCode;

    @EntityField(name = "客户名称")
    private String customerName;

    @EntityField(name = "客户外语名称")
    private String customerNameEn;

    @EntityField(name = "统一社会信用代码")
    private String unifiedSocialCode;

    @EntityField(name = "客户分类编号")
    private String customerCategoryNo;

    @EntityField(name = "客户分类名称")
    private String customerCategoryName;

    @EntityField(name = "助记码")
    private String mnemonicCode;

    @EntityField(name = "客户类型")
    private String transactionType;

    @EntityField(name = "客户类型名称")
    private String transactionTypeName;

    @EntityField(name = "是否散户")
    private Integer retailInvestors;

    @EntityField(name = "是否散户名称")
    private String retailInvestorsName;

    @EntityField(name = "备注")
    private String remark;

    @EntityField(name = "业务状态：1:合格 2：草稿")
    private Integer businessFlag;

    @EntityField(name = "是否关联企业：1-是，0-否")
    private Integer isAssociatedEnterprise;

    @EntityField(name = "关联组织编号")
    private String associatedOrgNo;

    @EntityField(name = "关联组织编码")
    private String associatedOrgCode;

    @EntityField(name = "关联组织名称")
    private String associatedOrgName;
}
