package com.yyigou.dsrp.cdc.client.v2.supplier.request;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;

@Data
public class SupplierAssignMdmExeRequest extends SupplierAssignExeRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    @EntityField(name = "使用组织编号-供应商编码映射")
    private Map<String, String> orgNo2SupplierNoMap;
}
