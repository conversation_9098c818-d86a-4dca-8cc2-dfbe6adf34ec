package com.yyigou.dsrp.cdc.client.v2.company.request;

import com.yyigou.ddc.common.service.annotation.EntityField;
import com.yyigou.dsrp.cdc.common.enums.LinkmanTypeEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Description 企业联系地址请求参数
 * @Classname CompanyAddressRequest
 * @Date 2025/3/18 17:27
 * @author: baoww
 */
@Data
public class CompanyLinkRequest implements Serializable {
    private static final long serialVersionUID = -5063385797592108676L;
    @EntityField(name = "租户编号")
    private String enterpriseNo;
    @EntityField(name = "组织编号")
    private String useOgNo;
    @EntityField(name = "来源类型：is_sale-销售（客户），is_supply-采购（供应商）")
    private LinkmanTypeEnum typeEnum;
    @EntityField(name = "来源档案编号")
    private List<String> sourceList;
}
