package com.yyigou.dsrp.cdc.client.supplier;

import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.dsrp.cdc.client.supplier.request.SupplierManQueryRequest;
import com.yyigou.dsrp.cdc.client.supplier.response.SupplierSalesManResponse;

import java.util.List;

/**
 * 供应商扩展接口
 * 用于提供供应商非标准接口
 */
public interface SupplierExtendClient {

    //废弃使用 querySupplierManList
    @Deprecated
    CallResult<List<SupplierSalesManResponse>> querySupplierMan(String enterpriseNo, String supplierNo, String orgNo, String orderManNo, List<Integer> orderSpecialist);


    CallResult<List<SupplierSalesManResponse>> querySupplierManList(SupplierManQueryRequest params);


}
