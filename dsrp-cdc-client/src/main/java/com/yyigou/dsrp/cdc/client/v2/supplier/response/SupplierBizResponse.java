package com.yyigou.dsrp.cdc.client.v2.supplier.response;

import com.yyigou.ddc.common.service.annotation.EntityField;
import com.yyigou.dsrp.cdc.client.v2.company.response.CompanyLinkmanResponse;
import com.yyigou.dsrp.cdc.client.v2.company.response.CompanyShippingAddressResponse;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Description 供应商业务信息
 * @Classname SupplierBizResponse
 * @Date 2025/3/18 16:39
 * @author: baoww
 */
@Data
public class SupplierBizResponse extends SupplierBasicResponse implements Serializable {
    private static final long serialVersionUID = -6413140533278133644L;

    // ------------业务信息------------
    @EntityField(name = "bizId")
    private Long bizId;

    @EntityField(name = "使用组织编号")
    private String useOrgNo;

    @EntityField(name = "使用组织名称")
    private String useOrgName;

    @EntityField(name = "使用组织修改标志")
    private Integer useOrgModifyFlag; //使用组织修改标志 0：未修改 1：已修改

    @EntityField(name = "合作性质")
    private String cooperationMode;

    @EntityField(name = "合作性质名称")
    private String cooperationModeName;

    @EntityField(name = "币种")
    private String currency;

    @EntityField(name = "币种名称")
    private String currencyName;

    @EntityField(name = "结算方式")
    private String settlementModes;

    @EntityField(name = "结算方式名称")
    private String settlementModesName;

    @EntityField(name = "付款协议id")
    private Long paymentAgreementId;

    @EntityField(name = "付款协议编码")
    private String paymentAgreementCode;

    @EntityField(name = "付款协议名称")
    private String paymentAgreementName;

    @EntityField(name = "付款条件")
    private String paymentTerm;

    @EntityField(name = "付款条件名称")
    private String paymentTermName;

    @EntityField(name = "信用额度")
    private String creditAmount;

    @EntityField(name = "信用期限")
    private Integer periodDays;

    @EntityField(name = "合作开始时间")
    private String coopStartTime;

    @EntityField(name = "合作结束时间")
    private String coopEndTime;

    @EntityField(name = "业务归属")
    private String ownerCompany;


    // ------------供应商首营信息------------
    @EntityField(name = "GSP首营状态")
    private Integer gspAuditStatus;


    // ------------供应商联系人信息------------
    @EntityField(name = "供应商联系人信息")
    private List<CompanyLinkmanResponse> companyLinkmanList;

    // ------------供应商联系地址信息------------
    @EntityField(name = "供应商联系地址信息")
    private List<CompanyShippingAddressResponse> companyShippingAddressList;

    // ------------供应商负责人信息------------
    private List<SupplierOrderManResponse> supplierOrderManList;

    // 银行信息 @see com.yyigou.dsrp.cdc.client.v2.company.response.CompanyBasicResponse#companyBankList


    // ------------分派信息------------
    @EntityField(name = "管控启停状态")
    private String controlStatus;


}
