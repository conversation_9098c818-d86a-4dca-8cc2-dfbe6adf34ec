package com.yyigou.dsrp.cdc.client.customer.response;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;

@Data
public class AreaResponse implements Serializable {
    /**
     * 客户编号
     */
    @EntityField(name = "客户编号")
    private String customerNo;
    /**
     * 客户名称
     */
    @EntityField(name = "客户名称")
    private String customerName;
    /**
     * 区域编号
     */
    @EntityField(name = "区域编号")
    private String areaNo;
    /**
     * 区域编码
     */
    @EntityField(name = "区域编码")
    private String areaName;
    /**
     * 区域编码
     */
    @EntityField(name = "区域编码")
    private String areaCode;
    /**
     * 所属企业编号
     */
    @EntityField(name = "所属企业编号")
    private String enterpriseNo;
    /**
     * 修改时间
     */
    @EntityField(name = "修改时间")
    private String operateNo;
    /**
     * 修改人名称
     */
    @EntityField(name = "修改人名称")
    private String operateName;
    /**
     * 修改时间
     */
    @EntityField(name = "修改时间")
    private String operateTime;

    /**
     * 是否删除 1:已删除 0：未删除
     */
    @EntityField(name = "是否删除 1:已删除 0：未删除")
    private Integer deleted;

    /**
     * 是否默认 1：是 0：否
     */
    @EntityField(name = "否默认 1：是 0：否", stringValueTypeLength = 255)
    private Integer isDefault;

}
