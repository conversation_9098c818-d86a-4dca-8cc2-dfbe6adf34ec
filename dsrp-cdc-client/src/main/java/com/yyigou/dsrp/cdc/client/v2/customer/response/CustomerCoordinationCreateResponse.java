package com.yyigou.dsrp.cdc.client.v2.customer.response;

import lombok.Data;

import java.io.Serializable;

/**
 * @Description 客商关系建立新增客户档案返回参数
 * @Classname CustomerCoordinationCreateResponse
 * @Date 2025/3/18 16:36
 * @author: baoww
 */
@Data
public class CustomerCoordinationCreateResponse implements Serializable {

    private static final long serialVersionUID = 8220684685463932041L;

    /**
     * 客户编号
     */
    private String customerNo;
    /**
     * 客户编码
     */
    private String customerCode;
    /**
     * 客户名称
     */
    private String customerName;
}
