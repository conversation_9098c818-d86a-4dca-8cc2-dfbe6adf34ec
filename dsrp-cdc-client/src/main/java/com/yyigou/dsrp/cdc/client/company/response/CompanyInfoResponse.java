package com.yyigou.dsrp.cdc.client.company.response;

import lombok.Data;

import java.io.Serializable;

@Data
public class CompanyInfoResponse implements Serializable {
    private static final long serialVersionUID = -3266519656702747946L;
    /**
     * 主键码
     */
    private String companyNo;
    /**
     * 企业编码
     */
    private String companyCode;
    /**
     * 公司/企业名称（外部）
     */
    private String companyName;
    /**
     * 本企业编号
     */
    private String enterpriseNo;
    /**
     * 统一社会信用代码
     */
    private String unifiedSocialCode;
    /**
     * 统一社会信用代码
     */
    private String partnership;
    /**
     * 统一社会信用代码
     */
    private String partnershipText;
    /**
     * 纳税类别
     */
    private String taxCategory;
}
