package com.yyigou.dsrp.cdc.client.v2.customer.request;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class CustomerAssignExeRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    @EntityField(name = "租户编号")
    private String enterpriseNo;

    @EntityField(name = "管理组织编号")
    private String manageOrgNo;

    @EntityField(name = "分派的档案项信息")
    private List<CustomerAssignDocExeRequest> docList;
}
