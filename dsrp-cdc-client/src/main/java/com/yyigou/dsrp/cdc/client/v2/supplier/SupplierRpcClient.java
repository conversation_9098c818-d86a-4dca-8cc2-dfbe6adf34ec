package com.yyigou.dsrp.cdc.client.v2.supplier;

import com.yyigou.ddc.common.page.PageDto;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.ddc.services.ddc.uap.domain.dto.grade.GradeDocAssignMessage;
import com.yyigou.ddc.services.ddc.uap.domain.vo.grade.GradeRpcAssignVO;
import com.yyigou.dsrp.cdc.client.supplier.request.SupplierNameRequest;
import com.yyigou.dsrp.cdc.client.supplier.request.SupplierNoRequest;
import com.yyigou.dsrp.cdc.client.v2.customer.request.CustomerCodeOrgPairListRequest;
import com.yyigou.dsrp.cdc.client.v2.customer.response.CustomerBizResponse;
import com.yyigou.dsrp.cdc.client.v2.supplier.request.SupplierCodeOrgPairListRequest;
import com.yyigou.dsrp.cdc.client.v2.supplier.request.SupplierGspStatusRequest;
import com.yyigou.dsrp.cdc.client.v2.supplier.request.SupplierQueryRequest;
import com.yyigou.dsrp.cdc.client.v2.supplier.request.SupplierStandardRequest;
import com.yyigou.dsrp.cdc.client.v2.supplier.response.SupplierBaseResponse;
import com.yyigou.dsrp.cdc.client.v2.supplier.response.SupplierBasicResponse;
import com.yyigou.dsrp.cdc.client.v2.supplier.response.SupplierBizResponse;

import java.util.List;

/**
 * @Description 供应商RPC接口
 * @Classname SupplierRpcClient
 * @Date 2025/3/18 16:35
 * @author: baoww
 */
public interface SupplierRpcClient {


    /**
     * 根据通用查询条件查询供应商基本信息
     *
     * @param params
     * @return
     */
    CallResult<List<SupplierBasicResponse>> selectSupplierBasicInfoList(SupplierStandardRequest params);

    /**
     * 根据通用查询条件查询供应商基本信息+业务信息
     *
     * @param params
     * @return
     */
    CallResult<List<SupplierBizResponse>> selectSupplierBizInfoList(SupplierStandardRequest params);



    /**
     * 根据供应商档案标准查询条件查询供应商档案信息
     *
     * @param params
     * @return
     */
    CallResult<List<SupplierBizResponse>> selectSupplierBizByCodeOrgPair(SupplierCodeOrgPairListRequest params);

    /**
     * 根据租户编号、使用组织编号、供应商编号查询供应商详细信息
     *
     * @param enterpriseNo
     * @param useOrgNo
     * @param supplierNo
     * @return
     */
    CallResult<SupplierBizResponse> getSupplierBizInfoBySupplierNo(String enterpriseNo, String useOrgNo, String supplierNo);

    /**
     * 根据租户编号、使用组织编号、供应商编码查询供应商详细信息
     *
     * @param enterpriseNo
     * @param useOrgNo
     * @param supplierCode
     * @return
     */
    CallResult<SupplierBizResponse> getSupplierBizInfoBySupplierCode(String enterpriseNo, String useOrgNo, String supplierCode);


    /**
     * 根据通用查询条件分页查询供应商基本信息+业务信息
     *
     * @param params
     * @param pageDto
     * @return
     */
    CallResult<PageVo<SupplierBizResponse>> selectSupplierBizInfoListPage(SupplierStandardRequest params, PageDto pageDto);

    /**
     * 根据供应商编号查询供应商基本信息+业务信息
     *
     * @param request
     * @return
     */
    CallResult<List<SupplierBizResponse>> selectSupplierBizInfoByNo(SupplierNoRequest request);

    /**
     * 根据供应商名称查询供应商基本信息+业务信息
     *
     * @param request
     * @return
     */
    CallResult<List<SupplierBizResponse>> selectSupplierBizInfoByName(SupplierNameRequest request);

    /**
     * 分派供应商档案
     * @param message
     * @return
     */
    CallResult<List<GradeRpcAssignVO>> assignSupplier(GradeDocAssignMessage message);

    /**
     * 供应商档案首营状态变更
     *
     * @param message
     * @return
     */
    CallResult<Boolean> changeGspStatus(SupplierGspStatusRequest message);

    /**
     * 根据供应商编码查询供应商基本信息
     *
     * @param request
     * @return
     */
    CallResult<List<SupplierBasicResponse>> querySupplierBasicInfoList(SupplierQueryRequest request);

    /**
     * 根据供应商编码查询供应商基本信息
     *
     * @param enterpriseNo
     * @param supplierCode
     * @return
     */
    CallResult<List<SupplierBaseResponse>> queryAssignInfo(String enterpriseNo, String supplierCode);

    /**
     * 根据租户编号+供应商编码获取供应商档案基本信息
     *
     * @param enterpriseNo
     * @param supplierCode
     * @return
     */
    CallResult<SupplierBasicResponse> getSupplierBasicInfoBySupplierCode(String enterpriseNo, String supplierCode);
}
