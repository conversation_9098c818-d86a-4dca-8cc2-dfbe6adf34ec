package com.yyigou.dsrp.cdc.client.v2.customer.response;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;

@Data
public class CustomerSalesManResponse implements Serializable {
    @EntityField(name = "ID")
    private Long id;

    @EntityField(name = "业务员编码")
    private String manCode;

    @EntityField(name = "部门编号")
    private String deptNo;

    @EntityField(name = "部门名称")
    private String deptName;

    @EntityField(name = "业务员编号")
    private String salesManNo;

    @EntityField(name = "")
    private String salesManName;

    @EntityField(name = "职位")
    private String post;

    @EntityField(name = "手机号")
    private String mobile;

    @EntityField(name = "默认订单专员  2:是 1：默认  0：否 ")
    private Integer orderSpecialist;

    @EntityField(name = "默认业务员  2:是 1：默认  0：否 ")
    private Integer isDefault;
}
