package com.yyigou.dsrp.cdc.client.customer.request;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;

@Data
public class CustomerInvoiceQueryRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    @EntityField(name = "租户编号")
    private String enterpriseNo;

    @EntityField(name = "使用组织编号")
    private String useOrgNo;


    @EntityField(name = "客户编码")
    private String customerCode;

}
