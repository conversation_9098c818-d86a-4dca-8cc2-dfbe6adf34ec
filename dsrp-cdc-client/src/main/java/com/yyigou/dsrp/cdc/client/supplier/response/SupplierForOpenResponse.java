package com.yyigou.dsrp.cdc.client.supplier.response;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class SupplierForOpenResponse implements Serializable {
    @EntityField(name = "供应商编码")
    private String supplierCode;//供应商编号
    @EntityField(name = "供应商名称")
    private String supplierName;//供应商名称
    @EntityField(name = "数据状态")
    private Integer flag;//数据状态
    @EntityField(name = "供应商编号")
    private String supplierNo;

    @EntityField(name = "供应商证件信息")
    private List<SupplierCertForOpenResponse> certificateList;
}

