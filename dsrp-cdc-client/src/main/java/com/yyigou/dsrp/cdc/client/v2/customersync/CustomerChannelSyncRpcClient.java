package com.yyigou.dsrp.cdc.client.v2.customersync;

import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.dsrp.cdc.client.v2.customersync.request.CustomerChannelSyncMappingRequest;
import com.yyigou.dsrp.cdc.client.v2.customersync.response.CustomerChannelSyncMappingResponse;

import java.util.List;

public interface CustomerChannelSyncRpcClient {

    CallResult<List<CustomerChannelSyncMappingResponse>> scanCustomer(Integer limit, Integer failTimes);

    CallResult<Boolean> syncKlpCustomer(CustomerChannelSyncMappingRequest customerChannelSyncMappingRequest);
}
