package com.yyigou.dsrp.cdc.client.v2.company.request;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Description 企业档案查询组织请求参数
 * @Classname CompanyAssociateOrgRequest
 * @Date 2025/4/30 21:26
 * @author: baoww
 */
@Data
public class CompanyAssociateOrgRequest implements Serializable {
    private static final long serialVersionUID = -7567300956672119335L;

    @EntityField(name = "租户编号")
    private String enterpriseNo;
    @EntityField(name = "组织编号集合")
    private List<String> orgNoList;
    @EntityField(name = "组织编码集合")
    private List<String> orgCodeList;
    @EntityField(name = "来源类型：1-供应商，2-客户，3-纳税主体组织")
    private Integer sourceType;
}
