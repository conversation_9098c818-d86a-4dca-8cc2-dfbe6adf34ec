package com.yyigou.dsrp.cdc.client.company;

import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.dsrp.cdc.client.company.request.CompanyCertFindRequest;
import com.yyigou.dsrp.cdc.client.company.response.CompanyCertFindResponse;

import java.util.List;

public interface CompanyCertClient {

    /**
     * 获取企业证照信息
     *
     * @param request
     * @return
     */
    CallResult<List<CompanyCertFindResponse>> findCompanyCertList(CompanyCertFindRequest request);
}
