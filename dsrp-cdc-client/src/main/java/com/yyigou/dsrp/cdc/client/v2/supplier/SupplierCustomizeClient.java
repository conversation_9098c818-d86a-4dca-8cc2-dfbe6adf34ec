package com.yyigou.dsrp.cdc.client.v2.supplier;

import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.dsrp.cdc.client.v2.supplier.request.SupplierCoordinationCreateRequest;
import com.yyigou.dsrp.cdc.client.v2.supplier.response.SupplierCoordinationCreateResponse;

/**
 * @Description 供应商档案定制RPC接口
 * @Classname SupplierCustomizeClient
 * @Date 2025/3/18 18:19
 * @author: baoww
 */
public interface SupplierCustomizeClient {

    /**
     * 定制接口使用场景：1.客商关系建立时创建供应商档案
     *
     * @param params
     * @return
     */
    CallResult<SupplierCoordinationCreateResponse> createCoordinationSupplier(SupplierCoordinationCreateRequest params);
}
