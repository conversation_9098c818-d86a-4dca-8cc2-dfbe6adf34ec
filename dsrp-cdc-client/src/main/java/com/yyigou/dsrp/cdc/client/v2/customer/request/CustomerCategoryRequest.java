package com.yyigou.dsrp.cdc.client.v2.customer.request;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class CustomerCategoryRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    @EntityField(name = "所属企业编号")
    private String enterpriseNo;

    @EntityField(name = "使用组织编号")
    private String useOrgNo;

    @EntityField(name = "分类编码列表")
    private List<String> noList;
}
