package com.yyigou.dsrp.cdc.client.company.response;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;

@Data
public class CompanyBankResponse implements Serializable {
    @EntityField(name = "id")
    private Long id;
    private String bankCode;

    private String bankType;

    private String bankTypeName;

    private String openBank;

    private String accountNo;

    private String accountName;

    private Integer accountType;

    private String linkPerson;

    private String linkPhone;

    private Integer status;

    private Integer isDefault;
    private String linkaddType;
    private String sourceNo;
}
