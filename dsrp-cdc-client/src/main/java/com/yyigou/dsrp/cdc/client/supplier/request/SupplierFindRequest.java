package com.yyigou.dsrp.cdc.client.supplier.request;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 描述：
 *
 * @author: myq
 * @date: 2023/8/29 16:18
 * @version: 1.0.0
 */
@Data
public class SupplierFindRequest implements Serializable {

    /**
     * 供应商编号精确查询
     */
    private List<String> supplierNoList;


    /**
     * 供应商编号精确查询
     */
    private List<String> notInSupplierNoList;

    /**
     * 供应商编号
     */
    private String supplierName;
    /**
     * 模糊搜索关键字：供应商名称
     */
    private String supplierNameKeyword;
    /**
     * 模糊搜索关键字：供应商编码/编号/名称模糊查询
     */
    private String supplierKeywords;

    /**
     * 业务状态
     */
    private List<Integer> businessFlagList;
}
