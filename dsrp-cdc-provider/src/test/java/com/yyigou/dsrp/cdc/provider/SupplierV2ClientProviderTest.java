package com.yyigou.dsrp.cdc.provider;

import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.dsrp.cdc.client.customer.CustomerClient;
import com.yyigou.dsrp.cdc.client.customer.response.response.CustomerInvoiceResponse;
import com.yyigou.dsrp.cdc.client.v2.supplier.SupplierRpcClient;
import com.yyigou.dsrp.cdc.client.v2.supplier.request.SupplierStandardRequest;
import com.yyigou.dsrp.cdc.client.v2.supplier.response.SupplierBizResponse;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.ImportResource;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 *
 **/
@SpringBootTest
//@EnableAsync
@ImportResource(value = {"classpath*:/META-INF/spring/applicationContext-cdc-provider.xml"})
//@DubboComponentScan(value = "com.yyigou.dsrp.cdc.provider")
//@EnableTransactionManagement
@RunWith(SpringRunner.class)
public class SupplierV2ClientProviderTest {

    @Resource
    private SupplierRpcClient supplierRpcClient;



    @Test
    public void selectSupplierBizInfoList_test() {
        SupplierStandardRequest params = new SupplierStandardRequest();
        params.setEnterpriseNo("**********");
        params.setSupplierNoList(Collections.singletonList("10400001675"));

        CallResult<List<SupplierBizResponse>> listCallResult = supplierRpcClient.selectSupplierBizInfoList(params);
        System.out.println(listCallResult);
    }

}
