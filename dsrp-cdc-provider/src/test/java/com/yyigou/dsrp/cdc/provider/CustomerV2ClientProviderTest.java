package com.yyigou.dsrp.cdc.provider;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.dsrp.cdc.client.v2.customer.CustomerRpcClient;
import com.yyigou.dsrp.cdc.client.v2.customer.request.CustomerCodeOrgPairListRequest;
import com.yyigou.dsrp.cdc.client.v2.customer.request.CustomerCodeOrgPairRequest;
import com.yyigou.dsrp.cdc.client.v2.customer.request.CustomerStandardRequest;
import com.yyigou.dsrp.cdc.client.v2.customer.response.CustomerBizResponse;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.ImportResource;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 *
 **/
@SpringBootTest
//@EnableAsync
@ImportResource(value = {"classpath*:/META-INF/spring/applicationContext-cdc-provider.xml"})
//@DubboComponentScan(value = "com.yyigou.dsrp.cdc.provider")
//@EnableTransactionManagement
@RunWith(SpringRunner.class)
public class CustomerV2ClientProviderTest {

    @Resource
    private CustomerRpcClient customerRpcClient;


    @Test
    public void selectCustomerBizInfoList_test() {
//        {
//            CustomerStandardRequest params = new CustomerStandardRequest();
//            params.setEnterpriseNo("2000002");
//            params.setCustomerNoList(Collections.singletonList("10500002789"));
//            params.setUseOrgNo("7711");
//
//            CallResult<List<CustomerBizResponse>> listCallResult = customerRpcClient.selectCustomerBizInfoList(params);
//            System.out.println(listCallResult);
//        }

//        {
//            CustomerStandardRequest params = new CustomerStandardRequest();
//            params.setEnterpriseNo("2000002");
//            params.setCustomerCodeList(Lists.newArrayList("KH0000348","KH0000358","KH0000357","KH0000353"));
//            params.setUseOrgNo("7879");
//
//            CallResult<List<CustomerBizResponse>> listCallResult = customerRpcClient.selectCustomerBizInfoList(params);
//            System.out.println("listCallResult=" + JSON.toJSONString(listCallResult));
//        }

        {
            CustomerCodeOrgPairListRequest params = new CustomerCodeOrgPairListRequest();
            params.setEnterpriseNo("2000002");

            List<CustomerCodeOrgPairRequest> customerCodeOrgPairRequestList = Lists.newArrayList();
            {
                CustomerCodeOrgPairRequest codeOrgPairRequest = new CustomerCodeOrgPairRequest();
                codeOrgPairRequest.setUseOrgNo("7879");
                codeOrgPairRequest.setCustomerCode("KH0000395");
                customerCodeOrgPairRequestList.add(codeOrgPairRequest);
            }

            {
                CustomerCodeOrgPairRequest codeOrgPairRequest = new CustomerCodeOrgPairRequest();
                codeOrgPairRequest.setUseOrgNo("7879");
                codeOrgPairRequest.setCustomerCode("KH0000396");
                customerCodeOrgPairRequestList.add(codeOrgPairRequest);
            }
            params.setCustomerCodeOrgPairList(customerCodeOrgPairRequestList);

            CallResult<List<CustomerBizResponse>> listCallResult = customerRpcClient.selectCustomerBizByCodeOrgPair(params);
            System.out.println("listCallResult=" + JSON.toJSONString(listCallResult));

        }
    }

    @Test
    public void selectCustomerBizInfoList_test2() {
        CustomerStandardRequest params = new CustomerStandardRequest();
        params.setEnterpriseNo("2000002");
//        params.setCustomerNoList(Collections.singletonList("10500002789"));
        params.setUseOrgNo("7879");
        params.setAssociatedOrgNoList(Lists.newArrayList("7711","7879"));

        CallResult<List<CustomerBizResponse>> listCallResult = customerRpcClient.selectCustomerBizInfoList(params);
        System.out.println(listCallResult);


    }
}
