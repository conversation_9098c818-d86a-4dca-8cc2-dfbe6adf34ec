package com.yyigou.dsrp.cdc.provider;

import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.dsrp.cdc.client.customer.CustomerClient;
import com.yyigou.dsrp.cdc.client.customer.response.response.CustomerInvoiceResponse;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.ImportResource;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;

/**
 *
 **/
@SpringBootTest
//@EnableAsync
@ImportResource(value = {"classpath*:/META-INF/spring/applicationContext-cdc-provider.xml"})
//@DubboComponentScan(value = "com.yyigou.dsrp.cdc.provider")
//@EnableTransactionManagement
@RunWith(SpringRunner.class)
public class CustomerClientProviderTest {

    @Resource
    private CustomerClient customerClient;



    @Test
    public void getInvoiceListByCustomerCode_test() {
        CallResult<List<CustomerInvoiceResponse>> invoiceListByCustomerCode = customerClient.getInvoiceListByCustomerCode("7711", "1000397");
        System.out.println(invoiceListByCustomerCode);
    }

}
