package com.yyigou.dsrp.cdc.provider;

import com.alibaba.fastjson.JSON;
import com.yyigou.ddc.common.page.PageDto;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.ddc.common.service.SessionUser;
import com.yyigou.dsrp.cdc.api.v2.company.dto.CompanyLinkmanQueryDTO;
import com.yyigou.dsrp.cdc.api.v2.company.vo.CompanyLinkmanVO;
import com.yyigou.dsrp.cdc.api.v2.customer.CustomerV2API;
import com.yyigou.dsrp.cdc.api.v2.customer.dto.CustomerAddressQueryDTO;
import com.yyigou.dsrp.cdc.api.v2.customer.dto.CustomerLinkmanListUpdateDTO;
import com.yyigou.dsrp.cdc.api.v2.customer.vo.CustomerAddressVO;
import com.yyigou.dsrp.cdc.common.util.CommonUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.ImportResource;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Arrays;

/**
 *
 **/
@SpringBootTest
//@EnableAsync
@ImportResource(value = {"classpath*:/META-INF/spring/applicationContext-cdc-provider.xml"})
//@DubboComponentScan(value = "com.yyigou.dsrp.cdc.provider")
//@EnableTransactionManagement
@RunWith(SpringRunner.class)
public class CustomerV2APIProviderTest {

    @Resource
    private CustomerV2API customerV2API;


    @Test
    public void updateCustomerLinkman_test() {
            SessionUser sessionUser = CommonUtil.createSessionUser();
            sessionUser.setEnterpriseNo("2000002");
            CommonUtil.threadPierce(sessionUser);

//        {
//            String json = "{\n" +
//                    "    \"linkmanList\": [\n" +
//                    "      {\n" +
//                    "        \"linkman\": \"张三\",\n" +
//                    "        \"sex\": \"secrecy\",\n" +
//                    "        \"linkmanType\": \"is_sale\",\n" +
//                    "        \"position\": \"销售\",\n" +
//                    "        \"fixedPhone\": \"15877776666\",\n" +
//                    "        \"mobilePhone\": \"15877776666\",\n" +
//                    "        \"qq\": \"15877776666\",\n" +
//                    "        \"wx\": \"15877776666\",\n" +
//                    "        \"email\": \"<EMAIL>\",\n" +
//                    "        \"status\": 1,\n" +
//                    "        \"isDefault\": 1,\n" +
//                    "        \"sexName\": \"保密\",\n" +
//                    "        \"id\": 25495,\n" +
//                    "      }\n" +
//                    "    ],\n" +
//                    "    \"customerCode\": \"1KH1741832142\",\n" +
//                    "    \"useOrgNo\": \"7711\"\n" +
//                    "}";
//            CustomerLinkmanListUpdateDTO params = JSON.parseObject(json, CustomerLinkmanListUpdateDTO.class);
//
//            CallResult<Boolean> booleanCallResult = customerV2API.updateCustomerLinkman(params);
//
//            System.out.println("booleanCallResult=" + JSON.toJSONString(booleanCallResult));
//
//        }

//        {
//            CompanyLinkmanQueryDTO params = new CompanyLinkmanQueryDTO();
//            params.setCustomerCode("1KH1741832142");
//            params.setUseOrgNo("7711");
//
//            PageDto pageDto = new PageDto();
//            pageDto.setPageIndex(1);
//            pageDto.setPageSize(20);
//
//
//            CallResult<PageVo<CompanyLinkmanVO>> customerLinkmanListPage = customerV2API.findCustomerLinkmanListPage(params, pageDto);
//
//            System.out.println("customerLinkmanListPage=" + JSON.toJSONString(customerLinkmanListPage));
//        }

        {
            CustomerAddressQueryDTO params = new CustomerAddressQueryDTO();
            params.setCustomerCode("1KH1741832142");
            params.setUseOrgNo("7711");
            params.setAddressTypeList(Arrays.asList("1", "2", "3", "4"));

            PageDto pageDto = new PageDto();
            pageDto.setPageIndex(1);
            pageDto.setPageSize(20);


            CallResult<PageVo<CustomerAddressVO>> customerLinkAddressPage = customerV2API.findCustomerLinkAddressPage(params, pageDto);

            System.out.println("customerLinkAddressPage=" + JSON.toJSONString(customerLinkAddressPage));
        }
    }
}
