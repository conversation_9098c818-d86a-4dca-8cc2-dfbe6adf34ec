<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright (C) 2018, <PERSON> (www.yyigou.com), Inc - All Rights Reserved
  ~ Unauthorized copying of this file, via any medium is strictly prohibited
  ~ Proprietary and confidential
  ~ <AUTHOR> <<EMAIL>>
  ~ @updated 18-6-26 下午1:58
  ~ @project service-pay
  ~ @module service-dsrp-equipment
  ~ @file assembly.xml
  ~
  -->

<assembly>
    <id>assembly</id>
    <formats>
        <format>tar.gz</format>
    </formats>
    <includeBaseDirectory>true</includeBaseDirectory>
    <fileSets>
        <fileSet>
            <directory>src/main/scripts</directory>
            <outputDirectory>/bin</outputDirectory>
            <fileMode>0744</fileMode>
        </fileSet>
    </fileSets>
    <files>
        <!--copy jar file created by shade-->
        <file>
            <source>target/${artifact.artifactId}-${artifact.version}.jar</source>
            <outputDirectory>./lib</outputDirectory>
        </file>
    </files>
</assembly>