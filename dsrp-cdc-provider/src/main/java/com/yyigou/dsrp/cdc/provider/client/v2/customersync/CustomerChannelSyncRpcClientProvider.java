package com.yyigou.dsrp.cdc.provider.client.v2.customersync;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.config.annotation.Service;
import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.dsrp.cdc.client.v2.customersync.CustomerChannelSyncRpcClient;
import com.yyigou.dsrp.cdc.client.v2.customersync.request.CustomerChannelSyncMappingRequest;
import com.yyigou.dsrp.cdc.client.v2.customersync.response.CustomerChannelSyncMappingResponse;
import com.yyigou.dsrp.cdc.common.errorcode.ErrorCode;
import com.yyigou.dsrp.cdc.common.util.BeanUtil;
import com.yyigou.dsrp.cdc.dao.v2.customer.entity.CustomerChannelSyncMapping;
import com.yyigou.dsrp.cdc.model.v2.customer.req.CustomerChannelSyncMappingScanRequest;
import com.yyigou.dsrp.cdc.model.v2.customersync.req.CustomerChannelSyncReq;
import com.yyigou.dsrp.cdc.service.v2.customer.CustomerChannelSyncMappingService;
import com.yyigou.dsrp.cdc.service.v2.customersync.KlpCustomer2NCJobService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

@Component
@RequiredArgsConstructor
@Slf4j
@Service
public class CustomerChannelSyncRpcClientProvider implements CustomerChannelSyncRpcClient {
    @Resource
    private CustomerChannelSyncMappingService customerChannelSyncMappingService;

    @Resource
    private KlpCustomer2NCJobService klpCustomer2NCJobService;


    @Override
    public CallResult<List<CustomerChannelSyncMappingResponse>> scanCustomer(Integer limit, Integer failTimes) {
        CustomerChannelSyncMappingScanRequest request = new CustomerChannelSyncMappingScanRequest();
        request.setLimit(limit);
        request.setFailTimes(failTimes);

        List<CustomerChannelSyncMapping> customerChannelSyncMappings = customerChannelSyncMappingService.scanNeedSyncList(request);
        if (CollectionUtils.isNotEmpty(customerChannelSyncMappings)) {
            List<CustomerChannelSyncMappingResponse> customerChannelSyncMappingResponses = BeanUtil.copyFieldsList(customerChannelSyncMappings, CustomerChannelSyncMappingResponse.class);
            return CallResult.success(customerChannelSyncMappingResponses);
        }

        return CallResult.success(Collections.emptyList());
    }

    @Override
    public CallResult<Boolean> syncKlpCustomer(CustomerChannelSyncMappingRequest customerChannelSyncMappingResponse) {
        try {
            klpCustomer2NCJobService.syncCustomer2NC(BeanUtil.copyFields(customerChannelSyncMappingResponse, CustomerChannelSyncReq.class));
            return CallResult.success(true);
        } catch (Exception e) {
            log.error("同步客户到NC失败", e);
            return CallResult.failed(ErrorCode.EXIST_CODE, e.getMessage());
        }
    }
}
