package com.yyigou.dsrp.cdc.provider.api.supply;

import com.alibaba.dubbo.config.annotation.Service;
import com.yyigou.ddc.common.exception.CommonExceptionHandler;
import com.yyigou.ddc.common.message.MessageVO;
import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.ddc.common.service.ServiceBaseAbstract;
import com.yyigou.ddc.common.service.annotation.Interface;
import com.yyigou.ddc.common.service.annotation.Method;
import com.yyigou.ddc.common.service.annotation.MethodType;
import com.yyigou.dsrp.cdc.api.supply.SupplyExternalApi;
import com.yyigou.dsrp.cdc.api.supply.dto.SupplierExternalAssignDTO;
import com.yyigou.dsrp.cdc.api.supply.dto.SupplierExternalSaveDTO;
import com.yyigou.dsrp.cdc.api.supply.vo.SupplierExternalSaveVO;
import com.yyigou.dsrp.cdc.common.util.OperationModel;
import com.yyigou.dsrp.cdc.service.supplier.SupplyExternService;
import com.yyigou.dsrp.cdc.service.utils.UserHandleUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Service
@RequiredArgsConstructor
@Slf4j
@Interface(name = "供应商档案对外接口")
public class SupplyExternalApiProvider extends ServiceBaseAbstract implements SupplyExternalApi {

    private final SupplyExternService supplyExternService;

    /**
     * 保存客户档案
     *
     * @param params
     * @return
     */
    @Override
    @Method(aliasName = "dsrp.cdc.open.external.supplier.batchSave", name = "供应商档案保存接口-对外", methodType = MethodType.ADD, processState = 2, requestSession = true, requestAuthentication = true, invokeRange = Method.InvokeRange.OUTER_OPEN, domainCode = "JCY", subDomainCode = "supply")
    public CallResult<List<SupplierExternalSaveVO>> batchSave(List<SupplierExternalSaveDTO> params) {
        try {
            //补充租户信息
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            return CallResult.success(MessageVO.SUCCESS, supplyExternService.batchSave(operationModel, params));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.open.external.supplier.supplyAssign", name = "供应商档案分配接口-对外", methodType = MethodType.ADD, processState = 2, requestSession = true, requestAuthentication = true, invokeRange = Method.InvokeRange.OUTER_OPEN, domainCode = "JCY", subDomainCode = "supply")
    public CallResult<Boolean> supplyAssign(SupplierExternalAssignDTO params) {
        try {
            //补充租户信息
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            return CallResult.success(MessageVO.SUCCESS, supplyExternService.supplyAssign(operationModel, params));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }
}
