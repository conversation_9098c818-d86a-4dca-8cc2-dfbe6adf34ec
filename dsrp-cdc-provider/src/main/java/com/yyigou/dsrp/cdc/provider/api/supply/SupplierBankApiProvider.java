package com.yyigou.dsrp.cdc.provider.api.supply;

import com.alibaba.dubbo.config.annotation.Service;
import com.yyigou.ddc.common.exception.CommonExceptionHandler;
import com.yyigou.ddc.common.message.MessageVO;
import com.yyigou.ddc.common.page.PageDto;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.ddc.common.service.ServiceBaseAbstract;
import com.yyigou.ddc.common.service.annotation.Interface;
import com.yyigou.ddc.common.service.annotation.Method;
import com.yyigou.ddc.common.service.annotation.MethodType;
import com.yyigou.dsrp.cdc.api.supply.SupplierBankApi;
import com.yyigou.dsrp.cdc.api.supply.dto.SupplierBankQueryDTO;
import com.yyigou.dsrp.cdc.api.supply.vo.SupplierBankPageVO;
import com.yyigou.dsrp.cdc.common.util.OperationModel;
import com.yyigou.dsrp.cdc.service.supplier.SupplierBankService;
import com.yyigou.dsrp.cdc.service.utils.UserHandleUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 供应商银行
 **/
@Component
@Service
@RequiredArgsConstructor
@Slf4j
@Interface(name = "供应商银行前端接口")
public class SupplierBankApiProvider  extends ServiceBaseAbstract  implements SupplierBankApi {


    private final SupplierBankService supplierBankService;

    @Override
    @Method(aliasName = "dsrp.cdc.supplierBank.findPage", name = "分页查询供应商的银行信息", methodType = MethodType.QUERY, processState = 2, requestSession = true, requestAuthentication = false)
    public CallResult<PageVo<SupplierBankPageVO>> findBankPage(SupplierBankQueryDTO params, PageDto pageDto) {
        try {
            //补充租户信息
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            return CallResult.success(MessageVO.SUCCESS, supplierBankService.findBankPage(operationModel, params, pageDto));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }
}
