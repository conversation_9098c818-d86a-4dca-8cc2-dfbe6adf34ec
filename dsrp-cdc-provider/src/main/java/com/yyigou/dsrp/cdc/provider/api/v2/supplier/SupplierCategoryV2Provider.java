package com.yyigou.dsrp.cdc.provider.api.v2.supplier;

import cn.hutool.core.util.StrUtil;
import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.config.annotation.Service;
import com.yyigou.ddc.common.exception.CommonExceptionHandler;
import com.yyigou.ddc.common.message.MessageVO;
import com.yyigou.ddc.common.page.PageDto;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.ddc.common.service.ServiceBaseAbstract;
import com.yyigou.ddc.common.service.annotation.Interface;
import com.yyigou.ddc.common.service.annotation.Method;
import com.yyigou.ddc.common.service.annotation.MethodType;
import com.yyigou.dsrp.cdc.api.v2.supplier.SupplierCategoryV2API;
import com.yyigou.dsrp.cdc.api.v2.supplier.dto.*;
import com.yyigou.dsrp.cdc.api.v2.supplier.vo.SupplierCategoryTreeVO;
import com.yyigou.dsrp.cdc.api.v2.supplier.vo.SupplierCategoryVO;
import com.yyigou.dsrp.cdc.common.util.OperationModel;
import com.yyigou.dsrp.cdc.model.constant.DistributedLockConstant;
import com.yyigou.dsrp.cdc.model.constant.ViewNameConstant;
import com.yyigou.dsrp.cdc.model.v2.supplier.req.*;
import com.yyigou.dsrp.cdc.service.utils.RedisClientUtil;
import com.yyigou.dsrp.cdc.service.utils.UserHandleUtils;
import com.yyigou.dsrp.cdc.service.v2.supplier.SupplierCategoryV2Service;
import com.yyigou.dsrp.gcs.common.util.BeanUtil;
import com.yyigou.dsrp.gcs.model.api.constant.ErrorCode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Component
@Service
@RequiredArgsConstructor
@Slf4j
@Interface(name = "供应商分类档案页面接口")
public class SupplierCategoryV2Provider extends ServiceBaseAbstract implements SupplierCategoryV2API {
    @Resource(name = "supplierCategoryV2Service")
    private SupplierCategoryV2Service supplierCategoryV2Service;

    @Resource
    private RedisClientUtil redisClientUtil;

    @Override
    @Method(aliasName = "dsrp.cdc.suppliercategory.v2.queryTree", name = "供应商分类获取整棵树（使用）", viewNo = ViewNameConstant.BDC_SUPPLIER_CATEGORY_VIEW, methodType = MethodType.QUERY, processState = 2, version = "1.0.1", requestSession = false)
    public CallResult<List<SupplierCategoryTreeVO>> queryTree(SupplierCategoryQueryTreeDTO params) {
        try {
            OperationModel operationModel = UserHandleUtils.getOperationModel();

            SupplierCategoryQueryTreeReq queryReq = BeanUtil.copyFields(params, SupplierCategoryQueryTreeReq.class);
            //补充租户信息
            queryReq.setEnterpriseNo(operationModel.getEnterpriseNo());

            return CallResult.success(MessageVO.SUCCESS, supplierCategoryV2Service.queryTree(operationModel, queryReq));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.suppliercategory.v2.queryCategoryTree", name = "供应商分类获取整棵树（管理）", viewNo = ViewNameConstant.BDC_SUPPLIER_CATEGORY_VIEW, methodType = MethodType.QUERY, processState = 2, version = "1.0.1", requestSession = false)
    public CallResult<List<SupplierCategoryTreeVO>> queryCategoryTree(SupplierCategoryQueryCategoryTreeDTO params) {
        try {
            OperationModel operationModel = UserHandleUtils.getOperationModel();

            SupplierCategoryQueryCategoryTreeReq queryReq = BeanUtil.copyFields(params, SupplierCategoryQueryCategoryTreeReq.class);
            //补充租户信息
            queryReq.setEnterpriseNo(operationModel.getEnterpriseNo());

            return CallResult.success(MessageVO.SUCCESS, supplierCategoryV2Service.queryManageTree(operationModel, queryReq));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.suppliercategory.v2.queryUseCategoryTree", name = "供应商分类获取整棵树（使用）", viewNo = ViewNameConstant.BDC_SUPPLIER_CATEGORY_VIEW, methodType = MethodType.QUERY, processState = 2, version = "1.0.1", requestSession = false)
    public CallResult<List<SupplierCategoryTreeVO>> queryUseCategoryTree(SupplierCategoryQueryUseCategoryTreeDTO params) {
        try {
            OperationModel operationModel = UserHandleUtils.getOperationModel();

            SupplierCategoryQueryUseCategoryTreeReq queryReq = BeanUtil.copyFields(params, SupplierCategoryQueryUseCategoryTreeReq.class);
            //补充租户信息
            queryReq.setEnterpriseNo(operationModel.getEnterpriseNo());

            return CallResult.success(MessageVO.SUCCESS, supplierCategoryV2Service.queryUseTree(operationModel, queryReq));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.suppliercategory.v2.queryListPage", name = "供应商分类列表分页查", viewNo = ViewNameConstant.BDC_SUPPLIER_CATEGORY_VIEW, methodType = MethodType.QUERY, processState = 2, version = "1.0.1", requestSession = false)
    public CallResult<PageVo<SupplierCategoryVO>> queryListPage(SupplierCategoryQueryListPageDTO params, PageDto pageDto) {
        try {
            OperationModel operationModel = UserHandleUtils.getOperationModel();

            SupplierCategoryQueryListPageReq queryReq = BeanUtil.copyFields(params, SupplierCategoryQueryListPageReq.class);
            //补充租户信息
            queryReq.setEnterpriseNo(operationModel.getEnterpriseNo());

            return CallResult.success(MessageVO.SUCCESS, supplierCategoryV2Service.queryListPage(operationModel, queryReq, pageDto));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.suppliercategory.v2.checkUniqueName", name = "供应商分类名称唯一性校验", viewNo = ViewNameConstant.BDC_SUPPLIER_CATEGORY_VIEW, methodType = MethodType.QUERY, processState = 2, version = "1.0.1", requestSession = false)
    public CallResult<Boolean> checkUniqueName(SupplierCategoryCheckNameDTO params) {
        try {
            OperationModel operationModel = UserHandleUtils.getOperationModel();

            SupplierCategoryQueryListPageReq queryReq = BeanUtil.copyFields(params, SupplierCategoryQueryListPageReq.class);
            //补充租户信息
            queryReq.setEnterpriseNo(operationModel.getEnterpriseNo());

            return CallResult.success(MessageVO.SUCCESS, supplierCategoryV2Service.checkUniqueName(operationModel, params.getNo(), params.getName(), params.getParentNo()));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.suppliercategory.v2.checkUniqueCode", name = "供应商分类编码唯一性校验", viewNo = ViewNameConstant.BDC_SUPPLIER_CATEGORY_VIEW, methodType = MethodType.QUERY, processState = 2, version = "1.0.1", requestSession = false)
    public CallResult<Boolean> checkUniqueCode(SupplierCategoryCheckCodeDTO params) {
        try {
            OperationModel operationModel = UserHandleUtils.getOperationModel();

            SupplierCategoryQueryListPageReq queryReq = BeanUtil.copyFields(params, SupplierCategoryQueryListPageReq.class);
            //补充租户信息
            queryReq.setEnterpriseNo(operationModel.getEnterpriseNo());

            return CallResult.success(MessageVO.SUCCESS, supplierCategoryV2Service.checkUniqueCode(operationModel, params.getNo(), params.getCode()));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.suppliercategory.v2.checkUseOrgRemoval", name = "供应商分类编码唯一性校验", viewNo = ViewNameConstant.BDC_SUPPLIER_CATEGORY_VIEW, methodType = MethodType.QUERY, processState = 2, version = "1.0.1", requestSession = false)
    public CallResult<Boolean> checkUseOrgRemoval(SupplierCategoryCheckUseOrgRemovalDTO params) {
        try {
            OperationModel operationModel = UserHandleUtils.getOperationModel();

            SupplierCategoryCheckUseOrgRemovalReq queryReq = BeanUtil.copyFields(params, SupplierCategoryCheckUseOrgRemovalReq.class);
            //补充租户信息
            queryReq.setEnterpriseNo(operationModel.getEnterpriseNo());

            return CallResult.success(MessageVO.SUCCESS, supplierCategoryV2Service.checkUseOrgRemoval(operationModel, queryReq));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.suppliercategory.v2.save", name = "新增供应商分类", viewNo = ViewNameConstant.BDC_SUPPLIER_CATEGORY_VIEW, methodType = MethodType.ADD, processState = 2, version = "1.0.1", requestSession = false)
    public CallResult<SupplierCategoryVO> save(SupplierCategorySaveDTO params) {
        String lock = null;
        try {
            OperationModel operationModel = UserHandleUtils.getOperationModel();

            lock = String.join(DistributedLockConstant.DELIMITER, DistributedLockConstant.SUPPLIER_CATEGORY_SAVE_LOCK_KEY, operationModel.getEnterpriseNo(), params.getCategoryCode());
            if (!redisClientUtil.lock(lock, DistributedLockConstant.DEFAULT_WAIT_TIME, DistributedLockConstant.DEFAULT_LEASE_TIME)) {
                return CallResult.failed(ErrorCode.BUSINESS_ERROR_CODE, StrUtil.format("供应商分类【{}】正在处理，请稍后提交！", params.getCategoryCode()));
            }

            SupplierCategorySaveReq supplierCategorySaveReq = new SupplierCategorySaveReq();
            BeanUtils.copyProperties(params, supplierCategorySaveReq);
            //补充租户信息
            supplierCategorySaveReq.setEnterpriseNo(operationModel.getEnterpriseNo());
            return CallResult.success(MessageVO.SUCCESS, supplierCategoryV2Service.saveSupplierCategory(operationModel, supplierCategorySaveReq));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        } finally {
            if (null != lock) {
                redisClientUtil.unlock(lock);
            }
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.suppliercategory.v2.get", name = "获取供应商分类", viewNo = ViewNameConstant.BDC_SUPPLIER_CATEGORY_VIEW, methodType = MethodType.DETAIL, processState = 2, version = "1.0.1", requestSession = false)
    public CallResult<SupplierCategoryVO> get(SupplierCategoryGetDTO params) {
        try {
            SupplierCategoryGetReq supplierCategoryGetReq = new SupplierCategoryGetReq();
            BeanUtils.copyProperties(params, supplierCategoryGetReq);
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            //补充租户信息
            supplierCategoryGetReq.setEnterpriseNo(operationModel.getEnterpriseNo());
            return CallResult.success(MessageVO.SUCCESS, supplierCategoryV2Service.getSupplierCategory(operationModel, supplierCategoryGetReq));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.suppliercategory.v2.update", name = "更新供应商分类", viewNo = ViewNameConstant.BDC_SUPPLIER_CATEGORY_VIEW, methodType = MethodType.UPDATE, processState = 2, version = "1.0.1", requestSession = false)
    public CallResult<SupplierCategoryVO> update(SupplierCategoryUpdateDTO params) {
        try {
            SupplierCategoryUpdateReq supplierCategoryUpdateReq = new SupplierCategoryUpdateReq();
            BeanUtils.copyProperties(params, supplierCategoryUpdateReq);

            if (CollectionUtils.isNotEmpty(params.getUseOrgList())) {
                supplierCategoryUpdateReq.setUseOrgList(new ArrayList<>(params.getUseOrgList().size()));
                for (SupplierCategoryUseOrgDTO supplierCategoryUseOrgDto : params.getUseOrgList()) {
                    supplierCategoryUpdateReq.getUseOrgList().add(BeanUtil.copyFields(supplierCategoryUseOrgDto, SupplierCategoryUseOrgReq.class));
                }
            }

            OperationModel operationModel = UserHandleUtils.getOperationModel();
            //补充租户信息
            supplierCategoryUpdateReq.setEnterpriseNo(operationModel.getEnterpriseNo());
            return CallResult.success(MessageVO.SUCCESS, supplierCategoryV2Service.updateSupplierCategory(operationModel, supplierCategoryUpdateReq));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.suppliercategory.v2.delete", name = "删除供应商分类", viewNo = ViewNameConstant.BDC_SUPPLIER_CATEGORY_VIEW, methodType = MethodType.DELETE, processState = 2, version = "1.0.1", requestSession = false)
    public CallResult<Boolean> delete(SupplierCategoryDeleteDTO params) {
        try {
            SupplierCategoryDeleteReq supplierCategoryDeleteReq = new SupplierCategoryDeleteReq();
            BeanUtils.copyProperties(params, supplierCategoryDeleteReq);
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            //补充租户信息
            supplierCategoryDeleteReq.setEnterpriseNo(operationModel.getEnterpriseNo());
            return CallResult.success(MessageVO.SUCCESS, supplierCategoryV2Service.deleteSupplierCategory(operationModel, supplierCategoryDeleteReq));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.suppliercategory.v2.changeStatus", name = "启停供应商分类", viewNo = ViewNameConstant.BDC_SUPPLIER_CATEGORY_VIEW, methodType = MethodType.UPDATE, processState = 2, version = "1.0.1", requestSession = false)
    public CallResult<Boolean> changeStatus(SupplierCategoryChangeStatusDTO params) {
        try {
            SupplierCategoryChangeStatusReq supplierCategoryChangeStatusReq = new SupplierCategoryChangeStatusReq();
            BeanUtils.copyProperties(params, supplierCategoryChangeStatusReq);
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            //补充租户信息
            supplierCategoryChangeStatusReq.setEnterpriseNo(operationModel.getEnterpriseNo());
            return CallResult.success(MessageVO.SUCCESS, supplierCategoryV2Service.changeSupplierCategoryStatus(operationModel, supplierCategoryChangeStatusReq));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.suppliercategory.v2.queryFlatListPage", name = "获取分页列表", viewNo = ViewNameConstant.BDC_SUPPLIER_CATEGORY_VIEW, methodType = MethodType.QUERY, processState = 2)
    public CallResult<PageVo<SupplierCategoryVO>> queryFlatListPage(SupplierCategoryQueryFlagListPageDTO params, PageDto pageDto) {
        try {
            OperationModel operationModel = UserHandleUtils.getOperationModel();

            SupplierCategoryQueryFlagListPageReq queryReq = BeanUtil.copyFields(params, SupplierCategoryQueryFlagListPageReq.class);
            //补充租户信息
            queryReq.setEnterpriseNo(operationModel.getEnterpriseNo());
            queryReq.setUseOrgNo(operationModel.getOrgNo());

            return CallResult.success(MessageVO.SUCCESS, supplierCategoryV2Service.queryFLatListPage(operationModel, queryReq, pageDto));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }
}
