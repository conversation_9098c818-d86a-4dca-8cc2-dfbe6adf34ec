package com.yyigou.dsrp.cdc.provider.api.customer;

import com.alibaba.dubbo.config.annotation.Service;
import com.yyigou.ddc.common.exception.CommonExceptionHandler;
import com.yyigou.ddc.common.message.MessageVO;
import com.yyigou.ddc.common.page.PageDto;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.ddc.common.service.ServiceBaseAbstract;
import com.yyigou.ddc.common.service.annotation.Interface;
import com.yyigou.ddc.common.service.annotation.Method;
import com.yyigou.ddc.common.service.annotation.MethodType;
import com.yyigou.dsrp.cdc.api.customer.CustomerApi;
import com.yyigou.dsrp.cdc.api.customer.dto.QueryPageCustomerByOrgDTO;
import com.yyigou.dsrp.cdc.api.customer.dto.QueryPageCustomerBySpecifyOrgDTO;
import com.yyigou.dsrp.cdc.api.customer.dto.QueryPageCustomerDTO;
import com.yyigou.dsrp.cdc.api.customer.dto.SaveCustomerDTO;
import com.yyigou.dsrp.cdc.api.customer.vo.CustomerPageVO;
import com.yyigou.dsrp.cdc.api.customer.vo.CustomerVO;
import com.yyigou.dsrp.cdc.common.util.OperationModel;
import com.yyigou.dsrp.cdc.service.customer.CustomerService;
import com.yyigou.dsrp.cdc.service.utils.UserHandleUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Service
@RequiredArgsConstructor
@Slf4j
@Interface(name = "客户档案页面接口")
public class CustomerApiProvider extends ServiceBaseAbstract implements CustomerApi {

    private final CustomerService customerService;


    @Override
    @Method(aliasName = "dsrp.cdc.customer.checkOnlyCode", name = "校验客户编码是否存在", methodType = MethodType.ADD, processState = 2, requestSession = true, requestAuthentication = true)
    public CallResult<Boolean> checkOnlyCode(String no, String code) {
        try {
            //补充租户信息
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            return CallResult.success(MessageVO.SUCCESS, customerService.checkOnlyCode(operationModel, no, code));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.customer.checkOnlyName", name = "校验客户名称是否存在", methodType = MethodType.ADD, processState = 2, requestSession = true, requestAuthentication = true)
    public CallResult<Boolean> checkOnlyName(String no, String name) {
        try {
            //补充租户信息
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            return CallResult.success(MessageVO.SUCCESS, customerService.checkOnlyName(operationModel, no, name));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 保存客户档案
     *
     * @param params
     * @return
     */
    @Override
    @Method(aliasName = "dsrp.cdc.customer.save", name = "保存客户档案信息", methodType = MethodType.ADD, processState = 2, requestSession = true, requestAuthentication = true)
    public CallResult<String> saveCustomer(SaveCustomerDTO params) {
        try {
            //补充租户信息
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            return CallResult.success(MessageVO.SUCCESS, customerService.saveCustomer(operationModel, params, "客户档案新增"));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 保存客户档案
     *
     * @param params
     * @return
     */
    @Override
    @Method(aliasName = "dsrp.cdc.customer.updateCustomer", name = "编辑客户档案信息", methodType = MethodType.ADD, processState = 2, requestSession = true, requestAuthentication = true)
    public CallResult<Boolean> updateCustomer(SaveCustomerDTO params) {
        try {
            //补充租户信息
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            return CallResult.success(MessageVO.SUCCESS, customerService.updateCustomer(operationModel, params));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }


    @Override
    @Method(aliasName = "dsrp.cdc.customer.deleteCustomer", name = "删除客户档案信息", methodType = MethodType.ADD, processState = 2, requestSession = true, requestAuthentication = true)
    public CallResult<Boolean> deleteCustomer(List<String> customerNoList) {
        try {
            //补充租户信息
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            return CallResult.success(MessageVO.SUCCESS, customerService.deleteCustomer(operationModel, customerNoList));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.customer.getCustomer", name = "获取客户档案信息", methodType = MethodType.ADD, processState = 2, requestSession = true, requestAuthentication = true)
    public CallResult<CustomerVO> getCustomer(String customerNo) {
        try {
            //补充租户信息
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            return CallResult.success(MessageVO.SUCCESS, customerService.getCustomer(operationModel.getEnterpriseNo(), customerNo));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }


    /**
     * 分页获取客户档案
     *
     * @param params
     * @return
     */
    @Override
    @Method(aliasName = "dsrp.cdc.customer.queryPageCustomer", name = "分页获取客户档案", methodType = MethodType.ADD, processState = 2, requestSession = true, requestAuthentication = true)
    public CallResult<PageVo<CustomerPageVO>> queryPageCustomer(QueryPageCustomerDTO params, PageDto pageDto) {
        try {
            //补充租户信息
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            return CallResult.success(MessageVO.SUCCESS, customerService.queryPageCustomer(operationModel, params, pageDto));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 查询待处理客户数量
     *
     * @return
     */
    @Method(aliasName = "dsrp.cdc.customer.findExamCount", name = "查询待处理客户数量", methodType = MethodType.QUERY, processState = 2, requestSession = true, requestAuthentication = true)
    @Override
    public CallResult<Long> findExamCount() {
        try{
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            return CallResult.success(MessageVO.SUCCESS, customerService.findExamCount(operationModel));
        }catch (Exception e){
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 分页获取客户档案
     *
     * @param params
     * @param pageDto
     * @return
     */
    @Override
    @Method(aliasName = "dsrp.cdc.customer.group.queryPageCustomer", name = "分页获取客户档案", methodType = MethodType.ADD, processState = 2, requestSession = true, requestAuthentication = false)
    public CallResult<PageVo<CustomerPageVO>> queryPageCustomerByGroup(QueryPageCustomerDTO params, PageDto pageDto) {
        try {
            //补充租户信息
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            return CallResult.success(MessageVO.SUCCESS, customerService.queryPageCustomerByGroup(operationModel, params, pageDto));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 分页获取客户档案
     *
     * @param params
     * @param pageDto
     * @return
     */
    @Override
    @Method(aliasName = "dsrp.cdc.customer.group.org.queryPageCustomer", name = "分页获取客户档案", methodType = MethodType.ADD, processState = 2, requestSession = true, requestAuthentication = false)
    public CallResult<PageVo<CustomerPageVO>> queryPageCustomerByOrg(QueryPageCustomerByOrgDTO params, PageDto pageDto) {
        try {
            //补充租户信息
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            return CallResult.success(MessageVO.SUCCESS, customerService.queryPageCustomerByOrg(operationModel, params, pageDto));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 分页获取指定组织客户档案
     *
     * @param params
     * @param pageDto
     * @return
     */
    @Override
    @Method(aliasName = "dsrp.cdc.customer.group.specifyOrg.queryPageCustomer", name = "分页获取指定组织客户档案", methodType = MethodType.ADD, processState = 2, requestSession = true, requestAuthentication = false)
    public CallResult<PageVo<CustomerPageVO>> queryPageCustomerBySpecifyOrg(QueryPageCustomerBySpecifyOrgDTO params, PageDto pageDto) {
        try {
            //补充租户信息
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            return CallResult.success(MessageVO.SUCCESS, customerService.queryPageCustomerBySpecifyOrg(operationModel, params, pageDto));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }
    /**
     * 分页获取指定组织客户档案
     *
     * @param params
     * @param pageDto
     * @return
     */
    @Override
    @Method(aliasName = "dsrp.cdc.customer.group.compatibleOrg.queryPageCustomer", name = "分页获取指定组织客户档案", methodType = MethodType.ADD, processState = 2, requestSession = true, requestAuthentication = false)
    public CallResult<PageVo<CustomerPageVO>> queryPageCustomerByCompatibleOrg(QueryPageCustomerBySpecifyOrgDTO params, PageDto pageDto) {
        try {
            //补充租户信息
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            return CallResult.success(MessageVO.SUCCESS, customerService.queryPageCustomerByCompatibleOrg(operationModel, params, pageDto));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }
}
