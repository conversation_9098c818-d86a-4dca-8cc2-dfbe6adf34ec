package com.yyigou.dsrp.cdc.provider.api.supply;

import com.alibaba.dubbo.config.annotation.Service;
import com.yyigou.ddc.common.exception.CommonExceptionHandler;
import com.yyigou.ddc.common.message.MessageVO;
import com.yyigou.ddc.common.page.PageDto;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.ddc.common.service.ServiceBaseAbstract;
import com.yyigou.ddc.common.service.annotation.Interface;
import com.yyigou.ddc.common.service.annotation.Method;
import com.yyigou.ddc.common.service.annotation.MethodType;
import com.yyigou.dsrp.cdc.api.company.vo.CompanyShippingAddressVO;
import com.yyigou.dsrp.cdc.api.supply.SupplierLinkAddressApi;
import com.yyigou.dsrp.cdc.api.supply.dto.SupplierLinkAddressQueryDTO;
import com.yyigou.dsrp.cdc.api.supply.dto.SupplierLinkAddressSaveByOrgDTO;
import com.yyigou.dsrp.cdc.api.supply.dto.SupplierLinkAddressSaveDTO;
import com.yyigou.dsrp.cdc.common.util.OperationModel;
import com.yyigou.dsrp.cdc.service.supplier.SupplierLinkAddressService;
import com.yyigou.dsrp.cdc.service.utils.UserHandleUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Service
@RequiredArgsConstructor
@Slf4j
@Interface(name = "供应商联系地址")
public class SupplierLinkAddressProvider extends ServiceBaseAbstract implements SupplierLinkAddressApi {


    private final SupplierLinkAddressService supplierLinkAddressService;


    /**
     * 获取供应商联系地址列表
     *
     * @param supplierCode
     * @return
     */
    @Override
    @Method(aliasName = "dsrp.cdc.supplier.getSupplierLinkAddressList", name = "跨组织获取供应商联系地址列表", methodType = MethodType.QUERY, processState = 2, requestSession = false, requestAuthentication = false)
    public CallResult<List<CompanyShippingAddressVO>> getSupplierLinkAddressList(String supplierCode) {
        try {
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            return CallResult.success(MessageVO.SUCCESS, supplierLinkAddressService.getSupplierLinkAddressList(operationModel, supplierCode));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 获取供应商联系地址列表(分页)
     *
     * @param params
     * @param pageDto
     */
    @Override
    @Method(aliasName = "dsrp.cdc.supplier.findSupplierLinkAddressPage", name = "跨组织获取供应商联系地址列表", methodType = MethodType.QUERY, processState = 2, requestSession = false, requestAuthentication = false)
    public CallResult<PageVo<CompanyShippingAddressVO>> findSupplierLinkAddressPage(SupplierLinkAddressQueryDTO params, PageDto pageDto) {
        try {
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            return CallResult.success(MessageVO.SUCCESS, supplierLinkAddressService.findSupplierLinkAddressPage(operationModel, params, pageDto));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }


    @Override
    @Method(aliasName = "dsrp.cdc.supplier.saveSupplierLinkAddress", name = "创建取供应商联系地址", methodType = MethodType.QUERY, processState = 2, requestSession = false, requestAuthentication = false)
    public CallResult<CompanyShippingAddressVO> saveSupplierLinkAddress(SupplierLinkAddressSaveDTO params) {
        try {
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            return CallResult.success(MessageVO.SUCCESS, supplierLinkAddressService.saveSupplierLinkAddress(operationModel, params));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.supplier.editSupplierLinkAddress", name = "编辑取供应商联系地址", methodType = MethodType.QUERY, processState = 2, requestSession = false, requestAuthentication = false)
    public CallResult<CompanyShippingAddressVO> editSupplierLinkAddress(SupplierLinkAddressSaveDTO params) {
        try {
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            return CallResult.success(MessageVO.SUCCESS, supplierLinkAddressService.editSupplierLinkAddress(operationModel, params));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 跨组织获取供应商联系地址列表
     *
     * @param supplierCode
     * @param orgNo
     * @return
     */
    @Override
    @Method(aliasName = "dsrp.cdc.supplier.org.getSupplierLinkAddressListByOrg", name = "跨组织获取供应商联系地址列表", methodType = MethodType.QUERY, processState = 2, requestSession = false, requestAuthentication = false)
    public CallResult<List<CompanyShippingAddressVO>> getSupplierLinkAddressListByOrg(String supplierCode, String orgNo) {
        try {
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            return CallResult.success(MessageVO.SUCCESS, supplierLinkAddressService.getSupplierLinkAddressListByOrg(operationModel, supplierCode, orgNo));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 跨组织保存供应商联系地址列表
     *
     * @param params
     * @return
     */
    @Override
    @Method(aliasName = "dsrp.cdc.supplier.org.saveSupplierLinkAddressByOrg", name = "跨组织保存供应商联系地址列表", methodType = MethodType.QUERY, processState = 2, requestSession = false, requestAuthentication = false)
    public CallResult<CompanyShippingAddressVO> saveSupplierLinkAddressByOrg(SupplierLinkAddressSaveByOrgDTO params) {
        try {
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            return CallResult.success(MessageVO.SUCCESS, supplierLinkAddressService.saveSupplierLinkAddressByOrg(operationModel, params));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 跨组织编辑联系地址
     *
     * @param params
     * @return
     */
    @Override
    @Method(aliasName = "dsrp.cdc.supplier.org.editSupplierLinkAddressByOrg", name = "跨组织删除联系地址", methodType = MethodType.QUERY, processState = 2, requestSession = false, requestAuthentication = false)
    public CallResult<CompanyShippingAddressVO> editSupplierLinkAddressByOrg(SupplierLinkAddressSaveByOrgDTO params) {
        try {
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            return CallResult.success(MessageVO.SUCCESS, supplierLinkAddressService.editSupplierLinkAddressByOrg(operationModel, params));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 跨组织删除联系地址
     *
     * @param linkAddressId
     * @return
     */
    @Override
    @Method(aliasName = "dsrp.cdc.supplier.org.deleteSupplierLinkAddressByOrg", name = "跨组织删除联系地址", methodType = MethodType.QUERY, processState = 2, requestSession = false, requestAuthentication = false)
    public CallResult<Boolean> deleteSupplierLinkAddressByOrg(Long linkAddressId) {
        try {
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            return CallResult.success(MessageVO.SUCCESS, supplierLinkAddressService.deleteSupplierLinkAddressByOrg(operationModel, linkAddressId));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }
}
