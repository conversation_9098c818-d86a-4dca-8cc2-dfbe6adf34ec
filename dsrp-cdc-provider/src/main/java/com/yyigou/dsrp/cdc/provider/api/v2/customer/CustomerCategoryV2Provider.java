package com.yyigou.dsrp.cdc.provider.api.v2.customer;

import cn.hutool.core.util.StrUtil;
import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.config.annotation.Service;
import com.yyigou.ddc.common.exception.CommonExceptionHandler;
import com.yyigou.ddc.common.message.MessageVO;
import com.yyigou.ddc.common.page.PageDto;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.ddc.common.service.ServiceBaseAbstract;
import com.yyigou.ddc.common.service.annotation.Interface;
import com.yyigou.ddc.common.service.annotation.Method;
import com.yyigou.ddc.common.service.annotation.MethodType;
import com.yyigou.dsrp.cdc.api.v2.customer.CustomerCategoryV2API;
import com.yyigou.dsrp.cdc.api.v2.customer.dto.*;
import com.yyigou.dsrp.cdc.api.v2.customer.vo.CustomerCategoryTreeVO;
import com.yyigou.dsrp.cdc.api.v2.customer.vo.CustomerCategoryVO;
import com.yyigou.dsrp.cdc.common.util.OperationModel;
import com.yyigou.dsrp.cdc.model.constant.DistributedLockConstant;
import com.yyigou.dsrp.cdc.model.constant.ViewNameConstant;
import com.yyigou.dsrp.cdc.model.v2.customer.req.*;
import com.yyigou.dsrp.cdc.model.v2.supplier.req.SupplierCategoryCheckUseOrgRemovalReq;
import com.yyigou.dsrp.cdc.service.utils.RedisClientUtil;
import com.yyigou.dsrp.cdc.service.utils.UserHandleUtils;
import com.yyigou.dsrp.cdc.service.v2.customer.CustomerCategoryV2Service;
import com.yyigou.dsrp.gcs.common.util.BeanUtil;
import com.yyigou.dsrp.gcs.model.api.constant.ErrorCode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Component
@Service
@RequiredArgsConstructor
@Slf4j
@Interface(name = "客户分类档案页面接口")
public class CustomerCategoryV2Provider extends ServiceBaseAbstract implements CustomerCategoryV2API {
    @Resource(name = "customerCategoryV2Service")
    private CustomerCategoryV2Service customerCategoryV2Service;

    @Resource
    private RedisClientUtil redisClientUtil;

    @Override
    @Method(aliasName = "dsrp.cdc.customercategory.v2.queryTree", name = "客户分类获取整棵树（使用）", viewNo = ViewNameConstant.BDC_CUSTOMER_CATEGORY_VIEW, methodType = MethodType.QUERY, processState = 2, version = "1.0.1")
    public CallResult<List<CustomerCategoryTreeVO>> queryTree(CustomerCategoryQueryTreeDTO params) {
        try {
            OperationModel operationModel = UserHandleUtils.getOperationModel();

            CustomerCategoryQueryTreeReq queryReq = BeanUtil.copyFields(params, CustomerCategoryQueryTreeReq.class);
            //补充租户信息
            queryReq.setEnterpriseNo(operationModel.getEnterpriseNo());

            return CallResult.success(MessageVO.SUCCESS, customerCategoryV2Service.queryTree(operationModel, queryReq));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.customercategory.v2.queryCategoryTree", name = "客户分类获取整棵树（管理）", viewNo = ViewNameConstant.BDC_CUSTOMER_CATEGORY_VIEW, methodType = MethodType.QUERY, processState = 2, version = "1.0.1")
    public CallResult<List<CustomerCategoryTreeVO>> queryCategoryTree(CustomerCategoryQueryCategoryTreeDTO params) {
        try {
            OperationModel operationModel = UserHandleUtils.getOperationModel();

            CustomerCategoryQueryCategoryTreeReq queryReq = BeanUtil.copyFields(params, CustomerCategoryQueryCategoryTreeReq.class);
            //补充租户信息
            queryReq.setEnterpriseNo(operationModel.getEnterpriseNo());

            return CallResult.success(MessageVO.SUCCESS, customerCategoryV2Service.queryManageTree(operationModel, queryReq));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.customercategory.v2.queryUseCategoryTree", name = "客户分类获取整棵树（使用）", viewNo = ViewNameConstant.BDC_CUSTOMER_CATEGORY_VIEW, methodType = MethodType.QUERY, processState = 2, version = "1.0.1")
    public CallResult<List<CustomerCategoryTreeVO>> queryUseCategoryTree(CustomerCategoryQueryUseCategoryTreeDTO params) {
        try {
            OperationModel operationModel = UserHandleUtils.getOperationModel();

            CustomerCategoryQueryUseCategoryTreeReq queryReq = BeanUtil.copyFields(params, CustomerCategoryQueryUseCategoryTreeReq.class);
            //补充租户信息
            queryReq.setEnterpriseNo(operationModel.getEnterpriseNo());

            return CallResult.success(MessageVO.SUCCESS, customerCategoryV2Service.queryUseTree(operationModel, queryReq));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.customercategory.v2.queryListPage", name = "客户分类列表分页查", viewNo = ViewNameConstant.BDC_CUSTOMER_CATEGORY_VIEW, methodType = MethodType.QUERY, processState = 2, version = "1.0.1")
    public CallResult<PageVo<CustomerCategoryVO>> queryListPage(CustomerCategoryQueryListPageDTO params, PageDto pageDto) {
        try {
            OperationModel operationModel = UserHandleUtils.getOperationModel();

            CustomerCategoryQueryListPageReq queryReq = BeanUtil.copyFields(params, CustomerCategoryQueryListPageReq.class);
            //补充租户信息
            queryReq.setEnterpriseNo(operationModel.getEnterpriseNo());

            return CallResult.success(MessageVO.SUCCESS, customerCategoryV2Service.queryListPage(operationModel, queryReq, pageDto));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.customercategory.v2.checkUniqueName", name = "客户分类名称唯一性校验", viewNo = ViewNameConstant.BDC_CUSTOMER_CATEGORY_VIEW, methodType = MethodType.QUERY, processState = 2, version = "1.0.1")
    public CallResult<Boolean> checkUniqueName(CustomerCategoryCheckNameDTO params) {
        try {
            OperationModel operationModel = UserHandleUtils.getOperationModel();

            CustomerCategoryQueryListPageReq queryReq = BeanUtil.copyFields(params, CustomerCategoryQueryListPageReq.class);
            //补充租户信息
            queryReq.setEnterpriseNo(operationModel.getEnterpriseNo());

            return CallResult.success(MessageVO.SUCCESS, customerCategoryV2Service.checkUniqueName(operationModel, params.getNo(), params.getName(), params.getParentNo()));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.customercategory.v2.checkUniqueCode", name = "客户分类编码唯一性校验", viewNo = ViewNameConstant.BDC_CUSTOMER_CATEGORY_VIEW, methodType = MethodType.QUERY, processState = 2, version = "1.0.1")
    public CallResult<Boolean> checkUniqueCode(CustomerCategoryCheckCodeDTO params) {
        try {
            OperationModel operationModel = UserHandleUtils.getOperationModel();

            CustomerCategoryQueryListPageReq queryReq = BeanUtil.copyFields(params, CustomerCategoryQueryListPageReq.class);
            //补充租户信息
            queryReq.setEnterpriseNo(operationModel.getEnterpriseNo());

            return CallResult.success(MessageVO.SUCCESS, customerCategoryV2Service.checkUniqueCode(operationModel, params.getNo(), params.getCode()));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.customercategory.v2.checkUseOrgRemoval", name = "客户分类编码唯一性校验", viewNo = ViewNameConstant.BDC_CUSTOMER_CATEGORY_VIEW, methodType = MethodType.QUERY, processState = 2, version = "1.0.1")
    public CallResult<Boolean> checkUseOrgRemoval(CustomerCategoryCheckUseOrgRemovalDTO params) {
        try {
            OperationModel operationModel = UserHandleUtils.getOperationModel();

            CustomerCategoryCheckUseOrgRemovalReq queryReq = BeanUtil.copyFields(params, CustomerCategoryCheckUseOrgRemovalReq.class);
            //补充租户信息
            queryReq.setEnterpriseNo(operationModel.getEnterpriseNo());

            return CallResult.success(MessageVO.SUCCESS, customerCategoryV2Service.checkUseOrgRemoval(operationModel, queryReq));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.customercategory.v2.save", name = "新增客户分类", viewNo = ViewNameConstant.BDC_CUSTOMER_CATEGORY_VIEW, methodType = MethodType.ADD, processState = 2, version = "1.0.1")
    public CallResult<CustomerCategoryVO> save(CustomerCategorySaveDTO params) {
        String lock = null;
        try {
            OperationModel operationModel = UserHandleUtils.getOperationModel();

            lock = String.join(DistributedLockConstant.DELIMITER, DistributedLockConstant.CUSTOMER_CATEGORY_SAVE_LOCK_KEY, operationModel.getEnterpriseNo(), params.getCategoryCode());
            if (!redisClientUtil.lock(lock, DistributedLockConstant.DEFAULT_WAIT_TIME, DistributedLockConstant.DEFAULT_LEASE_TIME)) {
                return CallResult.failed(ErrorCode.BUSINESS_ERROR_CODE, StrUtil.format("客户分类【{}】正在处理，请稍后提交！", params.getCategoryCode()));
            }

            CustomerCategorySaveReq customerCategorySaveReq = new CustomerCategorySaveReq();
            BeanUtils.copyProperties(params, customerCategorySaveReq);
            //补充租户信息
            customerCategorySaveReq.setEnterpriseNo(operationModel.getEnterpriseNo());
            return CallResult.success(MessageVO.SUCCESS, customerCategoryV2Service.saveCustomerCategory(operationModel, customerCategorySaveReq));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        } finally {
            if (null != lock) {
                redisClientUtil.unlock(lock);
            }
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.customercategory.v2.get", name = "获取客户分类", viewNo = ViewNameConstant.BDC_CUSTOMER_CATEGORY_VIEW, methodType = MethodType.DETAIL, processState = 2, version = "1.0.1")
    public CallResult<CustomerCategoryVO> get(CustomerCategoryGetDTO params) {
        try {
            CustomerCategoryGetReq customerCategoryGetReq = new CustomerCategoryGetReq();
            BeanUtils.copyProperties(params, customerCategoryGetReq);
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            //补充租户信息
            customerCategoryGetReq.setEnterpriseNo(operationModel.getEnterpriseNo());
            return CallResult.success(MessageVO.SUCCESS, customerCategoryV2Service.getCustomerCategory(operationModel, customerCategoryGetReq));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.customercategory.v2.update", name = "更新客户分类", viewNo = ViewNameConstant.BDC_CUSTOMER_CATEGORY_VIEW, methodType = MethodType.UPDATE, processState = 2, version = "1.0.1")
    public CallResult<CustomerCategoryVO> update(CustomerCategoryUpdateDTO params) {
        try {
            CustomerCategoryUpdateReq customerCategoryUpdateReq = new CustomerCategoryUpdateReq();
            BeanUtils.copyProperties(params, customerCategoryUpdateReq);

            if (CollectionUtils.isNotEmpty(params.getUseOrgList())) {
                customerCategoryUpdateReq.setUseOrgList(new ArrayList<>(params.getUseOrgList().size()));
                for (CustomerCategoryUseOrgDTO customerCategoryUseOrgDto : params.getUseOrgList()) {
                    customerCategoryUpdateReq.getUseOrgList().add(BeanUtil.copyFields(customerCategoryUseOrgDto, CustomerCategoryUseOrgReq.class));
                }
            }

            OperationModel operationModel = UserHandleUtils.getOperationModel();
            //补充租户信息
            customerCategoryUpdateReq.setEnterpriseNo(operationModel.getEnterpriseNo());
            return CallResult.success(MessageVO.SUCCESS, customerCategoryV2Service.updateCustomerCategory(operationModel, customerCategoryUpdateReq));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.customercategory.v2.delete", name = "删除客户分类", viewNo = ViewNameConstant.BDC_CUSTOMER_CATEGORY_VIEW, methodType = MethodType.DELETE, processState = 2, version = "1.0.1")
    public CallResult<Boolean> delete(CustomerCategoryDeleteDTO params) {
        try {
            CustomerCategoryDeleteReq customerCategoryDeleteReq = new CustomerCategoryDeleteReq();
            BeanUtils.copyProperties(params, customerCategoryDeleteReq);
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            //补充租户信息
            customerCategoryDeleteReq.setEnterpriseNo(operationModel.getEnterpriseNo());
            return CallResult.success(MessageVO.SUCCESS, customerCategoryV2Service.deleteCustomerCategory(operationModel, customerCategoryDeleteReq));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.customercategory.v2.changeStatus", name = "启停客户分类", viewNo = ViewNameConstant.BDC_CUSTOMER_CATEGORY_VIEW, methodType = MethodType.UPDATE, processState = 2, version = "1.0.1")
    public CallResult<Boolean> changeStatus(CustomerCategoryChangeStatusDTO params) {
        try {
            CustomerCategoryChangeStatusReq customerCategoryChangeStatusReq = new CustomerCategoryChangeStatusReq();
            BeanUtils.copyProperties(params, customerCategoryChangeStatusReq);
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            //补充租户信息
            customerCategoryChangeStatusReq.setEnterpriseNo(operationModel.getEnterpriseNo());
            return CallResult.success(MessageVO.SUCCESS, customerCategoryV2Service.changeCustomerCategoryStatus(operationModel, customerCategoryChangeStatusReq));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }
}
