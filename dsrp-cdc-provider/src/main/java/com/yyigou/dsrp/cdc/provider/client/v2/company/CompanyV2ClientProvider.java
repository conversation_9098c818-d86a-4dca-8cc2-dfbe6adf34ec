package com.yyigou.dsrp.cdc.provider.client.v2.company;

import com.alibaba.dubbo.config.annotation.Service;
import com.yyigou.ddc.common.exception.CommonExceptionHandler;
import com.yyigou.ddc.common.message.MessageVO;
import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.dsrp.cdc.client.company.response.CompanyLinkmanResponse;
import com.yyigou.dsrp.cdc.client.company.response.CompanyShippingAddressResponse;
import com.yyigou.dsrp.cdc.client.v2.company.CompanyV2Client;
import com.yyigou.dsrp.cdc.client.v2.company.request.*;
import com.yyigou.dsrp.cdc.client.v2.company.response.Company2InfoResponse;
import com.yyigou.dsrp.cdc.client.v2.company.response.CompanyAssociatedOrgResponse;
import com.yyigou.dsrp.cdc.client.v2.company.response.CompanyBasicResponse;
import com.yyigou.dsrp.cdc.client.v2.company.response.CompanyWithCertResponse;
import com.yyigou.dsrp.cdc.manager.integration.cert.CertService;
import com.yyigou.dsrp.cdc.manager.integration.cert.res.CompanyCertResponse;
import com.yyigou.dsrp.cdc.model.v2.company.req.CompanyAssociateOrgReq;
import com.yyigou.dsrp.cdc.model.v2.company.req.CompanyCertQueryReq;
import com.yyigou.dsrp.cdc.model.v2.company.req.CompanyQueryListPageReq;
import com.yyigou.dsrp.cdc.service.v2.company.CompanyV2Service;
import com.yyigou.dsrp.cert.client.companycert.req.CompanyLicenseCertRequest;
import com.yyigou.dsrp.gcs.common.util.BeanUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 *
 **/
@Component
@Service
@RequiredArgsConstructor
@Slf4j
public class CompanyV2ClientProvider implements CompanyV2Client {


    private final CompanyV2Service companyV2Service;

    @Resource
    private CertService certService;

    @Override
    public CallResult<List<Company2InfoResponse>> findListByCompanyNoList(String enterpriseNo, List<String> companyNoList) {
        try {
            log.info("findListByCompanyNoListReq:{},{}", enterpriseNo, companyNoList);
            List<Company2InfoResponse> listByCompanyNoList = companyV2Service.findListByCompanyNoList(enterpriseNo, companyNoList);
            log.info("findListByCompanyNoListRes:{}", listByCompanyNoList);

            return CallResult.success(MessageVO.SUCCESS, listByCompanyNoList);
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    public CallResult<List<Company2InfoResponse>> findCompanyList(CompanyQueryListRequest params) {
        try {
            CompanyQueryListPageReq companyQueryListPageReq = BeanUtil.copyFields(params, CompanyQueryListPageReq.class);
            return CallResult.success(MessageVO.SUCCESS, companyV2Service.findCompanyList(companyQueryListPageReq));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    public CallResult<List<CompanyShippingAddressResponse>> getLinkAddressList(CompanyLinkRequest params) {
        try {
            log.info("getLinkAddressListReq:{}", params);
            List<CompanyShippingAddressResponse> linkAddressListBySourceNoList = companyV2Service.getLinkAddressListBySourceNoList(params.getEnterpriseNo(), params.getUseOgNo(), params.getTypeEnum(), params.getSourceList());
            log.info("getLinkAddressListRes:{}", linkAddressListBySourceNoList);

            return CallResult.success(MessageVO.SUCCESS, linkAddressListBySourceNoList);
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    public CallResult<List<CompanyLinkmanResponse>> getLinkmanList(CompanyLinkRequest params) {
        try {
            log.info("getLinkmanListReq:{}", params);
            List<CompanyLinkmanResponse> linkmanListBySourceNoList = companyV2Service.getLinkmanListBySourceNoList(params.getEnterpriseNo(), params.getUseOgNo(), params.getTypeEnum(), params.getSourceList());
            log.info("getLinkmanListRes:{}", linkmanListBySourceNoList);

            return CallResult.success(MessageVO.SUCCESS, linkmanListBySourceNoList);
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 通过组织查询企业相关信息
     *
     * @param params 组织请求参数
     * @return
     */
    @Override
    public CallResult<List<CompanyBasicResponse>> findCompanyBasicInfoByOrgList(CompanyAssociateOrgRequest params) {
        try {
            CompanyAssociateOrgReq associateOrgReq = BeanUtil.copyFields(params, CompanyAssociateOrgReq.class);
            List<CompanyBasicResponse> companyBasicInfoResponses = companyV2Service.findCompanyBasicInfoByOrgList(associateOrgReq);
            return CallResult.success(MessageVO.SUCCESS, companyBasicInfoResponses);
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    public CallResult<List<CompanyWithCertResponse>> findCompanyWithCertList(CompanyWithCertQueryRequest params) {
        try {
            CompanyCertQueryReq companyCertQueryReq = BeanUtil.copyFields(params, CompanyCertQueryReq.class);
            List<CompanyBasicResponse> companyBasicInfoResponses = companyV2Service.findCompanyBasicInfo(companyCertQueryReq);

            if (CollectionUtils.isEmpty(companyBasicInfoResponses)) {
                return CallResult.success(MessageVO.SUCCESS, Collections.emptyList());
            }

            List<CompanyWithCertResponse> result = new ArrayList<>(companyBasicInfoResponses.size());

            CompanyLicenseCertRequest companyLicenseCertRequest = BeanUtil.copyFields(params, CompanyLicenseCertRequest.class);
            List<CompanyCertResponse> companyLicenseCertList = certService.findCompanyLicenseCertList(companyLicenseCertRequest);

            Map<String, List<CompanyCertResponse>> companyCertMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(companyLicenseCertList)) {
                companyCertMap = companyLicenseCertList.stream().collect(Collectors.groupingBy(CompanyCertResponse::getSourceDocCode));
            }

            for (CompanyBasicResponse companyBasicResponse : companyBasicInfoResponses) {
                CompanyWithCertResponse companyWithCertResponse = BeanUtil.copyFields(companyBasicResponse, CompanyWithCertResponse.class);
                List<CompanyCertResponse> companyCertResponses = companyCertMap.get(companyBasicResponse.getCompanyNo());
                if (CollectionUtils.isNotEmpty(companyCertResponses)) {

                    List<com.yyigou.dsrp.cdc.client.v2.company.response.CompanyCertResponse> companyCertList = BeanUtil.copyFieldsListForJSON(companyCertResponses, com.yyigou.dsrp.cdc.client.v2.company.response.CompanyCertResponse.class);

                    companyWithCertResponse.setCompanyCertList(companyCertList);

                }

                result.add(companyWithCertResponse);
            }


            return CallResult.success(MessageVO.SUCCESS, result);
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    public CallResult<List<CompanyAssociatedOrgResponse>> findCompanyAssociatedOrgList(CompanyAssociateOrgRequest params) {
        try {
            CompanyAssociateOrgReq associateOrgReq = BeanUtil.copyFields(params, CompanyAssociateOrgReq.class);
            return CallResult.success(MessageVO.SUCCESS, companyV2Service.findCompanyAssociatedOrgList(associateOrgReq));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

}
