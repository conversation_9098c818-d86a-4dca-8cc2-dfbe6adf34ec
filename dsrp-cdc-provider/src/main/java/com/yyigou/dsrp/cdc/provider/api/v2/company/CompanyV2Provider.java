package com.yyigou.dsrp.cdc.provider.api.v2.company;

import cn.hutool.core.util.StrUtil;
import com.alibaba.dubbo.config.annotation.Service;
import com.alibaba.fastjson.JSON;
import com.yyigou.ddc.common.exception.CommonExceptionHandler;
import com.yyigou.ddc.common.message.MessageVO;
import com.yyigou.ddc.common.page.PageDto;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.ddc.common.service.ServiceBaseAbstract;
import com.yyigou.ddc.common.service.annotation.Interface;
import com.yyigou.ddc.common.service.annotation.Method;
import com.yyigou.ddc.common.service.annotation.MethodType;
import com.yyigou.ddc.common.util.ValidatorUtils;
import com.yyigou.ddc.services.ddc.uim.enums.TenantTypeEnum;
import com.yyigou.ddc.services.mq.dto.JmsHeadersDto;
import com.yyigou.ddc.services.mq.manager.MessageQueueManager;
import com.yyigou.dsrp.cdc.api.v2.company.CompanyV2API;
import com.yyigou.dsrp.cdc.api.v2.company.TenantCompanyDetailGetDTO;
import com.yyigou.dsrp.cdc.api.v2.company.dto.*;
import com.yyigou.dsrp.cdc.api.v2.company.vo.CompanyBankVO;
import com.yyigou.dsrp.cdc.api.v2.company.vo.CompanyBasicVO;
import com.yyigou.dsrp.cdc.api.v2.company.vo.CompanyDetailVO;
import com.yyigou.dsrp.cdc.common.util.OperationModel;
import com.yyigou.dsrp.cdc.model.constant.CdcMqConstant;
import com.yyigou.dsrp.cdc.model.constant.DistributedLockConstant;
import com.yyigou.dsrp.cdc.model.constant.ViewNameConstant;
import com.yyigou.dsrp.cdc.model.v2.company.req.CompanyQueryListPageForGoodsReq;
import com.yyigou.dsrp.cdc.model.v2.company.req.CompanyQueryListPageReq;
import com.yyigou.dsrp.cdc.model.v2.company.req.CompanySaveReq;
import com.yyigou.dsrp.cdc.model.v2.company.req.CompanyUpdateReq;
import com.yyigou.dsrp.cdc.service.listener.model.v2.CompanyUpdateModel;
import com.yyigou.dsrp.cdc.service.listener.model.v2.TenantCompanySaveModel;
import com.yyigou.dsrp.cdc.service.utils.RedisClientUtil;
import com.yyigou.dsrp.cdc.service.utils.UserHandleUtils;
import com.yyigou.dsrp.cdc.service.v2.company.CompanyV2Service;
import com.yyigou.dsrp.gcs.common.util.BeanUtil;
import com.yyigou.dsrp.gcs.model.api.constant.ErrorCode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.activemq.ScheduledMessage;
import org.apache.activemq.command.ActiveMQTopic;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component("companyV2Provider")
@Service
@RequiredArgsConstructor
@Slf4j
@Interface(name = "企业档案页面接口")
public class CompanyV2Provider extends ServiceBaseAbstract implements CompanyV2API {

    @Resource(name = "companyV2Service")
    private CompanyV2Service companyV2Service;

    @Resource
    private RedisClientUtil redisClientUtil;

    @Autowired
    private MessageQueueManager messageQueueManager;
    
    @Override
    @Method(aliasName = "dsrp.cdc.company.v2.findCompanyListPage", name = "分页查询企业档案列表", viewNo = ViewNameConstant.BDC_COMPANY_VIEW, methodType = MethodType.QUERY, processState = 2, version = "1.0.1")
    public CallResult<PageVo<CompanyDetailVO>> findCompanyListPage(CompanyQueryListPageDTO params, PageDto pageParams) {
        try {
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            CompanyQueryListPageReq queryReq = BeanUtil.copyFields(params, CompanyQueryListPageReq.class);
            //补充租户信息
            queryReq.setEnterpriseNo(operationModel.getEnterpriseNo());
            return CallResult.success(MessageVO.SUCCESS, companyV2Service.findCompanyListPage(queryReq, pageParams));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.company.v2.findCompanyListPageForGoods", name = "分页查询企业档案列表（添加产品）", viewNo = ViewNameConstant.BDC_COMPANY_VIEW, methodType = MethodType.QUERY, processState = 2, version = "1.0.1")
    public CallResult<PageVo<CompanyDetailVO>> findCompanyListPageForGoods(CompanyQueryListPageForGoodsDTO params, PageDto pageParams) {
        try {
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            CompanyQueryListPageForGoodsReq queryReq = BeanUtil.copyFields(params, CompanyQueryListPageForGoodsReq.class);
            //补充租户信息
            queryReq.setEnterpriseNo(operationModel.getEnterpriseNo());
            return CallResult.success(MessageVO.SUCCESS, companyV2Service.findCompanyListPageForGoods(queryReq, pageParams));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.company.v2.checkUniqueName", name = "校验企业名称的唯一性", viewNo = ViewNameConstant.BDC_COMPANY_VIEW, methodType = MethodType.QUERY, processState = 2, version = "1.0.1")
    public CallResult<Boolean> checkUniqueName(CompanyNameCheckDTO params) {
        try {
            ValidatorUtils.checkEmptyThrowEx(params.getCompanyName(), "企业名称");

            OperationModel operationModel = UserHandleUtils.getOperationModel();
            Boolean unique = companyV2Service.checkName(operationModel, params.getCompanyNo(), params.getCompanyName());
            return CallResult.success(unique?MessageVO.SUCCESS:String.format("已存在相同的企业名称[%s]", params.getCompanyName()), unique);
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.company.v2.checkUniqueUnifiedSocialCode", name = "校验统一社会信用代码的唯一性", viewNo = ViewNameConstant.BDC_COMPANY_VIEW, methodType = MethodType.QUERY, processState = 2, version = "1.0.1")
    public CallResult<Boolean> checkUniqueUnifiedSocialCode(CompanyUnifiedSocialCodeCheckDTO params) {
        try {
            ValidatorUtils.checkEmptyThrowEx(params.getUnifiedSocialCode(), "统一社会信用代码");

            OperationModel operationModel = UserHandleUtils.getOperationModel();
            return CallResult.success(MessageVO.SUCCESS, companyV2Service.checkUnifiedSocialCode(operationModel, params.getCompanyNo(), params.getUnifiedSocialCode(), params.getFactoryType()));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.company.v2.saveCompany", name = "保存企业信息", viewNo = ViewNameConstant.BDC_COMPANY_VIEW, methodType = MethodType.ADD, processState = 2, version = "1.0.1")
    public CallResult<CompanyBasicVO> saveCompany(CompanySaveDTO params) {
        String lock = null;
        try {
            OperationModel operationModel = UserHandleUtils.getOperationModel();

            lock = String.join(DistributedLockConstant.DELIMITER, DistributedLockConstant.COMPANY_SAVE_LOCK_KEY, operationModel.getEnterpriseNo(), params.getCompanyName());
            if (!redisClientUtil.lock(lock, DistributedLockConstant.DEFAULT_WAIT_TIME, DistributedLockConstant.DEFAULT_LEASE_TIME)) {
                return CallResult.failed(ErrorCode.BUSINESS_ERROR_CODE, StrUtil.format("企业【{}】正在处理，请稍后提交！", params.getCompanyName()));
            }

            CompanySaveReq companySaveReq = new CompanySaveReq();
            BeanUtils.copyProperties(params, companySaveReq);
            return CallResult.success(MessageVO.SUCCESS, companyV2Service.saveCompany(operationModel, companySaveReq));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        } finally {
            if (null != lock) {
                redisClientUtil.unlock(lock);
            }
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.company.v2.getCompanyDetail", name = "获取企业详情", viewNo = ViewNameConstant.BDC_COMPANY_VIEW, methodType = MethodType.QUERY, processState = 2, version = "1.0.1")
    public CallResult<CompanyDetailVO> getCompanyDetail(CompanyDetailGetDTO params) {
        try {
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            return CallResult.success(MessageVO.SUCCESS, companyV2Service.getDetailByEnterpriseAndCompanyNo(operationModel.getEnterpriseNo(), params.getCompanyNo()));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.company.v2.updateCompany", name = "更新企业信息", viewNo = ViewNameConstant.BDC_COMPANY_VIEW, methodType = MethodType.UPDATE, processState = 2, version = "1.0.1")
    public CallResult<Boolean> updateCompany(CompanyUpdateDTO params) {
        try {
            CompanyUpdateReq companyUpdateReq = new CompanyUpdateReq();
            BeanUtils.copyProperties(params, companyUpdateReq);
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            CompanyUpdateModel companyUpdateModel = companyV2Service.updateCompany(operationModel, companyUpdateReq);
            // 修改成功，发送消息，处理唯一客商名称修改
            if (companyUpdateModel.getUpdateSuccess()) {
                JmsHeadersDto jmsHeadersDto = new JmsHeadersDto();
                jmsHeadersDto.setSessionTransacted(true);
                Map<String, Object> customPropMap = new HashMap<>();
                customPropMap.put(ScheduledMessage.AMQ_SCHEDULED_DELAY, 5000L);
                messageQueueManager.produceMessage(new ActiveMQTopic(CdcMqConstant.CDC_COMPANY_UPDATE_TOPIC_V2), JSON.toJSONString(companyUpdateModel), customPropMap, jmsHeadersDto);
            }
            return CallResult.success(MessageVO.SUCCESS, companyUpdateModel.getUpdateSuccess());
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.company.v2.findCompanyListPageForQuote", name = "引用企业档案进行分页查询", viewNo = ViewNameConstant.BDC_COMPANY_VIEW, methodType = MethodType.QUERY, processState = 2, version = "1.0.1")
    public CallResult<PageVo<CompanyDetailVO>> findCompanyListPageForQuote(CompanyQuoteQueryDTO params, PageDto pageParams) {
        try {
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            CompanyQueryListPageReq queryReq = new CompanyQueryListPageReq();
            //补充租户信息
            queryReq.setEnterpriseNo(operationModel.getEnterpriseNo());

            queryReq.setCompanyNameKeywords(params.getCompanyNameKeywords());
            queryReq.setUnifiedSocialCodeKeywords(params.getUnifiedSocialCodeKeywords());
            queryReq.setPartnership(params.getPartnership());
            return CallResult.success(MessageVO.SUCCESS, companyV2Service.findCompanyListPage(queryReq, pageParams));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.company.v2.getCompanyByName", name = "根据企业名称获取企业信息", viewNo = ViewNameConstant.BDC_COMPANY_VIEW, methodType = MethodType.QUERY, processState = 2, version = "1.0.1")
    public CallResult<CompanyDetailVO> getCompanyByName(CompanyQueryDTO params) {
        try {
            ValidatorUtils.checkEmptyThrowEx(params.getCompanyName(), "企业名称");

            OperationModel operationModel = UserHandleUtils.getOperationModel();
            return CallResult.success(MessageVO.SUCCESS, companyV2Service.getDetailByEnterpriseAndCompanyName(operationModel.getEnterpriseNo(), params.getCompanyName()));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.company.v2.queryCompanyByName", name = "根据企业名称模糊搜索", viewNo = ViewNameConstant.BDC_COMPANY_VIEW, methodType = MethodType.QUERY, processState = 2, version = "1.0.1")
    public CallResult<PageVo<CompanyDetailVO>> queryCompanyByName(CompanyQueryDTO params, PageDto pageParams) {
        try {
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            CompanyQueryListPageReq queryReq = new CompanyQueryListPageReq();
            //补充租户信息
            queryReq.setEnterpriseNo(operationModel.getEnterpriseNo());
            queryReq.setCompanyNameKeywords(params.getCompanyName());
            return CallResult.success(MessageVO.SUCCESS, companyV2Service.findCompanyListPage(queryReq, pageParams));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.company.v2.findBankList", name = "查询银行列表", viewNo = ViewNameConstant.BDC_COMPANY_VIEW, methodType = MethodType.QUERY, processState = 2, version = "1.0.1")
    public CallResult<List<CompanyBankVO>> findBankList(CompanyBankQueryDTO params) {
        try {
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            //补充租户信息
            return CallResult.success(MessageVO.SUCCESS, companyV2Service.findBankList(operationModel.getEnterpriseNo(), params.getCompanyNo()));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.company.v2.getTenantCompany", name = "查询本企业证照", viewNo = ViewNameConstant.BDC_COMPANY_VIEW, methodType = MethodType.QUERY, processState = 1, version = "1.0.1" ,requestAuthentication = false)
    public CallResult<CompanyDetailVO> getTenantCompany(TenantCompanyDetailGetDTO params) {
        try {
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            if (!TenantTypeEnum.ENTERPRISE.getValue().equals(operationModel.getTenantType())) {
                return CallResult.failed(ErrorCode.BUSINESS_ERROR_CODE, "当前租户不支持");
            }

            return CallResult.success(MessageVO.SUCCESS, companyV2Service.getTenantCompany(operationModel.getEnterpriseNo()));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }


    @Override
    @Method(aliasName = "dsrp.cdc.company.v2.saveTenantCompany", name = "编辑本企业证照", viewNo = ViewNameConstant.BDC_COMPANY_VIEW, methodType = MethodType.ADD, processState = 1, version = "1.0.1")
    public CallResult<CompanyBasicVO> saveTenantCompany(CompanyUpdateDTO params) {
        try {
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            if (!TenantTypeEnum.ENTERPRISE.getValue().equals(operationModel.getTenantType())) {
                return CallResult.failed(ErrorCode.BUSINESS_ERROR_CODE, "当前租户不支持");
            }
            params.setCompanyNo(operationModel.getEnterpriseNo());

            CompanyUpdateReq companyUpdateReq = new CompanyUpdateReq();
            BeanUtils.copyProperties(params, companyUpdateReq);
            TenantCompanySaveModel tenantCompanySaveModel = companyV2Service.saveTenantCompany(operationModel, companyUpdateReq);
            if (tenantCompanySaveModel.getUpdate()) {
                CompanyUpdateModel companyUpdateModel = tenantCompanySaveModel.getCompanyUpdateModel();
                // 修改成功，发送消息，处理唯一客商名称修改
                if (companyUpdateModel.getUpdateSuccess()) {
                    JmsHeadersDto jmsHeadersDto = new JmsHeadersDto();
                    jmsHeadersDto.setSessionTransacted(true);
                    Map<String, Object> customPropMap = new HashMap<>();
                    customPropMap.put(ScheduledMessage.AMQ_SCHEDULED_DELAY, 5000L);
                    messageQueueManager.produceMessage(new ActiveMQTopic(CdcMqConstant.CDC_COMPANY_UPDATE_TOPIC_V2), JSON.toJSONString(companyUpdateModel), customPropMap, jmsHeadersDto);
                }
            }
            return CallResult.success(MessageVO.SUCCESS, tenantCompanySaveModel.getCompanyBasicVO());
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }
}
