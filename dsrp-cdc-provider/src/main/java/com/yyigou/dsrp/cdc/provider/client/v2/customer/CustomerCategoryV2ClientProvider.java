package com.yyigou.dsrp.cdc.provider.client.v2.customer;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.config.annotation.Service;
import com.yyigou.ddc.common.exception.CommonExceptionHandler;
import com.yyigou.ddc.common.message.MessageVO;
import com.yyigou.ddc.common.page.PageDto;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.ddc.common.util.ValidatorUtils;
import com.yyigou.ddc.services.ddc.uap.domain.dto.grade.GradeDocAssignMessage;
import com.yyigou.ddc.services.ddc.uap.domain.vo.grade.GradeRpcAssignVO;
import com.yyigou.dsrp.cdc.api.v2.customer.vo.CustomerCategoryVO;
import com.yyigou.dsrp.cdc.client.customer.request.CustomerNameRequest;
import com.yyigou.dsrp.cdc.client.customer.request.CustomerNoRequest;
import com.yyigou.dsrp.cdc.client.v2.customer.CustomerCategoryRpcClient;
import com.yyigou.dsrp.cdc.client.v2.customer.CustomerRpcClient;
import com.yyigou.dsrp.cdc.client.v2.customer.request.*;
import com.yyigou.dsrp.cdc.client.v2.customer.response.CustomerAssignExeResponse;
import com.yyigou.dsrp.cdc.client.v2.customer.response.CustomerBizResponse;
import com.yyigou.dsrp.cdc.client.v2.customer.response.CustomerCategoryResponse;
import com.yyigou.dsrp.cdc.common.util.BeanUtil;
import com.yyigou.dsrp.cdc.dao.v2.customer.entity.CustomerCategoryV2;
import com.yyigou.dsrp.cdc.model.v2.customer.req.CustomerCategoryQueryListPageReq;
import com.yyigou.dsrp.cdc.model.v2.customer.req.CustomerCodeOrgPairListReq;
import com.yyigou.dsrp.cdc.model.v2.customer.req.CustomerGspStatusReq;
import com.yyigou.dsrp.cdc.model.v2.customer.req.CustomerStandardQueryReq;
import com.yyigou.dsrp.cdc.service.v2.customer.CustomerCategoryV2Service;
import com.yyigou.dsrp.cdc.service.v2.customer.CustomerV2Service;
import com.yyigou.dsrp.gcs.model.api.constant.ErrorCode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Component
@RequiredArgsConstructor
@Slf4j
@Service
public class CustomerCategoryV2ClientProvider implements CustomerCategoryRpcClient {
    @Resource
    private CustomerCategoryV2Service customerCategoryV2Service;

    @Override
    public CallResult<List<CustomerCategoryResponse>> selectCustomerCategoryListByNo(String enterpriseNo, List<String> customerCategoryNoList) {
        try {
            log.info("selectCustomerCategoryListByNoReq:{},{}", enterpriseNo, customerCategoryNoList);

            List<CustomerCategoryV2> list = customerCategoryV2Service.getByNoList(enterpriseNo, customerCategoryNoList);

            log.info("selectCustomerCategoryListByNoRes:{}", list);

            return CallResult.success(MessageVO.SUCCESS, BeanUtil.copyFieldsList(list, CustomerCategoryResponse.class));

        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    public CallResult<List<CustomerCategoryResponse>> selectCustomerCategoryList(CustomerCategoryRequest request) {
        try {
            log.info("selectCustomerCategoryListReq:{}", request);

            CustomerCategoryQueryListPageReq customerCategoryQueryListPageReq = new CustomerCategoryQueryListPageReq();
            customerCategoryQueryListPageReq.setEnterpriseNo(request.getEnterpriseNo());
            customerCategoryQueryListPageReq.setUseOrgNo(request.getUseOrgNo());
            customerCategoryQueryListPageReq.setIdList(request.getNoList());

            List<CustomerCategoryVO> list = customerCategoryV2Service.queryList(customerCategoryQueryListPageReq);

            log.info("selectCustomerCategoryListRes:{}", list);

            return CallResult.success(MessageVO.SUCCESS, BeanUtil.copyFieldsList(list, CustomerCategoryResponse.class));

        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }
}
