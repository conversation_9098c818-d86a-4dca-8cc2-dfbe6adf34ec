package com.yyigou.dsrp.cdc.provider.client.v2.customer;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.config.annotation.Service;
import com.yyigou.ddc.common.exception.CommonExceptionHandler;
import com.yyigou.ddc.common.message.MessageVO;
import com.yyigou.ddc.common.page.PageDto;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.ddc.common.util.ValidatorUtils;
import com.yyigou.ddc.services.ddc.uap.domain.dto.grade.GradeDocAssignMessage;
import com.yyigou.ddc.services.ddc.uap.domain.vo.grade.GradeRpcAssignVO;
import com.yyigou.dsrp.cdc.api.v2.customer.vo.CustomerApplyDetailVO;
import com.yyigou.dsrp.cdc.api.v2.customer.vo.CustomerInvoiceVO;
import com.yyigou.dsrp.cdc.client.customer.request.CustomerInvoiceQueryRequest;
import com.yyigou.dsrp.cdc.client.customer.request.CustomerNameRequest;
import com.yyigou.dsrp.cdc.client.customer.request.CustomerNoRequest;
import com.yyigou.dsrp.cdc.client.v2.customer.CustomerApplyRpcClient;
import com.yyigou.dsrp.cdc.client.v2.customer.CustomerRpcClient;
import com.yyigou.dsrp.cdc.client.v2.customer.request.*;
import com.yyigou.dsrp.cdc.client.v2.customer.response.CustomerApplyResponse;
import com.yyigou.dsrp.cdc.client.v2.customer.response.CustomerAssignExeResponse;
import com.yyigou.dsrp.cdc.client.v2.customer.response.CustomerBizResponse;
import com.yyigou.dsrp.cdc.client.v2.customer.response.CustomerInvoiceResponse;
import com.yyigou.dsrp.cdc.common.enums.InvoiceTypeEnum;
import com.yyigou.dsrp.cdc.common.util.BeanUtil;
import com.yyigou.dsrp.cdc.common.util.OperationModel;
import com.yyigou.dsrp.cdc.model.v2.customer.req.*;
import com.yyigou.dsrp.cdc.service.v2.customer.CustomerApplyService;
import com.yyigou.dsrp.cdc.service.v2.customer.CustomerV2Service;
import com.yyigou.dsrp.gcs.model.api.constant.ErrorCode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Component
@RequiredArgsConstructor
@Slf4j
@Service
public class CustomerApplyV2ClientProvider implements CustomerApplyRpcClient {
    @Resource
    private CustomerApplyService customerApplyService;


    @Override
    public CallResult<CustomerApplyResponse> getCustomerApplyByNo(String enterpriseNo, String applyInstanceNo) {
        try {
            log.info("getCustomerApplyByNoReq:{},{}", enterpriseNo, applyInstanceNo);

            OperationModel operationModel = new OperationModel();
            operationModel.setEnterpriseNo(enterpriseNo);

            CustomerApplyGetReq customerApplyGetReq = new CustomerApplyGetReq();
            customerApplyGetReq.setApplyInstanceNo(applyInstanceNo);

            CustomerApplyDetailVO customerApplyDetailVO = customerApplyService.getDetail(operationModel, customerApplyGetReq);

            if (null == customerApplyDetailVO) {
                return CallResult.failed(ErrorCode.BUSINESS_ERROR_CODE, "无法找到客户档案申请单");
            }

            CustomerApplyResponse customerApplyResponse = BeanUtil.copyFields(customerApplyDetailVO, CustomerApplyResponse.class);

            log.info("getCustomerApplyByNoRes:{}", customerApplyResponse);

            return CallResult.success(MessageVO.SUCCESS, customerApplyResponse);

        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }
}
