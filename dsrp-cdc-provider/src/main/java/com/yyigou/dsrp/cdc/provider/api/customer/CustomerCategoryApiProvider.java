package com.yyigou.dsrp.cdc.provider.api.customer;

import com.alibaba.dubbo.config.annotation.Service;
import com.yyigou.ddc.common.exception.CommonExceptionHandler;
import com.yyigou.ddc.common.message.MessageVO;
import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.ddc.common.service.ServiceBaseAbstract;
import com.yyigou.ddc.common.service.annotation.Interface;
import com.yyigou.ddc.common.service.annotation.Method;
import com.yyigou.ddc.common.service.annotation.MethodType;
import com.yyigou.dsrp.cdc.api.customer.CustomerCategoryApi;
import com.yyigou.dsrp.cdc.api.customer.vo.CustomerCategoryTree;
import com.yyigou.dsrp.cdc.common.util.OperationModel;
import com.yyigou.dsrp.cdc.service.customer.CustomerCategoryService;
import com.yyigou.dsrp.cdc.service.utils.UserHandleUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Service
@RequiredArgsConstructor
@Slf4j
@Interface(name = "客户分类档案页面接口")
public class CustomerCategoryApiProvider extends ServiceBaseAbstract implements CustomerCategoryApi {

    private final CustomerCategoryService customerCategoryService;


    @Override
    @Method(aliasName = "dsrp.cdc.customerCategory.queryTree", name = "客户分类获取整棵树", methodType = MethodType.QUERY, processState = 2, requestSession = false, requestAuthentication = false)
    public CallResult<List<CustomerCategoryTree>> queryTree() {
        try {
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            return CallResult.success(MessageVO.SUCCESS, customerCategoryService.queryTree(operationModel));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.customerCategory.group.queryTree", name = "客户分类获取集团整棵树", methodType = MethodType.QUERY, processState = 2, requestSession = false, requestAuthentication = false)
    public CallResult<List<CustomerCategoryTree>> queryGroupTree() {
        try {
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            return CallResult.success(MessageVO.SUCCESS, customerCategoryService.queryGroupTree(operationModel));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }


    @Override
    @Method(aliasName = "dsrp.cdc.customerCategory.org.queryTree", name = "多组织获取客户分类", methodType = MethodType.QUERY, processState = 2, requestSession = false, requestAuthentication = false)
    public CallResult<List<CustomerCategoryTree>> queryOrgTree(String orgNo) {
        try {
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            return CallResult.success(MessageVO.SUCCESS, customerCategoryService.queryOrgTree(operationModel, orgNo));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }
}
