package com.yyigou.dsrp.cdc.provider.client.v2.supplier;

import com.alibaba.dubbo.config.annotation.Service;
import com.yyigou.ddc.common.exception.BusinessException;
import com.yyigou.ddc.common.page.PageDto;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.ddc.meta.base.service.referrpc.ReferQueryCondition;
import com.yyigou.ddc.meta.base.service.referrpc.ReferRpcReqDto;
import com.yyigou.ddc.meta.base.service.referrpc.service.IReferQueryFunc;
import com.yyigou.dsrp.cdc.api.v2.supplier.vo.SupplierPageVO;
import com.yyigou.dsrp.cdc.common.util.OperationModel;
import com.yyigou.dsrp.cdc.model.v2.supplier.req.SupplierFindQueryListPageForTenantReq;
import com.yyigou.dsrp.cdc.service.utils.UserHandleUtils;
import com.yyigou.dsrp.cdc.service.v2.supplier.SupplierV2Service;
import com.yyigou.dsrp.gcs.common.util.ObjectToMapUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 *  查询租户中的供应商档案(只返回管理组织的基本信息，但组织权限还是使用组织)
 */
@Component
@Service(group = "cdcSupplierForTenant")
@RequiredArgsConstructor
@Slf4j
public class SupplierV2ForTenantRefClientProvider implements IReferQueryFunc {
    private static final String FIELD_USE_ORG_NO = "use_org_no";

    private final SupplierV2Service supplierV2Service;


    /**
     * 查询租户供应商档案(类似集团库的查询效果)
     * 比如用户的组织权限是 a b c, 档案A的管理组织是d ，分派给 a b c，那么用户有档案A在使用组织 a b c中的权限（3条）。
     * 但接口只会返回组织A的基本信息（1条），管理组织显示d。
     * 在实现上很简单，
     *
     * @param referRpcReqDto
     * @return
     */
    @Override
    public PageVo<Map<String, Object>> queryReferData(ReferRpcReqDto referRpcReqDto) {
        log.warn("cdcSupplierForTenantReferRpcReqDto={}", referRpcReqDto);

        if (null == referRpcReqDto.getPageDto()) {
            PageDto pageDto = new PageDto();
            pageDto.setPageIndex(1);
            pageDto.setPageSize(100000);
            referRpcReqDto.setPageDto(pageDto);
        }
        OperationModel operationModel = UserHandleUtils.getOperationModel();

        SupplierFindQueryListPageForTenantReq supplierFindQueryListPageForTenantReq = new SupplierFindQueryListPageForTenantReq();
        for (ReferQueryCondition condition : referRpcReqDto.getConditions()) {
            if(condition.getColumn().equals(FIELD_USE_ORG_NO) && StringUtils.isNotBlank(condition.getValue())) {
                List<String> useOrgNoList = Arrays.stream(condition.getValue().split(",")).map(e -> e.replaceAll("'","")).collect(Collectors.toList());
                // 编码实现数据权限
                supplierFindQueryListPageForTenantReq.setUseOrgNoList(useOrgNoList);
            }
        }

        if (CollectionUtils.isEmpty(supplierFindQueryListPageForTenantReq.getUseOrgNoList())) {
            if(!operationModel.getSingleOrgFlag()) {
                throw new BusinessException("使用组织为空");
            }
        }

        supplierFindQueryListPageForTenantReq.setEnterpriseNo(operationModel.getEnterpriseNo());
        PageVo<SupplierPageVO> listPage = supplierV2Service.findListPageForTenant(supplierFindQueryListPageForTenantReq, referRpcReqDto.getPageDto());
        List<Map<String, Object>> rows = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(listPage.getRows())) {
            for (SupplierPageVO row : listPage.getRows()) {
                try {
                    // 将RegistrationCertPageVO转成Map，其中类属性如果是驼峰命名法，Map中的key用下划线分隔
                    Map<String, Object> map = ObjectToMapUtil.convertObjectToMap(row, SupplierPageVO.class);
                    rows.add(map);
                } catch (Exception e) {
                    log.error("映射异常", e);
                    throw new RuntimeException("映射异常");
                }
            }
        }
        return new PageVo<>(listPage.getPageIndex(), listPage.getPageSize(), listPage.getTotal(), rows);
    }



}
