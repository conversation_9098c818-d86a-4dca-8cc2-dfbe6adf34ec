package com.yyigou.dsrp.cdc.provider.client.company;

import com.alibaba.dubbo.config.annotation.Service;
import com.yyigou.ddc.common.exception.CommonExceptionHandler;
import com.yyigou.ddc.common.message.MessageVO;
import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.dsrp.cdc.client.company.CompanyClient;
import com.yyigou.dsrp.cdc.client.company.response.CompanyInfoResponse;
import com.yyigou.dsrp.cdc.service.company.CompanyService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
@Service
@RequiredArgsConstructor
@Slf4j
public class CompanyClientProvider implements CompanyClient {

    @Resource
    private CompanyService companyService;


    /**
     * 根据查询条件查询公司信息
     *
     * @param request
     */
    @Override
    public CallResult<List<CompanyInfoResponse>> findListByCompanyNoListIgnoringEnterpriseNo(List<String> companyNoList) {
        try {
            return CallResult.success(MessageVO.SUCCESS, companyService.findListByCompanyNoListIgnoringEnterpriseNo(companyNoList));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    public CallResult<List<CompanyInfoResponse>> findListByCompanyNoList(String enterpriseNo, List<String> companyNoList) {
        try {
            return CallResult.success(MessageVO.SUCCESS, companyService.findListByCompanyNoList(enterpriseNo, companyNoList));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    public CallResult<List<CompanyInfoResponse>> findListByCompanyCodeList(String enterpriseNo, List<String> companyCodeList) {
        try {
            return CallResult.success(MessageVO.SUCCESS, companyService.findListByCompanyCodeList(enterpriseNo, companyCodeList));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

}
