package com.yyigou.dsrp.cdc.provider.api.v2.supplier;

import cn.hutool.core.util.StrUtil;
import com.alibaba.dubbo.config.annotation.Service;
import com.yyigou.ddc.common.exception.CommonExceptionHandler;
import com.yyigou.ddc.common.message.MessageVO;
import com.yyigou.ddc.common.page.PageDto;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.ddc.common.service.ServiceBaseAbstract;
import com.yyigou.ddc.common.service.annotation.Interface;
import com.yyigou.ddc.common.service.annotation.Method;
import com.yyigou.ddc.common.service.annotation.MethodType;
import com.yyigou.ddc.common.service.annotation.Parameter;
import com.yyigou.ddc.services.ddc.task.message.incoming.DataImportDataSpiceMessage;
import com.yyigou.ddc.services.ddc.task.message.incoming.UapExcelImportDataSpiceMessage;
import com.yyigou.ddc.services.ddc.task.vo.incoming.ImportTaskDataSpiceVO;
import com.yyigou.ddc.services.ddc.uim.vo.OrganizationVo;
import com.yyigou.dsrp.cdc.api.v2.supplier.SupplierV2API;
import com.yyigou.dsrp.cdc.api.v2.supplier.dto.*;
import com.yyigou.dsrp.cdc.api.v2.supplier.vo.*;
import com.yyigou.dsrp.cdc.common.util.OperationModel;
import com.yyigou.dsrp.cdc.model.constant.DistributedLockConstant;
import com.yyigou.dsrp.cdc.model.constant.ViewNameConstant;
import com.yyigou.dsrp.cdc.model.v2.company.req.CompanyBankReq;
import com.yyigou.dsrp.cdc.model.v2.company.req.CompanyLinkmanReq;
import com.yyigou.dsrp.cdc.model.v2.company.req.CompanyShippingAddressReq;
import com.yyigou.dsrp.cdc.model.v2.supplier.req.*;
import com.yyigou.dsrp.cdc.service.utils.RedisClientUtil;
import com.yyigou.dsrp.cdc.service.utils.UserHandleUtils;
import com.yyigou.dsrp.cdc.service.v2.supplier.SupplierV2Service;
import com.yyigou.dsrp.cdc.service.v2.supplier.impl.SupplierV2ImportServiceImpl;
import com.yyigou.dsrp.gcs.common.util.BeanUtil;
import com.yyigou.dsrp.gcs.common.util.ValidatorUtils;
import com.yyigou.dsrp.gcs.model.api.constant.ErrorCode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
@Service
@RequiredArgsConstructor
@Slf4j
@Interface(name = "供应商档案页面接口")
public class SupplierV2Provider extends ServiceBaseAbstract implements SupplierV2API {
    @Resource(name = "supplierV2Service")
    private SupplierV2Service supplierV2Service;

    @Resource(name = "supplierV2ImportService")
    private SupplierV2ImportServiceImpl supplierV2ImportService;

    @Resource
    private RedisClientUtil redisClientUtil;

    @Override
    @Method(aliasName = "dsrp.cdc.supplier.v2.manageFindListPage", name = "供应商档案列表查询（管理）", orgAuth = true, viewNo = ViewNameConstant.BDC_SUPPLIER_VIEW, methodType = MethodType.QUERY, processState = 2, version = "1.0.1")
    public CallResult<PageVo<SupplierPageVO>> manageFindListPage(SupplierManageQueryListPageDTO params, PageDto pageParams) {
        try {
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            SupplierManageQueryListPageReq queryReq = BeanUtil.copyFields(params, SupplierManageQueryListPageReq.class);
            //补充租户信息
            queryReq.setEnterpriseNo(operationModel.getEnterpriseNo());
            return CallResult.success(MessageVO.SUCCESS, supplierV2Service.manageFindListPage(operationModel, queryReq, pageParams));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.supplier.v2.useFindListPage", name = "供应商档案列表查询（使用）", orgAuth = true, viewNo = ViewNameConstant.BDC_SUPPLIER_VIEW, methodType = MethodType.QUERY, processState = 2, version = "1.0.1")
    public CallResult<PageVo<SupplierPageVO>> useFindListPage(SupplierUseQueryListPageDTO params, PageDto pageParams) {
        try {
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            SupplierUseQueryListPageReq queryReq = BeanUtil.copyFields(params, SupplierUseQueryListPageReq.class);
            //补充租户信息
            queryReq.setEnterpriseNo(operationModel.getEnterpriseNo());
            return CallResult.success(MessageVO.SUCCESS, supplierV2Service.useFindListPage(operationModel, queryReq, pageParams));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.supplier.v2.useFindListPageNoAuthZ", name = "供应商档案列表查询（使用）", viewNo = ViewNameConstant.BDC_SUPPLIER_VIEW, methodType = MethodType.QUERY, processState = 2, version = "1.0.1")
    public CallResult<PageVo<SupplierPageVO>> useFindListPageNoAuthZ(SupplierUseQueryListPageNoAuthZDTO params, PageDto pageParams) {
        try {
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            SupplierUseQueryListPageNoAuthZReq queryReq = BeanUtil.copyFields(params, SupplierUseQueryListPageNoAuthZReq.class);
            //补充租户信息
            queryReq.setEnterpriseNo(operationModel.getEnterpriseNo());
            return CallResult.success(MessageVO.SUCCESS, supplierV2Service.useFindListPageNoAuthZ(operationModel, queryReq, pageParams));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.supplier.v2.reverseUseFindListPageNoAuthZ", name = "供应商档案列表查询（使用）", viewNo = ViewNameConstant.BDC_SUPPLIER_VIEW, methodType = MethodType.QUERY, processState = 2, version = "1.0.1")
    public CallResult<PageVo<SupplierReversePageVO>> reverseUseFindListPageNoAuthZ(SupplierUseQueryListPageNoAuthZDTO params, PageDto pageParams) {
        try {
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            SupplierReverseUseQueryListPageNoAuthZReq queryReq = BeanUtil.copyFields(params, SupplierReverseUseQueryListPageNoAuthZReq.class);
            //补充租户信息
            queryReq.setEnterpriseNo(operationModel.getEnterpriseNo());
            return CallResult.success(MessageVO.SUCCESS, supplierV2Service.reverseUseFindListPageNoAuthZ(operationModel, queryReq, pageParams));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }


    @Override
    @Method(aliasName = "dsrp.cdc.supplier.v2.manageDelete", name = "供应商档案删除", viewNo = ViewNameConstant.BDC_SUPPLIER_VIEW, methodType = MethodType.DELETE, processState = 2, version = "1.0.1")
    public CallResult<Boolean> manageDeleteSupplier(SupplierDeleteDTO params) {
        try {
            OperationModel operationModel = com.yyigou.dsrp.cdc.service.utils.UserHandleUtils.getOperationModel();
            SupplierDeleteReq supplierDeleteReq = BeanUtil.copyFields(params, SupplierDeleteReq.class);
            //补充租户信息
            supplierDeleteReq.setEnterpriseNo(operationModel.getEnterpriseNo());

            return CallResult.success(MessageVO.SUCCESS, supplierV2Service.manageDeleteSupplier(operationModel, supplierDeleteReq));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }


    @Override
    @Method(aliasName = "dsrp.cdc.supplier.v2.getDetail", name = "供应商档案获取", viewNo = ViewNameConstant.BDC_SUPPLIER_VIEW, methodType = MethodType.DETAIL, processState = 2, version = "1.0.1")
    public CallResult<SupplierVO> getSupplier(SupplierGetDTO params) {
        try {
            OperationModel operationModel = com.yyigou.dsrp.cdc.service.utils.UserHandleUtils.getOperationModel();

            SupplierGetReq supplierGetReq = BeanUtil.copyFields(params, SupplierGetReq.class);
            //补充租户信息
            supplierGetReq.setEnterpriseNo(operationModel.getEnterpriseNo());
            return CallResult.success(MessageVO.SUCCESS, supplierV2Service.getSupplier(operationModel, supplierGetReq));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.supplier.v2.changeStatus", name = "供应商档案启停", viewNo = ViewNameConstant.BDC_SUPPLIER_VIEW, methodType = MethodType.UPDATE, processState = 2, version = "1.0.1")
    public CallResult<Boolean> changeSupplierStatus(SupplierChangeStatusDTO params) {
        try {
            OperationModel operationModel = com.yyigou.dsrp.cdc.service.utils.UserHandleUtils.getOperationModel();

            SupplierChangeStatusReq supplierChangeStatusReq = BeanUtil.copyFields(params, SupplierChangeStatusReq.class);
            //补充租户信息
            supplierChangeStatusReq.setEnterpriseNo(operationModel.getEnterpriseNo());
            return CallResult.success(MessageVO.SUCCESS, supplierV2Service.changeSupplierStatus(operationModel, supplierChangeStatusReq));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.supplier.v2.getAssign", name = "查询供应商档案分派", viewNo = ViewNameConstant.BDC_SUPPLIER_VIEW, methodType = MethodType.QUERY, processState = 2, version = "1.0.1")
    public CallResult<List<SupplierAssignVO>> getAssignSupplier(SupplierGetAssignDTO params) {
        try {
            OperationModel operationModel = com.yyigou.dsrp.cdc.service.utils.UserHandleUtils.getOperationModel();

            SupplierGetAssignReq supplierAssignReq = BeanUtil.copyFields(params, SupplierGetAssignReq.class);
            //补充租户信息
            supplierAssignReq.setEnterpriseNo(operationModel.getEnterpriseNo());
            return CallResult.success(MessageVO.SUCCESS, supplierV2Service.getAssignSupplier(operationModel, supplierAssignReq));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.supplier.v2.getPendingCount", name = "查询待处理供应商数量", methodType = MethodType.QUERY, viewNo = ViewNameConstant.BDC_SUPPLIER_VIEW, orgAuth = true, processState = 2, version = "1.0.1")
    public CallResult<Long> getPendingCount() {
        try {
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            return CallResult.success(MessageVO.SUCCESS, supplierV2Service.getPendingCount(operationModel));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.supplier.v2.checkOnlyCode", name = "校验供应商编码是否存在", methodType = MethodType.QUERY, viewNo = ViewNameConstant.BDC_SUPPLIER_VIEW, orgAuth = true, processState = 2, version = "1.0.1")
    public CallResult<Boolean> checkOnlyCode(String no, String code) {
        try {
            //补充租户信息
            OperationModel operationModel = com.yyigou.dsrp.cdc.service.utils.UserHandleUtils.getOperationModel();
            return CallResult.success(MessageVO.SUCCESS, supplierV2Service.checkOnlyCode(operationModel, no, code));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.supplier.v2.checkOnlyName", name = "校验供应商名称是否存在", methodType = MethodType.QUERY, viewNo = ViewNameConstant.BDC_SUPPLIER_VIEW, orgAuth = true, processState = 2, version = "1.0.1")
    public CallResult<Boolean> checkOnlyName(String no, String name) {
        try {
            //补充租户信息
            OperationModel operationModel = com.yyigou.dsrp.cdc.service.utils.UserHandleUtils.getOperationModel();
            return CallResult.success(MessageVO.SUCCESS, supplierV2Service.checkOnlyName(operationModel, no, name));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.supplier.v2.findListPageByFormal", name = "查询对象集合-分页(正式供应商)", viewNo = ViewNameConstant.BDC_SUPPLIER_VIEW, methodType = MethodType.QUERY, processState = 2, requestSession = false, requestAuthentication = false, requestDomainAuthentication = false)
    public CallResult<PageVo<SupplierFormalPageVO>> findListPageByFormal(@Parameter(name = "查询信息", isRequired = true) SupplierPageByFormalQueryDTO params, @Parameter(name = "分页信息", isRequired = true) PageDto pageDto) {
        try {
            OperationModel operationModel = com.yyigou.dsrp.cdc.service.utils.UserHandleUtils.getOperationModel();

            SupplierPageByFormalQueryReq queryReq = BeanUtil.copyFields(params, SupplierPageByFormalQueryReq.class);
            //补充租户信息
            queryReq.setEnterpriseNo(operationModel.getEnterpriseNo());
            queryReq.setUseOrgNo(operationModel.getOrgNo());

            PageVo<SupplierFormalPageVO> pojoPageVo = supplierV2Service.findListPageByFormal(operationModel, queryReq, pageDto);
            return CallResult.success(MessageVO.SUCCESS, pojoPageVo);
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.supplier.v2.querySpecifyOrgPageSupplier", name = "分页获取指定组织供应商档案", viewNo = ViewNameConstant.BDC_SUPPLIER_VIEW, methodType = MethodType.QUERY, processState = 2, requestSession = true, requestAuthentication = false)
    public CallResult<PageVo<SupplierPageVO>> querySpecifyOrgPageSupplier(SupplierPageQueryBySpecifyOrgDTO params, PageDto pageDto) {
        try {
            //补充租户信息
            OperationModel operationModel = UserHandleUtils.getOperationModel();

            SupplierPageQueryBySpecifyOrgReq queryReq = BeanUtil.copyFields(params, SupplierPageQueryBySpecifyOrgReq.class);
            //补充租户信息
//            queryReq.setEnterpriseNo(operationModel.getEnterpriseNo());
//            queryReq.setOrgNo(operationModel.getOrgNo());

            return CallResult.success(MessageVO.SUCCESS, supplierV2Service.querySpecifyOrgPageSupplier(operationModel, queryReq, pageDto));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.supplier.v2.queryPageSupplier", name = "分页获取供应商档案", viewNo = ViewNameConstant.BDC_SUPPLIER_VIEW, methodType = MethodType.QUERY, processState = 2, requestSession = true, requestAuthentication = true)
    public CallResult<PageVo<SupplierPageVO>> queryPageSupplier(SupplierQueryPageDTO params, PageDto pageDto) {
        try {
            //补充租户信息
            OperationModel operationModel = UserHandleUtils.getOperationModel();

            SupplierPageQueryBySpecifyOrgReq queryReq = BeanUtil.copyFields(params, SupplierPageQueryBySpecifyOrgReq.class);
            //补充租户信息
//            queryReq.setEnterpriseNo(operationModel.getEnterpriseNo());
//            queryReq.setOrgNo(operationModel.getOrgNo());

            return CallResult.success(MessageVO.SUCCESS, supplierV2Service.querySpecifyOrgPageSupplier(operationModel, queryReq, pageDto));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.supplier.v2.manageSaveBasicAndBiz", name = "供应商档案保存", viewNo = ViewNameConstant.BDC_SUPPLIER_VIEW, methodType = MethodType.ADD, processState = 2, version = "1.0.1")
    public CallResult<String> manageSaveSupplierBasicAndBiz(SupplierSaveBasicAndBizDTO params) {
        String lock = null;
        try {
            OperationModel operationModel = com.yyigou.dsrp.cdc.service.utils.UserHandleUtils.getOperationModel();

            lock = String.join(DistributedLockConstant.DELIMITER, DistributedLockConstant.SUPPLIER_SAVE_LOCK_KEY, operationModel.getEnterpriseNo(), params.getSupplierCode());
            if (!redisClientUtil.lock(lock, DistributedLockConstant.DEFAULT_WAIT_TIME, DistributedLockConstant.DEFAULT_LEASE_TIME)) {
                return CallResult.failed(ErrorCode.BUSINESS_ERROR_CODE, StrUtil.format("供应商【{}】正在处理，请稍后提交！", params.getSupplierCode()));
            }

            SupplierSaveBasicAndBizReq supplierSaveBasicAndBizReq = BeanUtil.copyFields(params, SupplierSaveBasicAndBizReq.class);
            //补充租户信息
            supplierSaveBasicAndBizReq.setEnterpriseNo(operationModel.getEnterpriseNo());

            return CallResult.success(MessageVO.SUCCESS, supplierV2Service.manageSaveSupplierBasicAndBiz(operationModel, supplierSaveBasicAndBizReq));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        } finally {
            if (null != lock) {
                redisClientUtil.unlock(lock);
            }
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.supplier.v2.editBasicAndBiz", name = "供应商档案编辑", viewNo = ViewNameConstant.BDC_SUPPLIER_VIEW, methodType = MethodType.UPDATE, processState = 2, version = "1.0.1")
    public CallResult<Boolean> editSupplierBasicAndBiz(SupplierEditBasicAndBizDTO params) {
        try {
            OperationModel operationModel = com.yyigou.dsrp.cdc.service.utils.UserHandleUtils.getOperationModel();
            SupplierEditBasicAndBizReq supplierUpdateReq = BeanUtil.copyFields(params, SupplierEditBasicAndBizReq.class);
            //补充租户信息
            supplierUpdateReq.setEnterpriseNo(operationModel.getEnterpriseNo());

            return CallResult.success(MessageVO.SUCCESS, supplierV2Service.editSupplierBasicAndBiz(operationModel, supplierUpdateReq));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.supplier.v2.editAddressList", name = "供应商档案地址编辑", viewNo = ViewNameConstant.BDC_SUPPLIER_VIEW, methodType = MethodType.UPDATE, processState = 2, version = "1.0.1")
    public CallResult<List<Long>> editSupplierAddressList(SupplierAddressListEditDTO params) {
        try {
            OperationModel operationModel = UserHandleUtils.getOperationModel();

            SupplierAddressListEditReq supplierAddressListEditReq = BeanUtil.copyFields(params, SupplierAddressListEditReq.class);
            supplierAddressListEditReq.setLinkAddressList(BeanUtil.copyFieldsList(params.getLinkAddressList(), CompanyShippingAddressReq.class));
            //补充租户信息
            supplierAddressListEditReq.setEnterpriseNo(operationModel.getEnterpriseNo());

            return CallResult.success(MessageVO.SUCCESS, supplierV2Service.editSupplierAddressList(operationModel, supplierAddressListEditReq));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.supplier.v2.editLinkmanList", name = "供应商档案联系人编辑", viewNo = ViewNameConstant.BDC_SUPPLIER_VIEW, methodType = MethodType.UPDATE, processState = 2, version = "1.0.1")
    public CallResult<List<Long>> editSupplierLinkmanList(SupplierLinkmanListEditDTO params) {
        try {
            //补充租户信息
            OperationModel operationModel = UserHandleUtils.getOperationModel();

            SupplierLinkmanListEditReq supplierLinkmanListEditReq = BeanUtil.copyFields(params, SupplierLinkmanListEditReq.class);
            supplierLinkmanListEditReq.setLinkmanList(BeanUtil.copyFieldsList(params.getLinkmanList(), CompanyLinkmanReq.class));
            //补充租户信息
            supplierLinkmanListEditReq.setEnterpriseNo(operationModel.getEnterpriseNo());

            return CallResult.success(MessageVO.SUCCESS, supplierV2Service.editSupplierLinkmanList(operationModel, supplierLinkmanListEditReq));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.supplier.v2.editOrdermanList", name = "供应商档案负责人编辑", viewNo = ViewNameConstant.BDC_SUPPLIER_VIEW, methodType = MethodType.UPDATE, processState = 2, version = "1.0.1")
    public CallResult<List<Long>> editSupplierOrdermanList(SupplierOrdermanListEditDTO params) {
        try {
            OperationModel operationModel = UserHandleUtils.getOperationModel();

            SupplierOrdermanListEditReq supplierOrdermanListEditReq = BeanUtil.copyFields(params, SupplierOrdermanListEditReq.class);
            supplierOrdermanListEditReq.setSupplierManList(BeanUtil.copyFieldsList(params.getSupplierManList(), SupplierOrderManReq.class));
            //补充租户信息
            supplierOrdermanListEditReq.setEnterpriseNo(operationModel.getEnterpriseNo());

            return CallResult.success(MessageVO.SUCCESS, supplierV2Service.editSupplierOrdermanList(operationModel, supplierOrdermanListEditReq));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.supplier.v2.editBankList", name = "供应商档案银行编辑", viewNo = ViewNameConstant.BDC_SUPPLIER_VIEW, methodType = MethodType.UPDATE, processState = 2, version = "1.0.1")
    public CallResult<List<Long>> editSupplierBankList(SupplierBankListEditDTO params) {
        try {
            //补充租户信息
            OperationModel operationModel = UserHandleUtils.getOperationModel();

            SupplierBankListEditReq supplierBankListEditReq = BeanUtil.copyFields(params, SupplierBankListEditReq.class);
            supplierBankListEditReq.setBankList(BeanUtil.copyFieldsList(params.getBankList(), CompanyBankReq.class));
            //补充租户信息
            supplierBankListEditReq.setEnterpriseNo(operationModel.getEnterpriseNo());

            return CallResult.success(MessageVO.SUCCESS, supplierV2Service.editSupplierBankList(operationModel, supplierBankListEditReq));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.supplier.v2.findOrgListPageBySetting", name = "查询内部组织", viewNo = ViewNameConstant.BDC_SUPPLIER_VIEW, methodType = MethodType.QUERY, processState = 2, version = "1.0.1")
    public CallResult<PageVo<OrganizationVo>> findOrgListPageBySetting(SupplierEligibleOrgListQueryDTO params, PageDto pageParams) {
        try {
            OperationModel operationModel = com.yyigou.dsrp.cdc.service.utils.UserHandleUtils.getOperationModel();

            SupplierEligibleOrgListQueryReq queryReq = BeanUtil.copyFields(params, SupplierEligibleOrgListQueryReq.class);
            //补充租户信息
            queryReq.setEnterpriseNo(operationModel.getEnterpriseNo());

            PageVo<OrganizationVo> pojoPageVo = supplierV2Service.findOrgListPageBySetting(operationModel, queryReq, pageParams);
            return CallResult.success(MessageVO.SUCCESS, pojoPageVo);
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    public CallResult<ImportTaskDataSpiceVO> checkExcelSliceData(UapExcelImportDataSpiceMessage uapexcelImportDataSpiceMessage) {
        try {
            return CallResult.success(MessageVO.SUCCESS, supplierV2ImportService.checkExcelSliceData(uapexcelImportDataSpiceMessage));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    public CallResult<ImportTaskDataSpiceVO> handleImportSliceData(DataImportDataSpiceMessage dataImportDataSpiceMessage) {
        try {
            return CallResult.success(MessageVO.SUCCESS, supplierV2ImportService.handleImportSliceData(dataImportDataSpiceMessage));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.supplier.v2.checkConvertToCustomer", name = "检测管理组织是否可转客户", viewNo = ViewNameConstant.BDC_SUPPLIER_VIEW, methodType = MethodType.QUERY, processState = 2, version = "1.0.1")
    public CallResult<Boolean> checkConvertToCustomer(SupplierConvertToCustomerDTO params) {
        try {
            OperationModel operationModel = com.yyigou.dsrp.cdc.service.utils.UserHandleUtils.getOperationModel();

            ValidatorUtils.checkEmptyThrowEx(params, "参数不能为空");

            return CallResult.success(MessageVO.SUCCESS, supplierV2Service.checkConvertToCustomer(operationModel, params.getSupplierCode()));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }
}
