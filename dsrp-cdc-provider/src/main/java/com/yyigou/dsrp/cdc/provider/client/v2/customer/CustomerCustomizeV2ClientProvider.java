package com.yyigou.dsrp.cdc.provider.client.v2.customer;

import com.alibaba.dubbo.config.annotation.Service;
import com.yyigou.ddc.common.exception.CommonExceptionHandler;
import com.yyigou.ddc.common.message.MessageVO;
import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.dsrp.cdc.client.v2.customer.CustomerCustomizeClient;
import com.yyigou.dsrp.cdc.client.v2.customer.request.CustomerCoordinationCreateRequest;
import com.yyigou.dsrp.cdc.client.v2.customer.response.CustomerCoordinationCreateResponse;
import com.yyigou.dsrp.cdc.model.constant.DistributedLockConstant;
import com.yyigou.dsrp.cdc.service.utils.RedisClientUtil;
import com.yyigou.dsrp.cdc.service.v2.customer.CustomerV2Service;
import com.yyigou.dsrp.gcs.model.api.constant.ErrorCode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
@RequiredArgsConstructor
@Slf4j
@Service
public class CustomerCustomizeV2ClientProvider implements CustomerCustomizeClient {
    @Resource
    private CustomerV2Service customerV2Service;

    @Resource
    private RedisClientUtil redisClientUtil;

    @Override
    public CallResult<CustomerCoordinationCreateResponse> createCoordinationCustomer(CustomerCoordinationCreateRequest params) {
        String lock = null;
        try {
            lock = String.join(DistributedLockConstant.DELIMITER, DistributedLockConstant.CUSTOMER_SAVE_LOCK_KEY, params.getEnterpriseNo(), params.getCustomerName(), params.getUnifiedSocialCode(), params.getOmsCustomerNo());
            if (!redisClientUtil.lock(lock, DistributedLockConstant.DEFAULT_WAIT_TIME, DistributedLockConstant.DEFAULT_LEASE_TIME)) {
                return CallResult.failed(ErrorCode.BUSINESS_ERROR_CODE, "请不要在短时间内重复调用");
            }

            log.info("createCoordinationSupplierReq:{}", params);
            CustomerCoordinationCreateResponse coordinationSupplier = customerV2Service.createCoordinationCustomer(params.getEnterpriseNo(), params.getOrgNo(), params.getCustomerName(), params.getUnifiedSocialCode(), params.getOmsCustomerNo());
            log.info("createCoordinationSupplierRes:{}", coordinationSupplier);

            return CallResult.success(MessageVO.SUCCESS, coordinationSupplier);
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        } finally {
            if (null != lock) {
                redisClientUtil.unlock(lock);
            }
        }
    }
}
