package com.yyigou.dsrp.cdc.provider.api.customer;

import com.alibaba.dubbo.config.annotation.Service;
import com.yyigou.ddc.common.exception.CommonExceptionHandler;
import com.yyigou.ddc.common.message.MessageVO;
import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.ddc.common.service.ServiceBaseAbstract;
import com.yyigou.ddc.common.service.annotation.Interface;
import com.yyigou.ddc.common.service.annotation.Method;
import com.yyigou.ddc.common.service.annotation.MethodType;
import com.yyigou.dsrp.cdc.api.company.vo.CompanyLinkmanVO;
import com.yyigou.dsrp.cdc.api.customer.CustomerLinkManApi;
import com.yyigou.dsrp.cdc.api.customer.dto.CustomerLinkmanByOrgDTO;
import com.yyigou.dsrp.cdc.common.util.OperationModel;
import com.yyigou.dsrp.cdc.service.customer.CustomerLinkManService;
import com.yyigou.dsrp.cdc.service.utils.UserHandleUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Service
@RequiredArgsConstructor
@Slf4j
@Interface(name = "客户联系人")
public class CustomerLinkManProvider extends ServiceBaseAbstract implements CustomerLinkManApi {

    private final CustomerLinkManService customerLinkManService;

    /**
     * 跨组织获取客户联系人列表
     *
     * @param customerCode
     * @param orgNo
     * @return
     */
    @Override
    @Method(aliasName = "dsrp.cdc.customer.org.getCustomerLinkmanListByOrg", name = "跨组织获取客户联系人列表", methodType = MethodType.QUERY, processState = 2, requestSession = false, requestAuthentication = false)
    public CallResult<List<CompanyLinkmanVO>> getCustomerLinkmanListByOrg(String customerCode, String orgNo) {
        try {
            //补充租户信息
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            return CallResult.success(MessageVO.SUCCESS, customerLinkManService.getCustomerLinkmanListByOrg(operationModel, customerCode, orgNo));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 跨组织保存客户联系人列表
     *
     * @param params
     * @return
     */
    @Override
    @Method(aliasName = "dsrp.cdc.customer.org.saveCustomerLinkmanByOrg", name = "跨组织保存客户联系人列表", methodType = MethodType.QUERY, processState = 2, requestSession = false, requestAuthentication = false)
    public CallResult<CompanyLinkmanVO> saveCustomerLinkmanByOrg(CustomerLinkmanByOrgDTO params) {
        try {
            //补充租户信息
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            return CallResult.success(MessageVO.SUCCESS, customerLinkManService.saveCustomerLinkmanByOrg(operationModel, params));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 跨组织删除联系人
     *
     * @param params
     * @return
     */
    @Override
    @Method(aliasName = "dsrp.cdc.customer.org.editCustomerLinkmanByOrg", name = "跨组织删除联系人", methodType = MethodType.QUERY, processState = 2, requestSession = false, requestAuthentication = false)
    public CallResult<CompanyLinkmanVO> editCustomerLinkmanByOrg(CustomerLinkmanByOrgDTO params) {
        try {
            //补充租户信息
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            return CallResult.success(MessageVO.SUCCESS, customerLinkManService.editCustomerLinkmanByOrg(operationModel, params));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 跨组织删除联系人
     *
     * @param linkmanId
     * @return
     */
    @Override
    @Method(aliasName = "dsrp.cdc.customer.org.deleteCustomerLinkmanByOrg", name = "跨组织删除联系人", methodType = MethodType.QUERY, processState = 2, requestSession = false, requestAuthentication = false)
    public CallResult<Boolean> deleteCustomerLinkmanByOrg(Long linkmanId) {
        try {
            //补充租户信息
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            return CallResult.success(MessageVO.SUCCESS, customerLinkManService.deleteCustomerLinkmanByOrg(operationModel, linkmanId));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }
}
