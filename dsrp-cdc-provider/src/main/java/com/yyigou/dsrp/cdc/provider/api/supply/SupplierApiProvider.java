package com.yyigou.dsrp.cdc.provider.api.supply;

import com.alibaba.dubbo.config.annotation.Service;
import com.yyigou.ddc.common.exception.CommonExceptionHandler;
import com.yyigou.ddc.common.message.MessageVO;
import com.yyigou.ddc.common.page.PageDto;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.ddc.common.service.ServiceBaseAbstract;
import com.yyigou.ddc.common.service.annotation.Interface;
import com.yyigou.ddc.common.service.annotation.Method;
import com.yyigou.ddc.common.service.annotation.MethodType;
import com.yyigou.dsrp.cdc.api.common.vo.OrganizationVO;
import com.yyigou.dsrp.cdc.api.company.vo.CompanyDetailVO;
import com.yyigou.dsrp.cdc.api.supply.SupplierApi;
import com.yyigou.dsrp.cdc.api.supply.dto.*;
import com.yyigou.dsrp.cdc.api.supply.vo.*;
import com.yyigou.dsrp.cdc.common.util.OperationModel;
import com.yyigou.dsrp.cdc.manager.integration.uim.TenantService;
import com.yyigou.dsrp.cdc.service.supplier.SupplierService;
import com.yyigou.dsrp.cdc.service.utils.UserHandleUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Service
@RequiredArgsConstructor
@Slf4j
@Interface(name = "供应商档案前端接口")
public class SupplierApiProvider extends ServiceBaseAbstract implements SupplierApi {


    private final SupplierService supplierService;
    private final TenantService tenantService;

    @Override
    @Method(aliasName = "dsrp.cdc.supplier.checkOnlyCode", name = "校验供应商编码是否存在", methodType = MethodType.ADD, processState = 2, requestSession = true, requestAuthentication = true)
    public CallResult<Boolean> checkOnlyCode(String no, String code) {
        try {
            //补充租户信息
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            return CallResult.success(MessageVO.SUCCESS, supplierService.checkOnlyCode(operationModel, no, code));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.supplier.checkOnlyName", name = "校验供应商名称是否存在", methodType = MethodType.ADD, processState = 2, requestSession = true, requestAuthentication = true)
    public CallResult<Boolean> checkOnlyName(String no, String name) {
        try {
            //补充租户信息
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            return CallResult.success(MessageVO.SUCCESS, supplierService.checkOnlyName(operationModel, no, name));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 保存供应商档案
     *
     * @param params
     * @return: {@link CallResult < Boolean>}
     */
    @Override
    @Method(aliasName = "dsrp.cdc.supplier.save", name = "保存供应商档案", methodType = MethodType.ADD, processState = 2, requestSession = true, requestAuthentication = true)
    public CallResult<String> saveSupplier(SaveSupplierDTO params) {
        try {
            //补充租户信息
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            return CallResult.success(MessageVO.SUCCESS, supplierService.saveSupplier(operationModel, params, "供应商档案新增"));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 修改供应商档案信息
     *
     * @param params
     * @return: {@link CallResult< Boolean>}
     */
    @Override
    @Method(aliasName = "dsrp.cdc.supplier.updateSupplier", name = "修改供应商档案", methodType = MethodType.ADD, processState = 2, requestSession = true, requestAuthentication = true)
    public CallResult<Boolean> updateSupplier(SaveSupplierDTO params) {
        try {
            //补充租户信息
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            return CallResult.success(MessageVO.SUCCESS, supplierService.updateSupplier(operationModel, params));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 获取供应商详情
     *
     * @param supplierNo
     * @return: {@link CallResult<   CompanyDetailVO  >}
     */
    @Override
    @Method(aliasName = "dsrp.cdc.supplier.getSupplier", name = "校验供应商名称是否存在", methodType = MethodType.ADD, processState = 2, requestSession = true, requestAuthentication = true)
    public CallResult<SupplierVO> getSupplier(String supplierNo) {
        try {
            //补充租户信息
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            return CallResult.success(MessageVO.SUCCESS, supplierService.getSupplier(operationModel.getEnterpriseNo(), supplierNo));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 获取供应商详情 多组织获取详情
     *
     * @param supplierNo
     * @return: {@link CallResult<  CompanyDetailVO >}
     */
    @Override
    @Method(aliasName = "dsrp.cdc.supplier.org.getSupplier", name = "获取供应商详情 多组织获取详情", methodType = MethodType.ADD, processState = 2, requestSession = true, requestAuthentication = true)
    public CallResult<SupplierVO> getSupplierByOrg(String supplierNo) {
        try {
            //补充租户信息
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            return CallResult.success(MessageVO.SUCCESS, supplierService.getSupplierByOrg(operationModel, supplierNo));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 删除客户档案
     *
     * @param supplierNoList
     * @return
     */
    @Override
    @Method(aliasName = "dsrp.cdc.supplier.deleteSupplier", name = "删除草稿供应商", methodType = MethodType.ADD, processState = 2, requestSession = true, requestAuthentication = true)
    public CallResult<Boolean> deleteSupplier(List<String> supplierNoList) {
        try {
            //补充租户信息
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            return CallResult.success(MessageVO.SUCCESS, supplierService.deleteSupplier(operationModel, supplierNoList));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.supplier.findListPageForSync", name = "运营平台-供应商对照", methodType = MethodType.ADD, processState = 2, requestSession = true, requestAuthentication = true)
    public CallResult<PageVo<SupplierSyncQueryVO>> findListPageForSync(SupplierSyncQueryDTO params, PageDto pageParams) {
        try {
            //补充租户信息
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            return CallResult.success(MessageVO.SUCCESS, supplierService.findListPageForSync(operationModel, params, pageParams));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.supplier.findSupplierSyncCount", name = "运营平台-供应商对照统计数量", methodType = MethodType.ADD, processState = 2, requestSession = true, requestAuthentication = true)
    public CallResult<SupplierSyncCountVO> findSupplierSyncCount() {
        try {
            //补充租户信息
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            return CallResult.success(MessageVO.SUCCESS, supplierService.findSupplierSyncCount(operationModel));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.supplier.findListPageForPendingAdmission", name = "运营平台-待准入供应商-准入弹框列表", methodType = MethodType.ADD, processState = 2, requestSession = true, requestAuthentication = true)
    public CallResult<PageVo<SupplierSyncQueryVO>> findListPageForPendingAdmission(SupplierSyncQueryDTO params, PageDto pageParams) {
        try {
            //补充租户信息
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            return CallResult.success(MessageVO.SUCCESS, supplierService.findListPageForPendingAdmission(operationModel, params, pageParams));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 分页获取供应商档案
     *
     * @param params
     * @param pageDto
     * @return
     */
    @Override
    @Method(aliasName = "dsrp.cdc.supplier.queryPageSupplier", name = "分页获取供应商档案", methodType = MethodType.ADD, processState = 2, requestSession = true, requestAuthentication = true)
    public CallResult<PageVo<SupplierPageVO>> queryPageSupplier(QueryPageSupplierDTO params, PageDto pageDto) {
        try {
            //补充租户信息
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            return CallResult.success(MessageVO.SUCCESS, supplierService.queryPageSupplier(operationModel, params, pageDto));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 查询待处理供应商数量
     *
     * @return
     */
    @Method(aliasName = "dsrp.cdc.supplier.findExamCount", name = "查询待处理供应商数量", methodType = MethodType.QUERY, processState = 2, requestSession = true, requestAuthentication = true)
    @Override
    public CallResult<Long> findExamCount() {
        try{
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            return CallResult.success(MessageVO.SUCCESS, supplierService.findExamCount(operationModel));
        }catch (Exception e){
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 分页获取客户档案
     *
     * @param params
     * @param pageDto
     * @return
     */
    @Override
    @Method(aliasName = "dsrp.cdc.supplier.group.queryPageSupplier", name = "分页获取供应商档案", methodType = MethodType.ADD, processState = 2, requestSession = true, requestAuthentication = false)
    public CallResult<PageVo<SupplierPageVO>> queryPageSupplierByGroup(QueryPageSupplierDTO params, PageDto pageDto) {
        try {
            //补充租户信息
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            return CallResult.success(MessageVO.SUCCESS, supplierService.queryPageSupplierByGroup(operationModel, params, pageDto));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 分页获取客户档案
     *
     * @param params
     * @param pageDto
     * @return
     */
    @Override
    @Method(aliasName = "dsrp.cdc.supplier.group.org.queryPageSupplier", name = "分页获取供应商档案", methodType = MethodType.ADD, processState = 2, requestSession = true, requestAuthentication = false)
    public CallResult<PageVo<SupplierPageVO>> queryPageSupplierByOrg(QueryPageSupplierByOrgDTO params, PageDto pageDto) {
        try {
            //补充租户信息
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            return CallResult.success(MessageVO.SUCCESS, supplierService.queryPageSupplierByOrg(operationModel, params, pageDto));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 分页获取指定组织供应商档案
     *
     * @param params
     * @param pageDto
     * @return
     */
    @Override
    @Method(aliasName = "dsrp.cdc.supplier.group.specifyOrg.queryPageSupplier", name = "分页获取指定组织供应商档案", methodType = MethodType.ADD, processState = 2, requestSession = true, requestAuthentication = false)
    public CallResult<PageVo<SupplierPageVO>> queryPageSupplierBySpecifyOrg(QueryPageSupplierBySpecifyOrgDTO params, PageDto pageDto) {
        try {
            //补充租户信息
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            return CallResult.success(MessageVO.SUCCESS, supplierService.queryPageSupplierBySpecifyOrg(operationModel, params, pageDto));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 分页获取供应商档案运营平台版本
     *
     * @param params
     * @param pageParams
     * @return
     */
    @Override
    @Method(aliasName = "dsrp.cdc.supplier.operationPlatform.queryPageSupplier", name = "运营平台分页获取供应商档案", methodType = MethodType.ADD, processState = 2, requestSession = true, requestAuthentication = true)
    public CallResult<PageVo<SupplierOperationPlatformPageVO>> queryOperationPlatformPageSupplier(QueryPageOperationPlatformSupplierDTO params, PageDto pageParams) {
        try {
            //补充租户信息
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            return CallResult.success(MessageVO.SUCCESS, supplierService.queryOperationPlatformPageSupplier(operationModel, params, pageParams));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }



    /**
     * 分页获取供应商档案运营平台版本
     *
     * @param params
     * @return
     */
    @Override
    @Method(aliasName = "dsrp.cdc.supplier.group.org.findOrgInfoBySupplier", name = "根据供应商编号获取供应商的所属组织", methodType = MethodType.QUERY, processState = 2)
    public CallResult<List<OrganizationVO>> findOrgInfoBySupplier(OrgInfoBySupplierDTO params){
        try {
            //补充租户信息
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            String groupEnterpriseNo = tenantService.getGroupEnterpriseNo(operationModel.getEnterpriseNo());
            return CallResult.success(MessageVO.SUCCESS, supplierService.findOrgInfoBySupplier(operationModel, groupEnterpriseNo,params));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }
}
