package com.yyigou.dsrp.cdc.provider.client.v2.customer;

import com.alibaba.dubbo.config.annotation.Service;
import com.yyigou.ddc.common.exception.BusinessException;
import com.yyigou.ddc.common.page.PageDto;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.ddc.meta.base.service.referrpc.ReferQueryCondition;
import com.yyigou.ddc.meta.base.service.referrpc.ReferRpcReqDto;
import com.yyigou.ddc.meta.base.service.referrpc.service.IReferQueryFunc;
import com.yyigou.dsrp.cdc.api.v2.customer.vo.CustomerPageVO;
import com.yyigou.dsrp.cdc.common.util.OperationModel;
import com.yyigou.dsrp.cdc.model.v2.customer.req.CustomerFindQueryListPageForTenantReq;
import com.yyigou.dsrp.cdc.service.utils.UserHandleUtils;
import com.yyigou.dsrp.cdc.service.v2.customer.CustomerV2Service;
import com.yyigou.dsrp.gcs.common.util.ObjectToMapUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 *  查询租户中的客户档案(只返回管理组织的基本信息，但组织权限还是使用组织)
 */
@Component
@Service(group = "cdcCustomerForTenant")
@RequiredArgsConstructor
@Slf4j
public class CustomerV2ForTenantRefClientProvider implements IReferQueryFunc {
    private static final String FIELD_USE_ORG_NO = "use_org_no";

    private final CustomerV2Service customerV2Service;


    /**
     * 查询租户客户档案(类似集团库的查询效果)
     * 比如用户的组织权限是 a b c, 档案A的管理组织是d ，分派给 a b c，那么用户有档案A在使用组织 a b c中的权限（3条）。
     * 但接口只会返回组织A的基本信息（1条），管理组织显示d。
     * 在实现上很简单，
     *
     * @param referRpcReqDto
     * @return
     */
    @Override
    public PageVo<Map<String, Object>> queryReferData(ReferRpcReqDto referRpcReqDto) {
        log.warn("cdcCustomerForTenantReferRpcReqDto={}", referRpcReqDto);

        if (null == referRpcReqDto.getPageDto()) {
            PageDto pageDto = new PageDto();
            pageDto.setPageIndex(1);
            pageDto.setPageSize(100000);
            referRpcReqDto.setPageDto(pageDto);
        }

        OperationModel operationModel = UserHandleUtils.getOperationModel();

        CustomerFindQueryListPageForTenantReq customerFindQueryListPageForTenantReq = new CustomerFindQueryListPageForTenantReq();
        for (ReferQueryCondition condition : referRpcReqDto.getConditions()) {
            if(condition.getColumn().equals(FIELD_USE_ORG_NO) && StringUtils.isNotBlank(condition.getValue())) {
                List<String> useOrgNoList = Arrays.stream(condition.getValue().split(",")).map(e -> e.replaceAll("'","")).collect(Collectors.toList());
                // 编码实现数据权限
                customerFindQueryListPageForTenantReq.setUseOrgNoList(useOrgNoList);
            }
        }

        if (CollectionUtils.isEmpty(customerFindQueryListPageForTenantReq.getUseOrgNoList())) {
            if (!operationModel.getSingleOrgFlag()) {
                throw new BusinessException("使用组织为空");
            }
        }

        customerFindQueryListPageForTenantReq.setEnterpriseNo(operationModel.getEnterpriseNo());
        PageVo<CustomerPageVO> listPage = customerV2Service.findListPageForTenant(customerFindQueryListPageForTenantReq, referRpcReqDto.getPageDto());
        List<Map<String, Object>> rows = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(listPage.getRows())) {
            for (CustomerPageVO row : listPage.getRows()) {
                try {
                    // 将RegistrationCertPageVO转成Map，其中类属性如果是驼峰命名法，Map中的key用下划线分隔
                    Map<String, Object> map = ObjectToMapUtil.convertObjectToMap(row, CustomerPageVO.class);
                    rows.add(map);
                } catch (Exception e) {
                    log.error("映射异常", e);
                    throw new RuntimeException("映射异常");
                }
            }
        }
        return new PageVo<>(listPage.getPageIndex(), listPage.getPageSize(), listPage.getTotal(), rows);
    }



}
