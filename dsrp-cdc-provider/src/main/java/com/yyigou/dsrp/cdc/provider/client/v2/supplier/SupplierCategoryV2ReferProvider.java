package com.yyigou.dsrp.cdc.provider.client.v2.supplier;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.config.annotation.Service;
import com.yyigou.ddc.common.exception.BusinessException;
import com.yyigou.ddc.common.page.PageDto;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.ddc.common.treegrid.TreeGrid;
import com.yyigou.ddc.meta.base.service.referrpc.ReferQueryCondition;
import com.yyigou.ddc.meta.base.service.referrpc.ReferRpcReqDto;
import com.yyigou.ddc.meta.base.service.referrpc.service.IReferQueryFunc;
import com.yyigou.dsrp.cdc.api.v2.supplier.vo.SupplierCategoryTreeVO;
import com.yyigou.dsrp.cdc.common.util.OperationModel;
import com.yyigou.dsrp.cdc.model.v2.supplier.req.SupplierCategoryQuerySolutionTreeReq;
import com.yyigou.dsrp.cdc.provider.client.v2.supplier.vo.AttrValue;
import com.yyigou.dsrp.cdc.service.utils.UserHandleUtils;
import com.yyigou.dsrp.cdc.service.v2.supplier.SupplierCategoryV2Service;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@Service(group = "cdcSupplierCategory")
@RequiredArgsConstructor
@Slf4j
public class SupplierCategoryV2ReferProvider implements IReferQueryFunc {
    private static final int MAX_LAYER = 10;

    @Resource
    private SupplierCategoryV2Service supplierCategoryV2Service;

    @Override
    public PageVo<Map<String, Object>> queryReferData(ReferRpcReqDto referRpcReqDto) {
        log.warn("cdcSupplierCategoryReferRpcReqDto={}", referRpcReqDto);

        if (null == referRpcReqDto.getPageDto()) {
            PageDto pageDto = new PageDto();
            pageDto.setPageIndex(1);
            pageDto.setPageSize(100000);
            referRpcReqDto.setPageDto(pageDto);
        }

        OperationModel operationModel = UserHandleUtils.getOperationModel();

        SupplierCategoryQuerySolutionTreeReq supplierCategoryQuerySolutionTreeReq = packSupplierCategoryQuerySolutionTreeReq(referRpcReqDto, operationModel);

        List<SupplierCategoryTreeVO> supplierCategoryTreeVOS = supplierCategoryV2Service.queryReferTree(operationModel, supplierCategoryQuerySolutionTreeReq);

        log.warn("supplierCategoryTreeVOS={}", supplierCategoryTreeVOS);

        PageVo<Map<String, Object>> result = new PageVo<>();
        result.setPageIndex(referRpcReqDto.getPageDto().getPageIndex());
        result.setPageSize(referRpcReqDto.getPageDto().getPageSize());
        result.setPageCount(1);

        //把supplierCategoryTreeVO转成attrValue，children需要递归设置
        if (CollectionUtils.isNotEmpty(supplierCategoryTreeVOS)) {
            List<Map<String, Object>> outerWrappers = supplierCategoryTreeVOS.stream().map(supplierCategoryTreeVO -> {
                AttrValue attrValue = new AttrValue();

                attrValue.setParent_no(supplierCategoryTreeVO.getParentNo());
                attrValue.setNo(supplierCategoryTreeVO.getNo());
                attrValue.setCategory_code(supplierCategoryTreeVO.getCategoryCode());
                attrValue.setCategory_name(supplierCategoryTreeVO.getCategoryName());
                attrValue.setParentCode(supplierCategoryTreeVO.getParentNo());
                attrValue.setChildren(getChildren(supplierCategoryTreeVO));

                return BeanUtil.beanToMap(attrValue);
            }).collect(Collectors.toList());

            result.setRows(outerWrappers);

            log.warn("cdcSupplierCategoryResult={}", result);

            return result;
        }

        return result;
    }

    @NotNull
    private static SupplierCategoryQuerySolutionTreeReq packSupplierCategoryQuerySolutionTreeReq(ReferRpcReqDto referRpcReqDto, OperationModel operationModel) {
        SupplierCategoryQuerySolutionTreeReq supplierCategoryQuerySolutionTreeReq = new SupplierCategoryQuerySolutionTreeReq();
        supplierCategoryQuerySolutionTreeReq.setEnterpriseNo(operationModel.getEnterpriseNo());
        if (CollectionUtils.isNotEmpty(referRpcReqDto.getConditions())) {
            List<SupplierCategoryQuerySolutionTreeReq.QueryCondition> queryConditionList = new ArrayList<>(referRpcReqDto.getConditions().size());
            SupplierCategoryQuerySolutionTreeReq.QueryCondition queryCondition = new SupplierCategoryQuerySolutionTreeReq.QueryCondition();
            for (ReferQueryCondition condition : referRpcReqDto.getConditions()) {
                queryCondition.setColumn(condition.getColumn());
                queryCondition.setValue(condition.getValue());
                queryCondition.setOperator(condition.getOperator());

                queryConditionList.add(queryCondition);
            }
            supplierCategoryQuerySolutionTreeReq.setQueryConditionList(queryConditionList);
        }
        return supplierCategoryQuerySolutionTreeReq;
    }

    private List<AttrValue> getChildren(SupplierCategoryTreeVO supplierCategoryTreeVO) {
        return getChildren(supplierCategoryTreeVO, 1);
    }

    private List<AttrValue> getChildren(SupplierCategoryTreeVO supplierCategoryTreeVO, int level) {
        if (level > MAX_LAYER) {
            throw new BusinessException("分类层级异常");
        }

        if (CollectionUtils.isNotEmpty(supplierCategoryTreeVO.getChildren())) {
            List<AttrValue> children = new ArrayList<>();
            for (TreeGrid child : supplierCategoryTreeVO.getChildren()) {
                SupplierCategoryTreeVO ch = (SupplierCategoryTreeVO) child;
                AttrValue attrValue = new AttrValue();

                attrValue.setParent_no(ch.getParentNo());
                attrValue.setNo(ch.getNo());
                attrValue.setCategory_code(ch.getCategoryCode());
                attrValue.setCategory_name(ch.getCategoryName());
                attrValue.setParentCode(ch.getParentNo());

                attrValue.setChildren(getChildren(ch, level + 1));
                children.add(attrValue);
            }
            return children;
        }

        return null;
    }
}
