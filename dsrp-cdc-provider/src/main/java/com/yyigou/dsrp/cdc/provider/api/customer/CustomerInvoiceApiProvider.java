package com.yyigou.dsrp.cdc.provider.api.customer;

import com.alibaba.dubbo.config.annotation.Service;
import com.yyigou.ddc.common.exception.CommonExceptionHandler;
import com.yyigou.ddc.common.message.MessageVO;
import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.ddc.common.service.ServiceBaseAbstract;
import com.yyigou.ddc.common.service.annotation.Interface;
import com.yyigou.ddc.common.service.annotation.Method;
import com.yyigou.ddc.common.service.annotation.MethodType;
import com.yyigou.dsrp.cdc.api.customer.CustomerInvoiceApi;
import com.yyigou.dsrp.cdc.api.customer.dto.CustomerInvoiceQueryDTO;
import com.yyigou.dsrp.cdc.api.customer.vo.CustomerInvoiceVO;
import com.yyigou.dsrp.cdc.client.customer.response.response.CustomerInvoiceResponse;
import com.yyigou.dsrp.cdc.common.util.OperationModel;
import com.yyigou.dsrp.cdc.service.customer.CustomerInvoiceService;
import com.yyigou.dsrp.cdc.service.utils.UserHandleUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 *
 **/
@Component
@Service
@RequiredArgsConstructor
@Slf4j
@Interface(name = "客户税务信息")
public class CustomerInvoiceApiProvider extends ServiceBaseAbstract implements CustomerInvoiceApi {


    private final CustomerInvoiceService customerInvoiceService;


    @Override
    @Method(aliasName = "dsrp.cdc.customer.getInvoiceListByCustomerCode", name = "获取客户税务信息列表", methodType = MethodType.QUERY, processState = 2, requestSession = true, requestAuthentication = false)
    public CallResult<List<CustomerInvoiceVO>> getInvoiceListByCustomerCode(CustomerInvoiceQueryDTO params) {
        try {
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            return CallResult.success(MessageVO.SUCCESS, customerInvoiceService.getInvoiceListByCustomerCode(operationModel, params));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }
}
