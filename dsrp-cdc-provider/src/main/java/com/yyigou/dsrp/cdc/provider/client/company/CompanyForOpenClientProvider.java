package com.yyigou.dsrp.cdc.provider.client.company;

import com.alibaba.dubbo.config.annotation.Service;
import com.yyigou.ddc.common.exception.CommonExceptionHandler;
import com.yyigou.ddc.common.message.MessageVO;
import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.dsrp.cdc.client.company.CompanyForOpenClient;
import com.yyigou.dsrp.cdc.client.company.request.CompanyForOpenFindRequest;
import com.yyigou.dsrp.cdc.client.company.response.CompanyInfoResponse;
import com.yyigou.dsrp.cdc.client.supplier.request.SupplierForOpenRequest;
import com.yyigou.dsrp.cdc.client.supplier.response.SupplierForOpenResponse;
import com.yyigou.dsrp.cdc.service.company.CompanyForOpenService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
@Service
@RequiredArgsConstructor
@Slf4j
public class CompanyForOpenClientProvider implements CompanyForOpenClient {
    @Resource
    private CompanyForOpenService companyForOpenService;

    /**
     * spd拉取生产厂商专用接口
     * @param request
     * @return
     */
    @Override
    public CallResult<List<CompanyInfoResponse>> findList(CompanyForOpenFindRequest request) {
        try {
            return CallResult.success(MessageVO.SUCCESS, companyForOpenService.findList(request));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }
}
