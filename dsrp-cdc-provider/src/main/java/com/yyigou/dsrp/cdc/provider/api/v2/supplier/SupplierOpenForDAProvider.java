package com.yyigou.dsrp.cdc.provider.api.v2.supplier;

import com.alibaba.dubbo.config.annotation.Service;
import com.yyigou.ddc.common.exception.CommonExceptionHandler;
import com.yyigou.ddc.common.message.MessageVO;
import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.ddc.common.service.ServiceBaseAbstract;
import com.yyigou.ddc.common.service.annotation.Interface;
import com.yyigou.ddc.common.service.annotation.Method;
import com.yyigou.ddc.common.service.annotation.MethodType;
import com.yyigou.ddc.common.util.ValidatorUtils;
import com.yyigou.dsrp.cdc.api.supply.dto.SupplierExternalSaveDTO;
import com.yyigou.dsrp.cdc.api.supply.vo.SupplierExternalSaveVO;
import com.yyigou.dsrp.cdc.api.v2.supplier.openapi.SupplierForDAOpenAPI;
import com.yyigou.dsrp.cdc.common.util.OperationModel;
import com.yyigou.dsrp.cdc.model.constant.DistributedLockConstant;
import com.yyigou.dsrp.cdc.service.utils.RedisClientUtil;
import com.yyigou.dsrp.cdc.service.utils.UserHandleUtils;
import com.yyigou.dsrp.cdc.service.v2.supplier.SupplierV2ExternalService;
import com.yyigou.dsrp.gcs.model.api.constant.ErrorCode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

@Component
@Service
@RequiredArgsConstructor
@Slf4j
@Interface(name = "供应商档案开放平台接口")
public class SupplierOpenForDAProvider extends ServiceBaseAbstract implements SupplierForDAOpenAPI {
    @Resource(name = "supplierV2ExternalService")
    private SupplierV2ExternalService supplierV2ExternalService;

    @Resource
    private RedisClientUtil redisClientUtil;

    @Override
    @Method(aliasName = "dsrp.cdc.open.external.supplier.v2.batchSave", name = "供应商档案保存接口-对外", methodType = MethodType.ADD, processState = 2, requestSession = true, requestAuthentication = true, invokeRange = Method.InvokeRange.OUTER_OPEN, domainCode = "JCY", subDomainCode = "supply")
    public CallResult<List<SupplierExternalSaveVO>> batchSave(List<SupplierExternalSaveDTO> params) {
        ValidatorUtils.checkTrueThrowEx(CollectionUtils.isEmpty(params), "数据不能为空");
        ValidatorUtils.checkTrueThrowEx(params.size() > 20, "推送数据不能超过20条");

        String key = null;
        try {
            OperationModel operationModel = UserHandleUtils.getOperationModel();

            key = String.join(DistributedLockConstant.DELIMITER, DistributedLockConstant.SUPPLIER_DA_OPEN_SAVE_LOCK_KEY, operationModel.getEnterpriseNo());

            if (!redisClientUtil.lock(key, DistributedLockConstant.DEFAULT_WAIT_TIME, DistributedLockConstant.DEFAULT_LEASE_TIME)) {
                return CallResult.failed(ErrorCode.BUSINESS_ERROR_CODE, "正在处理，请稍后提交！");
            }

            return CallResult.success(MessageVO.SUCCESS, supplierV2ExternalService.batchSaveForDA(operationModel, params));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        } finally {
            if (null != key) {
                redisClientUtil.unlock(key);
            }
        }
    }
}
