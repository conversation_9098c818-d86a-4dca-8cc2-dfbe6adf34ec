package com.yyigou.dsrp.cdc.provider.api.v2.supplier;

import com.alibaba.dubbo.config.annotation.Service;
import com.yyigou.ddc.common.exception.CommonExceptionHandler;
import com.yyigou.ddc.common.message.MessageVO;
import com.yyigou.ddc.common.page.PageDto;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.ddc.common.service.ServiceBaseAbstract;
import com.yyigou.ddc.common.service.annotation.Interface;
import com.yyigou.ddc.common.service.annotation.Method;
import com.yyigou.ddc.common.service.annotation.MethodType;
import com.yyigou.dsrp.cdc.api.v2.supplier.SupplierApplyAPI;
import com.yyigou.dsrp.cdc.api.v2.supplier.dto.*;
import com.yyigou.dsrp.cdc.api.v2.supplier.vo.SupplierApplyDetailVO;
import com.yyigou.dsrp.cdc.api.v2.supplier.vo.SupplierApplyPageVO;
import com.yyigou.dsrp.cdc.common.util.OperationModel;
import com.yyigou.dsrp.cdc.model.constant.ViewNameConstant;
import com.yyigou.dsrp.cdc.model.v2.supplier.req.*;
import com.yyigou.dsrp.cdc.service.utils.RedisClientUtil;
import com.yyigou.dsrp.cdc.service.utils.UserHandleUtils;
import com.yyigou.dsrp.cdc.service.v2.supplier.SupplierApplyService;
import com.yyigou.dsrp.gcs.common.util.BeanUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
@Service
@RequiredArgsConstructor
@Slf4j
@Interface(name = "供应商档案申请页面接口")
public class SupplierApplyProvider extends ServiceBaseAbstract implements SupplierApplyAPI {
    @Resource(name = "supplierApplyService")
    private SupplierApplyService supplierApplyService;

    @Resource
    private RedisClientUtil redisClientUtil;
    
    @Override
    @Method(aliasName = "dsrp.cdc.supplierapply.v2.findListPage", name = "申请视角：供应商申请列表查询", viewNo = ViewNameConstant.BDC_SUPPLIER_APPLY_VIEW, orgAuth = true, methodType = MethodType.QUERY, processState = 2, version = "1.0.1")
    public CallResult<PageVo<SupplierApplyPageVO>> applyFindListPage(SupplierApplyQueryListPageDTO params, PageDto pageParams) {
        try {
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            SupplierApplyQueryListPageReq queryReq = BeanUtil.copyFields(params, SupplierApplyQueryListPageReq.class);
            //补充租户信息
            queryReq.setEnterpriseNo(operationModel.getEnterpriseNo());
            return CallResult.success(MessageVO.SUCCESS, supplierApplyService.applyFindListPage(operationModel, queryReq, pageParams));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.supplierapply.v2.findApproveListPage", name = "管理视角：供应商申请待审核列表查询", viewNo = ViewNameConstant.BDC_SUPPLIER_APPLY_VIEW, orgAuth = true, methodType = MethodType.QUERY, processState = 2, version = "1.0.1")
    public CallResult<PageVo<SupplierApplyPageVO>> approveFindListPage(SupplierApplyApproveQueryListPageDTO params, PageDto pageParams) {
        try {
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            SupplierApplyApproveQueryListPageReq queryReq = BeanUtil.copyFields(params, SupplierApplyApproveQueryListPageReq.class);
            //补充租户信息
            queryReq.setEnterpriseNo(operationModel.getEnterpriseNo());
            return CallResult.success(MessageVO.SUCCESS, supplierApplyService.approveFindListPage(operationModel, queryReq, pageParams));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.supplierapply.v2.getApprovePendingCount", name = "供应商申请待审核数量查询", viewNo = ViewNameConstant.BDC_SUPPLIER_APPLY_VIEW, orgAuth = true, methodType = MethodType.QUERY, processState = 2, version = "1.0.1")
    public CallResult<Long> getPendingCount() {
        OperationModel operationModel = UserHandleUtils.getOperationModel();

        return CallResult.success(MessageVO.SUCCESS, supplierApplyService.getPendingCount(operationModel));
    }

    @Override
    @Method(aliasName = "dsrp.cdc.supplierapply.v2.getDetail", name = "供应商申请详情查看", viewNo = ViewNameConstant.BDC_SUPPLIER_APPLY_VIEW, methodType = MethodType.DETAIL, processState = 2, version = "1.0.1")
    public CallResult<SupplierApplyDetailVO> getDetail(SupplierApplyGetDTO params) {
        try {
            OperationModel operationModel = com.yyigou.dsrp.cdc.service.utils.UserHandleUtils.getOperationModel();

            SupplierApplyGetReq supplierApplyGetReq = BeanUtil.copyFields(params, SupplierApplyGetReq.class);
            //补充租户信息
            supplierApplyGetReq.setEnterpriseNo(operationModel.getEnterpriseNo());

            return CallResult.success(MessageVO.SUCCESS, supplierApplyService.getDetail(operationModel, supplierApplyGetReq));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.supplierapply.v2.save", name = "供应商申请编辑", viewNo = ViewNameConstant.BDC_SUPPLIER_APPLY_VIEW, methodType = MethodType.UPDATE, processState = 2, version = "1.0.1")
    public CallResult<String> applySaveOrUpdate(SupplierApplySaveOrUpdateDTO params) {
        try {
            OperationModel operationModel = com.yyigou.dsrp.cdc.service.utils.UserHandleUtils.getOperationModel();

            SupplierApplySaveOrUpdateReq supplierApplySaveOrUpdateReq = BeanUtil.copyFields(params, SupplierApplySaveOrUpdateReq.class);
            //补充租户信息
            supplierApplySaveOrUpdateReq.setEnterpriseNo(operationModel.getEnterpriseNo());

            return CallResult.success(MessageVO.SUCCESS, supplierApplyService.applySaveOrUpdate(operationModel, supplierApplySaveOrUpdateReq));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.supplierapply.v2.approve", name = "供应商申请审批", viewNo = ViewNameConstant.BDC_SUPPLIER_APPLY_VIEW, methodType = MethodType.UPDATE, processState = 2, version = "1.0.1")
    public CallResult<Boolean> manageApprove(SupplierApplyApproveDTO params) {
        try {
            OperationModel operationModel = com.yyigou.dsrp.cdc.service.utils.UserHandleUtils.getOperationModel();

            SupplierApplyApproveReq supplierApplyApproveReq = BeanUtil.copyFields(params, SupplierApplyApproveReq.class);
            //补充租户信息
            supplierApplyApproveReq.setEnterpriseNo(operationModel.getEnterpriseNo());
            supplierApplyApproveReq.setAutoAudit(false);

            return CallResult.success(MessageVO.SUCCESS, supplierApplyService.manageApprove(operationModel, supplierApplyApproveReq));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.supplierapply.v2.withdraw", name = "供应商申请撤回", viewNo = ViewNameConstant.BDC_SUPPLIER_APPLY_VIEW, methodType = MethodType.UPDATE, processState = 2, version = "1.0.1")
    public CallResult<Boolean> withDrawApply(SupplierApplyWithdrawDTO params) {
        try {
            OperationModel operationModel = com.yyigou.dsrp.cdc.service.utils.UserHandleUtils.getOperationModel();

            SupplierApplyWithdrawReq supplierApplyWithdrawReq = BeanUtil.copyFields(params, SupplierApplyWithdrawReq.class);
            //补充租户信息
            supplierApplyWithdrawReq.setEnterpriseNo(operationModel.getEnterpriseNo());

            return CallResult.success(MessageVO.SUCCESS, supplierApplyService.withDrawApply(operationModel, supplierApplyWithdrawReq));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.supplierapply.v2.delete", name = "供应商申请删除", viewNo = ViewNameConstant.BDC_SUPPLIER_APPLY_VIEW, methodType = MethodType.UPDATE, processState = 2, version = "1.0.1")
    public CallResult<Boolean> deleteApply(SupplierApplyDeleteDTO params) {
        try {
            OperationModel operationModel = com.yyigou.dsrp.cdc.service.utils.UserHandleUtils.getOperationModel();

            SupplierApplyDeleteReq supplierApplyDeleteReq = BeanUtil.copyFields(params, SupplierApplyDeleteReq.class);
            //补充租户信息
            supplierApplyDeleteReq.setEnterpriseNo(operationModel.getEnterpriseNo());

            return CallResult.success(MessageVO.SUCCESS, supplierApplyService.deleteApply(operationModel, supplierApplyDeleteReq));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }
}
