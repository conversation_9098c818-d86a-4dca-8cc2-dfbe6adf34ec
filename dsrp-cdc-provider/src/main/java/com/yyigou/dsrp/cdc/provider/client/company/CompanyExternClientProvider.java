package com.yyigou.dsrp.cdc.provider.client.company;

import com.alibaba.dubbo.config.annotation.Service;
import com.yyigou.ddc.common.exception.CommonExceptionHandler;
import com.yyigou.ddc.common.message.MessageVO;
import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.dsrp.cdc.client.company.CompanyExternClient;
import com.yyigou.dsrp.cdc.client.company.response.CompanyInfoResponse;
import com.yyigou.dsrp.cdc.service.company.CompanyService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
@Service
@RequiredArgsConstructor
@Slf4j
public class CompanyExternClientProvider implements CompanyExternClient {

    @Resource
    private CompanyService companyService;


    @Override
    public CallResult<List<CompanyInfoResponse>> saveAndGetCompany(String enterpriseNo, List<String> companyNameList, String employerNo, String userName) {
        try {
            return CallResult.success(MessageVO.SUCCESS, companyService.saveAndGetCompany(enterpriseNo, companyNameList, employerNo, userName));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }
}
