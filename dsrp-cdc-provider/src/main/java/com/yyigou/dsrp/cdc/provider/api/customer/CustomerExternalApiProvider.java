package com.yyigou.dsrp.cdc.provider.api.customer;

import com.alibaba.dubbo.config.annotation.Service;
import com.yyigou.ddc.common.exception.CommonExceptionHandler;
import com.yyigou.ddc.common.message.MessageVO;
import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.ddc.common.service.ServiceBaseAbstract;
import com.yyigou.ddc.common.service.annotation.Interface;
import com.yyigou.ddc.common.service.annotation.Method;
import com.yyigou.ddc.common.service.annotation.MethodType;
import com.yyigou.dsrp.cdc.api.customer.CustomerExternalApi;
import com.yyigou.dsrp.cdc.api.customer.dto.CustomerExternalAssignDTO;
import com.yyigou.dsrp.cdc.api.customer.dto.CustomerExternalSaveDTO;
import com.yyigou.dsrp.cdc.api.customer.vo.CustomerExternalSaveVO;
import com.yyigou.dsrp.cdc.common.util.OperationModel;
import com.yyigou.dsrp.cdc.service.customer.CustomerExternService;
import com.yyigou.dsrp.cdc.service.utils.UserHandleUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Service
@RequiredArgsConstructor
@Slf4j
@Interface(name = "客户档案对外接口")
public class CustomerExternalApiProvider extends ServiceBaseAbstract implements CustomerExternalApi {

    private final CustomerExternService customerExternService;


    /**
     * 保存客户档案(迪安)
     *
     * @param params@return
     */
    @Override
    @Method(aliasName = "dsrp.cdc.open.external.customer.batchSave", name = "客户档案保存接口-对外", methodType = MethodType.ADD, processState = 2, requestSession = true, requestAuthentication = true, invokeRange = Method.InvokeRange.OUTER_OPEN, domainCode = "JCY", subDomainCode = "customer")
    public CallResult<List<CustomerExternalSaveVO>> saveCustomer(List<CustomerExternalSaveDTO> params) {
        try {
            //补充租户信息
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            return CallResult.success(MessageVO.SUCCESS, customerExternService.batchSave(operationModel, params));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.open.external.customer.customerAssign", name = "客户档案分配接口-对外", methodType = MethodType.ADD, processState = 2, requestSession = true, requestAuthentication = true, invokeRange = Method.InvokeRange.OUTER_OPEN, domainCode = "JCY", subDomainCode = "customer")
    public CallResult<Boolean> customerAssign(CustomerExternalAssignDTO params) {
        try {
            //补充租户信息
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            return CallResult.success(MessageVO.SUCCESS, customerExternService.customerAssign(operationModel, params));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }
}
