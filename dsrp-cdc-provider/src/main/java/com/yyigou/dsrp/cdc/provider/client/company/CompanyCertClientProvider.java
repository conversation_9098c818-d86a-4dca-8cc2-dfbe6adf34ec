package com.yyigou.dsrp.cdc.provider.client.company;

import com.alibaba.dubbo.config.annotation.Service;
import com.yyigou.ddc.common.exception.CommonExceptionHandler;
import com.yyigou.ddc.common.message.MessageVO;
import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.dsrp.cdc.client.company.CompanyCertClient;
import com.yyigou.dsrp.cdc.client.company.request.CompanyCertFindRequest;
import com.yyigou.dsrp.cdc.client.company.response.CompanyCertFindResponse;
import com.yyigou.dsrp.cdc.service.company.CompanyCertService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
@Service
@RequiredArgsConstructor
@Slf4j
public class CompanyCertClientProvider implements CompanyCertClient {

    @Resource
    private CompanyCertService companyCertService;

    /**
     * 获取企业证照信息
     *
     * @param request
     * @return
     */
    @Override
    public CallResult<List<CompanyCertFindResponse>> findCompanyCertList(CompanyCertFindRequest request) {
        try {
            return CallResult.success(MessageVO.SUCCESS, companyCertService.findCompanyCertList(request));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }
}
