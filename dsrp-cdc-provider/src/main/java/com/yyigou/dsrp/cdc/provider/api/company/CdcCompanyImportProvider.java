package com.yyigou.dsrp.cdc.provider.api.company;

import com.alibaba.dubbo.config.annotation.Service;
import com.yyigou.ddc.common.exception.CommonExceptionHandler;
import com.yyigou.ddc.common.message.MessageVO;
import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.ddc.common.service.ServiceBaseAbstract;
import com.yyigou.ddc.common.service.annotation.Interface;
import com.yyigou.ddc.services.ddc.task.message.incoming.DataImportDataSpiceMessage;
import com.yyigou.ddc.services.ddc.task.message.incoming.UapExcelImportDataSpiceMessage;
import com.yyigou.ddc.services.ddc.task.vo.incoming.ImportTaskDataSpiceVO;
import com.yyigou.dsrp.cdc.api.company.CdcCompanyImportAPI;
import com.yyigou.dsrp.cdc.service.company.CompanyImportService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Service
@RequiredArgsConstructor
@Slf4j
@Interface(name = "企业档案导入接口")
public class CdcCompanyImportProvider extends ServiceBaseAbstract implements CdcCompanyImportAPI {
    @Autowired
    private CompanyImportService companyImportService;

    /**
     * 分片校验逻辑
     *
     * @param uapExcelImportDataSpiceMessage
     * @return
     */
    @Override
    public CallResult<ImportTaskDataSpiceVO> checkExcelSliceData(UapExcelImportDataSpiceMessage uapExcelImportDataSpiceMessage) {
        try {
            return CallResult.success(MessageVO.SUCCESS, companyImportService.checkExcelSliceData(uapExcelImportDataSpiceMessage));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 分片导入逻辑
     *
     * @param dataImportDataSpiceMessage
     * @return
     */
    @Override
    public CallResult<ImportTaskDataSpiceVO> handleImportSliceData(DataImportDataSpiceMessage dataImportDataSpiceMessage) {
        try {
            return CallResult.success(MessageVO.SUCCESS, companyImportService.handleImportSliceData(dataImportDataSpiceMessage));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }
}
