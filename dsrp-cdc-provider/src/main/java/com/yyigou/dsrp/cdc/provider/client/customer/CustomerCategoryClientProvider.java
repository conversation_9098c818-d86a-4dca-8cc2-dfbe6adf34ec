package com.yyigou.dsrp.cdc.provider.client.customer;

import com.alibaba.dubbo.config.annotation.Service;
import com.yyigou.ddc.common.exception.CommonExceptionHandler;
import com.yyigou.ddc.common.message.MessageVO;
import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.dsrp.cdc.client.customer.CustomerCategoryClient;
import com.yyigou.dsrp.cdc.client.customer.response.response.CustomerCategoryResponse;
import com.yyigou.dsrp.cdc.dao.customer.entity.CustomerCategory;
import com.yyigou.dsrp.cdc.service.customer.CustomerCategoryService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
@Service
@RequiredArgsConstructor
@Slf4j
public class CustomerCategoryClientProvider implements CustomerCategoryClient {

    private final CustomerCategoryService customerCategoryService;


    @Override
    public CallResult<List<CustomerCategoryResponse>> queryCategoryNoList(String enterpriseNo, List<String> categoryNoList) {
        try {
            List<CustomerCategoryResponse> result = new ArrayList<>();
            final List<CustomerCategory> categoryList = customerCategoryService.getCategoryNoList(enterpriseNo, categoryNoList);
            categoryList.forEach(t -> {
                CustomerCategoryResponse response = new CustomerCategoryResponse();
                BeanUtils.copyProperties(t, response);
                result.add(response);
            });
            return CallResult.success(MessageVO.SUCCESS, result);
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

}
