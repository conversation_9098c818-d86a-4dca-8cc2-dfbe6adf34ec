package com.yyigou.dsrp.cdc.provider.api.customer;

import com.alibaba.dubbo.config.annotation.Service;
import com.yyigou.ddc.common.exception.CommonExceptionHandler;
import com.yyigou.ddc.common.message.MessageVO;
import com.yyigou.ddc.common.page.PageDto;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.ddc.common.service.ServiceBaseAbstract;
import com.yyigou.ddc.common.service.annotation.Interface;
import com.yyigou.ddc.common.service.annotation.Method;
import com.yyigou.ddc.common.service.annotation.MethodType;
import com.yyigou.ddc.services.dsrp.bdc.vo.CustomerSalesManVo;
import com.yyigou.dsrp.cdc.api.customer.CustomerSaleManApi;
import com.yyigou.dsrp.cdc.api.customer.dto.CustomerSalesManQueryBySpecifyDTO;
import com.yyigou.dsrp.cdc.api.customer.dto.CustomerSalesManQueryDTO;
import com.yyigou.dsrp.cdc.api.customer.vo.CustomerSalesManVO;
import com.yyigou.dsrp.cdc.common.util.OperationModel;
import com.yyigou.dsrp.cdc.service.customer.CustomerSaleManService;
import com.yyigou.dsrp.cdc.service.utils.UserHandleUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Service
@RequiredArgsConstructor
@Slf4j
@Interface(name = "客户销售人")
public class CustomerSaleManProvider extends ServiceBaseAbstract implements CustomerSaleManApi {

    private final CustomerSaleManService customerSaleManService;

    /**
     * 跨组织获取客户销售人列表
     *
     * @param customerCode
     * @param orgNo
     * @return
     */
    @Override
    @Method(aliasName = "dsrp.cdc.customerSale.org.getCustomerSaleManListByOrg", name = "跨组织获取客户负责人列表", methodType = MethodType.QUERY, processState = 2, requestSession = false, requestAuthentication = false)
    public CallResult<List<CustomerSalesManVO>> getCustomerSaleManListByOrg(String customerCode, String orgNo) {
        try {
            //补充租户信息
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            return CallResult.success(MessageVO.SUCCESS, customerSaleManService.getCustomerSaleManListByOrg(operationModel, customerCode, orgNo));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 获取客户销售人列表
     *
     * @param params
     * @return
     */
    @Override
    @Method(aliasName = "dsrp.cdc.customerSale.findList", name = "获取客户负责人列表", methodType = MethodType.QUERY, processState = 2, requestSession = false, requestAuthentication = false)
    public CallResult<List<CustomerSalesManVo>> findList(CustomerSalesManQueryDTO params) {
        try {
            //补充租户信息
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            return CallResult.success(MessageVO.SUCCESS, customerSaleManService.findList(operationModel, params));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 获取客户销售人列表
     *
     * @param params
     * @param pageDto
     * @return
     */
    @Override
    @Method(aliasName = "dsrp.cdc.customerSale.findPage", name = "获取客户负责人列表", methodType = MethodType.QUERY, processState = 2, requestSession = false, requestAuthentication = false)
    public CallResult<PageVo<CustomerSalesManVo>> findPage(CustomerSalesManQueryDTO params, PageDto pageDto) {
        try {
            //补充租户信息
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            return CallResult.success(MessageVO.SUCCESS, customerSaleManService.findPage(operationModel, params, pageDto));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 获取客户销售人列表
     *
     * @param params
     * @return
     */
    @Override
    @Method(aliasName = "dsrp.cdc.customerSale.org.findListBySpecifyOrg", name = "跨组织获取客户负责人列表", methodType = MethodType.QUERY, processState = 2, requestSession = false, requestAuthentication = false)
    public CallResult<List<CustomerSalesManVo>> findListBySpecifyOrg(CustomerSalesManQueryBySpecifyDTO params) {
        try {
            //补充租户信息
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            return CallResult.success(MessageVO.SUCCESS, customerSaleManService.findListBySpecifyOrg(operationModel, params));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 获取客户销售人列表
     *
     * @param params
     * @param pageDto
     * @return
     */
    @Override
    @Method(aliasName = "dsrp.cdc.customerSale.org.findPageBySpecifyOrg", name = "跨组织获取客户负责人列表", methodType = MethodType.QUERY, processState = 2, requestSession = false, requestAuthentication = false)
    public CallResult<PageVo<CustomerSalesManVo>> findPageBySpecifyOrg(CustomerSalesManQueryBySpecifyDTO params, PageDto pageDto) {
        try {
            //补充租户信息
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            return CallResult.success(MessageVO.SUCCESS, customerSaleManService.findPageBySpecifyOrg(operationModel, params, pageDto));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }
}
