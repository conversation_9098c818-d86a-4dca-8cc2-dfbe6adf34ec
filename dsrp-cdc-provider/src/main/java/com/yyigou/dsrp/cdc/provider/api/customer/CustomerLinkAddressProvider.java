package com.yyigou.dsrp.cdc.provider.api.customer;

import com.alibaba.dubbo.config.annotation.Service;
import com.yyigou.ddc.common.exception.CommonExceptionHandler;
import com.yyigou.ddc.common.message.MessageVO;
import com.yyigou.ddc.common.page.PageDto;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.ddc.common.service.ServiceBaseAbstract;
import com.yyigou.ddc.common.service.annotation.Interface;
import com.yyigou.ddc.common.service.annotation.Method;
import com.yyigou.ddc.common.service.annotation.MethodType;
import com.yyigou.dsrp.cdc.api.company.vo.CompanyShippingAddressVO;
import com.yyigou.dsrp.cdc.api.customer.CustomerLinkAddressApi;
import com.yyigou.dsrp.cdc.api.customer.dto.CustomerLinkAddressByOrgQueryDTO;
import com.yyigou.dsrp.cdc.api.customer.dto.CustomerLinkAddressQueryDTO;
import com.yyigou.dsrp.cdc.api.customer.dto.CustomerLinkAddressSaveByOrgDTO;
import com.yyigou.dsrp.cdc.api.customer.dto.CustomerLinkAddressSaveDTO;
import com.yyigou.dsrp.cdc.common.util.OperationModel;
import com.yyigou.dsrp.cdc.service.customer.CustomerLinkAddressService;
import com.yyigou.dsrp.cdc.service.utils.UserHandleUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;


@Component
@Service
@RequiredArgsConstructor
@Slf4j
@Interface(name = "客户联系地址")
public class CustomerLinkAddressProvider extends ServiceBaseAbstract implements CustomerLinkAddressApi {

    private final CustomerLinkAddressService customerLinkAddressService;


    /**
     * 获取客户联系地址列表
     *
     * @param customerCode
     * @return
     */
    @Override
    @Method(aliasName = "dsrp.cdc.customer.getCustomerLinkAddressList", name = "获取客户联系地址列表", methodType = MethodType.QUERY, processState = 2, requestSession = false, requestAuthentication = false)
    public CallResult<List<CompanyShippingAddressVO>> getCustomerLinkAddressList(String customerCode) {
        try {
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            return CallResult.success(MessageVO.SUCCESS, customerLinkAddressService.getCustomerLinkAddressList(operationModel, customerCode));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 取客户联系地址列表(分页)
     *
     * @param params
     * @param pageDto
     */
    @Override
    @Method(aliasName = "dsrp.cdc.customer.findCustomerLinkAddressPage", name = "获取客户联系地址列表(分页)", methodType = MethodType.QUERY, processState = 2, requestSession = false, requestAuthentication = false)
    public CallResult<PageVo<CompanyShippingAddressVO>> findCustomerLinkAddressPage(CustomerLinkAddressQueryDTO params, PageDto pageDto) {
        try {
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            return CallResult.success(MessageVO.SUCCESS, customerLinkAddressService.findCustomerLinkAddressPage(operationModel, params, pageDto));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 保存客户联系地址列表
     *
     * @param params
     * @return
     */
    @Override
    @Method(aliasName = "dsrp.cdc.customer.saveCustomerLinkAddress", name = "保存客户联系地址列表", methodType = MethodType.QUERY, processState = 2, requestSession = false, requestAuthentication = false)
    public CallResult<CompanyShippingAddressVO> saveCustomerLinkAddress(CustomerLinkAddressSaveDTO params) {
        try {
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            return CallResult.success(MessageVO.SUCCESS, customerLinkAddressService.saveCustomerLinkAddress(operationModel, params));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 编辑联系地址
     *
     * @param params
     * @return
     */
    @Override
    @Method(aliasName = "dsrp.cdc.customer.editCustomerLinkAddress", name = "编辑客户联系地址列表", methodType = MethodType.QUERY, processState = 2, requestSession = false, requestAuthentication = false)
    public CallResult<CompanyShippingAddressVO> editCustomerLinkAddress(CustomerLinkAddressSaveDTO params) {
        try {
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            return CallResult.success(MessageVO.SUCCESS, customerLinkAddressService.editCustomerLinkAddress(operationModel, params));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 跨组织获取客户联系地址列表
     *
     * @param customerCode
     * @param orgNo
     * @return
     */
    @Override
    @Method(aliasName = "dsrp.cdc.customer.org.getCustomerLinkAddressListByOrg", name = "跨组织获取客户联系地址列表", methodType = MethodType.QUERY, processState = 2, requestSession = false, requestAuthentication = false)
    public CallResult<List<CompanyShippingAddressVO>> getCustomerLinkAddressListByOrg(String customerCode, String orgNo) {
        try {
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            return CallResult.success(MessageVO.SUCCESS, customerLinkAddressService.getCustomerLinkAddressListByOrg(operationModel, customerCode, orgNo));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 跨组织获取客户联系地址列表
     *
     * @param params
     * @param pageDto
     * @return
     */
    @Override
    @Method(aliasName = "dsrp.cdc.customer.org.findCustomerLinkAddressListByOrgPage", name = "跨组织获取客户联系地址列表(分页)", methodType = MethodType.QUERY, processState = 2, requestSession = false, requestAuthentication = false)
    public CallResult<PageVo<CompanyShippingAddressVO>> findCustomerLinkAddressListByOrgPage(CustomerLinkAddressByOrgQueryDTO params, PageDto pageDto) {
        try {
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            return CallResult.success(MessageVO.SUCCESS, customerLinkAddressService.findCustomerLinkAddressListByOrgPage(operationModel, params, pageDto));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 跨组织保存客户联系地址列表
     *
     * @param params
     * @return
     */
    @Override
    @Method(aliasName = "dsrp.cdc.customer.org.saveCustomerLinkAddressByOrg", name = "跨组织保存客户联系地址列表", methodType = MethodType.QUERY, processState = 2, requestSession = false, requestAuthentication = false)
    public CallResult<CompanyShippingAddressVO> saveCustomerLinkAddressByOrg(CustomerLinkAddressSaveByOrgDTO params) {
        try {
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            return CallResult.success(MessageVO.SUCCESS, customerLinkAddressService.saveCustomerLinkAddressByOrg(operationModel, params));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 跨组织编辑联系地址
     *
     * @param params
     * @return
     */
    @Override
    @Method(aliasName = "dsrp.cdc.customer.org.editCustomerLinkAddressByOrg", name = "跨组织删除联系地址", methodType = MethodType.QUERY, processState = 2, requestSession = false, requestAuthentication = false)
    public CallResult<CompanyShippingAddressVO> editCustomerLinkAddressByOrg(CustomerLinkAddressSaveByOrgDTO params) {
        try {
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            return CallResult.success(MessageVO.SUCCESS, customerLinkAddressService.editCustomerLinkAddressByOrg(operationModel, params));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 跨组织删除联系地址
     *
     * @param linkAddressId
     * @return
     */
    @Override
    @Method(aliasName = "dsrp.cdc.customer.org.deleteCustomerLinkAddressByOrg", name = "跨组织删除联系地址", methodType = MethodType.QUERY, processState = 2, requestSession = false, requestAuthentication = false)
    public CallResult<Boolean> deleteCustomerLinkAddressByOrg(Long linkAddressId) {
        try {
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            return CallResult.success(MessageVO.SUCCESS, customerLinkAddressService.deleteCustomerLinkAddressByOrg(operationModel, linkAddressId));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }


}
