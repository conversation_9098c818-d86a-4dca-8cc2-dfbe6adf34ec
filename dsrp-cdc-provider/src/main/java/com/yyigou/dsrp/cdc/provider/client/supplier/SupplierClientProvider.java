package com.yyigou.dsrp.cdc.provider.client.supplier;

import com.alibaba.dubbo.config.annotation.Service;
import com.yyigou.ddc.common.exception.CommonExceptionHandler;
import com.yyigou.ddc.common.message.MessageVO;
import com.yyigou.ddc.common.page.PageDto;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.dsrp.cdc.client.common.response.QueryUseInfoResponse;
import com.yyigou.dsrp.cdc.client.supplier.SupplierClient;
import com.yyigou.dsrp.cdc.client.supplier.request.SupplierComponentRequest;
import com.yyigou.dsrp.cdc.client.supplier.request.SupplierFindRequest;
import com.yyigou.dsrp.cdc.client.supplier.request.SupplierNameRequest;
import com.yyigou.dsrp.cdc.client.supplier.request.SupplierNoRequest;
import com.yyigou.dsrp.cdc.client.supplier.response.SupplierComponentResponse;
import com.yyigou.dsrp.cdc.client.supplier.response.SupplierInfoResponse;
import com.yyigou.dsrp.cdc.client.supplier.response.SupplierResponse;
import com.yyigou.dsrp.cdc.service.common.MasterDataSyncService;
import com.yyigou.dsrp.cdc.service.supplier.SupplierService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
@Service
@RequiredArgsConstructor
@Slf4j
public class SupplierClientProvider implements SupplierClient {

    @Resource
    private SupplierService supplierService;
    private final MasterDataSyncService masterDataSyncService;

    /**
     * 根据供应商名称查询供应商信息
     *
     * @param request
     * @return: {@link CallResult <  List <  SupplierInfoResponse >>}
     */
    @Override
    public CallResult<List<SupplierInfoResponse>> findSupplierByName(SupplierNameRequest request) {
        try {
            return CallResult.success(MessageVO.SUCCESS, supplierService.findSupplierByName(request));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 根据供应商编号集合查询供应商信息
     *
     * @param request
     * @return: {@link CallResult< List< SupplierInfoResponse>>}
     */
    @Override
    public CallResult<List<SupplierInfoResponse>> findSupplierByNo(SupplierNoRequest request) {
        try {
            return CallResult.success(MessageVO.SUCCESS, supplierService.findSupplierByNo(request));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 查询客户信息
     *
     * @param enterpriseNo
     * @param request
     * @return
     */
    @Override
    public CallResult<List<SupplierResponse>> findSupplier(String enterpriseNo, SupplierFindRequest request) {
        try {
            return CallResult.success(MessageVO.SUCCESS, supplierService.findSupplier(enterpriseNo, request));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 查询集团租户供应商引用信息
     *
     * @param enterpriseNo
     * @param supplierCode
     * @return: {@link CallResult< List<   String  >>}
     */
    @Override
    public CallResult<List<String>> findSupplierUserList(String enterpriseNo, String supplierCode) {
        try {
            return CallResult.success(MessageVO.SUCCESS, supplierService.findSupplierUserList(enterpriseNo, supplierCode));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 处理供应商新增申请
     *
     * @param applyJson
     * @return
     */
    @Override
    public CallResult<String> handleSupplierApply(String applyJson) {
        try {
            return CallResult.success(MessageVO.SUCCESS, masterDataSyncService.handleSupplierApply(applyJson));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }


    /**
     * 分页查询供应商
     *
     * @param params
     * @param pageDto
     * @return
     */
    @Override
    public CallResult<PageVo<SupplierComponentResponse>> selectSupplierPageForCommonComponent(SupplierComponentRequest params, PageDto pageDto) {
        try {
            return CallResult.success(MessageVO.SUCCESS, supplierService.selectSupplierPageForCommonComponent(params, pageDto));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 按指定条件查询供应商
     *
     * @param params
     * @return
     */
    @Override
    public CallResult<List<SupplierComponentResponse>> selectSupplierListForCommonComponent(SupplierComponentRequest params) {
        try {
            return CallResult.success(MessageVO.SUCCESS, supplierService.selectSupplierListForCommonComponent(params));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 查询供应商详情
     *
     * @param enterpriseNo 租户编号
     * @param supplierNo   供应商no
     * @return
     */
    @Override
    public CallResult<SupplierInfoResponse> selectSupplierInfo(String enterpriseNo, String supplierNo) {
        try {
            return CallResult.success(MessageVO.SUCCESS, supplierService.selectSupplierInfo(enterpriseNo, supplierNo));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 查询供应商使用信息
     *
     * @param enterpriseNo 租户编号
     * @param supplierCode 供应商no
     * @return
     */
    @Override
    public CallResult<List<QueryUseInfoResponse>> queryUseInfo(String enterpriseNo, String supplierCode) {
        try {
            return CallResult.success(MessageVO.SUCCESS, supplierService.queryUseInfo(enterpriseNo, supplierCode));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }
}
