package com.yyigou.dsrp.cdc.provider.client.v2.customer;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.config.annotation.Service;
import com.yyigou.ddc.common.exception.BusinessException;
import com.yyigou.ddc.common.page.PageDto;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.ddc.common.treegrid.TreeGrid;
import com.yyigou.ddc.meta.base.service.referrpc.ReferQueryCondition;
import com.yyigou.ddc.meta.base.service.referrpc.ReferRpcReqDto;
import com.yyigou.ddc.meta.base.service.referrpc.service.IReferQueryFunc;
import com.yyigou.dsrp.cdc.api.v2.customer.vo.CustomerCategoryTreeVO;
import com.yyigou.dsrp.cdc.common.util.OperationModel;
import com.yyigou.dsrp.cdc.model.v2.customer.req.CustomerCategoryQuerySolutionTreeReq;
import com.yyigou.dsrp.cdc.provider.client.v2.customer.vo.AttrValue;
import com.yyigou.dsrp.cdc.service.utils.UserHandleUtils;
import com.yyigou.dsrp.cdc.service.v2.customer.CustomerCategoryV2Service;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@Service(group = "cdcCustomerCategory")
@RequiredArgsConstructor
@Slf4j
public class CustomerCategoryV2ReferProvider implements IReferQueryFunc {
    private static final int MAX_LAYER = 10;

    @Resource
    private CustomerCategoryV2Service customerCategoryV2Service;

    @Override
    public PageVo<Map<String, Object>> queryReferData(ReferRpcReqDto referRpcReqDto) {
        log.warn("cdcCustomerCategoryReferRpcReqDto={}", referRpcReqDto);

        if (null == referRpcReqDto.getPageDto()) {
            PageDto pageDto = new PageDto();
            pageDto.setPageIndex(1);
            pageDto.setPageSize(100000);
            referRpcReqDto.setPageDto(pageDto);
        }

        OperationModel operationModel = UserHandleUtils.getOperationModel();

        CustomerCategoryQuerySolutionTreeReq customerCategoryQuerySolutionTreeReq = packCustomerCategoryQuerySolutionTreeReq(referRpcReqDto, operationModel);

        List<CustomerCategoryTreeVO> customerCategoryTreeVOS = customerCategoryV2Service.queryReferTree(operationModel, customerCategoryQuerySolutionTreeReq);

        log.warn("customerCategoryTreeVOS={}", customerCategoryTreeVOS);

        PageVo<Map<String, Object>> result = new PageVo<>();
        result.setPageIndex(referRpcReqDto.getPageDto().getPageIndex());
        result.setPageSize(referRpcReqDto.getPageDto().getPageSize());
        result.setPageCount(1);

        //把supplierCategoryTreeVO转成attrValue，children需要递归设置
        if (CollectionUtils.isNotEmpty(customerCategoryTreeVOS)) {
            List<Map<String, Object>> outerWrappers = customerCategoryTreeVOS.stream().map(supplierCategoryTreeVO -> {
                AttrValue attrValue = new AttrValue();

                attrValue.setParent_no(supplierCategoryTreeVO.getParentNo());
                attrValue.setNo(supplierCategoryTreeVO.getNo());
                attrValue.setCategory_code(supplierCategoryTreeVO.getCategoryCode());
                attrValue.setCategory_name(supplierCategoryTreeVO.getCategoryName());
                attrValue.setParentCode(supplierCategoryTreeVO.getParentNo());
                attrValue.setChildren(getChildren(supplierCategoryTreeVO));

                return BeanUtil.beanToMap(attrValue);
            }).collect(Collectors.toList());

            result.setRows(outerWrappers);

            log.warn("cdcCustomerCategoryResult={}", result);

            return result;
        }

        return result;
    }

    @NotNull
    private static CustomerCategoryQuerySolutionTreeReq packCustomerCategoryQuerySolutionTreeReq(ReferRpcReqDto referRpcReqDto, OperationModel operationModel) {
        CustomerCategoryQuerySolutionTreeReq customerCategoryQuerySolutionTreeReq = new CustomerCategoryQuerySolutionTreeReq();
        customerCategoryQuerySolutionTreeReq.setEnterpriseNo(operationModel.getEnterpriseNo());
        if (CollectionUtils.isNotEmpty(referRpcReqDto.getConditions())) {
            List<CustomerCategoryQuerySolutionTreeReq.QueryCondition> queryConditionList = new ArrayList<>(referRpcReqDto.getConditions().size());
            CustomerCategoryQuerySolutionTreeReq.QueryCondition queryCondition = new CustomerCategoryQuerySolutionTreeReq.QueryCondition();
            for (ReferQueryCondition condition : referRpcReqDto.getConditions()) {
                queryCondition.setColumn(condition.getColumn());
                queryCondition.setValue(condition.getValue());
                queryCondition.setOperator(condition.getOperator());

                queryConditionList.add(queryCondition);
            }
            customerCategoryQuerySolutionTreeReq.setQueryConditionList(queryConditionList);
        }
        return customerCategoryQuerySolutionTreeReq;
    }

    private List<AttrValue> getChildren(CustomerCategoryTreeVO supplierCategoryTreeVO) {
        return getChildren(supplierCategoryTreeVO, 1);
    }

    private List<AttrValue> getChildren(CustomerCategoryTreeVO supplierCategoryTreeVO, int level) {
        if (level > MAX_LAYER) {
            throw new BusinessException("分类层级异常");
        }

        if (CollectionUtils.isNotEmpty(supplierCategoryTreeVO.getChildren())) {
            List<AttrValue> children = new ArrayList<>();
            for (TreeGrid child : supplierCategoryTreeVO.getChildren()) {
                CustomerCategoryTreeVO ch = (CustomerCategoryTreeVO) child;
                AttrValue attrValue = new AttrValue();

                attrValue.setParent_no(ch.getParentNo());
                attrValue.setNo(ch.getNo());
                attrValue.setCategory_code(ch.getCategoryCode());
                attrValue.setCategory_name(ch.getCategoryName());
                attrValue.setParentCode(ch.getParentNo());

                attrValue.setChildren(getChildren(ch, level + 1));
                children.add(attrValue);
            }
            return children;
        }

        return null;
    }
}
