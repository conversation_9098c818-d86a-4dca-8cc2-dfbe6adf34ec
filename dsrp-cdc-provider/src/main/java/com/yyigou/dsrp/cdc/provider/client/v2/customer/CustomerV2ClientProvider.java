package com.yyigou.dsrp.cdc.provider.client.v2.customer;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.config.annotation.Service;
import com.yyigou.ddc.common.exception.CommonExceptionHandler;
import com.yyigou.ddc.common.message.MessageVO;
import com.yyigou.ddc.common.page.PageDto;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.ddc.common.util.ValidatorUtils;
import com.yyigou.ddc.services.ddc.uap.domain.dto.grade.GradeDocAssignMessage;
import com.yyigou.ddc.services.ddc.uap.domain.vo.grade.GradeRpcAssignVO;
import com.yyigou.dsrp.cdc.api.v2.company.vo.CompanyLinkmanVO;
import com.yyigou.dsrp.cdc.api.v2.customer.vo.CustomerInvoiceVO;
import com.yyigou.dsrp.cdc.api.v2.customer.vo.CustomerVO;
import com.yyigou.dsrp.cdc.client.customer.request.CustomerInvoiceQueryRequest;
import com.yyigou.dsrp.cdc.client.customer.request.CustomerNameRequest;
import com.yyigou.dsrp.cdc.client.customer.request.CustomerNoRequest;
import com.yyigou.dsrp.cdc.client.v2.customer.CustomerRpcClient;
import com.yyigou.dsrp.cdc.client.v2.customer.request.*;
import com.yyigou.dsrp.cdc.client.v2.customer.response.*;
import com.yyigou.dsrp.cdc.common.enums.InvoiceTypeEnum;
import com.yyigou.dsrp.cdc.common.util.BeanUtil;
import com.yyigou.dsrp.cdc.model.v2.customer.req.*;
import com.yyigou.dsrp.cdc.service.v2.customer.CustomerV2Service;
import com.yyigou.dsrp.gcs.model.api.constant.ErrorCode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Component
@RequiredArgsConstructor
@Slf4j
@Service
public class CustomerV2ClientProvider implements CustomerRpcClient {
    @Resource
    private CustomerV2Service customerV2Service;

    @Override
    public CallResult<CustomerBizResponse> getCustomerBizInfoByCustomerNo(String enterpriseNo, String useOrgNo, String customerNo) {
        try {
            log.info("getCustomerBizInfoByCustomerNoReq:{},{},{}", enterpriseNo, useOrgNo, customerNo);

            CustomerStandardQueryReq customerStandardQueryReq = new CustomerStandardQueryReq();
            customerStandardQueryReq.setEnterpriseNo(enterpriseNo);
            customerStandardQueryReq.setUseOrgNo(useOrgNo);
            customerStandardQueryReq.setCustomerNo(customerNo);

            List<CustomerBizResponse> customerBizResponses = customerV2Service.queryWithBizInfo(customerStandardQueryReq);

            log.info("getCustomerBizInfoByCustomerNoRes:{}", customerBizResponses);

            if (CollectionUtils.isNotEmpty(customerBizResponses) && customerBizResponses.size() == 1) {
                return CallResult.success(MessageVO.SUCCESS, customerBizResponses.get(0));
            }

            return CallResult.failed(ErrorCode.BUSINESS_ERROR_CODE, "无法找到客户档案");
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    public CallResult<CustomerBizResponse> getCustomerBizInfoByCustomerCode(String enterpriseNo, String useOrgNo, String customerCode) {
        try {
            log.info("getCustomerBizInfoByCustomerCodeReq:{},{},{}", enterpriseNo, useOrgNo, customerCode);

            CustomerStandardQueryReq customerStandardQueryReq = new CustomerStandardQueryReq();
            customerStandardQueryReq.setEnterpriseNo(enterpriseNo);
            customerStandardQueryReq.setUseOrgNo(useOrgNo);
            customerStandardQueryReq.setCustomerCode(customerCode);

            List<CustomerBizResponse> customerBizResponses = customerV2Service.queryWithBizInfo(customerStandardQueryReq);

            log.info("getCustomerBizInfoByCustomerCodeRes:{}", customerBizResponses);

            if (CollectionUtils.isNotEmpty(customerBizResponses) && customerBizResponses.size() == 1) {
                return CallResult.success(MessageVO.SUCCESS, customerBizResponses.get(0));
            }

            return CallResult.failed(ErrorCode.BUSINESS_ERROR_CODE, "无法找到客户档案");
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    public CallResult<List<CustomerBizResponse>> selectCustomerBizInfoList(CustomerStandardRequest params) {
        try {
            log.info("selectCustomerBizInfoListReq:{}", params);

            CustomerStandardQueryReq customerStandardQueryReq = BeanUtil.copyFields(params, CustomerStandardQueryReq.class);

            List<CustomerBizResponse> data = customerV2Service.queryWithBizInfo(customerStandardQueryReq);

            log.info("selectCustomerBizInfoListRes:{}", data);

            return CallResult.success(MessageVO.SUCCESS, data);

        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    public CallResult<List<CustomerBizResponse>> selectCustomerBizByCodeOrgPair(CustomerCodeOrgPairListRequest params) {
        try {
            log.info("selectCustomerBizByCodeOrgPairReq:{}", params);

            CustomerCodeOrgPairListReq customerStandardQueryReq = BeanUtil.copyFields(params, CustomerCodeOrgPairListReq.class);

            List<CustomerBizResponse> data = customerV2Service.selectCustomerBizByCodeOrgPair(customerStandardQueryReq);

            log.info("selectCustomerBizByCodeOrgPairRes:{}", data);

            return CallResult.success(MessageVO.SUCCESS, data);

        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    public CallResult<PageVo<CustomerBizResponse>> selectCustomerBizInfoListPage(CustomerStandardRequest params, PageDto pageDto) {
        try {
            log.info("selectCustomerBizInfoListPageReq:{},{}", params, pageDto);

            CustomerStandardQueryReq customerStandardQueryReq = BeanUtil.copyFields(params, CustomerStandardQueryReq.class);

            PageVo<CustomerBizResponse> data = customerV2Service.queryPageWithBizInfo(customerStandardQueryReq, pageDto);

            log.info("selectCustomerBizInfoListPageRes:{}", data);

            return CallResult.success(MessageVO.SUCCESS, data);
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    public CallResult<List<CustomerBizResponse>> selectCustomerBizInfoByName(CustomerNameRequest request) {
        try {
            log.info("selectCustomerBizInfoByNameReq:{}", request);

            CustomerStandardQueryReq customerStandardQueryReq = new CustomerStandardQueryReq();
            customerStandardQueryReq.setEnterpriseNo(request.getEnterpriseNo());
            customerStandardQueryReq.setUseOrgNo(request.getUseOrgNo());
            customerStandardQueryReq.setCustomerName(request.getCustomerName());
            customerStandardQueryReq.setCustomerKeywords(request.getCustomerNameKeyword());

            List<CustomerBizResponse> data = customerV2Service.queryWithBizInfo(customerStandardQueryReq);

            log.info("selectCustomerBizInfoByNameRes:{}", data);

            return CallResult.success(MessageVO.SUCCESS, data);
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    public CallResult<List<CustomerBizResponse>> selectCustomerBizInfoByNo(CustomerNoRequest request) {
        try {
            log.info("selectCustomerBizInfoByNoReq:{}", request);

            CustomerStandardQueryReq customerStandardQueryReq = new CustomerStandardQueryReq();
            customerStandardQueryReq.setEnterpriseNo(request.getEnterpriseNo());
            customerStandardQueryReq.setUseOrgNo(request.getUseOrgNo());
            customerStandardQueryReq.setCustomerNo(request.getCustomerNo());
            customerStandardQueryReq.setCustomerCode(request.getCustomerCode());
            customerStandardQueryReq.setCustomerNoList(request.getCustomerNoList());
            customerStandardQueryReq.setCustomerCodeList(request.getCustomerCodeList());

            List<CustomerBizResponse> data = customerV2Service.queryWithBizInfo(customerStandardQueryReq);

            log.info("selectCustomerBizInfoByNoRes:{}", data);

            return CallResult.success(MessageVO.SUCCESS, data);
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    public CallResult<List<GradeRpcAssignVO>> assignCustomer(GradeDocAssignMessage message) {
        log.warn("assignCustomerReq={}", message);

        ValidatorUtils.checkEmptyThrowEx(message, "参数不能为空");
        ValidatorUtils.checkEmptyThrowEx(message.getEnterpriseNo(), "企业编号不能为空");
        ValidatorUtils.checkEmptyThrowEx(message.getMgrOrgNo(), "管理组织编号不能为空");
        ValidatorUtils.checkEmptyThrowEx(message.getDocNos(), "档案编号不能为空");
        ValidatorUtils.checkEmptyThrowEx(message.getShareOrgNos(), "使用组织编号不能为空");

        try {
            // 参数转换
            CustomerAssignExeRequest customerAssignExeRequest = new CustomerAssignExeRequest();
            customerAssignExeRequest.setEnterpriseNo(message.getEnterpriseNo());
            customerAssignExeRequest.setManageOrgNo(message.getMgrOrgNo());
            customerAssignExeRequest.setDocList(new ArrayList<>());

            for (String docNo : message.getDocNos()) {
                CustomerAssignDocExeRequest docRequest = new CustomerAssignDocExeRequest();
                docRequest.setCustomerCode(docNo);
                docRequest.setUseOrgNoList(new ArrayList<>());
                for (String shareOrgNo : message.getShareOrgNos()) {
                    docRequest.getUseOrgNoList().add(shareOrgNo);
                }
                customerAssignExeRequest.getDocList().add(docRequest);
            }

            log.warn("assignCustomerSrvReq={}", customerAssignExeRequest);
            List<CustomerAssignExeResponse> scustomerAssignExeResponseList = customerV2Service.assignCustomer(customerAssignExeRequest);
            log.warn("assignCustomerSrvRes={}", scustomerAssignExeResponseList);

            List<GradeRpcAssignVO> data = com.yyigou.dsrp.gcs.common.util.BeanUtil.copyFieldsList(scustomerAssignExeResponseList, GradeRpcAssignVO.class);
            log.warn("assignCustomerRpcResp={}", data);

            return CallResult.success(data);
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }


    @Override
    public CallResult<Boolean> changeGspStatus(CustomerGspStatusRequest message) {
        try {
            CustomerGspStatusReq customerGspStatusReq = BeanUtil.copyFields(message, CustomerGspStatusReq.class);
            return CallResult.success(customerV2Service.changeCustomerGspStatus(customerGspStatusReq));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    public CallResult<List<CustomerInvoiceResponse>> getInvoiceListByCustomerCode(CustomerInvoiceQueryRequest request) {
        ValidatorUtils.checkEmptyThrowEx(request, "参数不能为空");
        ValidatorUtils.checkEmptyThrowEx(request.getEnterpriseNo(), "企业编号不能为空");
        ValidatorUtils.checkEmptyThrowEx(request.getUseOrgNo(), "组织编号不能为空");
        ValidatorUtils.checkEmptyThrowEx(request.getCustomerCode(), "客户编码不能为空");

        CustomerInvoiceQueryReq customerInvoiceQueryReq = BeanUtil.copyFields(request, CustomerInvoiceQueryReq.class);

        List<CustomerInvoiceVO> customerInvoiceList = customerV2Service.findCustomerInvoiceList(request.getEnterpriseNo(), customerInvoiceQueryReq);
        if (CollectionUtils.isEmpty(customerInvoiceList)) {
            return CallResult.success(Collections.emptyList());
        }

        List<CustomerInvoiceResponse> result = new ArrayList<>(customerInvoiceList.size());
        for (CustomerInvoiceVO customerInvoiceVO : customerInvoiceList) {
            CustomerInvoiceResponse customerInvoiceResponse = BeanUtil.copyFields(customerInvoiceVO, CustomerInvoiceResponse.class);

            customerInvoiceResponse.setTypeName(InvoiceTypeEnum.getByType(customerInvoiceResponse.getType()) == null ? null : InvoiceTypeEnum.getByType(customerInvoiceResponse.getType()).getName());

            result.add(customerInvoiceResponse);
        }

        return CallResult.success(result);
    }

    @Override
    public CallResult<List<CompanyLinkManResponse>> getLinkManListByCustomerCode(CustomerLinkManQueryRequest request) {
        ValidatorUtils.checkEmptyThrowEx(request, "参数不能为空");
        ValidatorUtils.checkEmptyThrowEx(request.getEnterpriseNo(), "企业编号不能为空");
        ValidatorUtils.checkEmptyThrowEx(request.getUseOrgNo(), "组织编号不能为空");
        ValidatorUtils.checkEmptyThrowEx(request.getCustomerCode(), "客户编码不能为空");

        CustomerLinkmanQueryReq customerLinkmanQueryReq = BeanUtil.copyFields(request, CustomerLinkmanQueryReq.class);

        List<CompanyLinkmanVO> customerLinkmanList = customerV2Service.findCustomerLinkmanList(request.getEnterpriseNo(), customerLinkmanQueryReq);
        if (CollectionUtils.isEmpty(customerLinkmanList)) {
            return CallResult.success(Collections.emptyList());
        }

        List<CompanyLinkManResponse> result = new ArrayList<>(customerLinkmanList.size());
        for (CompanyLinkmanVO customerInvoiceVO : customerLinkmanList) {
            CompanyLinkManResponse companyLinkmanResponse = BeanUtil.copyFields(customerInvoiceVO, CompanyLinkManResponse.class);
            result.add(companyLinkmanResponse);
        }

        return CallResult.success(result);
    }

    @Override
    public CallResult<CustomerBasicResponse> getCustomerBasicInfoByCustomerCode(String enterpriseNo, String customerCode) {
        ValidatorUtils.checkEmptyThrowEx(enterpriseNo, "租户编号不能为空");
        ValidatorUtils.checkEmptyThrowEx(customerCode, "客户编码不能为空");

        CustomerVO customerVO = customerV2Service.getCustomerByCode(enterpriseNo, customerCode);
        if (customerVO != null) {
            CustomerBasicResponse customerBasicResponse = BeanUtil.copyFields(customerVO, CustomerBasicResponse.class);

            return CallResult.success(customerBasicResponse);
        }

        return null;
    }


}
