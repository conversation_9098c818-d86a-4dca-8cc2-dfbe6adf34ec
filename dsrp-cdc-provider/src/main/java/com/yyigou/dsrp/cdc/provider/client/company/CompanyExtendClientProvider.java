package com.yyigou.dsrp.cdc.provider.client.company;

import com.alibaba.dubbo.config.annotation.Service;
import com.yyigou.ddc.common.exception.CommonExceptionHandler;
import com.yyigou.ddc.common.message.MessageVO;
import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.dsrp.cdc.client.company.CompanyExtendClient;
import com.yyigou.dsrp.cdc.client.company.response.CompanyLinkmanResponse;
import com.yyigou.dsrp.cdc.client.company.response.CompanyShippingAddressResponse;
import com.yyigou.dsrp.cdc.common.enums.LinkmanTypeEnum;
import com.yyigou.dsrp.cdc.service.company.CompanyExtendService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
@Service
@RequiredArgsConstructor
@Slf4j
public class CompanyExtendClientProvider implements CompanyExtendClient {

    @Resource
    private CompanyExtendService companyExtendService;

    @Override
    public CallResult<List<CompanyLinkmanResponse>> getLinkmanList(String enterpriseNo, LinkmanTypeEnum typeEnum, List<String> sourceList) {

        try {
            return CallResult.success(MessageVO.SUCCESS, companyExtendService.getLinkmanList(enterpriseNo, typeEnum, sourceList));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }


    @Override
    public CallResult<List<CompanyShippingAddressResponse>> getLinkAddressList(String enterpriseNo, LinkmanTypeEnum typeEnum, List<String> sourceList) {
        try {
            return CallResult.success(MessageVO.SUCCESS, companyExtendService.getLinkAddressList(enterpriseNo, typeEnum, sourceList));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

}
