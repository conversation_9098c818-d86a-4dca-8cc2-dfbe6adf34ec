package com.yyigou.dsrp.cdc.provider.api.supply;

import com.alibaba.dubbo.config.annotation.Service;
import com.yyigou.ddc.common.exception.CommonExceptionHandler;
import com.yyigou.ddc.common.message.MessageVO;
import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.ddc.common.service.ServiceBaseAbstract;
import com.yyigou.ddc.common.service.annotation.Interface;
import com.yyigou.ddc.common.service.annotation.Method;
import com.yyigou.ddc.common.service.annotation.MethodType;
import com.yyigou.dsrp.cdc.api.supply.SupplierCategoryApi;
import com.yyigou.dsrp.cdc.api.supply.vo.SupplierCategoryTree;
import com.yyigou.dsrp.cdc.common.util.OperationModel;
import com.yyigou.dsrp.cdc.service.supplier.SupplierCategoryService;
import com.yyigou.dsrp.cdc.service.utils.UserHandleUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Service
@RequiredArgsConstructor
@Slf4j
@Interface(name = "供应商分类档案页面接口")
public class SupplierCategoryProvider extends ServiceBaseAbstract implements SupplierCategoryApi {

    private final SupplierCategoryService supplierCategoryService;


    @Override
    @Method(aliasName = "dsrp.cdc.supplierCategory.queryTree", name = "供应商分类获取整棵树", methodType = MethodType.QUERY, processState = 2, requestSession = false, requestAuthentication = false)
    public CallResult<List<SupplierCategoryTree>> queryTree() {
        try {
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            return CallResult.success(MessageVO.SUCCESS, supplierCategoryService.queryTree(operationModel));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.supplierCategory.group.queryTree", name = "供应商分类获取整棵树", methodType = MethodType.QUERY, processState = 2, requestSession = false, requestAuthentication = false)
    public CallResult<List<SupplierCategoryTree>> queryGroupTree() {
        try {
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            return CallResult.success(MessageVO.SUCCESS, supplierCategoryService.queryTree(operationModel));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }
}
