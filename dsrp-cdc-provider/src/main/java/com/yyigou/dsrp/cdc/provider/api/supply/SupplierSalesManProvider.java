package com.yyigou.dsrp.cdc.provider.api.supply;

import com.alibaba.dubbo.config.annotation.Service;
import com.yyigou.ddc.common.exception.CommonExceptionHandler;
import com.yyigou.ddc.common.message.MessageVO;
import com.yyigou.ddc.common.page.PageDto;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.ddc.common.service.ServiceBaseAbstract;
import com.yyigou.ddc.common.service.annotation.Interface;
import com.yyigou.ddc.common.service.annotation.Method;
import com.yyigou.ddc.common.service.annotation.MethodType;
import com.yyigou.ddc.services.dsrp.bdc.vo.SupplierOrderManVo;
import com.yyigou.dsrp.cdc.api.supply.SupplierSalesManApi;
import com.yyigou.dsrp.cdc.api.supply.dto.SupplierSalesManByOrgDTO;
import com.yyigou.dsrp.cdc.api.supply.dto.SupplierSalesManQueryBySpecifyOrgDTO;
import com.yyigou.dsrp.cdc.api.supply.dto.SupplierSalesManQueryDTO;
import com.yyigou.dsrp.cdc.common.util.OperationModel;
import com.yyigou.dsrp.cdc.service.supplier.SupplierSalesManService;
import com.yyigou.dsrp.cdc.service.utils.UserHandleUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Service
@RequiredArgsConstructor
@Slf4j
@Interface(name = "供应商负责人接口")
public class SupplierSalesManProvider extends ServiceBaseAbstract implements SupplierSalesManApi {

    private final SupplierSalesManService supplierSalesManService;

    /**
     * 跨组织获取供应商负责人列表
     *
     * @param supplierCode
     * @param orgNo
     * @return
     */
    @Override
    @Method(aliasName = "dsrp.cdc.supplier.org.getSupplierSalesManListByOrg", name = "跨组织获取供应商负责人列表", methodType = MethodType.QUERY, processState = 2, requestSession = false, requestAuthentication = false)
    public CallResult<List<SupplierOrderManVo>> getSupplierSalesManListByOrg(String supplierCode, String orgNo) {
        try {
            //补充租户信息
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            return CallResult.success(MessageVO.SUCCESS, supplierSalesManService.getSupplierSalesManListByOrg(operationModel, supplierCode, orgNo));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 跨组织保存供应商负责人列表
     *
     * @param params
     * @return
     */
    @Override
    @Method(aliasName = "dsrp.cdc.supplier.org.saveSupplierSalesManByOrg", name = "跨组织保存供应商负责人列表", methodType = MethodType.QUERY, processState = 2, requestSession = false, requestAuthentication = false)
    public CallResult<SupplierOrderManVo> saveSupplierSalesManByOrg(SupplierSalesManByOrgDTO params) {
        try {
            //补充租户信息
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            return CallResult.success(MessageVO.SUCCESS, supplierSalesManService.saveSupplierSalesManByOrg(operationModel, params));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 跨组织删除负责人
     *
     * @param params
     * @return
     */
    @Override
    @Method(aliasName = "dsrp.cdc.supplier.org.editSupplierSalesManByOrg", name = "跨组织编辑负责人", methodType = MethodType.QUERY, processState = 2, requestSession = false, requestAuthentication = false)
    public CallResult<SupplierOrderManVo> editSupplierSalesManByOrg(SupplierSalesManByOrgDTO params) {
        try {
            //补充租户信息
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            return CallResult.success(MessageVO.SUCCESS, supplierSalesManService.editSupplierSalesManByOrg(operationModel, params));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 跨组织删除负责人
     *
     * @param salesManId
     * @return
     */
    @Override
    @Method(aliasName = "dsrp.cdc.supplier.org.deleteSupplierSalesManByOrg", name = "跨组织删除负责人", methodType = MethodType.QUERY, processState = 2, requestSession = false, requestAuthentication = false)
    public CallResult<Boolean> deleteSupplierSalesManOrg(Long salesManId) {
        try {
            //补充租户信息
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            return CallResult.success(MessageVO.SUCCESS, supplierSalesManService.deleteSupplierSalesManOrg(operationModel, salesManId));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 获取供应商销售人列表
     *
     * @param params
     * @return
     */
    @Override
    @Method(aliasName = "dsrp.cdc.supplier.findList", name = "获取供应商销售人列表", methodType = MethodType.QUERY, processState = 2, requestSession = false, requestAuthentication = false)
    public CallResult<List<SupplierOrderManVo>> findList(SupplierSalesManQueryDTO params) {
        try {
            //补充租户信息
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            return CallResult.success(MessageVO.SUCCESS, supplierSalesManService.findList(operationModel, params));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 获取供应商销售人列表
     *
     * @param params
     * @param pageDto
     * @return
     */
    @Override
    @Method(aliasName = "dsrp.cdc.supplier.findPage", name = "分页获取供应商销售人列表", methodType = MethodType.QUERY, processState = 2, requestSession = false, requestAuthentication = false)
    public CallResult<PageVo<SupplierOrderManVo>> findPage(SupplierSalesManQueryDTO params, PageDto pageDto) {
        try {
            //补充租户信息
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            return CallResult.success(MessageVO.SUCCESS, supplierSalesManService.findPage(operationModel, params, pageDto));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 获取供应商销售人列表
     *
     * @param params
     * @return
     */
    @Override
    @Method(aliasName = "dsrp.cdc.supplier.org.findListBySpecifyOrg", name = "跨组获取供应商销售人列表", methodType = MethodType.QUERY, processState = 2, requestSession = false, requestAuthentication = false)
    public CallResult<List<SupplierOrderManVo>> findListBySpecifyOrg(SupplierSalesManQueryBySpecifyOrgDTO params) {
        try {
            //补充租户信息
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            return CallResult.success(MessageVO.SUCCESS, supplierSalesManService.findListBySpecifyOrg(operationModel, params));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 获取供应商销售人列表
     *
     * @param params
     * @param pageDto
     * @return
     */
    @Override
    @Method(aliasName = "dsrp.cdc.supplier.org.findPageBySpecifyOrg", name = "跨组织分页获取供应商销售人列表", methodType = MethodType.QUERY, processState = 2, requestSession = false, requestAuthentication = false)
    public CallResult<PageVo<SupplierOrderManVo>> findPageBySpecifyOrg(SupplierSalesManQueryBySpecifyOrgDTO params, PageDto pageDto) {
        try {
            //补充租户信息
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            return CallResult.success(MessageVO.SUCCESS, supplierSalesManService.findPageBySpecifyOrg(operationModel, params, pageDto));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }
}
