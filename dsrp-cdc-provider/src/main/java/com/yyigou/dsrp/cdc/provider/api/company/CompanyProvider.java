package com.yyigou.dsrp.cdc.provider.api.company;

import com.alibaba.dubbo.config.annotation.Service;
import com.alibaba.fastjson.JSON;
import com.yyigou.ddc.common.exception.CommonExceptionHandler;
import com.yyigou.ddc.common.message.MessageVO;
import com.yyigou.ddc.common.page.PageDto;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.ddc.common.service.ServiceBaseAbstract;
import com.yyigou.ddc.common.service.annotation.Interface;
import com.yyigou.ddc.common.service.annotation.Method;
import com.yyigou.ddc.common.service.annotation.MethodType;
import com.yyigou.ddc.services.dsrp.bdc.dto.CompanyCertDto;
import com.yyigou.ddc.services.dsrp.bdc.vo.CertControlVo;
import com.yyigou.ddc.services.mq.dto.JmsHeadersDto;
import com.yyigou.ddc.services.mq.manager.MessageQueueManager;
import com.yyigou.dsrp.cdc.api.common.dto.CompanyBasicDTO;
import com.yyigou.dsrp.cdc.api.company.CompanyAPI;
import com.yyigou.dsrp.cdc.api.company.dto.CompanyQueryListPageDTO;
import com.yyigou.dsrp.cdc.api.company.dto.CompanySaveDTO;
import com.yyigou.dsrp.cdc.api.company.vo.CompanyBasicVO;
import com.yyigou.dsrp.cdc.api.company.vo.CompanyDetailVO;
import com.yyigou.dsrp.cdc.common.util.OperationModel;
import com.yyigou.dsrp.cdc.model.company.req.CompanyQueryListPageReq;
import com.yyigou.dsrp.cdc.model.company.req.CompanySaveReq;
import com.yyigou.dsrp.cdc.model.constant.CdcMqConstant;
import com.yyigou.dsrp.cdc.service.company.CompanyService;
import com.yyigou.dsrp.cdc.service.listener.model.CompanyUpdateModel;
import com.yyigou.dsrp.cdc.service.utils.UserHandleUtils;
import com.yyigou.dsrp.gcs.common.util.BeanUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.activemq.ScheduledMessage;
import org.apache.activemq.command.ActiveMQTopic;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
@Service
@RequiredArgsConstructor
@Slf4j
@Interface(name = "企业档案页面接口")
public class CompanyProvider extends ServiceBaseAbstract implements CompanyAPI {

    @Resource
    private CompanyService companyService;
    @Autowired
    private MessageQueueManager messageQueueManager;

    @Override
    @Method(aliasName = "dsrp.cdc.company.save", name = "新增企业档案", methodType = MethodType.ADD, processState = 2)
    public CallResult<CompanyBasicVO> saveCompany(CompanySaveDTO params) {
        try {
            CompanySaveReq companySaveReq = new CompanySaveReq();
            BeanUtils.copyProperties(params, companySaveReq);
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            return CallResult.success(MessageVO.SUCCESS, companyService.saveCompany(operationModel, companySaveReq));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }


    @Override
    @Method(aliasName = "dsrp.cdc.company.detail", name = "获取企业详情", methodType = MethodType.QUERY, processState = 2)
    public CallResult<CompanyDetailVO> getCompanyDetail(CompanyBasicDTO params) {
        try {
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            return CallResult.success(MessageVO.SUCCESS, companyService.getDetailByEnterpriseAndCompanyNo(operationModel.getEnterpriseNo(), params.getCompanyNo()));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.company.getCompanyByName", name = "根据名称获取企业详情", methodType = MethodType.QUERY, processState = 2)
    public CallResult<CompanyDetailVO> getCompanyByName(String companyName) {
        try {
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            return CallResult.success(MessageVO.SUCCESS, companyService.getDetailByEnterpriseAndCompanyName(operationModel.getEnterpriseNo(), companyName));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 获取本企业证照
     *
     * @return
     */
    @Override
    @Method(aliasName = "dsrp.cdc.company.getEnterpriseDetail", name = "获取本企业证照", methodType = MethodType.QUERY, processState = 2)
    public CallResult<CompanyDetailVO> getEnterpriseDetail() {
        try {
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            return CallResult.success(MessageVO.SUCCESS, companyService.getEnterpriseDetail(operationModel.getEnterpriseNo()));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 修改企业档案信息
     *
     * @param params
     * @return: {@link CallResult< Boolean>}
     */
    @Override
    @Method(aliasName = "dsrp.cdc.company.update", name = "修改企业档案", methodType = MethodType.ADD, processState = 2)
    public CallResult<Boolean> updateCompany(CompanySaveDTO params) {
        try {
            CompanySaveReq companySaveReq = new CompanySaveReq();
            BeanUtils.copyProperties(params, companySaveReq);
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            CompanyUpdateModel companyUpdateModel = companyService.updateCompany(operationModel, companySaveReq);
            // 修改成功，发送消息，处理唯一客商名称修改、子租户信息自动更新等
            if (companyUpdateModel.getUpdateSuccess()) {
                JmsHeadersDto jmsHeadersDto = new JmsHeadersDto();
                jmsHeadersDto.setSessionTransacted(true);
                Map<String, Object> customPropMap = new HashMap<>();
                customPropMap.put(ScheduledMessage.AMQ_SCHEDULED_DELAY, 5000L);
                messageQueueManager.produceMessage(new ActiveMQTopic(CdcMqConstant.CDC_COMPANY_UPDATE_TOPIC), JSON.toJSONString(companyUpdateModel), customPropMap, jmsHeadersDto);
            }
            return CallResult.success(MessageVO.SUCCESS, companyUpdateModel.getUpdateSuccess());
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.company.list.page", name = "分页查询企业档案", methodType = MethodType.QUERY, processState = 2)
    public CallResult<PageVo<CompanyDetailVO>> findCompanyListPage(CompanyQueryListPageDTO params, PageDto pageParams) {
        try {
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            CompanyQueryListPageReq queryReq = BeanUtil.copyFields(params, CompanyQueryListPageReq.class);
            //补充租户信息
            queryReq.setEnterpriseNo(operationModel.getEnterpriseNo());
            return CallResult.success(MessageVO.SUCCESS, companyService.findCompanyListPage(queryReq, pageParams));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.company.list.pageForDing", name = "分页查询企业档案", methodType = MethodType.QUERY, processState = 2)
    public CallResult<PageVo<CompanyDetailVO>> findCompanyListPageForDing(CompanyQueryListPageDTO params, PageDto pageParams) {
        try {
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            CompanyQueryListPageReq queryReq = BeanUtil.copyFields(params, CompanyQueryListPageReq.class);
            //补充租户信息
            queryReq.setEnterpriseNo(operationModel.getEnterpriseNo());
            return CallResult.success(MessageVO.SUCCESS, companyService.findCompanyListPageForDing(queryReq, pageParams));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.company.checkQualification", name = "提交校验资质", methodType = MethodType.QUERY, processState = 2)
    public CallResult<List<CertControlVo>> checkQualification(CompanyCertDto params) {
        try {
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            return CallResult.success(MessageVO.SUCCESS, companyService.checkQualification(operationModel, params));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }
}
