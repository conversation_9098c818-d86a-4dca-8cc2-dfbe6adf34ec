package com.yyigou.dsrp.cdc.provider.client.supplier;

import com.alibaba.dubbo.config.annotation.Service;
import com.yyigou.ddc.common.exception.CommonExceptionHandler;
import com.yyigou.ddc.common.message.MessageVO;
import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.dsrp.cdc.client.supplier.SupplierCoordinationClient;
import com.yyigou.dsrp.cdc.client.supplier.response.SupplierSimpleComponentResponse;
import com.yyigou.dsrp.cdc.service.supplier.SupplierService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Service
@RequiredArgsConstructor
@Slf4j
public class SupplierCoordinationClientProvider implements SupplierCoordinationClient {


    private final SupplierService supplierService;


    @Override
    public CallResult<List<SupplierSimpleComponentResponse>> querySupplierList(List<String> enterpriseNoList, String supplierCode) {
        try {
            return CallResult.success(MessageVO.SUCCESS, supplierService.querySupplierList(enterpriseNoList, supplierCode));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }
}
