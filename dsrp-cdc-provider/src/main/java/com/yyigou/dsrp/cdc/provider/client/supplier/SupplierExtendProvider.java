package com.yyigou.dsrp.cdc.provider.client.supplier;

import com.alibaba.dubbo.config.annotation.Service;
import com.yyigou.ddc.common.exception.CommonExceptionHandler;
import com.yyigou.ddc.common.message.MessageVO;
import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.dsrp.cdc.client.supplier.SupplierExtendClient;
import com.yyigou.dsrp.cdc.client.supplier.request.SupplierManQueryRequest;
import com.yyigou.dsrp.cdc.client.supplier.response.SupplierSalesManResponse;
import com.yyigou.dsrp.cdc.service.supplier.SupplyExtendService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
@Service
@RequiredArgsConstructor
@Slf4j
public class SupplierExtendProvider implements SupplierExtendClient {
    @Resource
    private SupplyExtendService supplyExtendService;

    @Override
    public CallResult<List<SupplierSalesManResponse>> querySupplierMan(String enterpriseNo, String supplierNo, String orgNo, String orderManNo, List<Integer> orderSpecialist) {
        try {
            return CallResult.success(MessageVO.SUCCESS, supplyExtendService.querySupplierMan(enterpriseNo, supplierNo, orgNo, orderManNo, orderSpecialist));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    public CallResult<List<SupplierSalesManResponse>> querySupplierManList(SupplierManQueryRequest params) {
        try {
            return CallResult.success(MessageVO.SUCCESS, supplyExtendService.querySupplierManList(params));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

}
