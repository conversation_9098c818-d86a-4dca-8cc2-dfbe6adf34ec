package com.yyigou.dsrp.cdc.provider.api.v2.customer;

import com.alibaba.dubbo.config.annotation.Service;
import com.yyigou.ddc.common.exception.CommonExceptionHandler;
import com.yyigou.ddc.common.message.MessageVO;
import com.yyigou.ddc.common.page.PageDto;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.ddc.common.service.ServiceBaseAbstract;
import com.yyigou.ddc.common.service.annotation.Interface;
import com.yyigou.ddc.common.service.annotation.Method;
import com.yyigou.ddc.common.service.annotation.MethodType;
import com.yyigou.dsrp.cdc.api.v2.customer.CustomerApplyV2API;
import com.yyigou.dsrp.cdc.api.v2.customer.dto.*;
import com.yyigou.dsrp.cdc.api.v2.customer.vo.CustomerApplyDetailVO;
import com.yyigou.dsrp.cdc.api.v2.customer.vo.CustomerApplyPageVO;
import com.yyigou.dsrp.cdc.common.util.BeanUtil;
import com.yyigou.dsrp.cdc.common.util.OperationModel;
import com.yyigou.dsrp.cdc.model.constant.ViewNameConstant;
import com.yyigou.dsrp.cdc.model.v2.customer.req.*;
import com.yyigou.dsrp.cdc.service.utils.RedisClientUtil;
import com.yyigou.dsrp.cdc.service.utils.UserHandleUtils;
import com.yyigou.dsrp.cdc.service.v2.customer.CustomerApplyService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
@Service
@RequiredArgsConstructor
@Slf4j
@Interface(name = "客户档案申请页面接口")
public class CustomerApplyV2Provider extends ServiceBaseAbstract implements CustomerApplyV2API {
    @Resource(name = "customerApplyService")
    private CustomerApplyService customerApplyService;

    @Resource
    private RedisClientUtil redisClientUtil;


    @Override
    @Method(aliasName = "dsrp.cdc.customerapply.v2.findListPage", name = "客户申请列表查询", viewNo = ViewNameConstant.BDC_CUSTOMER_APPLY_VIEW, orgAuth = true, methodType = MethodType.QUERY, processState = 2, version = "1.0.1")
    public CallResult<PageVo<CustomerApplyPageVO>> applyFindListPage(CustomerApplyQueryListPageDTO params, PageDto pageParams) {
        try {
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            CustomerApplyQueryListPageReq queryReq = BeanUtil.copyFields(params, CustomerApplyQueryListPageReq.class);
            //补充租户信息
            queryReq.setEnterpriseNo(operationModel.getEnterpriseNo());
            return CallResult.success(MessageVO.SUCCESS, customerApplyService.applyFindListPage(operationModel, queryReq, pageParams));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.customerapply.v2.findApproveListPage", name = "客户申请待审核列表查询", viewNo = ViewNameConstant.BDC_CUSTOMER_APPLY_VIEW, orgAuth = true, methodType = MethodType.QUERY, processState = 2, version = "1.0.1")
    public CallResult<PageVo<CustomerApplyPageVO>> approveFindListPage(CustomerApplyApproveQueryListPageDTO params, PageDto pageParams) {
        try {
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            CustomerApplyApproveQueryListPageReq queryReq = BeanUtil.copyFields(params, CustomerApplyApproveQueryListPageReq.class);
            //补充租户信息
            queryReq.setEnterpriseNo(operationModel.getEnterpriseNo());
            return CallResult.success(MessageVO.SUCCESS, customerApplyService.approveFindListPage(operationModel, queryReq, pageParams));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.customerapply.v2.getApprovePendingCount", name = "客户申请待审核数量查询", viewNo = ViewNameConstant.BDC_CUSTOMER_APPLY_VIEW, orgAuth = true, methodType = MethodType.QUERY, processState = 2, version = "1.0.1")
    public CallResult<Long> getPendingCount() {
        OperationModel operationModel = UserHandleUtils.getOperationModel();

        return CallResult.success(MessageVO.SUCCESS, customerApplyService.getPendingCount(operationModel));
    }

    @Override
    @Method(aliasName = "dsrp.cdc.customerapply.v2.getDetail", name = "客户申请详情查看", viewNo = ViewNameConstant.BDC_CUSTOMER_APPLY_VIEW, methodType = MethodType.DETAIL, processState = 2, version = "1.0.1")
    public CallResult<CustomerApplyDetailVO> getDetail(CustomerApplyGetDTO params) {
        try {
            OperationModel operationModel = com.yyigou.dsrp.cdc.service.utils.UserHandleUtils.getOperationModel();

            CustomerApplyGetReq customerApplyGetReq = BeanUtil.copyFields(params, CustomerApplyGetReq.class);
            //补充租户信息
            customerApplyGetReq.setEnterpriseNo(operationModel.getEnterpriseNo());

            return CallResult.success(MessageVO.SUCCESS, customerApplyService.getDetail(operationModel, customerApplyGetReq));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.customerapply.v2.save", name = "客户申请编辑", viewNo = ViewNameConstant.BDC_CUSTOMER_APPLY_VIEW, methodType = MethodType.UPDATE, processState = 2, version = "1.0.1")
    public CallResult<String> applySaveOrUpdate(CustomerApplySaveOrUpdateDTO params) {
        try {
            OperationModel operationModel = com.yyigou.dsrp.cdc.service.utils.UserHandleUtils.getOperationModel();

            CustomerApplySaveOrUpdateReq customerApplySaveOrUpdateReq = BeanUtil.copyFields(params, CustomerApplySaveOrUpdateReq.class);
            //补充租户信息
            customerApplySaveOrUpdateReq.setEnterpriseNo(operationModel.getEnterpriseNo());

            return CallResult.success(MessageVO.SUCCESS, customerApplyService.applySaveOrUpdate(operationModel, customerApplySaveOrUpdateReq));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.customerapply.v2.approve", name = "客户申请审批", viewNo = ViewNameConstant.BDC_CUSTOMER_APPLY_VIEW, methodType = MethodType.UPDATE, processState = 2, version = "1.0.1")
    public CallResult<Boolean> manageApprove(CustomerApplyApproveDTO params) {
        try {
            OperationModel operationModel = com.yyigou.dsrp.cdc.service.utils.UserHandleUtils.getOperationModel();

            CustomerApplyApproveReq customerApplyApproveReq = BeanUtil.copyFields(params, CustomerApplyApproveReq.class);
            //补充租户信息
            customerApplyApproveReq.setEnterpriseNo(operationModel.getEnterpriseNo());
            customerApplyApproveReq.setAutoAudit(false);

            return CallResult.success(MessageVO.SUCCESS, customerApplyService.manageApprove(operationModel, customerApplyApproveReq));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.customerapply.v2.withdraw", name = "客户申请撤回", viewNo = ViewNameConstant.BDC_CUSTOMER_APPLY_VIEW, methodType = MethodType.UPDATE, processState = 2, version = "1.0.1")
    public CallResult<Boolean> withDrawApply(CustomerApplyWithdrawDTO params) {
        try {
            OperationModel operationModel = com.yyigou.dsrp.cdc.service.utils.UserHandleUtils.getOperationModel();

            CustomerApplyWithdrawReq customerApplyWithdrawReq = BeanUtil.copyFields(params, CustomerApplyWithdrawReq.class);
            //补充租户信息
            customerApplyWithdrawReq.setEnterpriseNo(operationModel.getEnterpriseNo());

            return CallResult.success(MessageVO.SUCCESS, customerApplyService.withDrawApply(operationModel, customerApplyWithdrawReq));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.customerapply.v2.delete", name = "客户申请删除", viewNo = ViewNameConstant.BDC_CUSTOMER_APPLY_VIEW, methodType = MethodType.UPDATE, processState = 2, version = "1.0.1")
    public CallResult<Boolean> deleteApply(CustomerApplyDeleteDTO params) {
        try {
            OperationModel operationModel = com.yyigou.dsrp.cdc.service.utils.UserHandleUtils.getOperationModel();

            CustomerApplyDeleteReq customerApplyDeleteReq = BeanUtil.copyFields(params, CustomerApplyDeleteReq.class);
            //补充租户信息
            customerApplyDeleteReq.setEnterpriseNo(operationModel.getEnterpriseNo());

            return CallResult.success(MessageVO.SUCCESS, customerApplyService.deleteApply(operationModel, customerApplyDeleteReq));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }
}
