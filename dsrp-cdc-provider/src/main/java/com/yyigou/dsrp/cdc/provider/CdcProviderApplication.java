package com.yyigou.dsrp.cdc.provider;

import com.alibaba.dubbo.config.spring.context.annotation.DubboComponentScan;
import com.yyigou.ddc.common.zkconfig.ConfigServerPlaceholderConfigurer;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ImportResource;
import org.springframework.core.io.Resource;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import java.util.ArrayList;
import java.util.List;

/**
 * 企业中心启动类
 *
 * @author:  Moore
 * @date: 2023/8/8 10:59
 * @version: 1.0.0
 */
@SpringBootApplication
@EnableAsync
@ImportResource(value = {"classpath:/META-INF/spring/applicationContext-cdc-provider.xml"})
@DubboComponentScan(value = "com.yyigou.dsrp.cdc.provider")
@EnableTransactionManagement
public class CdcProviderApplication {

    public static void main(String[] args) {
        SpringApplication.run(CdcProviderApplication.class, args);
    }

    /**
     * 配置中心加载中央配置信息, applicationContext.xml里会用到
     *
     * @return
     */
    @Bean("configServerPlaceholderConfigurer")
    public static ConfigServerPlaceholderConfigurer properties() {
        final ConfigServerPlaceholderConfigurer ppc = new ConfigServerPlaceholderConfigurer();
        ppc.setIgnoreResourceNotFound(true);
        ppc.setIgnoreUnresolvablePlaceholders(true);
        final List<Resource> resourceList = new ArrayList<>();
        ppc.setLocations(resourceList.toArray(new Resource[]{}));

        ppc.setZkConfigNodePath(System.getProperty("zk.config.node.path"));
        ppc.setZkUrl(System.getProperty("zk.config.url"));
        // spring boot 会从system里加载配置
        ppc.setWritePropsToSystem(true);
        return ppc;
    }

}
