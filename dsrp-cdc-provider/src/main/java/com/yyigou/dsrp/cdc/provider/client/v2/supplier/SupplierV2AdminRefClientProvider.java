package com.yyigou.dsrp.cdc.provider.client.v2.supplier;

import com.alibaba.dubbo.config.annotation.Service;
import com.yyigou.ddc.common.page.PageDto;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.ddc.meta.base.service.referrpc.ReferRpcReqDto;
import com.yyigou.ddc.meta.base.service.referrpc.service.IReferQueryFunc;
import com.yyigou.dsrp.cdc.api.v2.supplier.vo.SupplierPageVO;
import com.yyigou.dsrp.cdc.common.util.OperationModel;
import com.yyigou.dsrp.cdc.model.v2.supplier.req.SupplierManageQueryListPageReq;
import com.yyigou.dsrp.cdc.service.utils.UserHandleUtils;
import com.yyigou.dsrp.cdc.service.v2.supplier.SupplierV2Service;
import com.yyigou.dsrp.gcs.common.util.ObjectToMapUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 上帝视角：查询租户中的供应商档案
 */
@Component
@Service(group = "cdcSupplierAdminRefClient")
@RequiredArgsConstructor
@Slf4j
public class SupplierV2AdminRefClientProvider implements IReferQueryFunc {
    private final SupplierV2Service supplierV2Service;


    /**
     * @param referRpcReqDto
     * @return
     */
    @Override
    public PageVo<Map<String, Object>> queryReferData(ReferRpcReqDto referRpcReqDto) {
        log.warn("cdcSupplierV2AdminRefClientReqDto={}", referRpcReqDto);

        if (null == referRpcReqDto.getPageDto()) {
            PageDto pageDto = new PageDto();
            pageDto.setPageIndex(1);
            pageDto.setPageSize(100000);
            referRpcReqDto.setPageDto(pageDto);
        }
        OperationModel operationModel = UserHandleUtils.getOperationModel();

        SupplierManageQueryListPageReq supplierManageQueryListPageReq = new SupplierManageQueryListPageReq();
        supplierManageQueryListPageReq.setEnterpriseNo(operationModel.getEnterpriseNo());

        PageVo<SupplierPageVO> listPage = supplierV2Service.manageFindListPage(operationModel, supplierManageQueryListPageReq, referRpcReqDto.getPageDto());
        List<Map<String, Object>> rows = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(listPage.getRows())) {
            for (SupplierPageVO row : listPage.getRows()) {
                try {
                    // 将RegistrationCertPageVO转成Map，其中类属性如果是驼峰命名法，Map中的key用下划线分隔
                    Map<String, Object> map = ObjectToMapUtil.convertObjectToMap(row, SupplierPageVO.class);
                    rows.add(map);
                } catch (Exception e) {
                    log.error("映射异常", e);
                    throw new RuntimeException("映射异常");
                }
            }
        }
        return new PageVo<>(listPage.getPageIndex(), listPage.getPageSize(), listPage.getTotal(), rows);
    }


}
