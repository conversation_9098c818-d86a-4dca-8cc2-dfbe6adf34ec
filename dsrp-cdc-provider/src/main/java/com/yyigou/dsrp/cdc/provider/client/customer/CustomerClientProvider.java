package com.yyigou.dsrp.cdc.provider.client.customer;

import com.alibaba.dubbo.config.annotation.Service;
import com.yyigou.ddc.common.exception.CommonExceptionHandler;
import com.yyigou.ddc.common.message.MessageVO;
import com.yyigou.ddc.common.page.PageDto;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.dsrp.cdc.client.common.response.QueryUseInfoResponse;
import com.yyigou.dsrp.cdc.client.customer.CustomerClient;
import com.yyigou.dsrp.cdc.client.customer.request.CustomerComponentRequest;
import com.yyigou.dsrp.cdc.client.customer.request.CustomerFindRequest;
import com.yyigou.dsrp.cdc.client.customer.request.CustomerNameRequest;
import com.yyigou.dsrp.cdc.client.customer.request.CustomerNoRequest;
import com.yyigou.dsrp.cdc.client.customer.request.CustomerPriceCateRequest;
import com.yyigou.dsrp.cdc.client.customer.response.CustomerComponentResponse;
import com.yyigou.dsrp.cdc.client.customer.response.CustomerInfoResponse;
import com.yyigou.dsrp.cdc.client.customer.response.CustomerResponse;
import com.yyigou.dsrp.cdc.client.customer.response.response.CompanyLinkmanResponse;
import com.yyigou.dsrp.cdc.client.customer.response.response.CustomerInvoiceResponse;
import com.yyigou.dsrp.cdc.service.common.MasterDataSyncService;
import com.yyigou.dsrp.cdc.service.customer.CustomerInvoiceService;
import com.yyigou.dsrp.cdc.service.customer.CustomerService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
@Service
@RequiredArgsConstructor
@Slf4j
public class CustomerClientProvider implements CustomerClient {

    @Resource
    private CustomerService customerService;
    private final MasterDataSyncService masterDataSyncService;
    private final CustomerInvoiceService customerInvoiceService;

    /**
     * 根据客户名称查询客户信息
     *
     * @param request
     * @return: {@link CallResult <  List <  CustomerInfoResponse >>}
     */
    @Override
    public CallResult<List<CustomerInfoResponse>> findCustomerByName(CustomerNameRequest request) {
        try {
            return CallResult.success(MessageVO.SUCCESS, customerService.findCustomerByName(request));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 根据客户编号集合查询客户信息
     *
     * @param request
     * @return: {@link CallResult< List< CustomerInfoResponse>>}
     */
    @Override
    public CallResult<List<CustomerInfoResponse>> findCustomerByNo(CustomerNoRequest request) {
        try {
            return CallResult.success(MessageVO.SUCCESS, customerService.findCustomerByNo(request));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 根据价格体系查询客户信息
     *
     * @param request
     * @return: {@link CallResult< List< CustomerInfoResponse>>}
     */
    @Override
    public CallResult<List<CustomerInfoResponse>> findCustomerByPriceCate(CustomerPriceCateRequest request) {
        try {
            return CallResult.success(MessageVO.SUCCESS, customerService.findCustomerByPriceCate(request));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 查询客户信息
     *
     * @param enterpriseNo
     * @param request
     * @return
     */
    @Override
    public CallResult<List<CustomerResponse>> findCustomer(String enterpriseNo, CustomerFindRequest request) {
        try {
            return CallResult.success(MessageVO.SUCCESS, customerService.findCustomer(enterpriseNo, request));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 查询集团租户客户引用信息
     *
     * @param enterpriseNo
     * @param customerCode
     * @return: {@link CallResult< List<  String >>}
     */
    @Override
    public CallResult<List<String>> findCustomerUserList(String enterpriseNo, String customerCode) {
        try {
            return CallResult.success(MessageVO.SUCCESS, customerService.findCustomerUserList(enterpriseNo, customerCode));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 客户申请通过后执行
     *
     * @param applyJson 申请内容json
     * @return
     */
    @Override
    public CallResult<String> handleCustomerApply(String applyJson) {
        try {
            return CallResult.success(MessageVO.SUCCESS, masterDataSyncService.handleCustomerApply(applyJson));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    public CallResult<List<CompanyLinkmanResponse>> getLinkmanListByCustomerNo(String enterpriseNo, String customerNo) {
        try {
            return CallResult.success(MessageVO.SUCCESS, customerService.getLinkmanListByCustomerNo(enterpriseNo, customerNo));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    public CallResult<List<CompanyLinkmanResponse>> getLinkmanListByCustomerNoList(String enterpriseNo, List<String> customerNoList) {
        try {
            return CallResult.success(MessageVO.SUCCESS, customerService.getLinkmanListByCustomerNoList(enterpriseNo, customerNoList));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 分页查询客户
     *
     * @param params
     * @param pageDto
     * @return
     */
    @Override
    public CallResult<PageVo<CustomerComponentResponse>> selectCustomerPageForCommonComponent(CustomerComponentRequest params, PageDto pageDto) {
        try {
            return CallResult.success(MessageVO.SUCCESS, customerService.selectCustomerPageForCommonComponent(params, pageDto));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 按指定条件查询客户
     *
     * @param params
     * @return
     */
    @Override
    public CallResult<List<CustomerComponentResponse>> selectCustomerListForCommonComponent(CustomerComponentRequest params) {
        try {
            return CallResult.success(MessageVO.SUCCESS, customerService.selectCustomerListForCommonComponent(params));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 查询客户详情
     *
     * @param enterpriseNo 租户编号
     * @param customerNo   客户no
     * @return
     */
    @Override
    public CallResult<CustomerInfoResponse> selectCustomerInfo(String enterpriseNo, String customerNo) {
        try {
            return CallResult.success(MessageVO.SUCCESS, customerService.selectCustomerInfo(enterpriseNo, customerNo));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 查询供应商使用信息
     *
     * @param enterpriseNo 租户编号
     * @param customerCode 供应商no
     * @return
     */
    @Override
    public CallResult<List<QueryUseInfoResponse>> queryUseInfo(String enterpriseNo, String customerCode) {
        try {
            return CallResult.success(MessageVO.SUCCESS, customerService.queryUseInfo(enterpriseNo, customerCode));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 根据组织编号和客户编码查询税务信息
     * @param orgNo
     * @param customerCode
     * @return
     */
    @Override
    public CallResult<List<CustomerInvoiceResponse>> getInvoiceListByCustomerCode(String orgNo, String customerCode) {
        try {
            return CallResult.success(MessageVO.SUCCESS, customerInvoiceService.getInvoiceListByCustomerCodeNoSession(orgNo, customerCode));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }
}
