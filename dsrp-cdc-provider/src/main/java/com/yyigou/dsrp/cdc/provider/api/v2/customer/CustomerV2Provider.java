package com.yyigou.dsrp.cdc.provider.api.v2.customer;

import cn.hutool.core.util.StrUtil;
import com.alibaba.dubbo.config.annotation.Service;
import com.yyigou.ddc.common.exception.CommonExceptionHandler;
import com.yyigou.ddc.common.message.MessageVO;
import com.yyigou.ddc.common.page.PageDto;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.ddc.common.service.ServiceBaseAbstract;
import com.yyigou.ddc.common.service.annotation.Interface;
import com.yyigou.ddc.common.service.annotation.Method;
import com.yyigou.ddc.common.service.annotation.MethodType;
import com.yyigou.ddc.services.ddc.task.message.incoming.DataImportDataSpiceMessage;
import com.yyigou.ddc.services.ddc.task.message.incoming.UapExcelImportDataSpiceMessage;
import com.yyigou.ddc.services.ddc.task.vo.incoming.ImportTaskDataSpiceVO;
import com.yyigou.ddc.services.ddc.uim.vo.OrganizationVo;
import com.yyigou.dsrp.cdc.api.v2.company.dto.CompanyLinkmanQueryDTO;
import com.yyigou.dsrp.cdc.api.v2.company.vo.CompanyLinkmanVO;
import com.yyigou.dsrp.cdc.api.v2.customer.CustomerV2API;
import com.yyigou.dsrp.cdc.api.v2.customer.dto.*;
import com.yyigou.dsrp.cdc.api.v2.customer.vo.*;
import com.yyigou.dsrp.cdc.common.util.OperationModel;
import com.yyigou.dsrp.cdc.model.constant.DistributedLockConstant;
import com.yyigou.dsrp.cdc.model.constant.ViewNameConstant;
import com.yyigou.dsrp.cdc.model.v2.company.req.CompanyBankReq;
import com.yyigou.dsrp.cdc.model.v2.company.req.CompanyLinkmanReq;
import com.yyigou.dsrp.cdc.model.v2.company.req.CompanyShippingAddressReq;
import com.yyigou.dsrp.cdc.model.v2.customer.req.*;
import com.yyigou.dsrp.cdc.model.v2.customersync.req.CustomerChannelSyncReq;
import com.yyigou.dsrp.cdc.service.utils.RedisClientUtil;
import com.yyigou.dsrp.cdc.service.utils.UserHandleUtils;
import com.yyigou.dsrp.cdc.service.v2.customer.CustomerV2Service;
import com.yyigou.dsrp.cdc.service.v2.customer.impl.CustomerV2ImportServiceImpl;
import com.yyigou.dsrp.cdc.service.v2.customersync.KlpCustomer2NCJobService;
import com.yyigou.dsrp.gcs.common.util.BeanUtil;
import com.yyigou.dsrp.gcs.common.util.ValidatorUtils;
import com.yyigou.dsrp.gcs.model.api.constant.ErrorCode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
@Service
@RequiredArgsConstructor
@Slf4j
@Interface(name = "客户档案页面接口")
public class CustomerV2Provider extends ServiceBaseAbstract implements CustomerV2API {
    @Resource(name = "customerV2Service")
    private CustomerV2Service customerV2Service;

    @Resource(name = "customerV2ImportService")
    private CustomerV2ImportServiceImpl customerV2ImportService;

    @Resource
    private RedisClientUtil redisClientUtil;

    @Resource
    private KlpCustomer2NCJobService klpCustomer2NCJobService;

    @Override
    @Method(aliasName = "dsrp.cdc.customer.v2.manageFindListPage", name = "客户档案列表查询（管理）", viewNo = ViewNameConstant.BDC_CUSTOMER_VIEW, orgAuth = true, methodType = MethodType.QUERY, processState = 2, version = "1.0.1")
    public CallResult<PageVo<CustomerPageVO>> manageFindListPage(CustomerManageQueryListPageDTO params, PageDto pageParams) {
        try {
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            CustomerManageQueryListPageReq queryReq = BeanUtil.copyFields(params, CustomerManageQueryListPageReq.class);
            //补充租户信息
            queryReq.setEnterpriseNo(operationModel.getEnterpriseNo());
            return CallResult.success(MessageVO.SUCCESS, customerV2Service.manageFindListPage(operationModel, queryReq, pageParams));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.customer.v2.useFindListPage", name = "客户档案列表查询（使用）", viewNo = ViewNameConstant.BDC_CUSTOMER_VIEW, orgAuth = true, methodType = MethodType.QUERY, processState = 2, version = "1.0.1")
    public CallResult<PageVo<CustomerPageVO>> useFindListPage(CustomerUseQueryListPageDTO params, PageDto pageParams) {
        try {
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            CustomerUseQueryListPageReq queryReq = BeanUtil.copyFields(params, CustomerUseQueryListPageReq.class);
            //补充租户信息
            queryReq.setEnterpriseNo(operationModel.getEnterpriseNo());
            return CallResult.success(MessageVO.SUCCESS, customerV2Service.useFindListPage(operationModel, queryReq, pageParams));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.customer.v2.reverseUseFindListPageNoAuthZ", name = "客户档案列表查询（使用）", viewNo = ViewNameConstant.BDC_CUSTOMER_VIEW, methodType = MethodType.QUERY, processState = 2, version = "1.0.1")
    public CallResult<PageVo<CustomerReversePageVO>> reverseUseFindListPageNoAuthZ(CustomerUseQueryListPageNoAuthZDTO params, PageDto pageParams) {
        try {
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            CustomerReverseUseQueryListPageNoAuthZReq queryReq = BeanUtil.copyFields(params, CustomerReverseUseQueryListPageNoAuthZReq.class);
            //补充租户信息
            queryReq.setEnterpriseNo(operationModel.getEnterpriseNo());
            return CallResult.success(MessageVO.SUCCESS, customerV2Service.reverseUseFindListPageNoAuthZ(operationModel, queryReq, pageParams));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }


    @Override
    @Method(aliasName = "dsrp.cdc.customer.v2.manageDelete", name = "客户档案删除", viewNo = ViewNameConstant.BDC_CUSTOMER_VIEW, methodType = MethodType.DELETE, processState = 2, version = "1.0.1")
    public CallResult<Boolean> manageDeleteCustomer(CustomerDeleteDTO params) {
        try {
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            CustomerDeleteReq customerDeleteReq = BeanUtil.copyFields(params, CustomerDeleteReq.class);
            //补充租户信息
            customerDeleteReq.setEnterpriseNo(operationModel.getEnterpriseNo());

            return CallResult.success(MessageVO.SUCCESS, customerV2Service.manageDeleteCustomer(operationModel, customerDeleteReq));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }


    @Override
    @Method(aliasName = "dsrp.cdc.customer.v2.getDetail", name = "客户档案获取", viewNo = ViewNameConstant.BDC_CUSTOMER_VIEW, methodType = MethodType.DETAIL, processState = 2, version = "1.0.1")
    public CallResult<CustomerVO> getCustomer(CustomerGetDTO params) {
        try {
            OperationModel operationModel = UserHandleUtils.getOperationModel();

            CustomerGetReq customerGetReq = BeanUtil.copyFields(params, CustomerGetReq.class);
            //补充租户信息
            customerGetReq.setEnterpriseNo(operationModel.getEnterpriseNo());
            return CallResult.success(MessageVO.SUCCESS, customerV2Service.getCustomer(operationModel, customerGetReq));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.customer.v2.changeStatus", name = "客户档案启停", viewNo = ViewNameConstant.BDC_CUSTOMER_VIEW, methodType = MethodType.UPDATE, processState = 2, version = "1.0.1")
    public CallResult<Boolean> changeCustomerStatus(CustomerChangeStatusDTO params) {
        try {
            OperationModel operationModel = UserHandleUtils.getOperationModel();

            CustomerChangeStatusReq customerChangeStatusReq = BeanUtil.copyFields(params, CustomerChangeStatusReq.class);
            //补充租户信息
            customerChangeStatusReq.setEnterpriseNo(operationModel.getEnterpriseNo());
            return CallResult.success(MessageVO.SUCCESS, customerV2Service.changeCustomerStatus(operationModel, customerChangeStatusReq));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.customer.v2.getAssign", name = "查询客户档案分派", viewNo = ViewNameConstant.BDC_CUSTOMER_VIEW, methodType = MethodType.QUERY, processState = 2, version = "1.0.1")
    public CallResult<List<CustomerAssignVO>> getAssignCustomer(CustomerGetAssignDTO params) {
        try {
            OperationModel operationModel = UserHandleUtils.getOperationModel();

            CustomerGetAssignReq customerAssignReq = BeanUtil.copyFields(params, CustomerGetAssignReq.class);
            //补充租户信息
            customerAssignReq.setEnterpriseNo(operationModel.getEnterpriseNo());
            return CallResult.success(MessageVO.SUCCESS, customerV2Service.getAssignCustomer(operationModel, customerAssignReq));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.customer.v2.getPendingCount", name = "查询待处理客户数量", methodType = MethodType.QUERY, viewNo = ViewNameConstant.BDC_CUSTOMER_VIEW, orgAuth = true, processState = 2, version = "1.0.1")
    public CallResult<Long> getPendingCount() {
        try {
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            return CallResult.success(MessageVO.SUCCESS, customerV2Service.getPendingCount(operationModel));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.customer.v2.checkOnlyCode", name = "校验客户编码是否存在", methodType = MethodType.QUERY, viewNo = ViewNameConstant.BDC_CUSTOMER_VIEW, orgAuth = true, processState = 2, version = "1.0.1")
    public CallResult<Boolean> checkOnlyCode(String no, String code) {
        try {
            //补充租户信息
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            return CallResult.success(MessageVO.SUCCESS, customerV2Service.checkOnlyCode(operationModel, no, code));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.customer.v2.checkOnlyName", name = "校验客户名称是否存在", methodType = MethodType.QUERY, viewNo = ViewNameConstant.BDC_CUSTOMER_VIEW, orgAuth = true, processState = 2, version = "1.0.1")
    public CallResult<Boolean> checkOnlyName(String no, String name) {
        try {
            //补充租户信息
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            return CallResult.success(MessageVO.SUCCESS, customerV2Service.checkOnlyName(operationModel, no, name));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.customer.v2.queryPageCustomer", name = "分页获取客户档案", viewNo = ViewNameConstant.BDC_CUSTOMER_VIEW, methodType = MethodType.QUERY, processState = 2, requestSession = true, requestAuthentication = true)
    public CallResult<PageVo<CustomerPageVO>> queryPageCustomer(CustomerPageQueryDTO params, PageDto pageDto) {
        try {
            OperationModel operationModel = UserHandleUtils.getOperationModel();

            CustomerPageQueryReq queryReq = BeanUtil.copyFields(params, CustomerPageQueryReq.class);
            //补充租户信息
            queryReq.setEnterpriseNo(operationModel.getEnterpriseNo());
            queryReq.setUseOrgNo(params.getOrgNo());

            return CallResult.success(MessageVO.SUCCESS, customerV2Service.queryPageCustomer(operationModel, queryReq, pageDto));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.customer.v2.querySpecifyOrgPageCustomer", name = "分页获取指定组织客户档案", viewNo = ViewNameConstant.BDC_CUSTOMER_VIEW, methodType = MethodType.QUERY, processState = 2, requestSession = true, requestAuthentication = false)
    public CallResult<PageVo<CustomerPageVO>> querySpecifyOrgPageCustomer(CustomerPageQueryBySpecifyOrgDTO params, PageDto pageDto) {
        try {
            //补充租户信息
            OperationModel operationModel = UserHandleUtils.getOperationModel();

            CustomerPageQueryBySpecifyOrgReq queryReq = BeanUtil.copyFields(params, CustomerPageQueryBySpecifyOrgReq.class);
            //补充租户信息
//            queryReq.setEnterpriseNo(operationModel.getEnterpriseNo());
//            queryReq.setOrgNo(operationModel.getOrgNo());

            return CallResult.success(MessageVO.SUCCESS, customerV2Service.querySpecifyOrgPageCustomer(operationModel, queryReq, pageDto));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.customer.v2.findListPageByFormal", name = "模糊查询对象集合-分页(正式客户)", viewNo = ViewNameConstant.BDC_CUSTOMER_VIEW, methodType = MethodType.QUERY, processState = 2, requestSession = false, requestAuthentication = false, requestDomainAuthentication = false)
    public CallResult<PageVo<CustomerFormalPageVO>> findListPageByFormal(CustomerPageByFormalQueryDTO params, PageDto pageDto) {
        try {
            OperationModel operationModel = com.yyigou.dsrp.cdc.service.utils.UserHandleUtils.getOperationModel();

            CustomerPageByFormalQueryReq queryReq = BeanUtil.copyFields(params, CustomerPageByFormalQueryReq.class);

            //补充租户信息
            queryReq.setEnterpriseNo(operationModel.getEnterpriseNo());
            queryReq.setUseOrgNo(operationModel.getOrgNo());

            PageVo<CustomerFormalPageVO> pojoPageVo = customerV2Service.findListPageByFormal(operationModel, queryReq, pageDto);
            return CallResult.success(MessageVO.SUCCESS, pojoPageVo);
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.customer.v2.findNotInPageList", name = "排除传入集合", viewNo = ViewNameConstant.BDC_CUSTOMER_VIEW, methodType = MethodType.QUERY, processState = 2, requestSession = false, requestAuthentication = false, requestDomainAuthentication = false)
    public CallResult<PageVo<CustomerPageVO>> findNotInPageList(CustomerNotInPageListQueryDTO params, PageDto pageDto) {
        try {
            OperationModel operationModel = com.yyigou.dsrp.cdc.service.utils.UserHandleUtils.getOperationModel();

            CustomerNotInPageListQueryReq queryReq = BeanUtil.copyFields(params, CustomerNotInPageListQueryReq.class);

            //补充租户信息
            queryReq.setEnterpriseNo(operationModel.getEnterpriseNo());
            queryReq.setUseOrgNo(operationModel.getOrgNo());

            PageVo<CustomerPageVO> notInPageList = customerV2Service.findNotInPageList(operationModel, queryReq, pageDto);
            return CallResult.success(MessageVO.SUCCESS, notInPageList);
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.customer.v2.manageSaveBasicAndBiz", name = "客户档案保存", viewNo = ViewNameConstant.BDC_CUSTOMER_VIEW, methodType = MethodType.ADD, processState = 2, version = "1.0.1")
    public CallResult<String> manageSaveCustomerBasicAndBiz(CustomerSaveBasicAndBizDTO params) {
        String lock = null;
        try {
            OperationModel operationModel = com.yyigou.dsrp.cdc.service.utils.UserHandleUtils.getOperationModel();

            lock = String.join(DistributedLockConstant.DELIMITER, DistributedLockConstant.SUPPLIER_SAVE_LOCK_KEY, operationModel.getEnterpriseNo(), params.getCustomerCode());
            if (!redisClientUtil.lock(lock, DistributedLockConstant.DEFAULT_WAIT_TIME, DistributedLockConstant.DEFAULT_LEASE_TIME)) {
                return CallResult.failed(ErrorCode.BUSINESS_ERROR_CODE, StrUtil.format("客户【{}】正在处理，请稍后提交！", params.getCustomerCode()));
            }

            CustomerSaveBasicAndBizReq customerSaveBasicAndBizReq = BeanUtil.copyFields(params, CustomerSaveBasicAndBizReq.class);
            //补充租户信息
            customerSaveBasicAndBizReq.setEnterpriseNo(operationModel.getEnterpriseNo());

            return CallResult.success(MessageVO.SUCCESS, customerV2Service.manageSaveCustomerBasicAndBiz(operationModel, customerSaveBasicAndBizReq));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        } finally {
            if (null != lock) {
                redisClientUtil.unlock(lock);
            }
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.customer.v2.editBasicAndBiz", name = "客户档案编辑", viewNo = ViewNameConstant.BDC_CUSTOMER_VIEW, methodType = MethodType.UPDATE, processState = 2, version = "1.0.1")
    public CallResult<Boolean> editCustomerBasicAndBiz(CustomerEditBasicAndBizDTO params) {
        try {
            OperationModel operationModel = com.yyigou.dsrp.cdc.service.utils.UserHandleUtils.getOperationModel();
            CustomerEditBasicAndBizReq customerUpdateReq = BeanUtil.copyFields(params, CustomerEditBasicAndBizReq.class);
            //补充租户信息
            customerUpdateReq.setEnterpriseNo(operationModel.getEnterpriseNo());

            return CallResult.success(MessageVO.SUCCESS, customerV2Service.editCustomerBasicAndBiz(operationModel, customerUpdateReq));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.customer.v2.editAddressList", name = "客户档案地址编辑", viewNo = ViewNameConstant.BDC_CUSTOMER_VIEW, methodType = MethodType.UPDATE, processState = 2, version = "1.0.1")
    public CallResult<List<Long>> editCustomerAddressList(CustomerAddressListEditDTO params) {
        try {
            OperationModel operationModel = UserHandleUtils.getOperationModel();

            CustomerAddressListEditReq customerAddressListEditReq = BeanUtil.copyFields(params, CustomerAddressListEditReq.class);
            customerAddressListEditReq.setLinkAddressList(BeanUtil.copyFieldsList(params.getLinkAddressList(), CompanyShippingAddressReq.class));
            //补充租户信息
            customerAddressListEditReq.setEnterpriseNo(operationModel.getEnterpriseNo());

            return CallResult.success(MessageVO.SUCCESS, customerV2Service.editCustomerAddressList(operationModel, customerAddressListEditReq));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.customer.v2.editLinkmanList", name = "客户档案联系人编辑", viewNo = ViewNameConstant.BDC_CUSTOMER_VIEW, methodType = MethodType.UPDATE, processState = 2, version = "1.0.1")
    public CallResult<List<Long>> editCustomerLinkmanList(CustomerLinkmanListEditDTO params) {
        try {
            //补充租户信息
            OperationModel operationModel = UserHandleUtils.getOperationModel();

            CustomerLinkmanListEditReq customerLinkmanListEditReq = BeanUtil.copyFields(params, CustomerLinkmanListEditReq.class);
            customerLinkmanListEditReq.setLinkmanList(BeanUtil.copyFieldsList(params.getLinkmanList(), CompanyLinkmanReq.class));
            //补充租户信息
            customerLinkmanListEditReq.setEnterpriseNo(operationModel.getEnterpriseNo());

            return CallResult.success(MessageVO.SUCCESS, customerV2Service.editCustomerLinkmanList(operationModel, customerLinkmanListEditReq));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.customer.v2.editSalesmanList", name = "客户档案负责人编辑", viewNo = ViewNameConstant.BDC_CUSTOMER_VIEW, methodType = MethodType.UPDATE, processState = 2, version = "1.0.1")
    public CallResult<List<Long>> editCustomerSalesmanList(CustomerSalesmanListEditDTO params) {
        try {
            OperationModel operationModel = UserHandleUtils.getOperationModel();

            CustomerSalesmanListEditReq customerSalesmanListEditReq = BeanUtil.copyFields(params, CustomerSalesmanListEditReq.class);
            customerSalesmanListEditReq.setSalesManList(BeanUtil.copyFieldsList(params.getSalesManList(), CustomerSalesManReq.class));
            //补充租户信息
            customerSalesmanListEditReq.setEnterpriseNo(operationModel.getEnterpriseNo());

            return CallResult.success(MessageVO.SUCCESS, customerV2Service.editCustomerSalesmanList(operationModel, customerSalesmanListEditReq));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.customer.v2.editInvoiceList", name = "客户档案发票编辑", viewNo = ViewNameConstant.BDC_CUSTOMER_VIEW, methodType = MethodType.UPDATE, processState = 2, version = "1.0.1")
    public CallResult<List<Long>> editCustomerInvoiceList(CustomerInvoiceListEditDTO params) {
        try {
            OperationModel operationModel = UserHandleUtils.getOperationModel();

            CustomerInvoiceListEditReq customerInvoiceListEditReq = BeanUtil.copyFields(params, CustomerInvoiceListEditReq.class);
            customerInvoiceListEditReq.setInvoiceList(BeanUtil.copyFieldsList(params.getInvoiceList(), CustomerInvoiceReq.class));
            //补充租户信息
            customerInvoiceListEditReq.setEnterpriseNo(operationModel.getEnterpriseNo());

            return CallResult.success(MessageVO.SUCCESS, customerV2Service.editCustomerInvoiceList(operationModel, customerInvoiceListEditReq));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.customer.v2.editBankList", name = "客户档案银行编辑", viewNo = ViewNameConstant.BDC_CUSTOMER_VIEW, methodType = MethodType.UPDATE, processState = 2, version = "1.0.1")
    public CallResult<List<Long>> editCustomerBankList(CustomerBankListEditDTO params) {
        try {
            //补充租户信息
            OperationModel operationModel = UserHandleUtils.getOperationModel();

            CustomerBankListEditReq customerBankListEditReq = BeanUtil.copyFields(params, CustomerBankListEditReq.class);
            customerBankListEditReq.setBankList(BeanUtil.copyFieldsList(params.getBankList(), CompanyBankReq.class));
            //补充租户信息
            customerBankListEditReq.setEnterpriseNo(operationModel.getEnterpriseNo());

            return CallResult.success(MessageVO.SUCCESS, customerV2Service.editCustomerBankList(operationModel, customerBankListEditReq));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.customer.v2.findOrgListPageBySetting", name = "查询内部组织", viewNo = ViewNameConstant.BDC_CUSTOMER_VIEW, methodType = MethodType.QUERY, processState = 2, version = "1.0.1")
    public CallResult<PageVo<OrganizationVo>> findOrgListPageBySetting(CustomerEligibleOrgListQueryDTO params, PageDto pageParams) {
        try {
            OperationModel operationModel = com.yyigou.dsrp.cdc.service.utils.UserHandleUtils.getOperationModel();

            CustomerEligibleOrgListQueryReq queryReq = BeanUtil.copyFields(params, CustomerEligibleOrgListQueryReq.class);
            //补充租户信息
            queryReq.setEnterpriseNo(operationModel.getEnterpriseNo());

            PageVo<OrganizationVo> pojoPageVo = customerV2Service.findOrgListPageBySetting(operationModel, queryReq, pageParams);
            return CallResult.success(MessageVO.SUCCESS, pojoPageVo);
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    public CallResult<ImportTaskDataSpiceVO> checkExcelSliceData(UapExcelImportDataSpiceMessage uapexcelImportDataSpiceMessage) {
        try {
            return CallResult.success(MessageVO.SUCCESS, customerV2ImportService.checkExcelSliceData(uapexcelImportDataSpiceMessage));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    public CallResult<ImportTaskDataSpiceVO> handleImportSliceData(DataImportDataSpiceMessage dataImportDataSpiceMessage) {
        try {
            return CallResult.success(MessageVO.SUCCESS, customerV2ImportService.handleImportSliceData(dataImportDataSpiceMessage));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 取客户联系地址列表(分页)
     *
     * @param params
     * @param pageDto
     */
    @Override
    @Method(aliasName = "dsrp.cdc.customer.v2.findCustomerLinkAddressPage", name = "获取客户联系地址列表(分页)", viewNo = ViewNameConstant.BDC_CUSTOMER_VIEW, methodType = MethodType.QUERY, processState = 2, version = "1.0.1")
    public CallResult<PageVo<CustomerAddressVO>> findCustomerLinkAddressPage(CustomerAddressQueryDTO params, PageDto pageDto) {
        try {
            CustomerAddressQueryReq customerAddressQueryReq = BeanUtil.copyFields(params, CustomerAddressQueryReq.class);

            OperationModel operationModel = UserHandleUtils.getOperationModel();

            customerAddressQueryReq.setEnterpriseNo(operationModel.getEnterpriseNo());

            return CallResult.success(MessageVO.SUCCESS, customerV2Service.findCustomerAddressPageList(operationModel, customerAddressQueryReq, pageDto));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 保存客户联系地址列表
     *
     * @param params
     * @return
     */
    @Override
    @Method(aliasName = "dsrp.cdc.customer.v2.saveCustomerLinkAddress", name = "保存客户联系地址列表", viewNo = ViewNameConstant.BDC_CUSTOMER_VIEW, methodType = MethodType.ADD, processState = 2, version = "1.0.1")
    public CallResult<List<CustomerAddressVO>> saveCustomerLinkAddress(CustomerAddressListSaveDTO params) {
        try {
            OperationModel operationModel = UserHandleUtils.getOperationModel();

            CustomerAddressListSaveReq customerAddressListSaveReq = new CustomerAddressListSaveReq();
            customerAddressListSaveReq.setEnterpriseNo(operationModel.getEnterpriseNo());
            customerAddressListSaveReq.setUseOrgNo(params.getUseOrgNo());
            customerAddressListSaveReq.setCustomerCode(params.getCustomerCode());

            List<CompanyShippingAddressReq> companyShippingAddressReqList = BeanUtil.copyFieldsList(params.getLinkAddressList(), CompanyShippingAddressReq.class);
            customerAddressListSaveReq.setLinkAddressList(companyShippingAddressReqList);

            List<Long> ids = customerV2Service.saveCustomerAddressList(operationModel, customerAddressListSaveReq);

            return CallResult.success(MessageVO.SUCCESS, customerV2Service.findCustomerAddressListByIds(operationModel.getEnterpriseNo(), ids));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 跨组织删除联系地址
     *
     * @param params
     * @return
     */
    @Override
    @Method(aliasName = "dsrp.cdc.customer.v2.deleteCustomerLinkAddress", name = "跨组织删除联系地址", viewNo = ViewNameConstant.BDC_CUSTOMER_VIEW, methodType = MethodType.DELETE, processState = 2, version = "1.0.1")
    public CallResult<Boolean> deleteCustomerLinkAddress(CustomerAddressListDeleteDTO params) {
        try {
            OperationModel operationModel = UserHandleUtils.getOperationModel();

            CustomerAddressListDeleteReq customerAddressListDeleteReq = new CustomerAddressListDeleteReq();
            customerAddressListDeleteReq.setEnterpriseNo(operationModel.getEnterpriseNo());
            customerAddressListDeleteReq.setUseOrgNo(params.getUseOrgNo());
            customerAddressListDeleteReq.setCustomerCode(params.getCustomerCode());
            customerAddressListDeleteReq.setLinkAddressList(params.getLinkAddressList());

            customerV2Service.deleteCustomerAddressList(operationModel, customerAddressListDeleteReq);

            return CallResult.success(MessageVO.SUCCESS, true);
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 编辑联系地址
     *
     * @param params
     * @return
     */
    @Override
    @Method(aliasName = "dsrp.cdc.customer.v2.updateCustomerLinkAddress", name = "编辑客户联系地址列表", viewNo = ViewNameConstant.BDC_CUSTOMER_VIEW, methodType = MethodType.UPDATE, processState = 2, version = "1.0.1")
    public CallResult<Boolean> updateCustomerLinkAddress(CustomerAddressListUpdateDTO params) {
        try {
            OperationModel operationModel = UserHandleUtils.getOperationModel();

            CustomerAddressListUpdateReq customerAddressListUpdateReq = new CustomerAddressListUpdateReq();
            customerAddressListUpdateReq.setEnterpriseNo(operationModel.getEnterpriseNo());
            customerAddressListUpdateReq.setUseOrgNo(params.getUseOrgNo());
            customerAddressListUpdateReq.setCustomerCode(params.getCustomerCode());

            List<CompanyShippingAddressReq> companyShippingAddressReqList = BeanUtil.copyFieldsList(params.getLinkAddressList(), CompanyShippingAddressReq.class);
            customerAddressListUpdateReq.setLinkAddressList(companyShippingAddressReqList);

            customerV2Service.updateCustomerAddressList(operationModel, customerAddressListUpdateReq);

            return CallResult.success(MessageVO.SUCCESS, true);
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 取客户联系地址列表(分页)
     *
     * @param params
     */
    @Override
    @Method(aliasName = "dsrp.cdc.customer.v2.findCustomerSalesManList", name = "查询客户负责人列表（不分页）", viewNo = ViewNameConstant.BDC_CUSTOMER_VIEW, methodType = MethodType.QUERY, processState = 2, version = "1.0.1")
    public CallResult<List<CustomerSalesManVO>> findCustomerSalesManList(CustomerSalesManQueryDTO params) {
        try {
            CustomerSalesManQueryReq customerSalesManQueryReq = BeanUtil.copyFields(params, CustomerSalesManQueryReq.class);

            OperationModel operationModel = UserHandleUtils.getOperationModel();

            customerSalesManQueryReq.setEnterpriseNo(operationModel.getEnterpriseNo());

            return CallResult.success(MessageVO.SUCCESS, customerV2Service.findCustomerSalesManList(operationModel, customerSalesManQueryReq));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 取客户联系地址列表(分页)
     *
     * @param params
     * @param pageDto
     */
    @Override
    @Method(aliasName = "dsrp.cdc.customer.v2.findCustomerSalesManPage", name = "获取客户负责人列表(分页)", viewNo = ViewNameConstant.BDC_CUSTOMER_VIEW, methodType = MethodType.QUERY, processState = 2, version = "1.0.1")
    public CallResult<PageVo<CustomerSalesManVO>> findCustomerSalesManPage(CustomerSalesManQueryDTO params, PageDto pageDto) {
        try {
            CustomerSalesManQueryReq customerSalesManQueryReq = BeanUtil.copyFields(params, CustomerSalesManQueryReq.class);

            OperationModel operationModel = UserHandleUtils.getOperationModel();

            customerSalesManQueryReq.setEnterpriseNo(operationModel.getEnterpriseNo());

            return CallResult.success(MessageVO.SUCCESS, customerV2Service.findCustomerSalesManListPage(operationModel, customerSalesManQueryReq, pageDto));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.customer.v2.getInvoiceListByCustomerCode", name = "获取客户税务信息列表", viewNo = ViewNameConstant.BDC_CUSTOMER_VIEW, methodType = MethodType.QUERY, processState = 2, version = "1.0.1")
    public CallResult<List<CustomerInvoiceVO>> getInvoiceListByCustomerCode(CustomerInvoiceQueryDTO params) {
        try {
            OperationModel operationModel = UserHandleUtils.getOperationModel();

            CustomerInvoiceQueryReq customerInvoiceQueryReq = BeanUtil.copyFields(params, CustomerInvoiceQueryReq.class);
            customerInvoiceQueryReq.setEnterpriseNo(operationModel.getEnterpriseNo());

            return CallResult.success(MessageVO.SUCCESS, customerV2Service.findCustomerInvoiceList(operationModel.getEnterpriseNo(), customerInvoiceQueryReq));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.customer.v2.saveCustomerLinkman", name = "客户档案联系人新增", viewNo = ViewNameConstant.BDC_CUSTOMER_VIEW, methodType = MethodType.UPDATE, processState = 2, version = "1.0.1")
    public CallResult<List<CompanyLinkmanVO>> saveCustomerLinkman(CustomerLinkmanListSaveDTO params) {
        try {
            //补充租户信息
            OperationModel operationModel = UserHandleUtils.getOperationModel();

            CustomerLinkmanListSaveReq customerLinkmanListSaveReq = new CustomerLinkmanListSaveReq();
            customerLinkmanListSaveReq.setCustomerCode(params.getCustomerCode());
            customerLinkmanListSaveReq.setUseOrgNo(params.getUseOrgNo());
            customerLinkmanListSaveReq.setEnterpriseNo(operationModel.getEnterpriseNo());

            List<CompanyLinkmanReq> customerSalesManReqList = BeanUtil.copyFieldsList(params.getLinkmanList(), CompanyLinkmanReq.class);
            customerLinkmanListSaveReq.setLinkmanList(customerSalesManReqList);
            List<Long> ids = customerV2Service.saveCustomerLinkmanList(operationModel, customerLinkmanListSaveReq);

            return CallResult.success(MessageVO.SUCCESS, customerV2Service.findCustomerLinkmanListByIds(operationModel.getEnterpriseNo(), ids));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.customer.v2.updateCustomerLinkman", name = "客户档案联系人编辑", viewNo = ViewNameConstant.BDC_CUSTOMER_VIEW, methodType = MethodType.UPDATE, processState = 2, version = "1.0.1")
    public CallResult<Boolean> updateCustomerLinkman(CustomerLinkmanListUpdateDTO params) {
        try {
            //补充租户信息
            OperationModel operationModel = UserHandleUtils.getOperationModel();

            CustomerLinkmanListUpdateReq customerLinkmanListUpdateReq = new CustomerLinkmanListUpdateReq();
            customerLinkmanListUpdateReq.setCustomerCode(params.getCustomerCode());
            customerLinkmanListUpdateReq.setUseOrgNo(params.getUseOrgNo());
            customerLinkmanListUpdateReq.setEnterpriseNo(operationModel.getEnterpriseNo());

            List<CompanyLinkmanReq> customerSalesManReqList = BeanUtil.copyFieldsList(params.getLinkmanList(), CompanyLinkmanReq.class);
            customerLinkmanListUpdateReq.setLinkmanList(customerSalesManReqList);
            customerV2Service.updateCustomerLinkmanList(operationModel, customerLinkmanListUpdateReq);

            return CallResult.success(MessageVO.SUCCESS, true);
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.customer.v2.deleteCustomerLinkman", name = "客户档案联系人删除", viewNo = ViewNameConstant.BDC_CUSTOMER_VIEW, methodType = MethodType.UPDATE, processState = 2, version = "1.0.1")
    public CallResult<Boolean> deleteCustomerLinkman(CustomerLinkmanListDeleteDTO params) {
        try {
            //补充租户信息
            OperationModel operationModel = UserHandleUtils.getOperationModel();

            CustomerLinkmanListDeleteReq customerDeleteLinkmanListReq = new CustomerLinkmanListDeleteReq();
            customerDeleteLinkmanListReq.setEnterpriseNo(operationModel.getEnterpriseNo());
            customerDeleteLinkmanListReq.setCustomerCode(params.getCustomerCode());
            customerDeleteLinkmanListReq.setUseOrgNo(params.getUseOrgNo());
            customerDeleteLinkmanListReq.setLinkmanList(params.getLinkmanList());

            customerV2Service.deleteCustomerLinkmanList(operationModel, customerDeleteLinkmanListReq);

            return CallResult.success(MessageVO.SUCCESS, true);
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.customer.v2.findCustomerLinkmanListPage", name = "客户档案联系人分页查询", viewNo = ViewNameConstant.BDC_CUSTOMER_VIEW, methodType = MethodType.QUERY, processState = 2, version = "1.0.1")
    public CallResult<PageVo<CompanyLinkmanVO>> findCustomerLinkmanListPage(CompanyLinkmanQueryDTO params, PageDto pageParams) {
        try {
            //补充租户信息
            OperationModel operationModel = UserHandleUtils.getOperationModel();

            CustomerLinkmanQueryReq customerLinkManQueryReq = BeanUtil.copyFields(params, CustomerLinkmanQueryReq.class);

            customerLinkManQueryReq.setEnterpriseNo(operationModel.getEnterpriseNo());

            return CallResult.success(MessageVO.SUCCESS, customerV2Service.findCustomerLinkmanListPage(operationModel, customerLinkManQueryReq, pageParams));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.customer.v2.findCustomerLinkmanList", name = "客户档案联系人查询", viewNo = ViewNameConstant.BDC_CUSTOMER_VIEW, methodType = MethodType.QUERY, processState = 2, version = "1.0.1")
    public CallResult<List<CompanyLinkmanVO>> findCustomerLinkmanList(CompanyLinkmanQueryDTO params) {
        try {
            //补充租户信息
            OperationModel operationModel = UserHandleUtils.getOperationModel();

            CustomerLinkmanQueryReq customerLinkManQueryReq = BeanUtil.copyFields(params, CustomerLinkmanQueryReq.class);

            customerLinkManQueryReq.setEnterpriseNo(operationModel.getEnterpriseNo());

            return CallResult.success(MessageVO.SUCCESS, customerV2Service.findCustomerLinkmanList(operationModel, customerLinkManQueryReq));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.customer.v2.checkConvertToSupplier", name = "检测管理组织是否可转供应商", viewNo = ViewNameConstant.BDC_CUSTOMER_VIEW, methodType = MethodType.QUERY, processState = 2, version = "1.0.1")
    public CallResult<Boolean> checkConvertToSupplier(CustomerConvertToSupplierDTO params) {
        try {
            OperationModel operationModel = com.yyigou.dsrp.cdc.service.utils.UserHandleUtils.getOperationModel();

            ValidatorUtils.checkEmptyThrowEx(params, "参数不能为空");

            return CallResult.success(MessageVO.SUCCESS, customerV2Service.checkConvertToSupplier(operationModel, params.getCustomerCode()));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "dsrp.cdc.customer.v2.syncKlpCustomer", name = "mock触发同步客户到NC", viewNo = ViewNameConstant.BDC_CUSTOMER_VIEW, methodType = MethodType.UPDATE, processState = 2, version = "1.0.1", requestAuthentication = false, requestSession = false)
    public CallResult<Boolean> syncKlpCustomer(CustomerChannelSyncDTO params) {
        CustomerChannelSyncReq customerChannelSyncReq = BeanUtil.copyFields(params, CustomerChannelSyncReq.class);

        klpCustomer2NCJobService.syncCustomer2NC(customerChannelSyncReq);

        return CallResult.success(true);
    }

}
