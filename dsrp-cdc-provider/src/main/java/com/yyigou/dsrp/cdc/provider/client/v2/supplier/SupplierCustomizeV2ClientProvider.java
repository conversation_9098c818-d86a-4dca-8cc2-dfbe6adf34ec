package com.yyigou.dsrp.cdc.provider.client.v2.supplier;

import com.yyigou.ddc.common.exception.CommonExceptionHandler;
import com.yyigou.ddc.common.message.MessageVO;
import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.dsrp.cdc.client.v2.supplier.SupplierCustomizeClient;
import com.yyigou.dsrp.cdc.client.v2.supplier.request.SupplierCoordinationCreateRequest;
import com.yyigou.dsrp.cdc.client.v2.supplier.response.SupplierCoordinationCreateResponse;
import com.yyigou.dsrp.cdc.model.constant.DistributedLockConstant;
import com.yyigou.dsrp.cdc.service.utils.RedisClientUtil;
import com.yyigou.dsrp.cdc.service.v2.supplier.SupplierV2Service;
import com.yyigou.dsrp.gcs.model.api.constant.ErrorCode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
@RequiredArgsConstructor
@Slf4j
public class SupplierCustomizeV2ClientProvider implements SupplierCustomizeClient {
    @Resource
    private SupplierV2Service supplierV2Service;

    @Resource
    private RedisClientUtil redisClientUtil;

    @Override
    public CallResult<SupplierCoordinationCreateResponse> createCoordinationSupplier(SupplierCoordinationCreateRequest params) {
        String lock = null;
        try {
            lock = String.join(DistributedLockConstant.DELIMITER, DistributedLockConstant.SUPPLIER_SAVE_LOCK_KEY, params.getEnterpriseNo(), params.getSupplierName(), params.getUnifiedSocialCode(), params.getOmsSupplierNo());
            if (!redisClientUtil.lock(lock, DistributedLockConstant.DEFAULT_WAIT_TIME, DistributedLockConstant.DEFAULT_LEASE_TIME)) {
                return CallResult.failed(ErrorCode.BUSINESS_ERROR_CODE, "请不要在短时间内重复调用");
            }

            log.info("createCoordinationSupplierReq:{}", params);
            SupplierCoordinationCreateResponse coordinationSupplier = supplierV2Service.createCoordinationSupplier(params.getEnterpriseNo(), params.getOrgNo(), params.getSupplierName(), params.getUnifiedSocialCode(), params.getOmsSupplierNo());
            log.info("createCoordinationSupplierRes:{}", coordinationSupplier);

            return CallResult.success(MessageVO.SUCCESS, coordinationSupplier);
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        } finally {
            if (null != lock) {
                redisClientUtil.unlock(lock);
            }
        }
    }
}
