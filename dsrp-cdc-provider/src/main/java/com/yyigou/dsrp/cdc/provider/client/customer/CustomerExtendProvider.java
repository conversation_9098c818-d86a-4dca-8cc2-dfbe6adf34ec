package com.yyigou.dsrp.cdc.provider.client.customer;

import com.alibaba.dubbo.config.annotation.Service;
import com.yyigou.ddc.common.exception.CommonExceptionHandler;
import com.yyigou.ddc.common.message.MessageVO;
import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.dsrp.cdc.client.customer.CustomerExtendClient;
import com.yyigou.dsrp.cdc.client.customer.request.CoordinationCustomerRequest;
import com.yyigou.dsrp.cdc.client.customer.request.CustomerAreaRequest;
import com.yyigou.dsrp.cdc.client.customer.response.AreaResponse;
import com.yyigou.dsrp.cdc.client.customer.response.CoordinationCustomerResponse;
import com.yyigou.dsrp.cdc.client.supplier.request.SupplierManQueryRequest;
import com.yyigou.dsrp.cdc.client.supplier.response.SupplierSalesManResponse;
import com.yyigou.dsrp.cdc.service.customer.CustomerExtendService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
@Service
@RequiredArgsConstructor
@Slf4j
public class CustomerExtendProvider implements CustomerExtendClient {
    @Resource
    private final CustomerExtendService customerExtendService;

    @Override
    public CallResult<List<SupplierSalesManResponse>> querySupplierMan(String enterpriseNo, String supplierNo, String orgNo, String orderManNo, List<Integer> orderSpecialist) {
//        try {
//            return CallResult.success(MessageVO.SUCCESS, supplyExtendService.querySupplierMan(enterpriseNo, supplierNo, orgNo, orderManNo, orderSpecialist));
//        } catch (Exception e) {
//            return CommonExceptionHandler.handleException(e);
//        }
        return null;
    }

    @Override
    public CallResult<List<SupplierSalesManResponse>> querySupplierManList(SupplierManQueryRequest params) {
        return null;
    }

    /**
     * 查询协同客户
     *
     * @param params
     * @return
     */
    @Override
    public CallResult<List<CoordinationCustomerResponse>> queryCoordinationCustomer(CoordinationCustomerRequest params) {
        try {
            return CallResult.success(MessageVO.SUCCESS, customerExtendService.queryCoordinationCustomer(params));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    public CallResult<List<AreaResponse>> queryCustomerArea(CustomerAreaRequest params) {
        try {
            return CallResult.success(MessageVO.SUCCESS, customerExtendService.queryCustomerArea(params));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }
}
