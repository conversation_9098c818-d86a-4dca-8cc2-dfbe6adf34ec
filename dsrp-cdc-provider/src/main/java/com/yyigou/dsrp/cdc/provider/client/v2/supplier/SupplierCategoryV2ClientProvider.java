package com.yyigou.dsrp.cdc.provider.client.v2.supplier;

import com.alibaba.dubbo.config.annotation.Service;
import com.yyigou.ddc.common.exception.CommonExceptionHandler;
import com.yyigou.ddc.common.message.MessageVO;
import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.dsrp.cdc.client.v2.supplier.SupplierCategoryRpcClient;
import com.yyigou.dsrp.cdc.client.v2.supplier.response.SupplierCategoryResponse;
import com.yyigou.dsrp.cdc.common.util.BeanUtil;
import com.yyigou.dsrp.cdc.dao.v2.supplier.entity.SupplierCategoryV2;
import com.yyigou.dsrp.cdc.service.v2.supplier.SupplierCategoryV2Service;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
@RequiredArgsConstructor
@Slf4j
@Service
public class SupplierCategoryV2ClientProvider implements SupplierCategoryRpcClient {
    @Resource
    private SupplierCategoryV2Service supplierCategoryV2Service;

    @Override
    public CallResult<List<SupplierCategoryResponse>> selectSupplierCategoryListByNo(String enterpriseNo, List<String> supplierCategoryNoList) {
        try {
            log.info("selectSupplierCategoryListByNoReq:{},{}", enterpriseNo, supplierCategoryNoList);

            List<SupplierCategoryV2> list = supplierCategoryV2Service.getByNoList(enterpriseNo, supplierCategoryNoList);

            log.info("selectSupplierCategoryListByNoRes:{}", list);

            return CallResult.success(MessageVO.SUCCESS, BeanUtil.copyFieldsList(list, SupplierCategoryResponse.class));

        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }
}
