package com.yyigou.dsrp.cdc.provider.api.supply;

import com.alibaba.dubbo.config.annotation.Service;
import com.yyigou.ddc.common.exception.CommonExceptionHandler;
import com.yyigou.ddc.common.message.MessageVO;
import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.ddc.common.service.ServiceBaseAbstract;
import com.yyigou.ddc.common.service.annotation.Interface;
import com.yyigou.ddc.common.service.annotation.Method;
import com.yyigou.ddc.common.service.annotation.MethodType;
import com.yyigou.dsrp.cdc.api.company.vo.CompanyLinkmanVO;
import com.yyigou.dsrp.cdc.api.supply.SupplierLinkManApi;
import com.yyigou.dsrp.cdc.api.supply.dto.SupplierLinkmanByOrgDTO;
import com.yyigou.dsrp.cdc.common.util.OperationModel;
import com.yyigou.dsrp.cdc.service.supplier.SupplierLinkManService;
import com.yyigou.dsrp.cdc.service.utils.UserHandleUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Service
@RequiredArgsConstructor
@Slf4j
@Interface(name = "供应商联系人接口")
public class SupplierLinkManProvider extends ServiceBaseAbstract implements SupplierLinkManApi {

    private final SupplierLinkManService supplierLinkManService;

    /**
     * 跨组织获取供应商联系人列表
     *
     * @param supplierCode
     * @param orgNo
     * @return
     */
    @Override
    @Method(aliasName = "dsrp.cdc.supplier.org.getSupplierLinkmanListByOrg", name = "跨组织获取供应商联系人列表", methodType = MethodType.QUERY, processState = 2, requestSession = false, requestAuthentication = false)
    public CallResult<List<CompanyLinkmanVO>> getSupplierLinkmanListByOrg(String supplierCode, String orgNo) {
        try {
            //补充租户信息
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            return CallResult.success(MessageVO.SUCCESS, supplierLinkManService.getSupplierLinkmanListByOrg(operationModel, supplierCode, orgNo));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 跨组织保存供应商联系人列表
     *
     * @param params
     * @return
     */
    @Override
    @Method(aliasName = "dsrp.cdc.supplier.org.saveSupplierLinkmanByOrg", name = "跨组织保存供应商联系人列表", methodType = MethodType.QUERY, processState = 2, requestSession = false, requestAuthentication = false)
    public CallResult<CompanyLinkmanVO> saveSupplierLinkmanByOrg(SupplierLinkmanByOrgDTO params) {
        try {
            //补充租户信息
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            return CallResult.success(MessageVO.SUCCESS, supplierLinkManService.saveSupplierLinkmanByOrg(operationModel, params));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 跨组织删除联系人
     *
     * @param params
     * @return
     */
    @Override
    @Method(aliasName = "dsrp.cdc.supplier.org.editSupplierLinkmanByOrg", name = "跨组织删除联系人", methodType = MethodType.QUERY, processState = 2, requestSession = false, requestAuthentication = false)
    public CallResult<CompanyLinkmanVO> editSupplierLinkmanByOrg(SupplierLinkmanByOrgDTO params) {
        try {
            //补充租户信息
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            return CallResult.success(MessageVO.SUCCESS, supplierLinkManService.editSupplierLinkmanByOrg(operationModel, params));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 跨组织删除联系人
     *
     * @param linkmanId
     * @return
     */
    @Override
    @Method(aliasName = "dsrp.cdc.supplier.org.deleteSupplierLinkmanByOrg", name = "跨组织删除联系人", methodType = MethodType.QUERY, processState = 2, requestSession = false, requestAuthentication = false)
    public CallResult<Boolean> deleteSupplierLinkmanByOrg(Long linkmanId) {
        try {
            //补充租户信息
            OperationModel operationModel = UserHandleUtils.getOperationModel();
            return CallResult.success(MessageVO.SUCCESS, supplierLinkManService.deleteSupplierLinkmanByOrg(operationModel, linkmanId));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }
}
