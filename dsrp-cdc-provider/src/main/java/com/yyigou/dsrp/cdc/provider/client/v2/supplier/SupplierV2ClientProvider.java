package com.yyigou.dsrp.cdc.provider.client.v2.supplier;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.config.annotation.Service;
import com.yyigou.ddc.common.exception.CommonExceptionHandler;
import com.yyigou.ddc.common.message.MessageVO;
import com.yyigou.ddc.common.page.PageDto;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.ddc.common.util.ValidatorUtils;
import com.yyigou.ddc.services.ddc.uap.domain.dto.grade.GradeDocAssignMessage;
import com.yyigou.ddc.services.ddc.uap.domain.vo.grade.GradeRpcAssignVO;
import com.yyigou.dsrp.cdc.api.v2.supplier.vo.SupplierVO;
import com.yyigou.dsrp.cdc.client.supplier.request.SupplierNameRequest;
import com.yyigou.dsrp.cdc.client.supplier.request.SupplierNoRequest;
import com.yyigou.dsrp.cdc.client.v2.supplier.SupplierRpcClient;
import com.yyigou.dsrp.cdc.client.v2.supplier.request.*;
import com.yyigou.dsrp.cdc.client.v2.supplier.response.SupplierAssignExeResponse;
import com.yyigou.dsrp.cdc.client.v2.supplier.response.SupplierBaseResponse;
import com.yyigou.dsrp.cdc.client.v2.supplier.response.SupplierBasicResponse;
import com.yyigou.dsrp.cdc.client.v2.supplier.response.SupplierBizResponse;
import com.yyigou.dsrp.cdc.common.util.BeanUtil;
import com.yyigou.dsrp.cdc.model.v2.supplier.req.SupplierCodeOrgPairListReq;
import com.yyigou.dsrp.cdc.model.v2.supplier.req.SupplierGspStatusReq;
import com.yyigou.dsrp.cdc.model.v2.supplier.req.SupplierStandardQueryReq;
import com.yyigou.dsrp.cdc.service.v2.supplier.SupplierV2Service;
import com.yyigou.dsrp.gcs.model.api.constant.ErrorCode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Component
@RequiredArgsConstructor
@Slf4j
@Service
public class SupplierV2ClientProvider implements SupplierRpcClient {
    @Resource
    private SupplierV2Service supplierV2Service;

    @Override
    public CallResult<List<SupplierBasicResponse>> selectSupplierBasicInfoList(SupplierStandardRequest params) {
        try {
            log.info("selectSupplierBasicInfoListReq:{}", params);

            SupplierStandardQueryReq supplierStandardQueryReq = BeanUtil.copyFields(params, SupplierStandardQueryReq.class);

            List<SupplierBasicResponse> data = supplierV2Service.queryStandardInfo(supplierStandardQueryReq);

            log.info("selectSupplierBasicInfoListRes:{}", data);

            return CallResult.success(MessageVO.SUCCESS, data);
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    public CallResult<List<SupplierBizResponse>> selectSupplierBizInfoList(SupplierStandardRequest params) {
        try {
            log.info("selectSupplierBizInfoListReq:{}", params);

            SupplierStandardQueryReq supplierStandardQueryReq = BeanUtil.copyFields(params, SupplierStandardQueryReq.class);

            List<SupplierBizResponse> data = supplierV2Service.queryWithBizInfo(supplierStandardQueryReq);

            log.info("selectSupplierBizInfoListRes:{}", data);

            return CallResult.success(MessageVO.SUCCESS, data);
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    public CallResult<List<SupplierBizResponse>> selectSupplierBizByCodeOrgPair(SupplierCodeOrgPairListRequest params) {
        try {
            log.info("selectSupplierBizByCodeOrgPairReq:{}", params);

            SupplierCodeOrgPairListReq supplierStandardQueryReq = BeanUtil.copyFields(params, SupplierCodeOrgPairListReq.class);

            List<SupplierBizResponse> data = supplierV2Service.selectSupplierBizByCodeOrgPair(supplierStandardQueryReq);

            log.info("selectSupplierBizByCodeOrgPairRes:{}", data);

            return CallResult.success(MessageVO.SUCCESS, data);

        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    public CallResult<SupplierBizResponse> getSupplierBizInfoBySupplierNo(String enterpriseNo, String useOrgNo, String supplierNo) {
        try {
            log.info("getSupplierBizInfoBySupplierNoReq:{},{},{}", enterpriseNo, useOrgNo, supplierNo);

            SupplierStandardQueryReq supplierStandardQueryReq = new SupplierStandardQueryReq();
            supplierStandardQueryReq.setEnterpriseNo(enterpriseNo);
            supplierStandardQueryReq.setUseOrgNo(useOrgNo);
            supplierStandardQueryReq.setSupplierNo(supplierNo);

            List<SupplierBizResponse> supplierBizResponses = supplierV2Service.queryWithBizInfo(supplierStandardQueryReq);

            log.info("getSupplierBizInfoBySupplierNoRes:{}", supplierBizResponses);

            if (CollectionUtils.isNotEmpty(supplierBizResponses) && supplierBizResponses.size() == 1) {
                return CallResult.success(MessageVO.SUCCESS, supplierBizResponses.get(0));
            }

            return CallResult.failed(ErrorCode.BUSINESS_ERROR_CODE, "无法找到供应商档案");
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    public CallResult<SupplierBizResponse> getSupplierBizInfoBySupplierCode(String enterpriseNo, String useOrgNo, String supplierCode) {
        try {
            log.info("getSupplierBizInfoBySupplierCodeReq:{},{},{}", enterpriseNo, useOrgNo, supplierCode);

            SupplierStandardQueryReq supplierStandardQueryReq = new SupplierStandardQueryReq();
            supplierStandardQueryReq.setEnterpriseNo(enterpriseNo);
            supplierStandardQueryReq.setUseOrgNo(useOrgNo);
            supplierStandardQueryReq.setSupplierCode(supplierCode);

            List<SupplierBizResponse> supplierBizResponses = supplierV2Service.queryWithBizInfo(supplierStandardQueryReq);

            log.info("getSupplierBizInfoBySupplierCodeRes:{}", supplierBizResponses);

            if (CollectionUtils.isNotEmpty(supplierBizResponses) && supplierBizResponses.size() == 1) {
                return CallResult.success(MessageVO.SUCCESS, supplierBizResponses.get(0));
            }

            return CallResult.failed(ErrorCode.BUSINESS_ERROR_CODE, "无法找到供应商档案");
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    public CallResult<PageVo<SupplierBizResponse>> selectSupplierBizInfoListPage(SupplierStandardRequest params, PageDto pageDto) {
        try {
            log.info("selectSupplierBizInfoListPageReq:{},{}", params, pageDto);

            SupplierStandardQueryReq supplierStandardQueryReq = BeanUtil.copyFields(params, SupplierStandardQueryReq.class);

            PageVo<SupplierBizResponse> data = supplierV2Service.queryPageWithBizInfo(supplierStandardQueryReq, pageDto);

            log.info("selectSupplierBizInfoListPageRes:{}", data);

            return CallResult.success(MessageVO.SUCCESS, data);
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    public CallResult<List<SupplierBizResponse>> selectSupplierBizInfoByNo(SupplierNoRequest request) {
        try {
            log.info("selectSupplierBizInfoByNoReq:{}", request);

            SupplierStandardQueryReq supplierStandardQueryReq = new SupplierStandardQueryReq();
            supplierStandardQueryReq.setEnterpriseNo(request.getEnterpriseNo());
            supplierStandardQueryReq.setUseOrgNo(request.getUseOrgNo());
            supplierStandardQueryReq.setSupplierNo(request.getSupplierNo());
            supplierStandardQueryReq.setSupplierCode(request.getSupplierCode());
            supplierStandardQueryReq.setSupplierNoList(request.getSupplierNoList());
            supplierStandardQueryReq.setSupplierCodeList(request.getSupplierCodeList());

            List<SupplierBizResponse> data = supplierV2Service.queryWithBizInfo(supplierStandardQueryReq);

            log.info("selectSupplierBizInfoByNoRes:{}", data);

            return CallResult.success(MessageVO.SUCCESS, data);
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    public CallResult<List<SupplierBizResponse>> selectSupplierBizInfoByName(SupplierNameRequest request) {
        try {
            log.info("selectSupplierBizInfoByNameReq:{}", request);

            SupplierStandardQueryReq supplierStandardQueryReq = new SupplierStandardQueryReq();
            supplierStandardQueryReq.setEnterpriseNo(request.getEnterpriseNo());
            supplierStandardQueryReq.setUseOrgNo(request.getUseOrgNo());
            supplierStandardQueryReq.setSupplierName(request.getSupplierName());
            supplierStandardQueryReq.setSupplierKeywords(request.getSupplierNameKeyword());

            List<SupplierBizResponse> data = supplierV2Service.queryWithBizInfo(supplierStandardQueryReq);

            log.info("selectSupplierBizInfoByNameRes:{}", data);

            return CallResult.success(MessageVO.SUCCESS, data);
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    public CallResult<List<GradeRpcAssignVO>> assignSupplier(GradeDocAssignMessage message) {
        log.warn("assignSupplierReq={}", message);

        ValidatorUtils.checkEmptyThrowEx(message, "参数不能为空");
        ValidatorUtils.checkEmptyThrowEx(message.getEnterpriseNo(), "企业编号不能为空");
        ValidatorUtils.checkEmptyThrowEx(message.getMgrOrgNo(), "管理组织编号不能为空");
        ValidatorUtils.checkEmptyThrowEx(message.getDocNos(), "档案编号不能为空");
        ValidatorUtils.checkEmptyThrowEx(message.getShareOrgNos(), "使用组织编号不能为空");

        try {
            // 参数转换
            SupplierAssignExeRequest supplierAssignExeRequest = new SupplierAssignExeRequest();
            supplierAssignExeRequest.setEnterpriseNo(message.getEnterpriseNo());
            supplierAssignExeRequest.setManageOrgNo(message.getMgrOrgNo());
            supplierAssignExeRequest.setDocList(new ArrayList<>());

            for (String docNo : message.getDocNos()) {
                SupplierAssignDocExeRequest docRequest = new SupplierAssignDocExeRequest();
                docRequest.setSupplierCode(docNo);
                docRequest.setUseOrgNoList(new ArrayList<>());
                for (String shareOrgNo : message.getShareOrgNos()) {
                    docRequest.getUseOrgNoList().add(shareOrgNo);
                }
                supplierAssignExeRequest.getDocList().add(docRequest);
            }

            log.warn("assignSupplierSrvReq={}", supplierAssignExeRequest);
            List<SupplierAssignExeResponse> supplierAssignExeResponseList = supplierV2Service.assignSupplier(supplierAssignExeRequest);
            log.warn("assignSupplierSrvResp={}", supplierAssignExeResponseList);

            List<GradeRpcAssignVO> data = com.yyigou.dsrp.gcs.common.util.BeanUtil.copyFieldsList(supplierAssignExeResponseList, GradeRpcAssignVO.class);
            log.warn("assignSupplierRpcResp={}", data);

            return CallResult.success(data);
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    public CallResult<Boolean> changeGspStatus(SupplierGspStatusRequest message) {
        try {
            SupplierGspStatusReq supplierGspStatusReq = BeanUtil.copyFields(message, SupplierGspStatusReq.class);
            return CallResult.success(supplierV2Service.changeSupplierGspStatus(supplierGspStatusReq));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    public CallResult<List<SupplierBasicResponse>> querySupplierBasicInfoList(SupplierQueryRequest request) {
        try {
            log.info("querySupplierBasicInfoListReq:{}", request);

            SupplierStandardQueryReq supplierStandardQueryReq = new SupplierStandardQueryReq();
            supplierStandardQueryReq.setEnterpriseNo(request.getEnterpriseNo());
            supplierStandardQueryReq.setSupplierCodeList(request.getSupplierCodeList());

            List<SupplierBasicResponse> data = supplierV2Service.querySupplierByCodeList(request.getEnterpriseNo(), request.getSupplierCodeList());

            log.info("querySupplierBasicInfoListRes:{}", data);

            return CallResult.success(MessageVO.SUCCESS, data);
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 查询供应商使用信息
     *
     * @param enterpriseNo 租户编号
     * @param supplierCode 供应商编码
     * @return
     */
    @Override
    public CallResult<List<SupplierBaseResponse>> queryAssignInfo(String enterpriseNo, String supplierCode) {
        try {
            return CallResult.success(MessageVO.SUCCESS, supplierV2Service.queryAssignInfo(enterpriseNo, supplierCode));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    public CallResult<SupplierBasicResponse> getSupplierBasicInfoBySupplierCode(String enterpriseNo, String supplierCode) {
        ValidatorUtils.checkEmptyThrowEx(enterpriseNo, "租户编号不能为空");
        ValidatorUtils.checkEmptyThrowEx(supplierCode, "供应商编码不能为空");

        SupplierVO supplierVO = supplierV2Service.getSupplierByCode(enterpriseNo, supplierCode);
        if (supplierVO != null) {
            SupplierBasicResponse supplierBasicResponse = BeanUtil.copyFields(supplierVO, SupplierBasicResponse.class);

            return CallResult.success(supplierBasicResponse);
        }

        return null;
    }
}
