package com.yyigou.dsrp.cdc.provider.client.v2.supplier;

import com.alibaba.dubbo.config.annotation.Service;
import com.yyigou.ddc.common.exception.CommonExceptionHandler;
import com.yyigou.ddc.common.message.MessageVO;
import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.dsrp.cdc.api.v2.supplier.vo.SupplierApplyDetailVO;
import com.yyigou.dsrp.cdc.client.v2.supplier.SupplierApplyRpcClient;
import com.yyigou.dsrp.cdc.client.v2.supplier.response.SupplierApplyResponse;
import com.yyigou.dsrp.cdc.common.util.BeanUtil;
import com.yyigou.dsrp.cdc.common.util.OperationModel;
import com.yyigou.dsrp.cdc.model.v2.supplier.req.SupplierApplyGetReq;
import com.yyigou.dsrp.cdc.service.v2.supplier.SupplierApplyService;
import com.yyigou.dsrp.gcs.model.api.constant.ErrorCode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
@RequiredArgsConstructor
@Slf4j
@Service
public class SupplierApplyV2ClientProvider implements SupplierApplyRpcClient {
    @Resource
    private SupplierApplyService supplierApplyService;


    @Override
    public CallResult<SupplierApplyResponse> getSupplierApplyByNo(String enterpriseNo, String applyInstanceNo) {
        try {
            log.info("getSupplierApplyByNoReq:{},{}", enterpriseNo, applyInstanceNo);

            OperationModel operationModel = new OperationModel();
            operationModel.setEnterpriseNo(enterpriseNo);

            SupplierApplyGetReq supplierApplyGetReq = new SupplierApplyGetReq();
            supplierApplyGetReq.setApplyInstanceNo(applyInstanceNo);

            SupplierApplyDetailVO supplierApplyDetailVO = supplierApplyService.getDetail(operationModel, supplierApplyGetReq);

            if (null == supplierApplyDetailVO) {
                return CallResult.failed(ErrorCode.BUSINESS_ERROR_CODE, "无法找到供应商档案申请单");
            }

            SupplierApplyResponse supplierApplyResponse = BeanUtil.copyFields(supplierApplyDetailVO, SupplierApplyResponse.class);

            log.info("getSupplierApplyByNoRes:{}", supplierApplyResponse);

            return CallResult.success(MessageVO.SUCCESS, supplierApplyResponse);

        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }
}
