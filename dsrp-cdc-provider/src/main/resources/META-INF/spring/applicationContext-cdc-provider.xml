<?xml version="1.0" encoding="UTF-8"?>

<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:dubbo="http://dubbo.apache.org/schema/dubbo"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
    http://www.springframework.org/schema/beans/spring-beans.xsd
    http://www.springframework.org/schema/context
    http://www.springframework.org/schema/context/spring-context.xsd
    http://dubbo.apache.org/schema/dubbo http://dubbo.apache.org/schema/dubbo/dubbo.xsd">
    <!-- 对web包中的所有类进行扫描，以完成Bean创建和自动依赖注入的功能 -->
    <context:annotation-config/>
    <context:component-scan base-package="com.yyigou.dsrp.cdc.provider"/>

    <!-- API文档发布设置，开发环境中需配置此项用于自动发布API文档，开发电脑及其它环境不需要设置，或将isPublish设为false -->
    <bean id="apiDocumentConfig" class="com.yyigou.ddc.common.dubbo.registry.zookeeper.APIDocumentConfig">
        <property name="isPublish" value="true"/>
        <!-- 只有是以下指定IP的服务器才能发布，如不指定则不限制发布机器 -->
        <property name="publisherIPAddress" value=""/>
        <property name="discoveryUrl" value="${common.dubbo.provider.zkurl}"/>
    </bean>
    <!--配置热更新-->
    <bean id="zkMonitorClient" class="com.yyigou.ddc.common.zkmonitor.ZkMonitorClient">
        <property name="monitObjectScanPackage" value="com.yyigou"/>
    </bean>

    <!--dubbo服务配置-->
    <dubbo:application name="dsrp-cdc-provider" owner="yyigou" organization="yyigou.ddc"/>
    <dubbo:registry address="${common.dubbo.provider.zkurl}" file="/tmp/dubbo-cache/service-dsrp-cdc/default.cache"/>
    <dubbo:protocol name="dubbo" port="${service-dsrp-cdc.dubbo.provider.dubbo.port}"/>
    <dubbo:provider delay="-1" timeout="${common.dubbo.provider.timeout}" retries="0"/>

    <!-- dubbo服务提供 -->
    <import resource="classpath:META-INF/spring/dubbo/*.xml"/>
    <import resource="classpath*:application-cdc-service.xml"/>
    <import resource="classpath*:application-cdc-manager.xml"/>
    <import resource="classpath*:application-cdc-dao.xml"/>

</beans>
