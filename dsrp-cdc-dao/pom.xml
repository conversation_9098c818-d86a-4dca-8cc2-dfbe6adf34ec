<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.yyigou.dsrp.cdc</groupId>
        <artifactId>service-dsrp-cdc</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>dsrp-cdc-dao</artifactId>
    <packaging>jar</packaging>

    <name>dsrp-cdc-dao</name>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <!-- 启用mybatisplus依赖-->
        <dependency>
            <groupId>com.yyigou.ddc</groupId>
            <artifactId>common-persistent-mybatisplus</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yyigou.ddc</groupId>
            <artifactId>common-sql-analysis</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-generator</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.velocity</groupId>
            <artifactId>velocity-engine-core</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.yyigou.dsrp.cdc</groupId>
            <artifactId>dsrp-cdc-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yyigou.dsrp.cdc</groupId>
            <artifactId>dsrp-cdc-model</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yyigou.ddc.services</groupId>
            <artifactId>mq-manager</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yyigou.ddc.services</groupId>
            <artifactId>ddc-uap-client</artifactId>
        </dependency>
    </dependencies>
</project>
