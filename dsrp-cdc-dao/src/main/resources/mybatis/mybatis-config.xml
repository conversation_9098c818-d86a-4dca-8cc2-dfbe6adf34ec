<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright (C) 2018, Yun <PERSON> (www.yyigou.com), Inc - All Rights Reserved
  ~ Unauthorized copying of this file, via any medium is strictly prohibited
  ~ Proprietary and confidential
  ~ <AUTHOR> <<EMAIL>>
  ~ @updated 18-9-14 下午4:16
  ~ @project service_pay
  ~ @module pay-provider
  ~ @file mybatis-config.xml
  ~
  -->

<!DOCTYPE configuration
        PUBLIC "-//mybatis.org//DTD Config 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-config.dtd">
<configuration>
    <settings>
        <setting name="cacheEnabled" value="false"/>
        <setting name="lazyLoadingEnabled" value="true"/>
        <setting name="aggressiveLazyLoading" value="false"/>
        <setting name="localCacheScope" value="STATEMENT"/>
        <setting name="multipleResultSetsEnabled" value="true"/>
        <setting name="useColumnLabel" value="true"/>
        <setting name="defaultStatementTimeout" value="25000"/>
        <setting name="mapUnderscoreToCamelCase" value="true"/>
        <!-- 是否使用插入数据后自增主键的值，需要配合keyProperty使用 -->
        <!-- <setting name="useGeneratedKeys" value="true"/> -->
        <setting name="logPrefix" value="dao."/>
        <setting name="logImpl" value="STDOUT_LOGGING"/>
    </settings>
</configuration>