<?xml version="1.0" encoding="UTF-8"?>

<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:tx="http://www.springframework.org/schema/tx" xmlns:context="http://www.springframework.org/schema/context"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
    http://www.springframework.org/schema/tx
    http://www.springframework.org/schema/tx/spring-tx.xsd
    http://www.springframework.org/schema/context
    http://www.springframework.org/schema/context/spring-context.xsd http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd">

    <!-- 使用Druid连接池 -->
    <bean id="jdbcDataSource" class="com.alibaba.druid.pool.DruidDataSource"
          init-method="init" destroy-method="close">
        <!-- 基本属性 url、user、password -->
        <property name="url" value="${service-dsrp-cdc.jdbc.url}"/>
        <property name="username" value="${service-dsrp-cdc.jdbc.username}"/>
        <property name="password" value="${service-dsrp-cdc.jdbc.password}"/>
        <property name="driverClassName" value="${service-dsrp-cdc.jdbc.driverClassName}"/>

        <!-- 配置初始化大小、最小、最大 -->
        <property name="initialSize" value="${common.druid.initialSize}"/>
        <property name="minIdle" value="${common.druid.minIdle}"/>
        <property name="maxActive" value="${common.druid.maxActive}"/>

        <!-- 配置获取连接等待超时的时间 -->
        <property name="maxWait" value="10000"/>

        <!-- 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒 -->
        <property name="timeBetweenEvictionRunsMillis" value="6000"/>

        <!-- 配置一个连接在池中最小生存的时间，单位是毫秒 -->
        <property name="minEvictableIdleTimeMillis" value="300000"/>

        <property name="validationQuery" value="SELECT 'x'"/>
        <property name="testWhileIdle" value="true"/>
        <property name="testOnBorrow" value="true"/>
        <property name="testOnReturn" value="true"/>

        <!-- 打开PSCache，并且指定每个连接上PSCache的大小 如果用Oracle，则把poolPreparedStatements配置为true，
        mysql可以配置为false。分库分表较多的数据库，建议配置为false。
         -->
        <property name="poolPreparedStatements" value="false"/>
        <property name="maxPoolPreparedStatementPerConnectionSize"
                  value="20"/>
        <!-- 配置监控统计拦截的filters，去掉后监控界面sql无法统计 -->
        <property name="filters" value="stat"/>
    </bean>
    <!-- 使用Druid连接池 -->
    <bean id="sqlSessionFactory" class="com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean">
        <property name="dataSource" ref="jdbcDataSource"/>
        <!-- 配置扫描entity的包路径 -->
        <property name="typeAliasesPackage" value="com.yyigou.dsrp.cdc.dao"/>
        <property name="configLocation" value="classpath:mybatis/mybatis-config.xml"/>
        <!-- 配置mybatis配置文件的位置 -->
        <property name="mapperLocations" value="classpath:mapper/*.xml"/>
        <property name="globalConfig">
            <bean class="com.baomidou.mybatisplus.core.config.GlobalConfig">
                <property name="metaObjectHandler">
                    <bean class="com.yyigou.ddc.common.persistent.mybatisplus.handler.YygMetaObjectHandler"/>
                </property>
            </bean>
        </property>
        <property name="plugins">
            <array>
                <!-- uap插件配置-->
                <bean class="com.yyigou.ddc.services.ddc.uap.util.UapInterceptor"/>
                <!-- mp插件配置-->
                <bean class="com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor">
                    <property name="interceptors">
                        <list>
                            <bean id="paginationInnerInterceptor"
                                  class="com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor">
                                <!-- 对于单一数据库类型来说,都建议配置该值,避免每次分页都去抓取数据库类型 -->
                                <constructor-arg name="dbType" value="MYSQL"/>
                            </bean>
                        </list>
                    </property>
                </bean>
                <!--   pagehelper 5插件配置-->
                <bean class="com.github.pagehelper.PageInterceptor">
                    <!-- 这里的几个配置主要演示如何使用，如果不理解，一定要去掉下面的配置 -->
                    <property name="properties">
                        <value>
                            helperDialect=mysql
                            reasonable=true
                            supportMethodsArguments=true
                            params=count=countSql
                            autoRuntimeDialect=true
                        </value>
                    </property>
                </bean>

                <!--   客户档案NC同步拦截器 -->
                <bean class="com.yyigou.dsrp.cdc.dao.v2.customer.CustomerNcDAOInterceptor"/>
            </array>
        </property>
    </bean>
    <!-- DAO接口所在包名，Spring会自动查找其下的 -->
    <bean class="org.mybatis.spring.mapper.MapperScannerConfigurer">
        <property name="basePackage" value="com.yyigou.dsrp.cdc.dao"/>
        <property name="sqlSessionFactoryBeanName" value="sqlSessionFactory"/>
    </bean>

    <!--数据库事务传播-->
    <bean name="transactionManager"
          class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
        <property name="dataSource" ref="jdbcDataSource"/>
    </bean>
    <tx:advice id="txAdvice">
        <tx:attributes>
            <!--开启事务-->
            <tx:method name="create*" propagation="REQUIRED" read-only="false"/>
            <tx:method name="insert*" propagation="REQUIRED" read-only="false"/>
            <tx:method name="update*" propagation="REQUIRED" read-only="false"/>
            <tx:method name="delete*" propagation="REQUIRED" read-only="false"/>
            <tx:method name="save*" propagation="REQUIRED" read-only="false"/>
            <tx:method name="submit*" propagation="REQUIRED" read-only="false"/>
            <tx:method name="add*" propagation="REQUIRED" read-only="false"/>
            <tx:method name="upsert*" propagation="REQUIRED" read-only="false"/>
            <tx:method name="remove*" propagation="REQUIRED" read-only="false"/>

            <!--只读事务-->
            <tx:method name="*" propagation="SUPPORTS" read-only="true"/>
        </tx:attributes>
    </tx:advice>
    <tx:annotation-driven transaction-manager="transactionManager"/>
    <aop:config proxy-target-class="true">
        <!--配置事务切点 -->
        <aop:pointcut id="managersAOP"
                      expression="execution(public * com.yyigou.dsrp.cdc.service..*.*(..))"/>
        <aop:advisor pointcut-ref="managersAOP" advice-ref="txAdvice"/>
    </aop:config>

    <!-- sql审计 -->
    <context:component-scan base-package="com.yyigou.ddc.services.sql.analysis.util"/>
    <!-- 拦截器使用的bean配置 -->
    <import resource="classpath*:applicationContext-sqlAnalysis.xml"/>
</beans>
