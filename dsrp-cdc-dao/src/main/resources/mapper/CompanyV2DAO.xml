<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yyigou.dsrp.cdc.dao.v2.company.CompanyV2DAO">
    <update id="batchUpdateForDA">
        <foreach collection="list" item="item" separator=";">
            UPDATE bdc_company
            SET
            <trim suffixOverrides=",">
                <if test="item.companyName != null and item.companyName != ''">
                    company_name = #{item.companyName},
                </if>
                <if test="item.unifiedSocialCode != null and item.unifiedSocialCode != ''">
                    unified_social_code = #{item.unifiedSocialCode},
                </if>

                partnership = #{item.partnership},
                institutional_type = #{item.institutionalType},
                hospital_type = #{item.hospitalType},
                hospital_class = #{item.hospitalClass},
                legal_person = #{item.legalPerson},
                region_code = #{item.regionCode},
--                 is_associated_enterprise = #{item.isAssociatedEnterprise},
--                 associated_org_code = #{item.associatedOrgCode},
--                 associated_org_no = #{item.associatedOrgNo},
--                 associated_org_name = #{item.associatedOrgName},

                operate_no = #{item.operateNo},
                operate_name = #{item.operateName},
                operate_time = #{item.operateTime},
            </trim>
            WHERE
            enterprise_no = #{item.enterpriseNo}
            AND
            company_no = #{item.companyNo}
        </foreach>
    </update>
</mapper>