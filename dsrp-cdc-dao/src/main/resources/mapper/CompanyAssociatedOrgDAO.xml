<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yyigou.dsrp.cdc.dao.v2.company.CompanyAssociatedOrgDAO">

    <resultMap id="BaseResultMap" type="com.yyigou.dsrp.cdc.dao.v2.company.entity.CompanyAssociatedOrg">
            <id property="id" column="id" />
            <result property="enterpriseNo" column="enterprise_no" />
            <result property="companyNo" column="company_no" />
            <result property="sourceType" column="source_type" />
            <result property="sourceCode" column="source_code" />
            <result property="associatedOrgNo" column="associated_org_no" />
            <result property="associatedOrgName" column="associated_org_code" />
            <result property="associatedOrgName" column="associated_org_name" />
            <result property="status" column="status" />
            <result property="deleted" column="deleted" />
            <result property="createNo" column="create_no" />
            <result property="createName" column="create_name" />
            <result property="createTime" column="create_time" />
            <result property="modifyNo" column="modify_no" />
            <result property="modifyName" column="modify_name" />
            <result property="modifyTime" column="modify_time" />
            <result property="opTimestamp" column="op_timestamp" />
    </resultMap>

    <sql id="Base_Column_List">
        id,enterprise_no,company_no,source_type,source_code,associated_org_no,associated_org_code,associated_org_name,
        status,deleted,create_no,create_name,create_time,
        modify_no,modify_name,modify_time,op_timestamp
    </sql>
</mapper>
