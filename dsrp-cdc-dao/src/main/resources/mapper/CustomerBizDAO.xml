<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yyigou.dsrp.cdc.dao.v2.customer.CustomerBizDAO">
    <update id="batchUpdateForDA">
        <foreach collection="list" item="item" separator=";">
            UPDATE bdc_customer_biz
            SET
            <trim suffixOverrides=",">
                credit_amount = #{item.creditAmount},
                coop_start_time = #{item.coopStartTime},
                coop_end_time = #{item.coopEndTime},
                owner_company = #{item.ownerCompany},
                operate_no = #{item.operateNo},
                operate_name = #{item.operateName},
                operate_time = #{item.operateTime},
            </trim>
            WHERE
            enterprise_no = #{item.enterpriseNo}
            AND
            id = #{item.id}
            AND
            use_org_no = #{item.useOrgNo}
            AND
            customer_code = #{item.customerCode}
            AND
            deleted = 0
        </foreach>
    </update>
</mapper>