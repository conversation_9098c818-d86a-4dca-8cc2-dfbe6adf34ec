<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yyigou.dsrp.cdc.dao.v2.supplier.SupplierBaseDAO">
    <update id="batchUpdateForDA">
        <foreach collection="list" item="item" separator=";">
            UPDATE bdc_supplier_base
            SET
            <trim suffixOverrides=",">
                control_status = #{item.controlStatus},
            </trim>
            WHERE
            enterprise_no = #{item.enterpriseNo}
            AND
            id = #{item.id}
            AND
            use_org_no = #{item.useOrgNo}
            AND
            supplier_code = #{item.supplierCode}
            AND
            deleted = 0
        </foreach>
    </update>
</mapper>