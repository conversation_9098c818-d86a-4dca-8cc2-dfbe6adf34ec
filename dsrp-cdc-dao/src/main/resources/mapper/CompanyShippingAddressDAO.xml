<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yyigou.dsrp.cdc.dao.company.CompanyLinkmanDAO">

    <resultMap id="BaseResultMap" type="com.yyigou.dsrp.cdc.dao.company.entity.CompanyLinkman">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="enterpriseNo" column="enterprise_no" jdbcType="VARCHAR"/>
            <result property="companyNo" column="company_no" jdbcType="VARCHAR"/>
            <result property="linkman" column="linkman" jdbcType="VARCHAR"/>
            <result property="position" column="position" jdbcType="VARCHAR"/>
            <result property="mobilePhone" column="mobile_phone" jdbcType="VARCHAR"/>
            <result property="fixedPhone" column="fixed_phone" jdbcType="VARCHAR"/>
            <result property="sex" column="sex" jdbcType="VARCHAR"/>
            <result property="email" column="email" jdbcType="VARCHAR"/>
            <result property="qq" column="qq" jdbcType="VARCHAR"/>
            <result property="wx" column="wx" jdbcType="VARCHAR"/>
            <result property="status" column="status" jdbcType="TINYINT"/>
            <result property="deleted" column="deleted" jdbcType="TINYINT"/>
            <result property="createNo" column="create_no" jdbcType="VARCHAR"/>
            <result property="createName" column="create_name" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="VARCHAR"/>
            <result property="operateNo" column="operate_no" jdbcType="VARCHAR"/>
            <result property="operateName" column="operate_name" jdbcType="VARCHAR"/>
            <result property="operateTime" column="operate_time" jdbcType="VARCHAR"/>
            <result property="opTimestamp" column="op_timestamp" jdbcType="TIMESTAMP"/>
            <result property="opRevsion" column="op_revsion" jdbcType="INTEGER"/>
            <result property="opType" column="op_type" jdbcType="CHAR"/>
            <result property="isDefault" column="is_default" jdbcType="TINYINT"/>
            <result property="linkmanType" column="linkman_type" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,enterprise_no,company_no,
        linkman,position,mobile_phone,
        fixed_phone,sex,email,
        qq,wx,status,
        deleted,create_no,create_name,
        create_time,operate_no,operate_name,
        operate_time,op_timestamp,op_revsion,
        op_type,is_default,linkman_type
    </sql>
</mapper>
