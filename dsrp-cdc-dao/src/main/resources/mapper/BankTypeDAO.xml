<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yyigou.dsrp.cdc.dao.common.BankTypeDAO">

    <resultMap id="BaseResultMap" type="com.yyigou.dsrp.cdc.dao.common.entity.BankType">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="bankCode" column="bank_code" jdbcType="VARCHAR"/>
            <result property="bankName" column="bank_name" jdbcType="VARCHAR"/>
            <result property="bankLogo" column="bank_logo" jdbcType="VARCHAR"/>
            <result property="country" column="country" jdbcType="VARCHAR"/>
            <result property="deleted" column="deleted" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,bank_code,bank_name,
        bank_logo,country,deleted
    </sql>
</mapper>
