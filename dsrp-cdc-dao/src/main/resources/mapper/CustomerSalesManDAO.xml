<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yyigou.dsrp.cdc.dao.customer.CustomerSalesManDAO">

    <resultMap id="BaseResultMap" type="com.yyigou.dsrp.cdc.dao.customer.entity.CustomerSalesMan">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="customerNo" column="customer_no" jdbcType="VARCHAR"/>
            <result property="orgNo" column="org_no" jdbcType="VARCHAR"/>
            <result property="orgName" column="org_name" jdbcType="VARCHAR"/>
            <result property="salesManNo" column="sales_man_no" jdbcType="VARCHAR"/>
            <result property="salesManName" column="sales_man_name" jdbcType="VARCHAR"/>
            <result property="isDefault" column="is_default" jdbcType="INTEGER"/>
            <result property="orderSpecialist" column="order_specialist" jdbcType="INTEGER"/>
            <result property="operateNo" column="operate_no" jdbcType="VARCHAR"/>
            <result property="operateName" column="operate_name" jdbcType="VARCHAR"/>
            <result property="operateTime" column="operate_time" jdbcType="VARCHAR"/>
            <result property="post" column="post" jdbcType="VARCHAR"/>
            <result property="areaNo" column="area_no" jdbcType="VARCHAR"/>
            <result property="areaCode" column="area_code" jdbcType="VARCHAR"/>
            <result property="areaName" column="area_name" jdbcType="VARCHAR"/>
            <result property="deleted" column="deleted" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,customer_no,org_no,
        org_name,sales_man_no,sales_man_name,
        is_default,order_specialist,operate_no,
        operate_name,operate_time,post,
        area_no,area_code,area_name,
        deleted
    </sql>
</mapper>
