<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yyigou.dsrp.cdc.dao.supplier.SupplierOrderManDAO">

    <resultMap id="BaseResultMap" type="com.yyigou.dsrp.cdc.dao.supplier.entity.SupplierOrderMan">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="supplierNo" column="supplier_no" jdbcType="VARCHAR"/>
            <result property="orgNo" column="org_no" jdbcType="VARCHAR"/>
            <result property="orgName" column="org_name" jdbcType="VARCHAR"/>
            <result property="orderManNo" column="order_man_no" jdbcType="VARCHAR"/>
            <result property="orderManName" column="order_man_name" jdbcType="VARCHAR"/>
            <result property="isDefault" column="is_default" jdbcType="INTEGER"/>
            <result property="orderSpecialist" column="order_specialist" jdbcType="INTEGER"/>
            <result property="operateNo" column="operate_no" jdbcType="VARCHAR"/>
            <result property="operateName" column="operate_name" jdbcType="VARCHAR"/>
            <result property="operateTime" column="operate_time" jdbcType="VARCHAR"/>
            <result property="deleted" column="deleted" jdbcType="INTEGER"/>
            <result property="post" column="post" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,supplier_no,org_no,
        org_name,order_man_no,order_man_name,
        is_default,order_specialist,operate_no,
        operate_name,operate_time,deleted,
        post
    </sql>
</mapper>
