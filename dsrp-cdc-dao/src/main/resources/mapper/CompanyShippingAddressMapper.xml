<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yyigou.dsrp.cdc.dao.company.CompanyShippingAddressDAO">

    <resultMap id="BaseResultMap" type="com.yyigou.dsrp.cdc.dao.company.entity.CompanyShippingAddress">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="enterpriseNo" column="enterprise_no" jdbcType="VARCHAR"/>
            <result property="companyNo" column="company_no" jdbcType="VARCHAR"/>
            <result property="receiveUser" column="receive_user" jdbcType="VARCHAR"/>
            <result property="receivePhone" column="receive_phone" jdbcType="VARCHAR"/>
            <result property="regionCode" column="region_code" jdbcType="VARCHAR"/>
            <result property="regionName" column="region_name" jdbcType="VARCHAR"/>
            <result property="receiveAddr" column="receive_addr" jdbcType="VARCHAR"/>
            <result property="email" column="email" jdbcType="VARCHAR"/>
            <result property="isDefault" column="is_default" jdbcType="TINYINT"/>
            <result property="status" column="status" jdbcType="TINYINT"/>
            <result property="addressType" column="address_type" jdbcType="VARCHAR"/>
            <result property="createNo" column="create_no" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="VARCHAR"/>
            <result property="operateNo" column="operate_no" jdbcType="VARCHAR"/>
            <result property="operateName" column="operate_name" jdbcType="VARCHAR"/>
            <result property="operateTime" column="operate_time" jdbcType="VARCHAR"/>
            <result property="opTimestamp" column="op_timestamp" jdbcType="TIMESTAMP"/>
            <result property="opRevsion" column="op_revsion" jdbcType="INTEGER"/>
            <result property="opType" column="op_type" jdbcType="CHAR"/>
            <result property="linkaddType" column="linkadd_type" jdbcType="VARCHAR"/>
            <result property="deleted" column="deleted" jdbcType="TINYINT"/>
            <result property="tcSyncFlag" column="tc_sync_flag" jdbcType="TINYINT"/>
            <result property="tcSyncResult" column="tc_sync_result" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,enterprise_no,company_no,
        receive_user,receive_phone,region_code,
        region_name,receive_addr,email,
        is_default,status,address_type,
        create_no,create_time,operate_no,
        operate_name,operate_time,op_timestamp,
        op_revsion,op_type,linkadd_type,
        deleted,tc_sync_flag,tc_sync_result
    </sql>
</mapper>
