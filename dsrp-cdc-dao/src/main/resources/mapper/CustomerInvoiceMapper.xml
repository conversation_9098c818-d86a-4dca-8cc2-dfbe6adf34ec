<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yyigou.dsrp.cdc.dao.customer.CustomerInvoiceDAO">

    <resultMap id="BaseResultMap" type="com.yyigou.dsrp.cdc.dao.customer.entity.CustomerInvoice">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="enterpriseNo" column="enterprise_no" jdbcType="VARCHAR"/>
            <result property="customerNo" column="customer_no" jdbcType="VARCHAR"/>
            <result property="type" column="type" jdbcType="TINYINT"/>
            <result property="taxNo" column="tax_no" jdbcType="VARCHAR"/>
            <result property="invoiceTitle" column="invoice_title" jdbcType="VARCHAR"/>
            <result property="phone" column="phone" jdbcType="VARCHAR"/>
            <result property="invoicePhone" column="invoice_phone" jdbcType="VARCHAR"/>
            <result property="email" column="email" jdbcType="VARCHAR"/>
            <result property="regionCode" column="region_code" jdbcType="VARCHAR"/>
            <result property="regionName" column="region_name" jdbcType="VARCHAR"/>
            <result property="address" column="address" jdbcType="VARCHAR"/>
            <result property="bankDeposit" column="bank_deposit" jdbcType="VARCHAR"/>
            <result property="bankAccount" column="bank_account" jdbcType="VARCHAR"/>
            <result property="status" column="status" jdbcType="VARCHAR"/>
            <result property="isDefault" column="is_default" jdbcType="TINYINT"/>
            <result property="operateNo" column="operate_no" jdbcType="VARCHAR"/>
            <result property="operateName" column="operate_name" jdbcType="VARCHAR"/>
            <result property="operateTime" column="operate_time" jdbcType="VARCHAR"/>
            <result property="opTimestamp" column="op_timestamp" jdbcType="TIMESTAMP"/>
            <result property="opRevsion" column="op_revsion" jdbcType="INTEGER"/>
            <result property="opType" column="op_type" jdbcType="CHAR"/>
            <result property="addregionCode" column="addregion_code" jdbcType="VARCHAR"/>
            <result property="requirement" column="requirement" jdbcType="VARCHAR"/>
            <result property="outSystemId" column="out_system_id" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,enterprise_no,customer_no,
        type,tax_no,invoice_title,
        phone,invoice_phone,email,
        region_code,region_name,address,
        bank_deposit,bank_account,status,
        is_default,operate_no,operate_name,
        operate_time,op_timestamp,op_revsion,
        op_type,addregion_code,requirement,
        out_system_id
    </sql>
</mapper>
