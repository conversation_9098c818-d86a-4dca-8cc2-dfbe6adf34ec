<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yyigou.dsrp.cdc.dao.supplier.SupplierCategoryDAO">

    <resultMap id="BaseResultMap" type="com.yyigou.dsrp.cdc.dao.supplier.entity.SupplierCategory">
            <id property="no" column="no" jdbcType="VARCHAR"/>
            <result property="enterpriseNo" column="enterprise_no" jdbcType="VARCHAR"/>
            <result property="parentNo" column="parent_no" jdbcType="VARCHAR"/>
            <result property="mnemonicCode" column="mnemonic_code" jdbcType="VARCHAR"/>
            <result property="categoryCode" column="category_code" jdbcType="VARCHAR"/>
            <result property="categoryName" column="category_name" jdbcType="VARCHAR"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="status" column="status" jdbcType="OTHER"/>
            <result property="sortNum" column="sort_num" jdbcType="TINYINT"/>
            <result property="deleted" column="deleted" jdbcType="TINYINT"/>
            <result property="createNo" column="create_no" jdbcType="VARCHAR"/>
            <result property="createName" column="create_name" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="VARCHAR"/>
            <result property="operateNo" column="operate_no" jdbcType="VARCHAR"/>
            <result property="operateName" column="operate_name" jdbcType="VARCHAR"/>
            <result property="operateTime" column="operate_time" jdbcType="VARCHAR"/>
            <result property="opTimestamp" column="op_timestamp" jdbcType="TIMESTAMP"/>
            <result property="opRevsion" column="op_revsion" jdbcType="INTEGER"/>
            <result property="opType" column="op_type" jdbcType="CHAR"/>
            <result property="ysSyncFlag" column="ys_sync_flag" jdbcType="VARCHAR"/>
            <result property="groupNo" column="group_no" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        no,enterprise_no,parent_no,
        mnemonic_code,category_code,category_name,
        remark,status,sort_num,
        deleted,create_no,create_name,
        create_time,operate_no,operate_name,
        operate_time,op_timestamp,op_revsion,
        op_type,ys_sync_flag,group_no
    </sql>
</mapper>
