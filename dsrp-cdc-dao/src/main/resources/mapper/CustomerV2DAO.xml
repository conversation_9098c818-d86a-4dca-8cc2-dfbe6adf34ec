<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yyigou.dsrp.cdc.dao.v2.customer.CustomerV2DAO">
    <select id="selectManageView" resultType="com.yyigou.dsrp.cdc.dao.v2.customer.entity.CustomerV2WithBiz">
        SELECT
            a.*,
            c.id bizId,
            c.use_org_no
        FROM
            bdc_customer_base b JOIN bdc_customer_biz c JOIN bdc_customer a
        ON
            b.use_org_no = c.use_org_no
            AND b.customer_code = c.customer_code
            AND b.enterprise_no = c.enterprise_no

            AND b.enterprise_no = a.enterprise_no
            AND b.manage_org_no = a.manage_org_no
            AND b.customer_code = a.customer_code
        WHERE
          a.enterprise_no=#{enterpriseNo}
          AND b.manage_org_no = b.use_org_no
          AND a.deleted=0
          AND b.deleted=0
          AND c.deleted=0
        <if test="idList != null and idList.size()>0">
            and c.customer_no in
            <foreach collection="idList" item="no" separator="," open="(" close=")">
                #{no}
            </foreach>
        </if>
    </select>

    <select id="selectUseView" resultType="com.yyigou.dsrp.cdc.dao.v2.customer.entity.CustomerV2WithBiz">
        SELECT
            a.*,
            c.id bizId,
            c.use_org_no
        FROM
            bdc_customer_base b JOIN bdc_customer_biz c JOIN bdc_customer a
        ON
             b.use_org_no = c.use_org_no
            AND b.customer_code = c.customer_code
            AND b.enterprise_no = c.enterprise_no

            AND b.enterprise_no = a.enterprise_no
            AND b.manage_org_no = a.manage_org_no
            AND b.customer_code = a.customer_code
        WHERE
          a.enterprise_no=#{enterpriseNo}
          AND a.deleted=0
          AND b.deleted=0
          AND c.deleted=0
        <if test="idList != null and idList.size()>0">
            and c.customer_no in
            <foreach collection="idList" item="no" separator="," open="(" close=")">
                #{no}
            </foreach>
        </if>
    </select>

    <select id="selectDistinctCustomerCodeByUseOrgNoList" resultType="java.lang.String">
        select distinct customer_code
        from  bdc_customer_base
        where enterprise_no = #{enterpriseNo}
        and deleted = 0
        <if test="useOrgNoList != null and useOrgNoList.size > 0">
            and use_org_no in
            <foreach collection="useOrgNoList" item="useOrgNo" open="(" separator="," close=")">
                #{useOrgNo}
            </foreach>
        </if>
    </select>

    <select id="selectReverseUseViewNoAuthZ" resultType="com.yyigou.dsrp.cdc.dao.v2.customer.entity.CustomerV2WithBase">
        select a.*, t.id baseId, t.use_org_no
        from bdc_customer a
        left join
        (select * from bdc_customer_base where enterprise_no=#{enterpriseNo} and use_org_no = #{useOrgNo} and deleted=0) t
        on
        a.enterprise_no = t.enterprise_no
        and a.customer_code = t.customer_code
        where
        a.enterprise_no=#{enterpriseNo}
        AND a.deleted=0
        <if test="customerNameKeywords != null and customerNameKeywords != ''">
            and a.customer_name LIKE CONCAT('%',#{customerNameKeywords},'%')
        </if>
    </select>

    <select id="findListPageForTenant" resultType="com.yyigou.dsrp.cdc.dao.v2.customer.entity.CustomerV2WithBase">
        select basic.*,
        base.`id` baseId,
        base.`use_org_no`,
        base.`control_status`,
        base.customer_no
        from bdc_customer_base base
        left join
        bdc_customer basic
        on base.enterprise_no = basic.enterprise_no
        and base.manage_org_no = basic.manage_org_no
        and base.customer_code = basic.customer_code
        where base.deleted = 0
        and basic.deleted = 0
        and base.manage_org_no = base.use_org_no
        and base.enterprise_no = #{enterpriseNo}
        and basic.business_flag = 1
        <if test="customerCodeSet != null and customerCodeSet.size > 0">
            and base.customer_code in
            <foreach collection="customerCodeSet" item="customerCode" open="(" separator="," close=")">
                #{customerCode}
            </foreach>
        </if>
    </select>

    <select id="getPendingCount" resultType="java.lang.Long">
        SELECT count(1)
        FROM bdc_customer_base a
                 JOIN bdc_customer b
                      ON a.enterprise_no = b.enterprise_no AND a.manage_org_no = b.manage_org_no AND
                         a.customer_code = b.customer_code
        WHERE a.enterprise_no = #{enterpriseNo}
          AND a.deleted = #{deleted}
          AND b.business_flag = #{businessFlag}
          AND b.deleted = #{deleted}
    </select>

    <select id="queryStandardInfo" resultType="com.yyigou.dsrp.cdc.dao.v2.customer.entity.CustomerV2">
        SELECT
        a.*
        FROM
        bdc_customer_base b JOIN bdc_customer_biz c JOIN bdc_customer a
        ON
        b.use_org_no = c.use_org_no
        AND b.customer_code = c.customer_code
        AND b.enterprise_no = c.enterprise_no

        AND b.enterprise_no = a.enterprise_no
        AND b.manage_org_no = a.manage_org_no
        AND b.customer_code = a.customer_code
        WHERE
        a.enterprise_no=#{enterpriseNo}
        AND a.deleted=0
        AND b.deleted=0
        AND c.deleted=0
        <if test="manageOrgNo != null and manageOrgNo != ''">
            AND a.manage_org_no = #{manageOrgNo}
        </if>
        <if test="useOrgNo != null and useOrgNo != ''">
            AND c.use_org_no = #{useOrgNo}
        </if>
        <if test="customerNo != null and customerNo != ''">
            AND c.customer_no = #{customerNo}
        </if>
        <if test="customerCode != null and customerCode != ''">
            AND c.customer_code = #{customerCode}
        </if>
        <if test="customerName != null and customerName != ''">
            and a.customer_name = #{customerName}
        </if>
        <if test="unifiedSocialCode != null and unifiedSocialCode != ''">
            and a.unified_social_code = #{unifiedSocialCode}
        </if>
        <if test="customerKeywords != null and customerKeywords != ''">
            AND (
            a.customer_name LIKE CONCAT('%',#{customerKeywords},'%')
            OR a.customer_code LIKE CONCAT('%',#{customerKeywords},'%')
            )
        </if>
        <if test="customerNoList != null and customerNoList.size()>0">
            and c.customer_no in
            <foreach collection="customerNoList" item="no" separator="," open="(" close=")">
                #{no}
            </foreach>
        </if>
        <if test="customerCodeList != null and customerCodeList.size()>0">
            and a.customer_code in
            <foreach collection="customerCodeList" item="no" separator="," open="(" close=")">
                #{no}
            </foreach>
        </if>
        <if test="customerNameList != null and customerNameList.size()>0">
            and a.customer_name in
            <foreach collection="customerNameList" item="no" separator="," open="(" close=")">
                #{no}
            </foreach>
        </if>
        <if test="controlStatusList != null and controlStatusList.size()>0">
            and b.control_status in
            <foreach collection="controlStatusList" item="no" separator="," open="(" close=")">
                #{no}
            </foreach>
        </if>
        <if test="companyNoList != null and companyNoList.size()>0">
            and c.company_no in
            <foreach collection="companyNoList" item="no" separator="," open="(" close=")">
                #{no}
            </foreach>
        </if>
        <if test="customerCategoryNoList != null and customerCategoryNoList.size()>0">
            and c.customer_category_no in
            <foreach collection="customerCategoryNoList" item="no" separator="," open="(" close=")">
                #{no}
            </foreach>
        </if>
        <if test="cooperationModeList != null and cooperationModeList.size()>0">
            and c.cooperation_mode in
            <foreach collection="cooperationModeList" item="no" separator="," open="(" close=")">
                #{no}
            </foreach>
        </if>
        <if test="priceCategoryCodeList != null and priceCategoryCodeList.size()>0">
            and c.price_category_code in
            <foreach collection="priceCategoryCodeList" item="no" separator="," open="(" close=")">
                #{no}
            </foreach>
        </if>
        <if test="linkManAndPhoneKeywords != null and linkManAndPhoneKeywords != ''">
            and (
            c.link_man LIKE CONCAT('%',#{linkManAndPhoneKeywords},'%')
            OR c.link_phone LIKE CONCAT('%',#{linkManAndPhoneKeywords},'%')
            )
        </if>
    </select>

    <select id="queryWithBizInfo" resultType="com.yyigou.dsrp.cdc.dao.v2.customer.entity.CustomerV2WithBiz">
        SELECT
        a.*,
        c.id bizId,
        c.use_org_no
        FROM
        bdc_customer_base b JOIN bdc_customer_biz c JOIN bdc_customer a
        ON
        b.use_org_no = c.use_org_no
        AND b.customer_code = c.customer_code
        AND b.enterprise_no = c.enterprise_no

        AND b.enterprise_no = a.enterprise_no
        AND b.manage_org_no = a.manage_org_no
        AND b.customer_code = a.customer_code
        WHERE
        a.enterprise_no=#{enterpriseNo}
        AND a.deleted=0
        AND b.deleted=0
        AND c.deleted=0
        <if test="manageOrgNo != null and manageOrgNo != ''">
            AND a.manage_org_no = #{manageOrgNo}
        </if>
        <if test="useOrgNo != null and useOrgNo != ''">
            AND c.use_org_no = #{useOrgNo}
        </if>
        <if test="customerNo != null and customerNo != ''">
            AND c.customer_no = #{customerNo}
        </if>
        <if test="customerCode != null and customerCode != ''">
            AND c.customer_code = #{customerCode}
        </if>
        <if test="customerName != null and customerName != ''">
            and a.customer_name = #{customerName}
        </if>
        <if test="unifiedSocialCode != null and unifiedSocialCode != ''">
            and a.unified_social_code = #{unifiedSocialCode}
        </if>
        <if test="customerKeywords != null and customerKeywords != ''">
            AND (
            a.customer_name LIKE CONCAT('%',#{customerKeywords},'%')
            OR a.customer_code LIKE CONCAT('%',#{customerKeywords},'%')
            )
        </if>
        <if test="customerNoList != null and customerNoList.size()>0">
            and c.customer_no in
            <foreach collection="customerNoList" item="no" separator="," open="(" close=")">
                #{no}
            </foreach>
        </if>
        <if test="customerCodeList != null and customerCodeList.size()>0">
            and a.customer_code in
            <foreach collection="customerCodeList" item="no" separator="," open="(" close=")">
                #{no}
            </foreach>
        </if>
        <if test="customerNameList != null and customerNameList.size()>0">
            and a.customer_name in
            <foreach collection="customerNameList" item="no" separator="," open="(" close=")">
                #{no}
            </foreach>
        </if>
        <if test="controlStatusList != null and controlStatusList.size()>0">
            and b.control_status in
            <foreach collection="controlStatusList" item="no" separator="," open="(" close=")">
                #{no}
            </foreach>
        </if>
        <if test="companyNoList != null and companyNoList.size()>0">
            and c.company_no in
            <foreach collection="companyNoList" item="no" separator="," open="(" close=")">
                #{no}
            </foreach>
        </if>
        <if test="customerCategoryNoList != null and customerCategoryNoList.size()>0">
            and c.customer_category_no in
            <foreach collection="customerCategoryNoList" item="no" separator="," open="(" close=")">
                #{no}
            </foreach>
        </if>
        <if test="cooperationModeList != null and cooperationModeList.size()>0">
            and c.cooperation_mode in
            <foreach collection="cooperationModeList" item="no" separator="," open="(" close=")">
                #{no}
            </foreach>
        </if>
        <if test="priceCategoryCodeList != null and priceCategoryCodeList.size()>0">
            and c.price_category_code in
            <foreach collection="priceCategoryCodeList" item="no" separator="," open="(" close=")">
                #{no}
            </foreach>
        </if>
        <if test="linkManAndPhoneKeywords != null and linkManAndPhoneKeywords != ''">
            and (
            c.link_man LIKE CONCAT('%',#{linkManAndPhoneKeywords},'%')
            OR c.link_phone LIKE CONCAT('%',#{linkManAndPhoneKeywords},'%')
            )
        </if>
        <if test="associatedOrgNoList != null and associatedOrgNoList.size() > 0">
            and b.customer_code in (
                select source_code from bdc_company_associated_org
                where enterprise_no = #{enterpriseNo}
                and source_type = 2
                and deleted = 0
                and associated_org_no in
                <foreach collection="associatedOrgNoList" item="aOrgNo" separator="," open="(" close=")">
                    #{aOrgNo}
                </foreach>
            )
        </if>
    </select>

    <select id="selectCustomerBizByCodeOrgPair" resultType="com.yyigou.dsrp.cdc.dao.v2.customer.entity.CustomerV2WithBiz">
        SELECT
        a.*,
        c.id bizId,
        c.use_org_no
        FROM
        bdc_customer_base b JOIN bdc_customer_biz c JOIN bdc_customer a
        ON
        b.use_org_no = c.use_org_no
        AND b.customer_code = c.customer_code
        AND b.enterprise_no = c.enterprise_no

        AND b.enterprise_no = a.enterprise_no
        AND b.manage_org_no = a.manage_org_no
        AND b.customer_code = a.customer_code
        WHERE
        a.enterprise_no=#{enterpriseNo}
        AND a.deleted=0
        AND b.deleted=0
        AND c.deleted=0
        <if test="customerCodeOrgPairList != null and customerCodeOrgPairList != ''">
            and (c.use_org_no, c.customer_code) in
            <foreach collection="customerCodeOrgPairList" item="pair" separator="," open="(" close=")">
                (#{pair.useOrgNo}, #{pair.customerCode})
            </foreach>
        </if>
    </select>

    <update id="batchUpdateForDA">
        <foreach collection="list" item="item" separator=";">
            UPDATE bdc_customer
            SET
            <trim suffixOverrides=",">
                customer_name = #{item.customerName},
                customer_name_en = #{item.customerNameEn},
                customer_category_no = #{item.customerCategoryNo},
                mnemonic_code = #{item.mnemonicCode},
                remark = #{item.remark},
                operate_no = #{item.operateNo},
                operate_name = #{item.operateName},
                operate_time = #{item.operateTime},
            </trim>
            WHERE
            enterprise_no = #{item.enterpriseNo}
            AND
            manage_org_no = #{item.manageOrgNo}
            AND
            customer_no = #{item.customerNo}
            AND
            deleted = 0
        </foreach>
    </update>

    <select id="findListPageByFormal" resultType="com.yyigou.dsrp.cdc.dao.v2.customer.entity.CustomerV2WithBiz">
        SELECT
        a.*,
        c.id bizId,
        c.use_org_no
        FROM
        bdc_customer_base b JOIN bdc_customer_biz c JOIN bdc_customer a JOIN bdc_customer_category d
        ON
        b.use_org_no = c.use_org_no
        AND b.customer_code = c.customer_code
        AND b.enterprise_no = c.enterprise_no

        AND b.enterprise_no = a.enterprise_no
        AND b.manage_org_no = a.manage_org_no
        AND b.customer_code = a.customer_code

        AND a.enterprise_no = d.enterprise_no
        AND a.customer_category_no = d.no
        WHERE
        a.enterprise_no=#{enterpriseNo}
        AND c.use_org_no = #{useOrgNo}
        AND a.business_flag=1
        AND a.deleted=0
        AND b.deleted=0
        AND c.deleted=0
        AND d.deleted=0
        <if test="keywords != null and keywords!=''">
            and (
            a.customer_name like CONCAT('%', #{keywords},'%') or
            a.customer_code like CONCAT('%', #{keywords},'%')
            )
        </if>

        <if test="noCustomerNos != null and noCustomerNos.size()>0">
            AND c.customer_no not in
            <foreach collection="noCustomerNos" item="no" separator="," open="(" close=")">
                #{no}
            </foreach>
        </if>

        <if test="businessFlags != null and businessFlags.size()>0">
            AND a.business_flag in
            <foreach collection="businessFlags" item="no" separator="," open="(" close=")">
                #{no}
            </foreach>
        </if>

        <if test="controlStatusList != null and controlStatusList.size()>0">
            AND b.control_status in
            <foreach collection="controlStatusList" item="no" separator="," open="(" close=")">
                #{no}
            </foreach>
        </if>

        <if test="customerKeywords != null and customerKeywords != ''">
            and (
            locate(#{customerKeywords},c.customer_no)
            or locate(#{customerKeywords},a.customer_name)
            or locate(#{customerKeywords},c.customer_code)
            or locate(#{customerKeywords},a.mnemonic_code)
            )
        </if>

        <if test="customerExactKeywords != null and customerExactKeywords != ''">
            and (
            c.customer_no = #{customerExactKeywords}
            or a.customer_name =  #{customerExactKeywords}
            or c.customer_code = #{customerExactKeywords}
            or a.mnemonic_code = #{customerExactKeywords}
            )
        </if>

        <if test="customerCodeKeywords != null and customerCodeKeywords !=''">
            and (
            LOCATE(#{customerCodeKeywords},a.customer_code)
            )
        </if>

        <if test="customerNameKeywords != null and customerNameKeywords !=''">
            and (
            LOCATE(#{customerNameKeywords},a.customer_name)
            )
        </if>

        <if test="customerCategoryNo != null">
        AND a.customer_category_no = #{customerCategoryNo}
        </if>

<!--        <if test="enterpriseType != null">-->
<!--        AND (a.enterprise_type like CONCAT('%', #{enterpriseType},'%'))-->
<!--        </if>-->

        <if test="controlStatus != null">
        AND b.control_status = #{controlStatus}
        </if>
    </select>

    <select id="findNotInPageList" resultType="com.yyigou.dsrp.cdc.dao.v2.customer.entity.CustomerV2WithBiz">
        SELECT
        a.*,
        c.id bizId,
        c.use_org_no
        FROM
        bdc_customer_base b JOIN bdc_customer_biz c JOIN bdc_customer a JOIN bdc_customer_category d
        ON
        b.use_org_no = c.use_org_no
        AND b.customer_code = c.customer_code
        AND b.enterprise_no = c.enterprise_no

        AND b.enterprise_no = a.enterprise_no
        AND b.manage_org_no = a.manage_org_no
        AND b.customer_code = a.customer_code

        AND a.enterprise_no = d.enterprise_no
        AND a.customer_category_no = d.no
        WHERE
        a.enterprise_no=#{enterpriseNo}
        AND c.use_org_no = #{useOrgNo}
        AND a.deleted=0
        AND b.deleted=0
        AND c.deleted=0
        AND d.deleted=0
        <if test="keywords != null and keywords!=''">
            and (
            a.customer_name like CONCAT('%', #{keywords},'%') or
            a.customer_code like CONCAT('%', #{keywords},'%')
            )
        </if>

        <if test="customerCategoryNo != null">
            AND a.customer_category_no = #{customerCategoryNo}
        </if>

<!--        <if test="enterpriseType != null">-->
<!--            AND (a.enterprise_type like CONCAT('%', #{enterpriseType},'%'))-->
<!--        </if>-->

        <if test="controlStatusList != null and controlStatusList.size()>0">
            AND b.control_status in
            <foreach collection="controlStatusList" item="no" separator="," open="(" close=")">
                #{no}
            </foreach>
        </if>

        <if test="noCustomerNos != null and noCustomerNos.size()>0">
            AND c.customer_no not in
            <foreach collection="noCustomerNos" item="no" separator="," open="(" close=")">
                #{no}
            </foreach>
        </if>

        <if test="businessFlags != null and businessFlags.size()>0">
            AND a.business_flag in
            <foreach collection="businessFlags" item="no" separator="," open="(" close=")">
                #{no}
            </foreach>
        </if>
    </select>

    <select id="queryPageCustomer" resultType="com.yyigou.dsrp.cdc.dao.v2.customer.entity.CustomerV2WithBiz">
        SELECT
        a.*,
        c.id bizId,
        c.use_org_no
        FROM
        bdc_customer_base b JOIN bdc_customer_biz c JOIN bdc_customer a JOIN bdc_customer_category d
        ON
        b.use_org_no = c.use_org_no
        AND b.customer_code = c.customer_code
        AND b.enterprise_no = c.enterprise_no

        AND b.enterprise_no = a.enterprise_no
        AND b.manage_org_no = a.manage_org_no
        AND b.customer_code = a.customer_code

        AND a.enterprise_no = d.enterprise_no
        AND a.customer_category_no = d.no
        WHERE
        a.enterprise_no=#{enterpriseNo}
        AND c.use_org_no = #{useOrgNo}
        AND a.deleted=0
        AND b.deleted=0
        AND c.deleted=0
        AND d.deleted=0
        <if test="keywords != null and keywords!=''">
            and (
            a.customer_name like CONCAT('%', #{keywords},'%') or
            a.customer_code like CONCAT('%', #{keywords},'%')
            )
        </if>

        <if test="customerKeywords != null and customerKeywords != ''">
            and (
            locate(#{customerKeywords},c.customer_no)
            or locate(#{customerKeywords},a.customer_name)
            or locate(#{customerKeywords},c.customer_code)
            or locate(#{customerKeywords},a.mnemonic_code)
            )
        </if>

        <if test="customerExactKeywords != null and customerExactKeywords != ''">
            and (
            c.customer_no = #{customerExactKeywords}
            or a.customer_name =  #{customerExactKeywords}
            or c.customer_code = #{customerExactKeywords}
            or a.mnemonic_code = #{customerExactKeywords}
            )
        </if>

        <if test="customerCategoryNo != null">
            AND a.customer_category_no = #{customerCategoryNo}
        </if>

        <if test="customerCode != null and customerCode != ''">
            AND a.customer_code = #{customerCode}
        </if>

        <if test="controlStatus != null">
            AND b.control_status = #{controlStatus}
        </if>

        <if test="businessFlags != null and businessFlags.size()>0">
            AND a.business_flag in
            <foreach collection="businessFlags" item="no" separator="," open="(" close=")">
                #{no}
            </foreach>
        </if>

<!--        <if test="enterpriseType != null">-->
<!--            AND (a.enterprise_type like CONCAT('%', #{enterpriseType},'%'))-->
<!--        </if>-->
    </select>

    <select id="querySpecifyOrgPageCustomer" resultType="com.yyigou.dsrp.cdc.dao.v2.customer.entity.CustomerV2WithBiz">
        SELECT
        a.*,
        c.id bizId,
        c.use_org_no
        FROM
        bdc_customer_base b JOIN bdc_customer_biz c JOIN bdc_customer a JOIN bdc_customer_category d
        ON
        b.use_org_no = c.use_org_no
        AND b.customer_code = c.customer_code
        AND b.enterprise_no = c.enterprise_no

        AND b.enterprise_no = a.enterprise_no
        AND b.manage_org_no = a.manage_org_no
        AND b.customer_code = a.customer_code

        AND a.enterprise_no = d.enterprise_no
        AND a.customer_category_no = d.no
        WHERE
        a.enterprise_no=#{enterpriseNo}
        AND c.use_org_no = #{useOrgNo}
        AND a.deleted=0
        AND b.deleted=0
        AND c.deleted=0
        AND d.deleted=0
        AND a.business_flag
        <if test="keywords != null and keywords!=''">
            and (
            a.customer_name like CONCAT('%', #{keywords},'%') or
            a.customer_code like CONCAT('%', #{keywords},'%')
            )
        </if>

        <if test="customerKeywords != null and customerKeywords != ''">
            and (
            locate(#{customerKeywords},c.customer_no)
            or locate(#{customerKeywords},a.customer_name)
            or locate(#{customerKeywords},c.customer_code)
            or locate(#{customerKeywords},a.mnemonic_code)
            )
        </if>

        <if test="customerExactKeywords != null and customerExactKeywords != ''">
            and (
            c.customer_no = #{customerExactKeywords}
            or a.customer_name =  #{customerExactKeywords}
            or c.customer_code = #{customerExactKeywords}
            or a.mnemonic_code = #{customerExactKeywords}
            )
        </if>

        <if test="customerCode != null and customerCode != ''">
            AND a.customer_code = #{customerCode}
        </if>

        <if test="customerCategoryNo != null">
            AND a.customer_category_no = #{customerCategoryNo}
        </if>

        <if test="controlStatus != null">
            AND b.control_status = #{controlStatus}
        </if>

        <if test="controlStatusList != null and controlStatusList.size()>0">
            and b.control_status in
            <foreach collection="controlStatusList" item="no" separator="," open="(" close=")">
                #{no}
            </foreach>
        </if>

        <if test="noCustomerNos != null and noCustomerNos.size()>0">
            AND c.customer_no not in
            <foreach collection="noCustomerNos" item="no" separator="," open="(" close=")">
                #{no}
            </foreach>
        </if>
    </select>
</mapper>
