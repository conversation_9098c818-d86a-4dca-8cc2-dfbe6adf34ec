<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yyigou.dsrp.cdc.dao.v2.supplier.SupplierCategoryV2DAO">

    <resultMap id="GradedResultMap" type="com.yyigou.dsrp.cdc.dao.v2.supplier.entity.SupplierCategoryV2">
        <id property="no" column="no" jdbcType="VARCHAR"/>
        <result property="enterpriseNo" column="enterprise_no" jdbcType="VARCHAR"/>
        <result property="parentNo" column="parent_no" jdbcType="VARCHAR"/>
        <result property="mnemonicCode" column="mnemonic_code" jdbcType="VARCHAR"/>
        <result property="categoryCode" column="category_code" jdbcType="VARCHAR"/>
        <result property="categoryName" column="category_name" jdbcType="VARCHAR"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="OTHER"/>
        <result property="sortNum" column="sort_num" jdbcType="TINYINT"/>
        <result property="deleted" column="deleted" jdbcType="TINYINT"/>
        <result property="createNo" column="create_no" jdbcType="VARCHAR"/>
        <result property="createName" column="create_name" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="VARCHAR"/>
        <result property="operateNo" column="operate_no" jdbcType="VARCHAR"/>
        <result property="operateName" column="operate_name" jdbcType="VARCHAR"/>
        <result property="operateTime" column="operate_time" jdbcType="VARCHAR"/>
        <result property="opTimestamp" column="op_timestamp" jdbcType="TIMESTAMP"/>
        <result property="opRevsion" column="op_revsion" jdbcType="INTEGER"/>
        <result property="opType" column="op_type" jdbcType="CHAR"/>
        <result property="ysSyncFlag" column="ys_sync_flag" jdbcType="VARCHAR"/>
        <result property="groupNo" column="group_no" jdbcType="VARCHAR"/>
        <result property="manageOrgNo" column="manage_org_no" jdbcType="VARCHAR"/>
        <result property="level" column="level" jdbcType="TINYINT"/>
        <result property="topCategoryNo" column="top_category_no" jdbcType="VARCHAR"/>
        <result property="path" column="path" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Graded_Column_List">
        c.no,
        c.enterprise_no,
        c.parent_no,
        c.mnemonic_code,
        c.category_code,
        c.category_name,
        c.remark,
        c.status,
        c.sort_num,
        c.deleted,
        c.create_no,
        c.create_name,
        c.create_time,
        c.operate_no,
        c.operate_name,
        c.operate_time,
        c.op_timestamp,
        c.op_revsion,
        c.op_type,
        c.ys_sync_flag,
        c.group_no,
        c.manage_org_no,
        c.level,
        c.top_category_no,
        c.path
    </sql>

    <select id="selectListByUseOrgNo" resultMap="GradedResultMap">
        SELECT
        <include refid="Graded_Column_List"></include>
        FROM bdc_supplier_category_base b
        JOIN bdc_supplier_category c
        ON b.enterprise_no = c.enterprise_no and b.category_no=c.no
        WHERE b.enterprise_no= #{enterpriseNo} and b.use_org_no = #{useOrgNo} and b.deleted = #{deleteFlag}
        and c.deleted = #{deleteFlag}
        order by c.level asc, c.sort_num is null asc, c.sort_num asc
    </select>

    <select id="selectListByUseOrgNoList" resultMap="GradedResultMap">
        SELECT
        <include refid="Graded_Column_List"></include>
        FROM bdc_supplier_category_base b
        JOIN bdc_supplier_category c
        ON b.enterprise_no = c.enterprise_no and b.category_no=c.no
        WHERE b.enterprise_no= #{enterpriseNo} and b.use_org_no in
        <foreach collection="useOrgNoList" item="useOrgNo" separator="," open="(" close=")">
            #{useOrgNo}
        </foreach>
        and b.deleted = #{deleteFlag}
        and c.deleted = #{deleteFlag}
        order by c.level asc, c.sort_num is null asc, c.sort_num asc
    </select>

    <update id="batchStatusUpdate">
        <foreach collection="list" item="item" separator=";">
            UPDATE bdc_supplier_category
            SET
            status = #{item.status},
            operate_no = #{item.operateNo},
            operate_name = #{item.operateName},
            operate_time = #{item.operateTime}
            WHERE enterprise_no= #{item.enterpriseNo} and manage_org_no = #{item.manageOrgNo} and no = #{item.no}
        </foreach>
    </update>
</mapper>
