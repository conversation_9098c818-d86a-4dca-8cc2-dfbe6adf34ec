<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yyigou.dsrp.cdc.dao.customer.CustomerDAO">

    <select id="selectCustomerList" resultType="com.yyigou.dsrp.cdc.dao.customer.entity.Customer">
        SELECT
            a.*
        FROM
            bdc_customer a
        <if test="assignEnterprsieNoList != null and assignEnterprsieNoList.size()>0">
            JOIN
            (
            SELECT DISTINCT company_code FROM bdc_supplier_customer_use_info
            WHERE manage_enterprise_no = #{managerEnterpriseNo} AND company_type=2
            AND use_enterprise_no in
            <foreach collection="assignEnterprsieNoList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
            AND `status` = 1 AND deleted = 0
            ) as b ON a.customer_code=b.company_code
        </if>
        WHERE a.enterprise_no=#{enterpriseNo} AND a.deleted=0
        <if test="customerKeywords != null and customerKeywords != ''">
            AND (
            a.customer_name like CONCAT('%',#{customerKeywords},'%') or
            a.customer_name_en like CONCAT('%',#{customerKeywords},'%') or
            a.customer_code like CONCAT('%',#{customerKeywords},'%') or
            a.mnemonic_code like CONCAT('%',#{customerKeywords},'%')
            )
        </if>
        <if test="cooperationModes != null and cooperationModes.size()>0">
            and a.cooperation_mode in
            <foreach collection="cooperationModes" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="priceCategoryCodeList != null and priceCategoryCodeList.size()>0">
            and a.price_category_code in
            <foreach collection="priceCategoryCodeList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="customerCategoryNos != null and customerCategoryNos.size()>0">
            and a.customer_category_no in
            <foreach collection="customerCategoryNos" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="excludeCustomerCodeList != null and excludeCustomerCodeList.size()>0">
            and a.customer_code not in
            <foreach collection="excludeCustomerCodeList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="ownerCompanyKeywords != null and ownerCompanyKeywords != ''">
            AND a.owner_company like CONCAT('%',#{ownerCompanyKeywords},'%')
        </if>
        <if test="controlIdList != null and controlIdList.size()>0">
            and a.control_id in
            <foreach collection="controlIdList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="unifiedSocialCodeKeywords != null and unifiedSocialCodeKeywords != ''">
            AND a.unified_social_code like CONCAT('%',#{unifiedSocialCodeKeywords},'%')
        </if>
        <if test="businessTypeList != null and businessTypeList.size()>0">
            and a.business_type in
            <foreach collection="businessTypeList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="linkManAndPhoneKeywords != null and linkManAndPhoneKeywords != ''">
            AND EXISTS
            (
            SELECT 1 FROM bdc_company_linkman c
            WHERE c.enterprise_no=#{enterpriseNo} and c.source_no=a.customer_no and c.linkman_type='is_sale'
            and (
                c.mobile_phone like CONCAT('%',#{linkManAndPhoneKeywords},'%') or
                c.linkman like CONCAT('%',#{linkManAndPhoneKeywords},'%')
                )
            )
        </if>
    </select>
</mapper>
