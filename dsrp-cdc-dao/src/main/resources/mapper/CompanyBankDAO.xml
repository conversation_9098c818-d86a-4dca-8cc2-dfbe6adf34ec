<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yyigou.dsrp.cdc.dao.supplier.CompanyBankDAO">

    <resultMap id="BaseResultMap" type="com.yyigou.dsrp.cdc.dao.supplier.entity.CompanyBank">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="enterpriseNo" column="enterprise_no" jdbcType="VARCHAR"/>
            <result property="supplierNo" column="supplier_no" jdbcType="VARCHAR"/>
            <result property="bankType" column="bank_type" jdbcType="VARCHAR"/>
            <result property="bankTypeName" column="bank_type_name" jdbcType="VARCHAR"/>
            <result property="openBank" column="open_bank" jdbcType="VARCHAR"/>
            <result property="accountNo" column="account_no" jdbcType="VARCHAR"/>
            <result property="accountName" column="account_name" jdbcType="VARCHAR"/>
            <result property="accountType" column="account_type" jdbcType="TINYINT"/>
            <result property="linkPerson" column="link_person" jdbcType="VARCHAR"/>
            <result property="linkPhone" column="link_phone" jdbcType="VARCHAR"/>
            <result property="status" column="status" jdbcType="TINYINT"/>
            <result property="isDefault" column="is_default" jdbcType="TINYINT"/>
            <result property="deleted" column="deleted" jdbcType="TINYINT"/>
            <result property="createNo" column="create_no" jdbcType="VARCHAR"/>
            <result property="createName" column="create_name" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="VARCHAR"/>
            <result property="operateNo" column="operate_no" jdbcType="VARCHAR"/>
            <result property="operateName" column="operate_name" jdbcType="VARCHAR"/>
            <result property="operateTime" column="operate_time" jdbcType="VARCHAR"/>
            <result property="opTimestamp" column="op_timestamp" jdbcType="TIMESTAMP"/>
            <result property="opRevsion" column="op_revsion" jdbcType="INTEGER"/>
            <result property="opType" column="op_type" jdbcType="CHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,enterprise_no,supplier_no,
        bank_type,bank_type_name,open_bank,
        account_no,account_name,account_type,
        link_person,link_phone,status,
        is_default,deleted,create_no,
        create_name,create_time,operate_no,
        operate_name,operate_time,op_timestamp,
        op_revsion,op_type
    </sql>
</mapper>
