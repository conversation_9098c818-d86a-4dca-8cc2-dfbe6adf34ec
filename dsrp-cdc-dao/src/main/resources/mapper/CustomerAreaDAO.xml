<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yyigou.dsrp.cdc.dao.customer.CustomerAreaDAO">

    <resultMap id="BaseResultMap" type="com.yyigou.dsrp.cdc.dao.customer.entity.CustomerArea">
            <result property="customerNo" column="customer_no" jdbcType="VARCHAR"/>
            <result property="customerName" column="customer_name" jdbcType="VARCHAR"/>
            <result property="areaNo" column="area_no" jdbcType="VARCHAR"/>
            <result property="areaName" column="area_name" jdbcType="VARCHAR"/>
            <result property="areaCode" column="area_code" jdbcType="VARCHAR"/>
            <result property="enterpriseNo" column="enterprise_no" jdbcType="VARCHAR"/>
            <result property="operateNo" column="operate_no" jdbcType="VARCHAR"/>
            <result property="operateName" column="operate_name" jdbcType="VARCHAR"/>
            <result property="operateTime" column="operate_time" jdbcType="VARCHAR"/>
            <result property="deleted" column="deleted" jdbcType="INTEGER"/>
            <result property="isDefault" column="is_default" jdbcType="INTEGER"/>
            <result property="parentAreaNo" column="parent_area_no" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        customer_no,customer_name,area_no,
        area_name,area_code,enterprise_no,
        operate_no,operate_name,operate_time,
        deleted,is_default,parent_area_no
    </sql>
</mapper>
