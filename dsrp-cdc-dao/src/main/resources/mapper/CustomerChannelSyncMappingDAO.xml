<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yyigou.dsrp.cdc.dao.v2.customer.CustomerChannelSyncMappingDAO">

    <resultMap id="BaseResultMap" type="com.yyigou.dsrp.cdc.dao.v2.customer.entity.CustomerChannelSyncMapping">
        <id property="id" column="id"/>
        <result property="enterpriseNo" column="enterprise_no"/>
        <result property="orgNo" column="org_no"/>
        <result property="channelCode" column="channel_code"/>
        <result property="viewNo" column="view_no"/>
        <result property="innerBillNo" column="inner_bill_no"/>
        <result property="outBillNo" column="out_bill_no"/>
        <result property="syncStatus" column="sync_status"/>
        <result property="syncResult" column="sync_result"/>
        <result property="failTimes" column="fail_times"/>
        <result property="remark" column="remark"/>
        <result property="ext1" column="ext1"/>
        <result property="ext2" column="ext2"/>
        <result property="ext3" column="ext3"/>
        <result property="ext4" column="ext4"/>
        <result property="ext5" column="ext5"/>
        <result property="ext6" column="ext6"/>
        <result property="createTime" column="create_time"/>
        <result property="operateTime" column="operate_time"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,enterprise_no,org_no,channel_code,view_no,inner_bill_no,
        out_bill_no,sync_status,sync_result,fail_times,remark,
        ext1,ext2,ext3,ext4,ext5,
        ext6,create_time,operate_time
    </sql>
</mapper>
