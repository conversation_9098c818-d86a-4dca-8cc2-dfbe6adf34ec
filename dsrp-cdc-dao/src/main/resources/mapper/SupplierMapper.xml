<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yyigou.dsrp.cdc.dao.supplier.SupplierDAO">

    <select id="selectMultiOrgSupplierList" resultType="com.yyigou.dsrp.cdc.dao.supplier.entity.Supplier">
        SELECT
            a.*
        FROM
            bdc_supplier a
        <if test="assignEnterprsieNoList != null and assignEnterprsieNoList.size()>0">
            JOIN
            (
            SELECT DISTINCT company_code FROM bdc_supplier_customer_use_info
            WHERE manage_enterprise_no = #{managerEnterpriseNo} AND company_type=1
            AND use_enterprise_no in
            <foreach collection="assignEnterprsieNoList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
            AND `status` = 1 AND deleted = 0
            ) as b ON a.supplier_code=b.company_code
        </if>
        WHERE a.enterprise_no=#{enterpriseNo} AND a.deleted=0
        <if test="keywords != null and keywords != ''">
            AND (
            a.supplier_code like CONCAT('%',#{keywords},'%') or
            a.supplier_name like CONCAT('%',#{keywords},'%')
            )
        </if>
        <if test="supplierNoList != null and supplierNoList.size()>0">
            and a.supplier_no in
            <foreach collection="supplierNoList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>
</mapper>
