<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yyigou.dsrp.cdc.dao.v2.supplier.SupplierV2DAO">
    <select id="selectManageView" resultType="com.yyigou.dsrp.cdc.dao.v2.supplier.entity.SupplierV2WithBiz">
        SELECT
        a.*,
        c.id bizId,
        c.use_org_no
        FROM
        bdc_supplier_base b JOIN bdc_supplier_biz c JOIN bdc_supplier a
        ON
        b.use_org_no = c.use_org_no
        AND b.supplier_code = c.supplier_code
        AND b.enterprise_no = c.enterprise_no

        AND b.enterprise_no = a.enterprise_no
        AND b.manage_org_no = a.manage_org_no
        AND b.supplier_code = a.supplier_code
        WHERE
        a.enterprise_no=#{enterpriseNo}
        AND b.manage_org_no = b.use_org_no
        AND a.deleted=0
        AND b.deleted=0
        AND c.deleted=0
        <if test="idList != null and idList.size()>0">
            and c.supplier_no in
            <foreach collection="idList" item="no" separator="," open="(" close=")">
                #{no}
            </foreach>
        </if>
    </select>

    <select id="selectUseView" resultType="com.yyigou.dsrp.cdc.dao.v2.supplier.entity.SupplierV2WithBiz">
        SELECT
        a.*,
        c.id bizId,
        c.use_org_no
        FROM
        bdc_supplier_base b JOIN bdc_supplier_biz c JOIN bdc_supplier a
        ON
        b.use_org_no = c.use_org_no
        AND b.supplier_code = c.supplier_code
        AND b.enterprise_no = c.enterprise_no

        AND b.enterprise_no = a.enterprise_no
        AND b.manage_org_no = a.manage_org_no
        AND b.supplier_code = a.supplier_code
        WHERE
        a.enterprise_no=#{enterpriseNo}
        AND a.deleted=0
        AND b.deleted=0
        AND c.deleted=0
        <if test="idList != null and idList.size()>0">
            and c.supplier_no in
            <foreach collection="idList" item="no" separator="," open="(" close=")">
                #{no}
            </foreach>
        </if>
    </select>

    <select id="selectDistinctSupplierCodeByUseOrgNoList" resultType="java.lang.String">
        select distinct supplier_code
        from  bdc_supplier_base
        where enterprise_no = #{enterpriseNo}
          and deleted = 0
        <if test="useOrgNoList != null and useOrgNoList.size > 0">
            and use_org_no in
            <foreach collection="useOrgNoList" item="useOrgNo" open="(" separator="," close=")">
                #{useOrgNo}
            </foreach>
        </if>
    </select>

    <select id="selectUseViewNoAuthZ" resultType="com.yyigou.dsrp.cdc.dao.v2.supplier.entity.SupplierV2WithBiz">
        SELECT
        a.*,
        c.id bizId,
        c.use_org_no
        FROM
        bdc_supplier_base b JOIN bdc_supplier_biz c JOIN bdc_supplier a
        ON
        b.use_org_no = c.use_org_no
        AND b.supplier_code = c.supplier_code
        AND b.enterprise_no = c.enterprise_no

        AND b.enterprise_no = a.enterprise_no
        AND b.manage_org_no = a.manage_org_no
        AND b.supplier_code = a.supplier_code
        WHERE
        a.enterprise_no=#{enterpriseNo}
        AND a.deleted=0
        AND b.deleted=0
        AND c.deleted=0
        AND b.use_org_no=#{useOrgNo}
        <if test="controlStatusList != null and controlStatusList.size()>0">
            AND b.control_status in
            <foreach collection="controlStatusList" item="controlStatus" separator="," open="(" close=")">
                #{controlStatus}
            </foreach>
        </if>
        <if test="supplierNameKeywords != null and supplierNameKeywords != ''">
            AND a.supplier_name LIKE CONCAT('%',#{supplierNameKeywords},'%')
        </if>
        <if test="supplierKeywords != null and supplierKeywords != ''">
            AND (a.supplier_name LIKE CONCAT('%',#{supplierKeywords},'%') or a.supplier_code LIKE CONCAT('%',#{supplierKeywords},'%'))
        </if>
    </select>

    <select id="selectReverseUseViewNoAuthZ" resultType="com.yyigou.dsrp.cdc.dao.v2.supplier.entity.SupplierV2WithBase">
        select a.*, t.id baseId, t.use_org_no
        from bdc_supplier a
            left join
            (select * from bdc_supplier_base where enterprise_no=#{enterpriseNo} and use_org_no = #{useOrgNo} and deleted=0) t
            on
            a.enterprise_no = t.enterprise_no
            and a.supplier_code = t.supplier_code
        where
        a.enterprise_no=#{enterpriseNo}
        AND a.deleted=0
        <if test="supplierNameKeywords != null and supplierNameKeywords != ''">
            and a.supplier_name LIKE CONCAT('%',#{supplierNameKeywords},'%')
        </if>
    </select>

    <select id="findListPageForTenant" resultType="com.yyigou.dsrp.cdc.dao.v2.supplier.entity.SupplierV2WithBase">
        select basic.*,
               base.`id` baseId,
               base.`use_org_no`,
               base.`control_status`,
               base.supplier_no
        from bdc_supplier_base base
            left join
            bdc_supplier basic
                on base.enterprise_no = basic.enterprise_no
                and base.manage_org_no = basic.manage_org_no
                and base.supplier_code = basic.supplier_code
        where base.deleted = 0
        and basic.deleted = 0
        and base.manage_org_no = base.use_org_no
        and base.enterprise_no = #{enterpriseNo}
        and basic.business_flag = 1
        <if test="supplierCodeSet != null and supplierCodeSet.size > 0">
            and base.supplier_code in
            <foreach collection="supplierCodeSet" item="supplierCode" open="(" separator="," close=")">
                #{supplierCode}
            </foreach>
        </if>
    </select>

    <select id="getPendingCount" resultType="java.lang.Long">
        SELECT count(1)
        FROM bdc_supplier_base a
                 JOIN bdc_supplier b
                      ON a.enterprise_no = b.enterprise_no AND a.manage_org_no = b.manage_org_no AND
                         a.supplier_code = b.supplier_code
        WHERE a.enterprise_no = #{enterpriseNo}
          AND a.deleted = #{deleted}
          AND b.business_flag = #{businessFlag}
          AND b.deleted = #{deleted}
    </select>

    <select id="queryStandardInfo" resultType="com.yyigou.dsrp.cdc.dao.v2.supplier.entity.SupplierV2">
        SELECT
        a.*
        FROM
        bdc_supplier_base b JOIN bdc_supplier_biz c JOIN bdc_supplier a
        ON
        b.use_org_no = c.use_org_no
        AND b.supplier_code = c.supplier_code
        AND b.enterprise_no = c.enterprise_no

        AND b.enterprise_no = a.enterprise_no
        AND b.manage_org_no = a.manage_org_no
        AND b.supplier_code = a.supplier_code
        WHERE
        a.enterprise_no=#{enterpriseNo}
        AND a.deleted=0
        AND b.deleted=0
        AND c.deleted=0
        <if test="manageOrgNo != null and manageOrgNo != ''">
            AND a.manage_org_no = #{manageOrgNo}
        </if>
        <if test="useOrgNo != null and useOrgNo != ''">
            AND c.use_org_no = #{useOrgNo}
        </if>
        <if test="supplierNo != null and supplierNo != ''">
            AND c.supplier_no = #{supplierNo}
        </if>
        <if test="supplierCode != null and supplierCode != ''">
            AND c.supplier_code = #{supplierCode}
        </if>
        <if test="supplierName != null and supplierName != ''">
            and a.supplier_name = #{supplierName}
        </if>
        <if test="unifiedSocialCode != null and unifiedSocialCode != ''">
            and a.unified_social_code = #{unifiedSocialCode}
        </if>
        <if test="supplierKeywords != null and supplierKeywords != ''">
            AND (
            a.supplier_name LIKE CONCAT('%',#{supplierKeywords},'%')
            OR a.supplier_code LIKE CONCAT('%',#{supplierKeywords},'%')
            )
        </if>
        <if test="supplierNoList != null and supplierNoList.size()>0">
            and c.supplier_no in
            <foreach collection="supplierNoList" item="no" separator="," open="(" close=")">
                #{no}
            </foreach>
        </if>
        <if test="supplierCodeList != null and supplierCodeList.size()>0">
            and a.supplier_code in
            <foreach collection="supplierCodeList" item="no" separator="," open="(" close=")">
                #{no}
            </foreach>
        </if>
        <if test="supplierNameList != null and supplierNameList.size()>0">
            and a.supplier_name in
            <foreach collection="supplierNameList" item="no" separator="," open="(" close=")">
                #{no}
            </foreach>
        </if>
        <if test="controlStatusList != null and controlStatusList.size()>0">
            and b.control_status in
            <foreach collection="controlStatusList" item="no" separator="," open="(" close=")">
                #{no}
            </foreach>
        </if>
        <if test="companyNoList != null and companyNoList.size()>0">
            and c.company_no in
            <foreach collection="companyNoList" item="no" separator="," open="(" close=")">
                #{no}
            </foreach>
        </if>
        <if test="supplierCategoryNoList != null and supplierCategoryNoList.size()>0">
            and c.supplier_category_no in
            <foreach collection="supplierCategoryNoList" item="no" separator="," open="(" close=")">
                #{no}
            </foreach>
        </if>
        <if test="cooperationModeList != null and cooperationModeList.size()>0">
            and c.cooperation_mode in
            <foreach collection="cooperationModeList" item="no" separator="," open="(" close=")">
                #{no}
            </foreach>
        </if>
    </select>

    <select id="queryWithBizInfo" resultType="com.yyigou.dsrp.cdc.dao.v2.supplier.entity.SupplierV2WithBiz">
        SELECT
        a.*,
        c.id bizId,
        c.use_org_no
        FROM
        bdc_supplier_base b JOIN bdc_supplier_biz c JOIN bdc_supplier a
        ON
        b.use_org_no = c.use_org_no
        AND b.supplier_code = c.supplier_code
        AND b.enterprise_no = c.enterprise_no

        AND b.enterprise_no = a.enterprise_no
        AND b.manage_org_no = a.manage_org_no
        AND b.supplier_code = a.supplier_code
        WHERE
        a.enterprise_no=#{enterpriseNo}
        AND a.deleted=0
        AND b.deleted=0
        AND c.deleted=0
        <if test="manageOrgNo != null and manageOrgNo != ''">
            AND a.manage_org_no = #{manageOrgNo}
        </if>
        <if test="useOrgNo != null and useOrgNo != ''">
            AND c.use_org_no = #{useOrgNo}
        </if>
        <if test="supplierNo != null and supplierNo != ''">
            AND c.supplier_no = #{supplierNo}
        </if>
        <if test="supplierCode != null and supplierCode != ''">
            AND c.supplier_code = #{supplierCode}
        </if>
        <if test="supplierName != null and supplierName != ''">
            and a.supplier_name = #{supplierName}
        </if>
        <if test="unifiedSocialCode != null and unifiedSocialCode != ''">
            and a.unified_social_code = #{unifiedSocialCode}
        </if>
        <if test="supplierKeywords != null and supplierKeywords != ''">
            AND (
            a.supplier_name LIKE CONCAT('%',#{supplierKeywords},'%')
            OR a.supplier_code LIKE CONCAT('%',#{supplierKeywords},'%')
            )
        </if>
        <if test="supplierNoList != null and supplierNoList.size()>0">
            and c.supplier_no in
            <foreach collection="supplierNoList" item="no" separator="," open="(" close=")">
                #{no}
            </foreach>
        </if>
        <if test="supplierCodeList != null and supplierCodeList.size()>0">
            and a.supplier_code in
            <foreach collection="supplierCodeList" item="no" separator="," open="(" close=")">
                #{no}
            </foreach>
        </if>
        <if test="supplierNameList != null and supplierNameList.size()>0">
            and a.supplier_name in
            <foreach collection="supplierNameList" item="no" separator="," open="(" close=")">
                #{no}
            </foreach>
        </if>
        <if test="controlStatusList != null and controlStatusList.size()>0">
            and b.control_status in
            <foreach collection="controlStatusList" item="no" separator="," open="(" close=")">
                #{no}
            </foreach>
        </if>
        <if test="companyNoList != null and companyNoList.size()>0">
            and c.company_no in
            <foreach collection="companyNoList" item="no" separator="," open="(" close=")">
                #{no}
            </foreach>
        </if>
        <if test="supplierCategoryNoList != null and supplierCategoryNoList.size()>0">
            and c.supplier_category_no in
            <foreach collection="supplierCategoryNoList" item="no" separator="," open="(" close=")">
                #{no}
            </foreach>
        </if>
        <if test="cooperationModeList != null and cooperationModeList.size()>0">
            and c.cooperation_mode in
            <foreach collection="cooperationModeList" item="no" separator="," open="(" close=")">
                #{no}
            </foreach>
        </if>
    </select>

    <select id="selectSupplierBizByCodeOrgPair" resultType="com.yyigou.dsrp.cdc.dao.v2.supplier.entity.SupplierV2WithBiz">
        SELECT
        a.*,
        c.id bizId,
        c.use_org_no
        FROM
        bdc_supplier_base b JOIN bdc_supplier_biz c JOIN bdc_supplier a
        ON
        b.use_org_no = c.use_org_no
        AND b.supplier_code = c.supplier_code
        AND b.enterprise_no = c.enterprise_no

        AND b.enterprise_no = a.enterprise_no
        AND b.manage_org_no = a.manage_org_no
        AND b.supplier_code = a.supplier_code
        WHERE
        a.enterprise_no=#{enterpriseNo}
        AND a.deleted=0
        AND b.deleted=0
        AND c.deleted=0
        <if test="supplierCodeOrgPairList != null and supplierCodeOrgPairList != ''">
            and (c.use_org_no, c.supplier_code) in
            <foreach collection="supplierCodeOrgPairList" item="pair" separator="," open="(" close=")">
                (#{pair.useOrgNo}, #{pair.supplierCode})
            </foreach>
        </if>
    </select>

    <update id="batchUpdateForDA">
        <foreach collection="list" item="item" separator=";">
            UPDATE bdc_supplier
            SET
            <trim suffixOverrides=",">
                supplier_name = #{item.supplierName},
                supplier_name_en = #{item.supplierNameEn},
                supplier_category_no = #{item.supplierCategoryNo},
                mnemonic_code = #{item.mnemonicCode},
                remark = #{item.remark},
                operate_no = #{item.operateNo},
                operate_name = #{item.operateName},
                operate_time = #{item.operateTime},
            </trim>
            WHERE
            enterprise_no = #{item.enterpriseNo}
            AND
            manage_org_no = #{item.manageOrgNo}
            AND
            supplier_no = #{item.supplierNo}
            AND
            deleted = 0
        </foreach>
    </update>


    <select id="findListPageByFormal" resultType="com.yyigou.dsrp.cdc.dao.v2.supplier.entity.SupplierV2WithBiz">
        SELECT
        a.*,
        c.id bizId,
        c.use_org_no
        FROM
        bdc_supplier_base b JOIN bdc_supplier_biz c JOIN bdc_supplier a JOIN bdc_supplier_category d
        ON
        b.use_org_no = c.use_org_no
        AND b.supplier_code = c.supplier_code
        AND b.enterprise_no = c.enterprise_no

        AND b.enterprise_no = a.enterprise_no
        AND b.manage_org_no = a.manage_org_no
        AND b.supplier_code = a.supplier_code

        AND a.enterprise_no = d.enterprise_no
        AND a.supplier_category_no = d.no
        WHERE
        a.enterprise_no=#{enterpriseNo}
        AND c.use_org_no = #{useOrgNo}
        AND a.business_flag=1
        AND a.deleted=0
        AND b.deleted=0
        AND c.deleted=0
        AND d.deleted=0

        <if test="noSupplierNos != null and noSupplierNos.size()>0">
            AND c.supplier_no not in
            <foreach collection="noSupplierNos" item="no" separator="," open="(" close=")">
                #{no}
            </foreach>
        </if>
        <if test="supplierKeywords != null and supplierKeywords != ''">
            AND (
            a.supplier_name LIKE CONCAT('%',#{supplierKeywords},'%')
            OR a.supplier_code LIKE CONCAT('%',#{supplierKeywords},'%')
            )
        </if>
        <if test="supplierExactKeywords != null and supplierExactKeywords != ''">
            AND (
            a.supplier_name = #{supplierExactKeywords}
            OR a.supplier_code = #{supplierExactKeywords}
            )
        </if>
        <if test="cooperationModeList != null and cooperationModeList.size()>0">
            and c.cooperation_mode in
            <foreach collection="cooperationModeList" item="no" separator="," open="(" close=")">
                #{no}
            </foreach>
        </if>
        <if test="supplierCategoryKeywords != null and supplierCategoryKeywords != ''">
            AND d.category_name LIKE CONCAT('%',#{supplierCategoryKeywords},'%')
        </if>
        <if test="supplierCategoryNoList != null and supplierCategoryNoList.size()>0">
            and a.supplier_category_no in
            <foreach collection="supplierCategoryNoList" item="no" separator="," open="(" close=")">
                #{no}
            </foreach>
        </if>
        <if test="unifiedSocialCodeKeywords != null and unifiedSocialCodeKeywords != ''">
            AND a.unified_social_code LIKE CONCAT('%',#{unifiedSocialCodeKeywords},'%')
        </if>
    </select>
</mapper>
