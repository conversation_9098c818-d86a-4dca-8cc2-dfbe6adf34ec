package com.yyigou.dsrp.cdc.dao.customer;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yyigou.dsrp.cdc.common.enums.DeletedEnum;
import com.yyigou.dsrp.cdc.dao.config.CdcBaseMapper;
import com.yyigou.dsrp.cdc.dao.customer.entity.Customer;
import com.yyigou.dsrp.cdc.model.customer.req.CustomerListPageReq;

import java.util.List;

/**
 * <p>
 * 客户表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-16
 */
public interface CustomerDAO extends CdcBaseMapper<Customer> {

    default Customer getCustomerByNo(String enterpriseNo, String customerNo) {
        LambdaQueryWrapper<Customer> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(Customer::getEnterpriseNo, enterpriseNo)
                .eq(Customer::getCustomerNo, customerNo)
                .eq(Customer::getDeleted, DeletedEnum.UN_DELETE.getValue());
        return selectOne(wrapper);
    }

    default List<Customer> getCustomerByNoList(String enterpriseNo, List<String> customerNoList) {
        LambdaQueryWrapper<Customer> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(Customer::getEnterpriseNo, enterpriseNo)
                .in(Customer::getCustomerNo, customerNoList)
                .eq(Customer::getDeleted, DeletedEnum.UN_DELETE.getValue());
        return selectList(wrapper);
    }

    default List<Customer> getCustomerByCodeList(String enterpriseNo, List<String> customerCodeList) {
        LambdaQueryWrapper<Customer> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(Customer::getEnterpriseNo, enterpriseNo)
                .in(Customer::getCustomerCode, customerCodeList)
                .eq(Customer::getDeleted, DeletedEnum.UN_DELETE.getValue());
        return selectList(wrapper);
    }

    default List<Customer> getCustomerByCompanyNo(String enterpriseNo, String companyNo) {
        return selectList(Wrappers.<Customer>lambdaQuery()
                .eq(Customer::getEnterpriseNo, enterpriseNo)
                .eq(Customer::getCompanyNo, companyNo)
                .eq(Customer::getDeleted, DeletedEnum.UN_DELETE.getValue()));
    }

    default Customer getCustomerByCustomerCode(String enterpriseNo, String customerCode) {
        return selectOne(Wrappers.<Customer>lambdaQuery()
                .eq(Customer::getEnterpriseNo, enterpriseNo)
                .eq(Customer::getCustomerCode, customerCode)
                .eq(Customer::getDeleted, DeletedEnum.UN_DELETE.getValue()));
    }

    default Customer getCustomerByCustomerName(String enterpriseNo, String customerName) {
        return selectOne(Wrappers.<Customer>lambdaQuery()
                .eq(Customer::getEnterpriseNo, enterpriseNo)
                .eq(Customer::getCustomerName, customerName)
                .eq(Customer::getDeleted, DeletedEnum.UN_DELETE.getValue()));
    }

    default Customer getCustomerByNoEnterpriseNo(String customerNo) {
        return selectOne(Wrappers.<Customer>lambdaQuery()
                .eq(Customer::getCustomerNo, customerNo)
                .eq(Customer::getDeleted, DeletedEnum.UN_DELETE.getValue()));
    }

    List<Customer> selectCustomerList(CustomerListPageReq params);
}
