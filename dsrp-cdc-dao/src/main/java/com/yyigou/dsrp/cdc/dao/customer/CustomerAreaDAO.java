package com.yyigou.dsrp.cdc.dao.customer;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yyigou.dsrp.cdc.common.enums.DeletedEnum;
import com.yyigou.dsrp.cdc.dao.config.CdcBaseMapper;
import com.yyigou.dsrp.cdc.dao.customer.entity.CustomerArea;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【bdc_customer_area(客户区域表)】的数据库操作Mapper
 * @createDate 2024-07-30 13:35:06
 * @Entity com.yyigou.dsrp.cdc.dao.customer.entity.CustomerArea
 */
public interface CustomerAreaDAO extends CdcBaseMapper<CustomerArea> {

    default List<CustomerArea> getCustomerAreaByCustomerNo(String enterpriseNo, String customerNo) {
        LambdaQueryWrapper<CustomerArea> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(CustomerArea::getEnterpriseNo, enterpriseNo)
                .eq(CustomerArea::getCustomerNo, customerNo)
                .eq(CustomerArea::getDeleted, DeletedEnum.UN_DELETE.getValue());
        return selectList(wrapper);
    }


    default void deleteAreaByCustomerNo(String enterpriseNo, String customerNo) {
        update(new CustomerArea(), new UpdateWrapper<CustomerArea>().lambda().set(CustomerArea::getDeleted, DeletedEnum.DELETED.getValue())
                .eq(CustomerArea::getEnterpriseNo, enterpriseNo)
                .eq(CustomerArea::getCustomerNo, customerNo)
                .eq(CustomerArea::getDeleted, DeletedEnum.UN_DELETE.getValue()));
    }

    default void deleteAreaByCustomerNo(String enterpriseNo, String customerNo,List<String> areaNoList) {
        update(new CustomerArea(), new UpdateWrapper<CustomerArea>().lambda().set(CustomerArea::getDeleted, DeletedEnum.DELETED.getValue())
                .eq(CustomerArea::getEnterpriseNo, enterpriseNo)
                .eq(CustomerArea::getCustomerNo, customerNo)
                .in(CustomerArea::getAreaNo, areaNoList)
                .eq(CustomerArea::getDeleted, DeletedEnum.UN_DELETE.getValue()));
    }


}




