package com.yyigou.dsrp.cdc.dao.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * @TableName gc_bank_type
 */
@TableName(value = "gc_bank_type")
@Data
public class BankType implements Serializable {
    @TableId(type = IdType.AUTO)
    private Integer id;

    private String bankCode;

    private String bankName;

    private String bankLogo;

    private String country;

    private Integer deleted;

    private static final long serialVersionUID = 1L;
}