package com.yyigou.dsrp.cdc.dao.v2.supplier.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;

@TableName("bdc_supplier")
@Data
public class SupplierV2 implements Serializable {
    private static final long serialVersionUID = 1L;

    @EntityField(name = "供应商主键")
    @TableId(value = "supplier_no")
    private String supplierNo;

    @EntityField(name = "本企业编号（由scs平台分配）")
    private String enterpriseNo;

    @EntityField(name = "管理组织编号")
    private String manageOrgNo;

    @EntityField(name = "公司/企业编码（basic_company表)")
    private String companyNo;

    @EntityField(name = "供应商编码")
    private String supplierCode;

    @EntityField(name = "供应商名称")
    private String supplierName;

    @EntityField(name = "供应商外语名称")
    private String supplierNameEn;

    @EntityField(name = "统一社会信用代码。公立医院的事业单位法人证号")
    private String unifiedSocialCode;

    @EntityField(name = "供应商基本分类")
    private String supplierCategoryNo;

    @EntityField(name = "助记码")
    private String mnemonicCode;

    @EntityField(name = "是否GSP管控：1-是，0-否")
    private Integer isGspControl;

    @EntityField(name = "是否关联企业：1-是，0-否")
    private Integer isAssociatedEnterprise;

    @EntityField(name = "关联组织编号")
    private String associatedOrgNo;

    @EntityField(name = "关联组织编码")
    private String associatedOrgCode;

    @EntityField(name = "关联组织名称")
    private String associatedOrgName;

    @EntityField(name = "删除标志 0：未删除 1：已删除")
    private Integer deleted;

    @EntityField(name = "来源渠道：local-自行创建 scs-云端")
    private String sourceChannel;

    @EntityField(name = "备注")
    private String remark;

    @EntityField(name = "业务状态：1:正式 2：草稿")
    private Integer businessFlag;

    @EntityField(name = "创建人编号")
    private String createNo;

    @EntityField(name = "制单人")
    private String createName;

    @EntityField(name = "创建时间")
    private String createTime;

    @EntityField(name = "修改时间")
    private String operateNo;

    @EntityField(name = "修改人名称")
    private String operateName;

    @EntityField(name = "修改时间")
    private String operateTime;

    @EntityField(name = "是否散户")
    private Integer retailInvestors;

    @EntityField(name = "供应商类型")
    private String transactionType;

    @EntityField(name = "oms供应商编号")
    private String omsSupplierNo;
}
