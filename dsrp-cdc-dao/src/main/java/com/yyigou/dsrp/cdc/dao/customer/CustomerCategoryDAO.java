package com.yyigou.dsrp.cdc.dao.customer;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yyigou.dsrp.cdc.common.enums.DeletedEnum;
import com.yyigou.dsrp.cdc.dao.config.CdcBaseMapper;
import com.yyigou.dsrp.cdc.dao.customer.entity.CustomerCategory;

import java.util.List;

/**
 * 客户分类DAO
 *
 * @author: Moore
 * @date: 2024/7/17 15:26
 * @version: 1.0.0
 */
public interface CustomerCategoryDAO extends CdcBaseMapper<CustomerCategory> {


    default CustomerCategory getByNo(String enterpriseNo, String no) {
        return selectOne(Wrappers.<CustomerCategory>lambdaQuery()
                .eq(CustomerCategory::getEnterpriseNo, enterpriseNo)
                .eq(CustomerCategory::getNo, no)
                .eq(CustomerCategory::getDeleted, DeletedEnum.UN_DELETE.getValue()));
    }


    default CustomerCategory getByGroupNo(String enterpriseNo, String groupNo) {
        return selectOne(Wrappers.<CustomerCategory>lambdaQuery()
                .eq(CustomerCategory::getEnterpriseNo, enterpriseNo)
                .eq(CustomerCategory::getGroupNo, groupNo)
                .eq(CustomerCategory::getDeleted, DeletedEnum.UN_DELETE.getValue()));
    }

    default List<CustomerCategory> getListByGroupNo(String enterpriseNo, String groupNo) {
        return selectList(Wrappers.<CustomerCategory>lambdaQuery()
                .eq(CustomerCategory::getEnterpriseNo, enterpriseNo)
                .eq(CustomerCategory::getGroupNo, groupNo)
                .eq(CustomerCategory::getDeleted, DeletedEnum.UN_DELETE.getValue()));
    }



    default List<CustomerCategory> getByNoList(String enterpriseNo, List<String> noList) {
        return selectList(Wrappers.<CustomerCategory>lambdaQuery()
                .eq(CustomerCategory::getEnterpriseNo, enterpriseNo)
                .in(CustomerCategory::getNo, noList)
                .eq(CustomerCategory::getDeleted, DeletedEnum.UN_DELETE.getValue()));
    }

    default CustomerCategory findByGroupNoAndParentNo(String enterpriseNo, String groupNo, String parentNo) {
        return selectOne(Wrappers.<CustomerCategory>lambdaQuery()
                .eq(CustomerCategory::getEnterpriseNo, enterpriseNo)
                .eq(CustomerCategory::getNo, groupNo)
                .eq(CustomerCategory::getParentNo, parentNo)
                .eq(CustomerCategory::getDeleted, DeletedEnum.UN_DELETE.getValue()));
    }
}
