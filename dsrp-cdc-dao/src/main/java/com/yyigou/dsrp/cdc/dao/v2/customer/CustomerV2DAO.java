package com.yyigou.dsrp.cdc.dao.v2.customer;

import com.yyigou.dsrp.cdc.dao.config.CdcBaseMapper;
import com.yyigou.dsrp.cdc.dao.v2.customer.entity.CustomerV2;
import com.yyigou.dsrp.cdc.dao.v2.customer.entity.CustomerV2WithBase;
import com.yyigou.dsrp.cdc.dao.v2.customer.entity.CustomerV2WithBiz;
import com.yyigou.dsrp.cdc.dao.v2.supplier.entity.SupplierV2WithBase;
import com.yyigou.dsrp.cdc.model.v2.customer.req.*;
import com.yyigou.dsrp.cdc.model.v2.supplier.req.SupplierFindQueryListPageForTenantReq;
import com.yyigou.dsrp.cdc.model.v2.supplier.req.SupplierReverseUseQueryListPageNoAuthZReq;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

public interface CustomerV2DAO extends CdcBaseMapper<CustomerV2> {
    List<CustomerV2WithBiz> selectManageView(CustomerManageQueryListPageReq req);

    List<CustomerV2WithBiz> selectUseView(CustomerUseQueryListPageReq req);

    Set<String> selectDistinctCustomerCodeByUseOrgNoList(CustomerFindQueryListPageForTenantReq req);

    List<CustomerV2WithBase> selectReverseUseViewNoAuthZ(CustomerReverseUseQueryListPageNoAuthZReq req);

    List<CustomerV2WithBase> findListPageForTenant(CustomerFindQueryListPageForTenantReq req);

    Long getPendingCount(@Param("enterpriseNo") String enterpriseNo, @Param("businessFlag") Integer businessFlag, @Param("deleted") Integer deleted);

    List<CustomerV2> queryStandardInfo(CustomerStandardQueryReq customerStandardQueryReq);

    List<CustomerV2WithBiz> queryWithBizInfo(CustomerStandardQueryReq customerStandardQueryReq);

    List<CustomerV2WithBiz> selectCustomerBizByCodeOrgPair(CustomerCodeOrgPairListReq customerCodeOrgPairListReq);

    void batchUpdateForDA(@Param("list") List<CustomerV2> list);

    List<CustomerV2WithBiz> findListPageByFormal(CustomerPageByFormalQueryReq customerPageByFormalQueryReq);

    List<CustomerV2WithBiz> findNotInPageList(CustomerNotInPageListQueryReq customerNotInPageListQueryReq);

    List<CustomerV2WithBiz> queryPageCustomer(CustomerPageQueryReq customerPageQueryReq);

    List<CustomerV2WithBiz> querySpecifyOrgPageCustomer(CustomerPageQueryBySpecifyOrgReq customerPageQueryBySpecifyOrgReq);

}




