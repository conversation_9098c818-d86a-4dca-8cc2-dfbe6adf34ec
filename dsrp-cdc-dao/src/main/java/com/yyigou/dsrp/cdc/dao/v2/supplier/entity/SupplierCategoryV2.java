package com.yyigou.dsrp.cdc.dao.v2.supplier.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@TableName(value = "bdc_supplier_category")
@Data
public class SupplierCategoryV2 implements Serializable {
    private static final long serialVersionUID = 1L;

    @EntityField(name = "分类编码")
    @TableId(type = IdType.NONE)
    private String no;

    @EntityField(name = "所属企业编号")
    private String enterpriseNo;

    @EntityField(name = "管理组织编号")
    private String manageOrgNo;

    @EntityField(name = "上级分类编号")
    private String parentNo;

    @EntityField(name = "助记码")
    private String mnemonicCode;

    @EntityField(name = "分类编码")
    private String categoryCode;

    @EntityField(name = "分类名称")
    private String categoryName;

    @EntityField(name = "层级")
    private Integer level;

    @EntityField(name = "顶级编号")
    private String topCategoryNo;

    @EntityField(name = "路径")
    private String path;

    @EntityField(name = "描述")
    private String remark;

    @EntityField(name = "状态：1正常 0无效 ")
    private String status;

    @EntityField(name = "排序")
    private Integer sortNum;

    @EntityField(name = "0:未删除，1已删除")
    private Integer deleted;

    @EntityField(name = "创建人编号")
    private String createNo;

    @EntityField(name = "制单人")
    private String createName;

    @EntityField(name = "创建时间")
    private String createTime;

    @EntityField(name = "修改时间")
    private String operateNo;

    @EntityField(name = "修改人名称")
    private String operateName;

    @EntityField(name = "修改时间")
    private String operateTime;

    @EntityField(name = "最后操作时间戳")
    private Date opTimestamp;

    @EntityField(name = "数据记录版本")
    private Integer opRevsion;

    @EntityField(name = "数据操作类型 I:新增 U：更新 D:删除")
    private String opType;

    @EntityField(name = "ys同步标识 0:未同步 1:同步成功 2:同步失败")
    private String ysSyncFlag;

    @EntityField(name = "分组编号")
    private String groupNo;
}