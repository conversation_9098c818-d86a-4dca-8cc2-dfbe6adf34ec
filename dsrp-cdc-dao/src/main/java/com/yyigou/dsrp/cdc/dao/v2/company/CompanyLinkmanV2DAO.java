package com.yyigou.dsrp.cdc.dao.v2.company;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yyigou.dsrp.cdc.common.enums.DeletedEnum;
import com.yyigou.dsrp.cdc.dao.config.CdcBaseMapper;
import com.yyigou.dsrp.cdc.dao.v2.company.entity.CompanyLinkmanV2;

import java.util.List;

public interface CompanyLinkmanV2DAO extends CdcBaseMapper<CompanyLinkmanV2> {
    default List<CompanyLinkmanV2> getCompanyLinkmanListByCompanyNo(String enterpriseNo, String companyNo, String useOrgNo, String linkmanType, String sourceNo) {
        LambdaQueryWrapper<CompanyLinkmanV2> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(CompanyLinkmanV2::getEnterpriseNo, enterpriseNo)
                .eq(CompanyLinkmanV2::getCompanyNo, companyNo)
                .eq(CompanyLinkmanV2::getUseOrgNo, useOrgNo)
                .eq(CompanyLinkmanV2::getLinkmanType, linkmanType)
                .eq(CompanyLinkmanV2::getSourceNo, sourceNo)
                .eq(CompanyLinkmanV2::getDeleted, DeletedEnum.UN_DELETE.getValue());
        return selectList(wrapper);
    }

    default List<CompanyLinkmanV2> getCompanyLinkmanListBySourceNoList(String enterpriseNo, String useOrgNo, String linkmanType, List<String> sourceNoList) {
        LambdaQueryWrapper<CompanyLinkmanV2> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(CompanyLinkmanV2::getEnterpriseNo, enterpriseNo)
                .eq(CompanyLinkmanV2::getUseOrgNo, useOrgNo)
                .eq(CompanyLinkmanV2::getLinkmanType, linkmanType)
                .in(CompanyLinkmanV2::getSourceNo, sourceNoList)
                .eq(CompanyLinkmanV2::getDeleted, DeletedEnum.UN_DELETE.getValue());
        return selectList(wrapper);
    }
}




