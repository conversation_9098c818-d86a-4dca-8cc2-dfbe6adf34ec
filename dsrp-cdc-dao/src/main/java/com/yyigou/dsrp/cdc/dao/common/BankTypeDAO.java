package com.yyigou.dsrp.cdc.dao.common;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yyigou.dsrp.cdc.common.enums.DeletedEnum;
import com.yyigou.dsrp.cdc.dao.common.entity.BankType;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【gc_bank_type】的数据库操作Mapper
 * @createDate 2024-07-16 19:39:12
 * @Entity com.yyigou.dsrp.cdc.dao.common.entity.BankType
 */
public interface BankTypeDAO extends BaseMapper<BankType> {

    default List<BankType> getList() {
        LambdaQueryWrapper<BankType> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(BankType::getDeleted, DeletedEnum.UN_DELETE.getValue());
        return selectList(wrapper);
    }


}




