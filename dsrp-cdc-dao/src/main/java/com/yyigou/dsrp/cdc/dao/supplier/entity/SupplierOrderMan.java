package com.yyigou.dsrp.cdc.dao.supplier.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * @TableName bdc_supplier_order_man
 */
@TableName(value = "bdc_supplier_order_man")
@Data
public class SupplierOrderMan implements Serializable {
    @TableId(type = IdType.AUTO)
    private Long id;

    private String supplierNo;

    private String orgNo;

    private String orgName;

    private String manCode;

    private String orderManNo;

    private String orderManName;

    private Integer isDefault;

    private Integer orderSpecialist;

    private String operateNo;

    private String operateName;

    private String operateTime;

    private Integer deleted;

    private String post;

    private static final long serialVersionUID = 1L;
}