package com.yyigou.dsrp.cdc.dao.supplier;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yyigou.dsrp.cdc.common.enums.DeletedEnum;
import com.yyigou.dsrp.cdc.dao.config.CdcBaseMapper;
import com.yyigou.dsrp.cdc.dao.supplier.entity.SupplierOrderMan;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【bdc_supplier_order_man】的数据库操作Mapper
 * @createDate 2024-07-10 10:58:15
 * @Entity com.yyigou.dsrp.cdc.dao.supplier.entity.SupplierOrderMan
 */
public interface SupplierOrderManDAO extends CdcBaseMapper<SupplierOrderMan> {

    default List<SupplierOrderMan> getSupplierOrderManListBySupplierNoList(String enterpriseNo, List<String> supplierNoList) {
        LambdaQueryWrapper<SupplierOrderMan> wrapper = Wrappers.lambdaQuery();
        wrapper.in(SupplierOrderMan::getSupplierNo, supplierNoList)
                .eq(SupplierOrderMan::getDeleted, DeletedEnum.UN_DELETE.getValue());
        return selectList(wrapper);
    }

    default void deleteSupplierOrderManListBySupplierNoList(String enterpriseNo, List<String> supplierNoList) {
        update(new SupplierOrderMan(), new UpdateWrapper<SupplierOrderMan>().lambda().set(SupplierOrderMan::getDeleted, DeletedEnum.DELETED.getValue())
                .in(SupplierOrderMan::getSupplierNo, supplierNoList)
                .eq(SupplierOrderMan::getDeleted, DeletedEnum.UN_DELETE.getValue()));
    }

    default void deleteSupplierOrderManListBySupplierNoList(String enterpriseNo, List<String> supplierNoList, List<Long> idList) {
        update(new SupplierOrderMan(), new UpdateWrapper<SupplierOrderMan>().lambda().set(SupplierOrderMan::getDeleted, DeletedEnum.DELETED.getValue())
                .in(SupplierOrderMan::getSupplierNo, supplierNoList)
                .in(SupplierOrderMan::getId, idList)
                .eq(SupplierOrderMan::getDeleted, DeletedEnum.UN_DELETE.getValue()));
    }


}




