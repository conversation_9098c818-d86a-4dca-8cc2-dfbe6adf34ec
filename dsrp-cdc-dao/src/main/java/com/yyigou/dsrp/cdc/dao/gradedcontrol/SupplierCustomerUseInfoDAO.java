package com.yyigou.dsrp.cdc.dao.gradedcontrol;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yyigou.ddc.common.util.ValidatorUtils;
import com.yyigou.dsrp.cdc.common.enums.CommonStatusEnum;
import com.yyigou.dsrp.cdc.common.enums.DeletedEnum;
import com.yyigou.dsrp.cdc.dao.config.CdcBaseMapper;
import com.yyigou.dsrp.cdc.dao.gradedcontrol.entity.SupplierCustomerUseInfo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public interface SupplierCustomerUseInfoDAO extends CdcBaseMapper<SupplierCustomerUseInfo> {
    default List<SupplierCustomerUseInfo> findGroupSupplierCustomerBySubTenant(String enterpriseNo, List<String> supplierCustomerCodeList, Integer companyType){
        if (CollectionUtils.isEmpty(supplierCustomerCodeList)) {
            return new ArrayList<>();
        }
        ValidatorUtils.checkEmptyThrowEx(enterpriseNo, "使用组织租户编号为空");
        LambdaQueryWrapper<SupplierCustomerUseInfo> eq = Wrappers.lambdaQuery(SupplierCustomerUseInfo.class)
                .eq(SupplierCustomerUseInfo::getUseEnterpriseNo, enterpriseNo)
                .eq(SupplierCustomerUseInfo::getCompanyType, companyType)
                .in(SupplierCustomerUseInfo::getCompanyCode, supplierCustomerCodeList)
                .eq(SupplierCustomerUseInfo::getStatus, CommonStatusEnum.EFFECTIVE.getValue())
                .eq(SupplierCustomerUseInfo::getDeleted, DeletedEnum.UN_DELETE.getValue());
        return selectList(eq);
    }

    default List<SupplierCustomerUseInfo> findGroupSupplierCustomerUseList(String groupEnterpriseNo, List<String> supplierCustomerCodeList,Integer companyType){
        if (CollectionUtils.isEmpty(supplierCustomerCodeList)) {
            return new ArrayList<>();
        }
        ValidatorUtils.checkEmptyThrowEx(groupEnterpriseNo, "集团租户编号为空");
        LambdaQueryWrapper<SupplierCustomerUseInfo> eq = Wrappers.lambdaQuery(SupplierCustomerUseInfo.class)
                .eq(SupplierCustomerUseInfo::getManageEnterpriseNo, groupEnterpriseNo)
                .eq(SupplierCustomerUseInfo::getCompanyType, companyType)
                .in(SupplierCustomerUseInfo::getCompanyCode, supplierCustomerCodeList)
                .eq(SupplierCustomerUseInfo::getStatus, CommonStatusEnum.EFFECTIVE.getValue())
                .eq(SupplierCustomerUseInfo::getDeleted, DeletedEnum.UN_DELETE.getValue());
        return selectList(eq);
    }

    default List<String> findUseEnterpriseNoBySupplier(String groupEnterpriseNo, String supplierCode){
        if(StringUtils.isEmpty(supplierCode)){
            return new ArrayList<>();
        }
        Assert.isTrue(StringUtils.isNotEmpty(groupEnterpriseNo), "集团租户编号为空");
        LambdaQueryWrapper<SupplierCustomerUseInfo> eq = Wrappers.lambdaQuery(SupplierCustomerUseInfo.class)
                .eq(SupplierCustomerUseInfo::getGroupEnterpriseNo, groupEnterpriseNo)
                .eq(SupplierCustomerUseInfo::getCompanyCode, supplierCode)
                .eq(SupplierCustomerUseInfo::getStatus, CommonStatusEnum.EFFECTIVE.getValue())
                .eq(SupplierCustomerUseInfo::getDeleted, DeletedEnum.UN_DELETE.getValue())
                .eq(SupplierCustomerUseInfo::getCompanyType, 1)
                .select(SupplierCustomerUseInfo::getUseEnterpriseNo)
                ;


        List<SupplierCustomerUseInfo> list = selectList(eq);
        List<String> useEnterpriseNoList = list.stream().map(SupplierCustomerUseInfo::getUseEnterpriseNo).distinct().collect(Collectors.toList());
        useEnterpriseNoList.add(groupEnterpriseNo);
        return useEnterpriseNoList;
    }
}
