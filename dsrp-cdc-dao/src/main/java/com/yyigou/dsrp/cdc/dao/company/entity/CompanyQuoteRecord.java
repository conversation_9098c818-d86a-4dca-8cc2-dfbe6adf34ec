package com.yyigou.dsrp.cdc.dao.company.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * bdc_company_quote_record
 *
 * <AUTHOR>
@Data
@TableName(value = "bdc_company_quote_record")
public class CompanyQuoteRecord implements Serializable {
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 集团租户编号
     */
    private String groupEnterpriseNo;

    /**
     * 档案管理权租户编号
     */
    private String manageEnterpriseNo;

    /**
     * 集团企业编号
     */
    private String manageCompanyNo;

    /**
     * 使用组织编号
     */
    private String useOrgNo;

    /**
     * 使用组织名称
     */
    private String useOrgName;

    /**
     * 使用组织租户编号
     */
    private String useEnterpriseNo;

    /**
     * 使用组织企业编号
     */
    private String useCompanyNo;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 是否有效  1是 0否
     */
    private Integer status;

    /**
     * 是否删除  1是 0 否
     */
    private Integer deleted;

    /**
     * 创建人编号
     */
    private String createNo;

    /**
     * 创建人名称
     */
    private String createName;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 修改人编号
     */
    private String modifyNo;

    /**
     * 修改人名称
     */
    private String modifyName;

    /**
     * 修改时间
     */
    private String modifyTime;

    private static final long serialVersionUID = 1L;
}