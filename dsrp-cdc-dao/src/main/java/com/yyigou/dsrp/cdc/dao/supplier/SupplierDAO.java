package com.yyigou.dsrp.cdc.dao.supplier;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yyigou.dsrp.cdc.common.enums.DeletedEnum;
import com.yyigou.dsrp.cdc.dao.config.CdcBaseMapper;
import com.yyigou.dsrp.cdc.dao.supplier.entity.Supplier;
import com.yyigou.dsrp.cdc.model.supplier.req.SupplierMultiOrgQueryReq;

import java.util.List;

/**
 * <p>
 * 供应商表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-21
 */
public interface SupplierDAO extends CdcBaseMapper<Supplier> {

    default List<Supplier> getSupplierByCodeList(String enterpriseNo, List<String> supplierCodeList) {
        LambdaQueryWrapper<Supplier> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(Supplier::getEnterpriseNo, enterpriseNo)
                .in(Supplier::getSupplierCode, supplierCodeList)
                .eq(Supplier::getDeleted, DeletedEnum.UN_DELETE.getValue());
        return selectList(wrapper);
    }

    default List<Supplier> findSupplierByCompanyNo(String enterpriseNo, String companyNo) {
        return selectList(Wrappers.lambdaQuery(Supplier.class)
                .eq(Supplier::getEnterpriseNo, enterpriseNo)
                .eq(Supplier::getCompanyNo, companyNo)
                .eq(Supplier::getDeleted, DeletedEnum.UN_DELETE.getValue()));
    }

    default Supplier getSupplierBySupplierCode(String enterpriseNo, String supplierCode) {
        return selectOne(Wrappers.lambdaQuery(Supplier.class)
                .eq(Supplier::getEnterpriseNo, enterpriseNo)
                .eq(Supplier::getSupplierCode, supplierCode)
                .eq(Supplier::getDeleted, DeletedEnum.UN_DELETE.getValue()));
    }


    default Supplier getSupplierByNoEnterpriseNo(String supplierNo) {
        LambdaQueryWrapper<Supplier> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(Supplier::getSupplierNo, supplierNo)
                .eq(Supplier::getDeleted, DeletedEnum.UN_DELETE.getValue());
        return selectOne(wrapper);
    }

    default Supplier getSupplierByNo(String enterpriseNo, String supplierNo) {
        LambdaQueryWrapper<Supplier> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(Supplier::getEnterpriseNo, enterpriseNo)
                .eq(Supplier::getSupplierNo, supplierNo)
                .eq(Supplier::getDeleted, DeletedEnum.UN_DELETE.getValue());
        return selectOne(wrapper);
    }

    default List<Supplier> getSupplierByNoList(String enterpriseNo, List<String> supplierNoList) {
        LambdaQueryWrapper<Supplier> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(Supplier::getEnterpriseNo, enterpriseNo)
                .in(Supplier::getSupplierNo, supplierNoList)
                .eq(Supplier::getDeleted, DeletedEnum.UN_DELETE.getValue());
        return selectList(wrapper);
    }

    default Supplier getSupplierBySupplierName(String enterpriseNo, String supplierName) {
        LambdaQueryWrapper<Supplier> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(Supplier::getEnterpriseNo, enterpriseNo)
                .eq(Supplier::getSupplierName, supplierName)
                .eq(Supplier::getDeleted, DeletedEnum.UN_DELETE.getValue());
        return selectOne(wrapper);
    }


    /**
     * 批量更新多组织中同一供应商档案的状态
     * @param enterpriseNoList
     * @param supplierCode
     * @param controlStatus
     * @return
     */
    default int batchUpdateMultiOrganizeSupplierStatus(List<String> enterpriseNoList, String supplierCode, String controlStatus) {
        if(CollectionUtils.isEmpty(enterpriseNoList)) {
            return 0;
        }
        LambdaUpdateWrapper<Supplier> wrapper = Wrappers.lambdaUpdate();
        wrapper.set(Supplier::getControlStatus,controlStatus)
                .eq(Supplier::getSupplierCode, supplierCode)
                .eq(Supplier::getDeleted, DeletedEnum.UN_DELETE.getValue())
                .in(Supplier::getEnterpriseNo,enterpriseNoList);
        return update(null,wrapper);
    }

    List<Supplier> selectMultiOrgSupplierList(SupplierMultiOrgQueryReq req);
}
