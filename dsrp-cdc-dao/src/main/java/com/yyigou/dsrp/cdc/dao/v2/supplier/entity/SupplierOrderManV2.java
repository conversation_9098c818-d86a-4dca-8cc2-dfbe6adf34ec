package com.yyigou.dsrp.cdc.dao.v2.supplier.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;

@TableName(value = "bdc_supplier_order_man")
@Data
public class SupplierOrderManV2 implements Serializable {
    private static final long serialVersionUID = 1L;

    @EntityField(name = "ID")
    @TableId(type = IdType.AUTO)
    private Long id;

    @EntityField(name = "租户编号")
    private String enterpriseNo;

    @EntityField(name = "使用组织编号")
    private String useOrgNo;

    @EntityField(name = "供应商编号")
    private String supplierNo;

    @EntityField(name = "供应商编码")
    private String supplierCode;

//    private String orgNo;

//    private String orgName;

    @EntityField(name = "部门编号")
    private String deptNo;

    @EntityField(name = "部门名称")
    private String deptName;

    @EntityField(name = "业务员编码")
    private String manCode;

    @EntityField(name = "业务员编号")
    private String orderManNo;

    @EntityField(name = "业务员姓名")
    private String orderManName;

    @EntityField(name = "默认业务员  2:是 1：默认  0：否 ")
    private Integer isDefault;

    @EntityField(name = "默认订单专员  2:是 1：默认  0：否 ")
    private Integer orderSpecialist;

    @EntityField(name = "操作人编号")
    private String operateNo;

    @EntityField(name = "操作人名称")
    private String operateName;

    @EntityField(name = "操作时间")
    private String operateTime;

    @EntityField(name = "是否删除 1是 0否")
    private Integer deleted;

    @EntityField(name = "职位")
    private String post;
}