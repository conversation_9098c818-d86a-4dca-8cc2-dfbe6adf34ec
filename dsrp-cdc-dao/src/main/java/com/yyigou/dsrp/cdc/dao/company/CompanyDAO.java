package com.yyigou.dsrp.cdc.dao.company;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yyigou.ddc.common.util.ValidatorUtils;
import com.yyigou.dsrp.cdc.common.enums.CommonStatusEnum;
import com.yyigou.dsrp.cdc.common.enums.DeletedEnum;
import com.yyigou.dsrp.cdc.dao.company.entity.Company;
import com.yyigou.dsrp.cdc.dao.config.CdcBaseMapper;
import com.yyigou.dsrp.cdc.model.company.req.CompanyQueryListPageReq;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * <p>
 * 客户表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-16
 */
public interface CompanyDAO extends CdcBaseMapper<Company> {

    /**
     * 根据租户编码和企业名称查询企业列表
     *
     * @param enterpriseNo
     * @param companyName
     * @return: {@link List< Company>}
     */
    default List<Company> findByEnterpriseNoAndCompanyName(String enterpriseNo, String companyName) {
        ValidatorUtils.checkEmptyThrowEx(enterpriseNo, "租户编号不能为空");
        ValidatorUtils.checkEmptyThrowEx(companyName, "企业名称不能为空");

        LambdaQueryWrapper<Company> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Company::getEnterpriseNo, enterpriseNo)
                .eq(Company::getCompanyName, companyName)
                .eq(Company::getStatus, CommonStatusEnum.EFFECTIVE.getValue())
                .eq(Company::getDeleted, DeletedEnum.UN_DELETE.getValue());
        return selectList(wrapper);
    }

    default List<Company> findByEnterpriseNoAndCompanyName(String enterpriseNo, List<String> companyNameList) {
        ValidatorUtils.checkEmptyThrowEx(enterpriseNo, "租户编号不能为空");
        ValidatorUtils.checkEmptyThrowEx(companyNameList, "企业名称不能为空");

        LambdaQueryWrapper<Company> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Company::getEnterpriseNo, enterpriseNo)
                .in(Company::getCompanyName, companyNameList)
                .eq(Company::getStatus, CommonStatusEnum.EFFECTIVE.getValue())
                .eq(Company::getDeleted, DeletedEnum.UN_DELETE.getValue());
        return selectList(wrapper);
    }

    /**
     * 根据租户编号和统一社会信用代码查询企业列表
     *
     * @param enterpriseNo
     * @param unifiedSocialCode
     * @return: {@link List< Company>}
     */
    default List<Company> findByEnterpriseNoAndUnifiedSocialCode(String enterpriseNo, String unifiedSocialCode) {
        ValidatorUtils.checkEmptyThrowEx(enterpriseNo, "租户编号不能为空");
        ValidatorUtils.checkEmptyThrowEx(unifiedSocialCode, "统一社会信用代码不能为空");

        LambdaQueryWrapper<Company> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Company::getEnterpriseNo, enterpriseNo)
                .eq(Company::getUnifiedSocialCode, unifiedSocialCode)
                .eq(Company::getStatus, CommonStatusEnum.EFFECTIVE.getValue())
                .eq(Company::getDeleted, DeletedEnum.UN_DELETE.getValue());
        return selectList(wrapper);
    }

    default List<Company> findByEnterpriseNoAndUnifiedSocialCodeList(String enterpriseNo, List<String> unifiedSocialCodeList) {
        ValidatorUtils.checkEmptyThrowEx(enterpriseNo, "租户编号不能为空");
        ValidatorUtils.checkEmptyThrowEx(unifiedSocialCodeList, "统一社会信用代码不能为空");
        LambdaQueryWrapper<Company> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Company::getEnterpriseNo, enterpriseNo)
                .in(Company::getUnifiedSocialCode, unifiedSocialCodeList)
                .eq(Company::getStatus, CommonStatusEnum.EFFECTIVE.getValue())
                .eq(Company::getDeleted, DeletedEnum.UN_DELETE.getValue());
        return selectList(wrapper);
    }

    default List<Company> findByEnterpriseNoAndUnifiedSocialCodeAndCompanyName(String enterpriseNo, String unifiedSocialCode, String companyName) {
        ValidatorUtils.checkEmptyThrowEx(enterpriseNo, "租户编号不能为空");
        ValidatorUtils.checkEmptyThrowEx(unifiedSocialCode, "统一社会信用代码不能为空");
        ValidatorUtils.checkEmptyThrowEx(companyName, "企业名称不能为空");

        LambdaQueryWrapper<Company> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Company::getEnterpriseNo, enterpriseNo)
                .eq(Company::getUnifiedSocialCode, unifiedSocialCode)
                .eq(Company::getCompanyName, companyName)
                .eq(Company::getStatus, CommonStatusEnum.EFFECTIVE.getValue())
                .eq(Company::getDeleted, DeletedEnum.UN_DELETE.getValue());
        return selectList(wrapper);
    }

    /**
     * 根据租户编码和企业编号查询企业列表
     *
     * @param enterpriseNo
     * @param companyNo
     * @return: {@link Company}
     */
    default Company findByEnterpriseNoAndCompanyNo(String enterpriseNo, String companyNo) {
        ValidatorUtils.checkEmptyThrowEx(enterpriseNo, "租户编号不能为空");
        ValidatorUtils.checkEmptyThrowEx(companyNo, "企业编号不能为空");

        LambdaQueryWrapper<Company> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Company::getEnterpriseNo, enterpriseNo)
                .eq(Company::getCompanyNo, companyNo)
                .eq(Company::getStatus, CommonStatusEnum.EFFECTIVE.getValue())
                .eq(Company::getDeleted, DeletedEnum.UN_DELETE.getValue());
        return selectOne(wrapper);
    }

    /**
     * 根据租户编码和企业编号查询企业列表
     *
     * @param enterpriseNo
     * @param companyName
     * @return: {@link Company}
     */
    default List<Company> findByEnterpriseNoAndCompanyList(String enterpriseNo, String companyName) {
        ValidatorUtils.checkEmptyThrowEx(enterpriseNo, "租户编号不能为空");
        ValidatorUtils.checkEmptyThrowEx(companyName, "企业编号不能为空");

        LambdaQueryWrapper<Company> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Company::getEnterpriseNo, enterpriseNo)
                .eq(Company::getCompanyName, companyName)
                .eq(Company::getStatus, CommonStatusEnum.EFFECTIVE.getValue())
                .eq(Company::getDeleted, DeletedEnum.UN_DELETE.getValue());
        return selectList(wrapper);
    }

    default List<Company> findByEnterpriseNoAndCompanyNoList(String enterpriseNo, List<String> companyNoList) {
        return selectList(Wrappers.<Company>lambdaQuery()
                .eq(Company::getEnterpriseNo, enterpriseNo)
                .in(Company::getCompanyNo, companyNoList)
                .eq(Company::getStatus, CommonStatusEnum.EFFECTIVE.getValue())
                .eq(Company::getDeleted, DeletedEnum.UN_DELETE.getValue()));
    }

    default List<Company> findCompanyListPage(CompanyQueryListPageReq queryReq) {
        return selectList(Wrappers.<Company>lambdaQuery()
                .eq(Company::getEnterpriseNo, queryReq.getEnterpriseNo())
                .eq(Company::getStatus, CommonStatusEnum.EFFECTIVE.getValue())
                .eq(Company::getDeleted, DeletedEnum.UN_DELETE.getValue())
                .in(CollectionUtils.isNotEmpty(queryReq.getCompanyNoList()), Company::getCompanyNo, queryReq.getCompanyNoList())
                .like(StringUtils.isNotBlank(queryReq.getPartnership()), Company::getPartnership, queryReq.getPartnership())
                .like(StringUtils.isNotEmpty(queryReq.getKeywords()), Company::getCompanyName, queryReq.getKeywords()));
    }
}
