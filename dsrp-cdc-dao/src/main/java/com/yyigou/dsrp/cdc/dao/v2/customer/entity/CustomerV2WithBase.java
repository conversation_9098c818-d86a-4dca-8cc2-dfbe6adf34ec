package com.yyigou.dsrp.cdc.dao.v2.customer.entity;

import com.yyigou.ddc.common.service.annotation.EntityField;
import com.yyigou.dsrp.cdc.dao.v2.supplier.entity.SupplierV2;
import lombok.Data;

import java.io.Serializable;

@Data
public class CustomerV2WithBase extends CustomerV2 implements Serializable {
    private static final long serialVersionUID = 1L;

    @EntityField(name = "分派表主键")
    private Long baseId;

    @EntityField(name = "使用组织编号")
    private String useOrgNo;
}
