package com.yyigou.dsrp.cdc.dao.company;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yyigou.dsrp.cdc.common.enums.DeletedEnum;
import com.yyigou.dsrp.cdc.dao.company.entity.CompanyQuoteRecord;
import com.yyigou.dsrp.cdc.dao.config.CdcBaseMapper;

import java.util.List;

/**
 * 企业引用记录 Mapper 接口
 *
 * @author: Moore
 * @date: 2024/7/8 13:34
 * @version: 1.0.0
 */
public interface CompanyQuoteRecordDAO extends CdcBaseMapper<CompanyQuoteRecord> {

    default List<CompanyQuoteRecord> findCompanyAssignOrgList(String groupEnterpriseNo, String manageEnterpriseNo, List<String> manageCompanyNoList) {
        return selectList(Wrappers.<CompanyQuoteRecord>lambdaQuery()
                .eq(CompanyQuoteRecord::getGroupEnterpriseNo, groupEnterpriseNo)
                .eq(CompanyQuoteRecord::getManageEnterpriseNo, manageEnterpriseNo)
                .in(CompanyQuoteRecord::getManageCompanyNo, manageCompanyNoList)
                .eq(CompanyQuoteRecord::getDeleted, DeletedEnum.UN_DELETE.getValue()));
    }


    default List<CompanyQuoteRecord> findCompanyByUseEnterprise(String groupEnterpriseNo, String useEnterpriseNo, List<String> manageCompanyNoList) {
        return selectList(Wrappers.<CompanyQuoteRecord>lambdaQuery()
                .eq(CompanyQuoteRecord::getGroupEnterpriseNo, groupEnterpriseNo)
                .eq(CompanyQuoteRecord::getUseEnterpriseNo, useEnterpriseNo)
                .in(CompanyQuoteRecord::getManageCompanyNo, manageCompanyNoList)
                .eq(CompanyQuoteRecord::getDeleted, DeletedEnum.UN_DELETE.getValue()));
    }
}




