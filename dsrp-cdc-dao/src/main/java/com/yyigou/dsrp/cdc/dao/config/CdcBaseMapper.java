package com.yyigou.dsrp.cdc.dao.config;

import com.alibaba.dubbo.common.utils.ReflectUtils;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yyigou.ddc.common.exception.BusinessException;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.session.ExecutorType;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.jetbrains.annotations.Nullable;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 自定义接口方法
 *
 * @Author: fudj
 * @CreateTime: 2023-03-28  10:40
 * @Version: 1.0
 */
public interface CdcBaseMapper<T> extends BaseMapper<T> {
    /**
     * 通过id 与 企业编号 修改内容
     *
     * @param enterpriseNo 企业编号
     * @param entity       类对象
     * @return
     */
    default int updateById(String enterpriseNo, T entity) {
        Object id = getTableId(entity);
        if (id == null || StringUtils.isBlank(enterpriseNo)) {
            return 0;
        }
        QueryWrapper<T> wrapper = new QueryWrapper<T>();
        wrapper.eq("id", id);
        wrapper.eq("enterprise_no", enterpriseNo);
        return update(entity, wrapper);
    }

    /**
     * 通过id 与 企业编号 修改内容
     *
     * @param enterpriseNo 企业编号
     * @param entity       类对象
     * @return
     */
    default int updateByIdList(String enterpriseNo, List<? extends Object> idList, T entity) {
        if (CollectionUtils.isEmpty(idList) || StringUtils.isBlank(enterpriseNo)) {
            return 0;
        }
        QueryWrapper<T> wrapper = new QueryWrapper<T>();
        wrapper.in("id", idList);
        wrapper.eq("enterprise_no", enterpriseNo);
        return update(entity, wrapper);
    }

    /**
     * 通过id与租户编号获取单条信息
     *
     * @param enterpriseNo 企业编号
     * @param id           主键
     * @return
     */
    default T getById(String enterpriseNo, Object id) {
        if (id == null) {
            return null;
        }
        QueryWrapper<T> selectWrapper = new QueryWrapper<T>();
        selectWrapper.eq("id", id);
        selectWrapper.eq("enterprise_no", enterpriseNo);
        T t = selectOne(selectWrapper);
        return t;
    }

    /**
     * 通过id与租户编号获取单条信息
     *
     * @param enterpriseNo 企业编号
     * @param idList       主键集合
     * @return
     */
    default List<T> findListByIdList(String enterpriseNo, List<? extends Object> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return new ArrayList<>();
        }
        QueryWrapper<T> selectWrapper = new QueryWrapper<T>();
        selectWrapper.in("id", idList);
        selectWrapper.eq("enterprise_no", enterpriseNo);
        return selectList(selectWrapper);
    }

    /**
     * 通过id与租户编号删除单条信息
     *
     * @param enterpriseNo 企业编号
     * @param id           主键
     * @return
     */
    default Integer deleteById(String enterpriseNo, Object id) {
        if (id == null) {
            return 0;
        }
        QueryWrapper<T> selectWrapper = new QueryWrapper<T>();
        selectWrapper.eq("id", id);
        selectWrapper.eq("enterprise_no", enterpriseNo);
        return delete(selectWrapper);
    }


    /**
     * 批量新增
     * @param list 需要新增的类
     * @return 需要集合
     */
    default List<T> addBatch(List<T> list) {
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        // 使用批处理实现批量插入
        SqlSessionFactory sqlSessionFactory = SpringContextUtil.getBean(SqlSessionFactory.class);
        SqlSession sqlSession = sqlSessionFactory.openSession(ExecutorType.BATCH, false);
        list.forEach(this::insert);
        sqlSession.commit();
        sqlSession.clearCache();
        return list;
    }
    /**
     * 批量新增
     * @param list 需要新增的类
     * @return 需要集合
     */
    default List<T> updateByIdBatch(List<T> list) {
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        // 使用批处理实现批量插入
        SqlSessionFactory sqlSessionFactory = SpringContextUtil.getBean(SqlSessionFactory.class);
        SqlSession sqlSession = sqlSessionFactory.openSession(ExecutorType.BATCH, false);
        list.forEach(this::updateById);
        sqlSession.commit();
        sqlSession.clearCache();
        return list;
    }
    /**
     * 获取主键信息
     *
     * @param entity
     * @return
     */
    @Nullable
    default Object getTableId(T entity) {
        Map<String, Field> beanPropertyFields = ReflectUtils.getBeanPropertyFields(entity.getClass());
        for (Field value : beanPropertyFields.values()) {
            TableId annotation = value.getAnnotation(TableId.class);
            if (annotation != null) {
                try {
                    return value.get(entity);
                } catch (IllegalAccessException e) {
                    throw new RuntimeException("请输入id");
                }
            }
        }
        return new BusinessException("请先用@TableId注解标明主键");
    }

    /**
     * 因为MybatisPlus的updateById和update方法默认不更新null字段，所以增加这个方法
     * @param enterpriseNo
     * @param entity
     * @return
     */
    default int updateAllById(String enterpriseNo, T entity) {
        Object id = getTableId(entity);
        if (id == null || StringUtils.isBlank(enterpriseNo)) {
            return 0;
        }
        UpdateWrapper<T> wrapper = new UpdateWrapper<T>();
        wrapper.eq("id", id);
        wrapper.eq("enterprise_no", enterpriseNo);
        // 获取Class对象
        Class<?> clazz = entity.getClass();
        // 获取所有声明的字段
        Field[] fields = clazz.getDeclaredFields();
        for (Field field : fields) {

            // 允许访问私有字段
            field.setAccessible(true);
            String lineName = field.getName().replaceAll("([a-z])([A-Z])", "$1_$2").toLowerCase();

            if("serial_version_uid".equals(lineName)){
                continue;
            }
            try {
                wrapper.set(lineName, field.get(entity));
            } catch (IllegalAccessException e) {
                throw new RuntimeException(e);
            }
        }

        return update(entity, wrapper);
    }
}
