package com.yyigou.dsrp.cdc.dao.customer.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @TableName bdc_customer_invoice
 */
@TableName(value = "bdc_customer_invoice")
@Data
public class CustomerInvoice implements Serializable {
    @TableId(type = IdType.AUTO)
    private Long id;

    private String invoiceCode;
    private String enterpriseNo;

    private String customerNo;

    private Integer type;

    private String taxNo;

    private String invoiceTitle;

    private String phone;

    private String invoicePhone;

    private String email;

    private String regionCode;

    private String regionName;

    private String address;

    private String bankDeposit;

    private String bankAccount;

    private String status;

    private Integer isDefault;

    private String operateNo;

    private String operateName;

    private String operateTime;

    private Date opTimestamp;

    private Integer opRevsion;

    private String opType;

    private String addregionCode;

    private String requirement;

    private String outSystemId;

    private static final long serialVersionUID = 1L;
}