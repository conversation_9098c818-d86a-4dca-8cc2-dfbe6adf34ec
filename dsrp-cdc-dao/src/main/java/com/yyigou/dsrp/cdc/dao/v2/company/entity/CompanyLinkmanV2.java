package com.yyigou.dsrp.cdc.dao.v2.company.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @TableName bdc_company_linkman
 */
@TableName(value = "bdc_company_linkman")
@Data
public class CompanyLinkmanV2 implements Serializable {
    private static final long serialVersionUID = 1L;
    @TableId(type = IdType.AUTO)
    private Long id;
    private String enterpriseNo;
    private String useOrgNo;
    private String companyNo;
    private String linkCode;
    private String linkman;
    private String position;
    private String mobilePhone;
    private String fixedPhone;
    private String sex;
    private String email;
    private String qq;
    private String wx;
    private Integer status;
    private Integer deleted;
    private String createNo;
    private String createName;
    private String createTime;
    private String operateNo;
    private String operateName;
    private String operateTime;
    private Date opTimestamp;
    private Integer opRevsion;
    private String opType;
    private Integer isDefault;
    private String linkmanType;
    private String sourceNo;
//    private String sourceCode;
}