package com.yyigou.dsrp.cdc.dao.v2.company;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yyigou.dsrp.cdc.common.enums.DeletedEnum;
import com.yyigou.dsrp.cdc.dao.config.CdcBaseMapper;
import com.yyigou.dsrp.cdc.dao.v2.company.entity.CompanyShippingAddressV2;

import java.util.List;

public interface CompanyShippingAddressV2DAO extends CdcBaseMapper<CompanyShippingAddressV2> {
    default List<CompanyShippingAddressV2> getCompanyShippingAddressBySourceNo(String enterpriseNo, String companyNo, String useOrgNo, String linkmanType, String sourceNo) {
        LambdaQueryWrapper<CompanyShippingAddressV2> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(CompanyShippingAddressV2::getEnterpriseNo, enterpriseNo)
                .eq(CompanyShippingAddressV2::getCompanyNo, companyNo)
                .eq(CompanyShippingAddressV2::getUseOrgNo, useOrgNo)
                .eq(CompanyShippingAddressV2::getLinkaddType, linkmanType)
                .eq(CompanyShippingAddressV2::getSourceNo, sourceNo)
                .eq(CompanyShippingAddressV2::getDeleted, DeletedEnum.UN_DELETE.getValue());
        return selectList(wrapper);
    }

    default List<CompanyShippingAddressV2> getCompanyShippingAddressBySourceNoList(String enterpriseNo, String useOrgNo, String linkmanType, List<String> sourceNoList) {
        LambdaQueryWrapper<CompanyShippingAddressV2> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(CompanyShippingAddressV2::getEnterpriseNo, enterpriseNo)
                .eq(CompanyShippingAddressV2::getUseOrgNo, useOrgNo)
                .eq(CompanyShippingAddressV2::getLinkaddType, linkmanType)
                .in(CompanyShippingAddressV2::getSourceNo, sourceNoList)
                .eq(CompanyShippingAddressV2::getDeleted, DeletedEnum.UN_DELETE.getValue());
        return selectList(wrapper);
    }
}




