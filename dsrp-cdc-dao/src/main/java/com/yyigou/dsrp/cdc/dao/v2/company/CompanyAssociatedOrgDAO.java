package com.yyigou.dsrp.cdc.dao.v2.company;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yyigou.dsrp.cdc.common.enums.DeletedEnum;
import com.yyigou.dsrp.cdc.dao.config.CdcBaseMapper;
import com.yyigou.dsrp.cdc.dao.v2.company.entity.CompanyAssociatedOrg;

/**
* <AUTHOR>
* @description 针对表【bdc_company_associated_org(企业组织关联表)】的数据库操作Mapper
* @createDate 2025-05-21 16:50:47
* @Entity com.yyigou.dsrp.cdc.dao.v2.company.entity.CompanyAssociatedOrg
*/
public interface CompanyAssociatedOrgDAO extends CdcBaseMapper<CompanyAssociatedOrg>  {


    default CompanyAssociatedOrg selectBySourceTypeAndCode(String enterpriseNo, Integer sourceType, String sourceCode) {
        return this.selectOne(Wrappers.lambdaQuery(CompanyAssociatedOrg.class)
                .eq(CompanyAssociatedOrg::getEnterpriseNo, enterpriseNo)
                .eq(CompanyAssociatedOrg::getDeleted, DeletedEnum.UN_DELETE)
                .eq(CompanyAssociatedOrg::getSourceCode, sourceCode)
                .eq(CompanyAssociatedOrg::getSourceType, sourceType)
        );
    }

}




