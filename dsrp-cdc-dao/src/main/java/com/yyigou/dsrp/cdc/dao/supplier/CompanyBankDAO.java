package com.yyigou.dsrp.cdc.dao.supplier;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yyigou.dsrp.cdc.common.enums.DeletedEnum;
import com.yyigou.dsrp.cdc.dao.config.CdcBaseMapper;
import com.yyigou.dsrp.cdc.dao.supplier.entity.CompanyBank;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【bdc_supplier_bank】的数据库操作Mapper
 * @createDate 2024-07-10 10:51:54
 * @Entity com.yyigou.dsrp.cdc.dao.supplier.entity.BdcSupplierBank
 */
public interface CompanyBankDAO extends CdcBaseMapper<CompanyBank> {
    default List<CompanyBank> getCompanyBankListBySourceNoList(String enterpriseNo, String linkmanType, List<String> sourceNoList) {
        LambdaQueryWrapper<CompanyBank> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(CompanyBank::getEnterpriseNo, enterpriseNo)
                .eq(CompanyBank::getLinkaddType, linkmanType)
                .in(CompanyBank::getSourceNo, sourceNoList)
                .eq(CompanyBank::getDeleted, DeletedEnum.UN_DELETE.getValue());
        return selectList(wrapper);
    }


    default void deleteCompanyBankListBySourceNoList(String enterpriseNo, String linkmanType, List<String> sourceNoList) {
        update(new CompanyBank(), new UpdateWrapper<CompanyBank>().lambda().set(CompanyBank::getDeleted, DeletedEnum.DELETED.getValue())
                .eq(CompanyBank::getEnterpriseNo, enterpriseNo)
                .eq(CompanyBank::getLinkaddType, linkmanType)
                .in(CompanyBank::getSourceNo, sourceNoList)
                .eq(CompanyBank::getDeleted, DeletedEnum.UN_DELETE.getValue()));
    }

    default void deleteCompanyBankListBySourceNoList(String enterpriseNo, String linkmanType, List<String> sourceNoList, List<Long> idList) {
        update(new CompanyBank(), new UpdateWrapper<CompanyBank>().lambda().set(CompanyBank::getDeleted, DeletedEnum.DELETED.getValue())
                .eq(CompanyBank::getEnterpriseNo, enterpriseNo)
                .eq(CompanyBank::getLinkaddType, linkmanType)
                .in(CompanyBank::getSourceNo, sourceNoList)
                .in(CompanyBank::getId, idList)
                .eq(CompanyBank::getDeleted, DeletedEnum.UN_DELETE.getValue()));
    }
}




