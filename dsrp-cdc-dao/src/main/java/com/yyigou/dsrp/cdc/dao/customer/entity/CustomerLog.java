package com.yyigou.dsrp.cdc.dao.customer.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;


/**
 * CustomerLog 
 *
 * 客户日志
 */
@Data
@TableName("bdc_customer_log")
public class CustomerLog implements Serializable {
    private static final long serialVersionUID = -5060544129091735201L;
    /**
     * 客户编号
     */
    @EntityField(name = "客户编号")
	private String customerNo;
     /**
     * 操作内容
     */
    @EntityField(name = "操作内容")
	private String operateContent;
     /**
     * 操作员编号
     */
    @EntityField(name = "操作员编号")
	private String operateNo;
     /**
     * 操作员
     */
    @EntityField(name = "操作员")
	private String operateName;
     /**
     * 
     */
    @EntityField(name = "")
	private String operateTime;
}