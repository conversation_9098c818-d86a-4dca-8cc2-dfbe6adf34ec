package com.yyigou.dsrp.cdc.dao.v2.customer.entity;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;

@Data
public class CustomerApplyWithDetail extends CustomerApply implements Serializable {
    private static final long serialVersionUID = 1L;

    @EntityField(name = "审批通过后的详情JSON")
    private String approveContent;

    @EntityField(name = "申请时填写的详情JSON")
    private String applyContent;
}
