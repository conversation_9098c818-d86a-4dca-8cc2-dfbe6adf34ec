package com.yyigou.dsrp.cdc.dao.v2.company.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @TableName bdc_supplier_bank
 */
@TableName(value = "bdc_supplier_bank")
@Data
public class CompanyBankV2 implements Serializable {
    private static final long serialVersionUID = 1L;
    @TableId(type = IdType.AUTO)
    private Long id;

    private String enterpriseNo;

    private String supplierNo;

    private String bankCode;

    private String companyNo;

    private String bankType;

    private String bankTypeName;

    private String openBank;

    private String accountNo;

    private String accountName;

    private Integer accountType;

    private String linkPerson;

    private String linkPhone;

    private Integer status;

    private Integer isDefault;

    private String linkaddType;

    private String sourceNo;

    private Integer deleted;

    private String createNo;

    private String createName;

    private String createTime;

    private String operateNo;

    private String operateName;

    private String operateTime;

    private Date opTimestamp;

    private Integer opRevsion;

    private String opType;

    private String manageOrgNo;

//    private String currency;

    private String currencyId;
}