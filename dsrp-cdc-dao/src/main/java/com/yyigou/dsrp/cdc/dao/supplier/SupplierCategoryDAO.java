package com.yyigou.dsrp.cdc.dao.supplier;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yyigou.dsrp.cdc.common.enums.DeletedEnum;
import com.yyigou.dsrp.cdc.dao.config.CdcBaseMapper;
import com.yyigou.dsrp.cdc.dao.supplier.entity.SupplierCategory;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【bdc_supplier_category(行业分类)】的数据库操作Mapper
 * @createDate 2024-07-15 18:59:08
 * @Entity com.yyigou.dsrp.cdc.dao.supplier.entity.SupplierCategory
 */
public interface SupplierCategoryDAO extends CdcBaseMapper<SupplierCategory> {

    default SupplierCategory getByNo(String enterpriseNo, String no) {
        return selectOne(Wrappers.<SupplierCategory>lambdaQuery()
                .eq(SupplierCategory::getEnterpriseNo, enterpriseNo)
                .eq(SupplierCategory::getNo, no)
                .eq(SupplierCategory::getDeleted, DeletedEnum.UN_DELETE.getValue()));
    }
    default SupplierCategory getByGroupNo(String enterpriseNo, String groupNo) {
        return selectOne(Wrappers.<SupplierCategory>lambdaQuery()
                .eq(SupplierCategory::getEnterpriseNo, enterpriseNo)
                .eq(SupplierCategory::getGroupNo, groupNo)
                .eq(SupplierCategory::getDeleted, DeletedEnum.UN_DELETE.getValue()));
    }



    default SupplierCategory findByGroupNoAndParentNo(String enterpriseNo, String groupNo, String parentNo) {
        return selectOne(Wrappers.<SupplierCategory>lambdaQuery()
                .eq(SupplierCategory::getEnterpriseNo, enterpriseNo)
                .eq(SupplierCategory::getGroupNo, groupNo)
                .eq(SupplierCategory::getParentNo, parentNo)
                .eq(SupplierCategory::getDeleted, DeletedEnum.UN_DELETE.getValue()));
    }


    default List<SupplierCategory> getByNoList(String enterpriseNo, List<String> noList) {
        return selectList(Wrappers.<SupplierCategory>lambdaQuery()
                .eq(SupplierCategory::getEnterpriseNo, enterpriseNo)
                .in(SupplierCategory::getNo, noList)
                .eq(SupplierCategory::getDeleted, DeletedEnum.UN_DELETE.getValue()));
    }

}




