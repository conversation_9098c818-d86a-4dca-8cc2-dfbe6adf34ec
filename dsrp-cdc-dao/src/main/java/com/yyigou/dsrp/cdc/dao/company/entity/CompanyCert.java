package com.yyigou.dsrp.cdc.dao.company.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 企业证件基础表
 * @TableName bdc_company_cert
 */
@TableName(value ="bdc_company_cert")
@Data
public class CompanyCert implements Serializable {
    /**
     * 
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 所属企业编号(租户)
     */
    private String enterpriseNo;

    /**
     * 所属企业名称(租户)
     */
    private String enterpriseName;

    /**
     * 企业编号
     */
    private String companyNo;

    /**
     * 公司/企业名称
     */
    private String companyName;

    /**
     * 自定义分组编号（器械类、产品类等）
     */
    private String groupNo;

    /**
     * 资料类型：yyzz：营业执照，ylqxscqyxkz 医疗器械生产企业,ylqxscbaz 医疗器械生产备案证，ylqxjyqyxkz医疗器械经营企业许可证,ylqxjybaz医疗器械经营备案证
     */
    private String certType;

    /**
     * 证件编号（手动输入）
     */
    private String certCode;

    /**
     * 证书名称
     */
    private String certName;

    /**
     * 
     */
    private String startTime;

    /**
     * 
     */
    private String endTime;

    /**
     * 是否长期 1长期 0无
     */
    private Integer longTerm;

    /**
     * 是否长期 1长期 0无
     */
    private Date syncTime;

    /**
     * 状态：1正常 0无效 
     */
    private Object status;

    /**
     *  医疗器械监管分类
     */
    private String medicalScope;

    /**
     * 备注
     */
    private String remark;

    /**
     * 1:删除 0：未删除
     */
    private Integer deleted;

    /**
     * 推送平台状态 0未推送 1已推送 2无需推送
     */
    private Integer pushPlatform;

    /**
     * 来源类型 0本地新增 1平台新增
     */
    private Integer sourceType;

    /**
     * 创建人编号
     */
    private String createNo;

    /**
     * 制单人
     */
    private String createName;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 修改时间
     */
    private String operateNo;

    /**
     * 修改人名称
     */
    private String operateName;

    /**
     * 修改时间
     */
    private String operateTime;

    /**
     * 最后操作时间戳
     */
    private Date opTimestamp;

    /**
     * 数据记录版本
     */
    private Integer opRevsion;

    /**
     * 数据操作类型 I:新增 U：更新 D:删除
     */
    private String opType;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        CompanyCert other = (CompanyCert) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getEnterpriseNo() == null ? other.getEnterpriseNo() == null : this.getEnterpriseNo().equals(other.getEnterpriseNo()))
            && (this.getEnterpriseName() == null ? other.getEnterpriseName() == null : this.getEnterpriseName().equals(other.getEnterpriseName()))
            && (this.getCompanyNo() == null ? other.getCompanyNo() == null : this.getCompanyNo().equals(other.getCompanyNo()))
            && (this.getCompanyName() == null ? other.getCompanyName() == null : this.getCompanyName().equals(other.getCompanyName()))
            && (this.getGroupNo() == null ? other.getGroupNo() == null : this.getGroupNo().equals(other.getGroupNo()))
            && (this.getCertType() == null ? other.getCertType() == null : this.getCertType().equals(other.getCertType()))
            && (this.getCertCode() == null ? other.getCertCode() == null : this.getCertCode().equals(other.getCertCode()))
            && (this.getCertName() == null ? other.getCertName() == null : this.getCertName().equals(other.getCertName()))
            && (this.getStartTime() == null ? other.getStartTime() == null : this.getStartTime().equals(other.getStartTime()))
            && (this.getEndTime() == null ? other.getEndTime() == null : this.getEndTime().equals(other.getEndTime()))
            && (this.getLongTerm() == null ? other.getLongTerm() == null : this.getLongTerm().equals(other.getLongTerm()))
            && (this.getSyncTime() == null ? other.getSyncTime() == null : this.getSyncTime().equals(other.getSyncTime()))
            && (this.getStatus() == null ? other.getStatus() == null : this.getStatus().equals(other.getStatus()))
            && (this.getMedicalScope() == null ? other.getMedicalScope() == null : this.getMedicalScope().equals(other.getMedicalScope()))
            && (this.getRemark() == null ? other.getRemark() == null : this.getRemark().equals(other.getRemark()))
            && (this.getDeleted() == null ? other.getDeleted() == null : this.getDeleted().equals(other.getDeleted()))
            && (this.getPushPlatform() == null ? other.getPushPlatform() == null : this.getPushPlatform().equals(other.getPushPlatform()))
            && (this.getSourceType() == null ? other.getSourceType() == null : this.getSourceType().equals(other.getSourceType()))
            && (this.getCreateNo() == null ? other.getCreateNo() == null : this.getCreateNo().equals(other.getCreateNo()))
            && (this.getCreateName() == null ? other.getCreateName() == null : this.getCreateName().equals(other.getCreateName()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getOperateNo() == null ? other.getOperateNo() == null : this.getOperateNo().equals(other.getOperateNo()))
            && (this.getOperateName() == null ? other.getOperateName() == null : this.getOperateName().equals(other.getOperateName()))
            && (this.getOperateTime() == null ? other.getOperateTime() == null : this.getOperateTime().equals(other.getOperateTime()))
            && (this.getOpTimestamp() == null ? other.getOpTimestamp() == null : this.getOpTimestamp().equals(other.getOpTimestamp()))
            && (this.getOpRevsion() == null ? other.getOpRevsion() == null : this.getOpRevsion().equals(other.getOpRevsion()))
            && (this.getOpType() == null ? other.getOpType() == null : this.getOpType().equals(other.getOpType()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getEnterpriseNo() == null) ? 0 : getEnterpriseNo().hashCode());
        result = prime * result + ((getEnterpriseName() == null) ? 0 : getEnterpriseName().hashCode());
        result = prime * result + ((getCompanyNo() == null) ? 0 : getCompanyNo().hashCode());
        result = prime * result + ((getCompanyName() == null) ? 0 : getCompanyName().hashCode());
        result = prime * result + ((getGroupNo() == null) ? 0 : getGroupNo().hashCode());
        result = prime * result + ((getCertType() == null) ? 0 : getCertType().hashCode());
        result = prime * result + ((getCertCode() == null) ? 0 : getCertCode().hashCode());
        result = prime * result + ((getCertName() == null) ? 0 : getCertName().hashCode());
        result = prime * result + ((getStartTime() == null) ? 0 : getStartTime().hashCode());
        result = prime * result + ((getEndTime() == null) ? 0 : getEndTime().hashCode());
        result = prime * result + ((getLongTerm() == null) ? 0 : getLongTerm().hashCode());
        result = prime * result + ((getSyncTime() == null) ? 0 : getSyncTime().hashCode());
        result = prime * result + ((getStatus() == null) ? 0 : getStatus().hashCode());
        result = prime * result + ((getMedicalScope() == null) ? 0 : getMedicalScope().hashCode());
        result = prime * result + ((getRemark() == null) ? 0 : getRemark().hashCode());
        result = prime * result + ((getDeleted() == null) ? 0 : getDeleted().hashCode());
        result = prime * result + ((getPushPlatform() == null) ? 0 : getPushPlatform().hashCode());
        result = prime * result + ((getSourceType() == null) ? 0 : getSourceType().hashCode());
        result = prime * result + ((getCreateNo() == null) ? 0 : getCreateNo().hashCode());
        result = prime * result + ((getCreateName() == null) ? 0 : getCreateName().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getOperateNo() == null) ? 0 : getOperateNo().hashCode());
        result = prime * result + ((getOperateName() == null) ? 0 : getOperateName().hashCode());
        result = prime * result + ((getOperateTime() == null) ? 0 : getOperateTime().hashCode());
        result = prime * result + ((getOpTimestamp() == null) ? 0 : getOpTimestamp().hashCode());
        result = prime * result + ((getOpRevsion() == null) ? 0 : getOpRevsion().hashCode());
        result = prime * result + ((getOpType() == null) ? 0 : getOpType().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", enterpriseNo=").append(enterpriseNo);
        sb.append(", enterpriseName=").append(enterpriseName);
        sb.append(", companyNo=").append(companyNo);
        sb.append(", companyName=").append(companyName);
        sb.append(", groupNo=").append(groupNo);
        sb.append(", certType=").append(certType);
        sb.append(", certCode=").append(certCode);
        sb.append(", certName=").append(certName);
        sb.append(", startTime=").append(startTime);
        sb.append(", endTime=").append(endTime);
        sb.append(", longTerm=").append(longTerm);
        sb.append(", syncTime=").append(syncTime);
        sb.append(", status=").append(status);
        sb.append(", medicalScope=").append(medicalScope);
        sb.append(", remark=").append(remark);
        sb.append(", deleted=").append(deleted);
        sb.append(", pushPlatform=").append(pushPlatform);
        sb.append(", sourceType=").append(sourceType);
        sb.append(", createNo=").append(createNo);
        sb.append(", createName=").append(createName);
        sb.append(", createTime=").append(createTime);
        sb.append(", operateNo=").append(operateNo);
        sb.append(", operateName=").append(operateName);
        sb.append(", operateTime=").append(operateTime);
        sb.append(", opTimestamp=").append(opTimestamp);
        sb.append(", opRevsion=").append(opRevsion);
        sb.append(", opType=").append(opType);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}