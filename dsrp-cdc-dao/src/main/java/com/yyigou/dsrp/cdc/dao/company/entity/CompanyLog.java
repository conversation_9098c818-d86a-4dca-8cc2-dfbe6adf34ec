package com.yyigou.dsrp.cdc.dao.company.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;

/**
 * CompanyLog 
 *
 * 企业操作日志表
 */
@TableName(value = "bdc_company_log")
@Data
public class CompanyLog implements Serializable {
    private static final long serialVersionUID = -5372945664636456267L;
    /**
     * 主键
     */
    @EntityField(name = "主键")
    @TableId(type = IdType.AUTO)
	private Long id;
     /**
     * 租户编号
     */
    @EntityField(name = "租户编号")
	private String enterpriseNo;
     /**
     * 企业档案编号
     */
    @EntityField(name = "企业档案编号")
	private String companyNo;
     /**
     * 操作内容
     */
    @EntityField(name = "操作内容")
	private String operateContent;
     /**
     * 操作类型
     */
    @EntityField(name = "操作类型")
	private Integer operateType;
     /**
     * 操作人编号
     */
    @EntityField(name = "操作人编号")
	private String operateNo;
     /**
     * 操作人名称
     */
    @EntityField(name = "操作人名称")
	private String operateName;
     /**
     * 操作时间
     */
    @EntityField(name = "操作时间")
	private String operateTime;
}