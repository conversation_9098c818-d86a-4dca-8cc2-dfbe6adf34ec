package com.yyigou.dsrp.cdc.dao.v2.customer;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yyigou.dsrp.cdc.common.enums.DeletedEnum;
import com.yyigou.dsrp.cdc.dao.config.CdcBaseMapper;
import com.yyigou.dsrp.cdc.dao.v2.customer.entity.CustomerCategoryV2;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface CustomerCategoryV2DAO extends CdcBaseMapper<CustomerCategoryV2> {
    List<CustomerCategoryV2> selectListByUseOrgNo(@Param("enterpriseNo") String enterpriseNo, @Param("useOrgNo") String useOrgNo, @Param("deleteFlag") int deleteFlag);

    List<CustomerCategoryV2> selectListByUseOrgNoList(@Param("enterpriseNo") String enterpriseNo, @Param("useOrgNoList") List<String> useOrgNoList, @Param("deleteFlag") int deleteFlag);

    default List<CustomerCategoryV2> getByManageOrgNoList(String enterpriseNo, List<String> manageOrgNoList) {
        return selectList(Wrappers.<CustomerCategoryV2>lambdaQuery()
                .eq(CustomerCategoryV2::getEnterpriseNo, enterpriseNo)
                .in(CustomerCategoryV2::getManageOrgNo, manageOrgNoList)
                .eq(CustomerCategoryV2::getDeleted, DeletedEnum.UN_DELETE.getValue())
                .last("order by level asc, sort_num is null asc, sort_num asc")
        );
    }

    default CustomerCategoryV2 getByNo(String enterpriseNo, String no) {
        return selectOne(Wrappers.<CustomerCategoryV2>lambdaQuery()
                .eq(CustomerCategoryV2::getEnterpriseNo, enterpriseNo)
                .eq(CustomerCategoryV2::getNo, no)
                .eq(CustomerCategoryV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
    }

    default List<CustomerCategoryV2> getByNoList(String enterpriseNo, List<String> noList) {
        return selectList(Wrappers.<CustomerCategoryV2>lambdaQuery()
                .eq(CustomerCategoryV2::getEnterpriseNo, enterpriseNo)
                .in(CustomerCategoryV2::getNo, noList)
                .eq(CustomerCategoryV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
    }

    int batchStatusUpdate(@Param("list") List<CustomerCategoryV2> newCustomerCategoryV2List);
}




