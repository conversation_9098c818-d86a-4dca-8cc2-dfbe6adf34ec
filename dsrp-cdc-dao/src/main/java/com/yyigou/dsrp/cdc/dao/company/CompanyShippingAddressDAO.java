package com.yyigou.dsrp.cdc.dao.company;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yyigou.dsrp.cdc.common.enums.DeletedEnum;
import com.yyigou.dsrp.cdc.dao.company.entity.CompanyShippingAddress;
import com.yyigou.dsrp.cdc.dao.config.CdcBaseMapper;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【bdc_company_shipping_address(收货地址表)】的数据库操作Mapper
 * @createDate 2024-06-03 15:58:22
 * @Entity com.yyigou.dsrp.cdc.dao.company.entity.CompanyShippingAddress
 */
public interface CompanyShippingAddressDAO extends CdcBaseMapper<CompanyShippingAddress> {


    default List<CompanyShippingAddress> getCompanyShippingAddressByCompanyNo(String enterpriseNo, String companyNo, String linkmanType) {
        LambdaQueryWrapper<CompanyShippingAddress> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(CompanyShippingAddress::getEnterpriseNo, enterpriseNo)
                .eq(CompanyShippingAddress::getCompanyNo, companyNo)
                .eq(CompanyShippingAddress::getLinkaddType, linkmanType)
                .eq(CompanyShippingAddress::getDeleted, DeletedEnum.UN_DELETE.getValue());
        return selectList(wrapper);
    }

    default List<CompanyShippingAddress> getCompanyShippingAddressByCompanyNo(String enterpriseNo, String companyNo, String linkmanType, String sourceNo) {
        LambdaQueryWrapper<CompanyShippingAddress> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(CompanyShippingAddress::getEnterpriseNo, enterpriseNo)
                .eq(CompanyShippingAddress::getCompanyNo, companyNo)
                .eq(CompanyShippingAddress::getLinkaddType, linkmanType)
                .eq(CompanyShippingAddress::getSourceNo, sourceNo)
                .eq(CompanyShippingAddress::getDeleted, DeletedEnum.UN_DELETE.getValue());
        return selectList(wrapper);
    }

    default List<CompanyShippingAddress> getCompanyShippingAddressByCompanyNoAddressType(String enterpriseNo, String companyNo, String linkmanType, String sourceNo, List<String> addressTypeList, String addressType) {
        LambdaQueryWrapper<CompanyShippingAddress> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(CompanyShippingAddress::getEnterpriseNo, enterpriseNo)
                .eq(CompanyShippingAddress::getCompanyNo, companyNo)
                .in(CompanyShippingAddress::getCompanyNo, companyNo)
                .eq(CompanyShippingAddress::getLinkaddType, linkmanType)
                .eq(CompanyShippingAddress::getSourceNo, sourceNo)
                .eq(CompanyShippingAddress::getDeleted, DeletedEnum.UN_DELETE.getValue())
                .in(!CollectionUtils.isEmpty(addressTypeList), CompanyShippingAddress::getAddressType, addressTypeList)
                .eq(!StringUtils.isEmpty(addressType), CompanyShippingAddress::getAddressType, addressType);
        return selectList(wrapper);
    }

    default List<CompanyShippingAddress> getCompanyShippingAddressBySourceNoList(String enterpriseNo, String linkmanType, List<String> sourceNoList) {
        LambdaQueryWrapper<CompanyShippingAddress> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(CompanyShippingAddress::getEnterpriseNo, enterpriseNo)
                .eq(CompanyShippingAddress::getLinkaddType, linkmanType)
                .in(CompanyShippingAddress::getSourceNo, sourceNoList)
                .eq(CompanyShippingAddress::getDeleted, DeletedEnum.UN_DELETE.getValue());
        return selectList(wrapper);
    }


    default void deleteShippingAddressListBySourceNoList(String enterpriseNo, String linkmanType, List<String> sourceNoList) {
        update(new CompanyShippingAddress(), new UpdateWrapper<CompanyShippingAddress>().lambda().set(CompanyShippingAddress::getDeleted, DeletedEnum.DELETED.getValue())
                .eq(CompanyShippingAddress::getEnterpriseNo, enterpriseNo)
                .eq(CompanyShippingAddress::getLinkaddType, linkmanType)
                .in(CompanyShippingAddress::getSourceNo, sourceNoList)
                .eq(CompanyShippingAddress::getDeleted, DeletedEnum.UN_DELETE.getValue()));
    }

    default void deleteShippingAddressListBySourceNoList(String enterpriseNo, String linkmanType, List<String> sourceNoList, List<Long> idList) {
        update(new CompanyShippingAddress(), new UpdateWrapper<CompanyShippingAddress>().lambda().set(CompanyShippingAddress::getDeleted, DeletedEnum.DELETED.getValue())
                .eq(CompanyShippingAddress::getEnterpriseNo, enterpriseNo)
                .eq(CompanyShippingAddress::getLinkaddType, linkmanType)
                .in(CompanyShippingAddress::getSourceNo, sourceNoList)
                .in(CompanyShippingAddress::getId, idList)
                .eq(CompanyShippingAddress::getDeleted, DeletedEnum.UN_DELETE.getValue()));
    }
}




