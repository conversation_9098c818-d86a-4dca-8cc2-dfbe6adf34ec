package com.yyigou.dsrp.cdc.dao.v2.supplier;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yyigou.dsrp.cdc.common.enums.DeletedEnum;
import com.yyigou.dsrp.cdc.dao.config.CdcBaseMapper;
import com.yyigou.dsrp.cdc.dao.v2.supplier.entity.SupplierCategoryV2;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface SupplierCategoryV2DAO extends CdcBaseMapper<SupplierCategoryV2> {
    List<SupplierCategoryV2> selectListByUseOrgNo(@Param("enterpriseNo") String enterpriseNo, @Param("useOrgNo") String useOrgNo, @Param("deleteFlag") int deleteFlag);

    List<SupplierCategoryV2> selectListByUseOrgNoList(@Param("enterpriseNo") String enterpriseNo, @Param("useOrgNoList") List<String> useOrgNoList, @Param("deleteFlag") int deleteFlag);

    default List<SupplierCategoryV2> getByManageOrgNoList(String enterpriseNo, List<String> manageOrgNoList) {
        return selectList(Wrappers.<SupplierCategoryV2>lambdaQuery()
                .eq(SupplierCategoryV2::getEnterpriseNo, enterpriseNo)
                .in(SupplierCategoryV2::getManageOrgNo, manageOrgNoList)
                .eq(SupplierCategoryV2::getDeleted, DeletedEnum.UN_DELETE.getValue())
                .last("order by level asc, sort_num is null asc, sort_num asc")
        );
    }

    default SupplierCategoryV2 getByNo(String enterpriseNo, String no) {
        return selectOne(Wrappers.<SupplierCategoryV2>lambdaQuery()
                .eq(SupplierCategoryV2::getEnterpriseNo, enterpriseNo)
                .eq(SupplierCategoryV2::getNo, no)
                .eq(SupplierCategoryV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
    }

    default List<SupplierCategoryV2> getByNoList(String enterpriseNo, List<String> noList) {
        return selectList(Wrappers.<SupplierCategoryV2>lambdaQuery()
                .eq(SupplierCategoryV2::getEnterpriseNo, enterpriseNo)
                .in(SupplierCategoryV2::getNo, noList)
                .eq(SupplierCategoryV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
    }

    int batchStatusUpdate(@Param("list") List<SupplierCategoryV2> newSupplierCategoryV2List);
}




