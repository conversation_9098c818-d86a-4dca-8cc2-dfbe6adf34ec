package com.yyigou.dsrp.cdc.dao.company;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yyigou.dsrp.cdc.common.enums.DeletedEnum;
import com.yyigou.dsrp.cdc.dao.company.entity.CompanyLinkman;
import com.yyigou.dsrp.cdc.dao.config.CdcBaseMapper;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【bdc_company_linkman】的数据库操作Mapper
 * @createDate 2024-06-03 14:11:04
 * @Entity com.yyigou.dsrp.cdc.dao.company.entity.BdcCompanyLinkman
 */
public interface CompanyLinkmanDAO extends CdcBaseMapper<CompanyLinkman> {

    default List<CompanyLinkman> getCompanyLinkmanListByCompanyNo(String enterpriseNo, String companyNo, String linkmanType) {
        LambdaQueryWrapper<CompanyLinkman> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(CompanyLinkman::getEnterpriseNo, enterpriseNo)
                .eq(CompanyLinkman::getCompanyNo, companyNo)
                .eq(CompanyLinkman::getLinkmanType, linkmanType)
                .eq(CompanyLinkman::getDeleted, DeletedEnum.UN_DELETE.getValue());
        return selectList(wrapper);
    }

    default List<CompanyLinkman> getCompanyLinkmanListByCompanyNo(String enterpriseNo, String companyNo, String linkmanType, String sourceNo) {
        LambdaQueryWrapper<CompanyLinkman> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(CompanyLinkman::getEnterpriseNo, enterpriseNo)
                .eq(CompanyLinkman::getCompanyNo, companyNo)
                .eq(CompanyLinkman::getLinkmanType, linkmanType)
                .eq(CompanyLinkman::getSourceNo, sourceNo)
                .eq(CompanyLinkman::getDeleted, DeletedEnum.UN_DELETE.getValue());
        return selectList(wrapper);
    }

    default List<CompanyLinkman> getCompanyLinkmanListBySourceNoList(String enterpriseNo, String linkmanType, List<String> sourceNoList) {
        LambdaQueryWrapper<CompanyLinkman> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(CompanyLinkman::getEnterpriseNo, enterpriseNo)
                .eq(CompanyLinkman::getLinkmanType, linkmanType)
                .in(CompanyLinkman::getSourceNo, sourceNoList)
                .eq(CompanyLinkman::getDeleted, DeletedEnum.UN_DELETE.getValue());
        return selectList(wrapper);
    }

    default List<CompanyLinkman> getCompanyDefaultLinkmanListBySourceNoList(String enterpriseNo, String linkmanType, List<String> sourceNoList) {
        LambdaQueryWrapper<CompanyLinkman> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(CompanyLinkman::getEnterpriseNo, enterpriseNo)
                .eq(CompanyLinkman::getLinkmanType, linkmanType)
                .in(CompanyLinkman::getSourceNo, sourceNoList)
                .eq(CompanyLinkman::getIsDefault, 1)
                .eq(CompanyLinkman::getDeleted, DeletedEnum.UN_DELETE.getValue());
        return selectList(wrapper);
    }


    default void deleteCompanyLinkmanListByCompanyNo(String enterpriseNo, String linkmanType, List<String> sourceNoList) {
        update(new CompanyLinkman(), new UpdateWrapper<CompanyLinkman>().lambda().set(CompanyLinkman::getDeleted, DeletedEnum.DELETED.getValue())
                .eq(CompanyLinkman::getEnterpriseNo, enterpriseNo)
                .eq(CompanyLinkman::getLinkmanType, linkmanType)
                .in(CompanyLinkman::getSourceNo, sourceNoList)
                .eq(CompanyLinkman::getDeleted, DeletedEnum.UN_DELETE.getValue()));
    }

    default void deleteCompanyLinkmanListByCompanyNo(String enterpriseNo, String linkmanType, List<String> sourceNoList, List<Long> idList) {
        update(new CompanyLinkman(), new UpdateWrapper<CompanyLinkman>().lambda().set(CompanyLinkman::getDeleted, DeletedEnum.DELETED.getValue())
                .eq(CompanyLinkman::getEnterpriseNo, enterpriseNo)
                .eq(CompanyLinkman::getLinkmanType, linkmanType)
                .in(CompanyLinkman::getSourceNo, sourceNoList)
                .in(CompanyLinkman::getId, idList)
                .eq(CompanyLinkman::getDeleted, DeletedEnum.UN_DELETE.getValue()));
    }


}




