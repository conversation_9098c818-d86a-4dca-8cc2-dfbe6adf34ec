package com.yyigou.dsrp.cdc.dao.v2.company.entity;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 企业组织关联表
 * @TableName bdc_company_associated_org
 */
@Data
@TableName(value = "bdc_company_associated_org")
public class CompanyAssociatedOrg {
    /**
     * 
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 所属企业编号
     */
    private String enterpriseNo;

    /**
     * 企业编号
     */
    private String companyNo;

    /**
     * 来源类型：1-供应商，2-客户，3-纳税主体组织
     */
    private Integer sourceType;

    /**
     * 来源编码
     */
    private String sourceCode;

    /**
     * 关联组织编号
     */
    private String associatedOrgNo;

    private String associatedOrgCode;

    private String associatedOrgName;

    /**
     * 数据是否有效：0-无效，1-有效
     */
    private Integer status;

    /**
     * 删除标志 0：未删除 1：已删除
     */
    private Integer deleted;

    /**
     * 创建人编号
     */
    private String createNo;

    /**
     * 创建人名称
     */
    private String createName;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 操作人编号
     */
    private String modifyNo;

    /**
     * 操作人名称
     */
    private String modifyName;

    /**
     * 操作时间
     */
    private String modifyTime;

    /**
     * 最后操作时间戳
     */
    private Date opTimestamp;
}