package com.yyigou.dsrp.cdc.dao.v2.customer.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;

@TableName("bdc_customer")
@Data
public class CustomerV2 implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 供应商主键
     */
    @TableId(value = "customer_no")
    private String customerNo;

    /**
     * 本企业编号（由scs平台分配）
     */
    private String enterpriseNo;

    /**
     * 管理组织编号
     */
    private String manageOrgNo;

    /**
     * 公司/企业编码（basic_company表)
     */
    private String companyNo;

    /**
     * 来源渠道：local-自行创建 scs-云端
     */
    private String sourceChannel;

    /**
     * 客户编码
     */
    private String customerCode;

    /**
     * 客户名称
     */
    private String customerName;


    /**
     * 客户外语名称
     */
    private String customerNameEn;

    /**
     * 统一社会信用代码。公立医院的事业单位法人证号
     */
    private String unifiedSocialCode;

    /**
     * 客户基本分类
     */
    private String customerCategoryNo;

    /**
     * 助记码
     */
    private String mnemonicCode;

    @EntityField(name = "是否GSP管控：1-是，0-否")
    private Integer isGspControl;

    @EntityField(name = "是否关联企业：1-是，0-否")
    private Integer isAssociatedEnterprise;

    @EntityField(name = "关联组织编号")
    private String associatedOrgNo;

    @EntityField(name = "关联组织编码")
    private String associatedOrgCode;

    @EntityField(name = "关联组织名称")
    private String associatedOrgName;

    /**
     * 删除标志 0：未删除 1：已删除
     */
    private Integer deleted;


    /**
     * 备注
     */
    private String remark;

    /**
     * 业务状态：1:合格 2：草稿
     */
    private Integer businessFlag;

    /**
     * 创建人编号
     */
    private String createNo;

    /**
     * 制单人
     */
    private String createName;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 修改时间
     */
    private String operateNo;

    /**
     * 修改人名称
     */
    private String operateName;

    /**
     * 修改时间
     */
    private String operateTime;

    private Integer retailInvestors;

    private String transactionType;

    private String omsCustomerNo;
}
