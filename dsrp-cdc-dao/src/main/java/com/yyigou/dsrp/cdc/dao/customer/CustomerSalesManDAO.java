package com.yyigou.dsrp.cdc.dao.customer;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yyigou.dsrp.cdc.common.enums.DeletedEnum;
import com.yyigou.dsrp.cdc.dao.config.CdcBaseMapper;
import com.yyigou.dsrp.cdc.dao.customer.entity.CustomerSalesMan;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【bdc_customer_sales_man】的数据库操作Mapper
 * @createDate 2024-06-03 16:23:59
 * @Entity com.yyigou.dsrp.cdc.dao.customer.entity.CustomerSalesMan
 */
public interface CustomerSalesManDAO extends CdcBaseMapper<CustomerSalesMan> {

    default List<CustomerSalesMan> getCustomerSalesManByCustomerNoList(String enterpriseNo, List<String> customerNoList) {
        LambdaQueryWrapper<CustomerSalesMan> wrapper = Wrappers.lambdaQuery();
        wrapper.in(CustomerSalesMan::getCustomerNo, customerNoList)
                .eq(CustomerSalesMan::getDeleted, DeletedEnum.UN_DELETE.getValue());
        return selectList(wrapper);
    }


    default void deleteCustomerSalesManByCustomerNoList(String enterpriseNo, List<String> customerNoList) {
        update(new CustomerSalesMan(), new UpdateWrapper<CustomerSalesMan>().lambda().set(CustomerSalesMan::getDeleted, DeletedEnum.DELETED.getValue())
                .in(CustomerSalesMan::getCustomerNo, customerNoList)
                .eq(CustomerSalesMan::getDeleted, DeletedEnum.UN_DELETE.getValue()));
    }

    default void deleteCustomerSalesManByCustomerNoList(String enterpriseNo, List<String> customerNoList, List<Long> idList) {
        update(new CustomerSalesMan(), new UpdateWrapper<CustomerSalesMan>().lambda().set(CustomerSalesMan::getDeleted, DeletedEnum.DELETED.getValue())
                .in(CustomerSalesMan::getCustomerNo, customerNoList)
                .in(CustomerSalesMan::getId, idList)
                .eq(CustomerSalesMan::getDeleted, DeletedEnum.UN_DELETE.getValue()));
    }


}




