package com.yyigou.dsrp.cdc.dao.company.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @TableName bdc_company_shipping_address
 */
@TableName(value = "bdc_company_shipping_address")
@Data
public class CompanyShippingAddress implements Serializable {
    @TableId(type = IdType.AUTO)
    private Long id;

    private String enterpriseNo;

    private String companyNo;

    private String linkAddressCode;
    private String receiveUser;

    private String receivePhone;

    private String regionCode;

    private String regionName;

    private String receiveAddr;

    private String email;

    private Integer isDefault;

    private Integer status;

    private String addressType;

    private String addressDesc;


    private String createNo;

    private String createTime;

    private String operateNo;

    private String operateName;

    private String operateTime;

    private Date opTimestamp;

    private Integer opRevsion;

    private String opType;

    private String linkaddType;

    private Integer deleted;

    private Integer tcSyncFlag;

    private String tcSyncResult;

    private String sourceNo;

    private static final long serialVersionUID = 1L;
}