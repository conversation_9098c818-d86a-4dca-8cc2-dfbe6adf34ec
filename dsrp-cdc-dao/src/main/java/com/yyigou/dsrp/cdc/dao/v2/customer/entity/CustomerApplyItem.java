package com.yyigou.dsrp.cdc.dao.v2.customer.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;

@TableName("bdc_customer_apply_item")
@Data
public class CustomerApplyItem implements Serializable {
    private static final long serialVersionUID = 1L;

    @EntityField(name = "主键ID")
    @TableId(type = IdType.AUTO)
    private Long id;

    @EntityField(name = "租户编号")
    private String enterpriseNo;

    @EntityField(name = "申请单id")
    private Long applyInstanceId;

    @EntityField(name = "申请单号")
    private String applyInstanceNo;

    @EntityField(name = "审批通过前的详情JSON")
    private String preApproveContent;

    @EntityField(name = "审批通过后的详情JSON")
    private String approveContent;

    @EntityField(name = "申请时填写的详情JSON")
    private String applyContent;
}
