package com.yyigou.dsrp.cdc.dao.customer.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 客户表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-16
 */
@Data
@TableName("bdc_customer")
public class Customer implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 供应商主键
     */
    @TableId(type = IdType.NONE)
    private String customerNo;

    /**
     * 本企业编号（由scs平台分配）
     */
    private String enterpriseNo;

    /**
     * 公司/企业编码（basic_company表)
     */
    private String companyNo;

    /**
     * 来源渠道：local-自行创建 scs-云端
     */
    private String sourceChannel;

    /**
     * 客户编码
     */
    private String customerCode;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 客户简称
     */
    private String shortName;

    /**
     * 客户外语名称
     */
    private String customerNameEn;

    /**
     * 统一社会信用代码。公立医院的事业单位法人证号
     */
    private String unifiedSocialCode;

    /**
     * 客户基本分类
     */
    private String customerCategoryNo;

    /**
     * 助记码
     */
    private String mnemonicCode;

    /**
     * 删除标志 0：未删除 1：已删除
     */
    private Integer deleted;

    /**
     * 企业类型 cs:生产企业，jxs：经营企业，yy：医疗机构
     */
    private String enterpriseType;

    private String qtRemark;

    /**
     * 机构类型：yy:医院,dsfsys:第三方实验室,dsftjzx:第三方体检中心,jkzx:疾控中心,xz:血站,zs:诊所,ylhly:养老护理院,jgqt:其他
     */
    private String institutionalType;

    /**
     * 医院类型：yy：公立医院，mbyy:民营医院
     */
    private String hospitalType;

    /**
     * 医院等级：0:无等级,1:一级甲等,2:一级乙等,3:一级丙等,4:二级甲等,5:二级乙等,6:二级丙等,7:三级特等,8:三级甲等,9:三级乙等,10:三级丙等
     */
    private Integer hospitalClass;

    /**
     * 是否医药客户 0：否 1：是,会影响证照采集范围
     */
    private Integer isMedicineCustomer;

    /**
     * 合作性质
     */
    private String cooperationMode;

    /**
     * 等级体系编号
     */
    private String gradeSytemCode;

    /**
     * 等级编号
     */
    private String gradeCode;

    /**
     * 备注
     */
    private String remark;

    /**
     * 是否GSP 管控 1：是 0：否
     */
    private Integer isGspControl;

    /**
     * GSP的审核状态 0:未创建，1：待审批 2：审批通过 3：审核失败;	9-审批撤回
     */
    private String gspAuditStatus;

    /**
     * 父集团公司编号
     */
    private String parentCustomerNo;

    /**
     * 是否集团 1：是 0：否
     */
    private Integer isGroup;

    /**
     * 成为集团时间
     */
    private String bulidGroupTime;

    /**
     * 成为子公司时间
     */
    private String bulidSonTime;

    /**
     * 业务状态：0:潜在 1:合格 2：草稿
     */
    private Integer businessFlag;

    /**
     * 管控状态 1：启用 2：停用
     */
    private String controlStatus;

    /**
     * 最后申请流程：ZRSQ准入申请、BGSQ档案变更、TTSQ淘汰申请、HMDSQ黑名单申请、GSP首营
     */
    private String applyformBillType;

    /**
     * 最后申请流程状态：auditing-流转中、aborted-已作废、approved-审核通过、rejected-审核驳回
     */
    private String applyformStatus;

    /**
     * 价格分类编码
     */
    private String priceCategoryCode;

    /**
     * 价格分类名称
     */
    private String priceCategoryName;

    /**
     * 经营范围
     */
    private String manageScope;

    /**
     * 是否流向客户：1：是 0：否
     */
    private Integer isFlowCustomer;

    /**
     * 同步scs状态：0：未同步 1：同步成功 2：同步失败 3：已协同
     */
    private Integer isSyncScs;

    /**
     * 是否同步到wms 1:是 0：否
     */
    private Integer isSyncWms;

    /**
     * 是否同步到Erp 0:未同步 1:已同步
     */
    private Integer isSyncErp;

    /**
     * 创建人编号
     */
    private String createNo;

    /**
     * 制单人
     */
    private String createName;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 修改时间
     */
    private String operateNo;

    /**
     * 修改人名称
     */
    private String operateName;

    /**
     * 修改时间
     */
    private String operateTime;

    /**
     * 最后操作时间戳
     */
    private Date opTimestamp;

    /**
     * 数据记录版本
     */
    private Integer opRevsion;

    /**
     * 数据操作类型 I:新增 U：更新 D:删除
     */
    private String opType;

    /**
     * ys同步标识 0:未同步 1:同步成功 2:同步失败
     */
    private String ysSyncFlag;

    /**
     * 自定义属性(json格式)
     */
    private String customAttr;

    /**
     * 归属公司
     */
    private String ownerCompany;

    /**
     * 客户类型
     */
    private String customerType;

    /**
     * 客户类型名称
     */
    private String customerTypeName;

    /**
     * 是否账期管理 1：是 0：否
     */
    private Integer isAccountManager;

    /**
     * 信用等级
     */
    private String creditRate;

    /**
     * 信用等级名称
     */
    private String creditRateName;

    /**
     * 信用额度
     */
    private String creditAmount;

    /**
     * 信用期限
     */
    private String creditDates;

    /**
     * 结算方式
     */
    private String settlementModes;

    /**
     * 结算方式名称
     */
    private String settlementModesName;

    /**
     * 付款条件
     */
    private String paymentTerm;

    /**
     * 付款条件名称
     */
    private String paymentTermName;

    /**
     * 业务性质
     */
    private String businessType;

    /**
     * 业务性质名称
     */
    private String businessTypeName;

    /**
     * 合作起始时间
     */
    private String coopStartTime;

    /**
     * 合作结束时间
     */
    private String coopEndTime;

    /**
     * scs推送结果
     */
    private String scsPushResult;

    /**
     * ys推送结果
     */
    private String ysPushResult;

    /**
     * wms推送结果
     */
    private String wmsPushResult;

    /**
     * 管控类型id
     */
    private Long controlId;

    /**
     * 管控类型名称
     */
    private String controlTypeName;

    /**
     * 收款协议
     */
    private String paymentAgreement;
    /**
     * 收款条件
     */
    private String paymentCondition;
    /**
     * 交易币种
     */
    private String currency;

    private Integer retailInvestors;

    private String transactionType;
    /**
     * 版本号
     */
    private Integer version;
    /**
     * oms客户编号
     */
    private String omsCustomerNo;

    /**
     * tc同步标识 0:未同步 1:同步成功 2:同步失败
     */
    private Integer tcSyncFlag;
}
