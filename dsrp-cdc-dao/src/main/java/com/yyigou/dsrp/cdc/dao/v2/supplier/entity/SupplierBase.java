package com.yyigou.dsrp.cdc.dao.v2.supplier.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@TableName(value = "bdc_supplier_base")
@Data
public class SupplierBase implements Serializable {
    private static final long serialVersionUID = 1L;

    @EntityField(name = "主键")
    @TableId(type = IdType.AUTO)
    private Long id;

    @EntityField(name = "租户编号")
    private String enterpriseNo;

    @EntityField(name = "供应商编号")
    private String supplierNo;

    @EntityField(name = "供应商编码")
    private String supplierCode;

    @EntityField(name = "管理组织编号")
    private String manageOrgNo;

    @EntityField(name = "使用组织编号")
    private String useOrgNo;

    @EntityField(name = "管控状态")
    private String controlStatus;

    @EntityField(name = "删除标志 0：未删除 1：已删除")
    private Integer deleted;

    @EntityField(name = "创建人编号")
    private String createNo;

    @EntityField(name = "创建人名字")
    private String createName;

    @EntityField(name = "创建时间")
    private String createTime;

    @EntityField(name = "修改人编号")
    private String operateNo;

    @EntityField(name = "修改人名字")
    private String operateName;

    @EntityField(name = "修改时间")
    private String operateTime;

    @EntityField(name = "最后操作时间戳")
    private Date opTimestamp;

}