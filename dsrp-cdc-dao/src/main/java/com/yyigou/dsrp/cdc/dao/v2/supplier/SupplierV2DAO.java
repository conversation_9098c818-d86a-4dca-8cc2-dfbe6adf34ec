package com.yyigou.dsrp.cdc.dao.v2.supplier;

import com.yyigou.dsrp.cdc.dao.config.CdcBaseMapper;
import com.yyigou.dsrp.cdc.dao.v2.customer.entity.CustomerV2WithBiz;
import com.yyigou.dsrp.cdc.dao.v2.supplier.entity.SupplierV2;
import com.yyigou.dsrp.cdc.dao.v2.supplier.entity.SupplierV2WithBiz;
import com.yyigou.dsrp.cdc.dao.v2.supplier.entity.SupplierV2WithBase;
import com.yyigou.dsrp.cdc.model.v2.customer.req.CustomerCodeOrgPairListReq;
import com.yyigou.dsrp.cdc.model.v2.supplier.req.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

public interface SupplierV2DAO extends CdcBaseMapper<SupplierV2> {
    List<SupplierV2WithBiz> selectManageView(SupplierManageQueryListPageReq req);

    List<SupplierV2WithBiz> selectUseView(SupplierUseQueryListPageReq req);

    Set<String> selectDistinctSupplierCodeByUseOrgNoList(SupplierFindQueryListPageForTenantReq req);

    List<SupplierV2WithBiz> selectUseViewNoAuthZ(SupplierUseQueryListPageNoAuthZReq req);

    List<SupplierV2WithBase> selectReverseUseViewNoAuthZ(SupplierReverseUseQueryListPageNoAuthZReq req);

    List<SupplierV2WithBase> findListPageForTenant(SupplierFindQueryListPageForTenantReq req);

    Long getPendingCount(@Param("enterpriseNo") String enterpriseNo, @Param("businessFlag") Integer businessFlag, @Param("deleted") Integer deleted);

    List<SupplierV2> queryStandardInfo(SupplierStandardQueryReq supplierStandardQueryReq);

    List<SupplierV2WithBiz> queryWithBizInfo(SupplierStandardQueryReq supplierStandardQueryReq);

    List<SupplierV2WithBiz> selectSupplierBizByCodeOrgPair(SupplierCodeOrgPairListReq supplierCodeOrgPairListReq);

    void batchUpdateForDA(@Param("list") List<SupplierV2> list);

    List<SupplierV2WithBiz> findListPageByFormal(SupplierPageByFormalQueryReq supplierPageByFormalQueryReq);

}




