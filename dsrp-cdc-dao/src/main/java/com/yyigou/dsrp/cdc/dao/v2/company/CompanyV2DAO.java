package com.yyigou.dsrp.cdc.dao.v2.company;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yyigou.ddc.common.util.ValidatorUtils;
import com.yyigou.dsrp.cdc.common.enums.DeletedEnum;
import com.yyigou.dsrp.cdc.dao.config.CdcBaseMapper;
import com.yyigou.dsrp.cdc.dao.v2.company.entity.CompanyV2;
import com.yyigou.dsrp.cdc.model.v2.company.req.CompanyAssociateOrgReq;
import com.yyigou.dsrp.cdc.model.v2.company.req.CompanyQueryListPageForGoodsReq;
import com.yyigou.dsrp.cdc.model.v2.company.req.CompanyQueryListPageReq;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface CompanyV2DAO extends CdcBaseMapper<CompanyV2> {
    /**
     * 根据租户编码和企业编号查询企业列表
     *
     * @param enterpriseNo
     * @param companyNo
     * @return: {@link CompanyV2}
     */
    default CompanyV2 findByEnterpriseNoAndCompanyNo(String enterpriseNo, String companyNo) {
        ValidatorUtils.checkEmptyThrowEx(enterpriseNo, "租户编号不能为空");
        ValidatorUtils.checkEmptyThrowEx(companyNo, "企业编号不能为空");

        LambdaQueryWrapper<CompanyV2> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CompanyV2::getEnterpriseNo, enterpriseNo)
                .eq(CompanyV2::getCompanyNo, companyNo)
                .eq(CompanyV2::getDeleted, DeletedEnum.UN_DELETE.getValue());
        return selectOne(wrapper);
    }

    /**
     * 根据租户编码和企业编号查询企业列表
     *
     * @param enterpriseNo
     * @param companyName
     * @return: {@link CompanyV2}
     */
    default List<CompanyV2> findByEnterpriseNoAndCompanyList(String enterpriseNo, String companyName) {
        ValidatorUtils.checkEmptyThrowEx(enterpriseNo, "租户编号不能为空");
        ValidatorUtils.checkEmptyThrowEx(companyName, "企业编号不能为空");

        LambdaQueryWrapper<CompanyV2> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CompanyV2::getEnterpriseNo, enterpriseNo)
                .eq(CompanyV2::getCompanyName, companyName)
                .eq(CompanyV2::getDeleted, DeletedEnum.UN_DELETE.getValue());
        return selectList(wrapper);
    }

    default List<CompanyV2> findByEnterpriseNoAndCompanyNoList(String enterpriseNo, List<String> companyNoList) {
        return selectList(Wrappers.<CompanyV2>lambdaQuery()
                .eq(CompanyV2::getEnterpriseNo, enterpriseNo)
                .in(CompanyV2::getCompanyNo, companyNoList)
                .eq(CompanyV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
    }

    default List<CompanyV2> findCompanyListPage(CompanyQueryListPageReq queryReq) {
        return selectList(Wrappers.<CompanyV2>lambdaQuery()
                .eq(CompanyV2::getEnterpriseNo, queryReq.getEnterpriseNo())
                .eq(CompanyV2::getDeleted, DeletedEnum.UN_DELETE.getValue())
                .eq(null != queryReq.getFactoryType(), CompanyV2::getFactoryType, queryReq.getFactoryType())
//                .eq(null != queryReq.getIsAssociatedEnterprise(), CompanyV2::getIsAssociatedEnterprise, queryReq.getIsAssociatedEnterprise())
                .like(StringUtils.isNotBlank(queryReq.getPartnership()), CompanyV2::getPartnership, queryReq.getPartnership())
                .like(StringUtils.isNotEmpty(queryReq.getCompanyNameKeywords()), CompanyV2::getCompanyName, queryReq.getCompanyNameKeywords())
                .like(StringUtils.isNotEmpty(queryReq.getUnifiedSocialCodeKeywords()), CompanyV2::getUnifiedSocialCode, queryReq.getUnifiedSocialCodeKeywords())
                .in(CollectionUtils.isNotEmpty(queryReq.getCompanyNameList()), CompanyV2::getCompanyName, queryReq.getCompanyNameList())
                .in(CollectionUtils.isNotEmpty(queryReq.getIdList()), CompanyV2::getCompanyNo, queryReq.getIdList()));
    }

    default List<CompanyV2> findCompanyListPageForGoods(CompanyQueryListPageForGoodsReq queryReq) {
        LambdaQueryWrapper<CompanyV2> queryWrapper = Wrappers.<CompanyV2>lambdaQuery()
                .eq(CompanyV2::getEnterpriseNo, queryReq.getEnterpriseNo())
                .eq(CompanyV2::getDeleted, DeletedEnum.UN_DELETE.getValue());
        if (StringUtils.isNotEmpty(queryReq.getUnionKeyWord())) {
            queryWrapper.and(wrapper -> {
                wrapper.like(CompanyV2::getCompanyName, queryReq.getUnionKeyWord())
                        .or()
                        .like(CompanyV2::getCompanyCode, queryReq.getUnionKeyWord())
                        .or()
                        .like(CompanyV2::getUnifiedSocialCode, queryReq.getUnionKeyWord());
            });
        }

        return selectList(queryWrapper);
    }

    void batchUpdateForDA(@Param("list") List<CompanyV2> list);


}
