package com.yyigou.dsrp.cdc.dao.customer.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * @TableName bdc_customer_sales_man
 */
@TableName(value ="bdc_customer_sales_man")
@Data
public class CustomerSalesMan implements Serializable {
    @TableId(type = IdType.AUTO)
    private Long id;
    private String manCode;
    private String customerNo;

    private String orgNo;

    private String orgName;

    private String salesManNo;

    private String salesManName;

    private Integer isDefault;

    private Integer orderSpecialist;

    private String operateNo;

    private String operateName;

    private String operateTime;

    private String post;

    private String areaNo;

    private String areaCode;

    private String areaName;

    private Integer deleted;

    private static final long serialVersionUID = 1L;
}