package com.yyigou.dsrp.cdc.dao.v2.customer.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@TableName("bdc_customer_biz")
@Data
public class  CustomerBiz implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 客户主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    private Integer deleted;

    /**
     * 客户编号
     */
    private String customerNo;

    /**
     * 本企业编号（由scs平台分配）
     */
    private String enterpriseNo;


    /**
     * 使用组织编号
     */
    private String useOrgNo;

    /**
     * 使用组织修改标志 0：未修改 1：已修改
     */
    private int useOrgModifyFlag;

    /**
     * 客户编码
     */
    private String customerCode;

    /**
     * 合作性质
     */
    private String cooperationMode;

    /**
     * 价格分类编码
     */
    private String priceCategoryCode;

    /**
     * 创建人编号
     */
    private String createNo;

    /**
     * 制单人
     */
    private String createName;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 修改时间
     */
    private String operateNo;

    /**
     * 修改人名称
     */
    private String operateName;

    /**
     * 修改时间
     */
    private String operateTime;

    /**
     * 最后操作时间戳
     */
    private Date opTimestamp;

    /**
     * 业务归属
     */
    private String ownerCompany;

    /**
     * 信用额度
     */
    private String creditAmount;

    /**
     * 信用期限
     */
    private String creditDates;

    /**
     * 结算方式
     */
    private String settlementModes;

    /**
     * 结算方式名称
     */
    private String settlementModesName;

    /*
     * 收款协议
     */
    private String  receiveAgreement;

    /**
     * 收款条件
     */
    private String receiveCondition;

    /**
     * 业务性质
     */
    private String businessType;

    /**
     * 业务性质名称
     */
    private String businessTypeName;

    /**
     * 合作起始时间
     */
    private String coopStartTime;

    /**
     * 合作结束时间
     */
    private String coopEndTime;

    /**
     * 管控类型id
     */
//    private Long controlId;

    /**
     * 管控类型名称
     */
//    private String controlTypeName;

    /**
     * 交易币种
     */
    private String currencyId;

    /**
     * oms客户编号
     */
    private String omsCustomerNo;

}
