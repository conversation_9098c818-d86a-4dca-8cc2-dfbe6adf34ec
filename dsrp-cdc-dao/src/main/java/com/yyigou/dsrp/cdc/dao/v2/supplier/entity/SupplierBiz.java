package com.yyigou.dsrp.cdc.dao.v2.supplier.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@TableName("bdc_supplier_biz")
@Data
public class SupplierBiz implements Serializable {
    private static final long serialVersionUID = 1L;

    @EntityField(name = "主键")
    @TableId(type = IdType.AUTO)
    private Long id;

    @EntityField(name = "是否删除：0-否，1-是")
    private Integer deleted;

    @EntityField(name = "供应商编号")
    private String supplierNo;

    @EntityField(name = "本企业编号（由scs平台分配）")
    private String enterpriseNo;

    @EntityField(name = "使用组织编号")
    private String useOrgNo;

    @EntityField(name = "使用组织修改标志")
    private int useOrgModifyFlag;

    @EntityField(name = "合作性质")
    private String cooperationMode;

    @EntityField(name = "归属公司")
    private String ownerCompany;

    @EntityField(name = "创建人编号")
    private String createNo;

    @EntityField(name = "制单人")
    private String createName;

    @EntityField(name = "创建时间")
    private String createTime;

    @EntityField(name = "修改时间")
    private String operateNo;

    @EntityField(name = "修改人名称")
    private String operateName;

    @EntityField(name = "修改时间")
    private String operateTime;

    @EntityField(name = "最后操作时间戳")
    private Date opTimestamp;

    @EntityField(name = "供应商编码")
    private String supplierCode;

    @EntityField(name = "信用额度")
    private String creditAmount;

    @EntityField(name = "结算方式")
    private String settlementModes;

    @EntityField(name = "付款条件")
    private String paymentTerm;

    @EntityField(name = "付款条件名称")
    private String paymentTermName;

    @EntityField(name = "合作起始时间")
    private String coopStartTime;

    @EntityField(name = "合作结束时间")
    private String coopEndTime;

    @EntityField(name = "付款协议id")
    private Long paymentAgreementId;

    @EntityField(name = "付款协议id(ys的id)")
    private String paymentAgreementYsId;

    @EntityField(name = "付款协议编码")
    private String paymentAgreementCode;

    @EntityField(name = "付款协议名称")
    private String paymentAgreementName;

    @EntityField(name = "账期天数")
    private Integer periodDays;

//    @EntityField(name = "管控类型id")
//    private Long controlId;
//
//    @EntityField(name = "管控类型名称")
//    private String controlTypeName;

    @EntityField(name = "oms供应商编号")
    private String omsSupplierNo;

    @EntityField(name = "币种id")
    private String currencyId;
}
