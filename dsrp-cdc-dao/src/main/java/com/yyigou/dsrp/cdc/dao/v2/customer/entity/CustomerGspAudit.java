package com.yyigou.dsrp.cdc.dao.v2.customer.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;

@TableName("bdc_customer_gsp_audit")
@Data
public class CustomerGspAudit implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id")
    private Long id;

    /**
     * 本企业编号（由scs平台分配）
     */
    @EntityField(name = "企业编号")
    private String enterpriseNo;

    /**
     * 使用组织编号
     */
    @EntityField(name = "使用组织编号")
    private String useOrgNo;

    /**
     * 客户编号
     */
    @EntityField(name = "客户编号")
    private String customerNo;

    /**
     * 客户编码
     */
    @EntityField(name = "客户编码")
    private String customerCode;

    @EntityField(name = "首营状态")
    private Integer gspAuditStatus;

    @EntityField(name = "首营结果")
    private String gspAuditResult;

    @EntityField(name = "是否删除：0-否，1-是")
    private Integer deleted;

    @EntityField(name = "数据状态：0-无效，1-有效")
    private Integer status;
}
