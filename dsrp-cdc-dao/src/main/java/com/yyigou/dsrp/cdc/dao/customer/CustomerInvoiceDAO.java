package com.yyigou.dsrp.cdc.dao.customer;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yyigou.dsrp.cdc.common.enums.CommonStatusEnum;
import com.yyigou.dsrp.cdc.common.enums.DeletedEnum;
import com.yyigou.dsrp.cdc.dao.config.CdcBaseMapper;
import com.yyigou.dsrp.cdc.dao.customer.entity.CustomerInvoice;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【bdc_customer_invoice(发票信息)】的数据库操作Mapper
 * @createDate 2024-06-03 14:57:37
 * @Entity com.yyigou.dsrp.cdc.dao.customer.entity.CustomerInvoice
 */
public interface CustomerInvoiceDAO extends CdcBaseMapper<CustomerInvoice> {


    default List<CustomerInvoice> getCustomerInvoiceByCustomerNo(String enterpriseNo, String customerNo) {
        LambdaQueryWrapper<CustomerInvoice> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(CustomerInvoice::getEnterpriseNo, enterpriseNo)
                .eq(CustomerInvoice::getStatus, CommonStatusEnum.EFFECTIVE.getValue())
                .eq(CustomerInvoice::getCustomerNo, customerNo);
        return selectList(wrapper);
    }

    default List<CustomerInvoice> getCustomerInvoiceByCustomerNoList(String enterpriseNo, List<String> customerNoList) {
        LambdaQueryWrapper<CustomerInvoice> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(CustomerInvoice::getEnterpriseNo, enterpriseNo)
                .eq(CustomerInvoice::getStatus, CommonStatusEnum.EFFECTIVE.getValue())
                .in(CustomerInvoice::getCustomerNo, customerNoList);
        return selectList(wrapper);
    }


    default void deleteCustomerInvoiceByCustomerNo(String enterpriseNo, String customerNo) {
        update(new CustomerInvoice(), new UpdateWrapper<CustomerInvoice>().lambda().set(CustomerInvoice::getStatus, DeletedEnum.DELETED.getValue())
                .eq(CustomerInvoice::getEnterpriseNo, enterpriseNo)
                .eq(CustomerInvoice::getCustomerNo, customerNo)
                .eq(CustomerInvoice::getStatus, CommonStatusEnum.EFFECTIVE.getValue()));
    }
    default void deleteCustomerInvoiceByCustomerNo(String enterpriseNo, String customerNo,List<Long> idList) {
        update(new CustomerInvoice(), new UpdateWrapper<CustomerInvoice>().lambda().set(CustomerInvoice::getStatus, DeletedEnum.DELETED.getValue())
                .eq(CustomerInvoice::getEnterpriseNo, enterpriseNo)
                .eq(CustomerInvoice::getCustomerNo, customerNo)
                .in(CustomerInvoice::getId, idList)
                .eq(CustomerInvoice::getStatus, CommonStatusEnum.EFFECTIVE.getValue()));
    }



    default void deleteCustomerInvoiceByCustomerNoList(String enterpriseNo, List<String> customerNoList) {
        update(new CustomerInvoice(), new UpdateWrapper<CustomerInvoice>().lambda().set(CustomerInvoice::getStatus, DeletedEnum.DELETED.getValue())
                .eq(CustomerInvoice::getEnterpriseNo, enterpriseNo)
                .in(CustomerInvoice::getCustomerNo, customerNoList)
                .eq(CustomerInvoice::getStatus, CommonStatusEnum.EFFECTIVE.getValue()));
    }

}




