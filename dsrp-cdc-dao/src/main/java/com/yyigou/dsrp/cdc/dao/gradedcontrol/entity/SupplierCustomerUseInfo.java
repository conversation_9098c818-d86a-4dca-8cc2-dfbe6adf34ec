package com.yyigou.dsrp.cdc.dao.gradedcontrol.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * bdc_supplier_customer_use_info
 *
 * <AUTHOR>
@Data
@TableName("bdc_supplier_customer_use_info")
public class SupplierCustomerUseInfo implements Serializable {
    private static final long serialVersionUID = 5921962902429555066L;
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 企业类型 1-供应商 2-客户
     */
    private Integer companyType;

    /**
     * 供应商/客户名称
     */
    private String companyName;

    /**
     * 供应商/客户编号
     */
    private String companyCode;

    /**
     * 供应商/客户对应企业编号
     */
    private String companyNo;

    /**
     * 集团企业编号
     */
    private String groupEnterpriseNo;

    /**
     * 档案管理权企业编号
     */
    private String manageEnterpriseNo;

    /**
     * 使用组织编号
     */
    private String useOrgNo;

    /**
     * 使用组织名称
     */
    private String useOrgName;

    /**
     * 使用组织企业编号
     */
    private String useEnterpriseNo;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 是否有效  1是 0否
     */
    private Integer status;

    /**
     * 是否删除  1是 0 否
     */
    private Integer deleted;

    /**
     * 创建人编号
     */
    private String createNo;

    /**
     * 创建人名称
     */
    private String createName;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 修改人编号
     */
    private String modifyNo;

    /**
     * 修改人名称
     */
    private String modifyName;

    /**
     * 修改时间
     */
    private String modifyTime;

}