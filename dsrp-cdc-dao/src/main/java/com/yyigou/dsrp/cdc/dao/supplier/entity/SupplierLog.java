package com.yyigou.dsrp.cdc.dao.supplier.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;

/**
 * SupplierLog 
 *
 * 客户日志
 */
@Data
@TableName(value = "bdc_supplier_log")
public class SupplierLog implements Serializable {
    private static final long serialVersionUID = 526898694034977927L;
    /**
     * 客户编号
     */
    @EntityField(name = "客户编号")
	private String supplierNo;
    /**
     * 操作内容
     */
    @EntityField(name = "操作内容")
    private String operateContent;
     /**
     * 操作员编号
     */
    @EntityField(name = "操作员编号")
	private String operateNo;
     /**
     * 操作员
     */
    @EntityField(name = "操作员")
	private String operateName;
     /**
     * 
     */
    @EntityField(name = "")
	private String operateTime;
}