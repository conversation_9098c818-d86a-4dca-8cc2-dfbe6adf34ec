package com.yyigou.dsrp.cdc.dao.v2.supplier.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;

@TableName("bdc_supplier_gsp_audit")
@Data
public class SupplierGspAudit implements Serializable {

    private static final long serialVersionUID = 1L;

    @EntityField(name = "主键")
    @TableId(value = "id")
    private Long id;

    @EntityField(name = "企业编号")
    private String enterpriseNo;

    @EntityField(name = "使用组织编号")
    private String useOrgNo;

    @EntityField(name = "供应商编号")
    private String supplierNo;

    @EntityField(name = "供应商编码")
    private String supplierCode;

    @EntityField(name = "首营状态")
    private Integer gspAuditStatus;

    @EntityField(name = "首营结果")
    private String gspAuditResult;

    @EntityField(name = "是否删除：0-否，1-是")
    private Integer deleted;

    @EntityField(name = "数据状态：0-无效，1-有效")
    private Integer status;
}
