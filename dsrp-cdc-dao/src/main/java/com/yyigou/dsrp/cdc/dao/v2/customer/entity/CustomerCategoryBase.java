package com.yyigou.dsrp.cdc.dao.v2.customer.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @TableName bdc_customer_category_base
 */
@TableName(value = "bdc_customer_category_base")
@Data
public class CustomerCategoryBase implements Serializable {
    private static final long serialVersionUID = 1L;
    @TableId(type = IdType.AUTO)
    private Long id;
    private String enterpriseNo;
    private String categoryNo;
    private String categoryCode;
    private String manageOrgNo;
    private String useOrgNo;
    private Integer deleted;
    private String createNo;
    private String createName;
    private String createTime;
    private String operateNo;
    private String operateName;
    private String operateTime;
    private Date opTimestamp;
}