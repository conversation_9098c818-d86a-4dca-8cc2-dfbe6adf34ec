package com.yyigou.dsrp.cdc.dao.supplier.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @TableName bdc_supplier_category
 */
@TableName(value = "bdc_supplier_category")
@Data
public class SupplierCategory implements Serializable {

    @TableId(type = IdType.NONE)
    private String no;

    private String enterpriseNo;

    private String parentNo;

    private String mnemonicCode;

    private String categoryCode;

    private String categoryName;

    private String remark;

    private String status;

    private Integer sortNum;

    private Integer deleted;

    private String createNo;

    private String createName;

    private String createTime;

    private String operateNo;

    private String operateName;

    private String operateTime;

    private Date opTimestamp;

    private Integer opRevsion;

    private String opType;

    private String ysSyncFlag;

    private String groupNo;

    private static final long serialVersionUID = 1L;
}