package com.yyigou.dsrp.cdc.dao.customer.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * @TableName bdc_customer_area
 */
@TableName(value = "bdc_customer_area")
@Data
public class CustomerArea implements Serializable {

    @TableId(type = IdType.AUTO)
    private Integer id;

    private String customerNo;

    private String customerName;

    private String areaNo;

    private String areaName;

    private String areaCode;

    private String enterpriseNo;

    private String operateNo;

    private String operateName;

    private String operateTime;

    private Integer deleted;

    private Integer isDefault;

    private String parentAreaNo;

    private static final long serialVersionUID = 1L;
}