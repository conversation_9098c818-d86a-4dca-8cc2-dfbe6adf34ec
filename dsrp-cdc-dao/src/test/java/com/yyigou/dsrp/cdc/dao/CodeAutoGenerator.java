package com.yyigou.dsrp.cdc.dao;

import com.baomidou.mybatisplus.generator.FastAutoGenerator;
import com.baomidou.mybatisplus.generator.config.OutputFile;

import java.util.Collections;

/**
 * mybatis-plus代码自动生成
 *
 * @author: Moore
 * @date: 2023/7/31 14:31
 * @version: 1.0.0
 */
public class CodeAutoGenerator {

    public static void main(String[] args) {
        String projectPath = System.getProperty("user.dir");
        String generatorPath = "/dsrp-cdc-dao/src/test/java/generator/";
        FastAutoGenerator.create("**********************************************************************************************************", "devdba", "123456")
                .globalConfig(builder -> {
                    builder.author("cdc") // 设置作者
                            .outputDir(projectPath + generatorPath) // 指定输出目录
                            .disableOpenDir()
                    ; //禁止打开输出目录
                })
                .packageConfig(builder -> {
                    builder.parent("com.yyigou.dsrp.cdc.dao") // 设置父包名
                            //.moduleName("system") // 设置父包模块名
                            .mapper("dao")
                            .entity("entity")
                            .pathInfo(Collections.singletonMap(OutputFile.xml, projectPath + generatorPath + "/xml/")); // 设置mapperXml生成路径
                })
                .strategyConfig(builder -> {
                    builder.addInclude("bdc_supplier")// 设置需要生成的表名
                            .addTablePrefix("bdc_", "c_", "equ_"); // 设置过滤表前缀
                    builder.mapperBuilder().formatMapperFileName("%sDAO");
//                    builder.entityBuilder().formatFileName("%sDO").enableLombok();
                })
//                .templateConfig(builder -> {
//                    builder.;
//                })
                .execute();
    }
}
