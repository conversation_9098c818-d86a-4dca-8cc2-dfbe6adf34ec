-- 依赖服务：架构组的纳税主体组织消息

CREATE TABLE `bdc_company_associated_org` (
                                              `id` bigint(20) NOT NULL AUTO_INCREMENT,
                                              `enterprise_no` varchar(32) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT '' COMMENT '租户编号',
                                              `company_no` varchar(32) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL COMMENT '企业编号',
                                              `source_type` tinyint(11) NOT NULL COMMENT '来源类型：1-供应商，2-客户，3-纳税主体组织',
                                              `source_code` varchar(50) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT '' COMMENT '来源编码',
                                              `associated_org_no` varchar(50) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT '' COMMENT '关联组织编号',
                                              `associated_org_code` varchar(50) CHARACTER SET utf8 DEFAULT NULL COMMENT '关联组织编码',
                                              `associated_org_name` varchar(256) DEFAULT NULL COMMENT '关联组织名称',
                                              `status` tinyint(1) DEFAULT '1' COMMENT '数据是否有效：0-无效，1-有效',
                                              `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '删除标志 0：未删除 1：已删除',
                                              `create_no` varchar(32) DEFAULT '' COMMENT '创建人编号',
                                              `create_name` varchar(100) DEFAULT '' COMMENT '创建人名称',
                                              `create_time` varchar(19) DEFAULT '' COMMENT '创建时间',
                                              `modify_no` varchar(32) DEFAULT '' COMMENT '操作人编号',
                                              `modify_name` varchar(100) DEFAULT '' COMMENT '操作人名称',
                                              `modify_time` varchar(19) DEFAULT '' COMMENT '操作时间',
                                              `op_timestamp` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '最后操作时间戳',
                                              PRIMARY KEY (`id`),
                                              KEY `idx_enterpriseno_orgno` (`enterprise_no`,`associated_org_no`),
                                              KEY `idx_enterpriseno_companyno` (`enterprise_no`,`company_no`),
                                              KEY `idx_enterpriseno_sourcecode` (`enterprise_no`,`source_code`,`source_type`),
                                              KEY `idx_enterpriseno_orgcode` (`enterprise_no`,`associated_org_code`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='企业组织关联表';


-- 初始化sql,注意：不涉及历史客户、供应商的迁移（因为原来的关联关系是存在企业档案上，无法直接反推）
INSERT bdc_company_associated_org (enterprise_no, company_no, source_type, source_code, associated_org_no, STATUS, deleted,associated_org_code,associated_org_name) SELECT
                                                                                                                                                                        enterprise_no,
                                                                                                                                                                        company_no,
                                                                                                                                                                        2,
                                                                                                                                                                        customer_code,
                                                                                                                                                                        associated_org_no,
                                                                                                                                                                        1,
                                                                                                                                                                        0,
                                                                                                                                                                        associated_org_code,
                                                                                                                                                                        associated_org_name
FROM
    bdc_customer
WHERE
    deleted = 0
  AND is_associated_enterprise = 1;

INSERT bdc_company_associated_org (enterprise_no, company_no, source_type, source_code, associated_org_no, STATUS, deleted,associated_org_code,associated_org_name) SELECT
                                                                                                                                                                        enterprise_no,
                                                                                                                                                                        company_no,
                                                                                                                                                                        1,
                                                                                                                                                                        supplier_code,
                                                                                                                                                                        associated_org_no,
                                                                                                                                                                        1,
                                                                                                                                                                        0,
                                                                                                                                                                        associated_org_code,
                                                                                                                                                                        associated_org_name
FROM
    bdc_supplier
WHERE
    deleted = 0
  AND is_associated_enterprise = 1;