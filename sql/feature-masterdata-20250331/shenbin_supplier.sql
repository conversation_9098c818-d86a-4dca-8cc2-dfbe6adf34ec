-- 供应商分类
alter table bdc_supplier_category
    add manage_org_no varchar(32) default '' not null comment '管理组织编号';

alter table bdc_supplier_category
    add level int null comment '层级';

alter table bdc_supplier_category
    add top_category_no varchar(32) null comment '顶级编号';

alter table bdc_supplier_category
    add path varchar(256) null comment '路径';

-- alter table bdc_supplier_category
--     modify no varchar(32) default '' not null comment '分类编号';

-- 供应商分类分派记录
create table bdc_supplier_category_base
(
    id            bigint auto_increment comment '主键'
        primary key,
    enterprise_no varchar(32)  default ''                not null comment '租户编号',
    category_no   varchar(32)                            not null comment '供应商分类编号',
    category_code varchar(32)                            not null comment '供应商分类编码',
    manage_org_no varchar(32)                            not null comment '管理组织',
    use_org_no    varchar(32)                            not null comment '使用组织',
    deleted       tinyint(1)   default 0                 not null comment '删除标志 0：未删除 1：已删除',
    create_no     varchar(32)  default ''                null comment '创建人编号',
    create_name   varchar(100) default ''                null comment '创建人名字',
    create_time   varchar(19)  default ''                null comment '创建时间',
    operate_no    varchar(32)  default ''                null comment '修改人编号',
    operate_name  varchar(100) default ''                null comment '修改人名字',
    operate_time  varchar(19)  default ''                null comment '修改时间',
    op_timestamp  timestamp    default CURRENT_TIMESTAMP null comment '最后操作时间戳'
)
    comment '供应商分类分级引用记录';

create index idx_ent_org_no
    on bdc_supplier_category_base (enterprise_no, use_org_no, category_no);

-- 供应商
alter table bdc_supplier
    add manage_org_no varchar(32) default '' not null comment '管理组织编号' after enterprise_no;

alter table bdc_supplier
    add is_associated_enterprise tinyint(1) default 0 null comment '是否关联企业: 1是 0否';

alter table bdc_supplier
    add associated_org_no varchar(50) null comment '关联组织编号';

alter table bdc_supplier
    add associated_org_code varchar(50) null comment '关联组织编码';

alter table bdc_supplier
    add associated_org_name varchar(100) null comment '关联组织名称,关联企业时选择';

-- 供应商分派记录
create table bdc_supplier_base
(
    id            bigint auto_increment comment '主键'
        primary key,
    enterprise_no varchar(32)  default ''                not null comment '租户编号',
    supplier_no   varchar(32)                            not null comment '供应商编号',
    supplier_code varchar(32)                            not null comment '供应商编码',
    manage_org_no varchar(32)                            not null comment '管理组织',
    use_org_no    varchar(32)                            not null comment '使用组织',
    deleted       tinyint(1)   default 0                 not null comment '删除标志 0：未删除 1：已删除',
    create_no     varchar(32)  default ''                null comment '创建人编号',
    create_name   varchar(100) default ''                null comment '创建人名字',
    create_time   varchar(19)  default ''                null comment '创建时间',
    operate_no    varchar(32)  default ''                null comment '修改人编号',
    operate_name  varchar(100) default ''                null comment '修改人名字',
    operate_time  varchar(19)  default ''                null comment '修改时间',
    op_timestamp  timestamp    default CURRENT_TIMESTAMP null comment '最后操作时间戳'
)
    comment '供应商档案分级引用记录';

create index idx_ent_org_supp
    on bdc_supplier_base (enterprise_no, use_org_no, customer_code);

-- 供应商业务信息
create table bdc_supplier_biz
(
    id                      bigint auto_increment comment '客户业务信息主键'
        primary key,
    enterprise_no           varchar(32)                                     default ''                not null comment '本企业编号（由scs平台分配）',
    supplier_no             varchar(32)                                     default ''                not null comment '供应商编号',
    supplier_code           varchar(32)                                     default ''                not null comment '供应商编码',
    use_org_no              varchar(32)                                     default ''                not null comment '使用组织编号',
    use_org_modify_flag     tinyint(1)                                      default 0                 not null comment '是否修改过，0否 1是',
    cooperation_mode        varchar(10) charset utf8                                                  null comment '合作性质',
    grade_sytem_code        varchar(32) charset utf8                        default ''                null comment '等级体系编号',
    grade_code              varchar(50) charset utf8                        default ''                null comment '等级编号',
    is_gsp_control          tinyint(1)                                      default 0                 null comment '是否GSP 管控 1：是 0：否',
    gsp_audit_status        enum ('9', '3', '2', '1', '0') collate utf8_bin default '0'               null comment 'GSP的审核状态 0:未创建，1：待审批 2：审批通过
3：审核失败;9-审批撤回',
    business_flag           tinyint(1)                                      default 0                 null comment '业务状态：0:草稿 1:正式',
    control_status          enum ('3', '2', '1') charset utf8               default '1'               null comment '管控状态 1：启用 2：停用 3：冻结',
    black_history           tinyint(1)                                      default 0                 null comment '是否有黑历史 1：是 0：否',
    manage_scope            text charset utf8                                                         null comment '经营范围',
    is_sync_scs             int(1)                                          default 0                 null comment '同步scs状态：0：未同步 1：同步成功 2：同步失败 3：已协同',
    is_sync_wms             int(1)                                          default 0                 null comment '是否已经同步到wms 0：否 1：是',
    is_sync_erp             int(1)                                          default 0                 null comment '是否同步 0：否，1：已不同',
    create_no               varchar(32)                                     default ''                null comment '创建人编号',
    create_name             varchar(100)                                    default ''                null comment '制单人',
    create_time             varchar(19)                                     default ''                null comment '创建时间',
    operate_no              varchar(32)                                     default ''                null comment '修改时间',
    operate_name            varchar(100)                                    default ''                null comment '修改人名称',
    operate_time            varchar(19)                                     default ''                null comment '修改时间',
    op_timestamp            timestamp                                       default CURRENT_TIMESTAMP not null comment '最后操作时间戳',
    op_revsion              int                                             default 1                 not null comment '数据记录版本',
    op_type                 char collate utf8mb4_bin                        default 'I'               not null comment '数据操作类型 I:新增 U：更新 D:删除',
    ys_sync_flag            varchar(1) charset utf8                         default '0'               null comment 'ys同步标识 0:未同步 1:同步成功 2:同步失败',
    institutional_type      varchar(10) charset utf8                        default ''                null comment '机构类型：yy:医院,dsfsys:第三方实验室,dsftjzx:第三方体检中心,jkzx:疾控中心,xz:血站,zs:诊所,ylhly:养老护理院,jgqt:其他',
    hospital_type           varchar(4) collate utf8mb4_bin                  default ''                null comment '医院类型:yy：公立医院，mbyy:民营医院',
    hospital_class          int(10)                                                                   null comment '医院等级：0:无等级,1:一级甲等,2:一级乙等,3:一级丙等,4:二级甲等,5:二级乙等,6:二级丙等,7:三级特等,8:三级甲等,9:三级乙等,10:三级丙等',
    is_account_manager      int(1)                                          default 0                 null comment '是否账期管理 1：是 0：否',
    credit_rate             varchar(10) charset utf8                        default ''                null comment '信用等级',
    credit_rate_name        varchar(100) charset utf8                       default ''                null comment '信用等级名称',
    credit_amount           varchar(20) charset utf8                                                  null comment '信用额度',
    credit_dates            varchar(20) charset utf8                        default ''                null comment '信用期限',
    settlement_modes        varchar(10) charset utf8                        default ''                null comment '结算方式',
    payment_term            varchar(20) charset utf8                        default ''                null comment '付款条件',
    payment_term_name       varchar(100) charset utf8                       default ''                null comment '付款条件名称',
    coop_start_time         varchar(19) charset utf8                                                  null comment '合作起始时间',
    coop_end_time           varchar(19) charset utf8                                                  null comment '合作结束时间',
    scs_push_result         text collate utf8_bin                                                     null comment 'scs推送结果',
    ys_push_result          text collate utf8_bin                                                     null comment 'ys推送结果',
    wms_push_result         text collate utf8_bin                                                     null comment 'wms推送结果',
    qt_remark               varchar(255) charset utf8                                                 null,
    payment_agreement_id    bigint                                                                    null comment '付款协议id(本地的id)',
    payment_agreement_ys_id varchar(20) collate utf8_bin                                              null comment '付款协议id(ys的id)',
    payment_agreement_code  varchar(30) collate utf8_bin                                              null comment '付款协议编码',
    payment_agreement_name  varchar(50) collate utf8_bin                                              null comment '付款协议名称',
    period_days             int(5)                                                                    null comment '账期天数',
    control_id              bigint                                                                    null comment '管控类型id',
    control_type_name       varchar(128)                                                              null comment '管控类型名称',
    version                 int(10)                                         default 1                 null comment '版本号',
    oms_supplier_no         varchar(32) collate utf8mb4_bin                 default ''                null comment 'oms供应商编号',
    tc_sync_flag            tinyint(1)                                      default 0                 null comment '通昶同步标记 0未同步 1同步成功 2同步失败',
    tc_sync_result          text collate utf8_bin                                                     null comment '通昶同步结果（失败原因）',
    currency                varchar(50) collate utf8_bin                                              null comment '交易币种',
    transaction_type        varchar(50) collate utf8_bin                                              null comment '交易类型',
    owner_company           varchar(100)                                    default ''                null comment '归属公司'
)
    comment '供应商档案业务信息';

alter table bdc_supplier_biz add column deleted tinyint(1) default 0 null comment '是否删除：0-否，1-是';

create index idx_ent_org_supp
    on bdc_supplier_biz (enterprise_no, use_org_no, customer_code);

-- 供应商联系人
alter table bdc_company_linkman
    modify source_no varchar(32) null comment '来源no';

alter table bdc_company_linkman
    add uid varchar(50) null comment 'uuid';

alter table bdc_company_linkman
    add use_org_no varchar(32) default '' not null comment '使用组织编号';

-- 供应商地址
alter table bdc_company_shipping_address
    add use_org_no varchar(32) default '' not null comment '使用组织编号';

-- 银行
alter table bdc_supplier_bank
    add manage_org_no varchar(32) default '' not null comment '管理组织编号';

alter table bdc_supplier_bank
    add currency varchar(32) default '' not null comment '币种';

-- 供应商负责人
alter table bdc_supplier_order_man
    add supplier_code varchar(32) default '' not null comment '供应商编码' after supplier_no;

alter table bdc_supplier_order_man
    add use_org_no varchar(32) default '' not null comment '使用组织编号';

alter table bdc_supplier_order_man
    add enterprise_no varchar(32) default '' not null comment '租户编号';

alter table bdc_supplier_order_man
    add dept_no varchar(32) null comment '部门编号';

alter table bdc_supplier_order_man
    add dept_name varchar(100) default '' null comment '部门名称';



create table bdc_supplier_gsp_audit
(
    `id`               bigint auto_increment
        primary key,
    `enterprise_no`    varchar(32) default ''  not null comment '企业编号',
    `use_org_no`       varchar(32) default ''  not null comment '使用组织编号',
    `supplier_code`    varchar(32) default ''  not null comment '供应商编码',
    `supplier_no`      varchar(32) default ''  not null comment '供应商编号',
    `gsp_audit_status` int         default '0' not null comment '首营状态',
    `gsp_audit_result` varchar(256) comment '首营结果',
    `deleted`           tinyint(1)   DEFAULT '0' COMMENT '是否删除 1是 0否',
    `status`            tinyint(1)   DEFAULT '1' COMMENT '是否有效  0无效 1有效'
) comment '供应商档案首营记录表' charset = utf8mb4;
create index idx_ent_org_code
    on bdc_supplier_gsp_audit (enterprise_no, use_org_no, supplier_code);

alter table bdc_supplier_base
    add column control_status enum ('3', '2', '1') charset utf8 default '1' null comment '管控状态 1：启用 2：停用 3：冻结';

alter table bdc_supplier_biz
drop column currency;

alter table bdc_supplier_biz
    add currency_id varchar(50) null comment '币种' after oms_supplier_no;



create table bdc_supplier_apply
(
    id                bigint auto_increment
        primary key,
    apply_instance_no varchar(32)                          null comment '申请单号',
    enterprise_no     varchar(32)                          null comment '租户编号',
    apply_org_no      varchar(32)                          null comment '申请组织编号',
    apply_org_name    varchar(128)                         null comment '申请组织名称',
    use_org_no        varchar(32)                          null comment '使用组织编号',
    use_org_name      varchar(32)                          null comment '使用组织名称',
    manage_org_no     varchar(32)                          null comment '管理组织编号',
    manage_org_name   varchar(32)                          null comment '管理组织名称',
    company_no        varchar(32)                          null comment '企业编号',
    company_name      varchar(128)                         null comment '企业名称',
    supplier_code     varchar(32)                          null comment '供应商编码',
    supplier_name     varchar(128)                         null comment '供应商名称',
    apply_type        tinyint(1)                           null comment '申请类型 2-新增申请 3-变更申请',
    apply_reason      varchar(512)                         null comment '申请原因',
    apply_desc        varchar(512)                         null comment '申请说明',
    audit_status      tinyint(1) default 0                 null comment '审核状态：0-草稿 1-待审核 2-审核通过 3-审核拒绝',
    apply_result      tinyint(1)                           null comment '申请结果 1-成功 2-失败',
    apply_no          varchar(32)                          null comment '申请人编号',
    apply_name        varchar(128)                         null comment '申请人名称',
    apply_time        varchar(19)                          null comment '申请时间',
    audit_no          varchar(32)                          null comment '审核人编号',
    audit_name        varchar(128)                         null comment '审核人名称',
    audit_time        varchar(19)                          null comment '审核时间',
    audit_remark      varchar(512)                         null comment '审核意见',
    fail_reason       varchar(512)                         null comment '失败原因',
    deleted           tinyint(1) default 0                 null comment '是否删除 1是 0否',
    status            tinyint(1) default 1                 null comment '是否有效  0无效 1有效',
    create_no         varchar(32)                          null comment '创建人编号',
    create_name       varchar(128)                         null comment '创建人',
    create_time       varchar(19)                          null comment '创建时间',
    modify_no         varchar(32)                          null comment '更新人编号',
    modify_name       varchar(128)                         null comment '更新人',
    modify_time       varchar(19)                          null comment '更新时间',
    op_timestamp      timestamp  default CURRENT_TIMESTAMP null comment '最后操作时间戳'
)
    comment '供应商档案申请';

create index idx_ent_apply
    on bdc_supplier_apply (enterprise_no, apply_org_no);

create index idx_ent_manage
    on bdc_supplier_apply (enterprise_no, manage_org_no);

create index idx_ent_no
    on bdc_supplier_apply (enterprise_no, apply_instance_no);


create table bdc_supplier_apply_item
(
    id                  bigint auto_increment
        primary key,
    enterprise_no       varchar(32) null comment '租户编号',
    apply_instance_id   bigint      null comment '供应商申请单主表id',
    apply_instance_no   varchar(32) null comment '申请单号',
    pre_approve_content mediumtext  null comment '审批通过前的详情JSON',
    approve_content     mediumtext  null comment '审批通过后的详情JSON',
    apply_content       mediumtext  null comment '申请时填写的详情JSON'
)
    comment '供应商档案申请详情';

create index idx_applyid_ent
    on bdc_supplier_apply_item (apply_instance_id, enterprise_no);

create index idx_applyno_ent
    on bdc_supplier_apply_item (apply_instance_no, enterprise_no);

