INSERT INTO t_task_export_template (template_id, template_name, template_status, export_limit, burst_size, data_handle_api_ref, notify_mq_queue, export_file_meta, export_file_name_suffix, export_file_suffix, template_type, is_deleted)
VALUES ('dsrp_cdc_company_v2_export', '企业档案-导出', 1, 60000, 1000, 'dsrp.cdc.company.v2.findCompanyListPage', null, '[{"pojo":"_static","excel":"静态字段"}]', 'yyyy-MM-dd_HHmmss', 'xlsx', 0, 0);


INSERT INTO t_task_export_template (template_id, template_name, template_status, export_limit, burst_size, data_handle_api_ref, notify_mq_queue, export_file_meta, export_file_name_suffix, export_file_suffix, template_type, is_deleted)
VALUES ('dsrp_cdc_supplier_v2_manage_export', '供应商档案（管理）-导出', 1, 60000, 1000, 'dsrp.cdc.supplier.v2.manageFindListPage', null, '[{"pojo":"_static","excel":"静态字段"}]', 'yyyy-MM-dd_HHmmss', 'xlsx', 0, 0);

INSERT INTO t_task_export_template (template_id, template_name, template_status, export_limit, burst_size, data_handle_api_ref, notify_mq_queue, export_file_meta, export_file_name_suffix, export_file_suffix, template_type, is_deleted)
VALUES ('dsrp_cdc_supplier_v2_use_export', '供应商档案（使用）-导出', 1, 60000, 1000, 'dsrp.cdc.supplier.v2.useFindListPage', null, '[{"pojo":"_static","excel":"静态字段"}]', 'yyyy-MM-dd_HHmmss', 'xlsx', 0, 0);


INSERT INTO t_task_export_template (template_id, template_name, template_status, export_limit, burst_size, data_handle_api_ref, notify_mq_queue, export_file_meta, export_file_name_suffix, export_file_suffix, template_type, is_deleted)
VALUES ('dsrp_cdc_customer_v2_manage_export', '客户档案（管理）-导出', 1, 60000, 1000, 'dsrp.cdc.customer.v2.manageFindListPage', null, '[{"pojo":"_static","excel":"静态字段"}]', 'yyyy-MM-dd_HHmmss', 'xlsx', 0, 0);

INSERT INTO t_task_export_template (template_id, template_name, template_status, export_limit, burst_size, data_handle_api_ref, notify_mq_queue, export_file_meta, export_file_name_suffix, export_file_suffix, template_type, is_deleted)
VALUES ('dsrp_cdc_customer_v2_use_export', '客户档案（使用）-导出', 1, 60000, 1000, 'dsrp.cdc.customer.v2.useFindListPage', null, '[{"pojo":"_static","excel":"静态字段"}]', 'yyyy-MM-dd_HHmmss', 'xlsx', 0, 0);


INSERT INTO t_task_export_template (template_id, template_name, template_status, export_limit, burst_size, data_handle_api_ref, notify_mq_queue, export_file_meta, export_file_name_suffix, export_file_suffix, template_type, is_deleted)
VALUES ('dsrp_cdc_suppliercategory_v2_export', '供应商分类档案（管理）-导出', 1, 60000, 1000, 'dsrp.cdc.suppliercategory.v2.queryListPage', null, '[{"pojo":"_static","excel":"静态字段"}]', 'yyyy-MM-dd_HHmmss', 'xlsx', 0, 0);

INSERT INTO t_task_export_template (template_id, template_name, template_status, export_limit, burst_size, data_handle_api_ref, notify_mq_queue, export_file_meta, export_file_name_suffix, export_file_suffix, template_type, is_deleted)
VALUES ('dsrp_cdc_customercategory_v2_export', '客户分类档案（管理）-导出', 1, 60000, 1000, 'dsrp.cdc.customercategory.v2.queryListPage', null, '[{"pojo":"_static","excel":"静态字段"}]', 'yyyy-MM-dd_HHmmss', 'xlsx', 0, 0);



INSERT INTO t_task_export_template (template_id, template_name, template_status, export_limit, burst_size, data_handle_api_ref, notify_mq_queue, export_file_meta, export_file_name_suffix, export_file_suffix, template_type, is_deleted)
VALUES ('dsrp_cdc_supplierapply_applyorg_export', '供应商档案申请（申请组织）-导出', 1, 60000, 1000, 'dsrp.cdc.supplierapply.v2.findListPage', null, '[{"pojo":"_static","excel":"静态字段"}]', 'yyyy-MM-dd_HHmmss', 'xlsx', 0, 0);

INSERT INTO t_task_export_template (template_id, template_name, template_status, export_limit, burst_size, data_handle_api_ref, notify_mq_queue, export_file_meta, export_file_name_suffix, export_file_suffix, template_type, is_deleted)
VALUES ('dsrp_cdc_supplierapply_manageorg_export', '供应商档案申请（管理组织）-导出', 1, 60000, 1000, 'dsrp.cdc.supplierapply.v2.findApproveListPage', null, '[{"pojo":"_static","excel":"静态字段"}]', 'yyyy-MM-dd_HHmmss', 'xlsx', 0, 0);

INSERT INTO t_task_export_template (template_id, template_name, template_status, export_limit, burst_size, data_handle_api_ref, notify_mq_queue, export_file_meta, export_file_name_suffix, export_file_suffix, template_type, is_deleted)
VALUES ('dsrp_cdc_customerapply_applyorg_export', '客户档案申请（申请组织）-导出', 1, 60000, 1000, 'dsrp.cdc.customerapply.v2.findListPage', null, '[{"pojo":"_static","excel":"静态字段"}]', 'yyyy-MM-dd_HHmmss', 'xlsx', 0, 0);

INSERT INTO t_task_export_template (template_id, template_name, template_status, export_limit, burst_size, data_handle_api_ref, notify_mq_queue, export_file_meta, export_file_name_suffix, export_file_suffix, template_type, is_deleted)
VALUES ('dsrp_cdc_customerapply_manageorg_export', '客户档案申请（管理组织）-导出', 1, 60000, 1000, 'dsrp.cdc.customerapply.v2.findApproveListPage', null, '[{"pojo":"_static","excel":"静态字段"}]', 'yyyy-MM-dd_HHmmss', 'xlsx', 0, 0);
