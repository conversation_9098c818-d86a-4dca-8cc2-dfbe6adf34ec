-- 客户分类
alter table bdc_customer_category
    add manage_org_no varchar(32) default '' not null comment '管理组织编号';

alter table bdc_customer_category
    add level int null comment '层级';

alter table bdc_customer_category
    add top_category_no varchar(32) null comment '顶级编号';

alter table bdc_customer_category
    add path varchar(256) null comment '路径';

-- alter table bdc_customer_category
--     modify no varchar(32) default '' not null comment '分类编号';

-- 客户分类分派记录
create table bdc_customer_category_base
(
    id            bigint auto_increment comment '主键'
        primary key,
    enterprise_no varchar(32)  default ''                not null comment '租户编号',
    category_no   varchar(32)                            not null comment '客户分类编号',
    category_code varchar(32)                            not null comment '客户分类编码',
    manage_org_no varchar(32)                            not null comment '管理组织',
    use_org_no    varchar(32)                            not null comment '使用组织',
    deleted       tinyint(1)   default 0                 not null comment '删除标志 0：未删除 1：已删除',
    create_no     varchar(32)  default ''                null comment '创建人编号',
    create_name   varchar(100) default ''                null comment '创建人名字',
    create_time   varchar(19)  default ''                null comment '创建时间',
    operate_no    varchar(32)  default ''                null comment '修改人编号',
    operate_name  varchar(100) default ''                null comment '修改人名字',
    operate_time  varchar(19)  default ''                null comment '修改时间',
    op_timestamp  timestamp    default CURRENT_TIMESTAMP null comment '最后操作时间戳'
)
    comment '客户分类分级引用记录';

create index idx_ent_org_no
    on bdc_customer_category_base (enterprise_no, use_org_no, category_no);

-- 客户
alter table bdc_customer
    add manage_org_no varchar(32) default '' not null comment '管理组织编号' after enterprise_no;

alter table bdc_customer
    add is_associated_enterprise tinyint(1) default 0 null comment '是否关联企业: 1是 0否';

alter table bdc_customer
    add associated_org_no varchar(50) null comment '关联组织编号';

alter table bdc_customer
    add associated_org_code varchar(50) null comment '关联组织编码';

alter table bdc_customer
    add associated_org_name varchar(100) null comment '关联组织名称,关联企业时选择';

-- 客户分派记录
create table bdc_customer_base
(
    id            bigint auto_increment comment '主键'
        primary key,
    enterprise_no varchar(32)  default ''                not null comment '租户编号',
    customer_no   varchar(32)                            not null comment '客户编号',
    customer_code varchar(32)                            not null comment '客户编码',
    manage_org_no varchar(32)                            not null comment '管理组织',
    use_org_no    varchar(32)                            not null comment '使用组织',
    deleted       tinyint(1)   default 0                 not null comment '删除标志 0：未删除 1：已删除',
    create_no     varchar(32)  default ''                null comment '创建人编号',
    create_name   varchar(100) default ''                null comment '创建人名字',
    create_time   varchar(19)  default ''                null comment '创建时间',
    operate_no    varchar(32)  default ''                null comment '修改人编号',
    operate_name  varchar(100) default ''                null comment '修改人名字',
    operate_time  varchar(19)  default ''                null comment '修改时间',
    op_timestamp  timestamp    default CURRENT_TIMESTAMP null comment '最后操作时间戳'
)
    comment '客户档案分级引用记录';

create index idx_ent_org_cus
    on bdc_customer_base (enterprise_no, use_org_no, customer_code);

-- 客户业务信息
CREATE TABLE `bdc_customer_biz` (
                                    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '客户业务信息主键',
                                    `enterprise_no` varchar(32) DEFAULT '' COMMENT '本企业编号（由scs平台分配）',
                                    `customer_no` varchar(32) DEFAULT NULL COMMENT '客户编号',
                                    `customer_code` varchar(32) DEFAULT NULL COMMENT '客户编码',
                                    `use_org_no` varchar(32) DEFAULT '' COMMENT '使用组织编号',
                                    `control_status` tinyint(1) DEFAULT '1' COMMENT '管控状态 1：启用 2：停用 3：冻结',
                                    `control_id` bigint(20) DEFAULT NULL COMMENT '管控类型id',
                                    `control_type_name` varchar(128) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '管控类型名称',
                                    `business_type` varchar(50) DEFAULT '' COMMENT '客户性质',
                                    `cooperation_mode` varchar(10) DEFAULT NULL COMMENT '合作性质',
                                    `price_category_code` varchar(100) DEFAULT '' COMMENT '价格体系编码',
                                    `currency` varchar(50) DEFAULT NULL COMMENT '交易币种',
                                    `settlement_modes` varchar(10) DEFAULT '' COMMENT '结算方式',
                                    `receive_agreement` varchar(500) DEFAULT NULL COMMENT '收款协议',
                                    `receive_condition` varchar(500) DEFAULT NULL COMMENT '收款条件',
                                    `credit_amount` varchar(20) DEFAULT NULL COMMENT '信用额度',
                                    `coop_start_time` varchar(19) DEFAULT NULL COMMENT '合作起始时间',
                                    `coop_end_time` varchar(19) DEFAULT NULL COMMENT '合作结束时间',
                                    `credit_dates` varchar(20) DEFAULT '' COMMENT '信用期限',
                                    `ys_sync_flag` varchar(1) DEFAULT '0' COMMENT 'ys同步标识 0:未同步 1:同步成功 2:同步失败',
                                    `tc_sync_flag` tinyint(1) DEFAULT '0' COMMENT '通昶同步状态  0.未同步  1.同步成功  2.同步失败',
                                    `tc_sync_result` text COMMENT '通昶推送结果',
                                    `deleted` tinyint(1) DEFAULT '0' COMMENT '删除标志 0：未删除 1：已删除',
                                    `gsp_audit_status` enum('9','3','2','1','0') DEFAULT '0' COMMENT 'GSP的审核状态 0:未创建，1：待审批 2：审批通过 3：审核失败;\r\n9-审批撤回',
                                    `business_flag` tinyint(1) DEFAULT '0' COMMENT '业务状态：0:草稿 1:正式',
                                    `is_flow_customer` int(11) DEFAULT '0' COMMENT '是否流向客户：1：是 0：否',
                                    `is_sync_scs` int(1) DEFAULT '0' COMMENT '同步scs状态：0：未同步 1：同步成功 2：同步失败 3：已协同',
                                    `is_sync_wms` int(1) DEFAULT '0' COMMENT '是否同步到wms 1:是 0：否',
                                    `is_sync_erp` int(1) DEFAULT '0' COMMENT '是否同步到Erp 0:未同步 1:已同步',
                                    `create_no` varchar(32) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '创建人编号',
                                    `create_name` varchar(100) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '创建人名字',
                                    `create_time` varchar(19) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '创建时间',
                                    `operate_no` varchar(32) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '修改人编号',
                                    `operate_name` varchar(100) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '修改人名字',
                                    `operate_time` varchar(19) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '修改时间',
                                    `op_timestamp` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最后操作时间戳',
                                    `op_revsion` int(11) DEFAULT '1' COMMENT '数据记录版本',
                                    `op_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT 'I' COMMENT '数据操作类型 I:新增 U：更新 D:删除',
                                    `scs_push_result` text COMMENT 'scs推送结果',
                                    `ys_push_result` text COMMENT 'ys推送结果',
                                    `wms_push_result` text COMMENT 'wms推送结果',
                                    `version` int(10) DEFAULT '1' COMMENT '版本号',
                                    `oms_customer_no` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT 'oms客户编号',
                                    PRIMARY KEY (`id`) USING BTREE,
                                    KEY `idx_enterprise_no_customer_code` (`enterprise_no`,`customer_code`) USING BTREE
) COMMENT='客户档案业务信息';

alter table bdc_customer_biz add column deleted tinyint(1) default 0 null comment '是否删除：0-否，1-是';

create index idx_ent_org_cus
    on bdc_customer_biz (enterprise_no, use_org_no, customer_code);


-- 客户联系人：和供应商共用bdc_company_linkman

-- 客户地址：和供应商共用bdc_company_shipping_address

-- 银行：和供应商共用bdc_supplier_bank

-- 开票信息
alter table bdc_customer_invoice
    add customer_code varchar(32) default '' not null comment '客户档案编码';

alter table bdc_customer_invoice
    add use_org_no varchar(32) default '' not null comment '使用组织编号';

alter table bdc_customer_invoice
    add deleted tinyint(1) default 0 null comment '删除标志 0：未删除 1：已删除';

-- 客户负责人
alter table bdc_customer_sales_man
    add enterprise_no varchar(32) default '' not null comment '租户编号';

alter table bdc_customer_sales_man
    add use_org_no varchar(32) default '' not null comment '使用组织编号';

alter table bdc_customer_sales_man
    add customer_code varchar(32) default '' not null comment '客户编码';

alter table bdc_customer_sales_man
    add dept_no varchar(32) null comment '部门编号';

alter table bdc_customer_sales_man
    add dept_name varchar(100) default '' null comment '部门名称';


create table bdc_customer_gsp_audit
(
    `id`               bigint auto_increment
        primary key,
    `enterprise_no`    varchar(32) default ''  not null comment '企业编号',
    `use_org_no`       varchar(32) default ''  not null comment '使用组织编号',
    `customer_code`    varchar(32) default ''  not null comment '客户编码',
    `customer_no`      varchar(32) default ''  not null comment '客户编号',
    `gsp_audit_status` int         default '0' not null comment '首营状态',
    `gsp_audit_result` varchar(256) comment '首营结果',
    `deleted`           tinyint(1)   DEFAULT '0' COMMENT '是否删除 1是 0否',
    `status`            tinyint(1)   DEFAULT '1' COMMENT '是否有效  0无效 1有效'
) comment '客户档案首营记录表' charset = utf8mb4;
create index idx_ent_org_code
    on bdc_customer_gsp_audit (enterprise_no, use_org_no, customer_code);

alter table bdc_customer_base
    add column control_status enum ('3', '2', '1') charset utf8 default '1' null comment '管控状态 1：启用 2：停用 3：冻结';


alter table bdc_customer_biz
drop column currency;

alter table bdc_customer_biz
    add currency_id varchar(50) null comment '交易币种' after currency;


create table bdc_customer_apply
(
    id                bigint auto_increment
        primary key,
    apply_instance_no varchar(32)                          null comment '申请单号',
    enterprise_no     varchar(32)                          null comment '租户编号',
    apply_org_no      varchar(32)                          null comment '申请组织编号',
    apply_org_name    varchar(128)                         null comment '申请组织名称',
    use_org_no        varchar(32)                          null comment '使用组织编号',
    use_org_name      varchar(32)                          null comment '使用组织名称',
    manage_org_no     varchar(32)                          null comment '管理组织编号',
    manage_org_name   varchar(32)                          null comment '管理组织名称',
    company_no        varchar(32)                          null comment '企业编号',
    company_name      varchar(128)                         null comment '企业名称',
    customer_code     varchar(32)                          null comment '客户编码',
    customer_name     varchar(128)                         null comment '客户名称',
    apply_type        tinyint(1)                           null comment '申请类型 2-新增申请 3-变更申请',
    apply_reason      varchar(512)                         null comment '申请原因',
    apply_desc        varchar(512)                         null comment '申请说明',
    audit_status      tinyint(1) default 0                 null comment '审核状态：0-草稿 1-待审核 2-审核通过 3-审核拒绝',
    apply_result      tinyint(1)                           null comment '申请结果 1-成功 2-失败',
    apply_no          varchar(32)                          null comment '申请人编号',
    apply_name        varchar(128)                         null comment '申请人名称',
    apply_time        varchar(19)                          null comment '申请时间',
    audit_no          varchar(32)                          null comment '审核人编号',
    audit_name        varchar(128)                         null comment '审核人名称',
    audit_time        varchar(19)                          null comment '审核时间',
    audit_remark      varchar(512)                         null comment '审核意见',
    fail_reason       varchar(512)                         null comment '失败原因',
    deleted           tinyint(1) default 0                 null comment '是否删除 1是 0否',
    status            tinyint(1) default 1                 null comment '是否有效  0无效 1有效',
    create_no         varchar(32)                          null comment '创建人编号',
    create_name       varchar(128)                         null comment '创建人',
    create_time       varchar(19)                          null comment '创建时间',
    modify_no         varchar(32)                          null comment '更新人编号',
    modify_name       varchar(128)                         null comment '更新人',
    modify_time       varchar(19)                          null comment '更新时间',
    op_timestamp      timestamp  default CURRENT_TIMESTAMP null comment '最后操作时间戳'
)
    comment '客户档案申请';

create index idx_ent_apply
    on bdc_customer_apply (enterprise_no, apply_org_no);

create index idx_ent_manage
    on bdc_customer_apply (enterprise_no, manage_org_no);

create index idx_ent_no
    on bdc_customer_apply (enterprise_no, apply_instance_no);

create table bdc_customer_apply_item
(
    id                  bigint auto_increment
        primary key,
    enterprise_no       varchar(32) null comment '租户编号',
    apply_instance_id   bigint      null comment '客户申请单主表id',
    apply_instance_no   varchar(32) null comment '申请单号',
    pre_approve_content mediumtext  null comment '审批通过前的详情JSON',
    approve_content     mediumtext  null comment '审批通过后的详情JSON',
    apply_content       mediumtext  null comment '申请时填写的详情JSON'
)
    comment '客户档案申请详情';

create index idx_applyid_ent
    on bdc_customer_apply_item (apply_instance_id, enterprise_no);

create index idx_applyno_ent
    on bdc_customer_apply_item (apply_instance_no, enterprise_no);


create table dsrp.customer_channel_sync_mapping
(
    id            int auto_increment comment '编号'
        primary key,
    enterprise_no varchar(32)  null comment '租户编号;租户',
    org_no        varchar(32)  null comment '业务单元编号;内部单据创建组织',
    channel_code  varchar(32)  null comment '通道系统编号;三方系统',
    view_no       varchar(32)  null comment '单据视图;单据类型',
    inner_bill_no varchar(64)  null comment '内部单据号;内部系统档案或单据号',
    out_bill_no   varchar(256) null comment '外部单据号;外部系统档案或单据号',
    sync_status   int          null comment '同步状态;0未同步 1成功 2失败',
    sync_result   varchar(500) null comment '同步结果/异常原因',
    fail_times    int          null comment '同步失败次数',
    remark        varchar(64)  null comment '备注说明',
    ext1          varchar(32)  null comment '扩展属性1',
    ext2          varchar(64)  null comment '扩展属性2',
    ext3          varchar(128) null comment '扩展属性3',
    ext4          varchar(256) null comment '扩展属性4',
    ext5          int          null comment '扩展属性5',
    ext6          int          null comment '扩展属性6',
    create_time   varchar(19)  null comment '创建时间;对照创建日期',
    operate_time  varchar(19)  null comment '操作时间;对照修改日期'
)
    comment '客户三方通道同步对照表';

create index idx_ent_bill
    on dsrp.customer_channel_sync_mapping (enterprise_no, inner_bill_no);