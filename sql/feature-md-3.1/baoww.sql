-- bdc_company修改字段
ALTER TABLE yyigou_dsrp.bdc_company MODIFY COLUMN `factory_type` tinyint(1) DEFAULT '1' COMMENT '企业注册地域：1-境内，2-境外';
ALTER TABLE yyigou_dsrp.bdc_company ADD COLUMN `country` VARCHAR(50) DEFAULT NULL COMMENT '国家/地区';
ALTER TABLE yyigou_dsrp.bdc_company MODIFY COLUMN `tax_category` varchar(10) DEFAULT NULL COMMENT '纳税类别编码';
ALTER TABLE yyigou_dsrp.bdc_company MODIFY COLUMN `tax_category_name` varchar(100) DEFAULT NULL COMMENT '纳税类别名称';
ALTER TABLE yyigou_dsrp.bdc_company ADD COLUMN `economic_type` VARCHAR(30) DEFAULT NULL COMMENT '经济类型编码';
ALTER TABLE yyigou_dsrp.bdc_company ADD COLUMN `economic_type_name` varchar(100) DEFAULT NULL COMMENT '经济类型名称';
ALTER TABLE yyigou_dsrp.bdc_company
    ADD COLUMN `is_medical_institution` tinyint(1) DEFAULT '0' COMMENT '是否医疗机构：0-否，1-是';
ALTER TABLE yyigou_dsrp.bdc_company
    ADD COLUMN `associated_org_no` varchar(50) DEFAULT NULL COMMENT '关联组织编号,关联企业时选择';
ALTER TABLE yyigou_dsrp.bdc_company
    ADD COLUMN `associated_org_code` varchar(50) DEFAULT NULL COMMENT '关联组织编码,关联企业时选择';
ALTER TABLE yyigou_dsrp.bdc_company
    ADD COLUMN `associated_org_name` varchar(100) DEFAULT NULL COMMENT '关联组织名称,关联企业时选择';
ALTER TABLE yyigou_dsrp.bdc_company
    MODIFY COLUMN `op_timestamp` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后操作时间';
ALTER TABLE yyigou_dsrp.bdc_company MODIFY COLUMN `status` tinyint(2) DEFAULT '1' COMMENT '0-无效，1-有效';

-- 企业档案导出模板
INSERT INTO `yyigou_ddc_task`.`t_task_export_template` (`template_id`, `template_name`, `template_status`,
                                                        `export_limit`, `burst_size`, `data_handle_api_ref`,
                                                        `notify_mq_queue`, `export_file_meta`,
                                                        `export_file_name_suffix`, `export_file_suffix`,
                                                        `template_type`, `is_deleted`)
VALUES ('dsrp_cdc_company_export', '企业档案导出', 1, 60000, 1000, 'dsrp.cdc.company.list.page', NULL,
        '[{\"pojo\":\"_static\",\"excel\":\"静态字段\"}]', 'yyyy-MM-dd_HHmmss', 'xlsx', 0, 0);