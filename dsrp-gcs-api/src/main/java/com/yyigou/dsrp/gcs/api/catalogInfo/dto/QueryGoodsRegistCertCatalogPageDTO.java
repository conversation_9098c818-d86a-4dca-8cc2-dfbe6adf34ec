package com.yyigou.dsrp.gcs.api.catalogInfo.dto;

import com.yyigou.ddc.common.service.annotation.EntityField;
import com.yyigou.dsrp.gcs.model.common.dto.ExcelDynamicExportDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class QueryGoodsRegistCertCatalogPageDTO implements Serializable {

    @EntityField(name = "导出类型 1-导出数据+源文件  2-导出数据+加注件")
    private Integer exportType;

    @EntityField(name = "动态列导出列名集合")
    private List<ExcelDynamicExportDTO> columnList;

    @EntityField(name = "id精确查询")
    private List<Long> idList;

    /**
     * 查询使用
     */
    @EntityField(name = "审核状态，查询使用")
    private List<Integer> auditStatusList;


    @EntityField(name = "注册证no")
    private List<String> catalogNoList;
    @EntityField(name = "状态")
    private List<String> statusList;
    /**
     * 注册证代码
     */
    @EntityField(name = "注册证代码")
    private String certCode;
    @EntityField(name = "注册证模糊查询")
    private String certCodeKeyWord;

    @EntityField(name = "注册证模糊查询")
    private String keywords;


    @EntityField(name = "注册证批量查询")
    private List<String> certCodeList;

    @EntityField(name = "注册证名称/编码")
    private String keyWord;

    /**
     * 证件名称
     */
    @EntityField(name = "注册证名称")
    private String certName;
    @EntityField(name = "注册证名称模糊查询")
    private String certNameKeyWord;
    @EntityField(name = "注册证名称模糊查询")
    private List<String> certNameList;
    /**
     * 生效时间
     */
    @EntityField(name = "生效时间-开始")
    private String startTimeBegin;
    @EntityField(name = "生效时间-结束")
    private String startTimeEnd;

    /**
     * 失效时间
     */
    @EntityField(name = "失效时间-开始")
    private String endTimeBegin;
    @EntityField(name = "失效时间-结束")
    private String endTimeEnd;

    /**
     * 批准时间
     */
    @EntityField(name = "批准时间-开始")
    private String approvalDateBegin;
    @EntityField(name = "批准时间-结束")
    private String approvalDateEnd;

    @EntityField(name = "注册证类型")
    private List<Integer> goodsRegistTypeList;
    @EntityField(name = "注册人名称")
    private List<String> registerFactoryNoList;
    @EntityField(name = "注册人名称模糊查询")
    private String registerFactoryKeyWord;

    @EntityField(name = "注册人名称")
    private List<String> productFactoryNoList;
    @EntityField(name = "注册人名称模糊查询")
    private String productFactoryKeyWord;

    @EntityField(name = "医疗器械分类")
    private List<String> categoryCodeList;
    @EntityField(name = "上传状态")
    private Integer uploadStatus;

    private List<Integer> expiredStatusList;

    @EntityField(name = "钉钉模糊搜索关键字", stringValueTypeLength = 50)
    private String dingQueryKeywords;
}
