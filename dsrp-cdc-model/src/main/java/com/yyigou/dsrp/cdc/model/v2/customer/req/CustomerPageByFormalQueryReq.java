package com.yyigou.dsrp.cdc.model.v2.customer.req;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class CustomerPageByFormalQueryReq implements Serializable {
    @EntityField(name = "")
    private String enterpriseNo;

    @EntityField(name = "")
    private String useOrgNo;

    @EntityField(name = "")
    private String keywords;

    @EntityField(name = "客户档案关键字")
    private String customerKeywords;

    @EntityField(name = "")
    private String customerExactKeywords;

    @EntityField(name = "客户档案关键字")
    private String customerCodeKeywords;

    @EntityField(name = "")
    private String customerNameKeywords;

    @EntityField(name = "")
    private String customerCategoryNo;

    @EntityField(name = "")
    private String enterpriseType;

    @EntityField(name = "")
    private String controlStatus;

    @EntityField(name = "")
    private List<Integer> controlStatusList;

    @EntityField(name = "")
    private List<String> noCustomerNos;

    @EntityField(name = "")
    private List<Integer> businessFlags;
}
