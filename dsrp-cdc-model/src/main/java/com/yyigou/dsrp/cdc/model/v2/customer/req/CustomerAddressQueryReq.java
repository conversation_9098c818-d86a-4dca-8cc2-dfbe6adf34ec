package com.yyigou.dsrp.cdc.model.v2.customer.req;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class CustomerAddressQueryReq implements Serializable {
    private static final long serialVersionUID = 1999169811512860482L;

    @EntityField(name = "租户编号")
    private String enterpriseNo;

    @EntityField(name = "客户编号")
    private String customerCode;

    @EntityField(name = "使用组织编号")
    private String useOrgNo;

    @EntityField(name = "地址类型：1联系地址 2：收票地址 3:仓库地址 4：收货地址")
    private List<String> addressTypeList;

}
