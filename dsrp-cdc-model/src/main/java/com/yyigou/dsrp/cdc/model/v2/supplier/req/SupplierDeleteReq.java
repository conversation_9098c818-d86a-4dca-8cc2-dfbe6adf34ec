package com.yyigou.dsrp.cdc.model.v2.supplier.req;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;

@Data
public class SupplierDeleteReq implements Serializable {
    @EntityField(name = "租户编号")
    private String enterpriseNo;

    @EntityField(name = "管理组织编号")
    private String manageOrgNo;

    /**
     * 供应商编码
     */
    @EntityField(name = "供应商编码")
    private String supplierCode;
}
