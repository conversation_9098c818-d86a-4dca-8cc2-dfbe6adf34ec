package com.yyigou.dsrp.cdc.model.v2.supplier.req;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class SupplierCodeOrgPairListReq implements Serializable {
    private static final long serialVersionUID = 5009195985397117121L;

    @EntityField(name = "租户编号")
    private String enterpriseNo;

    @EntityField(name = "供应商编码")
    private List<SupplierCodeOrgPairReq> supplierCodeOrgPairList;

}
