package com.yyigou.dsrp.cdc.model.supplier.req;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 多组织供应商查询DTO
 *
 * @author: Moore
 * @date: 2024/11/12 18:26
 * @version: 1.0.0
 */
@Data
public class SupplierMultiOrgQueryReq implements Serializable {
    private static final long serialVersionUID = -6907998780361618712L;

    @EntityField(name = "供应商noList")
    private List<String> supplierNoList;

    @EntityField(name = "组织noList")
    private List<String> orgNoList;

    @EntityField(name = "前端组件临时模糊查询")
    private String keywords;

    @EntityField(name = "分派组织租户编号")
    private List<String> assignEnterprsieNoList;

    @EntityField(name = "租户编号")
    private String enterpriseNo;

    @EntityField(name = "管理组织租户编号")
    private String managerEnterpriseNo;
}
