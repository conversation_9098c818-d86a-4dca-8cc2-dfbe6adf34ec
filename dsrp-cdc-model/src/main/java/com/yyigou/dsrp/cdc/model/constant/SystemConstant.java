package com.yyigou.dsrp.cdc.model.constant;

/**
 * cdc服务通用常量类
 *
 * @author: <PERSON>
 * @date: 2024/7/17 15:38
 * @version: 1.0.0
 */
public class SystemConstant {

    /**
     * 通用的分类顶级编号
     */
    public static final String COMMON_TOP_CATEGORY_NO = ".0.";

    /**
     * 供应商合作性质字典项num
     */
    public static final String SUPPLIER_COOPERATION_NUMBER = "dsrp_supplier_cooperation_mode";
    /**
     * 客户合作性质字典项num
     */
    public static final String CUSTOMER_COOPERATION_NUMBER = "dsrp_customer_cooperation_mode";


    /**
     * 付款条件
     */
    public static final String DSRP_CUSTOMER_PAYMENT_TERM = "dsrp_customer_payment_term";


    /**
     * 自定义档案-税收分类
     */
    public static final String TAX_CATEGORY = "dsrp_tax_category";


    /**
     * 自定义档案-客户经济类型
     */
    public static final String ECONOMIC_TYPE = "economicType";


    /**
     * 自定义档案 客户档案-客户性质
     */
    public static final String DSRP_BUSINESS_TYPE = "dsrp_business_type";


    public static final String KH_CODE_GENERATE = "KH";

    public static final String GYS_CODE_GENERATE = "GYS";


    public static final String DATA_BASE = "yyigou_dsrp";


    public static final String DEFAULT_UNIFIED_SOCIAL_CODE = "/";


    /*
    客商档案申请原因
     */
    public static final String DSRP_APPLY_REASON = "baseReason";

}
