package com.yyigou.dsrp.cdc.model.v2.supplier.req;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 分页查询供应商档案参数
 *
 */
@Data
public class SupplierManageQueryListPageReq implements Serializable {
    private static final long serialVersionUID = -2322834031477677778L;

    @EntityField(name = "租户编号")
    private String enterpriseNo;

    @EntityField(name = "控制状态")
    private List<String> controlStatusList;

    @EntityField(name = "供应商名称模糊搜索")
    private String supplierNameKeywords;

    @EntityField(name = "供应商编码模糊搜索")
    private String supplierCodeKeywords;

    @EntityField(name = "供应商分类")
    private List<String> supplierCategoryNoList;

    @EntityField(name = "统一社会信用代码模糊搜索")
    private String unifiedSocialCodeKeywords;

    @EntityField(name = "管理组织编号")
    private List<String> manageOrgNoList;

    @EntityField(name = "使用组织编号")
    private List<String> useOrgNoList;

    @EntityField(name = "隐含条件")
    private Integer businessFlag;

    @EntityField(name = "勾选导出选的no")
    private List<String> idList;
}
