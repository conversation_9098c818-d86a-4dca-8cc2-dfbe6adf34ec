package com.yyigou.dsrp.cdc.model.v2.company.req;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class CompanyCertQueryReq implements Serializable {
    private static final long serialVersionUID = 1117212035056347555L;

    @EntityField(name = "租户编号")
    private String enterpriseNo;

    @EntityField(name = "企业编号列表")
    private List<String> companyNoList;
}
