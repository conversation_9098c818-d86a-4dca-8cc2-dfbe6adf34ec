package com.yyigou.dsrp.cdc.model.v2.supplier.req;

import com.yyigou.ddc.common.service.annotation.EntityField;
import com.yyigou.dsrp.cdc.model.v2.company.req.CompanyBankReq;
import com.yyigou.dsrp.cdc.model.v2.company.req.CompanyCertReq;
import com.yyigou.dsrp.cdc.model.v2.company.req.CompanyLinkmanReq;
import com.yyigou.dsrp.cdc.model.v2.company.req.CompanyShippingAddressReq;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class SupplierApplyFormReq implements Serializable {
    private static final long serialVersionUID = 1L;
    // ----------------组织信息----------------
    @EntityField(name = "管理组织编号")
    private String manageOrgNo;

    @EntityField(name = "管理组织名称")
    private String manageOrgName;

    @EntityField(name = "使用组织编号")
    private String useOrgNo;

    @EntityField(name = "使用组织名称")
    private String useOrgName;


    // ----------------供应商基本信息----------------
    @EntityField(name = "供应商编号")
    private String supplierNo;

    @EntityField(name = "供应商编码")
    private String supplierCode;

    @EntityField(name = "供应商名称")
    private String supplierName;

    @EntityField(name = "供应商英文名")
    private String supplierNameEn;

    @EntityField(name = "助记码")
    private String mnemonicCode;

    @EntityField(name = "供应商类型")
    private String transactionType;

    @EntityField(name = "供应商类型名称")
    private String transactionTypeName;

    @EntityField(name = "供应商分类编号")
    private String supplierCategoryNo;

    @EntityField(name = "供应商分类名称")
    private String supplierCategoryName;

    @EntityField(name = "是否散户")
    private Integer retailInvestors;

    @EntityField(name = "是否散户名称")
    private String retailInvestorsName;

    @EntityField(name = "是否内部组织")
    private Integer isAssociatedEnterprise;

    @EntityField(name = "是否内部组织")
    private String isAssociatedEnterpriseName;

    @EntityField(name = "内部组织编号")
    private String associatedOrgNo;

    @EntityField(name = "内部组织编码")
    private String associatedOrgCode;

    @EntityField(name = "内部组织名称")
    private String associatedOrgName;

    @EntityField(name = "备注")
    private String remark;


    // ----------------企业信息----------------
    @EntityField(name = "企业编号")
    private String companyNo;

    @EntityField(name = "企业名称")
    private String companyName;

    @EntityField(name = "企业统一社会信用代码")
    private String unifiedSocialCode;

    @EntityField(name = "企业区域")
    private Integer factoryType;

    @EntityField(name = "企业区域名称")
    private String factoryTypeName;

    @EntityField(name = "国家地区")
    private String countryRegionId;

    @EntityField(name = "国家地区")
    private String countryRegionName;

    @EntityField(name = "纳税类别")
    private String taxCategory;

    @EntityField(name = "纳税类别名称")
    private String taxCategoryName;

    @EntityField(name = "经济类型")
    private String economicType;

    @EntityField(name = "经济类型名称")
    private String economicTypeName;

//    @EntityField(name = "是否医疗机构")
//    private Integer isMedicalInstitution;
//
//    @EntityField(name = "是否医疗机构名称")
//    private String isMedicalInstitutionName;
//
//    @EntityField(name = "医疗机构类型")
//    private String institutionalType;
//
//    @EntityField(name = "医疗机构类型名称")
//    private String institutionalTypeName;
//
//    @EntityField(name = "医院性质")
//    private String hospitalType;
//
//    @EntityField(name = "医院性质名称")
//    private String hospitalTypeName;
//
//    @EntityField(name = "医院等级")
//    private Integer hospitalClass;
//
//    @EntityField(name = "医院等级名称")
//    private String hospitalClassName;

    @EntityField(name = "银行信息")
    private List<CompanyBankReq> bankList;

    @EntityField(name = "企业证书信息")
    private List<CompanyCertReq> companyCertList;


    // ----------------业务信息----------------
    @EntityField(name = "合作性质")
    private String cooperationMode;

    @EntityField(name = "合作性质名称")
    private String cooperationModeName;

    @EntityField(name = "业务归属")
    private String ownerCompany;

    @EntityField(name = "信用额度")
    private String creditAmount;

    @EntityField(name = "结算方式")
    private String settlementModes;

    @EntityField(name = "结算方式名称")
    private String settlementModesName;

    @EntityField(name = "付款条件")
    private String paymentTerm;

    @EntityField(name = "付款条件名称")
    private String paymentTermName;

    @EntityField(name = "合作起始时间")
    private String coopStartTime;

    @EntityField(name = "合作结束时间")
    private String coopEndTime;

    @EntityField(name = "付款协议id")
    private Long paymentAgreementId;

    @EntityField(name = "付款协议id(ys的id)")
    private String paymentAgreementYsId;

    @EntityField(name = "付款协议编码")
    private String paymentAgreementCode;

    @EntityField(name = "付款协议名称")
    private String paymentAgreementName;

    @EntityField(name = "账期天数")
    private Integer periodDays;

    @EntityField(name = "交易币种")
    private String currencyId;

    @EntityField(name = "交易币种名称")
    private String currencyName;


    // ----------------tab信息----------------
    @EntityField(name = "供应商证书信息")
    private List<CompanyCertReq> supplierCertList;
    
    @EntityField(name = "联系人信息")
    private List<CompanyLinkmanReq> linkmanList;

    @EntityField(name = "联系地址信息")
    private List<CompanyShippingAddressReq> linkAddressList;

    @EntityField(name = "负责人信息")
    private List<SupplierOrderManReq> supplierManList;
}
