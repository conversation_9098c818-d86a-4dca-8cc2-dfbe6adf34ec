package com.yyigou.dsrp.cdc.model.company.req;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;

@Data
public class CompanyBasicReq implements Serializable {
    private static final long serialVersionUID = 76475985963785482L;

    @EntityField(name = "租户编号")
    private String enterpriseNo;

    private Long id;

    @EntityField(name = "企业编号")
    private String companyNo;

    @EntityField(name = "企业名称")
    private String companyName;

    @EntityField(name = "企业编码")
    private String companyCode;
}
