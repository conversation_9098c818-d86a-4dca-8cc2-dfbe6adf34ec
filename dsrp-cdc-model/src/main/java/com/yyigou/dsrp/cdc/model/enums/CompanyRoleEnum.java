package com.yyigou.dsrp.cdc.model.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * 企业角色：1-供应商，2-客户
 *
 * @author:  Moore
 * @date: 2023/5/27 15:30
 * @version: 1.0.0
 */
public enum CompanyRoleEnum {
    SUPPLIER(1, "供应商"),
    CUSTOMER(2, "客户");


    private final Integer value;
    private final String name;

    public Integer getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    CompanyRoleEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    private static Map<Integer, CompanyRoleEnum> map = new HashMap<>();

    static {
        for (CompanyRoleEnum item : CompanyRoleEnum.values()) {
            map.put(item.getValue(), item);
        }

    }

    public static CompanyRoleEnum getByValue(final Integer value) {
        if (value == null) {
            return null;
        }
        return map.get(value);
    }
}
