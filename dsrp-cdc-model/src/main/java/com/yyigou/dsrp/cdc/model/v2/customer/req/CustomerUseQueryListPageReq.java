package com.yyigou.dsrp.cdc.model.v2.customer.req;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 分页查询客户档案参数
 *
 */
@Data
public class CustomerUseQueryListPageReq implements Serializable {
    private static final long serialVersionUID = -2322834031477677778L;

    @EntityField(name = "租户编号")
    private String enterpriseNo;

    @EntityField(name = "使用组织编号")
    private List<String> useOrgNoList;

    @EntityField(name = "控制状态")
    private List<String> controlStatusList;

    @EntityField(name = "客户名称模糊搜索")
    private String customerNameKeywords;

    @EntityField(name = "客户编码模糊搜索")
    private String customerCodeKeywords;

    @EntityField(name = "客户分类")
    private List<String> customerCategoryNoList;

    @EntityField(name = "统一社会信用代码模糊搜索")
    private String unifiedSocialCodeKeywords;

    @EntityField(name = "管理组织编号")
    private List<String> manageOrgNoList;

//    @EntityField(name = "首营状态")
//    private List<Integer> xxxStatus;

    @EntityField(name = "隐含条件")
    private Integer businessFlag;

    @EntityField(name = "wms同步状态")
    private List<Integer> wmsSyncStatus;

    @EntityField(name = "erp同步状态")
    private List<Integer> erpSyncStatus;

    @EntityField(name = "scs同步状态")
    private List<Integer> scsSyncStatus;

    @EntityField(name = "勾选导出选的no")
    private List<String> idList;
}
