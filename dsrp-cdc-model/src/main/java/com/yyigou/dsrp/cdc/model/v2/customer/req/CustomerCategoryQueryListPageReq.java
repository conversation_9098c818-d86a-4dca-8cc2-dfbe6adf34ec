package com.yyigou.dsrp.cdc.model.v2.customer.req;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;


@Data
public class CustomerCategoryQueryListPageReq implements Serializable {
    @EntityField(name = "租户编号")
    private String enterpriseNo;

    @EntityField(name = "使用组织编号")
    private String useOrgNo;

    @EntityField(name = "使用组织名称")
    private String useOrgName;

    @EntityField(name = "上级分类编号")
    private String parentNo;

    @EntityField(name = "客户分类编码名称模糊搜索")
    private String keywords;

    @EntityField(name = "状态")
    private String status;

    @EntityField(name = "列表展示方式")
    private Integer showType; //0 展示全部，1 仅展示下级

    @EntityField(name = "勾选导出选的no")
    private List<String> idList;
}