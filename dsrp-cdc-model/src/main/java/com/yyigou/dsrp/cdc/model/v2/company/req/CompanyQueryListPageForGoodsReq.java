package com.yyigou.dsrp.cdc.model.v2.company.req;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;

@Data
public class CompanyQueryListPageForGoodsReq implements Serializable {
    private static final long serialVersionUID = -2322834031477677778L;

    @EntityField(name = "租户编号")
    private String enterpriseNo;

    @EntityField(name = "名称/编码/信用代码模糊搜索")
    private String unionKeyWord;
}
