package com.yyigou.dsrp.cdc.model.v2.customer.req;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class CustomerPageQueryReq implements Serializable {
    private String enterpriseNo;
    private String useOrgNo;


    private String keywords;

    @EntityField(name = "客户档案关键字")
    private String customerKeywords;

    @EntityField(name = "")
    private String customerExactKeywords;

    @EntityField(name = "")
    private String customerCategoryNo;

    @EntityField(name = "")
    private String customerCode;

    @EntityField(name = "")
    private Integer controlStatus;

    @EntityField(name = "")
    private List<Integer> businessFlags;

    @EntityField(name = "")
    private List<String> noCustomerNos;

//    @EntityField(name = "")
//    private String orgNo;

    @EntityField(name = "")
    private String enterpriseType;

}
