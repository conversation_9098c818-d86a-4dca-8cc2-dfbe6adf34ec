package com.yyigou.dsrp.cdc.model.v2.supplier.req;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;


@Data
public class SupplierCategoryQuerySolutionTreeReq implements Serializable {
    @EntityField(name = "租户编号")
    private String enterpriseNo;

//    @EntityField(name = "使用组织编号")
//    private String useOrgNo;

    @EntityField(name = "查询条件")
    private List<QueryCondition> queryConditionList;

    @Data
    public static class QueryCondition implements Serializable {
        /**
         * 字段
         */
        private String column;

        /**
         * 值
         */
        private String value;

        /**
         * 操作符, = , !=, 暂时不支持in, not in
         *
         * @see com.yyigou.ddc.meta.base.common.MetaCondOperatorEnum
         */
        private String operator;
    }
}