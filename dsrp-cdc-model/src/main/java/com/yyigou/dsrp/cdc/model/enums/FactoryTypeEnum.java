package com.yyigou.dsrp.cdc.model.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * 企业注册地域
 *
 * @author: Moore
 * @date: 2024/8/6 14:50
 * @version: 1.0.0
 */
public enum FactoryTypeEnum {
    DOMESTIC(1, "境内"),
    ABROAD(2, "境外");

    private final Integer value;
    private final String name;

    FactoryTypeEnum(Integer source, String name) {
        this.value = source;
        this.name = name;
    }

    public Integer getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    private static Map<Integer, FactoryTypeEnum> maps = new HashMap<>();

    static {
        for (FactoryTypeEnum item : FactoryTypeEnum.values()) {
            maps.put(item.getValue(), item);
        }
    }

    public static FactoryTypeEnum getByType(final Integer value) {
        if (value == null) {
            return null;
        }
        return maps.get(value);
    }
}
