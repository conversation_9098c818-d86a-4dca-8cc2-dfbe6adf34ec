package com.yyigou.dsrp.cdc.model.v2.supplier.req;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;


@Data
public class SupplierCategoryQueryFlagListPageReq implements Serializable {
    @EntityField(name = "租户编号")
    private String enterpriseNo;

    @EntityField(name = "使用组织编号")
    private String useOrgNo;


    @EntityField(name = "客户分类编码名称模糊搜索")
    private String keywords;

    @EntityField(name = "状态")
    private String status;

    @EntityField(name = "分类编号列表")
    private List<String> supplierCategoryNoList;
}