package com.yyigou.dsrp.cdc.model.v2.customer.req;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 客户申请分页查询参数
 */
@Data
public class CustomerApplyApproveQueryListPageReq implements Serializable {
    private static final long serialVersionUID = 1999169811512860482L;

    @EntityField(name = "租户编号")
    private String enterpriseNo;

    @EntityField(name = "勾选导出选的id")
    private List<String> idList;
}
