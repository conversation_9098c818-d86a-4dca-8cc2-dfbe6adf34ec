package com.yyigou.dsrp.cdc.model.constant;

/**
 * 编码中心请求常量类
 *
 * @author: <PERSON>
 * @date: 2024/7/8 10:51
 * @version: 1.0.0
 */
public class NumberCenterConstant {

    /**
     * 企业编号编码KEY
     */
    public static final String COMPANY_NO_GENERATE_KEY = "dsrp.bdc.company.no";

    /**
     * 供应商档案
     */
    public static final String SUPPLY_NO_KEY = "dsrp.bdc.supplier.no";


    /**
     * 供应商分类
     */
    public static final String SUPPLY_NO_CATEGORY_KEY = "dsrp.bdc.supplier.category.no";

    /**
     * 新的供应商分类编码规则，全局唯一生成
     */
    public static final String SUPPLY_NO_CATEGORY_KEY_V2 = "dsrp.cdc.supplier.category.no";


    public static final String CUSTOMER_NO_KEY = "dsrp.bdc.customer.no";

    public static final String CUSTOMER_NO_CATEGORY_KEY = "dsrp.bdc.customer.category.no";

    /**
     * 新的客户分类编码规则，全局唯一生成
     */
    public static final String CUSTOMER_NO_CATEGORY_KEY_V2 = "dsrp.cdc.customer.category.no";
}
