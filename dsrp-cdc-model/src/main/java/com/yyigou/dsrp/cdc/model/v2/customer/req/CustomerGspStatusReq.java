package com.yyigou.dsrp.cdc.model.v2.customer.req;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;

@Data
public class CustomerGspStatusReq implements Serializable {
    private static final long serialVersionUID = 7651163492063573672L;

    @EntityField(name = "企业编号")
    private String enterpriseNo;

    @EntityField(name = "使用组织编号")
    private String useOrgNo;

    @EntityField(name = "客户编号")
    private String customerNo;

    @EntityField(name = "客户编码")
    private String customerCode;
}
