package com.yyigou.dsrp.cdc.model.v2.customersync.req;

import lombok.Data;

import java.io.Serializable;

@Data
public class CustomerChannelSyncReq implements Serializable {
    /**
     * 编号
     */
    private Long id;

    /**
     * 租户编号;租户
     */
    private String enterpriseNo;

    /**
     * 业务单元编号;内部单据创建组织
     */
    private String orgNo;

    /**
     * 通道系统编号;三方系统
     */
    private String channelCode;

    /**
     * 单据视图;单据类型
     */
    private String viewNo;

    /**
     * 内部单据号;内部系统档案或单据号
     */
    private String innerBillNo;

    /**
     * 外部单据号;外部系统档案或单据号
     */
    private String outBillNo;

    /**
     * 同步状态;0未同步 1成功 2失败
     */
    private Integer syncStatus;

    /**
     * 同步结果/异常原因
     */
    private String syncResult;

    /**
     * 同步失败次数
     */
    private Integer failTimes;

    /**
     * 备注说明
     */
    private String remark;

    /**
     * 扩展属性1
     */
    private String ext1;

    /**
     * 扩展属性2
     */
    private String ext2;

    /**
     * 扩展属性3
     */
    private String ext3;

    /**
     * 扩展属性4
     */
    private String ext4;

    /**
     * 扩展属性5
     */
    private Integer ext5;

    /**
     * 扩展属性6
     */
    private Integer ext6;

    /**
     * 创建时间;对照创建日期
     */
    private String createTime;

    /**
     * 操作时间;对照修改日期
     */
    private String operateTime;
}
