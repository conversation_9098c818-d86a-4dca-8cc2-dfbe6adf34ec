package com.yyigou.dsrp.cdc.model.v2.supplier.req;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;

/**
 * 供应商档案分页查询参数
 */
@Data
public class SupplierReverseUseQueryListPageNoAuthZReq implements Serializable {
    private static final long serialVersionUID = 1999169811512860482L;

    @EntityField(name = "租户编号")
    private String enterpriseNo;

    @EntityField(name = "使用组织编号")
    private String useOrgNo;

    @EntityField(name = "供应商名称模糊搜索")
    private String supplierNameKeywords;
}
