package com.yyigou.dsrp.cdc.model.v2.supplier.req;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class SupplierPageByFormalQueryReq implements Serializable {
    private String enterpriseNo;
    private String useOrgNo;

    @EntityField(name = "")
    private List<String> noSupplierNos;

    @EntityField(name = "供应商档案关键字")
    private String supplierKeywords;

    @EntityField(name = "")
    private String supplierExactKeywords;

    @EntityField(name = "合作性质列表")
    private List<String> cooperationModeList;

    @EntityField(name = "供应商分类关键字")
    private String supplierCategoryKeywords;

    @EntityField(name = "供应商分类编号列表")
    private List<String> supplierCategoryNoList;

    @EntityField(name = "统一社会信用代码关键字")
    private String unifiedSocialCodeKeywords;
}
