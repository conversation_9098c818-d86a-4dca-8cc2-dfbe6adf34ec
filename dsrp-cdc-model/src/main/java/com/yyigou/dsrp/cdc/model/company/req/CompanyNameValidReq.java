package com.yyigou.dsrp.cdc.model.company.req;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;

/**
 * 企业名称唯一性校验参数
 *
 * @author:  <PERSON>
 * @date: 2024/7/6 16:12
 * @version: 1.0.0
 */
@Data
public class CompanyNameValidReq implements Serializable {
    private static final long serialVersionUID = 6432399966744130302L;
    @EntityField(name = "租户编号")
    private String enterpriseNo;
    @EntityField(name = "企业编号")
    private String companyNo;
    @EntityField(name = "企业名称")
    private String companyName;
}
