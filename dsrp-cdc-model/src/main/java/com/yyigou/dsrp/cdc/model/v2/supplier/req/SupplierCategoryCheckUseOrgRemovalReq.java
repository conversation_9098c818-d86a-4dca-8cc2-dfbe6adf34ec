package com.yyigou.dsrp.cdc.model.v2.supplier.req;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;

@Data
public class SupplierCategoryCheckUseOrgRemovalReq implements Serializable {
    @EntityField(name = "租户编号")
    private String enterpriseNo;

    @EntityField(name = "使用组织")
    private String useOrgNo;

    @EntityField(name = "分类编号")
    private String no;
}