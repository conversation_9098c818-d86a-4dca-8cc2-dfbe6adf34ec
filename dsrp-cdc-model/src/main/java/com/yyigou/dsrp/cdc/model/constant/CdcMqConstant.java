package com.yyigou.dsrp.cdc.model.constant;

/**
 * mq 队列的常量
 *
 * @author: fudj
 * @createTime: 2023-05-11  10:40
 * @version: 1.0
 */
public class CdcMqConstant {

    /**
     * 接收企业证照变更消息消息发送方在BDC
     */
    public static final String BDC_COMPANY_CERT_UPDATE_TOPIC = "BDC.COMPANY_CERT.UPDATE";

    /**
     * 修改企业档案消息
     */
    public static final String CDC_COMPANY_UPDATE_TOPIC = "DSRP.CDC.COMPANY.UPDATE";

    /**
     * 修改企业档案消息，处理客商档案的名称
     */
    public static final String CDC_COMPANY_UPDATE_TOPIC_V2 = "DSRP.CDC.COMPANY.UPDATE.V2";

    /**
     * 客户档案分派监听
     */
    public static final String CDC_CUSTOMER_ASSIGN_TOPIC_V2 = "DSRP.CDC.CUSTOMER.ASSIGN.MQ_NOTIFY";

    /**
     * 客户档案分派监听回执
     */
    public static final String CDC_CUSTOMER_ASSIGN_RESULT_TOPIC_V2 = "DSRP.CDC.CUSTOMER.ASSIGN.RESULT.MQ_NOTIFY";


    public static final String CDC_CUSTOMER_ASSIGN_TOPIC = "DSRP.CDC.CUSTOMER_ASSIGN";

    public static final String CDC_SUPPLIER_ASSIGN_TOPIC = "DSRP.CDC.SUPPLIER_ASSIGN";



    public static final String DDC_SUPPLIER_RELATION_CREATE_QUEUE = "ddc.supplier.relationCreate.queue";


    /**
     * 纳税主体新增/变更消息
     */
    public static final String CREATE_TAXPAYER_ORG_QUEUE = "CREATE_TAXPAYER_ORG_QUEUE";



}
