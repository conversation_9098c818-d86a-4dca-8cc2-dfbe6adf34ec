package com.yyigou.dsrp.cdc.model.v2.customer.req;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;


@Data
public class CustomerCategoryDeleteReq implements Serializable {
    @EntityField(name = "分类编码")
    private String no;

    @EntityField(name = "所属企业编号")
    private String enterpriseNo;

    @EntityField(name = "管理组织编号")
    private String manageOrgNo;
}