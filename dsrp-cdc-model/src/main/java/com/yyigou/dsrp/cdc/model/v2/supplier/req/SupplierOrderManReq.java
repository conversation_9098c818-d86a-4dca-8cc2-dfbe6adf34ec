package com.yyigou.dsrp.cdc.model.v2.supplier.req;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

@Data
public class SupplierOrderManReq {
    @EntityField(name = "ID")
    private Long id;

    private String manCode;

    @EntityField(name = "部门编号")
    private String deptNo;

    @EntityField(name = "部门名称")
    private String deptName;

    @EntityField(name = "业务员编号")
    private String orderManNo;

    @EntityField(name = "")
    private String orderManName;

    @EntityField(name = "职位")
    private String post;

    @EntityField(name = "默认订单专员  2:是 1：默认  0：否 ")
    private Integer orderSpecialist;

    @EntityField(name = "默认业务员  2:是 1：默认  0：否 ")
    private Integer isDefault;


    // -----------名字信息------------
    @EntityField(name = "手机号")
    private String mobile;

    @EntityField(name = "默认订单专员  2:是 1：默认  0：否 ")
    private String orderSpecialistName;

    @EntityField(name = "默认业务员  2:是 1：默认  0：否 ")
    private String isDefaultName;

}
