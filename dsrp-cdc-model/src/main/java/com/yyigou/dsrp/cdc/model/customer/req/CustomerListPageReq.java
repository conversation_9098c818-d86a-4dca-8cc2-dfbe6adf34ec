package com.yyigou.dsrp.cdc.model.customer.req;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class CustomerListPageReq implements Serializable {
    private static final long serialVersionUID = -6173425514343651199L;

    /**
     * 目前是子租户模式，等多组织的时候，需要改成assignOrgNoList
     */
    @EntityField(name = "分派组织租户编号")
    private List<String> assignEnterprsieNoList;

    @EntityField(name = "租户编号")
    private String enterpriseNo;

    @EntityField(name = "管理组织租户编号")
    private String managerEnterpriseNo;

    /**
     * 以下是为了兼容前端同组件调用不同apiCode定制化的
     */
    @EntityField(name = "客户编码/客户名称/英文名/助记码查询")
    private String customerKeywords;

    @EntityField(name = "合作性质集合")
    private List<String> cooperationModes;

    @EntityField(name = "价格体系编码集合")
    private List<String> priceCategoryCodeList;

    @EntityField(name = "客户分类集合")
    private List<String> customerCategoryNos;

    @EntityField(name = "归属公司模糊查询")
    private String ownerCompanyKeywords;

    @EntityField(name = "管控类型id集合")
    private List<Long> controlIdList;

    @EntityField(name = "统一社会信用代码模糊查询")
    private String unifiedSocialCodeKeywords;

    @EntityField(name = "客户性质编码集合")
    private List<String> businessTypeList;

    @EntityField(name = "客户联系人/联系电话模糊查询")
    private String linkManAndPhoneKeywords;

    @EntityField(name = "客户编码List")
    private List<String> excludeCustomerCodeList;


}
