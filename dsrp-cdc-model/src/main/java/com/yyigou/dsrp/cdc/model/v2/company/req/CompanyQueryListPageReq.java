package com.yyigou.dsrp.cdc.model.v2.company.req;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class CompanyQueryListPageReq implements Serializable {
    private static final long serialVersionUID = -2322834031477677778L;

    @EntityField(name = "租户编号")
    private String enterpriseNo;

    @EntityField(name = "企业名称模糊搜索")
    private String companyNameKeywords;

    @EntityField(name = "统一社会信用代码模糊搜索")
    private String unifiedSocialCodeKeywords;

    @EntityField(name = "合作关系：customer-客户，supplier-供应商，factory-生产厂家")
    private String partnership;

    @EntityField(name = "企业注册地")
    private Integer factoryType;

    @EntityField(name = "内部组织")
    private Integer isAssociatedEnterprise;

    @EntityField(name = "企业名称列表")
    private List<String> companyNameList;

    @EntityField(name = "勾选导出选的no")
    private List<String> idList;
}
