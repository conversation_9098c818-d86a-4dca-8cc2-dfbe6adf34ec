package com.yyigou.dsrp.cdc.model.v2.company.req;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

@Data
public class CompanyBankReq {
    @EntityField(name = "id")
    private Long id;

    @EntityField(name = "银行编码")
    private String bankCode;

    @EntityField(name = "银行类型")
    private String bankType;

    @EntityField(name = "开户银行")
    private String openBank;

    @EntityField(name = "户名")
    private String accountName;

    @EntityField(name = "账号")
    private String accountNo;

    @EntityField(name = "账号类型")
    private Integer accountType;

    @EntityField(name = "联系人")
    private String linkPerson;

    @EntityField(name = "联系电话")
    private String linkPhone;

    @EntityField(name = "状态")
    private Integer status;

    @EntityField(name = "是否默认")
    private Integer isDefault;

    @EntityField(name = "币种")
    private String currencyId;


    // -----------名字信息-----------
    @EntityField(name = "银行类型")
    private String bankTypeName;

    @EntityField(name = "币种")
    private String currencyName;

    @EntityField(name = "账号类型")
    private String accountTypeName;

    @EntityField(name = "状态")
    private String statusName;

    @EntityField(name = "是否默认")
    private String isDefaultName;
}
