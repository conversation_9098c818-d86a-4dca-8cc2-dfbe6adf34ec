package com.yyigou.dsrp.cdc.model.v2.customer.req;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;

@Data
public class CustomerCodeOrgPairReq implements Serializable {
    private static final long serialVersionUID = 5009195985397117121L;

    @EntityField(name = "使用组织编号")
    private String useOrgNo;

    @EntityField(name = "客户编码")
    private String customerCode;

}
