package com.yyigou.dsrp.cdc.model.v2.company.req;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;

@Data
public class CompanyLinkmanReq implements Serializable {
    private Long id;

    @EntityField(name = "联系人")
    private String linkman;

    @EntityField(name = "岗位/职务")
    private String position;

    @EntityField(name = "联系电话")
    private String mobilePhone;

    @EntityField(name = "固定电话")
    private String fixedPhone;

    @EntityField(name = "性别('male','secrecy','female')")
    private String sex;

    @EntityField(name = "邮箱")
    private String email;

    @EntityField(name = "")
    private String qq;

    @EntityField(name = "")
    private String wx;

    @EntityField(name = "状态：1-正常、0-作废")
    private Integer status;

    @EntityField(name = "是否默认 1：是 0：否")
    private Integer isDefault;

    @EntityField(name = "联系编码code 外部对接主键")
    private String linkCode;

    // ----------------名字信息-----------------
    @EntityField(name = "性别('male','secrecy','female')")
    private String sexName;

    @EntityField(name = "状态：1-正常、0-作废")
    private String statusName;

    @EntityField(name = "是否默认 1：是 0：否")
    private String isDefaultName;
}
