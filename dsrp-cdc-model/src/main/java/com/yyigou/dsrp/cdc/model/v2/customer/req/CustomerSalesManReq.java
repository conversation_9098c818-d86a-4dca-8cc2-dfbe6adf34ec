package com.yyigou.dsrp.cdc.model.v2.customer.req;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

@Data
public class CustomerSalesManReq {
    @EntityField(name = "ID")
    private Long id;
    /**
     * 部门编号
     */
    @EntityField(name = "部门编号")
    private String deptNo;
    /**
     * 部门名称
     */
    @EntityField(name = "部门名称")
    private String deptName;
    /**
     * 业务员编号
     */
    @EntityField(name = "业务员编号")
    private String salesManNo;
    /**
     *
     */
    @EntityField(name = "业务员姓名")
    private String salesManName;

    /**
     * 职位
     */
    @EntityField(name = "职位")
    private String post;

    /**
     * 默认订单专员  2:是 1：默认  0：否
     */
    @EntityField(name = "默认订单专员  2:是 1：默认  0：否")
    private Integer orderSpecialist;

    /**
     * 默认业务员  2:是 1：默认  0：否
     */
    @EntityField(name = "默认业务员  2:是 1：默认  0：否")
    private Integer isDefault;


    // -----------名字信息------------
    /**
     * 手机号
     */
    @EntityField(name = "手机号")
    private String mobile;

    @EntityField(name = "默认订单专员  2:是 1：默认  0：否 ")
    private String orderSpecialistName;

    @EntityField(name = "默认业务员  2:是 1：默认  0：否 ")
    private String isDefaultName;
}