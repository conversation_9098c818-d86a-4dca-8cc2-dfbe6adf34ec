package com.yyigou.dsrp.cdc.model.company.req;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 分页查询企业档案参数
 *
 * @author: Moore
 * @date: 2024/8/6 11:04
 * @version: 1.0.0
 */
@Data
public class CompanyQueryListPageReq implements Serializable {
    private static final long serialVersionUID = -2322834031477677778L;

    @EntityField(name = "租户编号")
    private String enterpriseNo;

    @EntityField(name = "合作关系：customer-客户，supplier-供应商，factory-生产厂家")
    private String partnership;

    @EntityField(name = "企业编号集合，目前勾选导出使用")
    private List<String> companyNoList;

    @EntityField(name = "租户名称模糊搜索")
    private String keywords;
}
