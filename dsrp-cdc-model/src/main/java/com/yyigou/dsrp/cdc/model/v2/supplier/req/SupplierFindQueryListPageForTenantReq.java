package com.yyigou.dsrp.cdc.model.v2.supplier.req;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

/**
 * 分页查询供应商档案参数
 *
 */
@Data
public class SupplierFindQueryListPageForTenantReq implements Serializable {
    private static final long serialVersionUID = -2322834031477677778L;

    @EntityField(name = "租户编号")
    private String enterpriseNo;

    @EntityField(name = "使用组织编号")
    private List<String> useOrgNoList;

    @EntityField(name = "供应商编码集合")
    private Set<String> supplierCodeSet;

}
