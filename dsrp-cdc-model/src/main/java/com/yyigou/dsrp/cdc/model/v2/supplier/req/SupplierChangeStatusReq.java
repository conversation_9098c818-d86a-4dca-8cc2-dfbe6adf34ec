package com.yyigou.dsrp.cdc.model.v2.supplier.req;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;

@Data
public class SupplierChangeStatusReq implements Serializable {
    @EntityField(name = "租户编号")
    private String enterpriseNo;

    @EntityField(name = "使用组织编号")
    private String useOrgNo;

    /**
     * 供应商编码
     */
    @EntityField(name = "供应商编码")
    private String supplierCode;

    /**
     * 管控状态 1：启用 2：停用 3：冻结
     */
    @EntityField(name = "管控状态")
    private String controlStatus;

}
