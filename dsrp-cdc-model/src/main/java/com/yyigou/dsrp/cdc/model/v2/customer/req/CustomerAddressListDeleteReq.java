package com.yyigou.dsrp.cdc.model.v2.customer.req;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class CustomerAddressListDeleteReq implements Serializable {
    @EntityField(name = "租户编号")
    private String enterpriseNo;

    @EntityField(name = "使用组织编号")
    private String useOrgNo;

//    @EntityField(name = "管理组织编号")
//    private String manageOrgNo;
//
//    @EntityField(name = "客户编号")
//    private String customerNo;

    @EntityField(name = "客户编码")
    private String customerCode;

//    @EntityField(name = "客户名称")
//    private String customerName;
//
//    @EntityField(name = "客户英文名称")
//    private String customerNameEn;
//
//    @EntityField(name = "助记码")
//    private String mnemonicCode;
//
//    @EntityField(name = "客户类型")
//    private String transactionType;
//
//    @EntityField(name = "客户分类")
//    private String customerCategoryNo;
//
//    @EntityField(name = "企业编号")
//    private String companyNo;
//
//    @EntityField(name = "企业名称")
//    private String companyName;
//
//    @EntityField(name = "统一社会信用代码。公立医院的事业单位法人证号")
//    private String unifiedSocialCode;
//
//    @EntityField(name = "企业注册地域：1-境内，2-境外")
//    private Integer factoryType;
//
//    @EntityField(name = "国家/地区")
//    private String country;
//
//    @EntityField(name = "纳税类别")
//    private String taxCategory;
//
//    @EntityField(name = "经济类型")
//    private String economicType;
//
//    @EntityField(name = "是否散户")
//    private Integer retailInvestors;
//
//    @EntityField(name = "是否GSP管控")
//    private Integer isGspControl;
//
//    @EntityField(name = "是否关联企业：1-是，0-否")
//    private Integer isAssociatedEnterprise;
//
//    @EntityField(name = "关联组织编号,关联企业时选择")
//    private String associatedOrgNo;
//
//    @EntityField(name = "关联组织编码,关联企业时选择")
//    private String associatedOrgCode;
//
//    @EntityField(name = "关联组织名称,关联企业时选择")
//    private String associatedOrgName;
//
//    @EntityField(name = "是否医疗机构")
//    private Integer isMedicalInstitution;
//
//    @EntityField(name = "机构类型：yy:医院,dsfsys:第三方实验室,dsftjzx:第三方体检中心,jkzx:疾控中心,xz:血站,zs:诊所,ylhly:养老护理院,jgqt:其他")
//    private String institutionalType;
//
//    @EntityField(name = "医院类型:yy：公立医院，mbyy:民营医院")
//    private String hospitalType;
//
//    @EntityField(name = "医院等级：0:无等级,1:一级甲等,2:一级乙等,3:一级丙等,4:二级甲等,5:二级乙等,6:二级丙等,7:三级特等,8:三级甲等,9:三级乙等,10:三级丙等")
//    private Integer hospitalClass;
//
//    @EntityField(name = "备注")
//    private String remark;
//
//    @EntityField(name = "客户状态：1-正式，2-草稿")
//    private Integer businessFlag;

    //----------------以下为业务信息----------------

//    @EntityField(name = "合作性质")
//    private String cooperationMode;
//
//    @EntityField(name = "合作性质名称")
//    private String cooperationModeName;
//
//    @EntityField(name = "客户性质")
//    private String businessType;
//
//    @EntityField(name = "客户性质名称")
//    private String businessTypeName;
//
//    @EntityField(name = "价格体系")
//    private String priceCategoryCode;
//
//    @EntityField(name = "交易币种")
//    private String currencyId;
//
//    @EntityField(name = "结算方式")
//    private String settlementModes;
//
//    @EntityField(name = "结算方式名称")
//    private String settlementModesName;
//
//    @EntityField(name = "收款协议")
//    private String  receiveAgreement;
//
//    @EntityField(name = "收款条件")
//    private String receiveCondition;
//
//    @EntityField(name = "信用额度")
//    private String creditAmount;
//
//    @EntityField(name = "信用期限")
//    private String creditDates;
//
//    @EntityField(name = "合作起始时间")
//    private String coopStartTime;
//
//    @EntityField(name = "合作结束时间")
//    private String coopEndTime;
//
//    @EntityField(name = "业务归属")
//    private String ownerCompany;

    //----------------以下为关联信息----------------

//    @EntityField(name = "客户联系人")
//    private List<CompanyLinkmanDTO> linkmanList;
//
    @EntityField(name = "联系地址")
    private List<Long> linkAddressList;
//
//    @EntityField(name = "银行信息")
//    private List<CompanyBankDTO> bankList;
//
//    @EntityField(name = "开票信息")
//    private List<CustomerInvoiceDTO> invoiceList;
//
//    @EntityField(name = "负责人信息")
//    private List<CustomerSalesManDTO> salesManList;
}
