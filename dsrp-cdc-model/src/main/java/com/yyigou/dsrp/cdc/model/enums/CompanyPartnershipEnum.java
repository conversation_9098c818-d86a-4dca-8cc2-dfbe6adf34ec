package com.yyigou.dsrp.cdc.model.enums;

import lombok.Getter;

import java.util.HashMap;
import java.util.Map;
/**
 * 企业合作类型
 *
 * @author:  Moore
 * @date: 2024/7/17 11:00
 * @version: 1.0.0
 */
@Getter
public enum CompanyPartnershipEnum {

    CUSTOMER("customer", "客户"),
    SUPPLIER("supplier", "供应商"),
    FACTORY("factory", "生产厂家"),
    ;

    private final String type;
    private final String name;

    CompanyPartnershipEnum(String type, String name) {
        this.type = type;
        this.name = name;
    }

    private static Map<String, CompanyPartnershipEnum> maps = new HashMap<>();
    private static Map<String, CompanyPartnershipEnum> typeMaps = new HashMap<>();

    static {
        for (CompanyPartnershipEnum item : CompanyPartnershipEnum.values()) {
            maps.put(item.getType(), item);
            typeMaps.put(item.getName(),item);
        }
    }

    public static CompanyPartnershipEnum getByType(final String status) {
        if (status == null) {
            return null;
        }
        return maps.get(status);
    }

    public static CompanyPartnershipEnum getByName(final String name) {
        if (name == null) {
            return null;
        }
        return typeMaps.get(name);
    }
}
