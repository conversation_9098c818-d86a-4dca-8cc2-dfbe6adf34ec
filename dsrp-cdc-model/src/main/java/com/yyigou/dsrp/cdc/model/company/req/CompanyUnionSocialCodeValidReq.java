package com.yyigou.dsrp.cdc.model.company.req;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;

/**
 * 企业统一社会信用代码唯一性校验参数
 *
 * @author:  Moore
 * @date: 2024/7/6 18:10
 * @version: 1.0.0
 */
@Data
public class CompanyUnionSocialCodeValidReq implements Serializable {
    private static final long serialVersionUID = 8281492269562081053L;

    @EntityField(name = "租户编号")
    private String enterpriseNo;
    @EntityField(name = "企业编号")
    private String companyNo;
    @EntityField(name = "企业统一社会信用代码")
    private String unifiedSocialCode;
}
