package com.yyigou.dsrp.cdc.model.company.req;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;

/**
 * 企业操作日志表
 *
 * @author: Moore
 * @date: 2024/7/6 20:31
 * @version: 1.0.0
 */
@Data
public class CompanyLogReq implements Serializable {
    private static final long serialVersionUID = -7092335314516592273L;

    @EntityField(name = "租户编号", stringValueTypeLength = 50)
    private String enterpriseNo;

    @EntityField(name = "企业档案编号", stringValueTypeLength = 32)
    private String companyNo;

    @EntityField(name = "操作内容", stringValueTypeLength = 500)
    private String operateContent;

    @EntityField(name = "操作类型 1:企业资料库新增 2:企业资料库编辑 3:生产厂家档案新增 4:生产厂家档案编辑 5:客户档案新增 6:客户档案编辑 7:供应商档案新增 8:供应商档案编辑", stringValueTypeLength = 1)
    private Integer operateType;

    @EntityField(name = "操作人编号", stringValueTypeLength = 32)
    private String operateNo;

    @EntityField(name = "操作人名称", stringValueTypeLength = 100)
    private String operateName;

    @EntityField(name = "操作时间", stringValueTypeLength = 19)
    private String operateTime;

}