package com.yyigou.dsrp.cdc.model.constant;

public class DistributedLockConstant {
    public static final String DELIMITER = "-";

    public static final String COMPANY_SAVE_LOCK_KEY = "company_save";

    public static final String SUPPLIER_SAVE_LOCK_KEY = "supplier_save";

    public static final String SUPPLIER_CATEGORY_SAVE_LOCK_KEY = "supplier_category_save";

    public static final String CUSTOMER_SAVE_LOCK_KEY = "customer_save";

    public static final String CUSTOMER_CATEGORY_SAVE_LOCK_KEY = "customer_category_save";

    public static final String SUPPLIER_ASSIGN_LOCK_KEY = "supplier_assign";

    public static final String CUSTOMER_ASSIGN_LOCK_KEY = "customer_assign";


    // 供应商档案开放平台接口-迪安
    public static final String SUPPLIER_DA_OPEN_SAVE_LOCK_KEY = "da_supplier_save";

    // 客户档案开放平台接口-迪安
    public static final String CUSTOMER_DA_OPEN_SAVE_LOCK_KEY = "da_customer_save";


    public static final int DEFAULT_WAIT_TIME = 2000;
    public static final int DEFAULT_LEASE_TIME = 15000;
}
