package com.yyigou.dsrp.cdc.model.v2.company.req;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Description 企业档案查询组织请求参数
 * @Classname CompanyAssociateOrgRequest
 * @Date 2025/4/30 21:26
 * @author: baoww
 */
@Data
public class CompanyAssociateOrgReq implements Serializable {
    private static final long serialVersionUID = 1117212035056347555L;

    @EntityField(name = "租户编号")
    private String enterpriseNo;
    @EntityField(name = "组织编号集合")
    private List<String> orgNoList;
    @EntityField(name = "组织编号集合")
    private List<String> orgCodeList;
    @EntityField(name = "来源类型：1-供应商，2-客户，3-纳税主体组织")
    private Integer sourceType;
}
