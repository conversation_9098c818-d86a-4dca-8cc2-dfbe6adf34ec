package com.yyigou.dsrp.cdc.model.v2.company.req;

import com.yyigou.ddc.common.service.annotation.Entity;
import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;

@Data
@Entity(name = "类型地址")
public class CompanyShippingAddressReq implements Serializable {
    @EntityField(name = "主键")
    private Long id;

    @EntityField(name = "联系人编码")
    private String linkAddressCode;

    @EntityField(name = "收货人")
    private String receiveUser;

    @EntityField(name = "联系电话")
    private String receivePhone;

    @EntityField(name = "行政地区编码")
    private String regionCode;

    @EntityField(name = "行政地区名称")
    private String regionName;

    @EntityField(name = "收货详细地址")
    private String receiveAddr;

    @EntityField(name = "收货地址状态:1默认 0非默认")
    private Integer isDefault;

    @EntityField(name = "地址类型：1联系地址 2：收票地址 3:仓库地址 4：收货地址")
    private String addressType;

    @EntityField(name = "地址描述")
    private String addressDesc;

    // -----------名字信息-----------
    @EntityField(name = "地址类型：1联系地址 2：收票地址 3:仓库地址 4：收货地址")
    private String addressTypeName;

    @EntityField(name = "行政地区名全称")
    private String regionFullName;

    @EntityField(name = "收货地址状态:1默认 0非默认")
    private String isDefaultName;
}