package com.yyigou.dsrp.cdc.model.v2.customer.req;

import com.yyigou.ddc.common.service.annotation.EntityField;
import com.yyigou.dsrp.cdc.model.v2.company.req.CompanyBankReq;
import com.yyigou.dsrp.cdc.model.v2.company.req.CompanyCertReq;
import com.yyigou.dsrp.cdc.model.v2.company.req.CompanyLinkmanReq;
import com.yyigou.dsrp.cdc.model.v2.company.req.CompanyShippingAddressReq;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class CustomerApplyFormReq implements Serializable {
    private static final long serialVersionUID = 1L;
    // ----------------组织信息----------------
    @EntityField(name = "管理组织编号")
    private String manageOrgNo;

    @EntityField(name = "管理组织名称")
    private String manageOrgName;

    @EntityField(name = "使用组织编号")
    private String useOrgNo;

    @EntityField(name = "使用组织名称")
    private String useOrgName;


    // ----------------客户基本信息----------------
    @EntityField(name = "客户编号")
    private String customerNo;

    @EntityField(name = "客户编码")
    private String customerCode;

    @EntityField(name = "客户名称")
    private String customerName;

    @EntityField(name = "客户英文名")
    private String customerNameEn;

    @EntityField(name = "助记码")
    private String mnemonicCode;

    @EntityField(name = "客户类型")
    private String transactionType;

    @EntityField(name = "客户类型名称")
    private String transactionTypeName;

    @EntityField(name = "客户分类编号")
    private String customerCategoryNo;

    @EntityField(name = "客户分类名称")
    private String customerCategoryName;

    @EntityField(name = "是否散户")
    private Integer retailInvestors;

    @EntityField(name = "是否散户名称")
    private String retailInvestorsName;

    @EntityField(name = "是否内部组织")
    private Integer isAssociatedEnterprise;

    @EntityField(name = "是否内部组织")
    private String isAssociatedEnterpriseName;

    @EntityField(name = "内部组织编号")
    private String associatedOrgNo;

    @EntityField(name = "内部组织编码")
    private String associatedOrgCode;

    @EntityField(name = "内部组织名称")
    private String associatedOrgName;

    @EntityField(name = "是否Gsp管控：1-是，0-否")
    private Integer isGspControl;

    @EntityField(name = "是否Gsp管控：1-是，0-否")
    private String isGspControlName;

    @EntityField(name = "备注")
    private String remark;


    // ----------------企业信息----------------
    @EntityField(name = "企业编号")
    private String companyNo;

    @EntityField(name = "企业名称")
    private String companyName;

    @EntityField(name = "企业统一社会信用代码")
    private String unifiedSocialCode;

    @EntityField(name = "企业区域")
    private Integer factoryType;

    @EntityField(name = "企业区域名称")
    private String factoryTypeName;

    @EntityField(name = "国家地区")
    private String countryRegionId;

    @EntityField(name = "国家地区")
    private String countryRegionName;

    @EntityField(name = "注册地址")
    private String address;

    @EntityField(name = "纳税类别")
    private String taxCategory;

    @EntityField(name = "纳税类别名称")
    private String taxCategoryName;

    @EntityField(name = "经济类型")
    private String economicType;

    @EntityField(name = "经济类型名称")
    private String economicTypeName;

    @EntityField(name = "是否医疗机构")
    private Integer isMedicalInstitution;

    @EntityField(name = "是否医疗机构名称")
    private String isMedicalInstitutionName;

    @EntityField(name = "医疗机构类型")
    private String institutionalType;

    @EntityField(name = "医疗机构类型名称")
    private String institutionalTypeName;

    @EntityField(name = "医院性质")
    private String hospitalType;

    @EntityField(name = "医院性质名称")
    private String hospitalTypeName;

    @EntityField(name = "医院等级")
    private Integer hospitalClass;

    @EntityField(name = "医院等级名称")
    private String hospitalClassName;

    @EntityField(name = "银行信息")
    private List<CompanyBankReq> bankList;

    @EntityField(name = "企业证书信息")
    private List<CompanyCertReq> companyCertList;


    // ----------------业务信息----------------
    @EntityField(name = "合作性质")
    private String cooperationMode;

    @EntityField(name = "合作性质名称")
    private String cooperationModeName;

    @EntityField(name = "客户性质")
    private String businessType;

    @EntityField(name = "客户性质名称")
    private String businessTypeName;

    @EntityField(name = "价格体系")
    private String priceCategoryCode;

    @EntityField(name = "价格体系")
    private String priceCategoryName;

    @EntityField(name = "交易币种")
    private String currencyId;

    @EntityField(name = "交易币种名称")
    private String currencyName;

    @EntityField(name = "结算方式")
    private String settlementModes;

    @EntityField(name = "结算方式名称")
    private String settlementModesName;

    @EntityField(name = "收款协议")
    private String  receiveAgreement;

    @EntityField(name = "收款条件")
    private String receiveCondition;

    @EntityField(name = "信用额度")
    private String creditAmount;

    @EntityField(name = "信用期限")
    private String creditDates;

    @EntityField(name = "合作起始时间")
    private String coopStartTime;

    @EntityField(name = "合作结束时间")
    private String coopEndTime;

    @EntityField(name = "业务归属")
    private String ownerCompany;

    // ----------------tab信息----------------
    @EntityField(name = "客户证书信息")
    private List<CompanyCertReq> customerCertList;
    
    @EntityField(name = "联系人信息")
    private List<CompanyLinkmanReq> linkmanList;

    @EntityField(name = "联系地址信息")
    private List<CompanyShippingAddressReq> linkAddressList;

    @EntityField(name = "负责人信息")
    private List<CustomerSalesManReq> customerManList;

    @EntityField(name = "开票信息")
    private List<CustomerInvoiceReq> invoiceList;
}
