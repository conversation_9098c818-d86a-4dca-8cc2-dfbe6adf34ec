<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">

    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.yyigou.ddc</groupId>
        <artifactId>common-parent</artifactId>
        <version>2.0.0-SNAPSHOT</version>
    </parent>

    <groupId>com.yyigou.dsrp.cdc</groupId>
    <artifactId>service-dsrp-cdc</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>pom</packaging>

    <name>service-dsrp-cdc</name>
    <description>企业中心</description>

    <properties>
        <java.version>1.8</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <cdc.version>1.0.0-SNAPSHOT</cdc.version>
        <spring-boot.version>2.3.10.RELEASE</spring-boot.version>
        <spring_version>5.2.14.RELEASE</spring_version>
        <spring_kafka_version>2.5.12.RELEASE</spring_kafka_version>
        <service_mq_version>2.0.0-SNAPSHOT</service_mq_version>
        <api.gateway.client>2.0.0-SNAPSHOT</api.gateway.client>
        <mapstruct.version>1.4.2.Final</mapstruct.version>
        <pagehelper_version>5.3.2</pagehelper_version>
    </properties>

    <modules>
        <module>dsrp-cdc-api</module>
        <module>dsrp-cdc-client</module>
        <module>dsrp-cdc-dao</module>
        <module>dsrp-cdc-service</module>
        <module>dsrp-cdc-manager</module>
        <module>dsrp-cdc-provider</module>
        <module>dsrp-cdc-common</module>
        <module>dsrp-cdc-model</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.yyigou.dsrp.cdc</groupId>
                <artifactId>dsrp-cdc-common</artifactId>
                <version>${cdc.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yyigou.dsrp.cdc</groupId>
                <artifactId>dsrp-cdc-model</artifactId>
                <version>${cdc.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yyigou.dsrp.cdc</groupId>
                <artifactId>dsrp-cdc-api</artifactId>
                <version>${cdc.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yyigou.dsrp.cdc</groupId>
                <artifactId>dsrp-cdc-client</artifactId>
                <version>${cdc.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yyigou.dsrp.cdc</groupId>
                <artifactId>dsrp-cdc-manager</artifactId>
                <version>${cdc.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yyigou.dsrp.cdc</groupId>
                <artifactId>dsrp-cdc-dao</artifactId>
                <version>${cdc.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yyigou.dsrp.cdc</groupId>
                <artifactId>dsrp-cdc-service</artifactId>
                <version>${cdc.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yyigou.dsrp.cdc</groupId>
                <artifactId>dsrp-cdc-provider</artifactId>
                <version>${cdc.version}</version>
            </dependency>

            <!-- springboot -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-parent</artifactId>
                <type>pom</type>
                <scope>import</scope>
                <version>${spring-boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-log4j</artifactId>
                <version>1.3.8.RELEASE</version>
            </dependency>

            <!-- 公共依赖 -->
            <dependency>
                <groupId>com.yyigou.ddc</groupId>
                <artifactId>common-base-lite</artifactId>
                <version>${common_base_version}</version>
            </dependency>
            <!-- 启用mybatisplus依赖-->
            <dependency>
                <groupId>com.yyigou.ddc</groupId>
                <artifactId>common-persistent-mybatisplus</artifactId>
                <version>${common_base_version}</version>
            </dependency>

            <dependency>
                <groupId>com.yyigou.ddc.services</groupId>
                <artifactId>mq-manager</artifactId>
                <version>${service_mq_version}</version>
            </dependency>
            <dependency>
                <groupId>com.yyigou.ddc</groupId>
                <artifactId>common-sql-analysis</artifactId>
                <version>${common_base_version}</version>
            </dependency>
            <dependency>
                <groupId>com.yyigou.ddc</groupId>
                <artifactId>common-dubbo-registry-zookeeper</artifactId>
                <version>${common_base_version}</version>
            </dependency>
            <dependency>
                <groupId>com.yyigou.ddc</groupId>
                <artifactId>api-gateway-client</artifactId>
                <version>${api.gateway.client}</version>
            </dependency>
            <!--分布式事务相关依赖-->
            <dependency>
                <groupId>com.yyigou.ddc</groupId>
                <artifactId>common-distributed-transaction</artifactId>
                <version>${common_base_version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-generator</artifactId>
                <version>3.5.2</version>
            </dependency>
            <dependency>
                <groupId>org.apache.velocity</groupId>
                <artifactId>velocity-engine-core</artifactId>
                <version>2.3</version>
            </dependency>
            <dependency>
                <groupId>com.yyigou.ddc.services</groupId>
                <artifactId>ddc-task-api</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.yyigou.ddc</groupId>
                <artifactId>api-gateway-client</artifactId>
                <version>2.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>

            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <version>${mapstruct.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>com.yyigou.ddc</groupId>
                <artifactId>number-center-api</artifactId>
                <version>2.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>5.8.10</version>
            </dependency>
            <!--csc服务-->
            <dependency>
                <groupId>com.yyigou.ddc.services</groupId>
                <artifactId>dsrp-csc-api</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.yyigou.ddc.services</groupId>
                <artifactId>ddc-csc-api</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.yyigou.ddc.services</groupId>
                <artifactId>ddc-uim-api</artifactId>
                <version>3.0.0-SNAPSHOT</version>
                <exclusions>
                    <exclusion>
                        <!-- UIM引入的mybatis和本项目有冲突排除掉 -->
                        <groupId>com.yyigou.ddc</groupId>
                        <artifactId>common-persistent-mybatis</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.yyigou.ddc.services</groupId>
                <artifactId>ddc-cert-api</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <!--字典服务-->
            <dependency>
                <groupId>com.yyigou.ddc.services</groupId>
                <artifactId>ddc-dict-api</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.yyigou.ddc</groupId>
                <artifactId>common-cache-redisson3</artifactId>
                <version>${common_base_version}</version>
            </dependency>
            <dependency>
                <groupId>com.yyigou.ddc.services</groupId>
                <artifactId>ddc-task-api</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper</artifactId>
                <version>${pagehelper_version}</version>
            </dependency>

            <dependency>
                <groupId>com.yyigou.ddc.services</groupId>
                <artifactId>ddc-dw-doris-util</artifactId>
                <version>2.0.0-SNAPSHOT</version>
                <exclusions>
                    <exclusion>
                        <!-- 引入的mybatis和本项目有冲突排除掉 -->
                        <groupId>com.yyigou.ddc</groupId>
                        <artifactId>common-persistent-mybatis</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- 电子资料库依赖 -->
            <dependency>
                <groupId>com.yyigou.dsrp.cert</groupId>
                <artifactId>dsrp-cert-client-bean</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.yyigou.dsrp.cert</groupId>
                <artifactId>dsrp-cert-client</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <!-- uim -->
            <dependency>
                <groupId>com.yyigou.ddc.services</groupId>
                <artifactId>ddc-uim-api</artifactId>
                <version>3.0.0-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.yyigou.dsrp.gcs</groupId>
                <artifactId>dsrp-gcs-client</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <!--csc服务-->
            <dependency>
                <groupId>com.yyigou.ddc.services</groupId>
                <artifactId>dsrp-csc-api</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.yyigou.ddc</groupId>
                <artifactId>common-dubbo-filter</artifactId>
                <version>${common_base_version}</version>
            </dependency>
            <dependency>
                <groupId>com.yyigou.ddc.services</groupId>
                <artifactId>ddc-uap-api</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.yyigou.ddc.services</groupId>
                <artifactId>ddc-uap-client</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.yyigou.ddc.services</groupId>
                <artifactId>openlink-api</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.yyigou.ddc.services</groupId>
                <artifactId>ddc-dw-doris-util-plus</artifactId>
                <version>1.0.0-SNAPSHOT</version>
                <exclusions>
                    <exclusion>
                        <!-- 引入的mybatis和本项目有冲突排除掉 -->
                        <groupId>com.yyigou.ddc</groupId>
                        <artifactId>common-persistent-mybatis</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.yyigou.ddc.services</groupId>
                <artifactId>dsrp-gsp-api</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.yyigou.ddc.services</groupId>
                <artifactId>dlog-util</artifactId>
                <version>2.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>javax.annotation</groupId>
                <artifactId>javax.annotation-api</artifactId>
                <version>1.3.2</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <!-- spring boot 与 maven结合打包插件 , 打包期间用到的-->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <layers>
                        <enabled>true</enabled>
                    </layers>
                    <excludes>
                        <!-- 打包的时候忽略lombok，因为lombok值在编译器起作用 -->
                        <exclude>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                        </exclude>
                    </excludes>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                </configuration>
            </plugin>
            <!-- 统一管理父子模块版本号 -->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>versions-maven-plugin</artifactId>
                <version>2.8.1</version>
                <configuration>
                    <generateBackupPoms>false</generateBackupPoms>
                </configuration>
            </plugin>
        </plugins>
        <resources>
            <!-- 控制资源文件的拷贝 -->
            <resource>
                <directory>src/main/resources</directory>
                <targetPath>${project.build.directory}/classes</targetPath>
            </resource>
        </resources>
    </build>

</project>