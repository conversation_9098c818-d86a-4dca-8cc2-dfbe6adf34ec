package com.yyigou.dsrp.cdc.common.util;

import org.apache.commons.lang3.StringUtils;

public class FormatNameUtils {

    private static final String[] zhSymbol = {"！", "＃", "＄", "％", "＆", "＊", "‘", "（", "）", "＋", "－", "＝", "：", "；", "，", "．", "＜", "＞", "？", "の"};

    private static final String[] enSymbol = {"!", "#", "\\$", "%", "&", "\\*", "'", "\\(", "\\)", "\\+", "-", "=", ":", ";", ",", "\\.", "<", ">", "\\?", "@"};

    public static String formatName(String name, Boolean isChina) {
        if (StringUtils.isBlank(name)) {
            return name;
        }
        String formatName = name;
        if (isChina) {
            for (int i = 0; i < zhSymbol.length; i++) {
                formatName = formatName.replaceAll(enSymbol[i], zhSymbol[i]);
            }
            //国内企业不会有空格
            formatName = formatName.replaceAll(" ", "");
        } else {
            for (int i = 0; i < enSymbol.length; i++) {
                formatName = formatName.replaceAll(zhSymbol[i], enSymbol[i]);
            }
            formatName = formatName.trim();
        }
        return formatName;
    }
}
