package com.yyigou.dsrp.cdc.common.enums;

import java.util.HashMap;
import java.util.Map;

public enum EnableEnum {

    ENABLE(1, "启用"),
    DISABLE(2, "停用");

    private static Map<Integer, EnableEnum> MAPS = new HashMap<>();
    private static Map<String, EnableEnum> NAME_MAPS = new HashMap<>();

    static {
        for (EnableEnum item : EnableEnum.values()) {
            MAPS.put(item.getValue(), item);
            NAME_MAPS.put(item.getName(), item);
        }
    }

    private final Integer value;
    private final String name;

    EnableEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    public static EnableEnum getByValue(final Integer value) {
        if (value == null) {
            return null;
        }
        return MAPS.get(value);
    }

    public static EnableEnum getByName(final String name) {
        if (name == null) {
            return null;
        }
        return NAME_MAPS.get(name);
    }

    public static String getNameByValue(final Integer value) {
        if (value == null) {
            return null;
        }
        return MAPS.get(value) == null ? null : MAPS.get(value).getName();
    }

    public Integer getValue() {
        return value;
    }

    public String getName() {
        return name;
    }
}
