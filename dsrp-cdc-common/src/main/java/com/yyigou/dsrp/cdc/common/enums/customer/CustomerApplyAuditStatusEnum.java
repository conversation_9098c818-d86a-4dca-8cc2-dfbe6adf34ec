package com.yyigou.dsrp.cdc.common.enums.customer;

import java.util.HashMap;
import java.util.Map;

/**
 * 客户档案申请审核状态：0-草稿 1-待审核 2-审核通过 3-审核拒绝
 */
public enum CustomerApplyAuditStatusEnum {
    DRAFT(0, "草稿"),
    TO_BE_AUDITED(1, "待审核"),
    APPROVED(2, "审核通过"),
    REJECTED(3, "审核拒绝");


    private final Integer value;
    private final String name;

    public Integer getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    CustomerApplyAuditStatusEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    private static Map<Integer, CustomerApplyAuditStatusEnum> MAP = new HashMap<>();

    static {
        for (CustomerApplyAuditStatusEnum item : CustomerApplyAuditStatusEnum.values()) {
            MAP.put(item.getValue(), item);
        }

    }

    public static CustomerApplyAuditStatusEnum getByValue(final Integer value) {
        if (value == null) {
            return null;
        }
        return MAP.get(value);
    }

    public static String getNameByValue(final Integer value) {
        if (value == null) {
            return null;
        }
        return MAP.get(value) == null ? null : MAP.get(value).getName();
    }
}
