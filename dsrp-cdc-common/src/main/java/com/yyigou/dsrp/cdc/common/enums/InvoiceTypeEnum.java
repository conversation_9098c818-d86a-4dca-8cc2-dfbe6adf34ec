package com.yyigou.dsrp.cdc.common.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * 发票类型: 1增值税专用发票 2增值税普通发票 3.增值税电子专用发票 4.增值税电子普通发票
 */
public enum InvoiceTypeEnum {

    zzszyfq(1, "增值税专用发票"),
    zzsptfp(2, "增值税普通发票"),
    zzsdzzyfp(3, "增值税电子专用发票"),
    zzsdzptfp(4, "增值税电子普通发票"),
    qdptfp(32, "数电票（普通发票）"),
    qdzyfp(31, "数电票（增值税专票）"),
    All_ELECTRIC_NORMAL(55, "数电发票（普通发票）"),
    All_ELECTRIC_PROFESSION(56, "数电发票（增值税专用发票）"),
    All_ELECTRIC_PAPER_NORMAL(57, "数电纸质发票（普通发票）"),
    All_ELECTRIC_PAPER_PROFESSION(58, "数电纸质发票（增值税专用发票）");

    private final Integer type;
    private final String name;

    InvoiceTypeEnum(Integer type, String name) {
        this.type = type;
        this.name = name;
    }

    public Integer getType() {
        return type;
    }

    public String getName() {
        return name;
    }

    private static Map<Integer, InvoiceTypeEnum> maps = new HashMap<>();
    private static Map<String, InvoiceTypeEnum> nameMaps = new HashMap<>();

    static {
        for (InvoiceTypeEnum item : InvoiceTypeEnum.values()) {
            maps.put(item.getType(), item);
            nameMaps.put(item.getName(), item);
        }
    }

    public static InvoiceTypeEnum getByType(final Integer type) {
        if (type == null) {
            return null;
        }
        return maps.get(type);
    }

    public static InvoiceTypeEnum getByName(final String name) {
        if (name == null) {
            return null;
        }
        return nameMaps.get(name);
    }
}
