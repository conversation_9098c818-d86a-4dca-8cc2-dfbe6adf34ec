package com.yyigou.dsrp.cdc.common.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * 企业类型：1-供应商，2-客户
 *
 * <AUTHOR>
 */
public enum CompanyTypeEnum {

    SUPPLIER(1, "供应商"),
    CUSTOMER(2, "客户"),
    ;


    private final Integer value;
    private final String name;

    public Integer getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    CompanyTypeEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    private static Map<Integer, CompanyTypeEnum> MAP = new HashMap<>();

    static {
        for (CompanyTypeEnum item : CompanyTypeEnum.values()) {
            MAP.put(item.getValue(), item);
        }

    }

    public static CompanyTypeEnum getByValue(final Integer value) {
        if (value == null) {
            return null;
        }
        return MAP.get(value);
    }

    public static String getNameByValue(final Integer value) {
        if (value == null) {
            return null;
        }
        return MAP.get(value) == null ? null : MAP.get(value).getName();
    }
}
