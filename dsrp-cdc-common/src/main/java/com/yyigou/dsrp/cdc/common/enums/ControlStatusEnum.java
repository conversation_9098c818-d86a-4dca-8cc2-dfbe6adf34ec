package com.yyigou.dsrp.cdc.common.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * 客户管控状态：1-启用，2-停用
 *
 * <AUTHOR>
 */
public enum ControlStatusEnum {

    ENABLE(1, "启用"),
    DISABLE(2, "停用"),
    ;


    private final Integer value;
    private final String name;

    public Integer getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    ControlStatusEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    private static Map<Integer, ControlStatusEnum> MAP = new HashMap<>();

    static {
        for (ControlStatusEnum item : ControlStatusEnum.values()) {
            MAP.put(item.getValue(), item);
        }

    }

    public static ControlStatusEnum getByValue(final Integer value) {
        if (value == null) {
            return null;
        }
        return MAP.get(value);
    }

    public static String getNameByValue(final Integer value) {
        if (value == null) {
            return null;
        }
        return MAP.get(value) == null ? null : MAP.get(value).getName();
    }
}
