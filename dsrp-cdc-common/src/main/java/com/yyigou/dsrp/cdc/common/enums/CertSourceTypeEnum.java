package com.yyigou.dsrp.cdc.common.enums;

import java.util.HashMap;
import java.util.Map;

public enum CertSourceTypeEnum {
    COMPANY(1, "企业档案"),
    SUPPLIER(2, "供应商档案"),
    CUSTOMER(3, "客户档案");


    private final Integer value;
    private final String name;

    public Integer getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    CertSourceTypeEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    private static Map<Integer, CertSourceTypeEnum> MAP = new HashMap<>();
    private static Map<String, CertSourceTypeEnum> NAME_MAP = new HashMap<>();

    static {
        for (CertSourceTypeEnum item : CertSourceTypeEnum.values()) {
            MAP.put(item.getValue(), item);
            NAME_MAP.put(item.getName(), item);
        }

    }

    public static CertSourceTypeEnum getByValue(final Integer value) {
        if (value == null) {
            return null;
        }
        return MAP.get(value);
    }

    public static CertSourceTypeEnum getByName(final String name) {
        if (name == null) {
            return null;
        }
        return NAME_MAP.get(name);
    }

    public static String getNameByValue(final Integer value) {
        if (value == null) {
            return null;
        }
        return MAP.get(value) == null ? null : MAP.get(value).getName();
    }
}
