package com.yyigou.dsrp.cdc.common.enums;

import java.util.HashMap;
import java.util.Map;

public enum LinkmanTypeEnum {

    SALE("is_sale", "客户(销售)"),
    SUPPLY("is_supply", "供应商(采购)"),
    ;

    private final String value;
    private final String name;

    public String getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    LinkmanTypeEnum(String value, String name) {
        this.value = value;
        this.name = name;
    }

    private static Map<String, LinkmanTypeEnum> MAP = new HashMap<>();

    static {
        for (LinkmanTypeEnum item : LinkmanTypeEnum.values()) {
            MAP.put(item.getValue(), item);
        }

    }

    public static LinkmanTypeEnum getByValue(final String value) {
        if (value == null) {
            return null;
        }
        return MAP.get(value);
    }

    public static String getNameByValue(final String value) {
        if (value == null) {
            return null;
        }
        return MAP.get(value) == null ? null : MAP.get(value).getName();
    }

}
