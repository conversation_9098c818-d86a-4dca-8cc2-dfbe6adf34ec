package com.yyigou.dsrp.cdc.common.enums;

import java.util.HashMap;
import java.util.Map;

public enum CommonSyncStatusEnum {

    SUCC(1, "成功"),
    FAIL(2, "失败"),
    Pending(0, "未同步");


    private final Integer value;
    private final String name;

    public Integer getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    CommonSyncStatusEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    private static Map<Integer, CommonSyncStatusEnum> MAP = new HashMap<>();
    private static Map<String, CommonSyncStatusEnum> NAME_MAP = new HashMap<>();

    static {
        for (CommonSyncStatusEnum item : CommonSyncStatusEnum.values()) {
            MAP.put(item.getValue(), item);
            NAME_MAP.put(item.getName(), item);
        }

    }

    public static CommonSyncStatusEnum getByValue(final Integer value) {
        if (value == null) {
            return null;
        }
        return MAP.get(value);
    }

    public static CommonSyncStatusEnum getByName(final String name) {
        if (name == null) {
            return null;
        }
        return NAME_MAP.get(name);
    }

    public static String getNameByValue(final Integer value) {
        if (value == null) {
            return null;
        }
        return MAP.get(value) == null ? null : MAP.get(value).getName();
    }
}
