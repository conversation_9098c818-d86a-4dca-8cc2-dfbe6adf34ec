package com.yyigou.dsrp.cdc.common.enums.customer;

import java.util.HashMap;
import java.util.Map;

/**
 * 客商GSP的审核状态
 */
public enum CustomerGspAuditStatusEnum {
    /**
     * 客商GSP的审核状态
     * 0:未创建，1：待审批 2：审批通过 3：审核失败;9-审批撤回
     */
    NOT_CREATED("0", "未创建"),
    PENDING_APPROVAL("1", "待审批"),
    APPROVED("2", "审批通过"),
    AUDIT_AILED("3", "审核失败"),
    APPROVAL_REVOKE("9", "审批撤回");

    private final String type;
    private final String name;

    CustomerGspAuditStatusEnum(String type, String name) {
        this.type = type;
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public String getName() {
        return name;
    }

    private static Map<String, CustomerGspAuditStatusEnum> maps = new HashMap<>();

    static {
        for (CustomerGspAuditStatusEnum item : CustomerGspAuditStatusEnum.values()) {
            maps.put(item.getType(), item);
        }
    }

    public static CustomerGspAuditStatusEnum getByType(final String status) {
        if (status == null) {
            return null;
        }
        return maps.get(status);
    }
}
