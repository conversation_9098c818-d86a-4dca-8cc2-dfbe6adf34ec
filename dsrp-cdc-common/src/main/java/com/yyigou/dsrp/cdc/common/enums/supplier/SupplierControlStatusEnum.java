package com.yyigou.dsrp.cdc.common.enums.supplier;

import java.util.HashMap;
import java.util.Map;

/**
 * 客商管控状态
 */
public enum SupplierControlStatusEnum {
    /**
     * 客商管控状态  管控状态 1：启用 2：停用 3：冻结
     */
    ENABLED("1", "启用"),
    DISABLED("2", "停用"),
    FREEZE("3", "冻结");

    private final String type;
    private final String name;

    SupplierControlStatusEnum(String type, String name) {
        this.type = type;
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public String getName() {
        return name;
    }

    private static Map<String, SupplierControlStatusEnum> maps = new HashMap<>();

    static {
        for (SupplierControlStatusEnum item : SupplierControlStatusEnum.values()) {
            maps.put(item.getType(), item);
        }
    }

    public static SupplierControlStatusEnum getByType(final String type) {
        if (type == null) {
            return null;
        }
        return maps.get(type);
    }
}
