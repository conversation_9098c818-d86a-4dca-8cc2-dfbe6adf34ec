package com.yyigou.dsrp.cdc.common.util;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OperationModel {
    private String enterpriseNo;
    private String enterpriseName;
    private String companyNo;
    private String companyName;

    @Deprecated
    protected String userNo;
    /**
     * 操作人用这个
     */
    private String employerNo;
    protected String userName;

    /**
     * 集团租户编号
     */
    private String groupTenantNo;

    private Map<String,Object> extInfo;

    /**
     * 是否集团子租户
     */
    private Boolean subTenant = false;

    /**
     * 租户类型
     */
    private Integer tenantType;
    /**
     * 组织编号
     */
    private String orgNo;
    /**
     * 组织编码
     */
    private String orgCode;
    /**
     * 组织名称
     */
    private String orgName;


    /**
     * 租户编号
     * 1. 单组织租户下就是enterpriseNo
     * 2. 多组织租户下是groupTenantNo
     */
    private String tenantNo;


    private Boolean singleOrgFlag;
}
