package com.yyigou.dsrp.cdc.common.enums;

import java.util.HashMap;
import java.util.Map;

public enum CompanyLogTypeEnum {
    COMPANY_ADD(1,"企业档案新增"),
    COMPANY_EDIT(2,"企业档案编辑"),
//    factory_add(3,"生产厂家档案新增"),
//    factory_edit(4,"生产厂家档案编辑"),
//    customer_add(5,"客户档案新增"),
//    customer_edit(6,"客户档案编辑"),
//    supplier_add(7,"供应商档案新增"),
//    supplier_edit(8,"供应商档案编辑"),
//    QUOTE_GROUP(9,"引用集团库新增"),
//    QUOTE_PRODUCT_CERT(10,"注册证新增"),
//    QUOTE_PRODUCT(11,"产品新增"),
    ;


    private final Integer type;
    private final String name;

    CompanyLogTypeEnum(Integer type, String name) {
        this.type = type;
        this.name = name;
    }

    public Integer getType() {
        return type;
    }

    public String getName() {
        return name;
    }

    private static Map<String, CompanyLogTypeEnum> mapsByname = new HashMap<>();

    private static Map<Integer, CompanyLogTypeEnum> maps = new HashMap<>();

    static {
        for (CompanyLogTypeEnum item : CompanyLogTypeEnum.values()) {
            mapsByname.put(item.getName(), item);
        }
    }
    static {
        for (CompanyLogTypeEnum item : CompanyLogTypeEnum.values()) {
            maps.put(item.getType(), item);
        }
    }

    public static CompanyLogTypeEnum getByType(final Integer type) {
        if (type == null) {
            return null;
        }
        return maps.get(type);
    }
    public static CompanyLogTypeEnum getByName(final String name) {
        if (name == null) {
            return null;
        }
        return mapsByname.get(name);
    }
}
