package com.yyigou.dsrp.cdc.common.enums.customer;

import java.util.HashMap;
import java.util.Map;

/**
 * 客商GSP的审核状态
 */
public enum CustomerYsSyncEnum {
    /**
     * 客商GSP的审核状态
     * 0:未同步 1:同步成功 2:同步失败
     */
    NOT_SYNCHRONIZED("0", "未同步"),
    SYNCHRONIZATION_SUCCESSFUL("1", "同步成功"),
    SYNCHRONIZATION_FAILED("2", "同步失败"),
    ;

    private final String type;
    private final String name;

    CustomerYsSyncEnum(String type, String name) {
        this.type = type;
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public String getName() {
        return name;
    }

    private static Map<String, CustomerYsSyncEnum> maps = new HashMap<>();

    static {
        for (CustomerYsSyncEnum item : CustomerYsSyncEnum.values()) {
            maps.put(item.getType(), item);
        }
    }

    public static CustomerYsSyncEnum getByType(final String status) {
        if (status == null) {
            return null;
        }
        return maps.get(status);
    }
}
