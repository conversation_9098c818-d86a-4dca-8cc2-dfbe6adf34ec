package com.yyigou.dsrp.cdc.common.util;

import org.springframework.util.ReflectionUtils;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ReflectUtil {

    /**
     * 根据泛型类的已经声明泛型类型的子类获取指定位置的泛型
     *
     * @param clazz -- 当前类
     * @param index -- 第几个泛型, 从0开始
     * @return
     */
    @SuppressWarnings("unchecked")
    public static <T> Class<T> getGenericClass(Class<T> clazz, Integer index) {
        if (clazz == null) {
            return null;
        }
        if (index == null) {
            index = 0;
        }

        //提取泛型类数组
        ParameterizedType parameterizedType = (ParameterizedType) clazz.getGenericSuperclass();
        Type[] genericTypes = parameterizedType.getActualTypeArguments();

        //越界判断
        if ((index + 1) > genericTypes.length) {
            return null;
        }
        Class<T> retClass = (Class<T>) genericTypes[index];
        return retClass;
    }

    /**
     * 获取类的所有成员
     *
     * @param cls
     * @return
     */
    public static Map<String, Field> getAllField(Class<?> cls) {
        Map<String, Field> fieldMap = new HashMap<String, Field>();
        List<Field> fieldList = new ArrayList<Field>();
        Field[] fields;
        Class<?> tcls = cls;
        while (tcls != null && !tcls.getName().equals(Object.class.getName())) {
            fields = tcls.getDeclaredFields();
            fieldList.addAll(Arrays.asList(fields));
            tcls = tcls.getSuperclass();
        }

        for (Field field : fieldList) {
            if (!fieldList.contains(field.getName()) && !field.getName().equals("serialVersionUID")) {
                fieldMap.put(field.getName(), field);
            }
        }

        return fieldMap;
    }

    /**
     * 找到成员
     *
     * @param theClass
     * @param fieldName
     * @return null if not found
     */
    public static Field findField(Class<?> theClass, String fieldName) {
        Field field = ReflectionUtils.findField(theClass, fieldName);
        if (field != null) {
            field.setAccessible(true);
        }

        return field;
    }

    /**
     * 找到成员的值
     *
     * @param field
     * @param target
     * @return
     */
    public static Object findFieldValue(Field field, Object target) {
        return ReflectionUtils.getField(field, target);
    }

    public static String[] commonFieldType = {
            "int", "long", "short", "double", "float", "char", "byte", "boolean",
            "class java.lang.String", "class java.lang.Integer", "class java.lang.Long",
            "class java.lang.Short", "class java.lang.Double", "class java.lang.Float",
            "class java.lang.Byte", "class java.lang.Boolean", "class java.math.BigDecimal"
    };

    public static Object dynamicSetValue(Object obj, String fieldName, Object value) {
        try {
            String firstLetter = fieldName.substring(0, 1).toUpperCase(); // 取属性首字母转大写
            String setMethodName = "set" + firstLetter + fieldName.substring(1); // set方法名
            Field field = obj.getClass().getDeclaredField(fieldName); // 获取属性
            Method setMethod = obj.getClass().getDeclaredMethod(setMethodName, field.getType()); // 获取set方法
            setMethod.invoke(obj, value); // 通过set方法动态赋值
        } catch (Exception e) {
            e.printStackTrace();
        }
        return obj;
    }

    public static Object dynamicGetValue(Object obj, String fieldName) {
        Object value = null;
        try {
            //if ( isCommonTypeField(obj, fieldName) ) { // 限普通类型
            String firstLetter = fieldName.substring(0, 1).toUpperCase(); // 取属性首字母转大写
            String getMethodName = "get" + firstLetter + fieldName.substring(1); // get方法名
            Method getMethod = obj.getClass().getDeclaredMethod(getMethodName); // 获取get方法
            value = getMethod.invoke(obj); // 动态取值
        } catch (Exception e) {
            e.printStackTrace();
        }
        return value;
    }

    public static boolean isCustomerTypeField(Object obj, String fieldName, String filedType) {
        boolean result = false;
        try {
            Field field = obj.getClass().getDeclaredField(fieldName);
            String type = field.getGenericType().toString();
            if (type.equals(filedType)) {
                return true;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    public static boolean isCommonTypeField(Object obj, String fieldName) {
        boolean result = false;
        try {
            Field field = obj.getClass().getDeclaredField(fieldName);
            String type = field.getGenericType().toString();
            for (int i = 0; i < commonFieldType.length; i++) {
                if (commonFieldType[i].equals(type)) {
                    result = true;
                    break;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

}

