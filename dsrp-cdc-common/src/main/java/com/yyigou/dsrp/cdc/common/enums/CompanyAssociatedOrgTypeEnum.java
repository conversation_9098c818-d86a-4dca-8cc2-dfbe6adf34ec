package com.yyigou.dsrp.cdc.common.enums;

import java.util.HashMap;
import java.util.Map;

public enum CompanyAssociatedOrgTypeEnum {

    SUPPLIER(1, "供应商"),
    CUSTOMER(2, "客户"),
    TAXPAYER_ORG(3, "纳税主体组织"),
    ;


    private final Integer value;
    private final String name;

    public Integer getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    CompanyAssociatedOrgTypeEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    private static Map<Integer, CompanyAssociatedOrgTypeEnum> MAP = new HashMap<>();

    static {
        for (CompanyAssociatedOrgTypeEnum item : CompanyAssociatedOrgTypeEnum.values()) {
            MAP.put(item.getValue(), item);
        }

    }

    public static CompanyAssociatedOrgTypeEnum getByValue(final Integer value) {
        if (value == null) {
            return null;
        }
        return MAP.get(value);
    }

    public static String getNameByValue(final Integer value) {
        if (value == null) {
            return null;
        }
        return MAP.get(value) == null ? null : MAP.get(value).getName();
    }

}
