package com.yyigou.dsrp.cdc.common.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * 通用状态枚举：0-无效，1-有效
 *
 * <AUTHOR>
 */
public enum CommonStatusEnum {

    INVALID(0, "无效"),
    EFFECTIVE(1, "有效"),
    ;


    private final Integer value;
    private final String name;

    public Integer getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    CommonStatusEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    private static Map<Integer, CommonStatusEnum> MAP = new HashMap<>();

    static {
        for (CommonStatusEnum item : CommonStatusEnum.values()) {
            MAP.put(item.getValue(), item);
        }

    }

    public static CommonStatusEnum getByValue(final Integer value) {
        if (value == null) {
            return null;
        }
        return MAP.get(value);
    }

    public static String getNameByValue(final Integer value) {
        if (value == null) {
            return null;
        }
        return MAP.get(value) == null ? null : MAP.get(value).getName();
    }
}
