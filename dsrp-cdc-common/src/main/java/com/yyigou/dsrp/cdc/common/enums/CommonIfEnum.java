package com.yyigou.dsrp.cdc.common.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * 通用是否枚举类
 *
 * @author: Moore
 * @date: 2022/10/17 14:04
 * @version: 1.0.0
 */
public enum CommonIfEnum {

    YES(1, "是"),

    NO(0, "否");


    private final Integer value;
    private final String name;

    public Integer getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    CommonIfEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    private static Map<Integer, CommonIfEnum> MAP = new HashMap<>();
    private static Map<String, CommonIfEnum> NAME_MAP = new HashMap<>();
    static {
        for (CommonIfEnum item : CommonIfEnum.values()) {
            MAP.put(item.getValue(), item);
            NAME_MAP.put(item.getName(), item);
        }

    }

    public static CommonIfEnum getByValue(final Integer value) {
        if (value == null) {
            return null;
        }
        return MAP.get(value);
    }
    public static CommonIfEnum getByName(final String name) {
        if (name == null) {
            return null;
        }
        return NAME_MAP.get(name);
    }
    public static String getNameByValue(final Integer value) {
        if (value == null) {
            return null;
        }
        return MAP.get(value) == null ? null : MAP.get(value).getName();
    }
}
