package com.yyigou.dsrp.cdc.common.util;

import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import org.springframework.cglib.beans.BeanCopier;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 对bean的工具类处理
 *
 * @author: fudj
 * @createTime: 2023-04-21  15:39
 * @version: 1.0
 */
public class BeanUtil {
    /**
     * 复制单个对象
     *
     * @param s      源对象
     * @param tClass 复制后的新对象的class
     */
    public static <S, T> T copyFieldsForJson(S s, Class<T> tClass) {
        String json = JSON.toJSONString(s);
        T t = JSONObject.parseObject(json, tClass);
        return t;
    }


    /**
     * 复制单个对象
     *
     * @param s      源对象
     * @param tClass 复制后的新对象的class
     */
    public static <S, T> T copyFields(S s, Class<T> tClass) {
        if (s == null) {
            return null;
        }
        BeanCopier beanCopier = BeanCopier.create(s.getClass(), tClass, false);
        T o = null;
        try {
            o = tClass.newInstance();
            beanCopier.copy(s, o, null);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return o;
    }

    /**
     * 复制单个对象,忽略空值
     *
     * @param s 源对象
     * @param t 复制后的新对象的t
     */
    public static void copyFieldsIgnoreNull(Object s, Object t) {
        cn.hutool.core.bean.BeanUtil.copyProperties(s, t, new CopyOptions().ignoreNullValue());
    }

    /**
     * 复制集合对象
     *
     * @param sList 源对象集合
     * @param t     复制后的新对象的class
     */
    public static <S, T> List<T> copyFieldsList(List<S> sList, Class<T> t) {
        List<T> resultList = Lists.newCopyOnWriteArrayList();
        if (CollectionUtils.isEmpty(sList)) {
            return resultList;
        }
        S s = sList.get(0);
        BeanCopier beanCopier = BeanCopier.create(s.getClass(), t, false);
        try {
            for (S member : sList) {
                T resultT = t.newInstance();
                beanCopier.copy(member, resultT, null);
                resultList.add(resultT);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return resultList;
    }

    /**
     * 复制集合对象
     *
     * @param sList 源对象集合
     * @param t     复制后的新对象的class
     */
    public static <S, T> List<T> copyFieldsListForJSON(List<S> sList, Class<T> t) {
        List<T> resultList = Lists.newCopyOnWriteArrayList();
        if (CollectionUtils.isEmpty(sList)) {
            return resultList;
        }
        return JSON.parseArray(JSON.toJSONString(sList), t);
    }

    /**
     * 去除实体中属性值空格及换行
     * 基于hu_tool工具类
     *
     * @param object 实体属性
     */
    public static void trimData(Object object) {
        // 获取实体中所有属性字段
        Field[] fields = ReflectUtil.getFields(object.getClass());
        for (Field field : fields) {
            // 获取属性字段类型
            String canonicalName = field.getType().getCanonicalName();
            // 如果字段是String类型,则去除此字段数据的空格
            if ("java.lang.String".equals(canonicalName)) {
                // 获取字段值
                String fieldValue = (String) ReflectUtil.getFieldValue(object, field);
                if (StrUtil.isNotBlank(fieldValue)) {
                    // 去掉换行
                    String fieldValueStr = fieldValue.replaceAll("\r|\n", "");
                    // 将去除空格后的数据 替换 原数据
                    ReflectUtil.setFieldValue(object, field, fieldValueStr.trim());
                }
            }
        }
    }

    //比较oldList和newList，根据id属性进行比较生成新增、修改、删除的集合
    public static <T, Q> Map<String, List<?>> diffCUD(List<T> oldList, List<Q> newList) {
        return diffCUD(oldList, newList, "id", "id", Long.class);
    }

    //比较oldList和newList，根据指定字段进行比较生成新增、修改、删除的集合
    public static <T, Q, V> Map<String, List<?>> diffCUD(List<T> oldList, List<Q> newList, String oldFieldName, String newFieldName, Class<V> fieldType) {
        Map<String, List<?>> result = new HashMap<>();
        List<Q> addList = new ArrayList<>();
        List<Q> updateList = new ArrayList<>();
        List<T> deleteList = new ArrayList<>();

        if (org.apache.commons.collections.CollectionUtils.isEmpty(oldList) && org.apache.commons.collections.CollectionUtils.isNotEmpty(newList)) {
            // 全部新增
            addList.addAll(newList);
        } else if (org.apache.commons.collections.CollectionUtils.isNotEmpty(oldList) && org.apache.commons.collections.CollectionUtils.isEmpty(newList)) {
            // 全部删除
            deleteList.addAll(oldList);
        } else if (org.apache.commons.collections.CollectionUtils.isNotEmpty(oldList) && org.apache.commons.collections.CollectionUtils.isNotEmpty(newList)) {
            // 获取比较字段
            Field oldField = null;
            Field newField = null;
            try {
                oldField = oldList.get(0).getClass().getDeclaredField(oldFieldName);
                oldField.setAccessible(true);
            } catch (NoSuchFieldException e) {
                throw new RuntimeException("实体类缺少" + oldFieldName + "字段");
            }
            try {
                newField = newList.get(0).getClass().getDeclaredField(newFieldName);
                newField.setAccessible(true);
            } catch (NoSuchFieldException e) {
                throw new RuntimeException("实体类缺少" + newFieldName + "字段");
            }

            // 构建oldMap
            Map<V, T> oldMap = new HashMap<>();
            for (T old : oldList) {
                try {
                    V value = (V) oldField.get(old);
                    oldMap.put(value, old);
                } catch (IllegalAccessException e) {
                    throw new RuntimeException(e);
                }
            }

            // 遍历newList进行比对
            for (Q newItem : newList) {
                try {
                    V value = (V) newField.get(newItem);
                    if (value == null) {
                        // 比较字段为空表示新增
                        addList.add(newItem);
                    } else {
                        // 比较字段存在表示更新,从oldMap移除
                        updateList.add(newItem);
                        oldMap.remove(value);
                    }
                } catch (IllegalAccessException e) {
                    throw new RuntimeException(e);
                }
            }

            // oldMap中剩余的为删除
            if (!oldMap.isEmpty()) {
                deleteList.addAll(oldMap.values());
            }
        }

        result.put("addList", addList);
        result.put("updateList", updateList);
        result.put("deleteList", deleteList);
        return result;
    }
}
