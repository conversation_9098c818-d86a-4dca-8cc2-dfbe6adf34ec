package com.yyigou.dsrp.cdc.common.enums;

import java.util.HashMap;
import java.util.Map;

public enum GspAuditStatusEnum {
    NON_FIRST_BUSINESS(0, "未首营"),
    HAS_FIRST_BUSINESS(1, "已首营");

    private final Integer type;
    private final String name;

    GspAuditStatusEnum(Integer type, String name) {
        this.type = type;
        this.name = name;
    }

    public Integer getType() {
        return type;
    }

    public String getName() {
        return name;
    }

    private static Map<Integer, GspAuditStatusEnum> maps = new HashMap<>();

    static {
        for (GspAuditStatusEnum item : GspAuditStatusEnum.values()) {
            maps.put(item.getType(), item);
        }
    }

    public static GspAuditStatusEnum getByType(final Integer status) {
        if (status == null) {
            return null;
        }
        return maps.get(status);
    }
}
