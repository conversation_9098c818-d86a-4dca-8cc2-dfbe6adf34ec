package com.yyigou.dsrp.cdc.common.util;


import com.alibaba.fastjson.JSON;
import com.yyigou.ddc.common.error.ErrorCode;
import com.yyigou.ddc.common.exception.BusinessException;
import com.yyigou.ddc.common.message.MessageVO;
import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.ddc.common.service.RequestHeader;
import com.yyigou.ddc.common.service.ServiceBaseAbstract;
import com.yyigou.ddc.common.service.SessionUser;
import com.yyigou.ddc.common.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.reflect.FieldUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class CommonUtil {

    /**
     * 填充创建人信息
     *
     * @param object
     */
    public static void fillCreatInfo(Object object) {
        SessionUser sessionUser = ServiceBaseAbstract.currentRequest().getSession();
        ReflectUtil.dynamicSetValue(object, "createTime", DateUtil.getCurrentDate());
        if (sessionUser != null) {
            ReflectUtil.dynamicSetValue(object, "createName", sessionUser.getUserName());
            ReflectUtil.dynamicSetValue(object, "createNo", sessionUser.getEmployerNo());
        }
    }

    /**
     * 填充创建人信息
     *
     * @param operationModel 登录信息
     * @param object         单据
     */
    public static void fillCreatInfo(OperationModel operationModel, Object object) {
        ReflectUtil.dynamicSetValue(object, "createTime", DateUtil.getCurrentDate());
        if (operationModel != null) {
            ReflectUtil.dynamicSetValue(object, "createName", operationModel.getUserName());
            ReflectUtil.dynamicSetValue(object, "createNo", operationModel.getEmployerNo());
        }
    }

    public static void fillCreatInfo(SessionUser session, Object object) {
        ReflectUtil.dynamicSetValue(object, "createTime", DateUtil.getCurrentDate());
        if (session != null) {
            ReflectUtil.dynamicSetValue(object, "createName", session.getUserName());
            ReflectUtil.dynamicSetValue(object, "createNo", session.getEmployerNo());
        }
    }

    /**
     * 填充修改人信息
     *
     * @param object
     */
    public static void fillOperateInfo(Object object) {
        SessionUser sessionUser = ServiceBaseAbstract.currentRequest().getSession();
        ReflectUtil.dynamicSetValue(object, "operateTime", DateUtil.getCurrentDate());
        ReflectUtil.dynamicSetValue(object, "operateName", sessionUser.getUserName());
        ReflectUtil.dynamicSetValue(object, "operateNo", sessionUser.getEmployerNo());
    }

    /**
     * 填充修改人信息
     *
     * @param operationModel 登录信息
     * @param object         单据
     */
    public static void fillOperateInfo(OperationModel operationModel, Object object) {
        ReflectUtil.dynamicSetValue(object, "operateTime", DateUtil.getCurrentDate());
        if (operationModel != null) {
            ReflectUtil.dynamicSetValue(object, "operateNo", operationModel.getEmployerNo());
            ReflectUtil.dynamicSetValue(object, "operateName", operationModel.getUserName());
        }
    }

    public static void fillOperateInfo(SessionUser session, Object object) {
        ReflectUtil.dynamicSetValue(object, "operateTime", DateUtil.getCurrentDate());
        ReflectUtil.dynamicSetValue(object, "operateName", session.getUserName());
        ReflectUtil.dynamicSetValue(object, "operateNo", session.getEmployerNo());
    }

    /**
     * 填充修改人信息
     *
     * @param object
     */
    public static void fillModifyInfo(Object object) {
        SessionUser operationModel = ServiceBaseAbstract.currentRequest().getSession();
        ReflectUtil.dynamicSetValue(object, "modifyTime", DateUtil.getCurrentDate());
        ReflectUtil.dynamicSetValue(object, "modifyName", operationModel.getUserName());
        ReflectUtil.dynamicSetValue(object, "modifyNo", operationModel.getEmployerNo());
    }

    /**
     * 填充修改人信息
     *
     * @param operationModel 登录信息
     * @param object         单据
     */
    public static void fillModifyInfo(OperationModel operationModel, Object object) {
        ReflectUtil.dynamicSetValue(object, "modifyTime", DateUtil.getCurrentDate());
        ReflectUtil.dynamicSetValue(object, "modifyName", operationModel.getUserName());
        ReflectUtil.dynamicSetValue(object, "modifyNo", operationModel.getEmployerNo());
    }


    /**
     * 填充修改人信息
     *
     * @param object
     */
    public static void fillUpdateInfo(Object object) {
        SessionUser sessionUser = ServiceBaseAbstract.currentRequest().getSession();
        ReflectUtil.dynamicSetValue(object, "updateTime", DateUtil.getCurrentDate());
        ReflectUtil.dynamicSetValue(object, "updateName", sessionUser.getUserName());
        ReflectUtil.dynamicSetValue(object, "updateNo", sessionUser.getEmployerNo());
    }

    /**
     * 填充修改人信息
     *
     * @param operationModel 登录信息
     * @param object
     */
    public static void fillUpdateInfo(OperationModel operationModel, Object object) {
        ReflectUtil.dynamicSetValue(object, "updateTime", DateUtil.getCurrentDate());
        ReflectUtil.dynamicSetValue(object, "updateName", operationModel.getUserName());
        ReflectUtil.dynamicSetValue(object, "updateNo", operationModel.getEmployerNo());
    }


    public static boolean checkEmpty(Object target) {
        if (target == null) {
            return true;
        }
        if (target instanceof String && StringUtils.isEmpty(String.valueOf(target))) {
            return true;
        }
        if (target instanceof Collection && CollectionUtils.isEmpty((Collection) target)) {
            return true;
        }
        if (target instanceof Map && ((Map<?, ?>) target).size() == 0) {
            return true;
        }
        return false;
    }

    public static void checkEmptyThrowEx(Object target, String errorMessage) {
        if (checkEmpty(target)) {
            throw new BusinessException(ErrorCode.object_null_code, errorMessage);
        }
    }

    public static void checkThrowEx(Boolean condition, String errorMessage) {
        if (condition) {
            throw new BusinessException(ErrorCode.object_null_code, errorMessage);
        }
    }


    public static void checkEmptyThrowCustEx(Object target, BusinessException e) {
        if ((target instanceof String && StringUtils.isEmpty(String.valueOf(target))) ||
                (target instanceof Collection && CollectionUtils.isEmpty((Collection) target)) ||
                null == target)
            throw e;
    }


    public static SessionUser createSessionUser() {
        SessionUser sessionUser = null;
        try {
            sessionUser = ServiceBaseAbstract.currentRequest().getSession();
        } catch (Exception e) {
            if (null == sessionUser) {
                sessionUser = new SessionUser();
                sessionUser.setEmployerNo("-1");
                sessionUser.setUserName("系统自动生成");
            }
        }


        return sessionUser;
    }

    /**
     * 线程穿透
     *
     * @param sessionUser
     */
    public static void threadPierce(SessionUser sessionUser) {
        if (sessionUser == null) {
            throw new RuntimeException("session 为空");
        }
        RequestHeader header = new RequestHeader();
        header.setSession(sessionUser);
        Field tsf = FieldUtils.getField(ServiceBaseAbstract.class, "threadSession", true);
        ThreadLocal<RequestHeader> threadSession = null;
        try {
            threadSession = (ThreadLocal<RequestHeader>) tsf.get(null);
        } catch (IllegalAccessException e) {
            log.error("session线程穿透异常", e);
        }
        threadSession.set(header);

    }


    public static boolean existEmpty(Object... params) {
        if (params == null) {
            return true;
        }

        for (Object o : params) {
            if (checkEmpty(o)) {
                return true;
            }
        }
        return false;
    }


    /**
     * 是否存在不为空
     *
     * @param params
     * @return
     */
    public static boolean existNotEmpty(Object... params) {
        if (params == null) {
            return false;
        }

        for (Object o : params) {
            if (!checkEmpty(o)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 将一组数据固定分组，每组n个元素
     *
     * @param source 要分组的数据源
     * @param n      每组n个元素
     * @param <T>
     * @return
     */
    public static <T> List<List<T>> fixedGrouping2(List<T> source, int n) {

        if (null == source || source.size() == 0 || n <= 0) {
            return null;
        }
        List<List<T>> result = new ArrayList<List<T>>();
        int remainder = source.size() % n;
        int size = (source.size() / n);
        for (int i = 0; i < size; i++) {
            List<T> subset = null;
            subset = source.subList(i * n, (i + 1) * n);
            result.add(subset);
        }
        if (remainder > 0) {
            List<T> subset = null;
            subset = source.subList(size * n, size * n + remainder);
            result.add(subset);
        }
        return result;
    }


    /**
     * @param date
     * @desc true 过期 false 未过期。 判断日期 日期格式不对，返回 false
     */
    public static boolean isExpire(String date) {
        boolean dateValid = DateUtil.dateValid(date, "yyyy-MM-dd");
        if (!dateValid) {
            return true;
        }
        String current = DateUtil.getCurrentDate("yyyy-MM-dd");
        int days = DateUtil.calculateDays2(current, date, "yyyy-MM-dd");
        if (days >= 0) {
            return false;
        }
        return true;
    }

    public static String getDays(String date) {
        boolean dateValid = DateUtil.dateValid(date, "yyyy-MM-dd");
        if (!dateValid) {
            return "未知";
        }
        String current = DateUtil.getCurrentDate("yyyy-MM-dd");
        int days = DateUtil.calculateDays2(date, current, "yyyy-MM-dd");
        return days + "";
    }

    public static <T> T parseResult(CallResult<T> result) {
        if (!result.getCode().equals(MessageVO.SUCCESS)) {
            log.error("调用接口异常：{}", JSON.toJSONString(result));
            throw new BusinessException(result.getCode(), result.getMessage());
        } else {
            return result.getData();
        }
    }
}
