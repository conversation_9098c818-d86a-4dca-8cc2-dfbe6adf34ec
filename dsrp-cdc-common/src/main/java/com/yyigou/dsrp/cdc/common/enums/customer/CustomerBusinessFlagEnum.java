package com.yyigou.dsrp.cdc.common.enums.customer;

import java.util.HashMap;
import java.util.Map;

/**
 * 客户业务状态：0:潜在 1:合格
 *
 * <AUTHOR>
 */
public enum CustomerBusinessFlagEnum {

    POTENTIAL(0, "潜在客户"),
    FORMAL(1, "正式客户"),
    DRAFT(2, "草稿"),
    ;


    private final Integer value;
    private final String name;

    public Integer getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    CustomerBusinessFlagEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    private static Map<Integer, CustomerBusinessFlagEnum> MAP = new HashMap<>();

    static {
        for (CustomerBusinessFlagEnum item : CustomerBusinessFlagEnum.values()) {
            MAP.put(item.getValue(), item);
        }

    }

    public static CustomerBusinessFlagEnum getByValue(final Integer value) {
        if (value == null) {
            return null;
        }
        return MAP.get(value);
    }

    public static String getNameByValue(final Integer value) {
        if (value == null) {
            return null;
        }
        return MAP.get(value) == null ? null : MAP.get(value).getName();
    }
}
