package com.yyigou.dsrp.cdc.common.enums.company;

import java.util.HashMap;
import java.util.Map;

/**
 * 机构类型
 * 0:无等级,1:一级甲等,2:一级乙等,3:一级丙等,4:二级甲等,5:二级乙等,6:二级丙等,7:三级特等,8:三级甲等,9:三级乙等,10:三级丙等
 * <AUTHOR>
 *
 */
public enum HospitalClassEnum {
	a(0,"无等级"),
	b(1,"一级甲等"),
	c(2,"一级乙等"),
	d(3,"一级丙等"),
	e(4,"二级甲等"),
	f(5,"二级乙等"),
	g(6,"二级丙等"),
	h(7,"三级特等"),
	i(8,"三级甲等"),
	j(9,"三级乙等"),
	k(10,"三级丙等");
    private final Integer type;
    private final String name;

    HospitalClassEnum(Integer type, String name) {
        this.type = type;
        this.name = name;
    }

    public Integer getType() {
        return type;
    }

    public String getName() {
        return name;
    }

    private static Map<String, HospitalClassEnum> mapsByname = new HashMap<>();

    private static Map<Integer, HospitalClassEnum> maps = new HashMap<>();

    static {
        for (HospitalClassEnum item : HospitalClassEnum.values()) {
            mapsByname.put(item.getName(), item);
        }
    }
    static {
        for (HospitalClassEnum item : HospitalClassEnum.values()) {
            maps.put(item.getType(), item);
        }
    }

    public static HospitalClassEnum getByType(final Integer type) {
        if (type == null) {
            return null;
        }
        return maps.get(type);
    }
    public static HospitalClassEnum getByName(final String name) {
        if (name == null) {
            return null;
        }
        return mapsByname.get(name);
    }
}
