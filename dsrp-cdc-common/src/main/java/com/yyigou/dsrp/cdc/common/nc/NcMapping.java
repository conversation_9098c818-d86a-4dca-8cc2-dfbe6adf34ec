package com.yyigou.dsrp.cdc.common.nc;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.yyigou.ddc.common.zkmonitor.EnableZkMonitor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.locks.ReentrantReadWriteLock;

@Component
@Slf4j
public class NcMapping {
    /**
     * ZK配置热更新
     */
    @EnableZkMonitor(zkProperty = "ncCustomerEnterpriseNoSet", zkNode = "/ddc-config/common")
    private volatile String ncCustomerEnterpriseNoSet;

    private ReentrantReadWriteLock rwLock = new ReentrantReadWriteLock();
    private ReentrantReadWriteLock.ReadLock rLock = rwLock.readLock();
    private ReentrantReadWriteLock.WriteLock wLock = rwLock.writeLock();
    private Map<String, Set<String>> ncCustomerEnterpriseNoSetMap = new HashMap<>();

    /**
     * 热部署, 需要自行实现, zk监控客户端会在数据变更时优先调用set方法进行回调, 可在这里实现热部署
     *
     * @param ncCustomerEnterpriseNoSet
     */
    public void setNcCustomerEnterpriseNoSet(String ncCustomerEnterpriseNoSet) {
        if (StringUtils.isEmpty(ncCustomerEnterpriseNoSet)) {
            ncCustomerEnterpriseNoSet = "{}";
        }

        try {
            wLock.lock();

            Map<String, Set<String>> tmpMap = JSON.parseObject(ncCustomerEnterpriseNoSet, new TypeReference<HashMap<String, Set<String>>>() {
            });

            ncCustomerEnterpriseNoSetMap.clear();
            ncCustomerEnterpriseNoSetMap.putAll(tmpMap);

            log.warn("nc租户更新成功,当前灰度租户列表:{}", ncCustomerEnterpriseNoSetMap);

            Set<String> klpEnterpriseNoSet = ncCustomerEnterpriseNoSetMap.get("klp");
            initMapping(klpEnterpriseNoSet);
        } catch (Exception e) {
            log.error("nc租户更新异常", e);
            log.error("nc租户更新异常,入参:{}", ncCustomerEnterpriseNoSet);
        } finally {
            wLock.unlock();
        }
    }

    private Map<String, Map<Integer, Integer>> ncCustomerHospitalClassMap;
    private Map<String, Map<String, String>> ncCustomerHospitalTypeMap;
    private Map<String, Map<String, String>> ncAreaMap;

    public Map<String, Map<Integer, Integer>> getNcCustomerHospitalClassMap() {
        try {
            rLock.lock();

            return ncCustomerHospitalClassMap;
        } finally {
            rLock.unlock();
        }
    }

    public Map<String, Map<String, String>> getNcCustomerHospitalTypeMap() {
        try {
            rLock.lock();

            return ncCustomerHospitalTypeMap;
        } finally {
            rLock.unlock();
        }
    }

    public Map<String, Map<String, String>> getNcAreaMap() {
        try {
            rLock.lock();

            return ncAreaMap;
        } finally {
            rLock.unlock();
        }
    }

    private void initMapping(Set<String> enterpriseNoSet) {
        if (CollectionUtils.isEmpty(enterpriseNoSet)) {
            return;
        }

        for (String enterpriseNo : enterpriseNoSet) {
            ncCustomerHospitalClassMap:
            {
            /*
                11	一级甲等
                12	一级乙等
                13	一级丙等
                21	二级甲等
                22	二级乙等
                23	二级丙等
                30	三级特等
                31	三级甲等
                32	三级乙等
                33	三级丙等
                4	未定级
                5	无等级
            */
                Map<String, Map<Integer, Integer>> innerNcCustomerHospitalClassMap = new HashMap<>();
                Map<Integer, Integer> mapping = new HashMap<>();
                mapping.put(0, 5);
                mapping.put(1, 11);
                mapping.put(2, 12);
                mapping.put(3, 13);
                mapping.put(4, 21);
                mapping.put(5, 22);
                mapping.put(6, 23);
                mapping.put(7, 30);
                mapping.put(8, 31);
                mapping.put(9, 32);
                mapping.put(10, 33);
                mapping.put(null, 4);
                innerNcCustomerHospitalClassMap.put(enterpriseNo, mapping);

                ncCustomerHospitalClassMap = Collections.unmodifiableMap(innerNcCustomerHospitalClassMap);
            }

            ncCustomerHospitalTypeMap:
            {
            /*
                1	公立
                2	私营
             */
                Map<String, Map<String, String>> innerNcCustomerHospitalTypeMap = new HashMap<>();
                Map<String, String> mapping = new HashMap<>();
                mapping.put("yy", "1");
                mapping.put("mbyy", "2");
                innerNcCustomerHospitalTypeMap.put(enterpriseNo, mapping);

                ncCustomerHospitalTypeMap = Collections.unmodifiableMap(innerNcCustomerHospitalTypeMap);
            }

            ncAreaMap:
            {
                /*
                110000	北京市
                120000	天津市
                130000	河北省
                140000	山西省
                150000	内蒙古自治区
                210000	辽宁省
                220000	吉林省
                230000	黑龙江省
                310000	上海市
                320000	江苏省
                330000	浙江省
                340000	安徽省
                350000	福建省
                360000	江西省
                370000	山东省
                410000	河南省
                420000	湖北省
                430000	湖南省
                440000	广东省
                450000	广西壮族自治区
                460000	海南省
                500000	重庆市
                510000	四川省
                520000	贵州省
                530000	云南省
                540000	西藏自治区
                610000	陕西省
                620000	甘肃省
                630000	青海省
                640000	宁夏回族自治区
                650000	新疆维吾尔自治区
                710000	台湾省
                810000	香港特别行政区
                820000	澳门特别行政区
                 */
                Map<String, Map<String, String>> innerNcAreaMap = new HashMap<>();
                Map<String, String> mapping = new HashMap<>();
                mapping.put("110000", "110000");
                innerNcAreaMap.put(enterpriseNo, mapping);

                ncAreaMap = Collections.unmodifiableMap(innerNcAreaMap);
            }
        }
    }
}

