package com.yyigou.dsrp.cdc.common.enums.supplier;

import java.util.HashMap;
import java.util.Map;

/**
 * 供应商业务状态：0:潜在 1:合格
 *
 * <AUTHOR>
 */
public enum SupplierBusinessFlagEnum {

    @Deprecated
    POTENTIAL(0, "潜在供应商"),
    FORMAL(1, "正式供应商"),
    DRAFT(2, "草稿"),
    ;


    private final Integer value;
    private final String name;

    public Integer getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    SupplierBusinessFlagEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    private static Map<Integer, SupplierBusinessFlagEnum> MAP = new HashMap<>();

    static {
        for (SupplierBusinessFlagEnum item : SupplierBusinessFlagEnum.values()) {
            MAP.put(item.getValue(), item);
        }

    }

    public static SupplierBusinessFlagEnum getByValue(final Integer value) {
        if (value == null) {
            return null;
        }
        return MAP.get(value);
    }

    public static String getNameByValue(final Integer value) {
        if (value == null) {
            return null;
        }
        return MAP.get(value) == null ? null : MAP.get(value).getName();
    }
}
