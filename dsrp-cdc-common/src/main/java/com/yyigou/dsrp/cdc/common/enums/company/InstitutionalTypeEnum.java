package com.yyigou.dsrp.cdc.common.enums.company;

import java.util.HashMap;
import java.util.Map;

/**
 * 机构类型
 * yy:医院,dsfsys:第三方实验室,dsftjzx:第三方体检中心,jkzx:疾控中心,xz:血站,zs:诊所,ylhly:养老护理院,jgqt:其他
 * <AUTHOR>
 *
 */
public enum InstitutionalTypeEnum {
	yy("yy","医院"),
	dsfsys("dsfsys","第三方实验室"),
    yxsys("yxsys","医学实验室"),
	dsftjzx("dsftjzx","第三方体检中心"),
	jkzx("jkzx","疾控中心"),
	xz("xz","血站"),
	zs("zs","诊所"),
	ylhly("ylhly","养老护理院"),
	jgqt("jgqt","其他");
    private final String type;
    private final String name;

    InstitutionalTypeEnum(String type, String name) {
        this.type = type;
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public String getName() {
        return name;
    }

    private static Map<String, InstitutionalTypeEnum> mapsByname = new HashMap<>();

    private static Map<String, InstitutionalTypeEnum> maps = new HashMap<>();

    static {
        for (InstitutionalTypeEnum item : InstitutionalTypeEnum.values()) {
            mapsByname.put(item.getName(), item);
        }
    }
    static {
        for (InstitutionalTypeEnum item : InstitutionalTypeEnum.values()) {
            maps.put(item.getType(), item);
        }
    }

    public static InstitutionalTypeEnum getByType(final String type) {
        if (type == null) {
            return null;
        }
        return maps.get(type);
    }
    public static InstitutionalTypeEnum getByName(final String name) {
        if (name == null) {
            return null;
        }
        return mapsByname.get(name);
    }
}
