package com.yyigou.dsrp.cdc.common.enums.company;

import java.util.HashMap;
import java.util.Map;

/**
 * 企业合作关系枚举
 */
public enum CompanyPartnershipEnum {

    CUSTOMER("customer", "客户"),
    SUPPLIER("supplier", "供应商"),
    REGISTRAR("registrar", "注册人"),
    INTERNALORG("internalorg", "内部组织"),
    FACTORY("factory", "生产厂家");

    private final String type;
    private final String name;

    CompanyPartnershipEnum(String type, String name) {
        this.type = type;
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public String getName() {
        return name;
    }

    private static Map<String, CompanyPartnershipEnum> maps = new HashMap<>();
    private static Map<String, CompanyPartnershipEnum> typeMaps = new HashMap<>();

    static {
        for (CompanyPartnershipEnum item : CompanyPartnershipEnum.values()) {
            maps.put(item.getType(), item);
            typeMaps.put(item.getName(), item);
        }
    }

    public static CompanyPartnershipEnum getByType(final String status) {
        if (status == null) {
            return null;
        }
        return maps.get(status);
    }

    public static CompanyPartnershipEnum getByName(final String name) {
        if (name == null) {
            return null;
        }
        return typeMaps.get(name);
    }
}
