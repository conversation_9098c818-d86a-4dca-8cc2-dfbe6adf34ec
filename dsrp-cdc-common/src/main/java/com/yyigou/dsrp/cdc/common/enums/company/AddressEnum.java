package com.yyigou.dsrp.cdc.common.enums.company;

import java.util.HashMap;
import java.util.Map;

/**
 * 地址
 */
public enum AddressEnum {
    LINK("1", "联系地址"),
    RECEIVE_INVOICE("2", "收票地址"),
    STORAGE("3", "仓库地址"),
    RECEIVE_GOODS("4", "收货地址");

    private final String type;
    private final String name;

    AddressEnum(String type, String name) {
        this.type = type;
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public String getName() {
        return name;
    }

    private static Map<String, AddressEnum> maps = new HashMap<>();

    static {
        for (AddressEnum item : AddressEnum.values()) {
            maps.put(item.getType(), item);
        }
    }

    public static AddressEnum getByType(final String type) {
        if (type == null) {
            return null;
        }
        return maps.get(type);
    }
}
