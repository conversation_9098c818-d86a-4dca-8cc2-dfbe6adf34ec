package com.yyigou.dsrp.cdc.common.enums.supplier;

import java.util.HashMap;
import java.util.Map;

/**
 * 供应商档案申请类型：2-新增申请 3-变更申请
 */
public enum SupplierApplyTypeEnum {
    ADD_APPLY(2, "新增申请"),
    MODIFY_APPLY(3, "变更申请");


    private final Integer value;
    private final String name;

    public Integer getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    SupplierApplyTypeEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    private static Map<Integer, SupplierApplyTypeEnum> MAP = new HashMap<>();

    static {
        for (SupplierApplyTypeEnum item : SupplierApplyTypeEnum.values()) {
            MAP.put(item.getValue(), item);
        }

    }

    public static SupplierApplyTypeEnum getByValue(final Integer value) {
        if (value == null) {
            return null;
        }
        return MAP.get(value);
    }

    public static String getNameByValue(final Integer value) {
        if (value == null) {
            return null;
        }
        return MAP.get(value) == null ? null : MAP.get(value).getName();
    }
}
