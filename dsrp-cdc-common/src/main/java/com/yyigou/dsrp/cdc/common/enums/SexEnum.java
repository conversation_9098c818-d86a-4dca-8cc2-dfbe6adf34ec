package com.yyigou.dsrp.cdc.common.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * 性别
 */
public enum SexEnum {
    MALE("male", "男"),
    SECRECY("secrecy", "保密"),
    FEMALE("female", "女");

    private final String type;
    private final String name;

    SexEnum(String type, String name) {
        this.type = type;
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public String getName() {
        return name;
    }

    private static Map<String, SexEnum> maps = new HashMap<>();

    static {
        for (SexEnum item : SexEnum.values()) {
            maps.put(item.getType(), item);
        }
    }

    public static SexEnum getByType(final String type) {
        if (type == null) {
            return null;
        }
        return maps.get(type);
    }
}
