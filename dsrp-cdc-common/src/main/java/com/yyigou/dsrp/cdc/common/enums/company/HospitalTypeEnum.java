package com.yyigou.dsrp.cdc.common.enums.company;

import java.util.HashMap;
import java.util.Map;

/**
 * 医院类型
 * <AUTHOR>
 *
 */
public enum HospitalTypeEnum {
	yy("yy","公立"),
	mbyy("mbyy","民营");
    private final String type;
    private final String name;

    HospitalTypeEnum(String type, String name) {
        this.type = type;
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public String getName() {
        return name;
    }

    private static Map<String, HospitalTypeEnum> mapsByname = new HashMap<>();

    private static Map<String, HospitalTypeEnum> maps = new HashMap<>();

    static {
        for (HospitalTypeEnum item : HospitalTypeEnum.values()) {
            mapsByname.put(item.getName(), item);
        }
    }
    static {
        for (HospitalTypeEnum item : HospitalTypeEnum.values()) {
            maps.put(item.getType(), item);
        }
    }

    public static HospitalTypeEnum getByType(final String type) {
        if (type == null) {
            return null;
        }
        return maps.get(type);
    }
    public static HospitalTypeEnum getByName(final String name) {
        if (name == null) {
            return null;
        }
        return mapsByname.get(name);
    }
}
