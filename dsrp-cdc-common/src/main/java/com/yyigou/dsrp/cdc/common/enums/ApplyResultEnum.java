package com.yyigou.dsrp.cdc.common.enums;

import java.util.HashMap;
import java.util.Map;

public enum ApplyResultEnum {

    SUCC(1, "成功"),

    FAIL(2, "失败");


    private final Integer value;
    private final String name;

    public Integer getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    ApplyResultEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    private static Map<Integer, ApplyResultEnum> MAP = new HashMap<>();
    private static Map<String, ApplyResultEnum> NAME_MAP = new HashMap<>();
    static {
        for (ApplyResultEnum item : ApplyResultEnum.values()) {
            MAP.put(item.getValue(), item);
            NAME_MAP.put(item.getName(), item);
        }

    }

    public static ApplyResultEnum getByValue(final Integer value) {
        if (value == null) {
            return null;
        }
        return MAP.get(value);
    }
    public static ApplyResultEnum getByName(final String name) {
        if (name == null) {
            return null;
        }
        return NAME_MAP.get(name);
    }
    public static String getNameByValue(final Integer value) {
        if (value == null) {
            return null;
        }
        return MAP.get(value) == null ? null : MAP.get(value).getName();
    }
}
