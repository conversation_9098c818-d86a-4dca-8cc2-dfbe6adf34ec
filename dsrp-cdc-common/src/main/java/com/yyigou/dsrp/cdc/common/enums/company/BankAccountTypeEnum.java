package com.yyigou.dsrp.cdc.common.enums.company;

import java.util.HashMap;
import java.util.Map;

/**
 * 企业银行账号类型
 */
public enum BankAccountTypeEnum {
    ENTERPRISE(1, "公司"),
    INDIVIDUAL(2, "个人");


    private final Integer value;
    private final String name;

    public Integer getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    BankAccountTypeEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    private static Map<Integer, BankAccountTypeEnum> MAP = new HashMap<>();

    static {
        for (BankAccountTypeEnum item : BankAccountTypeEnum.values()) {
            MAP.put(item.getValue(), item);
        }

    }

    public static BankAccountTypeEnum getByValue(final Integer value) {
        if (value == null) {
            return null;
        }
        return MAP.get(value);
    }

    public static String getNameByValue(final Integer value) {
        if (value == null) {
            return null;
        }
        return MAP.get(value) == null ? null : MAP.get(value).getName();
    }
}
