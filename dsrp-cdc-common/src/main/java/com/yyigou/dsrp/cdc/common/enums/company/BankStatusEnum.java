package com.yyigou.dsrp.cdc.common.enums.company;

import java.util.HashMap;
import java.util.Map;

public enum BankStatusEnum {

    INVALID(0, "停用"),
    EFFECTIVE(1, "启用");


    private final Integer value;
    private final String name;

    public Integer getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    BankStatusEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    private static Map<Integer, BankStatusEnum> MAP = new HashMap<>();

    static {
        for (BankStatusEnum item : BankStatusEnum.values()) {
            MAP.put(item.getValue(), item);
        }

    }

    public static BankStatusEnum getByValue(final Integer value) {
        if (value == null) {
            return null;
        }
        return MAP.get(value);
    }

    public static String getNameByValue(final Integer value) {
        if (value == null) {
            return null;
        }
        return MAP.get(value) == null ? null : MAP.get(value).getName();
    }
}
