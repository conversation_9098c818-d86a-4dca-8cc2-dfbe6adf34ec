package com.yyigou.dsrp.cdc.common.enums.supplier;

import java.util.HashMap;
import java.util.Map;

/**
 * 供应商档案申请审核状态：0-草稿 1-待审核 2-审核通过 3-审核拒绝
 */
public enum SupplierApplyAuditStatusEnum {
    DRAFT(0, "草稿"),
    TO_BE_AUDITED(1, "待审核"),
    APPROVED(2, "审核通过"),
    REJECTED(3, "审核拒绝");


    private final Integer value;
    private final String name;

    public Integer getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    SupplierApplyAuditStatusEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    private static Map<Integer, SupplierApplyAuditStatusEnum> MAP = new HashMap<>();

    static {
        for (SupplierApplyAuditStatusEnum item : SupplierApplyAuditStatusEnum.values()) {
            MAP.put(item.getValue(), item);
        }

    }

    public static SupplierApplyAuditStatusEnum getByValue(final Integer value) {
        if (value == null) {
            return null;
        }
        return MAP.get(value);
    }

    public static String getNameByValue(final Integer value) {
        if (value == null) {
            return null;
        }
        return MAP.get(value) == null ? null : MAP.get(value).getName();
    }
}
