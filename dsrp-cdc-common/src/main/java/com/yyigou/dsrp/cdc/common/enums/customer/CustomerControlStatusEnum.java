package com.yyigou.dsrp.cdc.common.enums.customer;

import java.util.HashMap;
import java.util.Map;

/**
 * 客商管控状态
 */
public enum CustomerControlStatusEnum {
    /**
     * 客商管控状态  管控状态 1：启用 2：停用 3：冻结
     */
    ENABLED("1", "启用"),
    DISABLED("2", "停用"),
    FREEZE("3", "冻结");

    private final String type;
    private final String name;

    CustomerControlStatusEnum(String type, String name) {
        this.type = type;
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public String getName() {
        return name;
    }

    private static Map<String, CustomerControlStatusEnum> maps = new HashMap<>();

    static {
        for (CustomerControlStatusEnum item : CustomerControlStatusEnum.values()) {
            maps.put(item.getType(), item);
        }
    }

    public static CustomerControlStatusEnum getByType(final String type) {
        if (type == null) {
            return null;
        }
        return maps.get(type);
    }
}
