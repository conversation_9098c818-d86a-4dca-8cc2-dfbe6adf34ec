package com.yyigou.dsrp.cdc.common.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * 分类列表查询枚举
 *
 * <AUTHOR> @date
 */
public enum HierarchyQueryEnum {
    ALL_DESCENDANTS(0, "展示全部"),

    SUB_LEVEL(1, "仅展示下级");


    private final Integer value;
    private final String name;

    public Integer getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    HierarchyQueryEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    private static Map<Integer, HierarchyQueryEnum> map = new HashMap<>();

    static {
        for (HierarchyQueryEnum item : HierarchyQueryEnum.values()) {
            map.put(item.getValue(), item);
        }

    }

    public static HierarchyQueryEnum getByValue(final Integer value) {
        if (value == null) {
            return null;
        }
        return map.get(value);
    }
}
