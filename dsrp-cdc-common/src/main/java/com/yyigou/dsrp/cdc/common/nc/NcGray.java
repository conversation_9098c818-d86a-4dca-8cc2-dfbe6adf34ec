package com.yyigou.dsrp.cdc.common.nc;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.yyigou.ddc.common.zkmonitor.EnableZkMonitor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.locks.ReentrantReadWriteLock;

@Component
@Slf4j
public class NcGray {
    /**
     * ZK配置热更新
     */
    @EnableZkMonitor(zkProperty = "ncCustomerEnterpriseNoSet", zkNode = "/ddc-config/common")
    private volatile String ncCustomerEnterpriseNoSet;

    private ReentrantReadWriteLock rwLock = new ReentrantReadWriteLock();
    private ReentrantReadWriteLock.ReadLock rLock = rwLock.readLock();
    private ReentrantReadWriteLock.WriteLock wLock = rwLock.writeLock();
    private Map<String, Set<String>> ncCustomerEnterpriseNoSetMap = new HashMap<>();

    /**
     * 热部署, 需要自行实现, zk监控客户端会在数据变更时优先调用set方法进行回调, 可在这里实现热部署
     *
     * @param ncCustomerEnterpriseNoSet
     */
    public void setNcCustomerEnterpriseNoSet(String ncCustomerEnterpriseNoSet) {
        if (StringUtils.isEmpty(ncCustomerEnterpriseNoSet)) {
            ncCustomerEnterpriseNoSet = "{}";
        }

        try {
            wLock.lock();

            Map<String, Set<String>> tmpMap = JSON.parseObject(ncCustomerEnterpriseNoSet, new TypeReference<HashMap<String, Set<String>>>() {
            });

            ncCustomerEnterpriseNoSetMap.clear();
            ncCustomerEnterpriseNoSetMap.putAll(tmpMap);

            log.warn("nc租户更新成功,当前灰度租户列表:{}", ncCustomerEnterpriseNoSetMap);
        } catch (Exception e) {
            log.error("nc租户更新异常", e);
            log.error("nc租户更新异常,入参:{}", ncCustomerEnterpriseNoSet);
        } finally {
            wLock.unlock();
        }
    }

    public Set<String> grayEnterpriseNoSet(String enterpriseName) {
        try {
            rLock.lock();

            return ncCustomerEnterpriseNoSetMap.get(enterpriseName);
        } finally {
            rLock.unlock();
        }
    }

    /**
     * 判断租户是否在灰度范围内
     *
     * @param enterpriseName
     * @param enterpriseNo
     * @return
     */
    public boolean isEnterpriseGray(String enterpriseName, String enterpriseNo) {
        try {
            rLock.lock();

            boolean nameExists = ncCustomerEnterpriseNoSetMap.containsKey(enterpriseName);
            if (!nameExists) {
                return false;
            }

            Set<String> enterpriseNoSet = ncCustomerEnterpriseNoSetMap.get(enterpriseName);

            return enterpriseNoSet.contains(enterpriseNo);
        } finally {
            rLock.unlock();
        }
    }


    /**
     * 判断租户是否在灰度范围内
     *
     * @param enterpriseNo
     * @return
     */
    public boolean isEnterpriseGray(String enterpriseNo) {
        try {
            rLock.lock();

            return ncCustomerEnterpriseNoSetMap.values().stream()
                    .anyMatch(set -> set.contains(enterpriseNo));
        } finally {
            rLock.unlock();
        }
    }
}

