package com.yyigou.dsrp.cdc.common.enums.customer;

import java.util.HashMap;
import java.util.Map;

public enum CustomerScsSyncEnum {
    /**
     * 客商GSP的审核状态
     * 0:未同步 1:同步成功 2:同步失败
     */
    NOT_SYNCHRONIZED(0, "未同步"),

    SYNCHRONIZATION_SUCCESSFUL(1, "同步成功"),

    SYNCHRONIZATION_FAILED(2, "同步失败"),
    COLLABORATED(3, "协同"),
    ;

    private final Integer type;
    private final String name;

    CustomerScsSyncEnum(Integer type, String name) {
        this.type = type;
        this.name = name;
    }

    public Integer getType() {
        return type;
    }

    public String getName() {
        return name;
    }

    private static Map<Integer, CustomerScsSyncEnum> maps = new HashMap<>();

    static {
        for (CustomerScsSyncEnum item : CustomerScsSyncEnum.values()) {
            maps.put(item.getType(), item);
        }
    }

    public static CustomerScsSyncEnum getByType(final Integer type) {
        if (type == null) {
            return null;
        }
        return maps.get(type);
    }
}
