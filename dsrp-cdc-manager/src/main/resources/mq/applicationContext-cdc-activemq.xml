<?xml version="1.0" encoding="UTF-8"?>

<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
    http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd">

    <!--消息队列-->
    <context:component-scan base-package="com.yyigou.ddc.services.mq"/>

    <bean id="jmsConnectionFactory" class="org.apache.activemq.ActiveMQConnectionFactory">
        <!--connection 相关属性-->
        <property name="brokerURL" value="${common.mq.brokersURL}"/>
        <!--<property name="userName" value="admin"/>-->
        <!--<property name="password" value="password"/>-->
        <!--因spring jms prefetch导致带宽过高问题，特关闭prefetch功能-->
        <property name="prefetchPolicy">
            <bean class="org.apache.activemq.ActiveMQPrefetchPolicy">
                <property name="queuePrefetch" value="1"/>
                <property name="topicPrefetch" value="1"/>
            </bean>
        </property>
    </bean>
    <bean id="pooledConnectionFactory" class="org.apache.activemq.pool.PooledConnectionFactory" init-method="start"
          destroy-method="stop">
        <property name="connectionFactory" ref="jmsConnectionFactory"/>
        <!--pool相关参数-->
        <property name="maxConnections" value="${common.mq.maxConnections}"/>
        <property name="maximumActiveSessionPerConnection" value="${common.mq.maximumActiveSessionPerConnection}"/>
    </bean>


</beans>
