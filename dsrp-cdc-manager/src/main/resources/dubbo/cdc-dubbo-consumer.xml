<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd http://code.alibabatech.com/schema/dubbo http://code.alibabatech.com/schema/dubbo/dubbo.xsd">

    <!-- 以下是dubbo引用的接口 -->
    <!--    <dubbo:reference id="orderAPI" interface="com.yyigou.ddc.services.ddc.oms.api.OrderAPI"/>-->

    <!-- 导出接口定义 -->
    <dubbo:reference id="importTaskAPI" interface="com.yyigou.ddc.services.ddc.task.api.ImportTaskAPI" check="false"/>
    <dubbo:reference id="importTemplateAPI" interface="com.yyigou.ddc.services.ddc.task.api.ImportTemplateAPI"
                     check="false"/>
    <!-- excel操作接口定义 -->
    <dubbo:reference id="taskExcelAPI" interface="com.yyigou.ddc.services.ddc.task.api.TaskExcelAPI" check="false"/>
    <!-- 导出接口定义 -->
    <dubbo:reference id="exportTaskAPI" interface="com.yyigou.ddc.services.ddc.task.api.ExportTaskAPI" check="false"
                     lazy="true"/>
    <dubbo:reference id="dLogAPI" interface="com.yyigou.ddc.services.dlog.api.DLogAPI" check="false"/>
    <dubbo:reference id="fastDFSAPI" interface="com.yyigou.ddc.services.dfs.api.FastDFSAPI" protocol="hessian"
                     check="false"/>
    <dubbo:reference id="fileUploadTaskAPI" interface="com.yyigou.ddc.services.ddc.task.api.FileUploadTaskAPI"
                     protocol="dubbo" check="false"/>

    <!--编码中心 平台编码规则算号器 -->
    <dubbo:reference id="numberAPI" interface="com.yyigou.ddc.service.number.center.api.NumberAPI" check="false"/>

    <!--编码中心 单据编码规则算号器 -->
    <dubbo:reference id="numberComputerAPI" interface="com.yyigou.ddc.service.number.center.api.NumberComputerAPI" check="false"/>

    <!-- uap查询方案dubbo客户端 -->
    <dubbo:reference id="billViewQueryPlanClient"
                     interface="com.yyigou.ddc.services.ddc.uap.api.BillViewQueryPlanClient" check="false"/>
    <!-- uap数据权限dubbo客户端 -->
    <dubbo:reference id="authenticationClient" interface="com.yyigou.ddc.services.ddc.uap.api.AuthenticationClient"
                     check="false"/>
    <!-- uap业务流dubbo客户端 -->
    <dubbo:reference id="businessFlowClient" interface="com.yyigou.ddc.services.ddc.uap.api.BusinessFlowClient"
                     check="false"/>
    <!-- uap状态机客户端-->
    <dubbo:reference id="stateMachineClient" interface="com.yyigou.ddc.services.ddc.uap.api.StateMachineClient"
                     check="false"/>
    <dubbo:reference id="supplierMappingAPI" interface="com.yyigou.ddc.services.openlink.api.SupplierMappingAPI"
                     check="false" protocol="dubbo"/>
    <dubbo:reference id="enterpriseAPI" interface="com.yyigou.ddc.services.ddc.ecs.api.EnterpriseAPI" check="false"
                     protocol="dubbo"/>
    <dubbo:reference id="ddcCustomerAPI" interface="com.yyigou.ddc.services.ddc.psr.api.CustomerAPI" check="false"
                     protocol="dubbo"/>
    <dubbo:reference id="dorisGeneralAPI" interface="com.yyigou.ddc.services.dw.api.DorisGeneralAPI" protocol="dubbo"
                     check="false"/>
    <dubbo:reference id="uLogAPI" interface="com.yyigou.ddc.services.dlog.api.ULogAPI" check="false"/>
    <dubbo:reference id="organizationAPI" interface="com.yyigou.ddc.services.ddc.uim.api.OrganizationAPI"
                     check="false"/>
    <dubbo:reference id="companyCertClient" interface="com.yyigou.dsrp.cert.client.companycert.CompanyCertClient"
                     check="false"/>
    <dubbo:reference id="paymentAgreementDubboAPI" interface="com.yyigou.ddc.services.dsrp.bdc.api.PaymentAgreementDubboAPI"
                     check="false"/>
</beans>
