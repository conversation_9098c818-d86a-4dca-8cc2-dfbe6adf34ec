<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd">

    <context:annotation-config/>
    <context:component-scan base-package="com.yyigou.dsrp.cdc.manager"/>

    <context:component-scan base-package="com.yyigou.ddc.services.ddc.task"/>

    <context:component-scan base-package="com.yyigou.ddc.services.ddc.uap.util"/>

    <context:component-scan base-package="com.yyigou.dsrp.cdc.common"/>


    <!-- kafka客户端配置 -->
    <import resource="classpath*:applicationContext-kafka-base.xml"/>

    <import resource="classpath*:mq/applicationContext-cdc-activemq.xml"/>
    <import resource="classpath:dubbo/cdc-dubbo-consumer.xml"/>
    <!--引入dlogUtil.xml-->
    <import resource="classpath*:META-INF/spring/applicationContext-dlogUtil.xml"/>

    <bean id="apiClient" class="com.yyigou.ddc.api.gateway.client.ApiClientDefault"/>
    <bean id="apiClientConfig"
          class="com.yyigou.ddc.api.gateway.client.ApiClientConfig">
        <property name="apiGatewayCallUrl" value="${common.api.gateway.callUrl}"/>
        <property name="bindTokenFromCaller" value="true"/>
    </bean>
</beans>