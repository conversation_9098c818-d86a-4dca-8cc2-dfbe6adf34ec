package com.yyigou.dsrp.cdc.manager.integration.cert.req;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;

@Data
public class CompanyCertQueryRequest implements Serializable {
    private static final long serialVersionUID = 227632384154382384L;

    @EntityField(name = "租户编号")
    private String enterpriseNo;

    @EntityField(name = "组织编号")
    private String orgNo;

    @EntityField(name = "企业编号")
    private String companyNo;

    @EntityField(name = "资料来源档案类型：1-企业档案，2-供应商档案，3-客户档案")
    private Integer sourceDocType;

    @EntityField(name = "资料来源档案编码")
    private String sourceDocCode;
}
