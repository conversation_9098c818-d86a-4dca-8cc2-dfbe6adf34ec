package com.yyigou.dsrp.cdc.manager.integration.utils;

import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cglib.beans.BeanCopier;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Slf4j
@Service
public class BeanCopierCopyUtil {

    public static <S, T> T copyFields(S s, Class<T> tClass) {
        BeanCopier beanCopier = BeanCopier.create(s.getClass(), tClass, false);
        T o = null;
        try {
            o = tClass.newInstance();
            beanCopier.copy(s, o, null);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return o;
    }

    public static <S, T> List<T> copyFieldsList(List<S> sList, Class<T> t) {
        List<T> resultList = Lists.newCopyOnWriteArrayList();
        if (CollectionUtils.isEmpty(sList)) {
            return resultList;
        }
        S s = sList.get(0);
        BeanCopier beanCopier = BeanCopier.create(s.getClass(), t, false);
        try {
            for (S member : sList) {
                T resultT = t.newInstance();
                beanCopier.copy(member, resultT, null);
                resultList.add(resultT);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return resultList;
    }
}
