package com.yyigou.dsrp.cdc.manager.integration.cert.res;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;

@Data
public class CompanyCertBusinessScopeResponse implements Serializable {
    private Long id;

    @EntityField(name = "经营范围id")
    private Long medCategoryId;

    @EntityField(name = "经营范围编码")
    private String medCategoryCode;

    @EntityField(name = "经营范围名称")
    private String medCategoryName;

    @EntityField(name = "经营范围分类")
    private String medManageCategory;

    @EntityField(name = "经营范围父id")
    private Integer medCategoryParentId;

    @EntityField(name = "是否是叶子节点")
    private Integer medCategoryIsLeaf;

    @EntityField(name = "经营范围状态")
    private String medCategoryStatus;

    @EntityField(name = "1:删除 0：未删除")
    private Integer deleted;

}
