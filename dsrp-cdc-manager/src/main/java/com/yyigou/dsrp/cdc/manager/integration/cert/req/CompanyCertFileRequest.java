package com.yyigou.dsrp.cdc.manager.integration.cert.req;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;

@Data
public class CompanyCertFileRequest implements Serializable {

    @EntityField(name = "文件地址", stringValueTypeLength = 512)
    private String filePath;
    
    @EntityField(name = "文件名称", stringValueTypeLength = 512)
    private String fileName;
    
}




