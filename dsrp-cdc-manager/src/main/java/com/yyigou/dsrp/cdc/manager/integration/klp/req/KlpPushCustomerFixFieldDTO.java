package com.yyigou.dsrp.cdc.manager.integration.klp.req;

import lombok.Getter;

import java.io.Serializable;

@Getter
public class KlpPushCustomerFixFieldDTO implements Serializable {
    // 来源系统*，固定值：DMS，表示来自DMS系统
    private String data_source = "DMS";

    // 接口类型*：固定值：customer，表示客户档案接口
    private String c_bill_type = "customer";

    // 数据类型*：固定值：2，表示客户档案
    private String custsupp = "2";

    // 建立公司组织编码*，固定值：KLP；表示导入NC集团为集团统管
    private String orgcode = "KLP";

    // 客户类型 0/1（二选一）：0外部单位/1内部单位；NC的内部单位的客户是NC公司组织来推式生成而不是录入客户的，故一般情况下DMS传送的都是0外部单位。
    private String custprop = "0";

    // 启用状态（固定传送2）。1=未启用，2=已启用，3=已停用
    private Integer enablestate = 2;

}
