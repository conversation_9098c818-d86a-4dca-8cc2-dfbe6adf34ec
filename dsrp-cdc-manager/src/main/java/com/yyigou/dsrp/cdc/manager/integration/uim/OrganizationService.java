package com.yyigou.dsrp.cdc.manager.integration.uim;

import com.alibaba.dubbo.config.annotation.Reference;
import com.yyigou.ddc.common.error.ErrorCode;
import com.yyigou.ddc.common.exception.BusinessException;
import com.yyigou.ddc.common.message.MessageUtil;
import com.yyigou.ddc.common.message.MessageVO;
import com.yyigou.ddc.common.page.PageDto;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.ddc.services.ddc.uim.api.OrganizationAPI;
import com.yyigou.ddc.services.ddc.uim.dto.OrganizationDto;
import com.yyigou.ddc.services.ddc.uim.dto.OrganizationTreeDto;
import com.yyigou.ddc.services.ddc.uim.enums.TenantTypeEnum;
import com.yyigou.ddc.services.ddc.uim.vo.OrganizationExtandVo;
import com.yyigou.ddc.services.ddc.uim.vo.OrganizationTreeVo;
import com.yyigou.ddc.services.ddc.uim.vo.OrganizationVo;
import com.yyigou.dsrp.cdc.common.util.CommonUtil;
import com.yyigou.dsrp.cdc.common.util.OperationModel;
import com.yyigou.dsrp.cdc.manager.integration.uim.response.UimOrganizationResponse;
import com.yyigou.dsrp.gcs.common.util.CallResultUtil;
import com.yyigou.dsrp.gcs.common.util.ValidatorUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class OrganizationService {
    @Reference(check = false)
    private OrganizationAPI organizationAPI;

    /**
     * 根据企业编号查询集团业务单元树
     *
     * @param enterpriseNo 租户编号
     * @return
     */
    public List<UimOrganizationResponse> findGroupOrgTreeList(String enterpriseNo) {
        OrganizationTreeDto organizationTreeDto = new OrganizationTreeDto();
        organizationTreeDto.setEnterpriseNo(enterpriseNo);
        CallResult<List<OrganizationTreeVo>> callResult = organizationAPI.findGroupOrgTreeListForDubbo(organizationTreeDto);
        List<OrganizationTreeVo> data = CallResultUtil.parseResult(callResult);
        List<UimOrganizationResponse> result = new ArrayList<>();
        getDataNoTre(data, result);
        return result;
    }

    /**
     * 根据租户编号查询集团租户编号
     *
     * @param enterpriseNo 租户编号
     * @return
     */
    public String queryGroupMasterEnterpriseNo(String enterpriseNo) {
        CallResult<String> callResult = organizationAPI.queryGroupMasterEnterpriseNoForDubbo(enterpriseNo);
        return CallResultUtil.parseResult(callResult);
    }

    public PageVo<UimOrganizationResponse> findOrgList(String groupEnterpriseNo, String enterpriseNo, String keyword, PageDto var2) {
        OrganizationDto organizationDto = new OrganizationDto();
        organizationDto.setEnterpriseNo(groupEnterpriseNo);
        organizationDto.setKeywords(keyword);
        if (!groupEnterpriseNo.equalsIgnoreCase(enterpriseNo)) {
            //如果不是集团租户则只能查询自己本身
            organizationDto.setBindingEnterpriseNo(enterpriseNo);
        }
        CallResult<PageVo<OrganizationVo>> callResult = organizationAPI.findOrgListForDubbo(organizationDto, var2);
        PageVo<OrganizationVo> pageVO = CallResultUtil.parseResult(callResult);
        List<UimOrganizationResponse> result = new ArrayList<>();

        if (!CollectionUtils.isEmpty(pageVO.getRows())) {
            pageVO.getRows().forEach(t -> {
                UimOrganizationResponse uimVo = new UimOrganizationResponse();
                BeanUtils.copyProperties(t, uimVo);
                result.add(uimVo);
            });
        }
        PageVo<UimOrganizationResponse> pageResult = new PageVo();
        pageResult.setPageIndex(pageVO.getPageIndex());
        pageResult.setPageSize(pageVO.getPageSize());
        pageResult.setTotal(pageVO.getTotal());
        pageResult.setPageCount(pageVO.getPageCount());
        pageResult.setRows(result);
        return pageResult;
    }

    /**
     * 递归
     *
     * @param organizations 数据
     * @param collections   收集器
     */
    private void getDataNoTre(List<OrganizationTreeVo> organizations, List<UimOrganizationResponse> collections) {
        if (!CollectionUtils.isEmpty(organizations)) {
            organizations.forEach(t -> {
                UimOrganizationResponse uimOrganizationResponse = new UimOrganizationResponse();
                BeanUtils.copyProperties(t, uimOrganizationResponse);
                collections.add(uimOrganizationResponse);
                getDataNoTre(t.getChildOrgs(), collections);
            });
        }

    }


    /**
     * 获取机构详情
     *
     * @param enterpriseNo
     */
    public UimOrganizationResponse getOrgDetail(String groupEnterpriseNo, String enterpriseNo) {
        List<UimOrganizationResponse> uimOrganizationResponses = findGroupOrgTreeList(groupEnterpriseNo);
//筛选处启用并且绑定了租户的叶子节点的树
        List<UimOrganizationResponse> list = uimOrganizationResponses.stream()
                .filter(t -> new Integer(1).equals(t.getOrgState())
                        && !StringUtils.isEmpty(t.getBindingEnterpriseNo())
                        && StringUtils.equals(t.getBindingEnterpriseNo(), enterpriseNo)
                ).collect(Collectors.toList());

        return CollectionUtils.isEmpty(list) ? null : list.get(0);

    }


    public List<UimOrganizationResponse> findChildList(String groupEnterpriseNo) {
        List<UimOrganizationResponse> uimOrganizationResponses = findGroupOrgTreeList(groupEnterpriseNo);
//筛选处启用并且绑定了租户的叶子节点的树
        List<UimOrganizationResponse> list = uimOrganizationResponses.stream()
                .filter(t -> new Integer(1).equals(t.getOrgState())
                        && !StringUtils.isEmpty(t.getBindingEnterpriseNo())
                ).collect(Collectors.toList());

        return list;

    }


    /**
     * 获取已授权的企业信息
     *
     * @param enterpriseNo 租户信息
     * @param employerNo   人员信息
     * @return
     */
    public List<OrganizationVo> getAuthEnterpriseList(String enterpriseNo, String userNo, String employerNo) {
        OrganizationDto params = new OrganizationDto();
        params.setEnterpriseNo(enterpriseNo);
        params.setEmployeeNo(employerNo);
        params.setUserNo(userNo);
        params.setBindTenantFlag(1);
        CallResult<List<OrganizationVo>> callResult = organizationAPI.findList(params);
        List<OrganizationVo> filterVoList = CallResultUtil.parseResult(callResult);
        //筛选启用并且绑定了租户的叶子节点的数据
        if (org.apache.commons.collections.CollectionUtils.isEmpty(filterVoList)) {
            return new ArrayList<>();
        }
        List<OrganizationVo> collect = filterVoList.stream().filter(x -> StringUtils.isNotBlank(x.getBindingEnterpriseNo())).collect(Collectors.toList());
        return collect;
    }

    public String getEnterpriseNoByOrgNo(String enterpriseNo, String orgNo){
        //通过采购组织编号查询租户编号
        OrganizationDto organizationDto = new OrganizationDto();
        organizationDto.setEnterpriseNo(enterpriseNo);
        organizationDto.setOrgNos(Arrays.asList(orgNo));
        CallResult<List<OrganizationVo>> callResult = organizationAPI.findListNoAuth(organizationDto);
        if (!MessageVO.SUCCESS.equals(callResult.getCode())) {
            throw new BusinessException("1", "依据业务单位编号查询租户信息失败：" + callResult.getMessage());
        }
        List<OrganizationVo> organizationVos = callResult.getData();
        if(!CollectionUtils.isEmpty(organizationVos)){
            enterpriseNo = organizationVos.get(0).getBindingEnterpriseNo();
            if(StringUtils.isEmpty(enterpriseNo)){
                throw new BusinessException("1", "所选组织未绑定租户");
            }
        }
        return enterpriseNo;
    }


    /**
     * 校验组织授权
     * */
    public void checkOrgAuthAndEnable(OperationModel operationModel, String orgNo, List<String> orgBusinessTypeNos) {
        //获取组织信息
        OrganizationExtandVo organizationExtandVo = getOrgDetailByOrgNo(orgNo);
        if (null == organizationExtandVo) {
            throw new BusinessException(ErrorCode.param_invalid_code, "未找到组织信息");
        }

        //判断组织是否停用
        if (!new Integer(1).equals(organizationExtandVo.getOrgState())) {
            throw new BusinessException(ErrorCode.param_invalid_code, "组织" + organizationExtandVo.getOrgName() + "已停用，请重新维护");
        }


        if (!TenantTypeEnum.ENTERPRISE.getValue().equals(operationModel.getTenantType())) {
            //判断是用户授权
            List<OrganizationVo> orgListByUserNo = getOrgListByUserNo(operationModel, orgBusinessTypeNos);
            if (CollectionUtils.isEmpty(orgListByUserNo)) {
                throw new BusinessException(ErrorCode.param_invalid_code, "组织" + organizationExtandVo.getOrgName() + "授权失败，请重新维护");
            }

            boolean flag = false;
            for (OrganizationVo organizationVo : orgListByUserNo) {
                if (orgNo.equals(organizationVo.getOrgNo())) {
                    flag = true;
                    break;
                }
            }
            if (!flag) {
                throw new BusinessException(ErrorCode.param_invalid_code, "组织" + organizationExtandVo.getOrgName() + "授权失败，请重新维护");
            }
        }
    }

    /**
     * 根据当前登录人，获取授权组织
     * */
    public List<OrganizationVo> getOrgListByUserNo(OperationModel operationModel, List<String> orgBusinessTypeNos){
        OrganizationDto organizationDto = new OrganizationDto();
        organizationDto.setEnterpriseNo(operationModel.getEnterpriseNo());
        organizationDto.setUserNo(operationModel.getUserNo());
        organizationDto.setEmployeeNo(operationModel.getEmployerNo());
        organizationDto.setOrgBusinessTypeNos(orgBusinessTypeNos);
        CallResult<List<OrganizationVo>> callResult = organizationAPI.findList(organizationDto);
        if(!MessageVO.SUCCESS.equals(callResult.getCode())){
            throw new BusinessException(callResult.getCode(),callResult.getMessage());
        }

        List<OrganizationVo> data = callResult.getData();
        if(CollectionUtils.isEmpty(data)){
            return new ArrayList<>();
        }
        return data;
    }


    /**
     * 根据租户编号获取组织信息
     */
    public OrganizationExtandVo getOrgDetailByOrgNo(String orgNo){
        ValidatorUtils.checkEmptyThrowEx(orgNo, "组织编号不能为空");
        CallResult<OrganizationExtandVo> callResult = organizationAPI.getOrgDetailNoSession(orgNo);
        MessageUtil.ensureCallResultSuccess(callResult);
        OrganizationExtandVo organizationExtandVo = callResult.getData();

        if(organizationExtandVo == null){
            throw new BusinessException(ErrorCode.param_invalid_code, "根据组织编号获取组织信息失败");
        }

        return organizationExtandVo;
    }

    public List<OrganizationVo> findListNoAuth(String enterpriseNo, List<String> orgNoList) {
        OrganizationDto organizationDto = new OrganizationDto();
        organizationDto.setEnterpriseNo(enterpriseNo);
        organizationDto.setOrgNos(orgNoList);

        CallResult<List<OrganizationVo>> listNoAuth = organizationAPI.findListNoAuth(organizationDto);

        return CommonUtil.parseResult(listNoAuth);
    }

    public PageVo<OrganizationVo> findListPageBySetting(OperationModel operationModel, List<String> excludeOrgNoList, PageDto pageDto) {
        OrganizationDto organizationDto = new OrganizationDto();
        organizationDto.setEnterpriseNo(operationModel.getEnterpriseNo());
        organizationDto.setExcludeOrgNos(excludeOrgNoList);
        CallResult<PageVo<OrganizationVo>> listPageBySetting = organizationAPI.findListPageBySetting(organizationDto, pageDto);
        return CommonUtil.parseResult(listPageBySetting);
    }

    /**
     * 批量查询组织信息
     * @param groupEnterpriseNo
     * @param enterpriseNo enterpriseNo可以和groupEnterpriseNo相同
     * @return
     */
    public OrganizationVo getOrgByEnterpriseNo(String groupEnterpriseNo, String enterpriseNo) {
        OrganizationDto organizationDto = new OrganizationDto();
        organizationDto.setEnterpriseNo(groupEnterpriseNo);
        CallResult<List<OrganizationVo>> callResult = organizationAPI.findListNoAuth(organizationDto);
        List<OrganizationVo> organizationVos = CallResultUtil.parseResult(callResult);
        return organizationVos.stream().filter(o -> Objects.equals(o.getBindingEnterpriseNo(), enterpriseNo)).findFirst().orElse(null);

    }
}
