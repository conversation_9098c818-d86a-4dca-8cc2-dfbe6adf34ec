package com.yyigou.dsrp.cdc.manager.integration.ulog;

import com.alibaba.dubbo.config.annotation.Reference;
import com.yyigou.ddc.services.dlog.api.ULogAPI;
import com.yyigou.ddc.services.dlog.dto.IntegrationLogDTO;
import com.yyigou.ddc.services.dlog.vo.IntegrationLogSaveVO;
import com.yyigou.dsrp.cdc.common.util.CommonUtil;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ULogService {
    @Reference(check = false)
    private ULogAPI uLogAPI;

    public List<IntegrationLogSaveVO> batchSaveLog(List<IntegrationLogDTO> params) {
        return CommonUtil.parseResult(uLogAPI.saveBatchForRpc(params));
    }
}
