package com.yyigou.dsrp.cdc.manager.integration.bizcodeGenerator;

import com.alibaba.dubbo.config.annotation.Reference;
import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.ddc.services.dsrp.csc.api.BizcodeGeneratorAPI;
import com.yyigou.ddc.services.dsrp.csc.dto.BizcodeGeneratorDto;
import com.yyigou.dsrp.gcs.common.util.CallResultUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
@Slf4j
public class BizCodeGeneratorService {

    @Reference(check = false)
    private BizcodeGeneratorAPI bizcodeGeneratorAPI;


    /**
     * 获取业务编码列表。
     *
     * @param enterpriseNo 企业编号，用于指定生成业务编码的企业。
     * @param billType     业务类型，指定生成的业务编码的类型。
     * @param size         需要生成的业务编码的数量。
     * @return 返回生成的业务编码列表。如果无法生成或生成为空，则返回空列表。
     */
    public List<String> getBizCodeList(String enterpriseNo, String billType, Integer size) {
        // 创建BizCodeGeneratorDto实例并设置生成业务编码所需的参数
        BizcodeGeneratorDto bizcodeGeneratorDto = new BizcodeGeneratorDto();
        bizcodeGeneratorDto.setBizcodeType(billType);
        bizcodeGeneratorDto.setEnterpriseNo(enterpriseNo);
        bizcodeGeneratorDto.setSize(size);
        // 调用接口获取业务编码列表
        CallResult<List<String>> callResult = bizcodeGeneratorAPI.getBizcodeNoSession(bizcodeGeneratorDto);
        List<String> result = CallResultUtil.parseResult(callResult);
        // 如果获取的业务编码列表为空，则直接返回空列表
        if (CollectionUtils.isEmpty(result)) {
            return new ArrayList<>();
        }
        // 返回获取到的业务编码列表
        return result;
    }


    public String getBizCode(String enterpriseNo, String billType) {
        // 创建BizCodeGeneratorDto实例并设置生成业务编码所需的参数
        BizcodeGeneratorDto bizcodeGeneratorDto = new BizcodeGeneratorDto();
        bizcodeGeneratorDto.setBizcodeType(billType);
        bizcodeGeneratorDto.setEnterpriseNo(enterpriseNo);
        bizcodeGeneratorDto.setSize(1);
        // 调用接口获取业务编码列表
        CallResult<List<String>> callResult = bizcodeGeneratorAPI.getBizcodeNoSession(bizcodeGeneratorDto);
        List<String> result = CallResultUtil.parseResult(callResult);
        return result.get(0);
    }


}
