package com.yyigou.dsrp.cdc.manager.integration.numberCenter;

import com.alibaba.dubbo.config.annotation.Reference;
import com.yyigou.ddc.common.error.ErrorCode;
import com.yyigou.ddc.common.exception.BusinessException;
import com.yyigou.ddc.common.exception.PojoErrorCode;
import com.yyigou.ddc.common.message.MessageUtil;
import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.ddc.common.util.StringUtil;
import com.yyigou.ddc.common.util.ValidatorUtils;
import com.yyigou.ddc.service.number.center.api.NumberAPI;
import com.yyigou.ddc.service.number.center.api.NumberComputerAPI;
import com.yyigou.ddc.service.number.center.domain.dto.NumberRuleDTO;
import com.yyigou.ddc.service.number.center.domain.vo.NumberVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Slf4j
public class NumberCenterService {

    @Reference(check = false)
    private NumberAPI numberAPI;

    @Reference(check = false)
    private NumberComputerAPI numberComputerAPI;

    /**
     * 生成NO
     *
     * @param key
     */
    public String createNumber(String key) {
        ValidatorUtils.checkEmptyThrowEx(key, "生成编号的类型不能为空");
        CallResult<String> res = numberAPI.create(key);
        if (res == null) {
            throw new BusinessException(ErrorCode.object_null_code, ErrorCode.object_null_msg);
        }
        if (res.getData() == null || StringUtil.isEmpty(res.getData())) {
            throw new BusinessException(ErrorCode.object_null_code, res.getMessage());
        }
        return res.getData();
    }

    public List<String> batchGenerateNoList(String billType, Integer size) {
        ValidatorUtils.checkEmptyThrowEx(billType, "生成编号的类型不能为空");
        ValidatorUtils.checkEmptyThrowEx(size, "生成编号数量不能为空");
        CallResult<List<String>> res = numberAPI.createBatch(billType, size);
        if (res == null || res.getData() == null) {
            throw new BusinessException(PojoErrorCode.ADD_FAIL_MSG, "生成编码失败");
        }
        return res.getData();
    }

    /**
     * 生成NO
     *
     * @param key
     * @param parentNo
     */
    public String createTreeNumber(String key, String parentNo) {
        CallResult<String> res = numberAPI.createTreeNumber(key, parentNo);
        if (res == null || res.getData() == null) {
            throw new BusinessException(ErrorCode.object_null_code, ErrorCode.object_null_code);
        }
        return res.getData();
    }

    /**
     * 生成单据编码
     * @param billNo
     * @param enterpriseNo
     * @return
     */
    public String createNumberForBill(String billNo, String enterpriseNo) {
        NumberRuleDTO numberRuleDTO = new NumberRuleDTO();
        numberRuleDTO.setBillNo(billNo);
        numberRuleDTO.setEnterpriseNo(enterpriseNo);
        numberRuleDTO.setBatchSize(1); // 单据号批量
        NumberVO numberVO = MessageUtil.ensureCallResultSuccess(numberComputerAPI.createBillNumberForEnterpriseInternal(numberRuleDTO, null));
        return numberVO.getNumbers().get(0);
    }
}
