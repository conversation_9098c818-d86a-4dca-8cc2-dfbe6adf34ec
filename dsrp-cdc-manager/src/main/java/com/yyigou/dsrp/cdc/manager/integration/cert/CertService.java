package com.yyigou.dsrp.cdc.manager.integration.cert;

import com.alibaba.dubbo.rpc.RpcContext;
import com.alibaba.fastjson.JSONObject;
import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.ddc.common.util.SessionUtils;
import com.yyigou.dsrp.cdc.common.util.BeanUtil;
import com.yyigou.dsrp.cdc.common.util.CommonUtil;
import com.yyigou.dsrp.cdc.manager.integration.cert.req.CompanyCertQueryRequest;
import com.yyigou.dsrp.cdc.manager.integration.cert.req.CompanyCertUpsertRequest;
import com.yyigou.dsrp.cdc.manager.integration.cert.res.CompanyCertResponse;
import com.yyigou.dsrp.cert.client.companycert.CompanyCertClient;
import com.yyigou.dsrp.cert.client.companycert.req.CompanyLicenseCertRequest;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

import static com.yyigou.ddc.common.service.Constants.CALL_CONTEXT_SESSION_KEY;

@Service
public class CertService {
    @Resource
    private CompanyCertClient companyCertClient;

    public Boolean upsert(CompanyCertUpsertRequest companyCertUpsertRequest) {
        RpcContext.getContext().setAttachment(CALL_CONTEXT_SESSION_KEY,
                JSONObject.toJSONString(SessionUtils.simplySessionUser(SessionUtils.getSessionUser())));

        com.yyigou.dsrp.cert.client.companycert.req.CompanyCertUpsertRequest companyCertUpsertReq = BeanUtil.copyFieldsForJson(companyCertUpsertRequest, com.yyigou.dsrp.cert.client.companycert.req.CompanyCertUpsertRequest.class);
        CallResult<Boolean> callResult = companyCertClient.upSert(companyCertUpsertReq);
        return CommonUtil.parseResult(callResult);

    }

    public Boolean preValidateUpsert(CompanyCertUpsertRequest companyCertUpsertRequest) {
        RpcContext.getContext().setAttachment(CALL_CONTEXT_SESSION_KEY,
                JSONObject.toJSONString(SessionUtils.simplySessionUser(SessionUtils.getSessionUser())));

        com.yyigou.dsrp.cert.client.companycert.req.CompanyCertUpsertRequest companyCertUpsertReq = BeanUtil.copyFieldsForJson(companyCertUpsertRequest, com.yyigou.dsrp.cert.client.companycert.req.CompanyCertUpsertRequest.class);
        CallResult<Boolean> callResult = companyCertClient.preValidateUpSert(companyCertUpsertReq);
        return CommonUtil.parseResult(callResult);

    }

    public List<CompanyCertResponse> findList(CompanyCertQueryRequest companyCertQueryRequest) {
        com.yyigou.dsrp.cert.client.companycert.req.CompanyCertQueryRequest companyCertQueryReq = BeanUtil.copyFieldsForJson(companyCertQueryRequest, com.yyigou.dsrp.cert.client.companycert.req.CompanyCertQueryRequest.class);
        CallResult<List<com.yyigou.dsrp.cert.client.companycert.res.CompanyCertResponse>> callResult = companyCertClient.findList(companyCertQueryReq);
        List<com.yyigou.dsrp.cert.client.companycert.res.CompanyCertResponse> companyCertRes = CommonUtil.parseResult(callResult);
        if (CollectionUtils.isEmpty(companyCertRes)) {
            return Collections.emptyList();
        }

        return BeanUtil.copyFieldsListForJSON(companyCertRes, CompanyCertResponse.class);

    }

    public List<CompanyCertResponse> findCompanyLicenseCertList(CompanyLicenseCertRequest params) {
        CallResult<List<com.yyigou.dsrp.cert.client.companycert.res.CompanyCertResponse>> callResult = companyCertClient.findCompanyLicenseCertList(params);
        List<com.yyigou.dsrp.cert.client.companycert.res.CompanyCertResponse> companyCertRes = CommonUtil.parseResult(callResult);
        if (CollectionUtils.isEmpty(companyCertRes)) {
            return Collections.emptyList();
        }

        return BeanUtil.copyFieldsListForJSON(companyCertRes, CompanyCertResponse.class);

    }

}
