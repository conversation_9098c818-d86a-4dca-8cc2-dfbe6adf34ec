package com.yyigou.dsrp.cdc.manager.integration.uap;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.dubbo.rpc.RpcContext;
import com.alibaba.fastjson.JSONObject;
import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.ddc.common.service.SessionUser;
import com.yyigou.ddc.common.util.SessionUtils;
import com.yyigou.ddc.services.ddc.uap.api.GradeControlClient;
import com.yyigou.ddc.services.ddc.uap.api.grade.GradeDocAPI;
import com.yyigou.ddc.services.ddc.uap.domain.dto.GradeAutoReviewConfigDTO;
import com.yyigou.ddc.services.ddc.uap.domain.dto.grade.GradeCheckMgrOrgDTO;
import com.yyigou.ddc.services.ddc.uap.domain.dto.grade.GradeControlAssignDTO;
import com.yyigou.ddc.services.ddc.uap.domain.dto.grade.GradeControlCancelAssignDTO;
import com.yyigou.ddc.services.ddc.uap.domain.dto.grade.GradeControlDocUpdateDTO;
import com.yyigou.ddc.services.ddc.uap.domain.vo.GradeAutoReviewConfigVO;
import com.yyigou.ddc.services.ddc.uap.domain.vo.grade.GradeCancelAssignVO;
import com.yyigou.ddc.services.ddc.uap.domain.vo.grade.GradeCheckMgrOrgVO;
import com.yyigou.dsrp.cdc.common.util.CommonUtil;
import com.yyigou.dsrp.cdc.manager.integration.uap.res.GradeCancelAssignRes;
import com.yyigou.dsrp.cdc.manager.integration.uap.res.GradeCheckMgrOrgRes;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.yyigou.ddc.common.service.Constants.CALL_CONTEXT_SESSION_KEY;

@Service
public class GradeControlService {
    @Reference(check = false)
    private GradeControlClient gradeControlClient;

    @Reference(check = false)
    private GradeDocAPI gradeDocAPI;

    public Boolean executeCancelAssignDoc(String enterpriseNo, String billNo, String docNo, String useOrgNo) {
        GradeControlCancelAssignDTO gradeControlCancelAssignDTO = new GradeControlCancelAssignDTO();
        gradeControlCancelAssignDTO.setEnterpriseNo(enterpriseNo);
        gradeControlCancelAssignDTO.setBillNo(billNo);
        gradeControlCancelAssignDTO.setDocNo(docNo);
        gradeControlCancelAssignDTO.setUseOrgNo(useOrgNo);

        return CommonUtil.parseResult(gradeControlClient.executeCancelAssignDoc(gradeControlCancelAssignDTO));
    }


    public Map<String, GradeCancelAssignRes> executeCancelAssignDoc(String enterpriseNo, String billNo, String docNo, List<String> useOrgNoList) {
        GradeControlCancelAssignDTO gradeControlCancelAssignDTO = new GradeControlCancelAssignDTO();
        gradeControlCancelAssignDTO.setEnterpriseNo(enterpriseNo);
        gradeControlCancelAssignDTO.setBillNo(billNo);
        gradeControlCancelAssignDTO.setDocNo(docNo);
        gradeControlCancelAssignDTO.setUseOrgNos(useOrgNoList);

        Map<String, GradeCancelAssignVO> orgNo2AssignResult = CommonUtil.parseResult(gradeControlClient.executeCancelAssignDocBatch(gradeControlCancelAssignDTO));

        Map<String, GradeCancelAssignRes> resMap = new HashMap<>(orgNo2AssignResult.size());

        orgNo2AssignResult.forEach((orgNo, gradeCancelAssignVO) -> {
            GradeCancelAssignRes gradeCancelAssignRes = new GradeCancelAssignRes();
            gradeCancelAssignRes.setDocNo(gradeCancelAssignVO.getDocNo());
            gradeCancelAssignRes.setUseOrgNo(gradeCancelAssignVO.getUseOrgNo());
            gradeCancelAssignRes.setSuccess(gradeCancelAssignVO.getSuccess());
            gradeCancelAssignRes.setMessage(gradeCancelAssignVO.getMessage());

            resMap.put(orgNo, gradeCancelAssignRes);
        });

        return resMap;
    }

    public List<String> listMgrOrgNos(String enterpriseNo, String billNo) {
        return CommonUtil.parseResult(gradeControlClient.listMgrOrgNos(enterpriseNo, billNo));
    }

    public List<String> listUseOrgNos(String enterpriseNo, String viewNo, String mgrOrgNo) {
        return CommonUtil.parseResult(gradeControlClient.listUseOrgNos(enterpriseNo, viewNo, mgrOrgNo));
    }

    /**
     * @param enterpriseNo
     * @param billNo
     * @param docNo
     * @param useOrgNo
     * @param updateMode   1 insert模式；0 update模式
     * @return
     */
    public Boolean updateDocHandler(String enterpriseNo, String billNo, String docNo, String useOrgNo, int updateMode) {
        GradeControlDocUpdateDTO gradeControlDocUpdateDTO = new GradeControlDocUpdateDTO();
        gradeControlDocUpdateDTO.setEnterpriseNo(enterpriseNo);
        gradeControlDocUpdateDTO.setBillNo(billNo);
        gradeControlDocUpdateDTO.setDocNo(docNo);
        gradeControlDocUpdateDTO.setUseOrgNo(useOrgNo);
        gradeControlDocUpdateDTO.setUpdateMode(updateMode);

        return CommonUtil.parseResult(gradeControlClient.updateDocHandler(gradeControlDocUpdateDTO));
    }

    public String executeAssignDocs(String enterpriseNo, String viewNo, String docNo, String manageOrgNo, List<String> useOrgNoList) {
        SessionUser sessionUser = new SessionUser();
        sessionUser.setEnterpriseNo(enterpriseNo);

        RpcContext.getContext().setAttachment(CALL_CONTEXT_SESSION_KEY, JSONObject.toJSONString(SessionUtils.simplySessionUser(sessionUser)));

        GradeControlAssignDTO gradeControlAssignDTO = new GradeControlAssignDTO();
        gradeControlAssignDTO.setDocNos(Collections.singletonList(docNo));
        gradeControlAssignDTO.setViewNo(viewNo);
        gradeControlAssignDTO.setMgrOrgNo(manageOrgNo);
        gradeControlAssignDTO.setUseOrgNos(useOrgNoList);

        return CommonUtil.parseResult(gradeDocAPI.executeAssignDocs(gradeControlAssignDTO));
    }

    public GradeCheckMgrOrgRes checkOrgNoAndListMgrOrgNos(String enterpriseNo, String billNo, String viewNo, String orgNo) {
        GradeCheckMgrOrgDTO gradeCheckMgrOrgDTO = new GradeCheckMgrOrgDTO();
        gradeCheckMgrOrgDTO.setEnterpriseNo(enterpriseNo);
        gradeCheckMgrOrgDTO.setBillNo(billNo);
        gradeCheckMgrOrgDTO.setViewNo(viewNo);
        gradeCheckMgrOrgDTO.setOrgNo(orgNo);

        CallResult<GradeCheckMgrOrgVO> gradeCheckMgrOrgVOCallResult = gradeControlClient.checkOrgNoAndListMgrOrgNos(gradeCheckMgrOrgDTO);
        GradeCheckMgrOrgVO gradeCheckMgrOrgVO = CommonUtil.parseResult(gradeCheckMgrOrgVOCallResult);

        GradeCheckMgrOrgRes gradeCheckMgrOrgRes = new GradeCheckMgrOrgRes();
        gradeCheckMgrOrgRes.setMgrPrevEnabled(gradeCheckMgrOrgVO.getMgrPrevEnabled());
        gradeCheckMgrOrgRes.setMgrOrgNos(gradeCheckMgrOrgVO.getMgrOrgNos());

        return gradeCheckMgrOrgRes;
    }

    public static String AUTO_REVIEW = "1";

    public List<GradeAutoReviewConfigVO> listApplyAutoReviewConfig(String enterpriseNo, String viewNo, String manageOrgNo, List<String> useOrgNos, Boolean isAdd) {
        GradeAutoReviewConfigDTO gradeAutoReviewConfigDTO = new GradeAutoReviewConfigDTO();
        gradeAutoReviewConfigDTO.setEnterpriseNo(enterpriseNo);
        gradeAutoReviewConfigDTO.setViewNo(viewNo);
        gradeAutoReviewConfigDTO.setManageOrgNo(manageOrgNo);
        if (isAdd) {
            gradeAutoReviewConfigDTO.setAddAutoReview(AUTO_REVIEW);
        } else {
            gradeAutoReviewConfigDTO.setUpdateAutoReview(AUTO_REVIEW);
        }
        gradeAutoReviewConfigDTO.setUseOrgNos(useOrgNos);

        return CommonUtil.parseResult(gradeControlClient.listApplyAutoReviewConfig(gradeAutoReviewConfigDTO));
    }
}
