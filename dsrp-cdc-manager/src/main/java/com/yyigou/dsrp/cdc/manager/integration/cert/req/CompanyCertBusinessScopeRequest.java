package com.yyigou.dsrp.cdc.manager.integration.cert.req;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;

@Data
public class CompanyCertBusinessScopeRequest implements Serializable {

    private static final long serialVersionUID = 1107337187555463477L;

    @EntityField(name = "经营范围id")
    private Long medCategoryId;

    /**
     * 经营范围编码
     */
    @EntityField(name = "经营范围编码")
    private String medCategoryCode;

    /**
     * 经营范围名称
     */
    @EntityField(name = "经营范围名称")
    private String medCategoryName;

    /**
     * 经营范围分类
     */
    @EntityField(name = "经营范围分类")
    private String medManageCategory;

    @EntityField(name = "经营范围父id")
    private Integer medCategoryParentId;

    @EntityField(name = "是否是叶子节点")
    private Integer medCategoryIsLeaf;

    @EntityField(name = "经营范围状态")
    private String medCategoryStatus;
}
