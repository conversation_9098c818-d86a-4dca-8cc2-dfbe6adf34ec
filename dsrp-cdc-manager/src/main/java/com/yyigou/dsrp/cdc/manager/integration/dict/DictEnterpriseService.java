package com.yyigou.dsrp.cdc.manager.integration.dict;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.dubbo.rpc.RpcContext;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yyigou.ddc.common.message.MessageVO;
import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.ddc.common.service.ServiceBaseAbstract;
import com.yyigou.ddc.common.service.SessionUser;
import com.yyigou.ddc.common.util.SessionUtils;
import com.yyigou.ddc.services.ddc.dict.api.AreaCodeAPI;
import com.yyigou.ddc.services.ddc.dict.api.CountryRegionAPI;
import com.yyigou.ddc.services.ddc.dict.api.CurrencyAPI;
import com.yyigou.ddc.services.ddc.dict.api.DictEnterpriseAPI;
import com.yyigou.ddc.services.ddc.dict.dto.AreaCodeBatchQueryDto;
import com.yyigou.ddc.services.ddc.dict.dto.countryRegion.CountryRegionQueryDTO;
import com.yyigou.ddc.services.ddc.dict.dto.currency.CurrencyQueryDTO;
import com.yyigou.ddc.services.ddc.dict.vo.AreaCodeVo;
import com.yyigou.ddc.services.ddc.dict.vo.CountryRegionVO;
import com.yyigou.ddc.services.ddc.dict.vo.CurrencyVO;
import com.yyigou.ddc.services.ddc.dict.vo.DictEnterpriseVo;
import com.yyigou.dsrp.cdc.common.util.OperationModel;
import com.yyigou.dsrp.cdc.manager.integration.dict.enums.DictNumberEnum;
import com.yyigou.dsrp.gcs.common.util.CallResultUtil;
import com.yyigou.dsrp.gcs.common.util.ValidatorUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.yyigou.ddc.common.service.Constants.CALL_CONTEXT_SESSION_KEY;

/**
 * 租户字典表
 *
 * @author: fudj
 * @createTime: 2023-04-29  23:09
 * @version: 1.0
 */
@Service
@Slf4j
public class DictEnterpriseService {
    @Reference(check = false)
    private DictEnterpriseAPI dictEnterpriseAPI;
    @Reference(check = false)
    private AreaCodeAPI areaCodeAPI;

    @Reference(check = false)
    private CountryRegionAPI countryRegionAPI;

    @Reference(check = false)
    private CurrencyAPI currencyAPI;


    /**
     * 根据数据字典的字典编码获取内容
     *
     * @param enterpriseNo   企业编号
     * @param dictNumberEnum 字典编号枚举
     * @return key->字典编号,value->字典值
     */
    public Map<String, String> getValMapByNumber(String enterpriseNo, DictNumberEnum dictNumberEnum) {
        DictEnterpriseVo result = findListByNumber(enterpriseNo, dictNumberEnum);
        Map<String, String> productOneLevelMap = result.getChildren().stream().collect(Collectors.toMap(DictEnterpriseVo::getDictNumber, DictEnterpriseVo::getDictVal));
        return productOneLevelMap;
    }


    /**
     * 根据数据字典的字典编码获取内容
     *
     * @param enterpriseNo   企业编号
     * @param dictNumberEnum 字典编号枚举
     * @return key->字典值,value->字典编号
     */
    public Map<String, String> getNameMapByNumber(String enterpriseNo, DictNumberEnum dictNumberEnum) {
        DictEnterpriseVo result = findListByNumber(enterpriseNo, dictNumberEnum);
        Map<String, String> productOneLevelMap = result.getChildren().stream()
                .collect(Collectors.toMap(DictEnterpriseVo::getDictVal, DictEnterpriseVo::getDictNumber, (k1, k2) -> k2));
        return productOneLevelMap;
    }

    /**
     * 通过字典编码获取集合
     *
     * @param enterpriseNo   企业编号
     * @param dictNumberEnum 字典编号
     * @return DictEnterpriseVo
     */
    public DictEnterpriseVo findListByNumber(String enterpriseNo, DictNumberEnum dictNumberEnum) {
        ValidatorUtils.checkEmptyThrowEx(enterpriseNo, "企业编号不能为空");
        ValidatorUtils.checkEmptyThrowEx(dictNumberEnum, "字典编号不能为空");
        CallResult<DictEnterpriseVo> callResult = dictEnterpriseAPI.findByNumber(dictNumberEnum.getValue(), enterpriseNo);
        DictEnterpriseVo result = CallResultUtil.parseResult(callResult);
        ValidatorUtils.checkEmptyThrowEx(result, "没有配置字典项:" + dictNumberEnum.getName());
        return result;
    }


    private DictEnterpriseVo findDictConfig(String dictKey, String enterpriseNo) {
        CallResult<DictEnterpriseVo> callResult = dictEnterpriseAPI.findByNumber(dictKey, enterpriseNo);
        if (!MessageVO.SUCCESS.equals(callResult.getCode())) {
            log.error("接口访问错误dictEnterpriseAPI.findByNumber,dictKey={},enterpriseNo={}", dictKey, enterpriseNo);
            return null;
        }
        //再查询下子节点
        DictEnterpriseVo dictVo = callResult.getData();
        if (dictVo == null) {
            return null;
        }

        return callResult.getData();
    }

    /**
     * 根据区域编码查询区域信息
     *
     * @param areaCodeList
     * @return: {@link List< AreaCodeVo>}
     */
    public List<AreaCodeVo> findAreaByCodes(List<String> areaCodeList) {
        ValidatorUtils.checkEmptyThrowEx(areaCodeList, "区域编码不能为空");
        AreaCodeBatchQueryDto queryDto = new AreaCodeBatchQueryDto();
        queryDto.setCodeList(areaCodeList);
        CallResult<List<AreaCodeVo>> areaCallResult = areaCodeAPI.queryByCodes(queryDto);
        if (!MessageVO.SUCCESS.equals(areaCallResult.getCode())) {
            log.error("根据区域编码查询区域信息出错，区域编码：{}", JSON.toJSONString(areaCodeList));
            return null;
        }
        return areaCallResult.getData();
    }

    /**
     * 根据区域编码查询区域信息
     *
     * @param areaCodeList
     * @return:
     */
    public Map<String, String> findAreaMapByCodes(List<String> areaCodeList) {
        List<AreaCodeVo> areaCodeVos = findAreaByCodes(areaCodeList);
        if (CollectionUtils.isEmpty(areaCodeVos)) {
            return null;
        }
        Map<String, String> areaMap = areaCodeVos.stream().collect(Collectors.toMap(AreaCodeVo::getAreaCode, AreaCodeVo::getAreaName, (k1, k2) -> k1));
        return areaMap;
    }

    /**
     * 根据区域编码查询区域信息
     *
     * @param areaCodeList
     * @return:
     */
    public Map<String, String> findAreaMapByCodesWithFullName(List<String> areaCodeList) {
        List<AreaCodeVo> areaCodeVos = findAreaByCodes(areaCodeList);
        if (CollectionUtils.isEmpty(areaCodeVos)) {
            return null;
        }
        Map<String, String> areaMap = areaCodeVos.stream().filter(t -> null != t.getAreaFullName()).collect(Collectors.toMap(AreaCodeVo::getAreaCode, AreaCodeVo::getAreaFullName, (k1, k2) -> k1));
        return areaMap;
    }

    public Map<String, String> findPlatformCountryRegionList() {
        Map<String, String> result = new HashMap<>();

        CountryRegionQueryDTO countryRegionQueryDTO = new CountryRegionQueryDTO();
        CallResult<List<CountryRegionVO>> callResult = countryRegionAPI.findPlatformCountryRegionList(countryRegionQueryDTO);
        List<CountryRegionVO> countryRegionVOList = CallResultUtil.parseResult(callResult);
        if (CollectionUtils.isNotEmpty(countryRegionVOList)) {
            countryRegionVOList.forEach(t -> {
                result.put(t.getIso2Code(), t.getNameZh());
            });
        }
        return result;
    }

    public Map<String, String> findCurrencyList(String enterpriseNo) {
        Map<String, String> result = new HashMap<>();

        CurrencyQueryDTO currencyQueryDTO = new CurrencyQueryDTO();
        currencyQueryDTO.setEnterpriseNo(enterpriseNo);
//        SessionUser sessionUser = ServiceBaseAbstract.currentRequest().getSession();
//        RpcContext.getContext().setAttachment(CALL_CONTEXT_SESSION_KEY,
//                JSONObject.toJSONString(SessionUtils.simplySessionUser(sessionUser)));
        CallResult<List<CurrencyVO>> callResult = currencyAPI.findCurrencyList(currencyQueryDTO);
        List<CurrencyVO> currencyVOList = CallResultUtil.parseResult(callResult);
        if (CollectionUtils.isNotEmpty(currencyVOList)) {
            currencyVOList.forEach(t -> {
                result.put(t.getCurrencyCode(), t.getCurrencyName());
            });
        }

        return result;

    }
}
