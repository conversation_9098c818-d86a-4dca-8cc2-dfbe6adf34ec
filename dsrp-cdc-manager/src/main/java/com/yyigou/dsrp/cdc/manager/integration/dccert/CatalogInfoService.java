package com.yyigou.dsrp.cdc.manager.integration.dccert;

import com.alibaba.dubbo.config.annotation.Reference;
import com.yyigou.ddc.common.message.MessageUtil;
import com.yyigou.ddc.common.page.PageDto;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.dsrp.cert.client.OperateUserClientReq;
import com.yyigou.dsrp.cert.client.certbase.CatalogCollectRelateClient;
import com.yyigou.dsrp.cert.client.certbase.CatalogInfoClient;
import com.yyigou.dsrp.cert.client.certbase.req.CatalogCollectDispatchScopeClientReq;
import com.yyigou.dsrp.cert.client.certbase.req.CatalogInfoBigClientReq;
import com.yyigou.dsrp.cert.client.certbase.req.CatalogInfoClientReq;
import com.yyigou.dsrp.cert.client.certbase.res.CatalogInfoClientRes;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;

@Service
@Slf4j
public class CatalogInfoService {

    @Reference(check = false)
    private CatalogInfoClient catalogInfoClient;

    @Reference(check = false)
    private CatalogCollectRelateClient catalogCollectRelateClient;

    /**
     * 企业档案分派给子租户
     *
     * @param groupEnterpriseNo
     * @param subEnterpriseNo
     * @param groupCompanyNo
     * @param subCompanyNo
     * @return
     */
    public void companyDispatch(String groupEnterpriseNo, String subEnterpriseNo, String groupCompanyNo, String subCompanyNo) {
        if (StringUtils.isEmpty(groupCompanyNo) || StringUtils.isEmpty(groupEnterpriseNo) || StringUtils.isEmpty(subEnterpriseNo)) {
            return;
        }
        CatalogCollectDispatchScopeClientReq catalogCollectDispatchScopeClientReq = new CatalogCollectDispatchScopeClientReq();
        catalogCollectDispatchScopeClientReq.setGroupEnterpriseNo(groupEnterpriseNo);
        catalogCollectDispatchScopeClientReq.setSubEnterpriseNo(subEnterpriseNo);
        catalogCollectDispatchScopeClientReq.setGroupObjectNo(groupCompanyNo);
        catalogCollectDispatchScopeClientReq.setSubObjectNo(subCompanyNo);
        catalogCollectRelateClient.companyDispatch(catalogCollectDispatchScopeClientReq);
    }

    /**
     * 保存电子资料
     *
     * @param params
     * @return: {@link CatalogInfoClientRes}
     */
    public CatalogInfoClientRes saveCert(CatalogInfoClientReq params) {
        CallResult<CatalogInfoClientRes> callResult = catalogInfoClient.save(params);
        return MessageUtil.ensureCallResultSuccess(callResult);
    }

    /**
     * 批量保存企业资料
     *
     * @param catalogInfoClientReqList
     * @param operateUserClientReq
     * @return: {@link Boolean}
     */
    public Boolean saveCertBatch(List<CatalogInfoClientReq> catalogInfoClientReqList, OperateUserClientReq operateUserClientReq) {
        CallResult<Boolean> callResult = catalogInfoClient.batchSave(catalogInfoClientReqList, operateUserClientReq);
        return MessageUtil.ensureCallResultSuccess(callResult);
    }

    /**
     * 批量更新企业资料
     *
     * @param catalogInfoClientReqList
     * @param operateUserClientReq
     * @return: {@link Boolean}
     */
    public Boolean updateCertBatch(List<CatalogInfoClientReq> catalogInfoClientReqList, OperateUserClientReq operateUserClientReq) {
        CallResult<Boolean> callResult = catalogInfoClient.batchUpdate(catalogInfoClientReqList, operateUserClientReq);
        return MessageUtil.ensureCallResultSuccess(callResult);
    }

    /**
     * 批量保存或更新企业资料
     *
     * @param catalogInfoBigClientReq
     * @return: {@link Boolean}
     */
    public Boolean saveOrUpdateCertBatch(CatalogInfoBigClientReq catalogInfoBigClientReq) {
        CallResult<Boolean> callResult = catalogInfoClient.saveOrUpdate(catalogInfoBigClientReq);
        return MessageUtil.ensureCallResultSuccess(callResult);
    }

    public List<CatalogInfoClientRes> findCompanyCatalogInfoList(CatalogInfoBigClientReq catalogInfoBigClientReq, List<String> companyNoList) {
        PageDto pageParams = new PageDto();
        pageParams.setPageIndex(1);
        pageParams.setPageSize(999);
        CallResult<PageVo<CatalogInfoClientRes>> callResult = catalogInfoClient.findCompanyCatalogInfoPageList(catalogInfoBigClientReq, companyNoList, pageParams);
        PageVo<CatalogInfoClientRes> resPageVo = MessageUtil.ensureCallResultSuccess(callResult);
        return resPageVo.getRows();
    }

}
