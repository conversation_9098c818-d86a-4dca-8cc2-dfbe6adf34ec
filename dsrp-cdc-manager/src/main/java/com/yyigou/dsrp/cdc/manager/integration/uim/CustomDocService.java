package com.yyigou.dsrp.cdc.manager.integration.uim;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.yyigou.ddc.common.message.MessageVO;
import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.ddc.services.ddc.uim.api.CustomDocAPI;
import com.yyigou.ddc.services.ddc.uim.dto.customDoc.CustomDocItemQueryDTO;
import com.yyigou.ddc.services.ddc.uim.vo.customDoc.CustomDocDetailVO;
import com.yyigou.ddc.services.ddc.uim.vo.customDoc.CustomDocItemVO;
import com.yyigou.dsrp.cdc.manager.integration.uim.response.CustomDocResponse;
import com.yyigou.dsrp.gcs.common.util.CallResultUtil;
import com.yyigou.dsrp.gcs.model.enums.commonspucategory.CustomCodeEnum;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 自定义档案API
 */
@Service
public class CustomDocService {

    @Reference(check = false)
    private CustomDocAPI customDocAPI;

    public List<CustomDocResponse> getItemListByCustomDocCode(String enterpriseNo, String docCode) {
        CustomDocItemQueryDTO queryDTO = new CustomDocItemQueryDTO();
        queryDTO.setEnterpriseNo(enterpriseNo);
        queryDTO.setCustomDocCode(docCode);
        CallResult<List<CustomDocItemVO>> callResult = customDocAPI.findItemByIdListForDubbo(queryDTO);
        List<CustomDocItemVO> dataList = CallResultUtil.parseResult(callResult);
        List<CustomDocResponse> result = new ArrayList<>();
        dataList.forEach(t -> {
            CustomDocResponse response = new CustomDocResponse();
            BeanUtils.copyProperties(t, response);
            result.add(response);
        });
        return result;
    }

    public List<CustomDocResponse> getItemListByCustomDocCodeAndKeywords(String enterpriseNo, String docCode, String keywords) {
        CustomDocItemQueryDTO queryDTO = new CustomDocItemQueryDTO();
        queryDTO.setEnterpriseNo(enterpriseNo);
        queryDTO.setCustomDocCode(docCode);
        queryDTO.setDocItemNameKeywords(keywords);
        CallResult<List<CustomDocItemVO>> callResult = customDocAPI.findItemByIdListForDubbo(queryDTO);
        List<CustomDocItemVO> dataList = CallResultUtil.parseResult(callResult);
        List<CustomDocResponse> result = new ArrayList<>();
        dataList.forEach(t -> {
            CustomDocResponse response = new CustomDocResponse();
            BeanUtils.copyProperties(t, response);
            result.add(response);
        });
        return result;
    }

    /**
     * 获取自定义档案
     *
     * @param enterpriseNo
     * @param docCode
     * @return
     */
    public Map<String, CustomDocResponse> getItemByCustomDocCode(String enterpriseNo, String docCode) {
        return getItemListByCustomDocCode(enterpriseNo, docCode).stream().collect(Collectors.toMap(CustomDocResponse::getDocItemCode, Function.identity(), (n, w) -> n));
    }

    public List<CustomDocResponse> findItemByCode(String enterpriseNo, CustomCodeEnum customCodeEnum, List<String> itemCodes) {
        CallResult<List<CustomDocItemVO>> customDocItemCallResult = customDocAPI.findItemByCodeForDubbo(enterpriseNo, customCodeEnum.getValue(), itemCodes);
        Assert.isTrue(MessageVO.SUCCESS.equals(customDocItemCallResult.getCode()),
                String.format("调用接口【customDocAPI.findItemByCodeForDubbo】异常,请求参数：%s %s %s,返回参数：%s", enterpriseNo,
                        customCodeEnum.getValue(), JSON.toJSONString(itemCodes), JSON.toJSONString(customDocItemCallResult)));

        return customDocItemCallResult.getData() != null ? customDocItemCallResult.getData().stream().map(t -> {
            CustomDocResponse response = new CustomDocResponse();
            BeanUtils.copyProperties(t, response);
            response.setLongId(t.getId());
            return response;
        }).collect(Collectors.toList()) : new ArrayList<>();
    }

    public CustomDocResponse getItemByCode(String enterpriseNo, CustomCodeEnum customCodeEnum, String itemCode) {
        List<CustomDocResponse> customDocResponses = findItemByCode(enterpriseNo, customCodeEnum, Arrays.asList(itemCode));
        return CollectionUtils.isNotEmpty(customDocResponses) ? customDocResponses.get(0) : null;

    }

    /**
     * 新增或者更新自定义档案
     *
     * @author: Moore
     * @date: 2024/11/14 19:58
     * @version: 1.0.0
     */
    public List<CustomDocResponse> saveAndUpdate(String enterpriseNo, String customDocCode, Map<String, String> itemMap, String employerNo, String userName) {
        CallResult<List<CustomDocDetailVO>> customDocItemCallResult = customDocAPI.saveAndUpdateForDubbo(enterpriseNo, customDocCode, itemMap, employerNo, userName);
        Assert.isTrue(MessageVO.SUCCESS.equals(customDocItemCallResult.getCode()), String.format("调用接口【customDocAPI.saveAndUpdateForDubbo】异常,请求参数：%s %s %s,返回参数：%s", enterpriseNo, customDocCode, JSON.toJSONString(itemMap), JSON.toJSONString(customDocItemCallResult)));
        return customDocItemCallResult.getData() != null ? customDocItemCallResult.getData().stream().map(t -> {
            CustomDocResponse response = new CustomDocResponse();
            BeanUtils.copyProperties(t, response);
            response.setLongId(t.getId());
            return response;
        }).collect(Collectors.toList()) : new ArrayList<>();
    }
}
