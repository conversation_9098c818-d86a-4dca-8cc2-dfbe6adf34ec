package com.yyigou.dsrp.cdc.manager.integration.dict.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * ddc_dict 字典项枚举
 *
 * @author: fudj
 * @createTime: 2023-04-23  21:10
 * @version: 1.0
 */
public enum DictNumberEnum {
    DSRP_CURRENCY("dsrp_currency", "交易币种"),
    DSRP_PRICE_CUSTOMER_CATEGORY("dsrp_price_customer_category", "客户价格体系"),
    STORAGE("storage", "存储条件"),
    PURCHASE_PURPOSE("dsrp_cgyt", "采购用途"),
    CERTIFICATE_AUTHORITY("certificateAuthority", "发证机关"),
    DSRP_ANALYTE_CODE("dsrp_analyte_code", "分析物"),
    DSRP_SAMPLE_CODE("dsrp_sample_code", "样本编码"),
    PRODUCT_CLASSIFICATION("CPFL", "产品分类"),
    PURCHASE_MODE("purchase_mode", "采购方式"),
    BCS_INVOICE_CATEGORY("bcs_invoice_category", "发票种类"),
    DSRP_CUSTOMER_SETTLEMENT_MODES("dsrp_customer_settlement_modes", "结算方式"),

    ;


    private static final Map<String, DictNumberEnum> MAP = new HashMap<>();

    static {
        for (DictNumberEnum item : DictNumberEnum.values()) {
            MAP.put(item.getValue(), item);
        }

    }

    private final String value;
    private final String name;

    DictNumberEnum(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public static DictNumberEnum getByValue(final String value) {
        if (value == null) {
            return null;
        }
        return MAP.get(value);
    }

    public static String getNameByValue(final String value) {
        if (value == null) {
            return null;
        }
        return MAP.get(value) == null ? null : MAP.get(value).getName();
    }

    public String getValue() {
        return value;
    }

    public String getName() {
        return name;
    }
}
