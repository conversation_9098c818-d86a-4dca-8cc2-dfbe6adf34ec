package com.yyigou.dsrp.cdc.manager.integration.uim;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.yyigou.ddc.common.exception.BusinessException;
import com.yyigou.ddc.common.message.MessageVO;
import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.ddc.services.ddc.uim.api.TenantAPI;
import com.yyigou.ddc.services.ddc.uim.enums.TenantTypeEnum;
import com.yyigou.ddc.services.ddc.uim.vo.TenantVo;
import com.yyigou.dsrp.gcs.model.api.constant.ErrorCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 描述：
 *
 * @author: myq
 * @date: 2023/12/19 9:44
 * @version: 1.0.0
 */
@Slf4j
@Service
public class TenantService {

    @Reference(check = false)
    private TenantAPI tenantAPI;


    /**
     * 判断是否是子租户
     *
     * @param enterpriseNo
     * @return
     */
    public boolean isSub(String enterpriseNo) {
        return TenantTypeEnum.SUB_ENTERPRISE.equals(getEnterpriseType(enterpriseNo));
    }


    public TenantTypeEnum getEnterpriseType(String enterpriseNo) {
        if (StringUtils.isEmpty(enterpriseNo)) {
            throw new BusinessException(ErrorCode.BUSINESS_ERROR_CODE, "租户编号不得为空");
        }


        CallResult<TenantVo> callResult = tenantAPI.getTenantByEnterpriseNo(enterpriseNo);
        if (!MessageVO.SUCCESS.equals(callResult.getCode())) {
            throw new BusinessException(com.yyigou.ddc.common.error.ErrorCode.param_invalid_code,
                    "接口【tenantAPI.getTenantByEnterpriseNo】调用异常：入参：" + enterpriseNo + " 出参：" + JSON.toJSONString(callResult));
        }

        TenantVo tenantVo = callResult.getData();
        if (tenantVo == null) {
            throw new BusinessException(ErrorCode.BUSINESS_ERROR_CODE, "未查询到租户信息");
        }
        return TenantTypeEnum.getByValue(tenantVo.getTenantType());
    }


    public String getGroupEnterpriseNo(String enterpriseNo) {
        if (StringUtils.isEmpty(enterpriseNo)) {
            throw new BusinessException(ErrorCode.BUSINESS_ERROR_CODE, "租户编号不得为空");
        }

        CallResult<TenantVo> callResult = tenantAPI.getTenantByEnterpriseNo(enterpriseNo);
        if (!MessageVO.SUCCESS.equals(callResult.getCode())) {
            throw new BusinessException(com.yyigou.ddc.common.error.ErrorCode.param_invalid_code,
                    "接口【tenantAPI.getTenantByEnterpriseNo】调用异常：入参：" + enterpriseNo + " 出参：" + JSON.toJSONString(callResult));
        }

        TenantVo tenantVo = callResult.getData();
        if (tenantVo == null) {
            throw new BusinessException(ErrorCode.BUSINESS_ERROR_CODE, "未查询到租户信息");
        }
        TenantTypeEnum typeEnum = TenantTypeEnum.getByValue(tenantVo.getTenantType());
        if (typeEnum == null) {
            throw new BusinessException(ErrorCode.BUSINESS_ERROR_CODE, "无法识别的租户类型");
        }
        String groupEnterpriseNo = enterpriseNo;
        if (TenantTypeEnum.SUB_ENTERPRISE.equals(typeEnum)) {
            groupEnterpriseNo = tenantVo.getGroupEnterpriseNo();
        }
//        log.info("返回集团租户号{} {}",groupEnterpriseNo, JSON.toJSONString(tenantVo));
        return groupEnterpriseNo;
    }

    public TenantVo getEnterpriseInfo(String enterpriseNo) {
        if (StringUtils.isEmpty(enterpriseNo)) {
            throw new BusinessException(ErrorCode.BUSINESS_ERROR_CODE, "租户编号不得为空");
        }

        CallResult<TenantVo> callResult = tenantAPI.getTenantByEnterpriseNo(enterpriseNo);
        if (!MessageVO.SUCCESS.equals(callResult.getCode())) {
            throw new BusinessException(com.yyigou.ddc.common.error.ErrorCode.param_invalid_code,
                    "接口【tenantAPI.getTenantByEnterpriseNo】调用异常：入参：" + enterpriseNo + " 出参：" + JSON.toJSONString(callResult));
        }

        TenantVo tenantVo = callResult.getData();
        if (tenantVo == null) {
            throw new BusinessException(ErrorCode.BUSINESS_ERROR_CODE, "未查询到租户信息");
        }
        return tenantVo;
    }

    public List<TenantVo> getChildEnterprise(String groupEnterpriseNo) {
        if (StringUtils.isEmpty(groupEnterpriseNo)) {
            throw new BusinessException(ErrorCode.BUSINESS_ERROR_CODE, "集团租户编号不得为空");
        }

        CallResult<List<TenantVo>> callResult = tenantAPI.getSubTenantByGroupEnterpriseNo(groupEnterpriseNo);
        if (!MessageVO.SUCCESS.equals(callResult.getCode())) {
            throw new BusinessException(com.yyigou.ddc.common.error.ErrorCode.param_invalid_code,
                    "接口【tenantAPI.getTenantByEnterpriseNo】调用异常：入参：" + groupEnterpriseNo + " 出参：" + JSON.toJSONString(callResult));
        }

        List<TenantVo> tenantVoList = callResult.getData();
        if (tenantVoList == null) {
            throw new BusinessException(ErrorCode.BUSINESS_ERROR_CODE, "未查询到租户信息");
        }
        return tenantVoList;
    }

    public List<TenantVo> findEnterprise(List<String> enterpriseNos) {
        if (CollectionUtils.isEmpty(enterpriseNos)) {
            throw new BusinessException(ErrorCode.BUSINESS_ERROR_CODE, "租户编号不得为空");
        }

        CallResult<List<TenantVo>> callResult = tenantAPI.findTenantByEnterpriseNos(enterpriseNos);
        if (!MessageVO.SUCCESS.equals(callResult.getCode())) {
            throw new BusinessException(com.yyigou.ddc.common.error.ErrorCode.param_invalid_code,
                    "接口【tenantAPI.findTenantByEnterpriseNos】调用异常：入参：" + JSON.toJSONString(enterpriseNos) + " 出参：" + JSON.toJSONString(callResult));
        }

        List<TenantVo> tenantVos = callResult.getData();
        if (tenantVos == null) {
            return new ArrayList<>();
        }
        return tenantVos;
    }


    /**
     * 根据集团租户获取子租户编号
     *
     * @param groupEnterpriseNo
     * @return
     */
    public List<String> findChildEnterpriseNo(String groupEnterpriseNo) {
        if (StringUtils.isEmpty(groupEnterpriseNo)) {
            throw new BusinessException(ErrorCode.BUSINESS_ERROR_CODE, "租户编号不得为空");
        }

        TenantTypeEnum enterpriseType = getEnterpriseType(groupEnterpriseNo);
        if (enterpriseType != TenantTypeEnum.GROUP) {
            return new ArrayList<>();
        }

        CallResult<List<TenantVo>> callResult = tenantAPI.getSubTenantByGroupEnterpriseNo(groupEnterpriseNo);
        if (!MessageVO.SUCCESS.equals(callResult.getCode())) {
            throw new BusinessException(com.yyigou.ddc.common.error.ErrorCode.param_invalid_code,
                    "接口【tenantAPI.getSubTenantByGroupEnterpriseNo】调用异常：入参：" + groupEnterpriseNo + " 出参：" + JSON.toJSONString(callResult));
        }


        List<TenantVo> subTenantList = callResult.getData();
        if (CollectionUtils.isEmpty(subTenantList)) {
            return new ArrayList<>();
        }
        return subTenantList.stream().map(TenantVo::getEnterpriseNo).collect(Collectors.toList());
    }


    /**
     * 校验是否是集团租户
     *
     * @param enterpriseNo
     */
    public void checkGroup(String enterpriseNo) {
        if (StringUtils.isEmpty(enterpriseNo)) {
            throw new BusinessException(ErrorCode.BUSINESS_ERROR_CODE, "租户编号不得为空");
        }

        if (!isGroup(enterpriseNo)) {
            throw new BusinessException(ErrorCode.BUSINESS_ERROR_CODE, "无权限进行此操作");
        }
    }

    private boolean isGroup(String enterpriseNo) {
        if (StringUtils.isEmpty(enterpriseNo)) {
            throw new BusinessException(ErrorCode.BUSINESS_ERROR_CODE, "租户编号不得为空");
        }

        CallResult<TenantVo> callResult = tenantAPI.getTenantByEnterpriseNo(enterpriseNo);
        if (!MessageVO.SUCCESS.equals(callResult.getCode())) {
            throw new BusinessException(com.yyigou.ddc.common.error.ErrorCode.param_invalid_code,
                    "接口【tenantAPI.getTenantByEnterpriseNo】调用异常：入参：" + enterpriseNo + " 出参：" + JSON.toJSONString(callResult));
        }

        TenantVo tenantVo = callResult.getData();
        if (tenantVo == null) {
            throw new BusinessException(ErrorCode.BUSINESS_ERROR_CODE, "未查询到租户信息");
        }

        return TenantTypeEnum.GROUP.getValue().equals(tenantVo.getTenantType()) || TenantTypeEnum.ENTERPRISE.getValue().equals(tenantVo.getTenantType());
    }

}
