package com.yyigou.dsrp.cdc.manager.integration.cert.req;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class CompanyCertBasicRequest implements Serializable {

    private static final long serialVersionUID = -9187827208945818530L;

    @EntityField(name = "资料编号(系统自动生成)", stringValueTypeLength = 100)
    private String certNo;

    @EntityField(name = "资料类型编码")
    private String certTypeCode;

    @EntityField(name = "资料号码（手动输入）", stringValueTypeLength = 100)
    private String certCode;

    @EntityField(name = "资料名称", stringValueTypeLength = 128)
    private String certName;

    @EntityField(name = "资料生效日期", stringValueTypeLength = 19)
    private String startTime;

    @EntityField(name = "资料失效日期", stringValueTypeLength = 19)
    private String endTime;

    @EntityField(name = "资料是否长期：0-否，1-是")
    private Integer longTerm;

    @EntityField(name = "发证机关", stringValueTypeLength = 200)
    private String issuingAuthority;

    @EntityField(name = "资料备注", stringValueTypeLength = 300)
    private String remark;

    @EntityField(name = "资料附件列表")
    private List<CompanyCertFileRequest> fileList;

    @EntityField(name = "经营范围")
    private List<CompanyCertBusinessScopeRequest> businessScopeList;

}
