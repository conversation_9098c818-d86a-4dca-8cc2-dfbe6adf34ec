package com.yyigou.dsrp.cdc.manager.integration.klp.req;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Getter;

import java.io.Serializable;

@Getter
public class KlpTokenRequestDTO implements Serializable {
    @EntityField(name = "NC登录账号（用户编码）,固定值：dms  ,NC为DMS专门建立的登录用户")
    private String usercode = "dms";

    @EntityField(name = "NC登录账号的密码（用户密码），固定值：dms@2025")
    private String pwd = "dms@2025";
}