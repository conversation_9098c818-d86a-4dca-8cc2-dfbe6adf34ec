package com.yyigou.dsrp.cdc.manager.integration.uim.response;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

@Data
public class TransactionTypeResponse {
    @EntityField(
            name = "交易类型编码",
            stringValueTypeLength = 255
    )
    private String transactionTypeCode;
    @EntityField(
            name = "交易类型名称",
            stringValueTypeLength = 255
    )
    private String transactionTypeName;
    @EntityField(
            name = "是否默认,1为默认"
    )
    private Integer defaultUsed;
    @EntityField(
            name = "备注",
            stringValueTypeLength = 255
    )
    private String remark;

}
