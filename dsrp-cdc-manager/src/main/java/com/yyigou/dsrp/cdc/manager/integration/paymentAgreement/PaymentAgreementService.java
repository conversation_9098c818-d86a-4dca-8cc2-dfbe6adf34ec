package com.yyigou.dsrp.cdc.manager.integration.paymentAgreement;

import com.alibaba.dubbo.config.annotation.Reference;
import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.ddc.services.dsrp.bdc.api.PaymentAgreementDubboAPI;
import com.yyigou.ddc.services.dsrp.bdc.dto.PaymentAgreement.PaymentAgreementDto;
import com.yyigou.ddc.services.dsrp.bdc.vo.PaymentAgreementVo;
import com.yyigou.dsrp.cdc.common.util.CommonUtil;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class PaymentAgreementService {
    @Reference(check = false)
    private PaymentAgreementDubboAPI paymentAgreementDubboAPI;

    public List<PaymentAgreementVo> findByIdList(String enterpriseNo, List<Long> idList) {
        PaymentAgreementDto paymentAgreementDto = new PaymentAgreementDto();
        paymentAgreementDto.setEnterpriseNo(enterpriseNo);
        paymentAgreementDto.setIdList(idList);
        CallResult<List<PaymentAgreementVo>> list = paymentAgreementDubboAPI.findList(paymentAgreementDto);

        return CommonUtil.parseResult(list);
    }

    public List<PaymentAgreementVo> findList(String enterpriseNo) {
        PaymentAgreementDto paymentAgreementDto = new PaymentAgreementDto();
        paymentAgreementDto.setEnterpriseNo(enterpriseNo);
        CallResult<List<PaymentAgreementVo>> list = paymentAgreementDubboAPI.findList(paymentAgreementDto);

        return CommonUtil.parseResult(list);
    }
}
