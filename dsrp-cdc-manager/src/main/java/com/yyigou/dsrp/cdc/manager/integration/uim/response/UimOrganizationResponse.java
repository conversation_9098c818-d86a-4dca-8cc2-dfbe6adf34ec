package com.yyigou.dsrp.cdc.manager.integration.uim.response;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class UimOrganizationResponse {
    /**
     * 组织no
     */
    @EntityField(name = "组织no", stringValueTypeLength = 32)
    private String orgNo;
    /**
     * 上级组织
     */
    @EntityField(name = "上级组织no", stringValueTypeLength = 32)
    private String parentOrgNo;
    /**
     * 组织编码
     */
    @EntityField(name = "组织编码", stringValueTypeLength = 32)
    private String orgCode;
    /**
     * 组织名称
     */
    @EntityField(name = "组织名称", stringValueTypeLength = 256)
    private String orgName;
    /**
     * 组织简称
     */
    @EntityField(name = "组织简称", stringValueTypeLength = 256)
    private String orgAbbreviation;
    /**
     * 组织类型
     */
    @EntityField(name = "组织类型", stringValueTypeLength = 32)
    private String orgTypeNo;
    /**
     * 组织类型名称
     */
    @EntityField(name = "组织类型名称", stringValueTypeLength = 32)
    private String orgTypeName;
    /**
     * 组织是否启用 1.启用 0.未启用
     */
    @EntityField(name = "组织是否启用 1.启用 0.未启用", stringValueTypeLength = 2)
    private Integer orgState;

    /**
     * 绑定租户
     */
    @EntityField(name = "绑定租户", stringValueTypeLength = 32)
    private String bindingEnterpriseNo;
}
