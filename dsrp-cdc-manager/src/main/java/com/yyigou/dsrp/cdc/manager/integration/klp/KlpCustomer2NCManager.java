package com.yyigou.dsrp.cdc.manager.integration.klp;

import com.alibaba.fastjson.JSON;
import com.yyigou.ddc.common.util.ValidatorUtils;
import com.yyigou.dsrp.cdc.manager.integration.klp.req.KlpAssignCustomerDTO;
import com.yyigou.dsrp.cdc.manager.integration.klp.req.KlpCustomerAssginOrgDTO;
import com.yyigou.dsrp.cdc.manager.integration.klp.req.KlpPushCustomerDTO;
import com.yyigou.dsrp.cdc.manager.integration.klp.res.KlpResCommonVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service
public class KlpCustomer2NCManager {
    @Resource
    private KlpCommonManager klpCommonManager;

    private void pushCustomerValidate(KlpPushCustomerDTO klpPushCustomerDTO) {
        // 必填项校验
        ValidatorUtils.checkEmptyThrowEx(klpPushCustomerDTO, "入参不能为空");
        ValidatorUtils.checkEmptyThrowEx(klpPushCustomerDTO.getClasstype(), "客户基本分类编码不能为空");
        ValidatorUtils.checkEmptyThrowEx(klpPushCustomerDTO.getCustprop(), "客户类型不能为空");
        ValidatorUtils.checkEmptyThrowEx(klpPushCustomerDTO.getCust_grade(), "客户等级不能为空");
        ValidatorUtils.checkEmptyThrowEx(klpPushCustomerDTO.getOwnership(), "所有制不能为空");
        ValidatorUtils.checkEmptyThrowEx(klpPushCustomerDTO.getEconomicstyle(), "经济类型不能为空");
        ValidatorUtils.checkEmptyThrowEx(klpPushCustomerDTO.getCountry(), "国家地区不能为空");
        ValidatorUtils.checkEmptyThrowEx(klpPushCustomerDTO.getTaxpayerid(), "纳税人识别号不能为空");
        ValidatorUtils.checkEmptyThrowEx(klpPushCustomerDTO.getCode(), "客户编码不能为空");
        ValidatorUtils.checkEmptyThrowEx(klpPushCustomerDTO.getName(), "客户名称不能为空");
        ValidatorUtils.checkEmptyThrowEx(klpPushCustomerDTO.getAddress(), "企业地址不能为空");

        // 范围校验
        ValidatorUtils.checkTrueThrowEx(klpPushCustomerDTO.getClasstype().length() > 20, "客户基本分类编码不能大于20");
        ValidatorUtils.checkTrueThrowEx(klpPushCustomerDTO.getCust_grade().length() > 20, "客户等级不能大于20");
        ValidatorUtils.checkTrueThrowEx(klpPushCustomerDTO.getOwnership().length() > 20, "所有制不能大于20");
        ValidatorUtils.checkTrueThrowEx(klpPushCustomerDTO.getEconomicstyle().length() > 20, "经济类型不能大于20");
        ValidatorUtils.checkTrueThrowEx(klpPushCustomerDTO.getCountry().length() > 20, "国家地区不能大于20");
        ValidatorUtils.checkTrueThrowEx(klpPushCustomerDTO.getAreacl().length() > 20, "地区分类不能大于20");
        ValidatorUtils.checkTrueThrowEx(klpPushCustomerDTO.getTaxpayerid().length() > 20, "纳税人识别号不能大于20");
        ValidatorUtils.checkTrueThrowEx(klpPushCustomerDTO.getCode().length() > 20, "客户编码不能大于20");
        ValidatorUtils.checkTrueThrowEx(klpPushCustomerDTO.getName().length() > 200, "客户名称不能大于200");
        ValidatorUtils.checkTrueThrowEx(klpPushCustomerDTO.getShortname().length() > 200, "客户简称不能大于200");
        ValidatorUtils.checkTrueThrowEx(klpPushCustomerDTO.getEname().length() > 200, "英文名称不能大于200");
        ValidatorUtils.checkTrueThrowEx(klpPushCustomerDTO.getEmail().length() > 30, "邮箱不能大于30");
        ValidatorUtils.checkTrueThrowEx(klpPushCustomerDTO.getAddress().length() > 100, "企业地址不能大于100");
        ValidatorUtils.checkTrueThrowEx(klpPushCustomerDTO.getRemark().length() > 100, "备注不能大于100");
    }


    /**
     * 推送客户到NC
     *
     * @param klpPushCustomerDTO
     * @return
     */
    public KlpResCommonVO pushCustomer(KlpPushCustomerDTO klpPushCustomerDTO) {
        pushCustomerValidate(klpPushCustomerDTO);

        String request = JSON.toJSONString(klpPushCustomerDTO);

        KlpResCommonVO klpResCommonVO = klpCommonManager.requestForKlpForNoLog(request);

        return klpResCommonVO;
    }

    private void assignCustomerValidate(KlpAssignCustomerDTO klpAssignCustomerDTO) {
        // 必填项校验
        ValidatorUtils.checkEmptyThrowEx(klpAssignCustomerDTO, "入参不能为空");
        ValidatorUtils.checkEmptyThrowEx(klpAssignCustomerDTO.getCode(), "客户编码不能为空");
        ValidatorUtils.checkEmptyThrowEx(klpAssignCustomerDTO.getAssignStatus(), "分派状态不能为空");

        // 范围校验
        if (CollectionUtils.isNotEmpty(klpAssignCustomerDTO.getAssignOrgList())) {
            for (KlpCustomerAssginOrgDTO klpCustomerAssginOrgDTO : klpAssignCustomerDTO.getAssignOrgList()) {
                ValidatorUtils.checkTrueThrowEx(null != klpCustomerAssginOrgDTO.getOrgCode() && klpCustomerAssginOrgDTO.getOrgCode().length() > 32, "分派组织编码不能大于32");
            }
        }
    }

    /**
     * 分派客户到NC
     *
     * @param klpAssignCustomerDTO
     * @return
     */
    public KlpResCommonVO assignCustomer(KlpAssignCustomerDTO klpAssignCustomerDTO) {
        assignCustomerValidate(klpAssignCustomerDTO);

        String request = JSON.toJSONString(klpAssignCustomerDTO);

        KlpResCommonVO klpResCommonVO = klpCommonManager.requestForKlpForNoLog(request);

        return klpResCommonVO;
    }
}