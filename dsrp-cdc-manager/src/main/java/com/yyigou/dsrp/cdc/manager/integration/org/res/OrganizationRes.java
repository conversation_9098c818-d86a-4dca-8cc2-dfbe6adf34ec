package com.yyigou.dsrp.cdc.manager.integration.org.res;


import com.yyigou.ddc.common.service.annotation.EntityField;
import com.yyigou.ddc.common.service.annotation.MethodType;
import com.yyigou.ddc.services.ddc.uim.vo.BusinessTypeVo;
import com.yyigou.ddc.services.ddc.uim.vo.OrgBusinessTypeVo;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class OrganizationRes {
    /**
     * 组织no
     */
    @EntityField(name = "组织no", stringValueTypeLength = 32)
    private String orgNo;
    /**
     * 租户no
     */
    @EntityField(name = "租户no", stringValueTypeLength = 32)
    private String enterpriseNo;
    /**
     * 上级组织
     */
    @EntityField(name = "上级组织", stringValueTypeLength = 32)
    private String parentOrgNo;
    /**
     * 上级组织名称
     */
    @EntityField(name = "上级组织", stringValueTypeLength = 256)
    private String parentOrgName;
    /**
     * 组织编码
     */
    @EntityField(name = "组织编码", stringValueTypeLength = 32)
    private String orgCode;
    /**
     * 组织名称
     */
    @EntityField(name = "组织名称", stringValueTypeLength = 256)
    private String orgName;
    /**
     * 组织简称
     */
    @EntityField(name = "组织简称", stringValueTypeLength = 256)
    private String orgAbbreviation;
    /**
     * 组织类型
     */
    @EntityField(name = "组织类型", stringValueTypeLength = 32)
    private String orgTypeNo;
    /**
     * 组织类型名称
     */
    @EntityField(name = "组织类型名称", stringValueTypeLength = 32)
    private String orgTypeName;
    /**
     * 组织是否启用 1.启用 0.未启用
     */
    @EntityField(name = "组织是否启用 1.启用 0.未启用", stringValueTypeLength = 2)
    private Integer orgState;
    /**
     * 是否是部门 1.是 0.否
     */
    @EntityField(name = "是否是部门 1.是 0.否", stringValueTypeLength = 2)
    private Integer deptFlag;

    /**
     * 是否是子租户 1.是 0.否
     */
    @EntityField(name = "是否是子租户 1.是 0.否", requiredMetTypes = MethodType.ADD, stringValueTypeLength = 2)
    private String subTenantFlag;
    /**
     * 是否有人力资源属性 1.是 0.否
     */
    @EntityField(name = "是否有人力资源属性 1.是 0.否", stringValueTypeLength = 2)
    private Integer humanFlag;
    /**
     * 负责人-员工no
     */
    @EntityField(name = "负责人-员工no", stringValueTypeLength = 32)
    private String orgResponsible;

    @EntityField(name = "法定代表人", stringValueTypeLength = 50)
    private String legalPerson;
    /**
     * 负责人-员工名称
     */
    @EntityField(name = "负责人-名称", stringValueTypeLength = 32)
    private String orgResponsibleName;
    /**
     * 分管领导-员工no
     */
    @EntityField(name = "分管领导-员工no", stringValueTypeLength = 32)
    private String orgLeader;
    /**
     * 分管领导-员工name
     */
    @EntityField(name = "分管领导-name", stringValueTypeLength = 32)
    private String orgLeaderName;
    /**
     * 纳税人识别号
     */
    @EntityField(name = "纳税人识别号", stringValueTypeLength = 32)
    private String taxpayerIdentityNumber;
    /**
     * 纳税人名称
     */
    @EntityField(name = "纳税人名称", stringValueTypeLength = 256)
    private String taxpayerIdentityName;
    /**
     * 曾用纳税人识别号
     */
    @EntityField(name = "曾用纳税人识别号", stringValueTypeLength = 32)
    private String oldTaxpayerIdentityNumber;
    /**
     * 曾用纳税人名称
     */
    @EntityField(name = "曾用纳税人名称", stringValueTypeLength = 256)
    private String oldTaxpayerIdentityName;
    /**
     * 公司地址
     */
    @EntityField(name = "公司地址", stringValueTypeLength = 512)
    private String companyAddress;
    /**
     * 公司电话
     */
    @EntityField(name = "公司电话", stringValueTypeLength = 16)
    private String companyTelephone;
    /**
     * 联系人
     */
    @EntityField(name = "联系人", stringValueTypeLength = 128)
    private String contacts;
    /**
     * 描述
     */
    @EntityField(name = "描述", stringValueTypeLength = 512)
    private String describe;
    /**
     * 纳税人类型
     */
    @EntityField(name = "纳税人类型", stringValueTypeLength = 16)
    private Integer taxpayerType;

    /**
     * 绑定租户
     */
    @EntityField(name = "绑定租户", stringValueTypeLength = 32)
    private String bindingEnterpriseNo;
    /**
     * 组织业务类型列表
     */
    @EntityField(name = "组织业务类型列表", requiredMetTypes = MethodType.ADD)
    private List<OrgBusinessTypeVo> orgBusinessTypes;

    /**
     * 排序值
     */
    @EntityField(name = "排序值", stringValueTypeLength = 10)
    private Integer sortNum;
    /**
     * 组织层级
     */
    @EntityField(name = "组织层级", stringValueTypeLength = 2)
    private Integer level;
    /**
     * 组织业务类型列表
     */
    @EntityField(name = "组织业务类型列表")
    private List<BusinessTypeVo> businessTypeVos;
    /**
     * 操作人no
     */
    @EntityField(name = "操作人no", stringValueTypeLength = 32)
    private String operNo;
    /**
     * 数据创建时间
     */
    @EntityField(name = "数据创建时间", stringValueTypeLength = 19)
    private String operTime;
    /**
     * 最后操作时间戳
     */
    @EntityField(name = "最后操作时间戳", stringValueTypeLength = 0)
    private Date opTimestamp;
    /**
     * 数据记录版本
     */
    @EntityField(name = "数据记录版本", stringValueTypeLength = 11)
    private Integer opRevsion;
    /**
     * 部门所属人力上级组织
     */
    @EntityField(name = "部门所属人力上级组织", stringValueTypeLength = 32)
    private String deptParentOrgNo;
    /**
     * 部门所属人力上级组织名称
     */
    @EntityField(name = "部门所属人力上级组织", stringValueTypeLength = 32)
    private String deptParentOrgName;
    /**
     * 部门上级部门(需是人力上级组织下的部门)
     */
    @EntityField(name = "部门上级部门", stringValueTypeLength = 32)
    private String deptParentDeptNo;
    /**
     * 部门上级部门(需是人力上级组织下的部门)名称
     */
    @EntityField(name = "部门上级部门名称", stringValueTypeLength = 32)
    private String deptParentDeptName;

    @EntityField(name = "业务单元分组id")
    private Long    orgGroupId;
}
