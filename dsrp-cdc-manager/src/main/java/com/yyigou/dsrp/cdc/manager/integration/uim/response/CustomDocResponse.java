package com.yyigou.dsrp.cdc.manager.integration.uim.response;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

@Data
public class CustomDocResponse {
    @EntityField(
            name = "id"
    )
    private String id;
    @EntityField(
            name = "id"
    )
    private Long longId;
    @EntityField(
            name = "本级编码"
    )
    private String code;
    @EntityField(
            name = "父级编码"
    )
    private String parentCode;
    @EntityField(
            name = "档案项编码"
    )
    private String docItemCode;
    @EntityField(
            name = "档案项名称"
    )
    private String docItemName;
    @EntityField(
            name = "节点深度"
    )
    private Integer deep;
    @EntityField(
            name = "简称",
            stringValueTypeLength = 255
    )
    private String shortName;
    @EntityField(
            name = "备注",
            stringValueTypeLength = 500
    )
    private String remark;

    @EntityField(
            name = "启用状态:1启用2停用",
            stringValueTypeLength = 1
    )
    private Integer enableStatus;
}
