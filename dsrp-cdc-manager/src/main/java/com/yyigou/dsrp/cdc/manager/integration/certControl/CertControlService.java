package com.yyigou.dsrp.cdc.manager.integration.certControl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.yyigou.ddc.common.message.MessageUtil;
import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.ddc.services.dsrp.bdc.vo.CertControlVo;
import com.yyigou.ddc.services.dsrp.gsp.api.CertControlTypeAPI;
import com.yyigou.ddc.services.dsrp.gsp.api.QualificationControlBillRecordAPI;
import com.yyigou.ddc.services.dsrp.gsp.vo.CertControlTypeVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class CertControlService {
    @Reference(check = false)
    private QualificationControlBillRecordAPI qualificationControlBillRecordAPI;
    @Reference(check = false)
    private CertControlTypeAPI certControlTypeAPI;

    public List<CertControlVo> checkCustomerSupplierCanSave(String enterpriseNo, String companyNo, Long controlId, List<String> certTypeCodeList) {
        final CallResult<List<CertControlVo>> callResult = qualificationControlBillRecordAPI.checkCustomerSupplierCanSave(enterpriseNo, companyNo, controlId, certTypeCodeList);
        return MessageUtil.ensureCallResultSuccess(callResult);
    }

    public List<CertControlTypeVo> getIdList(String enterpriseNo, List<Long> idList) {
        final CallResult<List<CertControlTypeVo>> callResult = certControlTypeAPI.getListNoSession(idList, enterpriseNo);
        return MessageUtil.ensureCallResultSuccess(callResult);
    }

    public CertControlTypeVo getId(String enterpriseNo, Long id) {
        final CallResult<CertControlTypeVo> callResult = certControlTypeAPI.getNoSession(id, enterpriseNo);
        return MessageUtil.ensureCallResultSuccess(callResult);
    }

}
