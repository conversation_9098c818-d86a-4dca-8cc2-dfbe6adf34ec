package com.yyigou.dsrp.cdc.manager.integration.org;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.yyigou.ddc.common.error.ErrorCode;
import com.yyigou.ddc.common.exception.BusinessException;
import com.yyigou.ddc.common.message.MessageVO;
import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.ddc.services.ddc.uim.api.OrganizationAPI;
import com.yyigou.ddc.services.ddc.uim.api.TenantAPI;
import com.yyigou.ddc.services.ddc.uim.dto.OrganizationDto;
import com.yyigou.ddc.services.ddc.uim.dto.OrganizationTreeDto;
import com.yyigou.ddc.services.ddc.uim.enums.TenantTypeEnum;
import com.yyigou.ddc.services.ddc.uim.vo.OrganizationExtandVo;
import com.yyigou.ddc.services.ddc.uim.vo.OrganizationTreeVo;
import com.yyigou.ddc.services.ddc.uim.vo.OrganizationVo;
import com.yyigou.ddc.services.ddc.uim.vo.TenantVo;
import com.yyigou.dsrp.cdc.common.util.CommonUtil;
import com.yyigou.dsrp.cdc.manager.integration.org.res.OrganizationRes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * uim-租户相关Service
 *
 * @author: Moore
 * @date: 2024/7/13 15:25
 * @version: 1.0.0
 */
@Service
@Slf4j
public class UimTenantService {

    @Reference(check = false)
    private TenantAPI tenantAPI;
    @Reference(check = false)
    private OrganizationAPI organizationAPI;

    /**
     * 根据租户编号查询租户类型
     *
     * @param enterpriseNo
     * @return: {@link TenantTypeEnum}
     */
    public TenantTypeEnum getTenantType(String enterpriseNo) {
        if (StringUtils.isEmpty(enterpriseNo)) {
            throw new BusinessException(ErrorCode.object_null_code, "租户编号不得为空");
        }

        CallResult<TenantVo> callResult = tenantAPI.getTenantByEnterpriseNo(enterpriseNo);
        if (!MessageVO.SUCCESS.equals(callResult.getCode())) {
            throw new BusinessException(com.yyigou.ddc.common.error.ErrorCode.param_invalid_code,
                    "接口【tenantAPI.getTenantByEnterpriseNo】调用异常：入参：" + enterpriseNo + " 出参：" + JSON.toJSONString(callResult));
        }

        TenantVo tenantVo = callResult.getData();
        if (tenantVo == null) {
            throw new BusinessException(ErrorCode.val_null_code, "未查询到租户信息");
        }
        return TenantTypeEnum.getByValue(tenantVo.getTenantType());
    }

    /**
     * 获取组织信息
     *
     * @param enterpriseNo
     * @param groupEnterpriseNo
     * @return
     */
    public OrganizationTreeVo getOrganizationTreeVo(String enterpriseNo, String groupEnterpriseNo) {
        OrganizationTreeDto organizationTreeDto = new OrganizationTreeDto();
        organizationTreeDto.setEnterpriseNo(groupEnterpriseNo);
        CallResult<List<OrganizationTreeVo>> callResult = organizationAPI.findGroupOrgTreeListForDubbo(organizationTreeDto);
        List<OrganizationTreeVo> filterVoList = CommonUtil.parseResult(callResult);
        //筛选启用并且绑定了租户的叶子节点的数据
        if (CollectionUtils.isEmpty(filterVoList)) {
            return new OrganizationTreeVo();
        }
        List<OrganizationTreeVo> result = new ArrayList<>();
        getDataNoTre(filterVoList, result);

        List<OrganizationTreeVo> collect = result.stream().filter(x -> StringUtils.isNotBlank(x.getBindingEnterpriseNo()) && x.getBindingEnterpriseNo().equals(enterpriseNo)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(collect)) {
            return new OrganizationTreeVo();
        }
        OrganizationTreeVo organizationTreeVo = collect.get(0);
        return organizationTreeVo;
    }

    /**
     * 获取组织信息
     *
     * @param enterpriseNo
     * @return
     */
    public List<OrganizationTreeVo> getOrganizationTreeList(String enterpriseNo) {
        OrganizationTreeDto organizationTreeDto = new OrganizationTreeDto();
        organizationTreeDto.setEnterpriseNo(enterpriseNo);
        CallResult<List<OrganizationTreeVo>> callResult = organizationAPI.findGroupOrgTreeListForDubbo(organizationTreeDto);
        List<OrganizationTreeVo> filterVoList = CommonUtil.parseResult(callResult);
        //筛选启用并且绑定了租户的叶子节点的数据
        if (CollectionUtils.isEmpty(filterVoList)) {
            return new ArrayList<>();
        }
        List<OrganizationTreeVo> result = new ArrayList<>();
        getDataNoTre(filterVoList, result);
        List<OrganizationTreeVo> collect = result.stream().filter(x -> StringUtils.isNotBlank(x.getBindingEnterpriseNo())).collect(Collectors.toList());
        return collect;
    }


    /**
     * 获取已授权的企业信息
     *
     * @param enterpriseNo 租户信息
     * @param employerNo   人员信息
     * @return
     */
    public List<OrganizationVo> getAuthEnterpriseList(String enterpriseNo, String userNo, String employerNo) {
        OrganizationDto params = new OrganizationDto();
        params.setEnterpriseNo(enterpriseNo);
        params.setEmployeeNo(employerNo);
        params.setUserNo(userNo);
        params.setBindTenantFlag(1);
        CallResult<List<OrganizationVo>> callResult = organizationAPI.findList(params);
        List<OrganizationVo> filterVoList = CommonUtil.parseResult(callResult);
        //筛选启用并且绑定了租户的叶子节点的数据
        if (CollectionUtils.isEmpty(filterVoList)) {
            return new ArrayList<>();
        }
        List<OrganizationVo> collect = filterVoList.stream().filter(x -> StringUtils.isNotBlank(x.getBindingEnterpriseNo())).collect(Collectors.toList());
        return collect;
    }

    /**
     * 递归组织信息
     *
     * @param organizations 数据
     * @param collections   收集器
     */
    private void getDataNoTre(List<OrganizationTreeVo> organizations, List<OrganizationTreeVo> collections) {
        if (CollectionUtils.isNotEmpty(organizations)) {
            organizations.forEach(t -> {
                OrganizationTreeVo uimOrganizationResponse = new OrganizationTreeVo();
                BeanUtils.copyProperties(t, uimOrganizationResponse);
                collections.add(uimOrganizationResponse);
                getDataNoTre(t.getChildOrgs(), collections);
            });
        }
    }

    /**
     * 根绑定租户编号查询业务单元
     * @param enterpriseNo
     * @param bindEnterpriseNoList
     * @return
     */
    public List<OrganizationRes> findOrgByBindEnterpriseNoList(String enterpriseNo, List<String> bindEnterpriseNoList) {
        Assert.isTrue(org.apache.commons.lang.StringUtils.isNotBlank(enterpriseNo), "企业编号不能为空");
        Assert.notEmpty(bindEnterpriseNoList, "绑定租户编号不能为空");


        CallResult<Map<String,TenantVo>> callResult = tenantAPI.findGroupEnterpriseByEnterpriseNos(bindEnterpriseNoList);
        if (!MessageVO.SUCCESS.equals(callResult.getCode())) {
            throw new BusinessException(ErrorCode.param_invalid_code, "接口【tenantAPI.findGroupEnterpriseByEnterpriseNos】调用异常 入参："
                    + JSON.toJSONString(bindEnterpriseNoList) + " 出参：" + JSON.toJSONString(callResult));
        }


        if(callResult.getData() == null){
            return new ArrayList<>();
        }


        return callResult.getData().entrySet().stream().map(x->{
            OrganizationRes organizationRes = new OrganizationRes();
            organizationRes.setOrgCode(x.getValue().getOrgCode());
            organizationRes.setOrgName(x.getValue().getOrgName());
            organizationRes.setOrgNo(x.getValue().getOrgNo());
            organizationRes.setBindingEnterpriseNo(x.getKey());
            return organizationRes;

        }).collect(Collectors.toList());

    }


    /**
     * 根据组织编号获取组织详情信息
     * @param orgNo
     * @return
     */
    public OrganizationExtandVo getOrganizationByOrgNo(String orgNo) {
        CallResult<OrganizationExtandVo> callResult = organizationAPI.getOrgDetailNoSession(orgNo);
        return CommonUtil.parseResult(callResult);
    }

    /**
     * 根据集团租户编号查询组织信息
     *
     * @param groupEnterpriseNo
     * @return
     */
    public List<OrganizationVo> findOrgListByGroupEnterpriseNo(String groupEnterpriseNo) {
        OrganizationDto organizationDto = new OrganizationDto();
        organizationDto.setEnterpriseNo(groupEnterpriseNo);
        organizationDto.setBindTenantFlag(1); //返回绑定组织的租户

        log.info("findOrgListByGroupEnterpriseNoReq:{}", JSON.toJSONString(organizationDto));

        CallResult<List<OrganizationVo>> listNoAuth = organizationAPI.findListNoAuth(organizationDto);

        log.info("findOrgListByGroupEnterpriseNoResp:{}", JSON.toJSONString(listNoAuth));

        List<OrganizationVo> organizationVos = CommonUtil.parseResult(listNoAuth);

        if (CollectionUtils.isEmpty(organizationVos)) {
            return new ArrayList<>();
        }

        return organizationVos;
    }

}
