package com.yyigou.dsrp.cdc.manager.integration.uim;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.dubbo.rpc.RpcContext;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.yyigou.ddc.common.message.MessageUtil;
import com.yyigou.ddc.common.service.SessionUser;
import com.yyigou.ddc.common.util.SessionUtils;
import com.yyigou.ddc.services.ddc.uap.api.ufm.TransactionTypeAPI;
import com.yyigou.ddc.services.ddc.uap.domain.dto.ufm.TransTypeQueryDTO;
import com.yyigou.ddc.services.ddc.uap.domain.vo.ufm.TransactionTypeVO;
import com.yyigou.dsrp.cdc.manager.integration.uim.response.TransactionTypeResponse;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.List;

import static com.yyigou.ddc.common.service.Constants.CALL_CONTEXT_SESSION_KEY;

@Component
public class TransactionTypeService {

    @Reference(check = false)
    private TransactionTypeAPI transactionTypeAPI;


    public List<TransactionTypeResponse> getTransactionTypeList(String enterpriseNo, String billNo) {
        List<TransactionTypeResponse> transactionTypeResponses = Lists.newArrayList();
        // 调用注入会话
        SessionUser sessionUser = new SessionUser();
        sessionUser.setEnterpriseNo(enterpriseNo);
        RpcContext.getContext().setAttachment(CALL_CONTEXT_SESSION_KEY, JSONObject.toJSONString(SessionUtils.simplySessionUser(sessionUser)));
        TransTypeQueryDTO dto = new TransTypeQueryDTO();
        dto.setFilterBillNoList(Lists.newArrayList(billNo));
        List<TransactionTypeVO> transactionTypeVOS = MessageUtil.ensureCallResultSuccess(transactionTypeAPI.findTransTypeListWithConfig(dto));
        if (CollectionUtils.isNotEmpty(transactionTypeVOS)) {
            transactionTypeVOS.forEach(t -> {
                TransactionTypeResponse response = new TransactionTypeResponse();
                BeanUtils.copyProperties(t, response);
                transactionTypeResponses.add(response);
            });

        }

        return transactionTypeResponses;
    }

}
