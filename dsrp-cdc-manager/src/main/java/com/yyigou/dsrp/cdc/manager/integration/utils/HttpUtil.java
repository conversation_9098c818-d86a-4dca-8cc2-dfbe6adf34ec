package com.yyigou.dsrp.cdc.manager.integration.utils;

import com.yyigou.ddc.common.util.StringUtil;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLConnection;

public class HttpUtil {
    private static final Log log = LogFactory.getLog(com.yyigou.dsrp.cdc.manager.integration.utils.HttpUtil.class);
    private static final String defalue_OutTime = "5000";
    private static final String contentType = null;

    public HttpUtil() {
    }

    public static String getStringFromBufferedReader(HttpServletRequest request) {
        String bodyData = "";

        try {
            byte[] buffer = new byte[8192];
            ServletInputStream sis = request.getInputStream();
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            int bLen = 0;

            while ((bLen = sis.read(buffer)) > 0) {
                baos.write(buffer, 0, bLen);
            }

            bodyData = new String(baos.toByteArray());
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }

        return bodyData;
    }

    public static String doHttpPost(String strUrl, String sContent, String charsetName) {
        return doHttpPost(strUrl, sContent, charsetName, contentType, "5000");
    }

    public static String doHttpPostXml(String strUrl, String sContent, String charsetName, String timeOut) {
        return doHttpPost(strUrl, sContent, charsetName, "text/xml", timeOut);
    }

    public static String doHttpPost(String strUrl, String sContent, String charsetName, String contentType, String timeOut) {
        return doSimpleHttpPost(strUrl, sContent, charsetName, contentType, timeOut, null);
    }

    public static String doHttpPostAuthor(String strUrl, String sContent, String charsetName, String contentType, String timeOut, String authorization) {
        return doSimpleHttpPost(strUrl, sContent, charsetName, contentType, timeOut, authorization);
    }

    public static String doSimpleHttpPost(String strUrl, String sContent, String charsetName, String contentType, String timeOut, String authorization) {
        String strReturnVal = "";
        HttpURLConnection httpURLConnection = null;

        try {
            URL url = new URL(strUrl);
            URLConnection urlcn = url.openConnection();
            httpURLConnection = (HttpURLConnection) urlcn;
            if (!StringUtil.isEmpty(contentType)) {
                httpURLConnection.setRequestProperty("Content-type", contentType);
            }

            if (!StringUtil.isEmpty(authorization)) {
                httpURLConnection.setRequestProperty("Authorization", authorization);
            }

            httpURLConnection.setRequestMethod("POST");
            urlcn.setReadTimeout(Integer.parseInt(timeOut));
            urlcn.setConnectTimeout(Integer.parseInt(timeOut));
            System.setProperty("sun.net.client.defaultConnectTimeout", timeOut);
            System.setProperty("sun.net.client.defaultReadTimeout", timeOut);
            httpURLConnection.setDoOutput(true);
            httpURLConnection.setDoInput(true);
            OutputStreamWriter osw = new OutputStreamWriter(httpURLConnection.getOutputStream(), charsetName);
            osw.write(sContent);
            osw.close();
        } catch (Exception e) {
            e.printStackTrace();
            log.error("post error:" + e.getMessage());
            return strReturnVal;
        }

        try {
            InputStreamReader isr = new InputStreamReader(httpURLConnection.getInputStream(), charsetName);

            while (true) {
                char[] c = new char[10];
                int ilen = isr.read(c);
                if (ilen <= 0) {
                    isr.close();
                    return strReturnVal;
                }

                String sTemp = new String(c, 0, ilen);
                strReturnVal = strReturnVal.concat(sTemp);
            }
        } catch (Exception e) {
            log.error("post error:" + e.getMessage());
            return strReturnVal;
        }
    }

    public static String doSimpleHttpPostForKlp(String strUrl, String sContent, String charsetName, String contentType, String timeOut, String authorization
            , String uapToken) {
        String strReturnVal = "";
        HttpURLConnection httpURLConnection = null;

        try {
            URL url = new URL(strUrl);
            URLConnection urlcn = url.openConnection();
            httpURLConnection = (HttpURLConnection) urlcn;
            if (!StringUtil.isEmpty(contentType)) {
                httpURLConnection.setRequestProperty("Content-type", contentType);
            }

            if (!StringUtil.isEmpty(authorization)) {
                httpURLConnection.setRequestProperty("Authorization", authorization);
            }

            httpURLConnection.setRequestProperty("uap_dataSource", "NCC2105");

            httpURLConnection.setRequestProperty("uap_usercode", "dms");

            if (!StringUtil.isEmpty(uapToken)) {
                httpURLConnection.setRequestProperty("uap_token", uapToken);
            }

            httpURLConnection.setRequestMethod("POST");
            urlcn.setReadTimeout(Integer.parseInt(timeOut));
            urlcn.setConnectTimeout(Integer.parseInt(timeOut));
            System.setProperty("sun.net.client.defaultConnectTimeout", timeOut);
            System.setProperty("sun.net.client.defaultReadTimeout", timeOut);
            httpURLConnection.setDoOutput(true);
            httpURLConnection.setDoInput(true);
            OutputStreamWriter osw = new OutputStreamWriter(httpURLConnection.getOutputStream(), charsetName);
            osw.write(sContent);
            osw.close();
        } catch (Exception e) {
            e.printStackTrace();
            log.error("post error:" + e.getMessage());
            return strReturnVal;
        }

        try {
            InputStreamReader isr = new InputStreamReader(httpURLConnection.getInputStream(), charsetName);

            while (true) {
                char[] c = new char[10];
                int ilen = isr.read(c);
                if (ilen <= 0) {
                    isr.close();
                    return strReturnVal;
                }

                String sTemp = new String(c, 0, ilen);
                strReturnVal = strReturnVal.concat(sTemp);
            }
        } catch (Exception e) {
            log.error("post error:" + e.getMessage());
            return strReturnVal;
        }
    }
}
