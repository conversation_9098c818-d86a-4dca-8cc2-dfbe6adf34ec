package com.yyigou.dsrp.cdc.manager.integration.cert.req;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.util.List;

@Data
public class CompanyCertUpsertRequest extends CompanyCertSourceRequest {
    private static final long serialVersionUID = 398519124463649028L;

    @EntityField(name = "企业证照信息")
    private List<CompanyCertBasicRequest> companyCertList;
}
