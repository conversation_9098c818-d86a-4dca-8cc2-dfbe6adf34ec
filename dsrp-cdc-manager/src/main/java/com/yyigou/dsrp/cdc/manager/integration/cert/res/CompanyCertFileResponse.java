package com.yyigou.dsrp.cdc.manager.integration.cert.res;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;

@Data
public class CompanyCertFileResponse implements Serializable {
    private Long id;

    @EntityField(name = "文件地址", stringValueTypeLength = 512)
    private String filePath;

    @EntityField(name = "文件名称", stringValueTypeLength = 512)
    private String fileName;

    @EntityField(name = "1:删除 0：未删除")
    private Integer deleted;

}
