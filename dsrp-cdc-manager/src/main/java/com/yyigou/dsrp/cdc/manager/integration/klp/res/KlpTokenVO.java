package com.yyigou.dsrp.cdc.manager.integration.klp.res;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;

@Data
public class KlpTokenVO implements Serializable {
    @EntityField(name = "NC登录账号（用户编码）返回值等于传入值")
    private String uap_usercode;

    @EntityField(name = "NC登录系统数据源 返回值等于传入值")
    private String uap_dataSource;

    @EntityField(name = "token值")
    private String uap_token;
}