package com.yyigou.dsrp.cdc.manager.integration.uim;

import com.alibaba.dubbo.config.annotation.Reference;
import com.yyigou.ddc.common.exception.BusinessException;
import com.yyigou.ddc.common.message.MessageUtil;
import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.ddc.services.ddc.uim.api.EmployeeAPI;
import com.yyigou.ddc.services.ddc.uim.dto.EmployeeDto;
import com.yyigou.ddc.services.ddc.uim.vo.EmployeeVo;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class EmployeeService {
    @Reference(check = false)
    private EmployeeAPI employeeAPI;

    public List<EmployeeVo> getEmployeeList(String enterpriseNo, List<String> employeeNos) {
        //获取员工信息
        EmployeeDto employeeDto = new EmployeeDto();
        employeeDto.setEnterpriseNo(enterpriseNo);
        employeeDto.setEmployeeNos(employeeNos);
        CallResult<List<EmployeeVo>> callResult = employeeAPI.findListByNoSession(employeeDto);
        List<EmployeeVo> employeeVoList = MessageUtil.ensureCallResultSuccess(callResult);
        return employeeVoList;
    }

    public List<EmployeeVo> getEmployeeByNames(String enterpriseNo,  List<String>  employeeNames) {
        EmployeeDto employeeDto = new EmployeeDto();
        employeeDto.setEnterpriseNo(enterpriseNo);
        employeeDto.setUserNames(employeeNames);
        CallResult<List<EmployeeVo>> callResult = employeeAPI.findListByNoSession(employeeDto);

        return MessageUtil.ensureCallResultSuccess(callResult);
    }
}
