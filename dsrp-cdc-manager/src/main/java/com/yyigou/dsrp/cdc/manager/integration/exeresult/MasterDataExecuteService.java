package com.yyigou.dsrp.cdc.manager.integration.exeresult;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.yyigou.ddc.common.exception.BusinessException;
import com.yyigou.ddc.common.message.MessageVO;
import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.dsrp.gcs.client.executeRecord.ExecuteRecordClient;
import com.yyigou.dsrp.gcs.client.executeRecord.request.BatchSaveExecuteRecordRequest;
import com.yyigou.dsrp.gcs.client.executeRecord.request.SaveExecuteRecordRequest;
import com.yyigou.dsrp.gcs.client.executeRecord.request.UpdateExecuteRecordRequest;
import com.yyigou.dsrp.gcs.client.executeRecord.response.ExecuteRecordResponse;
import com.yyigou.dsrp.gcs.model.api.constant.ErrorCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class MasterDataExecuteService {
    @Reference(check = false)
    private ExecuteRecordClient executeRecordClient;

    public List<ExecuteRecordResponse> saveExecuteRecordNoExecute(BatchSaveExecuteRecordRequest batchSaveExecuteRecordRequest) {
        CallResult<List<ExecuteRecordResponse>> callResult = executeRecordClient.batchSaveExecuteRecordNoExecute(batchSaveExecuteRecordRequest);
        if (!MessageVO.SUCCESS.equals(callResult.getCode())) {
            log.error("接口访问错误executeRecordClient.batchSaveExecuteRecordNoExecute,batchSaveExecuteRecordRequest={}", JSON.toJSONString(batchSaveExecuteRecordRequest));
            throw new BusinessException(ErrorCode.BUSINESS_ERROR_CODE, "executeRecordClient.batchSaveExecuteRecord异常：" + callResult.getMessage());
        }
        log.warn("saveExecuteRecordNoExecute {} {}" , JSON.toJSONString(batchSaveExecuteRecordRequest) , JSON.toJSONString(callResult.getData()));
        return callResult.getData();
    }


    public Integer saveExecuteRecordNoExecute(SaveExecuteRecordRequest batchSaveExecuteRecordRequest) {
        CallResult<Integer> result = executeRecordClient.saveExecuteRecordNoExecute(batchSaveExecuteRecordRequest);
        if (!MessageVO.SUCCESS.equals(result.getCode())) {
            log.error("接口访问错误executeRecordClient.saveExecuteRecordNoExecute,batchSaveExecuteRecordRequest={}", JSON.toJSONString(batchSaveExecuteRecordRequest));
            throw new BusinessException(ErrorCode.BUSINESS_ERROR_CODE, "executeRecordClient.saveExecuteRecordNoExecute异常：" + result.getMessage());
        }
        log.warn("saveExecuteRecordNoExecute {} {}" , JSON.toJSONString(batchSaveExecuteRecordRequest), result.getData());
        return result.getData();
    }

    public void updateExecuteRecord(UpdateExecuteRecordRequest updateExecuteRecordRequest) {
        CallResult<Boolean> result = executeRecordClient.updateExecuteRecord(updateExecuteRecordRequest);
        log.warn("updateExecuteRecord {} {}" , JSON.toJSONString(updateExecuteRecordRequest), JSON.toJSONString(result));
        if (!MessageVO.SUCCESS.equals(result.getCode())) {
            throw new BusinessException(ErrorCode.BUSINESS_ERROR_CODE, "executeRecordClient.updateExecuteRecord异常：" + result.getMessage());
        }
    }

    public List<ExecuteRecordResponse> saveExecuteRecordListNoExecute(List<SaveExecuteRecordRequest> saveExecuteRecordList) {
        CallResult<List<ExecuteRecordResponse>> callResult = executeRecordClient.saveExecuteRecordListNoExecute(saveExecuteRecordList);
        if (!MessageVO.SUCCESS.equals(callResult.getCode())) {
            log.error("接口访问错误executeRecordClient.saveExecuteRecordListNoExecute,saveExecuteRecordList={}", JSON.toJSONString(saveExecuteRecordList));
            throw new BusinessException(ErrorCode.BUSINESS_ERROR_CODE, "executeRecordClient.updateExecuteRecord异常：" + callResult.getMessage());
        }
        log.warn("saveExecuteRecordListNoExecute {} {}" , JSON.toJSONString(saveExecuteRecordList), JSON.toJSONString(callResult.getData()));
        return callResult.getData();
    }
}
