### set log levels ###
log4j.rootLogger=${logger.level:info},stdout,D,E,network

### \u8F93\u51FA\u5230\u63A7\u5236\u53F0 ###
log4j.appender.stdout = org.apache.log4j.ConsoleAppender
log4j.appender.stdout.Target = System.out
log4j.appender.stdout.layout = org.apache.log4j.PatternLayout
log4j.appender.stdout.layout.ConversionPattern =  [%5p] [dsrp-cdc] %d{yyyy-MM-dd HH\:mm\:ss}\: %-4r [%t] ( %F,%L ) - %m%n

### \u8F93\u51FA\u5230\u65E5\u5FD7\u6587\u4EF6 ###
log4j.appender.D = org.apache.log4j.RollingFileAppender
log4j.appender.D.File = /ddc/logs/service-dsrp-cdc/service.log
log4j.appender.D.Append = true
log4j.appender.D.Threshold = DEBUG
log4j.appender.D.layout = org.apache.log4j.PatternLayout
log4j.appender.D.layout.ConversionPattern = [%5p] [dsrp-cdc] %d{yyyy-MM-dd HH\:mm\:ss}\: %-4r [%t] ( %F,%L ) - %m%n
log4j.appender.D.MaxFileSize = 100MB

### \u4FDD\u5B58\u5F02\u5E38\u4FE1\u606F\u5230\u5355\u72EC\u6587\u4EF6 ###
log4j.appender.E = org.apache.log4j.RollingFileAppender
log4j.appender.E.File = /ddc/logs/service-dsrp-cdc/error.log
log4j.appender.E.Append = true
log4j.appender.E.Threshold = ERROR
log4j.appender.E.layout = org.apache.log4j.PatternLayout
log4j.appender.E.layout.ConversionPattern = [%5p] [dsrp-cdc] %d{yyyy-MM-dd HH\:mm\:ss}\: %-4r [%t] ( %F,%L ) - %m%n
log4j.appender.E.MaxFileSize = 100MB

#log4j.appender.network = com.yyigou.ddc.common.appender.TraceSocketAppender
#log4j.appender.network.Threshold = WARN
#log4j.appender.network.Port=${logstash.port:4561}
#log4j.appender.network.RemoteHost=${logstash.ip:127.0.0.1}
#log4j.appender.network.ReconnectionDelay = 10000
#log4j.appender.network.Application = service-dsrp-cdc

# \u8BBE\u7F6E\u67D0\u4E9B\u7C7B\u7684\u65E5\u5FD7\u7EA7\u522B
log4j.logger.org.apache.activemq.transport.AbstractInactivityMonitor=info
log4j.logger.org.apache.activemq.transport=INFO
# zk
log4j.logger.org.apache.zookeeper=INFO
log4j.logger.org.apache.zookeeper.ClientCnxn=warn
log4j.logger.org.I0Itec.zkclient=INFO
# \u7981\u7528kafka\u5927\u91CF\u7684warn\u4FE1\u606F
log4j.logger.org.apache.kafka.clients.NetworkClient=error
# \u7981\u7528dubbo\u7684\u4E00\u4E9Berror
log4j.logger.com.alibaba.dubbo.common.Version=fatal
log4j.logger.com.alibaba.dubbo.remoting.transport.AbstractClient=error
log4j.logger.com.alibaba.dubbo.registry.support.AbstractRegistry=error
log4j.logger.com.alibaba.dubbo.rpc.protocol.dubbo.DubboProtocol=error
log4j.logger.com.alibaba.dubbo.remoting.exchange.support.header=INFO
log4j.logger.com.alibaba.dubbo.remoting.exchange.support.DefaultFuture=error
log4j.logger.com.alibaba.dubbo.registry.zookeeper.ZookeeperRegistry=WARN
# jdbc log
log4j.logger.org.springframework.jdbc.datasource.DataSourceTransactionManager=INFO