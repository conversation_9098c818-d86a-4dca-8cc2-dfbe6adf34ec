<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:dubbo="http://dubbo.apache.org/schema/dubbo"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd http://dubbo.apache.org/schema/dubbo http://dubbo.apache.org/schema/dubbo/dubbo.xsd">



    <bean id="propertyConfigurer" class="com.yyigou.ddc.common.zkconfig.ConfigServerPlaceholderConfigurer">
        <property name="locations">
            <list>
                <value>classpath:*.properties</value>
                <value>classpath*:*.properties</value>
            </list>
        </property>
        <!--zkUrl to find config from zk server-->
        <!-- for production, need config zk.config.url in service startup script JAVA_OPTS-->
        <property name="zkUrl" value="*************:2181"/>
        <property name="zkConfigNodePath" value="/ddc-config/common,/ddc-config/service-dsrp-cdc"/>
    </bean>

<!--    <import resource="classpath:META-INF/spring/dubbo/*.xml"/>-->
    <import resource="classpath*:application-cdc-service.xml"/>
    <import resource="classpath*:application-cdc-manager.xml"/>
    <import resource="classpath*:application-cdc-dao.xml"/>

    <!--dubbo服务配置-->
    <dubbo:application name="dsrp-cdc-service" owner="yyigou" organization="yyigou.ddc"/>
    <!--dubbo消费端-->
    <dubbo:registry address="${common.dubbo.provider.zkurl}" file="/tmp/dubbo-cache/service-dsrp-cdc/default.cache"/>

</beans>