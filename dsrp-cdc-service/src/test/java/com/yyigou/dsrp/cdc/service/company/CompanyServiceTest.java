package com.yyigou.dsrp.cdc.service.company;

import com.alibaba.fastjson.JSON;
import com.yyigou.ddc.common.page.PageDto;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.dsrp.cdc.api.company.vo.CompanyBasicVO;
import com.yyigou.dsrp.cdc.api.company.vo.CompanyDetailVO;
import com.yyigou.dsrp.cdc.common.util.OperationModel;
import com.yyigou.dsrp.cdc.model.company.req.CompanyQueryListPageReq;
import com.yyigou.dsrp.cdc.model.company.req.CompanySaveReq;
import com.yyigou.dsrp.cdc.service.BaseTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.annotation.Rollback;

@Slf4j
public class CompanyServiceTest extends BaseTest {

    @Autowired
    private CompanyService companyService;

    @Test
    @Rollback(false)
    public void testSave(){
        OperationModel operationModel = new OperationModel();
        operationModel.setEnterpriseNo("2000002");
        CompanySaveReq companySaveReq = new CompanySaveReq();
        companySaveReq.setCompanyName("中国人民解放军第85医院");
        companySaveReq.setUnifiedSocialCode("336689127D");
        companySaveReq.setFactoryType(1);
        companySaveReq.setTaxCategory("1");
        companyService.saveCompany(operationModel,companySaveReq);
    }

    @Test
    public void testSave2(){
        CompanySaveReq companySaveReq = JSON.parseObject("{\n" +
                "        \"companyName\": \"test企业6\",\n" +
                "        \"isAssociatedEnterprise\": 1,\n" +
                "        \"unifiedSocialCode\": \"test企业6\",\n" +
                "        \"legalPerson\": \"\",\n" +
                "        \"businessStatus\": \"\",\n" +
                "        \"establishmentDate\": \"\",\n" +
                "        \"businessStartTime\": \"\",\n" +
                "        \"businessEndTime\": \"\",\n" +
                "        \"businessLongTerm\": 0,\n" +
                "        \"registedCapital\": \"100万\",\n" +
                "        \"paidCapital\": \"100万\",\n" +
                "        \"companyBusinessType\": \"\",\n" +
                "        \"industry\": \"\",\n" +
                "        \"businessRegistNo\": \"\",\n" +
                "        \"organizationNo\": \"\",\n" +
                "        \"taxpayerNo\": \"\",\n" +
                "        \"taxpayerQualification\": \"\",\n" +
                "        \"taxCategory\": \"XMG\",\n" +
                "        \"approvalDate\": \"\",\n" +
                "        \"registrationAuthority\": \"\",\n" +
                "        \"regionCode\": \"\",\n" +
                "        \"regionName\": \"\",\n" +
                "        \"address\": \"\",\n" +
                "        \"lastName\": \"\",\n" +
                "        \"isListed\": null,\n" +
                "        \"insuredNumber\": \"\",\n" +
                "        \"manageScope\": \"\",\n" +
                "        \"webSite\": \"\",\n" +
                "        \"email\": \"\",\n" +
                "        \"fax\": \"\",\n" +
                "        \"factoryType\": 1,\n" +
                "        \"taxCategoryName\": \"小规模\",\n" +
                "        \"catalogInfoClientReqList\": [\n" +
                "            {\n" +
                "                \"certType\": \"EVIL_统一管理验证子租户\",\n" +
                "                \"certCode\": \"资料1\",\n" +
                "                \"certName\": \"资料1\",\n" +
                "                \"startTime\": null,\n" +
                "                \"endTime\": null,\n" +
                "                \"longTerm\": null,\n" +
                "                \"remark\": null,\n" +
                "                \"certTypeId\": 11789,\n" +
                "                \"certTypeName\": \"EVIL_统一管理验证子租户\",\n" +
                "                \"termControl\": 0,\n" +
                "                \"medicalDeviceControl\": 0,\n" +
                "                \"companyBusinessScopeList\": [\n" +
                "\n" +
                "                ],\n" +
                "                \"certContentList\": [\n" +
                "\n" +
                "                ],\n" +
                "                \"fileFormatLimit\": \"jpg,jpeg,bmp,png,pdf\",\n" +
                "                \"manageMode\": 1,\n" +
                "                \"baseFileList\": [\n" +
                "                    {\n" +
                "                        \"fileName\": \"img2.png\",\n" +
                "                        \"fileUrl\": \"s2/M00/05/C2/rB4r9WaL2GmAeud2AAbS_IvHLU0312.png\",\n" +
                "                        \"createName\": \"姚琼\",\n" +
                "                        \"createTime\": \"2024-07-08 20:15:36\"\n" +
                "                    }\n" +
                "                ],\n" +
                "                \"fileSizeLimit\": 200,\n" +
                "                \"fileQuantityLimit\": 20,\n" +
                "                \"fileNum\": 1,\n" +
                "                \"baseFileStampedCompanyList\": [\n" +
                "                    {\n" +
                "                        \"rangeType\": 1,\n" +
                "                        \"fileShareScopeList\": [\n" +
                "\n" +
                "                        ]\n" +
                "                    }\n" +
                "                ],\n" +
                "                \"indexX\": 0,\n" +
                "                \"_X_ROW_KEY\": \"row_14\"\n" +
                "            }\n" +
                "        ]\n" +
                "    }",CompanySaveReq.class);
        OperationModel operationModel = new OperationModel();
        operationModel.setEnterpriseNo("2000002");
        operationModel.setUserName("系统测试");
        operationModel.setEmployerNo("系统测试");
        CompanyBasicVO companyBasicVO = companyService.saveCompany(operationModel, companySaveReq);
        log.info("保存企业档案结果:{}", JSON.toJSONString(companyBasicVO));
    }

    @Test
    public void testGetDetailByEnterpriseAndCompanyNo(){
        CompanyDetailVO detail = companyService.getDetailByEnterpriseAndCompanyNo("2000002", "10300018249");
        log.info("企业详情:{}", JSON.toJSON(detail));
    }

    @Test
    public void testFindListPage() {
        CompanyQueryListPageReq queryReq = new CompanyQueryListPageReq();
        queryReq.setEnterpriseNo("2000002");
        PageVo<CompanyDetailVO> pageVo = companyService.findCompanyListPage(queryReq, new PageDto());
        log.info("pageVO:{}", JSON.toJSONString(pageVo));
    }
}
