package com.yyigou.dsrp.cdc.service;

import com.alibaba.dubbo.config.spring.context.annotation.DubboComponentScan;
import com.yyigou.ddc.common.service.RequestHeader;
import com.yyigou.ddc.common.service.ServiceBaseAbstract;
import com.yyigou.ddc.common.service.SessionUser;
import junit.framework.TestCase;
import org.apache.commons.lang3.reflect.FieldUtils;
import org.junit.Before;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.lang.reflect.Field;

/**
 * Unit test for simple App.
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration({"classpath*:dsrp-cdc-service-test.xml"})
@DubboComponentScan(value = "com.yyigou.dsrp.cdc.service")
public class BaseTest extends TestCase {
    /**
     * 模拟网关调用provider透传sessionUser
     */
    @Before
    @SuppressWarnings("unchecked")
    public void setup() {
        try {
            SessionUser sessionUser = new SessionUser();
            sessionUser.setUserName("李四");
            sessionUser.setUserNo("************");
            sessionUser.setUserType(SessionUser.USER_TYPE_PLATFORM);
            sessionUser.setEmployerNo("************");
            sessionUser.setEnterpriseGroupType(1);
            sessionUser.setIsMaster(true);
            sessionUser.setLoginAccount("admin");
//            sessionUser.setEnterpriseName("杭州迪安医学检验中心有限公司");
//            sessionUser.setEnterpriseNo("**********");
            sessionUser.setEnterpriseName("云医购平台科技行业库");
            sessionUser.setEnterpriseNo("**********");
            sessionUser.setEnterpriseNo("**********");
            sessionUser.setEmployerNo("************");//spd测试李峰
            sessionUser.setUserType("0");
            sessionUser.setClientId("8712603d9f324aa5b4508d5ae567d418");
            sessionUser.setToken("96986dbe47c6f8589eef3c4023cffa5a");
            RequestHeader header = new RequestHeader();
            header.setSession(sessionUser);

            Field tsf = FieldUtils.getField(ServiceBaseAbstract.class, "threadSession", true);
            ThreadLocal<RequestHeader> threadSession = (ThreadLocal<RequestHeader>) tsf.get(null);
            threadSession.set(header);
        } catch (IllegalAccessException e) {
            //
        }
    }



}
