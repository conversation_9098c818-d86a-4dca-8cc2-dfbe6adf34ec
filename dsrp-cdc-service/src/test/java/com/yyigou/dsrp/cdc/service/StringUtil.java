package com.yyigou.dsrp.cdc.service;

public class StringUtil {
    public static String toFullWidth(String input) {
        char[] chars = input.toCharArray();
        for (int i = 0; i < chars.length; i++) {
            // 对于ASCII范围内的字符，将其转换为对应的全角字符
            if (chars[i] <= '\u007e') {
                chars[i] = (char)(chars[i] + 0xFEE0);
            }
        }
        return new String(chars);
    }

    // 示例使用
    public static void main(String[] args) {
        String productName = "产品名称";
        String commonName = "通用名称";
        String partNumber = "货号123";
        String specification = "规格描述：50人份/盒";
        String model = "型号ABC";

        productName = toFullWidth(productName);
        commonName = toFullWidth(commonName);
        partNumber = toFullWidth(partNumber);
        specification = toFullWidth(specification);
        model = toFullWidth(model);

        // 然后将转换后的字符串存入数据库
        // ... 存储到数据库的相关代码 ...

        System.out.println(productName);
        System.out.println(commonName);
        System.out.println(partNumber);
        System.out.println(specification);
        System.out.println(model);
    }
}