package com.yyigou.dsrp.cdc.service.customer;

import com.alibaba.fastjson.JSON;
import com.yyigou.ddc.common.page.PageDto;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.dsrp.cdc.api.customer.dto.QueryPageCustomerBySpecifyOrgDTO;
import com.yyigou.dsrp.cdc.api.customer.vo.CustomerPageVO;
import com.yyigou.dsrp.cdc.client.customer.request.CustomerFindRequest;
import com.yyigou.dsrp.cdc.client.customer.request.CustomerNameRequest;
import com.yyigou.dsrp.cdc.client.customer.request.CustomerNoRequest;
import com.yyigou.dsrp.cdc.client.customer.response.CustomerInfoResponse;
import com.yyigou.dsrp.cdc.client.customer.response.CustomerResponse;
import com.yyigou.dsrp.cdc.common.util.OperationModel;
import com.yyigou.dsrp.cdc.service.BaseTest;
import com.yyigou.dsrp.cdc.service.utils.UserHandleUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

@Slf4j
public class CustomerServiceTest extends BaseTest {

    @Resource
    private CustomerService customerService;

    @Test
    public void testFindCustomer(){
        CustomerNoRequest request = new CustomerNoRequest();
        request.setEnterpriseNo("6019604591");
        request.setCustomerNo("10500002212");
        List<CustomerInfoResponse> result = customerService.findCustomerByNo(request);
        log.info("结果数:{}",result.size());
    }

    @Test
    public void testFindCustomerName(){
        CustomerNameRequest request = new CustomerNameRequest();
        request.setEnterpriseNo("6019604591");
//        request.setCustomerName("浙江阳光文化发展股份有限公司");
        request.setCustomerNameKeyword("浙江阳光文化发展");
        List<CustomerInfoResponse> result = customerService.findCustomerByName(request);
        log.info("结果数:{}",result.size());
    }


    @Test
    public void findCustomer(){
        CustomerFindRequest request = new CustomerFindRequest();
        request.setCustomerName("setCustomerName");
        request.setCustomerNameKeyword("setCustomerNameKeyword");
        request.setCustomerKeywords("setCustomerKeywords");
        request.setCustomerNoList(Arrays.asList("1"));
        List<CustomerResponse> result = customerService.findCustomer("6019604591",request);
        log.info("结果数:{}",result.size());
    }

    @Test
    public void queryPageCustomerBySpecifyOrg(){
        String aa = "{\n" +
                "        \"customerKeywords\": null,\n" +
                "        \"cooperationModes\": [],\n" +
                "        \"priceCategoryCodeList\": [],\n" +
                "        \"customerCategoryKeywords\": null,\n" +
                "        \"customerCategoryNos\": null,\n" +
                "        \"showcustomerCategoryKeywords\": null,\n" +
                "        \"ownerCompanyKeywords\": null,\n" +
                "        \"controlIdList\": [],\n" +
                "        \"unifiedSocialCodeKeywords\": null,\n" +
                "        \"businessTypeList\": [],\n" +
                "        \"linkManAndPhoneKeywords\": null,\n" +
                "        \"noCustomerNos\": [],\n" +
                "        \"controlStatusList\": [\n" +
                "            1\n" +
                "        ],\n" +
                "        \"orgNo\": \"12374\",\n" +
                "        \"excludeCustomerCodeList\": [],\n" +
                "        \"priceCategoryCodeList\": [\"10001\"],\n" +
                "        \"customerCode\": null\n" +
                "    }";
        QueryPageCustomerBySpecifyOrgDTO request = JSON.parseObject(aa, QueryPageCustomerBySpecifyOrgDTO.class);
        OperationModel operationModel = UserHandleUtils.getOperationModel();

        PageDto pageDto = new PageDto();
        pageDto.setPageIndex(1);
        pageDto.setPageSize(10);
        PageVo<CustomerPageVO> customerPageVOPageVo = customerService.queryPageCustomerBySpecifyOrg(operationModel, request, pageDto);
        log.info("结果数:{}",customerPageVOPageVo.getRows());
    }

    @Test
    public void queryPageCustomerByCompatibleOrg(){
        String aa = "{\n" +
                "  \"customerKeywords\": null,\n" +
                "  \"cooperationModes\": [],\n" +
                "  \"priceCategoryCodeList\": [],\n" +
                "  \"customerCategoryKeywords\": null,\n" +
                "  \"customerCategoryNos\": null,\n" +
                "  \"showcustomerCategoryKeywords\": null,\n" +
                "  \"ownerCompanyKeywords\": null,\n" +
                "  \"controlIdList\": [],\n" +
                "  \"unifiedSocialCodeKeywords\": null,\n" +
                "  \"businessTypeList\": [],\n" +
                "  \"linkManAndPhoneKeywords\": null,\n" +
                "  \"noCustomerNos\": [],\n" +
                "  \"controlStatusList\": [\n" +
                "    1\n" +
                "  ],\n" +
                "  \"orgNo\": \"645\",\n" +
                "  \"excludeCustomerCodeList\": [],\n" +
                "  \"priceCategoryCode\": null,\n" +
                "  \"customerCode\": null\n" +
                "}";
        QueryPageCustomerBySpecifyOrgDTO request = JSON.parseObject(aa, QueryPageCustomerBySpecifyOrgDTO.class);
        OperationModel operationModel = UserHandleUtils.getOperationModel();

        PageDto pageDto = new PageDto();
        pageDto.setPageIndex(1);
        pageDto.setPageSize(10);
        PageVo<CustomerPageVO> customerPageVOPageVo = customerService.queryPageCustomerByCompatibleOrg(operationModel, request, pageDto);
        log.info("结果数:{}",customerPageVOPageVo.getRows());
    }

    @Test
    public void queryPageCustomerByCompatibleOrg1(){
        String aa = "{\n" +
                "        \"customerKeywords\": null,\n" +
                "        \"cooperationModes\": [],\n" +
                "        \"priceCategoryCodeList\": [],\n" +
                "        \"customerCategoryKeywords\": null,\n" +
                "        \"customerCategoryNos\": null,\n" +
                "        \"showcustomerCategoryKeywords\": null,\n" +
                "        \"ownerCompanyKeywords\": null,\n" +
                "        \"controlIdList\": [],\n" +
                "        \"unifiedSocialCodeKeywords\": null,\n" +
                "        \"businessTypeList\": [],\n" +
                "        \"linkManAndPhoneKeywords\": null,\n" +
                "        \"noCustomerNos\": [],\n" +
                "        \"controlStatusList\": [\n" +
                "            1\n" +
                "        ],\n" +
                "        \"orgNo\": \"12374\",\n" +
                "        \"excludeCustomerCodeList\": [],\n" +
                "        \"priceCategoryCodeList\": [\"10001\"],\n" +
                "        \"customerCode\": null\n" +
                "    }";
        QueryPageCustomerBySpecifyOrgDTO request = JSON.parseObject(aa, QueryPageCustomerBySpecifyOrgDTO.class);
        OperationModel operationModel = UserHandleUtils.getOperationModel();

        PageDto pageDto = new PageDto();
        pageDto.setPageIndex(1);
        pageDto.setPageSize(10);
        PageVo<CustomerPageVO> customerPageVOPageVo = customerService.queryPageCustomerByCompatibleOrg(operationModel, request, pageDto);
        log.info("结果数:{}",customerPageVOPageVo.getRows());
    }
}
