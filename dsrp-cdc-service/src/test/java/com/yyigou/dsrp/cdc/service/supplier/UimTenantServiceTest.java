package com.yyigou.dsrp.cdc.service.supplier;


import com.alibaba.fastjson.JSON;
import com.yyigou.dsrp.cdc.manager.integration.org.UimTenantService;
import com.yyigou.dsrp.cdc.manager.integration.org.res.OrganizationRes;
import com.yyigou.dsrp.cdc.service.BaseTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;


import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

@Slf4j
public class UimTenantServiceTest extends BaseTest {
    @Resource
    private UimTenantService uimTenantService;

    @Test
    public void findOrgByBindEnterpriseNoList(){

        List<OrganizationRes> result = uimTenantService.findOrgByBindEnterpriseNoList("2000002",Arrays.asList("6019604591"));
        System.out.println(JSON.toJSONString(result));
    }


}