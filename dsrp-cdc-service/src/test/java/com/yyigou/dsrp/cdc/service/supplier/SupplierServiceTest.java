package com.yyigou.dsrp.cdc.service.supplier;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.yyigou.ddc.common.page.PageDto;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.dsrp.cdc.api.supply.dto.SupplierSyncQueryDTO;
import com.yyigou.dsrp.cdc.api.supply.vo.SupplierPageVO;
import com.yyigou.dsrp.cdc.api.supply.vo.SupplierSyncCountVO;
import com.yyigou.dsrp.cdc.api.supply.vo.SupplierSyncQueryVO;
import com.yyigou.dsrp.cdc.client.supplier.request.SupplierFindRequest;
import com.yyigou.dsrp.cdc.client.supplier.request.SupplierNameRequest;
import com.yyigou.dsrp.cdc.client.supplier.request.SupplierNoRequest;
import com.yyigou.dsrp.cdc.client.supplier.response.SupplierInfoResponse;
import com.yyigou.dsrp.cdc.client.supplier.response.SupplierResponse;
import com.yyigou.dsrp.cdc.common.util.OperationModel;
import com.yyigou.dsrp.cdc.service.BaseTest;
import com.yyigou.dsrp.cdc.service.utils.UserHandleUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

@Slf4j
public class SupplierServiceTest extends BaseTest {

    @Resource
    private SupplierService supplierService;



    @Test
    public void findSupplier(){
        SupplierFindRequest request = new SupplierFindRequest();
        request.setSupplierName("setCustomerName");
        request.setSupplierNameKeyword("setCustomerNameKeyword");
        request.setSupplierKeywords("setCustomerKeywords");
        request.setSupplierNoList(Arrays.asList("1"));
        List<SupplierResponse> result = supplierService.findSupplier("6019604591",request);
        log.info("结果数:{}",result.size());
    }

    @Test
    public void test(){
        SupplierNameRequest request = new SupplierNameRequest();
        request.setEnterpriseNo("6019604591");
        request.setSupplierName("珠海");
        List<SupplierInfoResponse> list = supplierService.findSupplierByName(request);
        log.info("查询结果:{}", list.size());
    }

    @Test
    public void test2(){
        SupplierNoRequest request = new SupplierNoRequest();
        request.setEnterpriseNo("6019604591");
        request.setSupplierNoList(Lists.newArrayList("10400001402","10400001438","10400001390",""));
        List<SupplierInfoResponse> result = supplierService.findSupplierByNo(request);
        log.info("查询结果:{}",result.size());
    }

    @Test
    public void test3(){
        OperationModel operationModel = UserHandleUtils.getOperationModel();
        SupplierSyncQueryDTO params = new SupplierSyncQueryDTO();
        params.setBindStatus(1);
        PageDto pageDto = new PageDto();
        PageVo<SupplierSyncQueryVO> result = supplierService.findListPageForSync(operationModel,params,pageDto);
        log.info("查询结果:{}", JSON.toJSONString(result));
    }

    @Test
    public void test4(){
        OperationModel operationModel = UserHandleUtils.getOperationModel();
        SupplierSyncCountVO supplierSyncCountVO =  supplierService.findSupplierSyncCount(operationModel);
        log.info("查询结果:{}", JSON.toJSONString(supplierSyncCountVO));
    }

    @Test
    public void test5(){
        OperationModel operationModel = UserHandleUtils.getOperationModel();
        SupplierSyncQueryDTO params = new SupplierSyncQueryDTO();
        params.setBindStatus(1);
        PageDto pageDto = new PageDto();
        PageVo<SupplierSyncQueryVO> result = supplierService.findListPageForPendingAdmission(operationModel,params,pageDto);
        log.info("查询结果:{}", JSON.toJSONString(result));
    }
}
