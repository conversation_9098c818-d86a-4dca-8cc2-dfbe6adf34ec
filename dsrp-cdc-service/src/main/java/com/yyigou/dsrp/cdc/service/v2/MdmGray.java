package com.yyigou.dsrp.cdc.service.v2;

import com.alibaba.fastjson.JSONArray;
import com.yyigou.ddc.common.zkmonitor.EnableZkMonitor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.locks.ReentrantReadWriteLock;

@Component
@Slf4j
public class MdmGray {
    /**
     * ZK配置热更新
     */
    @EnableZkMonitor(zkProperty = "mdmEnterpriseNoList", zkNode = "/ddc-config/service-dsrp-cdc")
    private volatile String mdmEnterpriseNoList;

    private ReentrantReadWriteLock rwLock = new ReentrantReadWriteLock();
    private ReentrantReadWriteLock.ReadLock rLock = rwLock.readLock();
    private ReentrantReadWriteLock.WriteLock wLock = rwLock.writeLock();
    private Set<String> mdmEnterpriseNoSet = new HashSet<>();

    /**
     * 热部署, 需要自行实现, zk监控客户端会在数据变更时优先调用set方法进行回调, 可在这里实现热部署
     *
     * @param mdmEnterpriseNoList
     */
    public void setMdmEnterpriseNoList(String mdmEnterpriseNoList) {
        if (StringUtils.isEmpty(mdmEnterpriseNoList)) {
            mdmEnterpriseNoList = "[]";
        }

        try {
            wLock.lock();

            List<String> list = JSONArray.parseArray(mdmEnterpriseNoList, String.class);

            mdmEnterpriseNoSet.clear();
            mdmEnterpriseNoSet.addAll(list);

            log.warn("mdm灰度租户更新成功,当前灰度租户列表:{}", mdmEnterpriseNoSet);
        } catch (Exception e) {
            log.error("mdm灰度租户更新异常", e);
            log.error("mdm灰度租户更新异常,入参:{}", mdmEnterpriseNoList);
        } finally {
            wLock.unlock();
        }
    }

    /**
     * 判断租户是否在灰度范围内
     *
     * @param enterpriseNo
     * @return
     */
    public boolean isEnterpriseGray(String enterpriseNo) {
        try {
            rLock.lock();

            return mdmEnterpriseNoSet.contains(enterpriseNo);
        } finally {
            rLock.unlock();
        }
    }
}

