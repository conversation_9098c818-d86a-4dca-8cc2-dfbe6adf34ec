package com.yyigou.dsrp.cdc.service.v2.customersync;

import com.yyigou.ddc.common.exception.BusinessException;
import com.yyigou.ddc.common.util.DateUtil;
import com.yyigou.ddc.services.ddc.dict.vo.AreaCodeVo;
import com.yyigou.dsrp.cdc.api.v2.customer.vo.CustomerVO;
import com.yyigou.dsrp.cdc.common.nc.NcGray;
import com.yyigou.dsrp.cdc.common.nc.NcMapping;
import com.yyigou.dsrp.cdc.common.util.BeanUtil;
import com.yyigou.dsrp.cdc.dao.v2.customer.entity.CustomerBase;
import com.yyigou.dsrp.cdc.dao.v2.customer.entity.CustomerCategoryV2;
import com.yyigou.dsrp.cdc.dao.v2.customer.entity.CustomerChannelSyncMapping;
import com.yyigou.dsrp.cdc.manager.integration.dict.DictEnterpriseService;
import com.yyigou.dsrp.cdc.manager.integration.klp.KlpCustomer2NCManager;
import com.yyigou.dsrp.cdc.manager.integration.klp.req.KlpAssignCustomerDTO;
import com.yyigou.dsrp.cdc.manager.integration.klp.req.KlpCustomerAssginOrgDTO;
import com.yyigou.dsrp.cdc.manager.integration.klp.req.KlpPushCustomerDTO;
import com.yyigou.dsrp.cdc.manager.integration.klp.res.KlpResCommonVO;
import com.yyigou.dsrp.cdc.model.constant.ViewNameConstant;
import com.yyigou.dsrp.cdc.model.v2.customersync.req.CustomerChannelSyncReq;
import com.yyigou.dsrp.cdc.service.v2.customer.CustomerCategoryV2Service;
import com.yyigou.dsrp.cdc.service.v2.customer.CustomerChannelSyncMappingService;
import com.yyigou.dsrp.cdc.service.v2.customer.CustomerV2Service;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;


@Slf4j
@Component
public class KlpCustomer2NCJobService {
    @Resource
    private NcGray ncGray;

    @Resource
    private NcMapping ncMapping;

    @Resource
    private CustomerChannelSyncMappingService customerChannelSyncMappingService;

    @Resource
    private KlpCustomer2NCManager klpCustomer2NCManager;

    @Resource
    private CustomerV2Service customerV2Service;

    @Resource
    private DictEnterpriseService dictEnterpriseService;

    @Resource
    private CustomerCategoryV2Service customerCategoryV2Service;

    public void syncCustomer2NC(CustomerChannelSyncReq customerChannelSyncReq) {
        try {
            log.warn("开始同步客户到NC, req={}", customerChannelSyncReq);

            CustomerChannelSyncMapping customerChannelSyncMapping = BeanUtil.copyFields(customerChannelSyncReq, CustomerChannelSyncMapping.class);

            syncCustomer2NC(customerChannelSyncMapping);
        } catch (Exception e) {
            log.error("同步客户到NC失败", e);
        }
    }

    private void syncCustomer2NC(CustomerChannelSyncMapping customerChannelSyncMapping) {
        try {
            log.warn("开始同步客户到NC,{}", customerChannelSyncMapping);

            String viewNo = customerChannelSyncMapping.getViewNo();

            if (ViewNameConstant.BDC_CUSTOMER_VIEW.equals(viewNo)) {
                syncCustomer(customerChannelSyncMapping);
            } else if (ViewNameConstant.DUMMY_BDC_CUSTOMER_ASSIGN_VIEW.equals(viewNo)) {
                syncCustomerAssign(customerChannelSyncMapping);
            }

            log.warn("同步客户到NC成功,{}", customerChannelSyncMapping);
        } catch (Exception e) {
            log.error("同步客户到NC失败", e);
        }
    }

    private void syncCustomer(CustomerChannelSyncMapping customerChannelSyncMapping) {
        try {
            log.warn("开始同步客户到NC,{}", customerChannelSyncMapping);

            CustomerVO customer = customerV2Service.getCustomerByCode(customerChannelSyncMapping.getEnterpriseNo(), customerChannelSyncMapping.getInnerBillNo());

            CustomerCategoryV2 customerCategory = customerCategoryV2Service.getByNo(customerChannelSyncMapping.getEnterpriseNo(), customer.getCustomerCategoryNo());
            Map<String, String> customerCategoryMapping = ncMapping.getNcCustomerCategoryMap().get(customerChannelSyncMapping.getEnterpriseNo());
            if (!customerCategoryMapping.containsKey(customerCategory.getCategoryCode())) {
                throw new BusinessException("NC没有对应的客户分类");
            }
            Map<Integer, String> customerHospitalClassMapping = ncMapping.getNcCustomerHospitalClassMap().get(customerChannelSyncMapping.getEnterpriseNo());
            if (!customerHospitalClassMapping.containsKey(customer.getHospitalClass())) {
                throw new BusinessException("NC没有对应的客户医院等级");
            }

            Map<String, String> customerHospitalTypeMapping = ncMapping.getNcCustomerHospitalTypeMap().get(customerChannelSyncMapping.getEnterpriseNo());
            if (!customerHospitalTypeMapping.containsKey(customer.getHospitalType())) {
                throw new BusinessException("NC没有对应的客户医院类型");
            }

            String regionCode = null;
            if (null != customer.getRegionCode()) {
                String tmpRegionCode = customer.getRegionCode();
                for (int i = 0; i < 100; i++) {
                    List<AreaCodeVo> areaCodeList = dictEnterpriseService.findAreaByCodes(Collections.singletonList(tmpRegionCode));
                    if (CollectionUtils.isEmpty(areaCodeList)) {
                        break;
                    }

                    AreaCodeVo areaCodeVo = areaCodeList.get(0);
                    if (StringUtils.isEmpty(areaCodeVo.getParentCode())) {
                        break;
                    }

                    tmpRegionCode = areaCodeVo.getParentCode();
                }

                if (tmpRegionCode.equals(customer.getRegionCode())) {
                    throw new BusinessException("没有找到省份编码");
                }

                regionCode = tmpRegionCode;
            }
            Map<String, String> areaMapping = ncMapping.getNcAreaMap().get(customerChannelSyncMapping.getEnterpriseNo());
            if (!areaMapping.containsKey(regionCode)) {
                throw new BusinessException("NC没有对应的客户区域");
            }

            KlpPushCustomerDTO klpPushCustomerDTO = new KlpPushCustomerDTO();
            klpPushCustomerDTO.setClasstype(customerCategoryMapping.get(customerCategory.getCategoryCode()));
            klpPushCustomerDTO.setCust_grade(customerHospitalClassMapping.get(customer.getHospitalClass()));
            klpPushCustomerDTO.setOwnership(customerHospitalTypeMapping.get(customer.getHospitalType()));
            klpPushCustomerDTO.setEconomicstyle(customer.getEconomicType());
            klpPushCustomerDTO.setCountry(customer.getCountryRegionId());
            klpPushCustomerDTO.setAreacl(areaMapping.get(regionCode));
            klpPushCustomerDTO.setTaxpayerid(customer.getUnifiedSocialCode());
            klpPushCustomerDTO.setCode(customer.getCustomerCode());
            klpPushCustomerDTO.setName(customer.getCustomerName());
            klpPushCustomerDTO.setShortname("");
            klpPushCustomerDTO.setEname(customer.getCustomerNameEn());
            klpPushCustomerDTO.setEmail(customer.getEmail());
            klpPushCustomerDTO.setAddress(customer.getAddress());
            klpPushCustomerDTO.setRemark(customer.getRemark());

            log.warn("开始同步客户到NC,req={}", klpPushCustomerDTO);
            KlpResCommonVO klpResCommonVO = klpCustomer2NCManager.pushCustomer(klpPushCustomerDTO);
            log.warn("同步客户到NC,resp={}", klpResCommonVO);

            if (!"1".equals(klpResCommonVO.getState())) {
                throw new BusinessException("同步客户到NC失败");
            }

            customerChannelSyncMapping.setSyncStatus(1);
            customerChannelSyncMapping.setSyncResult("同步成功");
            customerChannelSyncMapping.setFailTimes(0);
            customerChannelSyncMapping.setRemark("同步成功");
            customerChannelSyncMapping.setOperateTime(DateUtil.getCurrentDate());
            customerChannelSyncMapping.setOutBillNo(klpResCommonVO.getNc_billno());

            customerChannelSyncMappingService.updateSyncStatus(customerChannelSyncMapping);
        } catch (Exception e) {
            log.error("同步客户到NC失败", e);

            // 截取300长度的错误信息
            String errorMessage = e.getMessage();
            if (errorMessage != null && errorMessage.length() > 300) {
                errorMessage = errorMessage.substring(0, 300);
            }

            if (null == customerChannelSyncMapping.getFailTimes()) {
                customerChannelSyncMapping.setFailTimes(0);
            }

            customerChannelSyncMapping.setSyncStatus(2);
            customerChannelSyncMapping.setSyncResult(errorMessage);
            customerChannelSyncMapping.setFailTimes(customerChannelSyncMapping.getFailTimes() + 1);
            customerChannelSyncMapping.setRemark("同步失败");
            customerChannelSyncMapping.setOperateTime(DateUtil.getCurrentDate());
            customerChannelSyncMappingService.updateSyncStatus(customerChannelSyncMapping);
        }
    }

    private void syncCustomerAssign(CustomerChannelSyncMapping customerChannelSyncMapping) {
        try {
            log.warn("开始同步客户分派到NC,{}", customerChannelSyncMapping);

            CustomerBase customerBase = customerV2Service.getCustomerBaseByCodeAndUseOrgNo(customerChannelSyncMapping.getEnterpriseNo(), customerChannelSyncMapping.getInnerBillNo(), customerChannelSyncMapping.getOrgNo());

            KlpAssignCustomerDTO klpAssignCustomerDTO = new KlpAssignCustomerDTO();
            klpAssignCustomerDTO.setCode(customerChannelSyncMapping.getInnerBillNo());
            KlpCustomerAssginOrgDTO klpCustomerAssginOrgDTO = new KlpCustomerAssginOrgDTO();
            klpCustomerAssginOrgDTO.setOrgCode(customerChannelSyncMapping.getOrgNo());
            klpAssignCustomerDTO.setAssignOrgList(Collections.singletonList(klpCustomerAssginOrgDTO));

            if (null == customerBase) {
                // 取消分派
                klpAssignCustomerDTO.setAssignStatus(0);
            } else {
                klpAssignCustomerDTO.setAssignStatus(1);
            }
            log.warn("开始同步客户分派到NC,req={}", klpAssignCustomerDTO);
            KlpResCommonVO klpResCommonVO = klpCustomer2NCManager.assignCustomer(klpAssignCustomerDTO);
            log.warn("同步客户分派到NC,resp={}", klpResCommonVO);

            if (!"1".equals(klpResCommonVO.getState())) {
                throw new BusinessException("同步客户分派到NC失败");
            }

            customerChannelSyncMapping.setSyncStatus(1);
            customerChannelSyncMapping.setSyncResult("同步成功");
            customerChannelSyncMapping.setFailTimes(0);
            customerChannelSyncMapping.setRemark("同步成功");
            customerChannelSyncMapping.setOperateTime(DateUtil.getCurrentDate());
            customerChannelSyncMapping.setOutBillNo(klpResCommonVO.getNc_billno());

            customerChannelSyncMappingService.updateSyncStatus(customerChannelSyncMapping);
        } catch (Exception e) {
            log.error("同步客户分派到NC失败", e);

            customerChannelSyncMapping.setSyncStatus(2);
            customerChannelSyncMapping.setSyncResult(e.getMessage());
            customerChannelSyncMapping.setFailTimes(customerChannelSyncMapping.getFailTimes() + 1);
            customerChannelSyncMapping.setRemark("同步失败");
            customerChannelSyncMapping.setOperateTime(DateUtil.getCurrentDate());
            customerChannelSyncMappingService.updateSyncStatus(customerChannelSyncMapping);
        }
    }
}