package com.yyigou.dsrp.cdc.service.supplier;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yyigou.dsrp.cdc.api.customer.vo.CustomerCategoryTree;
import com.yyigou.dsrp.cdc.api.supply.dto.SupplierExternalSaveDTO;
import com.yyigou.dsrp.cdc.api.supply.vo.SupplierCategoryTree;
import com.yyigou.dsrp.cdc.common.util.OperationModel;
import com.yyigou.dsrp.cdc.dao.customer.entity.CustomerCategory;
import com.yyigou.dsrp.cdc.dao.supplier.entity.SupplierCategory;

import java.util.LinkedList;
import java.util.List;

public interface SupplierCategoryService extends IService<SupplierCategory> {


    void createAndUpdateCategory(OperationModel operationModel, List<SupplierExternalSaveDTO> params);


    /**
     * 根据供应商分类编号查询供应商分类信息
     *
     * @param enterpriseNo
     * @param no
     * @return: {@link SupplierCategory}
     */
    SupplierCategory getByNo(String enterpriseNo, String no);


    /**
     * 根据供应商分类编号查询供应商分类信息
     *
     * @param enterpriseNo
     * @param groupNo
     * @return: {@link SupplierCategory}
     */
    SupplierCategory getByGroupNo(String enterpriseNo, String groupNo);

    /**
     * 根据供应商分类编号查询父级分类信息
     *
     * @author: Moore
     * @date: 2024/7/17 15:30
     * @version: 1.0.0
     */
    LinkedList<SupplierCategory> getParentNoLinkedList(String enterpriseNo, String no);

    /**
     * 根据租户编号+集团租户分类编号+父级分类编号查询分类信息
     *
     * @param enterpriseNo
     * @param groupNo
     * @param parentNo
     * @return: {@link CustomerCategory}
     */
    SupplierCategory findByGroupNoAndParentNo(String enterpriseNo, String groupNo, String parentNo);


    List<SupplierCategoryTree> queryTree(OperationModel operationModel);

    List<SupplierCategoryTree> queryGroupTree(OperationModel operationModel);
}
