package com.yyigou.dsrp.cdc.service.v2.supplier;

import com.yyigou.dsrp.cdc.api.supply.dto.SupplierExternalSaveDTO;
import com.yyigou.dsrp.cdc.api.supply.vo.SupplierExternalSaveVO;
import com.yyigou.dsrp.cdc.api.supply.vo.SupplierInternalSaveVO;
import com.yyigou.dsrp.cdc.common.util.OperationModel;

import java.util.List;

public interface SupplierV2ExternalService {
    List<SupplierExternalSaveVO> batchSaveForDA(OperationModel operationModel, List<SupplierExternalSaveDTO> params);

    /**
     * 迪安供应商批量保存接口(兼容MDM)，会在多组织的表里也插入一份数据
     *
     * @param operationModel
     * @param params
     */
    void batchSaveMdmCompatible(OperationModel operationModel, List<SupplierExternalSaveDTO> params, List<SupplierInternalSaveVO> resultList);
}
