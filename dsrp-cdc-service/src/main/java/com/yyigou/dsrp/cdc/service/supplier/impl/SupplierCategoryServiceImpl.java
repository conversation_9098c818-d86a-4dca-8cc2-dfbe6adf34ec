package com.yyigou.dsrp.cdc.service.supplier.impl;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yyigou.ddc.common.treegrid.TreeGridUtils;
import com.yyigou.ddc.common.util.ValidatorUtils;
import com.yyigou.dsrp.cdc.api.supply.dto.SupplierExternalSaveDTO;
import com.yyigou.dsrp.cdc.api.supply.vo.SupplierCategoryTree;
import com.yyigou.dsrp.cdc.common.enums.CommonStatusEnum;
import com.yyigou.dsrp.cdc.common.enums.DeletedEnum;
import com.yyigou.dsrp.cdc.common.util.CommonUtil;
import com.yyigou.dsrp.cdc.common.util.OperationModel;
import com.yyigou.dsrp.cdc.dao.customer.entity.CustomerCategory;
import com.yyigou.dsrp.cdc.dao.supplier.SupplierCategoryDAO;
import com.yyigou.dsrp.cdc.dao.supplier.entity.SupplierCategory;
import com.yyigou.dsrp.cdc.manager.integration.numberCenter.NumberCenterService;
import com.yyigou.dsrp.cdc.manager.integration.uim.TenantService;
import com.yyigou.dsrp.cdc.model.constant.NumberCenterConstant;
import com.yyigou.dsrp.cdc.model.constant.ServiceConstant;
import com.yyigou.dsrp.cdc.model.constant.SystemConstant;
import com.yyigou.dsrp.cdc.service.supplier.SupplierCategoryService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class SupplierCategoryServiceImpl extends ServiceImpl<SupplierCategoryDAO, SupplierCategory> implements SupplierCategoryService {

    private final SupplierCategoryDAO supplierCategoryDAO;
    private final NumberCenterService numberCenterService;
    private final TenantService tenantService;

    /**
     * 创建更新产品分类
     *
     * @param operationModel
     * @param params
     */
    public void createAndUpdateCategory(OperationModel operationModel, List<SupplierExternalSaveDTO> params) {
        // 1.查询企业下存货分类
        LambdaQueryWrapper<SupplierCategory> queryWrapper = Wrappers.lambdaQuery(SupplierCategory.class)
                .eq(SupplierCategory::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(SupplierCategory::getDeleted, DeletedEnum.UN_DELETE.getValue());
        List<SupplierCategory> supplierCategoryList = supplierCategoryDAO.selectList(queryWrapper);

        Map<String, List<SupplierCategory>> goodsCategoryGroupMap = supplierCategoryList.stream().collect(Collectors.groupingBy(SupplierCategory::getParentNo));
        List<SupplierCategory> topNodeList = goodsCategoryGroupMap.getOrDefault(ServiceConstant.TOP_PARENT_NO, new ArrayList<>());

        params.forEach(t -> {
            if (StringUtils.isBlank(t.getSupplierCategoryCode_1())) {
                return;
            }
            Optional<SupplierCategory> categoryOptional = topNodeList.stream().filter(supplierCategory -> t.getSupplierCategoryCode_1().equals(supplierCategory.getCategoryCode())).findAny();
            SupplierCategory currentCategory = null;
            if (categoryOptional.isPresent()) {
                SupplierCategory goodsCategory = categoryOptional.get();
                goodsCategory.setCategoryName(t.getSupplierCategoryName_1());
                supplierCategoryDAO.updateById(goodsCategory);
                currentCategory = goodsCategory;
                t.setSupplierCategoryNo(currentCategory.getNo());
            } else {
                //新增逻辑
                SupplierCategory category = new SupplierCategory();
                category.setParentNo(ServiceConstant.TOP_PARENT_NO);
                category.setNo(numberCenterService.createTreeNumber(NumberCenterConstant.SUPPLY_NO_CATEGORY_KEY, ServiceConstant.TOP_PARENT_NO));
                category.setEnterpriseNo(operationModel.getEnterpriseNo());
                category.setCategoryName(t.getSupplierCategoryName_1());
                category.setCategoryCode(t.getSupplierCategoryCode_1());
                category.setMnemonicCode(t.getSupplierCategoryCode_1());
                category.setStatus(CommonStatusEnum.EFFECTIVE.getValue().toString());
                category.setDeleted(DeletedEnum.UN_DELETE.getValue());
                CommonUtil.fillCreatInfo(operationModel, category);
                supplierCategoryDAO.insert(category);
                currentCategory = category;
                supplierCategoryList.add(currentCategory);
                t.setSupplierCategoryNo(currentCategory.getNo());
            }
            if (StringUtils.isNotBlank(t.getSupplierCategoryCode_2())) {
                SupplierCategory finalCurrentCategory = currentCategory;
                Optional<SupplierCategory> categoryItemOptional = supplierCategoryList.stream().filter(s -> s.getParentNo().equals(finalCurrentCategory.getNo()) && t.getSupplierCategoryCode_2().equals(s.getCategoryCode())).findAny();
                if (categoryItemOptional.isPresent()) {
                    SupplierCategory supplierCategory = categoryItemOptional.get();
                    supplierCategory.setCategoryName(t.getSupplierCategoryName_2());
                    supplierCategoryDAO.updateById(supplierCategory);
                    currentCategory = supplierCategory;
                } else {
                    //新增逻辑
                    SupplierCategory supplierCategory = new SupplierCategory();
                    supplierCategory.setNo(numberCenterService.createTreeNumber(NumberCenterConstant.SUPPLY_NO_CATEGORY_KEY, currentCategory.getNo()));
                    supplierCategory.setParentNo(currentCategory.getNo());
                    supplierCategory.setEnterpriseNo(operationModel.getEnterpriseNo());
                    supplierCategory.setCategoryName(t.getSupplierCategoryName_2());
                    supplierCategory.setCategoryCode(t.getSupplierCategoryCode_2());
                    supplierCategory.setMnemonicCode(t.getSupplierCategoryCode_2());
                    supplierCategory.setStatus(CommonStatusEnum.EFFECTIVE.getValue().toString());
                    supplierCategory.setDeleted(DeletedEnum.UN_DELETE.getValue());
                    CommonUtil.fillCreatInfo(operationModel, supplierCategory);
                    supplierCategoryDAO.insert(supplierCategory);
                    currentCategory = supplierCategory;
                    supplierCategoryList.add(currentCategory);
                }
                t.setSupplierCategoryNo(currentCategory.getNo());
            }

            if (StringUtils.isNotBlank(t.getSupplierCategoryCode_3())) {
                SupplierCategory finalCurrentCategory = currentCategory;
                Optional<SupplierCategory> categoryItemOptional = supplierCategoryList.stream().filter(s -> s.getParentNo().equals(finalCurrentCategory.getNo()) && t.getSupplierCategoryCode_3().equals(s.getCategoryCode())).findAny();
                if (categoryItemOptional.isPresent()) {
                    SupplierCategory supplierCategory = categoryItemOptional.get();
                    supplierCategory.setCategoryName(t.getSupplierCategoryName_3());
                    supplierCategoryDAO.updateById(supplierCategory);
                    currentCategory = supplierCategory;
                } else {
                    //新增逻辑
                    SupplierCategory supplierCategory = new SupplierCategory();
                    supplierCategory.setNo(numberCenterService.createTreeNumber(NumberCenterConstant.SUPPLY_NO_CATEGORY_KEY, currentCategory.getNo()));
                    supplierCategory.setParentNo(currentCategory.getNo());
                    supplierCategory.setEnterpriseNo(operationModel.getEnterpriseNo());
                    supplierCategory.setCategoryName(t.getSupplierCategoryName_3());
                    supplierCategory.setCategoryCode(t.getSupplierCategoryCode_3());
                    supplierCategory.setMnemonicCode(t.getSupplierCategoryCode_3());
                    supplierCategory.setStatus(CommonStatusEnum.EFFECTIVE.getValue().toString());
                    supplierCategory.setDeleted(DeletedEnum.UN_DELETE.getValue());
                    CommonUtil.fillCreatInfo(operationModel, supplierCategory);
                    supplierCategoryDAO.insert(supplierCategory);
                    currentCategory = supplierCategory;
                    supplierCategoryList.add(currentCategory);
                }
                t.setSupplierCategoryNo(currentCategory.getNo());
            }

            if (StringUtils.isNotBlank(t.getSupplierCategoryCode_4())) {
                SupplierCategory finalCurrentCategory = currentCategory;
                Optional<SupplierCategory> categoryItemOptional = supplierCategoryList.stream().filter(s -> s.getParentNo().equals(finalCurrentCategory.getNo()) && t.getSupplierCategoryCode_4().equals(s.getCategoryCode())).findAny();
                if (categoryItemOptional.isPresent()) {
                    SupplierCategory supplierCategory = categoryItemOptional.get();
                    supplierCategory.setCategoryName(t.getSupplierCategoryName_4());
                    supplierCategoryDAO.updateById(supplierCategory);
                    currentCategory = supplierCategory;
                } else {
                    //新增逻辑
                    SupplierCategory supplierCategory = new SupplierCategory();
                    supplierCategory.setNo(numberCenterService.createTreeNumber(NumberCenterConstant.SUPPLY_NO_CATEGORY_KEY, currentCategory.getNo()));
                    supplierCategory.setParentNo(currentCategory.getNo());
                    supplierCategory.setEnterpriseNo(operationModel.getEnterpriseNo());
                    supplierCategory.setCategoryName(t.getSupplierCategoryName_4());
                    supplierCategory.setCategoryCode(t.getSupplierCategoryCode_4());
                    supplierCategory.setMnemonicCode(t.getSupplierCategoryCode_4());
                    supplierCategory.setStatus(CommonStatusEnum.EFFECTIVE.getValue().toString());
                    supplierCategory.setDeleted(DeletedEnum.UN_DELETE.getValue());
                    CommonUtil.fillCreatInfo(operationModel, supplierCategory);
                    supplierCategoryDAO.insert(supplierCategory);
                    currentCategory = supplierCategory;
                    supplierCategoryList.add(currentCategory);
                }
                t.setSupplierCategoryNo(currentCategory.getNo());
            }
            if (StringUtils.isNotBlank(t.getSupplierCategoryCode_5())) {
                SupplierCategory finalCurrentCategory = currentCategory;
                Optional<SupplierCategory> categoryItemOptional = supplierCategoryList.stream().filter(s -> s.getParentNo().equals(finalCurrentCategory.getNo()) && t.getSupplierCategoryCode_5().equals(s.getCategoryCode())).findAny();
                if (categoryItemOptional.isPresent()) {
                    SupplierCategory supplierCategory = categoryItemOptional.get();
                    supplierCategory.setCategoryName(t.getSupplierCategoryName_5());
                    supplierCategoryDAO.updateById(supplierCategory);
                    currentCategory = supplierCategory;
                } else {
                    //新增逻辑
                    SupplierCategory supplierCategory = new SupplierCategory();
                    supplierCategory.setNo(numberCenterService.createTreeNumber(NumberCenterConstant.SUPPLY_NO_CATEGORY_KEY, currentCategory.getNo()));
                    supplierCategory.setParentNo(currentCategory.getNo());
                    supplierCategory.setEnterpriseNo(operationModel.getEnterpriseNo());
                    supplierCategory.setCategoryName(t.getSupplierCategoryName_5());
                    supplierCategory.setCategoryCode(t.getSupplierCategoryCode_5());
                    supplierCategory.setMnemonicCode(t.getSupplierCategoryCode_5());
                    supplierCategory.setStatus(CommonStatusEnum.EFFECTIVE.getValue().toString());
                    supplierCategory.setDeleted(DeletedEnum.UN_DELETE.getValue());
                    CommonUtil.fillCreatInfo(operationModel, supplierCategory);
                    supplierCategoryDAO.insert(supplierCategory);
                    currentCategory = supplierCategory;
                    supplierCategoryList.add(currentCategory);
                }
                t.setSupplierCategoryNo(currentCategory.getNo());
            }
        });
    }

    /**
     * 根据供应商分类编号查询供应商分类信息
     *
     * @param enterpriseNo
     * @param no
     * @return: {@link SupplierCategory}
     */
    @Override
    public SupplierCategory getByNo(String enterpriseNo, String no) {
        ValidatorUtils.checkEmptyThrowEx(enterpriseNo, "租户编号不能为空");
        ValidatorUtils.checkEmptyThrowEx(no, "供应商分类编号不能为空");
        return supplierCategoryDAO.getByNo(enterpriseNo, no);
    }

    @Override
    public SupplierCategory getByGroupNo(String enterpriseNo, String groupNo) {
        ValidatorUtils.checkEmptyThrowEx(enterpriseNo, "租户编号不能为空");
        ValidatorUtils.checkEmptyThrowEx(groupNo, "集团供应商分类编号不能为空");
        return supplierCategoryDAO.getByGroupNo(enterpriseNo, groupNo);
    }

    /**
     * 根据供应商分类编号查询父级分类信息
     *
     * @param enterpriseNo
     * @param no
     * @author: Moore
     * @date: 2024/7/17 15:30
     * @version: 1.0.0
     */
    @Override
    public LinkedList<SupplierCategory> getParentNoLinkedList(String enterpriseNo, String no) {
        List<SupplierCategory> supplierCategoryList = getParentCategoryNoList(enterpriseNo, no);
        LinkedList<SupplierCategory> linkedList = new LinkedList<>();
        String parentNo = SystemConstant.COMMON_TOP_CATEGORY_NO;
        while (true) {
            String finalParentNo = parentNo;
            Optional<SupplierCategory> optional = supplierCategoryList.stream().filter(t -> finalParentNo.equals(t.getParentNo())).findAny();
            if (!optional.isPresent()) {
                break;
            }
            SupplierCategory supplierCategory = optional.get();
            linkedList.add(supplierCategory);
            parentNo = supplierCategory.getNo();
        }
        return linkedList;
    }

    public List<SupplierCategory> getParentCategoryNoList(String enterpriseNo, String categoryNo) {
        List<SupplierCategory> parentList = new ArrayList<>();
        SupplierCategory supplierCategory = getByNo(enterpriseNo, categoryNo);
        parentList.add(supplierCategory);
        String categoryParentNo = supplierCategory.getParentNo();
        while (true) {
            if (StringUtils.isEmpty(categoryParentNo) || SystemConstant.COMMON_TOP_CATEGORY_NO.equals(categoryParentNo)) {
                break;
            }
            SupplierCategory parentCategory = getByNo(enterpriseNo, categoryParentNo);
            parentList.add(parentCategory);
            categoryParentNo = parentCategory.getParentNo();
        }
        return parentList;
    }

    /**
     * 根据租户编号+集团租户分类编号+父级分类编号查询分类信息
     *
     * @param enterpriseNo
     * @param groupNo
     * @param parentNo
     * @return: {@link CustomerCategory}
     */
    @Override
    public SupplierCategory findByGroupNoAndParentNo(String enterpriseNo, String groupNo, String parentNo) {
        ValidatorUtils.checkEmptyThrowEx(enterpriseNo, "租户编号不能为空");
        return supplierCategoryDAO.findByGroupNoAndParentNo(enterpriseNo, groupNo, parentNo);
    }


    @Override
    public List<SupplierCategoryTree> queryTree(OperationModel operationModel) {
        final List<SupplierCategory> categoryList = supplierCategoryDAO.selectList(Wrappers.<SupplierCategory>lambdaQuery()
                .eq(SupplierCategory::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(SupplierCategory::getDeleted, DeletedEnum.UN_DELETE.getValue()));
        if (CollectionUtils.isEmpty(categoryList)) {
            return new ArrayList<>();
        }
        List<SupplierCategoryTree> treeList = new ArrayList<>();
        categoryList.forEach(t -> {
            SupplierCategoryTree tree = new SupplierCategoryTree();
            BeanUtils.copyProperties(t, tree);
            treeList.add(tree);
        });
        return TreeGridUtils.getChildCategoryTrees(treeList, ServiceConstant.TOP_PARENT_NO);
    }

    @Override
    public List<SupplierCategoryTree> queryGroupTree(OperationModel operationModel) {
        final String groupEnterpriseNo = tenantService.getGroupEnterpriseNo(operationModel.getEnterpriseNo());
        operationModel.setEnterpriseNo(groupEnterpriseNo);
        return queryTree(operationModel);
    }
}
