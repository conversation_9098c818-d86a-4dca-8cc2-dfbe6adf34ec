package com.yyigou.dsrp.cdc.service.supplier;

import com.yyigou.dsrp.cdc.api.supply.dto.SupplierExternalAssignDTO;
import com.yyigou.dsrp.cdc.api.supply.dto.SupplierExternalSaveDTO;
import com.yyigou.dsrp.cdc.api.supply.vo.SupplierExternalSaveVO;
import com.yyigou.dsrp.cdc.common.util.OperationModel;

import java.util.List;

public interface SupplyExternService {

    List<SupplierExternalSaveVO> batchSave(OperationModel operationModel, List<SupplierExternalSaveDTO> params);

    Boolean supplyAssign(OperationModel operationModel, SupplierExternalAssignDTO params);

}
