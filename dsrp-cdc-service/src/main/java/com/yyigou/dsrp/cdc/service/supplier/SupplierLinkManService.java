package com.yyigou.dsrp.cdc.service.supplier;

import com.yyigou.dsrp.cdc.api.company.vo.CompanyLinkmanVO;
import com.yyigou.dsrp.cdc.api.supply.dto.SupplierLinkmanByOrgDTO;
import com.yyigou.dsrp.cdc.common.util.OperationModel;

import java.util.List;

public interface SupplierLinkManService {
    /**
     * 跨组织获取供应商联系人列表
     *
     * @param operationModel 操作人信息
     * @param supplierCode
     * @param orgNo
     * @return
     */
    List<CompanyLinkmanVO> getSupplierLinkmanListByOrg(OperationModel operationModel, String supplierCode, String orgNo);

    /**
     * 跨组织保存供应商联系人列表
     *
     * @param params
     * @return
     */
    CompanyLinkmanVO saveSupplierLinkmanByOrg(OperationModel operationModel, SupplierLinkmanByOrgDTO params);

    /**
     * 跨组织删除联系人
     *
     * @param params
     * @param
     * @return
     */
    CompanyLinkmanVO editSupplierLinkmanByOrg(OperationModel operationModel, SupplierLinkmanByOrgDTO params);

    /**
     * 跨组织删除联系人
     *
     * @param linkmanId
     * @param
     * @return
     */
    Boolean deleteSupplierLinkmanByOrg(OperationModel operationModel, Long linkmanId);
}
