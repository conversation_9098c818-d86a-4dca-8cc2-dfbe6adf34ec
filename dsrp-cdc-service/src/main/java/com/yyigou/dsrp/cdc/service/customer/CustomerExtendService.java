package com.yyigou.dsrp.cdc.service.customer;

import com.yyigou.dsrp.cdc.client.customer.request.CoordinationCustomerRequest;
import com.yyigou.dsrp.cdc.client.customer.request.CustomerAreaRequest;
import com.yyigou.dsrp.cdc.client.customer.response.AreaResponse;
import com.yyigou.dsrp.cdc.client.customer.response.CoordinationCustomerResponse;
import com.yyigou.dsrp.gcs.client.executeRecord.response.ExecuteRecordResponse;

import java.util.List;

public interface CustomerExtendService {

    void startCustomerAssignTask(ExecuteRecordResponse executeRecord);
    List<CoordinationCustomerResponse> queryCoordinationCustomer(CoordinationCustomerRequest params);

    List<AreaResponse> queryCustomerArea(CustomerAreaRequest params);

}
