package com.yyigou.dsrp.cdc.service.company;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yyigou.ddc.common.page.PageDto;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.ddc.common.service.SessionUser;
import com.yyigou.ddc.services.dsrp.bdc.dto.CompanyCertDto;
import com.yyigou.ddc.services.dsrp.bdc.vo.CertControlVo;
import com.yyigou.dsrp.cdc.api.company.dto.CompanyImportDTO;
import com.yyigou.dsrp.cdc.api.company.vo.CompanyBasicVO;
import com.yyigou.dsrp.cdc.api.company.vo.CompanyDetailVO;
import com.yyigou.dsrp.cdc.client.company.response.CompanyInfoResponse;
import com.yyigou.dsrp.cdc.common.util.OperationModel;
import com.yyigou.dsrp.cdc.dao.company.entity.Company;
import com.yyigou.dsrp.cdc.model.company.req.CompanyQueryListPageReq;
import com.yyigou.dsrp.cdc.model.company.req.CompanySaveReq;
import com.yyigou.dsrp.cdc.service.listener.model.CompanyUpdateModel;
import com.yyigou.dsrp.cert.client.certbase.req.CatalogInfoBigClientReq;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public interface CompanyService extends IService<Company> {

    List<CompanyInfoResponse> findListByCompanyNoListIgnoringEnterpriseNo(List<String> companyNoList);

    List<CompanyInfoResponse> findListByCompanyNoList(String enterpriseNo, List<String> companyNoList);

    List<CompanyInfoResponse> findListByCompanyCodeList(String enterpriseNo, List<String> companyCodeList);

    List<CompanyInfoResponse> findListByCompanyNameList(String enterpriseNo, List<String> companyNameList);

    /**
     * 保存企业档案信息
     *
     * @param operationModel
     * @param companySaveReq
     * @return: {@link CompanyBasicVO}
     */
    CompanyBasicVO saveCompany(OperationModel operationModel, CompanySaveReq companySaveReq);

    /**
     * 根据租户编号和企业编号获取企业档案详情
     *
     * @param enterpriseNo
     * @param companyNo
     * @return: {@link CompanyDetailVO}
     */
    CompanyDetailVO getDetailByEnterpriseAndCompanyNo(String enterpriseNo, String companyNo);

    CompanyDetailVO getDetailByEnterpriseAndCompanyName(String enterpriseNo, String companyName);


    /**
     * 获取本企业证照
     *
     * @param enterpriseNo
     * @return
     */
    CompanyDetailVO getEnterpriseDetail(String enterpriseNo);

    /**
     * 修改企业档案
     *
     * @param operationModel
     * @param companySaveReq
     * @return: {@link CompanyUpdateModel}
     */
    CompanyUpdateModel updateCompany(OperationModel operationModel, CompanySaveReq companySaveReq);


    List<CompanyInfoResponse> saveAndGetCompany(String enterpriseNo, List<String> companyNameList, String employerNo, String userName);

    void saveOrUpdateCert(CatalogInfoBigClientReq catalogInfoBigClientReq, String enterpriseNo, String companyNo, OperationModel operationModel);


    void updateCompany(OperationModel operationModel, Company company);

    /**
     * 根据租户编号+企业编码集合查询企业信息
     *
     * @param enterpriseNo
     * @param companyNoList
     * @return: {@link List< Company>}
     */
    List<Company> findByEnterpriseNoAndCompanyNoList(String enterpriseNo, List<String> companyNoList);

    PageVo<CompanyDetailVO> findCompanyListPage(CompanyQueryListPageReq queryReq, PageDto pageDTO);

    PageVo<CompanyDetailVO> findCompanyListPageForDing(CompanyQueryListPageReq queryReq, PageDto pageDTO);

    /**
     * 根据统一社会信用代码查询企业信息
     *
     * @param enterpriseNo
     * @param unifiedSocialCodeList
     * @return: {@link List< Company>}
     */
    List<Company> findByUnifiedSocialCodeList(String enterpriseNo, List<String> unifiedSocialCodeList);

    /**
     * 根据企业名称查询企业信息
     *
     * @param enterpriseNo
     * @param companyNameList
     * @return: {@link List< Company>}
     */
    List<Company> findByCompanyNameList(String enterpriseNo, List<String> companyNameList);

    List<CertControlVo> checkQualification(OperationModel operationModel, CompanyCertDto params);

    /**
     * 批量保存导入企业档案
     *
     * @param sessionUser
     * @param companyImportDTOList
     * @return: {@link List< CompanyImportDTO>}
     */
    List<CompanyImportDTO> saveImportCompanyBatch(SessionUser sessionUser, List<CompanyImportDTO> companyImportDTOList);
}
