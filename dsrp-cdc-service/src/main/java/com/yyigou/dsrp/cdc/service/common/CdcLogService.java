package com.yyigou.dsrp.cdc.service.common;

import com.yyigou.ddc.common.util.DateUtil;
import com.yyigou.ddc.common.util.ValidatorUtils;
import com.yyigou.dsrp.cdc.common.enums.CompanyLogTypeEnum;
import com.yyigou.dsrp.cdc.common.util.CommonUtil;
import com.yyigou.dsrp.cdc.common.util.OperationModel;
import com.yyigou.dsrp.cdc.dao.company.CompanyLogDAO;
import com.yyigou.dsrp.cdc.dao.company.entity.CompanyLog;
import com.yyigou.dsrp.cdc.dao.customer.CustomerLogDAO;
import com.yyigou.dsrp.cdc.dao.customer.entity.CustomerLog;
import com.yyigou.dsrp.cdc.dao.supplier.SupplierLogDAO;
import com.yyigou.dsrp.cdc.dao.supplier.entity.SupplierLog;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * cdc服务日志类
 *
 * @author:  Moore
 * @date: 2024/7/8 13:23
 * @version: 1.0.0
 */
@Service
@Slf4j
public class CdcLogService {

    @Autowired
    private CompanyLogDAO companyLogDAO;
    @Autowired
    private CustomerLogDAO customerLogDAO;
    @Autowired
    private SupplierLogDAO supplierLogDAO;

    /**
     * 保存企业日志
     *
     * @param operationModel
     * @param companyNo
     * @param type
     * @return:
     */
    public void saveCompanyLog(OperationModel operationModel, String companyNo, Integer type) {
        ValidatorUtils.checkEmptyThrowEx(operationModel.getEnterpriseNo(),"租户编号不能为空");
        ValidatorUtils.checkEmptyThrowEx(companyNo,"企业编号不能为空");
        ValidatorUtils.checkEmptyThrowEx(type,"操作类型不能为空");
        CompanyLog companyLog = new CompanyLog();
        companyLog.setEnterpriseNo(operationModel.getEnterpriseNo());
        companyLog.setCompanyNo(companyNo);
        companyLog.setOperateType(type);
        companyLog.setOperateContent(CompanyLogTypeEnum.getByType(type).getName());
        CommonUtil.fillOperateInfo(operationModel,companyLog);
        companyLogDAO.insert(companyLog);
    }

    /**
     * 保存客户日志
     *
     * @param customerNo
     * @param operationModel
     * @param content
     * @return:
     */
    public void saveCustomerLog(String customerNo, OperationModel operationModel, String content) {
        ValidatorUtils.checkEmptyThrowEx(customerNo,"客户编号不能为空");
        CustomerLog customerLog = new CustomerLog();
        customerLog.setCustomerNo(customerNo);
        customerLog.setOperateContent(content);
        customerLog.setOperateNo(operationModel.getEmployerNo());
        customerLog.setOperateName(operationModel.getUserName());
        customerLog.setOperateTime(DateUtil.getCurrentDate());
        customerLogDAO.insert(customerLog);
    }

    /**
     * 保存供应商日志
     *
     * @param supplierNo
     * @param operationModel
     * @param content
     * @return:
     */
    public void saveSupplierLog(String supplierNo, OperationModel operationModel, String content) {
        SupplierLog supplierLog = new SupplierLog();
        supplierLog.setSupplierNo(supplierNo);
        supplierLog.setOperateContent(content);
        supplierLog.setOperateNo(operationModel.getEmployerNo());
        supplierLog.setOperateName(operationModel.getUserName());
        supplierLog.setOperateTime(DateUtil.getCurrentDate());
        supplierLogDAO.insert(supplierLog);
    }
}
