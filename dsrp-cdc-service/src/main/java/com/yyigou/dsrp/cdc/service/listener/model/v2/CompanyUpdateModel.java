package com.yyigou.dsrp.cdc.service.listener.model.v2;

import com.yyigou.dsrp.cdc.common.util.OperationModel;
import com.yyigou.dsrp.cdc.dao.v2.company.entity.CompanyV2;
import lombok.Data;

import java.io.Serializable;

/**
 * 企业档案变更消息监听实体类
 *
 * @author:  Moore
 * @date: 2024/7/10 21:34
 * @version: 1.0.0
 */
@Data
public class CompanyUpdateModel implements Serializable {
    private static final long serialVersionUID = 3083247682745396838L;

    /**
     * 是否更新成功
     */
    private Boolean updateSuccess = false;

    /**
     * 旧企业档案信息
     */
    private CompanyV2 oldCompanyV2;

    /**
     * 新企业档案信息
     */
    private CompanyV2 newCompanyV2;

    /**
     * 操作用户信息
     */
    private OperationModel operationModel;

    /**
     * 租户编号
     */
    private String enterpriseNo;
}
