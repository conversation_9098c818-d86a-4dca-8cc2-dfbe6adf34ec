package com.yyigou.dsrp.cdc.service.supplier.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.yyigou.ddc.common.page.PageDto;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.ddc.common.util.ValidatorUtils;
import com.yyigou.ddc.services.ddc.uim.vo.EmployeeVo;
import com.yyigou.ddc.services.ddc.uim.vo.OrganizationVo;
import com.yyigou.ddc.services.dsrp.bdc.vo.SupplierOrderManVo;
import com.yyigou.dsrp.cdc.api.supply.dto.SupplierSalesManByOrgDTO;
import com.yyigou.dsrp.cdc.api.supply.dto.SupplierSalesManQueryBySpecifyOrgDTO;
import com.yyigou.dsrp.cdc.api.supply.dto.SupplierSalesManQueryDTO;
import com.yyigou.dsrp.cdc.common.enums.DeletedEnum;
import com.yyigou.dsrp.cdc.common.util.OperationModel;
import com.yyigou.dsrp.cdc.dao.customer.entity.CustomerSalesMan;
import com.yyigou.dsrp.cdc.dao.supplier.SupplierDAO;
import com.yyigou.dsrp.cdc.dao.supplier.SupplierOrderManDAO;
import com.yyigou.dsrp.cdc.dao.supplier.entity.Supplier;
import com.yyigou.dsrp.cdc.dao.supplier.entity.SupplierOrderMan;
import com.yyigou.dsrp.cdc.manager.integration.org.UimTenantService;
import com.yyigou.dsrp.cdc.manager.integration.uim.EmployeeService;
import com.yyigou.dsrp.cdc.service.supplier.SupplierSalesManService;
import com.yyigou.dsrp.cdc.service.utils.PageUtils;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class SupplierSalesManServiceImpl implements SupplierSalesManService {
    private final UimTenantService uimTenantService;
    private final SupplierDAO supplierDAO;
    private final SupplierOrderManDAO supplierOrderManDAO;
    private final EmployeeService employeeService;

    /**
     * 跨组织获取供应商负责人列表
     *
     * @param operationModel
     * @param supplierCode
     * @param orgNo
     * @return
     */
    @Override
    public List<SupplierOrderManVo> getSupplierSalesManListByOrg(OperationModel operationModel, String supplierCode, String orgNo) {
        ValidatorUtils.checkTrueThrowEx(StringUtils.isEmpty(supplierCode), "供应商编码不存在");
        ValidatorUtils.checkTrueThrowEx(StringUtils.isEmpty(orgNo), "组织编码不存在");
        //获取有权限的组织列表
        List<OrganizationVo> organizationList = uimTenantService.getAuthEnterpriseList(operationModel.getEnterpriseNo(), operationModel.getUserNo(), operationModel.getEmployerNo());
        final Optional<OrganizationVo> optional = organizationList.stream().filter(t -> orgNo.equals(t.getOrgNo())).findAny();
        if (!optional.isPresent()) {
            //无权限查询
            return new ArrayList<>();
        }
        final OrganizationVo organizationVo = optional.get();
        Supplier supplier = supplierDAO.getSupplierBySupplierCode(organizationVo.getBindingEnterpriseNo(), supplierCode);
        ValidatorUtils.checkTrueThrowEx(supplier == null, "供应商不存在");
        List<SupplierOrderManVo> supplierOrderManVoList = new ArrayList<>();
        //供应商联系人
        List<SupplierOrderMan> customerLinkmanList = supplierOrderManDAO.getSupplierOrderManListBySupplierNoList(supplier.getEnterpriseNo(), Collections.singletonList(supplier.getSupplierNo()));
        if (CollectionUtils.isNotEmpty(customerLinkmanList)) {
            final List<String> salesManNoList = customerLinkmanList.stream().map(SupplierOrderMan::getOrderManNo).collect(Collectors.toList());
            final List<EmployeeVo> employeeList = employeeService.getEmployeeList(supplier.getEnterpriseNo(), salesManNoList);
            Map<String, EmployeeVo> employeeMap = employeeList.stream().collect(Collectors.toMap(EmployeeVo::getEmployeeNo, Function.identity()));
            customerLinkmanList.forEach(t -> {
                SupplierOrderManVo vo = new SupplierOrderManVo();
                BeanUtils.copyProperties(t, vo);
                vo.setDeptNo(t.getOrgNo());
                vo.setDeptName(t.getOrgName());
                if (employeeMap.containsKey(t.getOrderManNo())) {
                    vo.setMobile(employeeMap.get(t.getOrderManNo()).getMobile());
                }
                supplierOrderManVoList.add(vo);
            });
        }
        return supplierOrderManVoList;
    }

    /**
     * 跨组织保存供应商负责人列表
     *
     * @param operationModel
     * @param params
     * @return
     */
    @Override
    public SupplierOrderManVo saveSupplierSalesManByOrg(OperationModel operationModel, SupplierSalesManByOrgDTO params) {
        ValidatorUtils.checkTrueThrowEx(StringUtils.isEmpty(params.getSupplierCode()), "供应商编码不存在");
        ValidatorUtils.checkTrueThrowEx(StringUtils.isEmpty(params.getOrgNo()), "组织编码不存在");
        //获取有权限的组织列表
        List<OrganizationVo> organizationList = uimTenantService.getAuthEnterpriseList(operationModel.getEnterpriseNo(), operationModel.getUserNo(), operationModel.getEmployerNo());
        final Optional<OrganizationVo> optional = organizationList.stream().filter(t -> params.getOrgNo().equals(t.getOrgNo())).findAny();
        ValidatorUtils.checkTrueThrowEx(!optional.isPresent(), "无权限操作");

        final OrganizationVo organizationVo = optional.get();
        Supplier supplier = supplierDAO.getSupplierBySupplierCode(organizationVo.getBindingEnterpriseNo(), params.getSupplierCode());
        ValidatorUtils.checkTrueThrowEx(supplier == null, "供应商不存在");
        //说明这个人有权限操作这个供应商
        //应该走新增逻辑
        SupplierOrderMan supplierOrderMan = new SupplierOrderMan();
        supplierOrderMan.setSupplierNo(supplier.getSupplierNo());
        supplierOrderMan.setManCode(UUID.randomUUID().toString());
        supplierOrderMan.setOrderManNo(params.getOrderManNo());
        supplierOrderMan.setOrderManName(params.getOrderManName());
        supplierOrderMan.setOrgNo(params.getDeptNo());
        supplierOrderMan.setOrgName(params.getDeptName());
        supplierOrderMan.setPost(params.getPost());
        supplierOrderMan.setIsDefault(params.getIsDefault());
        supplierOrderMan.setOrderSpecialist(params.getOrderSpecialist());
        supplierOrderMan.setPost(params.getPost());
        supplierOrderManDAO.insert(supplierOrderMan);
        //如果新保存的这个条记录是默认的，则将之前的默认联系人修改为非默认
        if (new Integer(1).equals(params.getIsDefault())) {
            SupplierOrderMan updateDefault = new SupplierOrderMan();
            updateDefault.setIsDefault(0);
            supplierOrderManDAO.update(updateDefault, new QueryWrapper<SupplierOrderMan>().lambda()
                    .eq(SupplierOrderMan::getSupplierNo, supplier.getSupplierNo()).eq(SupplierOrderMan::getIsDefault, 1)
                    .eq(SupplierOrderMan::getDeleted, DeletedEnum.UN_DELETE.getValue()).ne(SupplierOrderMan::getId, supplierOrderMan.getId()));
        }
        SupplierOrderManVo orderManVo = new SupplierOrderManVo();
        BeanUtils.copyProperties(supplierOrderMan, orderManVo);
        return orderManVo;
    }

    /**
     * 跨组织删除负责人
     *
     * @param operationModel
     * @param params
     * @return
     */
    @Override
    public SupplierOrderManVo editSupplierSalesManByOrg(OperationModel operationModel, SupplierSalesManByOrgDTO params) {
        ValidatorUtils.checkTrueThrowEx(StringUtils.isEmpty(params.getSupplierCode()), "供应商编码不存在");
        ValidatorUtils.checkTrueThrowEx(StringUtils.isEmpty(params.getOrgNo()), "组织编码不存在");
        //获取有权限的组织列表
        List<OrganizationVo> organizationList = uimTenantService.getAuthEnterpriseList(operationModel.getEnterpriseNo(), operationModel.getUserNo(), operationModel.getEmployerNo());
        final Optional<OrganizationVo> optional = organizationList.stream().filter(t -> params.getOrgNo().equals(t.getOrgNo())).findAny();
        ValidatorUtils.checkTrueThrowEx(!optional.isPresent(), "无权限操作");

        final OrganizationVo organizationVo = optional.get();
        Supplier supplier = supplierDAO.getSupplierBySupplierCode(organizationVo.getBindingEnterpriseNo(), params.getSupplierCode());
        ValidatorUtils.checkTrueThrowEx(supplier == null, "供应商不存在");

        final SupplierOrderMan supplierOrderMan = supplierOrderManDAO.selectById(params.getId());
        ValidatorUtils.checkTrueThrowEx(supplierOrderMan == null, "联系人信息不存在");

        //说明这个人有权限操作这个供应商
        //应该走新增逻辑
        supplierOrderMan.setOrderManNo(params.getOrderManNo());
        supplierOrderMan.setOrderManName(params.getOrderManName());
        supplierOrderMan.setOrgNo(params.getDeptNo());
        supplierOrderMan.setOrgName(params.getDeptName());
        supplierOrderMan.setPost(params.getPost());
        supplierOrderMan.setIsDefault(params.getIsDefault());
        supplierOrderMan.setOrderSpecialist(params.getOrderSpecialist());
        supplierOrderMan.setPost(params.getPost());
        supplierOrderManDAO.updateById(supplierOrderMan);
        //如果新保存的这个条记录是默认的，则将之前的默认联系人修改为非默认
        if (new Integer(1).equals(params.getIsDefault())) {
            SupplierOrderMan updateDefault = new SupplierOrderMan();
            updateDefault.setIsDefault(0);
            supplierOrderManDAO.update(updateDefault, new QueryWrapper<SupplierOrderMan>().lambda()
                    .eq(SupplierOrderMan::getSupplierNo, supplier.getSupplierNo()).eq(SupplierOrderMan::getIsDefault, 1)
                    .eq(SupplierOrderMan::getDeleted, DeletedEnum.UN_DELETE.getValue()).ne(SupplierOrderMan::getId, supplierOrderMan.getId()));
        }
        SupplierOrderManVo orderManVo = new SupplierOrderManVo();
        BeanUtils.copyProperties(supplierOrderMan, orderManVo);
        return orderManVo;
    }

    /**
     * 跨组织删除负责人
     *
     * @param salesManId
     * @return
     */
    @Override
    public Boolean deleteSupplierSalesManOrg(OperationModel operationModel, Long salesManId) {
        //获取有权限的组织列表
        List<OrganizationVo> organizationList = uimTenantService.getAuthEnterpriseList(operationModel.getEnterpriseNo(), operationModel.getUserNo(), operationModel.getEmployerNo());
        ValidatorUtils.checkTrueThrowEx(CollectionUtils.isEmpty(organizationList), "无权限删除负责人");
        final SupplierOrderMan supplierOrderMan = supplierOrderManDAO.selectById(salesManId);
        ValidatorUtils.checkTrueThrowEx(supplierOrderMan == null, "负责人不存在");
        Supplier supplier = supplierDAO.getSupplierByNoEnterpriseNo(supplierOrderMan.getSupplierNo());
        ValidatorUtils.checkTrueThrowEx(supplier == null, "供应商不存在");
        final Optional<OrganizationVo> optional = organizationList.stream().filter(t -> supplier.getEnterpriseNo().equals(t.getBindingEnterpriseNo())).findAny();
        ValidatorUtils.checkTrueThrowEx(!optional.isPresent(), "无权限删除负责人");
        supplierOrderMan.setDeleted(DeletedEnum.DELETED.getValue());
        supplierOrderManDAO.updateById(supplierOrderMan);
        return Boolean.TRUE;
    }


    /**
     * 获取供应商负责人列表
     *
     * @param params
     * @return
     */
    @Override
    public List<SupplierOrderManVo> findList(OperationModel operationModel, SupplierSalesManQueryDTO params) {
        LambdaQueryWrapper<SupplierOrderMan> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(SupplierOrderMan::getDeleted, DeletedEnum.UN_DELETE.getValue());
        handleQueryParams(lambdaQueryWrapper, params);
        lambdaQueryWrapper.inSql(SupplierOrderMan::getSupplierNo, "select supplier_no  from  bdc_supplier where deleted=0 and enterprise_no in ('" + operationModel.getEnterpriseNo() + "')");
        final List<SupplierOrderMan> list = supplierOrderManDAO.selectList(lambdaQueryWrapper);
        return list.stream().map(supplierOrderMan -> {
            SupplierOrderManVo supplierOrderManVo = new SupplierOrderManVo();
            BeanUtils.copyProperties(supplierOrderMan, supplierOrderManVo);
            return supplierOrderManVo;
        }).collect(Collectors.toList());
    }

    /**
     * 获取供应商负责人列表
     *
     * @param params
     * @param pageDto
     * @return
     */
    @Override
    public PageVo<SupplierOrderManVo> findPage(OperationModel operationModel, SupplierSalesManQueryDTO params, PageDto pageDto) {
        Page<CustomerSalesMan> page = PageHelper.startPage(pageDto.getPageIndex(), pageDto.getPageSize(), StringUtils.isBlank(pageDto.getOrderBy()) ? "create_time desc" : pageDto.getOrderBy());
        LambdaQueryWrapper<SupplierOrderMan> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(SupplierOrderMan::getDeleted, DeletedEnum.UN_DELETE.getValue());
        handleQueryParams(lambdaQueryWrapper, params);
        lambdaQueryWrapper.inSql(SupplierOrderMan::getSupplierNo, "select supplier_no  from  bdc_supplier where deleted=0 and enterprise_no in ('" + operationModel.getEnterpriseNo() + "')");
        supplierOrderManDAO.selectList(lambdaQueryWrapper);
        return PageUtils.convertPageVo(page, data -> {
            List<SupplierOrderManVo> result = new ArrayList<>();
            data.forEach(t -> {
                SupplierOrderManVo vo = new SupplierOrderManVo();
                BeanUtils.copyProperties(t, vo);
                result.add(vo);
            });
            return result;
        });
    }

    /**
     * 获取供应商负责人列表
     *
     * @param params
     * @return
     */
    @Override
    public List<SupplierOrderManVo> findListBySpecifyOrg(OperationModel operationModel, SupplierSalesManQueryBySpecifyOrgDTO params) {
        List<OrganizationVo> organizationList = uimTenantService.getAuthEnterpriseList(operationModel.getEnterpriseNo(), operationModel.getUserNo(), operationModel.getEmployerNo());
        final Optional<OrganizationVo> optional = organizationList.stream().filter(t -> params.getOrgNo().equals(t.getOrgNo())).findAny();
        if (!optional.isPresent()) {
            //无权限查询
            return new ArrayList<>();
        }
        LambdaQueryWrapper<SupplierOrderMan> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(SupplierOrderMan::getDeleted, DeletedEnum.UN_DELETE.getValue());
        handleQueryParams(lambdaQueryWrapper, params);
        lambdaQueryWrapper.inSql(SupplierOrderMan::getSupplierNo, "select supplier_no  from  bdc_supplier where deleted=0 and enterprise_no in ('" + optional.get().getBindingEnterpriseNo() + "')");
        final List<SupplierOrderMan> list = supplierOrderManDAO.selectList(lambdaQueryWrapper);
        return list.stream().map(customerSalesMan -> {
            SupplierOrderManVo vo = new SupplierOrderManVo();
            BeanUtils.copyProperties(customerSalesMan, vo);
            vo.setDeptNo(customerSalesMan.getOrgNo());
            vo.setDeptName(customerSalesMan.getOrgName());
            return vo;

        }).collect(Collectors.toList());
    }

    /**
     * 获取供应商负责人列表
     *
     * @param params
     * @param pageDto
     * @return
     */
    @Override
    public PageVo<SupplierOrderManVo> findPageBySpecifyOrg(OperationModel operationModel, SupplierSalesManQueryBySpecifyOrgDTO params, PageDto pageDto) {
        List<OrganizationVo> organizationList = uimTenantService.getAuthEnterpriseList(operationModel.getEnterpriseNo(), operationModel.getUserNo(), operationModel.getEmployerNo());
        final Optional<OrganizationVo> optional = organizationList.stream().filter(t -> params.getOrgNo().equals(t.getOrgNo())).findAny();
        if (!optional.isPresent()) {
            //无权限查询
            return new PageVo<>();
        }
        Page<CustomerSalesMan> page = PageHelper.startPage(pageDto.getPageIndex(), pageDto.getPageSize(), StringUtils.isBlank(pageDto.getOrderBy()) ? "create_time desc" : pageDto.getOrderBy());
        LambdaQueryWrapper<SupplierOrderMan> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(SupplierOrderMan::getDeleted, DeletedEnum.UN_DELETE.getValue());
        handleQueryParams(lambdaQueryWrapper, params);
        lambdaQueryWrapper.inSql(SupplierOrderMan::getSupplierNo, "select supplier_no  from  bdc_supplier where deleted=0 and enterprise_no in ('" + optional.get().getBindingEnterpriseNo() + "')");
        supplierOrderManDAO.selectList(lambdaQueryWrapper);
        return PageUtils.convertPageVo(page, data -> {
            List<SupplierOrderManVo> result = new ArrayList<>();
            data.forEach(t -> {
                SupplierOrderManVo vo = new SupplierOrderManVo();
                BeanUtils.copyProperties(t, vo);
                result.add(vo);
            });
            return result;
        });
    }

    private void handleQueryParams(LambdaQueryWrapper<SupplierOrderMan> wrapper, SupplierSalesManQueryDTO params) {
        wrapper.in(CollectionUtils.isNotEmpty(params.getSupplierNoList()), SupplierOrderMan::getSupplierNo, params.getSupplierNoList())
                .in(CollectionUtils.isNotEmpty(params.getDeptNoList()), SupplierOrderMan::getOrgNo, params.getDeptNoList())
                .in(CollectionUtils.isNotEmpty(params.getIsDefaultList()), SupplierOrderMan::getIsDefault, params.getIsDefaultList())
                .in(CollectionUtils.isNotEmpty(params.getOrderSpecialistList()), SupplierOrderMan::getOrderSpecialist, params.getOrderSpecialistList())
        ;
    }
}
