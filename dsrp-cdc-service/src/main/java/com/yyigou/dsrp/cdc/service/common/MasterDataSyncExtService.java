package com.yyigou.dsrp.cdc.service.common;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.yyigou.ddc.common.util.DateUtil;
import com.yyigou.ddc.services.ddc.uim.vo.OrganizationTreeVo;
import com.yyigou.ddc.services.mq.dto.JmsHeadersDto;
import com.yyigou.ddc.services.mq.manager.MessageQueueManager;
import com.yyigou.ddc.services.mq.manager.exception.MessageQueueException;
import com.yyigou.dsrp.cdc.common.enums.CompanyTypeEnum;
import com.yyigou.dsrp.cdc.common.util.OperationModel;
import com.yyigou.dsrp.cdc.dao.customer.entity.Customer;
import com.yyigou.dsrp.cdc.dao.gradedcontrol.SupplierCustomerUseInfoDAO;
import com.yyigou.dsrp.cdc.dao.gradedcontrol.entity.SupplierCustomerUseInfo;
import com.yyigou.dsrp.cdc.dao.supplier.entity.Supplier;
import com.yyigou.dsrp.cdc.manager.integration.exeresult.MasterDataExecuteService;
import com.yyigou.dsrp.cdc.manager.integration.org.UimTenantService;
import com.yyigou.dsrp.cdc.model.constant.CdcMqConstant;
import com.yyigou.dsrp.cdc.service.listener.model.CustomerAssignModel;
import com.yyigou.dsrp.cdc.service.listener.model.SupplierAssignModel;
import com.yyigou.dsrp.gcs.client.executeRecord.request.SaveExecuteRecordRequest;
import com.yyigou.dsrp.gcs.common.enums.executeRecord.ExecuteRecordDataTypeEnum;
import com.yyigou.dsrp.gcs.common.enums.executeRecord.ExecuteRecordExcuteTypeEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.activemq.ScheduledMessage;
import org.apache.activemq.command.ActiveMQTopic;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
@Slf4j
public class MasterDataSyncExtService {
    private final SupplierCustomerUseInfoDAO supplierCustomerUseInfoDAO;
    private final MessageQueueManager messageQueueManager;
    private final MasterDataExecuteService masterDataExecuteService;
    private final UimTenantService uimTenantService;

    @Async("maserDateSyncExecutor")
    public void updateSyncSubTenantCustomer(String customerCode, Customer customer, OperationModel operationModel) {
        List<SupplierCustomerUseInfo> usedSkuCodeList = supplierCustomerUseInfoDAO.findGroupSupplierCustomerUseList(operationModel.getEnterpriseNo(), Lists.newArrayList(customerCode), CompanyTypeEnum.CUSTOMER.getValue());
        if (CollectionUtils.isNotEmpty(usedSkuCodeList)) {
            final List<OrganizationTreeVo> organizationTreeList = uimTenantService.getOrganizationTreeList(operationModel.getEnterpriseNo());
            final Map<String, OrganizationTreeVo> organizationMap = organizationTreeList.stream().collect(Collectors.toMap(OrganizationTreeVo::getBindingEnterpriseNo, Function.identity()));
            usedSkuCodeList.forEach(t -> {
                CustomerAssignModel model = new CustomerAssignModel();
                model.setCustomerCode(customerCode);
                model.setOperationModel(operationModel);
                model.setGroupEnterpriseNo(operationModel.getEnterpriseNo());
                model.setSubEnterpriseNo(t.getUseEnterpriseNo());
                model.setExecuteRecordExecuteTypeEnum(ExecuteRecordExcuteTypeEnum.CHANGE_UPDATE);
                try {
                    if (organizationMap.containsKey(t.getUseEnterpriseNo())) {
                        final OrganizationTreeVo organizationTreeVo = organizationMap.get(t.getUseEnterpriseNo());
                        SaveExecuteRecordRequest recordRequest = new SaveExecuteRecordRequest();
                        recordRequest.setDataType(ExecuteRecordDataTypeEnum.CUSTOMER);
                        recordRequest.setExecuteType(ExecuteRecordExcuteTypeEnum.CHANGE_UPDATE);
                        recordRequest.setApplyOrgNo(organizationTreeVo.getOrgNo());
                        recordRequest.setApplyOrgName(organizationTreeVo.getOrgName());
                        recordRequest.setApplySource("DSRP");
                        recordRequest.setApplyEnterpriseNo(t.getUseEnterpriseNo());
                        recordRequest.setGroupEnterpriseNo(operationModel.getEnterpriseNo());
                        recordRequest.setManageEnterpriseNo(operationModel.getEnterpriseNo());
                        recordRequest.setObjNo(customer.getCustomerNo());
                        recordRequest.setObjCode(customer.getCustomerCode());
                        recordRequest.setObjName(customer.getCustomerName());
                        recordRequest.setExecuteTime(DateUtil.getCurrentDate());
                        recordRequest.setCreateNo(operationModel.getEmployerNo());
                        recordRequest.setCreateName(operationModel.getUserName());
                        final Integer recordId = masterDataExecuteService.saveExecuteRecordNoExecute(recordRequest);
                        model.setRecordId(recordId);
                        log.warn("客户{}开始更新分派", JSON.toJSONString(model));

                        JmsHeadersDto jmsHeadersDto = new JmsHeadersDto();
                        jmsHeadersDto.setSessionTransacted(true);
                        Map<String, Object> customPropMap = new HashMap<>();
                        customPropMap.put(ScheduledMessage.AMQ_SCHEDULED_DELAY, 5000L);
                        messageQueueManager.produceMessage(new ActiveMQTopic(CdcMqConstant.CDC_CUSTOMER_ASSIGN_TOPIC), JSON.toJSONString(model), customPropMap, jmsHeadersDto);
                    }
                } catch (MessageQueueException e) {
                    throw new RuntimeException(e);
                }
            });
        }

    }

    @Async("maserDateSyncExecutor")
    public void updateSyncSubTenantSupplier(String supplierCode, Supplier supplier, OperationModel operationModel) {
        List<SupplierCustomerUseInfo> usedSkuCodeList = supplierCustomerUseInfoDAO.findGroupSupplierCustomerUseList(operationModel.getEnterpriseNo(), Lists.newArrayList(supplierCode), CompanyTypeEnum.SUPPLIER.getValue());
        final List<OrganizationTreeVo> organizationTreeList = uimTenantService.getOrganizationTreeList(operationModel.getEnterpriseNo());
        final Map<String, OrganizationTreeVo> organizationMap = organizationTreeList.stream().collect(Collectors.toMap(OrganizationTreeVo::getBindingEnterpriseNo, Function.identity()));
        if (CollectionUtils.isNotEmpty(usedSkuCodeList)) {
            usedSkuCodeList.forEach(t -> {
                SupplierAssignModel model = new SupplierAssignModel();
                model.setSupplierCode(supplierCode);
                model.setOperationModel(operationModel);
                model.setGroupEnterpriseNo(operationModel.getEnterpriseNo());
                model.setSubEnterpriseNo(t.getUseEnterpriseNo());
                model.setExecuteRecordExecuteTypeEnum(ExecuteRecordExcuteTypeEnum.CHANGE_UPDATE);
                try {
                    if (organizationMap.containsKey(t.getUseEnterpriseNo())) {
                        final OrganizationTreeVo organizationTreeVo = organizationMap.get(t.getUseEnterpriseNo());
                        SaveExecuteRecordRequest recordRequest = new SaveExecuteRecordRequest();
                        recordRequest.setDataType(ExecuteRecordDataTypeEnum.SUPPLIER);
                        recordRequest.setExecuteType(ExecuteRecordExcuteTypeEnum.CHANGE_UPDATE);
                        recordRequest.setApplyOrgNo(organizationTreeVo.getOrgNo());
                        recordRequest.setApplyOrgName(organizationTreeVo.getOrgName());
                        recordRequest.setApplySource("DSRP");
                        recordRequest.setApplyEnterpriseNo(t.getUseEnterpriseNo());
                        recordRequest.setGroupEnterpriseNo(operationModel.getEnterpriseNo());
                        recordRequest.setManageEnterpriseNo(operationModel.getEnterpriseNo());
                        recordRequest.setObjNo(supplier.getSupplierNo());
                        recordRequest.setObjCode(supplier.getSupplierCode());
                        recordRequest.setObjName(supplier.getSupplierName());
                        recordRequest.setExecuteTime(DateUtil.getCurrentDate());
                        recordRequest.setCreateNo(operationModel.getEmployerNo());
                        recordRequest.setCreateName(operationModel.getUserName());
                        final Integer recordId = masterDataExecuteService.saveExecuteRecordNoExecute(recordRequest);
                        model.setRecordId(recordId);
                        log.warn("供应商{}开始更新分派", JSON.toJSONString(model));

                        JmsHeadersDto jmsHeadersDto = new JmsHeadersDto();
                        jmsHeadersDto.setSessionTransacted(true);

                        Map<String, Object> customPropMap = new HashMap<>();
                        customPropMap.put(ScheduledMessage.AMQ_SCHEDULED_DELAY, 5000L);
                        messageQueueManager.produceMessage(new ActiveMQTopic(CdcMqConstant.CDC_SUPPLIER_ASSIGN_TOPIC), JSON.toJSONString(model), customPropMap, jmsHeadersDto);
                    }
                } catch (MessageQueueException e) {
                    throw new RuntimeException(e);
                }
            });
        }

    }

}
