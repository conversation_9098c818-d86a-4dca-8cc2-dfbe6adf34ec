package com.yyigou.dsrp.cdc.service.listener;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yyigou.dsrp.cdc.common.enums.CommonIfEnum;
import com.yyigou.dsrp.cdc.common.enums.DeletedEnum;
import com.yyigou.dsrp.cdc.dao.customer.CustomerDAO;
import com.yyigou.dsrp.cdc.dao.customer.entity.Customer;
import com.yyigou.dsrp.cdc.dao.supplier.SupplierDAO;
import com.yyigou.dsrp.cdc.dao.supplier.entity.Supplier;
import com.yyigou.dsrp.cdc.service.listener.model.CompanyCertUpdateModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jms.listener.SessionAwareMessageListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.jms.JMSException;
import javax.jms.Message;
import javax.jms.Session;
import javax.jms.TextMessage;
import java.util.List;
import java.util.stream.Collectors;

@Component
@Slf4j
public class CompanyCertUpdateListener implements SessionAwareMessageListener {
    @Resource
    private SupplierDAO supplierDAO;
    @Resource
    private CustomerDAO customerDAO;

    /**
     * 企业
     *
     * @param message
     * @param session
     * @throws JMSException
     */
    @Override
    public void onMessage(Message message, Session session) throws JMSException {
        //  获取消息
        try {
            TextMessage textMessage = (TextMessage) message;
            log.warn("客商监听企业证照变更消息,参数:" + textMessage.getText());
            //1、企业证照更换 修改影响的产品档案
            CompanyCertUpdateModel model = JSONObject.parseObject(textMessage.getText(), CompanyCertUpdateModel.class);
            if (StringUtils.isBlank(model.getCompanyNo()) || StringUtils.isBlank(model.getEnterpriseNo())) {
                log.warn("数据不合法");
                return;
            }
            // 将供应商变成未推送
            LambdaQueryWrapper<Supplier> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(Supplier::getEnterpriseNo, model.getEnterpriseNo()).eq(Supplier::getCompanyNo, model.getCompanyNo())
                    .eq(Supplier::getDeleted, DeletedEnum.UN_DELETE.getValue());
            List<Supplier> supplierList = supplierDAO.selectList(lambdaQueryWrapper);
            if (CollectionUtils.isNotEmpty(supplierList)) {
                log.warn("企业{}证照变更,变更供应商档案为未推送{}", model.getCompanyNo(), supplierList.stream().map(Supplier::getSupplierCode).collect(Collectors.toList()));
                supplierList.forEach(t -> {
                    t.setYsSyncFlag(CommonIfEnum.NO.getValue().toString());
                    t.setYsPushResult("");
                    t.setIsSyncWms(CommonIfEnum.NO.getValue());
                    t.setWmsPushResult("");
                    supplierDAO.updateById(t);
                });
            } else {
                log.warn("企业{}证照变更,未影响任何供应商", model.getCompanyNo());
            }
            // 将客户变成未推送
            LambdaQueryWrapper<Customer> customerLambdaQueryWrapper = new LambdaQueryWrapper<>();
            customerLambdaQueryWrapper.eq(Customer::getEnterpriseNo, model.getEnterpriseNo()).eq(Customer::getCompanyNo, model.getCompanyNo())
                    .eq(Customer::getDeleted, DeletedEnum.UN_DELETE.getValue());
            List<Customer> customerList = customerDAO.selectList(customerLambdaQueryWrapper);
            if (CollectionUtils.isNotEmpty(customerList)) {
                log.warn("企业{}证照变更,变更客户档案为未推送{}", model.getCompanyNo(), customerList.stream().map(Customer::getCustomerCode).collect(Collectors.toList()));
                customerList.forEach(t -> {
                    t.setYsSyncFlag(CommonIfEnum.NO.getValue().toString());
                    t.setYsPushResult("");
                    t.setIsSyncWms(CommonIfEnum.NO.getValue());
                    t.setWmsPushResult("");
                    customerDAO.updateById(t);
                });
            } else {
                log.warn("企业{}证照变更,未影响任何客户", model.getCompanyNo());
            }


        } catch (Exception e) {
            log.error("客商监听企业证照变更消息,处理失败", e);
        } finally {
            // 主动发送消息回执, 告知broker消息已经被消费了
            try {
                message.acknowledge();
            } catch (JMSException e) {
                log.error(e.getMessage(), e);
            }
        }

    }
}
