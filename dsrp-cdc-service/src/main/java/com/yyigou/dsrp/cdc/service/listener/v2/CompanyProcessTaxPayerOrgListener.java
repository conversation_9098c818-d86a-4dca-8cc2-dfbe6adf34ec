package com.yyigou.dsrp.cdc.service.listener.v2;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yyigou.ddc.common.util.DateUtil;
import com.yyigou.dsrp.cdc.api.v2.company.vo.CompanyBasicVO;
import com.yyigou.dsrp.cdc.common.enums.CommonStatusEnum;
import com.yyigou.dsrp.cdc.common.enums.CompanyAssociatedOrgTypeEnum;
import com.yyigou.dsrp.cdc.common.enums.DeletedEnum;
import com.yyigou.dsrp.cdc.common.util.BeanUtil;
import com.yyigou.dsrp.cdc.dao.v2.company.CompanyAssociatedOrgDAO;
import com.yyigou.dsrp.cdc.dao.v2.company.CompanyV2DAO;
import com.yyigou.dsrp.cdc.dao.v2.company.entity.CompanyAssociatedOrg;
import com.yyigou.dsrp.cdc.dao.v2.company.entity.CompanyV2;
import com.yyigou.dsrp.cdc.model.v2.company.req.CompanySaveReq;
import com.yyigou.dsrp.cdc.service.listener.model.v2.CreateTaxPayerOrgModel;
import com.yyigou.dsrp.cdc.service.v2.company.CompanyV2Service;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jms.listener.SessionAwareMessageListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.jms.JMSException;
import javax.jms.Message;
import javax.jms.Session;
import javax.jms.TextMessage;
import java.util.List;
import java.util.Objects;


/**
 * 监听纳税主体新增/变更消息，新增/修改企业档案；
 */
@Component
@Slf4j
public class CompanyProcessTaxPayerOrgListener implements SessionAwareMessageListener {
    @Resource
    private CompanyV2DAO companyV2DAO;

    @Resource
    private CompanyAssociatedOrgDAO companyAssociatedOrgDAO;

    @Resource
    private CompanyV2Service companyV2Service;

    /**
     * 1. 根据纳税人识别号查询企业档案是否存在
     * 1.1 如果存在，根据纳税人名称查询企业档案是否存在
     * 1.1.1 如果也存在，检查两次查询结果是否是同一企业，不是则异常，记录日志，同时清空该组织的关联关系
     * 1.1.2 如果也存在，如果是同一企业，则是合法数据，建立关联关系
     * 1.1.3 如果根据名称查不到，则异常，记录日志，同时清空该组织的关联关系
     * <p>
     * 1.2 如果不存在，继续根据纳税人名称查询企业档案是否存在
     * 1.2.1 不存在，创建新企业，建立关联关系
     * 1.2.2 存在，也是异常数据，记录日志，同时清空异常关联关系
     *
     * @param message
     * @param session
     * @throws JMSException
     */
    @Override
    public void onMessage(Message message, Session session) throws JMSException {
        //  获取消息
        try {
            TextMessage textMessage = (TextMessage) message;
            log.warn("企业监听纳税主体新增/变更消息,参数:" + textMessage.getText());
            CreateTaxPayerOrgModel model = JSONObject.parseObject(textMessage.getText(), CreateTaxPayerOrgModel.class);
            if (StringUtils.isBlank(model.getAssociatedOrgNo()) || StringUtils.isBlank(model.getEnterpriseNo())) {
                log.warn("数据不合法");
                return;
            }

            List<CompanyV2> companyV2ListByUnifiedSocialCode = companyV2DAO.selectList(Wrappers.lambdaQuery(CompanyV2.class)
                    .eq(CompanyV2::getEnterpriseNo, model.getEnterpriseNo())
                    .eq(CompanyV2::getDeleted, DeletedEnum.UN_DELETE.getValue())
                    .eq(CompanyV2::getUnifiedSocialCode, model.getTaxpayerIdentityNumber())
            );
            if (CollectionUtils.isNotEmpty(companyV2ListByUnifiedSocialCode)) {
                List<CompanyV2> companyV2ListByName = companyV2DAO.selectList(Wrappers.lambdaQuery(CompanyV2.class)
                        .eq(CompanyV2::getEnterpriseNo, model.getEnterpriseNo())
                        .eq(CompanyV2::getDeleted, DeletedEnum.UN_DELETE.getValue())
                        .eq(CompanyV2::getCompanyName, model.getTaxpayerIdentityName())
                );
                if (companyV2ListByUnifiedSocialCode.size() > 1) {
                    log.error("异常数据,根据社会信用代码找到多个企业:{}", textMessage.getText());
                    // 删除该组织关联的企业关联关系记录
                    deleteOrgAssociatedCompanyRelation(model);
                    return;
                }

                if (CollectionUtils.isNotEmpty(companyV2ListByName)) {
                    if (checkIsSameCompany(companyV2ListByUnifiedSocialCode, companyV2ListByName, model)) {
                        // 1.1.2 分支
                        saveOrgAssociatedCompanyRelation(model, companyV2ListByUnifiedSocialCode.get(0).getCompanyNo());
                        return;
                    } else {
                        // 1.1.1 分支
                        log.error("异常数据，查询到的企业不唯一:{}", textMessage.getText());
                        // 删除该组织关联的企业关联关系记录
                        deleteOrgAssociatedCompanyRelation(model);
                        return;
                    }
                } else {
                    // 1.1.3 分支
                    log.error("异常数据，查询到的企业名称不一致:{}", textMessage.getText());
                    // 删除该组织关联的企业关联关系记录
                    deleteOrgAssociatedCompanyRelation(model);
                    return;
                }
            } else {
                List<CompanyV2> companyV2ListByName = companyV2DAO.selectList(Wrappers.lambdaQuery(CompanyV2.class)
                        .eq(CompanyV2::getEnterpriseNo, model.getEnterpriseNo())
                        .eq(CompanyV2::getDeleted, DeletedEnum.UN_DELETE.getValue())
                        .eq(CompanyV2::getCompanyName, model.getTaxpayerIdentityName())
                );
                if (CollectionUtils.isNotEmpty(companyV2ListByName)) {
                    // 1.2.2 分支，说明企业名已经被其他社会信用代码的企业使用，异常数据
                    log.error("异常数据，企业名已经被其他社会信用代码的企业使用:{}", textMessage.getText());
                    deleteOrgAssociatedCompanyRelation(model);
                    return;
                } else {
                    // 新增企业
                    CompanySaveReq companySaveReq = new CompanySaveReq();
                    companySaveReq.setCompanyName(model.getTaxpayerIdentityName());
                    companySaveReq.setUnifiedSocialCode(model.getTaxpayerIdentityNumber());
                    CompanyBasicVO simpleCompany = companyV2Service.createSimpleCompany(model.getEnterpriseNo(), companySaveReq);
                    saveOrgAssociatedCompanyRelation(model, simpleCompany.getCompanyNo());
                    return;
                }
            }

        } catch (Exception e) {
            log.error("企业监听纳税主体新增/变更消息,处理失败", e);
        } finally {
            // 主动发送消息回执, 告知broker消息已经被消费了
            try {
                message.acknowledge();
            } catch (JMSException e) {
                log.error(e.getMessage(), e);
            }
        }

    }

    /**
     * 保存组织关联企业关系，来源是纳税主体业务单元
     */
    private void saveOrgAssociatedCompanyRelation( CreateTaxPayerOrgModel model, String bindCompanyNo) {
        String enterpriseNo = model.getEnterpriseNo();
        CompanyAssociatedOrg companyAssociatedOrg = companyAssociatedOrgDAO.selectBySourceTypeAndCode(enterpriseNo, CompanyAssociatedOrgTypeEnum.TAXPAYER_ORG.getValue(), model.getAssociatedOrgNo());
        if (companyAssociatedOrg == null) {
            companyAssociatedOrg = BeanUtil.copyFields(model, CompanyAssociatedOrg.class);
            companyAssociatedOrg.setEnterpriseNo(enterpriseNo);
            companyAssociatedOrg.setDeleted(DeletedEnum.UN_DELETE.getValue());
            companyAssociatedOrg.setSourceType(CompanyAssociatedOrgTypeEnum.TAXPAYER_ORG.getValue());
            companyAssociatedOrg.setStatus(CommonStatusEnum.EFFECTIVE.getValue());
            companyAssociatedOrg.setSourceCode(model.getAssociatedOrgNo());
            companyAssociatedOrg.setModifyTime(DateUtil.getCurrentDate());
            companyAssociatedOrg.setCreateTime(DateUtil.getCurrentDate());
            companyAssociatedOrg.setCompanyNo(bindCompanyNo);
            companyAssociatedOrgDAO.insert(companyAssociatedOrg);
        } else if (!Objects.equals(companyAssociatedOrg.getCompanyNo(), bindCompanyNo)) {
            companyAssociatedOrg.setCompanyNo(bindCompanyNo);
            companyAssociatedOrg.setModifyTime(DateUtil.getCurrentDate());
            companyAssociatedOrgDAO.updateById(enterpriseNo, companyAssociatedOrg);
        }
    }

    /**
     * 删除组织关联企业关系，来源是纳税主体业务单元
     */
    private void deleteOrgAssociatedCompanyRelation(CreateTaxPayerOrgModel model) {
        String enterpriseNo = model.getEnterpriseNo();
        CompanyAssociatedOrg companyAssociatedOrg = companyAssociatedOrgDAO.selectBySourceTypeAndCode(enterpriseNo, CompanyAssociatedOrgTypeEnum.TAXPAYER_ORG.getValue(), model.getAssociatedOrgNo());
        if (companyAssociatedOrg != null && StringUtils.isNotEmpty(companyAssociatedOrg.getCompanyNo())) {
            companyAssociatedOrg.setCompanyNo(null);
            companyAssociatedOrg.setModifyTime(DateUtil.getCurrentDate());
            companyAssociatedOrgDAO.updateAllById(enterpriseNo, companyAssociatedOrg);
        }
    }

    /**
     * 检查根据统一社会信用代码/企业名称对应的企业档案是否是同一个
     *
     * @param companyV2ListByUnifiedSocialCode
     * @param companyV2ListByName
     * @param model
     * @return
     */
    private boolean checkIsSameCompany(List<CompanyV2> companyV2ListByUnifiedSocialCode, List<CompanyV2> companyV2ListByName, CreateTaxPayerOrgModel model) {
        if (companyV2ListByUnifiedSocialCode.size() == 1 && companyV2ListByName.size() == 1) {
            if (companyV2ListByUnifiedSocialCode.get(0).getCompanyNo().equals(companyV2ListByName.get(0).getCompanyNo())) {
                return true;
            }
        }
        log.error("异常数据，companyV2ListByUnifiedSocialCode:{}", JSON.toJSONString(companyV2ListByUnifiedSocialCode));
        log.error("异常数据，companyV2ListByName:{}", JSON.toJSONString(companyV2ListByName));
        return false;
    }
}
