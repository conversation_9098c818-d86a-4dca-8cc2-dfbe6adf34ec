package com.yyigou.dsrp.cdc.service.listener.model.v2;

import com.yyigou.dsrp.cdc.api.v2.company.vo.CompanyBasicVO;
import lombok.Data;

import java.io.Serializable;

/**
 * 企业档案变更消息监听实体类
 *
 * @author:  Moore
 * @date: 2024/7/10 21:34
 * @version: 1.0.0
 */
@Data
public class TenantCompanySaveModel implements Serializable {
    private static final long serialVersionUID = 3083247682745396838L;

    /**
     * 更新模式
     * false-新增 ；true-编辑
     */
    private Boolean update;

    private CompanyUpdateModel companyUpdateModel;

    private CompanyBasicVO companyBasicVO;

}
