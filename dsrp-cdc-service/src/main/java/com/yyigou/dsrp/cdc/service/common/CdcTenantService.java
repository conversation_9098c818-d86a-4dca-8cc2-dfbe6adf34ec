package com.yyigou.dsrp.cdc.service.common;

import com.yyigou.ddc.common.error.ErrorCode;
import com.yyigou.ddc.common.exception.BusinessException;
import com.yyigou.ddc.common.service.ServiceBaseAbstract;
import com.yyigou.ddc.common.service.SessionUser;
import com.yyigou.ddc.common.util.ValidatorUtils;
import com.yyigou.ddc.services.ddc.uim.enums.TenantTypeEnum;
import com.yyigou.ddc.services.ddc.uim.vo.OrganizationTreeVo;
import com.yyigou.dsrp.cdc.manager.integration.org.UimTenantService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;
/**
 * 租户登录信息service
 *
 * @author:  Moore
 * @date: 2024/7/17 13:25
 * @version: 1.0.0
 */
@Service
@Slf4j
public class CdcTenantService {

    @Autowired
    private UimTenantService uimTenantService;

    /**
     * 获取当前登录用户信息
     *
     * @param
     * @return: {@link SessionUser}
     */
    public static SessionUser getSessionUser() {
        SessionUser session = ServiceBaseAbstract.currentRequest().getSession();
        if (null == session) {
            throw new BusinessException(ErrorCode.object_null_code, "service-dsrp-cdc：获取用户登录信息失败");
        }
        return session;
    }

    /**
     * 获取当前登录租户编号
     *
     * @param
     * @return: {@link String}
     */
    public String getEnterpriseNo() {
        SessionUser sessionUser = getSessionUser();
        return sessionUser.getEnterpriseNo();
    }

    /**
     * 根据租户编号获取当前登录租户类型
     *
     * @param enterpriseNo
     * @return: {@link TenantTypeEnum}
     */
    public TenantTypeEnum getTenantType(String enterpriseNo) {
        return uimTenantService.getTenantType(enterpriseNo);
    }

    /**
     * 判断当前登录租户是不是集团租户
     *
     * @param
     * @return: {@link Boolean}
     */
    public Boolean getGroupTenantFlag(String enterpriseNo) {
        if (StringUtils.isBlank(enterpriseNo)) {
            enterpriseNo = getEnterpriseNo();
        }
        ValidatorUtils.checkEmptyThrowEx(enterpriseNo, "查询是否集团租户时，租户编号不能为空");
        return TenantTypeEnum.GROUP.equals(getTenantType(enterpriseNo));
    }

    /**
     * 判断当前登录租户是不是子租户
     *
     * @param
     * @return: {@link Boolean}
     */
    public Boolean getSubTenantFlag(String enterpriseNo) {
        if (StringUtils.isBlank(enterpriseNo)) {
            enterpriseNo = getEnterpriseNo();
        }
        ValidatorUtils.checkEmptyThrowEx(enterpriseNo, "查询是否子租户时，租户编号不能为空");
        return TenantTypeEnum.SUB_ENTERPRISE.equals(getTenantType(enterpriseNo));
    }

    /**
     * 获取集团租户编号
     *
     * @return
     */
    public String getGroupEnterpriseNo() {
        SessionUser sessionUser = getSessionUser();
        String enterpriseNo = sessionUser.getEnterpriseNo();
        Map<String, Object> map = sessionUser.getExtInfo();
        String groupTenantNo = MapUtils.getString(map, "groupTenantNo");//集团主租户
        // 若当前登录是子租户
        if (StringUtils.isNotBlank(groupTenantNo) && !enterpriseNo.equals(groupTenantNo)) {
            enterpriseNo = groupTenantNo;
        }
        // 不是子租户，就是集团租户或者独立租户，就拿自己的enterpriseNo
        return enterpriseNo;
    }

    /**
     * 获取组织树信息
     *
     * @param enterpriseNo
     * @param groupEnterpriseNo
     * @return: {@link OrganizationTreeVo}
     */
    public OrganizationTreeVo getOrganizationTreeVo(String enterpriseNo, String groupEnterpriseNo){
        return uimTenantService.getOrganizationTreeVo(enterpriseNo, groupEnterpriseNo);
    }

}
