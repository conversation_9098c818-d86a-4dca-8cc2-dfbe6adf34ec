package com.yyigou.dsrp.cdc.service.listener;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yyigou.ddc.services.ddc.uim.vo.OrganizationVo;
import com.yyigou.dsrp.cdc.client.v2.customer.request.CustomerAssignDocExeRequest;
import com.yyigou.dsrp.cdc.client.v2.customer.request.CustomerAssignMdmExeRequest;
import com.yyigou.dsrp.cdc.client.v2.customer.response.CustomerAssignExeResponse;
import com.yyigou.dsrp.cdc.manager.integration.exeresult.MasterDataExecuteService;
import com.yyigou.dsrp.cdc.manager.integration.org.UimTenantService;
import com.yyigou.dsrp.cdc.service.common.MasterDataSyncService;
import com.yyigou.dsrp.cdc.service.listener.model.CustomerAssignModel;
import com.yyigou.dsrp.cdc.service.v2.MdmGray;
import com.yyigou.dsrp.cdc.service.v2.customer.CustomerV2Service;
import com.yyigou.dsrp.gcs.client.executeRecord.request.UpdateExecuteRecordRequest;
import com.yyigou.dsrp.gcs.common.enums.executeRecord.ExecuteRecordExecuteResultEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jms.listener.SessionAwareMessageListener;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.jms.JMSException;
import javax.jms.Message;
import javax.jms.Session;
import javax.jms.TextMessage;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
@Slf4j
@Service
public class CustomerAssignListener implements SessionAwareMessageListener {
    @Resource
    private MasterDataSyncService masterDataSyncService;

    @Resource
    private MasterDataExecuteService masterDataExecuteService;

    @Resource
    private MdmGray mdmGray;

    @Resource
    private CustomerV2Service customerV2Service;

    @Resource
    private UimTenantService uimTenantService;

    /**
     * 企业
     *
     * @param message
     * @param session
     * @throws JMSException
     */
    @Override
    public void onMessage(Message message, Session session) throws JMSException {
        ExecuteRecordExecuteResultEnum executeResultEnum = ExecuteRecordExecuteResultEnum.EXECUTE_SUCCESS;
        String errorMsg = "";
        CustomerAssignModel model = null;
        //  获取消息
        try {
            TextMessage textMessage = (TextMessage) message;
            log.warn("客户分派消息处理,参数: {}" , textMessage.getText());
            //1、企业证照更换 修改影响的产品档案
            model = JSONObject.parseObject(textMessage.getText(), CustomerAssignModel.class);
            String genCustomerNo = masterDataSyncService.handCustomer(model);
            // 执行多组织兼容逻辑的分派
            try {
                log.warn("客户分派消息处理,开始执行多组织兼容逻辑的分派,groupEnterpriseNo={},genCustomerNo={}", model.getGroupEnterpriseNo(), genCustomerNo);
                if (mdmGray.isEnterpriseGray(model.getGroupEnterpriseNo()) && StringUtils.isNotEmpty(genCustomerNo)) {
                    log.warn("客户分派消息处理,灰度租户开始执行多组织兼容逻辑的分派");
                    List<OrganizationVo> orgListByGroupEnterpriseNo = uimTenantService.findOrgListByGroupEnterpriseNo(model.getGroupEnterpriseNo());
                    Map<String, OrganizationVo> organizationMap = orgListByGroupEnterpriseNo.stream().collect(Collectors.toMap(OrganizationVo::getBindingEnterpriseNo, Function.identity()));
                    OrganizationVo manageOrganizationTreeVo = organizationMap.get(model.getGroupEnterpriseNo());
                    OrganizationVo useOrganizationTreeVo = organizationMap.get(model.getSubEnterpriseNo());
                    log.warn("客户分派消息处理,打印管理组织和使用组织,manageOrganizationTreeVo={},useOrganizationTreeVo={}", manageOrganizationTreeVo, useOrganizationTreeVo);
                    if (null != manageOrganizationTreeVo && null != useOrganizationTreeVo) {
                        CustomerAssignMdmExeRequest customerAssignExeRequest = new CustomerAssignMdmExeRequest();
                        customerAssignExeRequest.setEnterpriseNo(model.getGroupEnterpriseNo());
                        customerAssignExeRequest.setManageOrgNo(manageOrganizationTreeVo.getOrgNo());
                        List<CustomerAssignDocExeRequest> docList = new ArrayList<>();
                        CustomerAssignDocExeRequest customerAssignDocExeRequest = new CustomerAssignDocExeRequest();
                        customerAssignDocExeRequest.setCustomerCode(model.getCustomerCode());
                        customerAssignDocExeRequest.setUseOrgNoList(Collections.singletonList(useOrganizationTreeVo.getOrgNo()));
                        docList.add(customerAssignDocExeRequest);
                        customerAssignExeRequest.setDocList(docList);

                        Map<String, String> orgNo2CustomerNoMap = new HashMap<>();
                        orgNo2CustomerNoMap.put(useOrganizationTreeVo.getOrgNo(), genCustomerNo);
                        customerAssignExeRequest.setOrgNo2CustomerNoMap(orgNo2CustomerNoMap);

                        log.warn("客户分派消息处理,开始执行多组织兼容逻辑的分派,请求参数: {}", JSON.toJSONString(customerAssignExeRequest));
                        List<CustomerAssignExeResponse> customerAssignExeResponses = customerV2Service.assignMdmCustomer(customerAssignExeRequest);
                        log.warn("客户分派消息处理,执行多组织兼容逻辑的分派结果: {}", JSON.toJSONString(customerAssignExeResponses));
                    }
                }
                log.warn("客户分派消息处理,结束执行多组织兼容逻辑的分派");
            } catch (Exception e) {
                log.error("mdm接口兼容多组织客户分派失败", e);
            }
        } catch (Exception e) {
            log.error("客户分派消息处理,处理失败", e);
            executeResultEnum = ExecuteRecordExecuteResultEnum.EXECUTE_FAIL;
            errorMsg = e.getMessage();
            if (e instanceof NullPointerException){
                errorMsg = "空指针";
            }
        } finally {
            log.warn("客户分派消息处理,处理结果: {} {}" , JSON.toJSONString(model), executeResultEnum.getName());
            try{
                if (StringUtils.isNotEmpty(errorMsg)) {
                    if (errorMsg.length() > 500) {
                        errorMsg = errorMsg.substring(0, 500);
                    }
                }
                if (ExecuteRecordExecuteResultEnum.EXECUTE_FAIL.equals(executeResultEnum) &&
                        model != null && model.getRecordId() != null && (model.getGenerateExecutionRecord() == null || !model.getGenerateExecutionRecord())){
                    UpdateExecuteRecordRequest updateExecuteRecordRequest = new UpdateExecuteRecordRequest();
                    updateExecuteRecordRequest.setId(model.getRecordId());
                    updateExecuteRecordRequest.setErrorMsg(errorMsg);
                    updateExecuteRecordRequest.setExecuteResult(executeResultEnum);
                    updateExecuteRecordRequest.setModifyNo("-1");
                    updateExecuteRecordRequest.setModifyName("system");
                    masterDataExecuteService.updateExecuteRecord(updateExecuteRecordRequest);
                }
            }catch (Exception e){
                log.error(e.getMessage(), e);
            }
            // 主动发送消息回执, 告知broker消息已经被消费了
            try {
                message.acknowledge();
            } catch (JMSException e) {
                log.error(e.getMessage(), e);
            }
        }

    }
}
