package com.yyigou.dsrp.cdc.service.company.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yyigou.ddc.common.util.ValidatorUtils;
import com.yyigou.dsrp.cdc.dao.company.CompanyQuoteRecordDAO;
import com.yyigou.dsrp.cdc.dao.company.entity.CompanyQuoteRecord;
import com.yyigou.dsrp.cdc.service.company.CompanyQuoteRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class CompanyQuoteRecordServiceImpl extends ServiceImpl<CompanyQuoteRecordDAO, CompanyQuoteRecord> implements CompanyQuoteRecordService {
    @Autowired
    private CompanyQuoteRecordDAO companyQuoteRecordDAO;

    /**
     * 查询剧团企业档案分派子租户信息
     *
     * @param groupEnterpriseNo
     * @param manageEnterpriseNo
     * @param manageCompanyNoList
     * @return: {@link List< CompanyQuoteRecord>}
     */
    @Override
    public List<CompanyQuoteRecord> findCompanyAssignOrgList(String groupEnterpriseNo, String manageEnterpriseNo, List<String> manageCompanyNoList) {
        ValidatorUtils.checkEmptyThrowEx(groupEnterpriseNo, "集团租户编号不能为空");
        ValidatorUtils.checkEmptyThrowEx(manageCompanyNoList, "企业编号不能为空");
        return companyQuoteRecordDAO.findCompanyAssignOrgList(groupEnterpriseNo, manageEnterpriseNo, manageCompanyNoList);
    }

    @Override
    public List<CompanyQuoteRecord> findCompanyByUseEnterprise(String groupEnterpriseNo, String useEnterpriseNo, List<String> companyNoList) {
        ValidatorUtils.checkEmptyThrowEx(groupEnterpriseNo, "集团租户编号不能为空");
        ValidatorUtils.checkEmptyThrowEx(useEnterpriseNo, "企业编号不能为空");
        ValidatorUtils.checkEmptyThrowEx(companyNoList, "企业编号不能为空");
        return companyQuoteRecordDAO.findCompanyByUseEnterprise(groupEnterpriseNo, useEnterpriseNo, companyNoList);
    }
}
