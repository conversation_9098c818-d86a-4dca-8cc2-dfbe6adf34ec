package com.yyigou.dsrp.cdc.service.common;

import com.yyigou.ddc.services.mq.dto.JmsHeadersDto;
import com.yyigou.ddc.services.mq.manager.MessageQueueManager;
import com.yyigou.ddc.services.mq.manager.exception.MessageQueueException;
import com.yyigou.ddc.services.mq.manager.util.MessageQueueUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.jms.Destination;

@Service
@Slf4j
public class ActiveMQService {

    @Autowired
    private MessageQueueManager messageQueueManager;

    @Autowired
    private MessageQueueUtil messageQueueUtil;

    /**
     * 发送消息
     *
     * @param destination   消息目标
     * @param messageBody   消息体
     * @return:
     */
    public void produceMessage(Destination destination,String messageBody) {
        // 构建消息头
        JmsHeadersDto jmsHeadersDto = new JmsHeadersDto();
        // 联动数据库事务, 可能分布式事务场景会失效 (假提交可能导致这里消息事务提交)
        jmsHeadersDto.setSessionTransacted(true);
        // 设置broker帮我持久化保存消息, 防止消费者不在线导致消息丢失的场景
        jmsHeadersDto.setPersistent(true);
        // 发送消息
        try {
            messageQueueManager.produceMessage(destination, messageBody, null, jmsHeadersDto);
        } catch (MessageQueueException e) {
            log.error("发送MQ消息失败，消息内容:{}", messageBody, e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 延迟发送消息
     *
     * @param destination       消息目标
     * @param messageBody       消息体
     * @param millisecond       延迟毫秒
     */
    public void produceMessageDelayed(Destination destination, String messageBody, long millisecond) {
        messageQueueUtil.sendDelayedNotify(messageBody, destination, millisecond);
    }
}
