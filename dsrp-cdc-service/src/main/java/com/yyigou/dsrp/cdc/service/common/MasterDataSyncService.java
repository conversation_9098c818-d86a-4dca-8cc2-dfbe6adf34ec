package com.yyigou.dsrp.cdc.service.common;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.yyigou.ddc.common.exception.BusinessException;
import com.yyigou.ddc.common.util.DateUtil;
import com.yyigou.ddc.common.util.ValidatorUtils;
import com.yyigou.ddc.services.ddc.uim.vo.OrganizationTreeVo;
import com.yyigou.ddc.services.mq.dto.JmsHeadersDto;
import com.yyigou.ddc.services.mq.manager.MessageQueueManager;
import com.yyigou.ddc.services.mq.manager.exception.MessageQueueException;
import com.yyigou.dsrp.cdc.api.customer.dto.SaveCustomerDTO;
import com.yyigou.dsrp.cdc.api.supply.dto.SaveSupplierDTO;
import com.yyigou.dsrp.cdc.common.enums.CompanyTypeEnum;
import com.yyigou.dsrp.cdc.common.util.CommonUtil;
import com.yyigou.dsrp.cdc.common.util.OperationModel;
import com.yyigou.dsrp.cdc.dao.company.CompanyDAO;
import com.yyigou.dsrp.cdc.dao.company.CompanyQuoteRecordDAO;
import com.yyigou.dsrp.cdc.dao.company.entity.Company;
import com.yyigou.dsrp.cdc.dao.company.entity.CompanyQuoteRecord;
import com.yyigou.dsrp.cdc.dao.customer.CustomerDAO;
import com.yyigou.dsrp.cdc.dao.customer.entity.Customer;
import com.yyigou.dsrp.cdc.dao.customer.entity.CustomerCategory;
import com.yyigou.dsrp.cdc.dao.gradedcontrol.SupplierCustomerUseInfoDAO;
import com.yyigou.dsrp.cdc.dao.gradedcontrol.entity.SupplierCustomerUseInfo;
import com.yyigou.dsrp.cdc.dao.supplier.SupplierDAO;
import com.yyigou.dsrp.cdc.dao.supplier.entity.Supplier;
import com.yyigou.dsrp.cdc.dao.supplier.entity.SupplierCategory;
import com.yyigou.dsrp.cdc.manager.integration.bizcodeGenerator.BizCodeGeneratorService;
import com.yyigou.dsrp.cdc.manager.integration.dccert.CatalogInfoService;
import com.yyigou.dsrp.cdc.manager.integration.exeresult.MasterDataExecuteService;
import com.yyigou.dsrp.cdc.manager.integration.numberCenter.NumberCenterService;
import com.yyigou.dsrp.cdc.manager.integration.org.UimTenantService;
import com.yyigou.dsrp.cdc.model.constant.CdcMqConstant;
import com.yyigou.dsrp.cdc.model.constant.NumberCenterConstant;
import com.yyigou.dsrp.cdc.model.constant.ServiceConstant;
import com.yyigou.dsrp.cdc.model.constant.SystemConstant;
import com.yyigou.dsrp.cdc.model.enums.CompanyRoleEnum;
import com.yyigou.dsrp.cdc.service.common.model.ApplyPersonModel;
import com.yyigou.dsrp.cdc.service.common.model.CompanyBasicStandardsInfoModel;
import com.yyigou.dsrp.cdc.service.company.CompanyQuoteRecordService;
import com.yyigou.dsrp.cdc.service.company.CompanyService;
import com.yyigou.dsrp.cdc.service.customer.CustomerCategoryService;
import com.yyigou.dsrp.cdc.service.customer.CustomerExtendService;
import com.yyigou.dsrp.cdc.service.customer.CustomerService;
import com.yyigou.dsrp.cdc.service.listener.model.CustomerAssignModel;
import com.yyigou.dsrp.cdc.service.listener.model.SupplierAssignModel;
import com.yyigou.dsrp.cdc.service.supplier.SupplierCategoryService;
import com.yyigou.dsrp.cdc.service.supplier.SupplierService;
import com.yyigou.dsrp.cdc.service.supplier.SupplyExtendService;
import com.yyigou.dsrp.gcs.client.executeRecord.request.BatchSaveExecuteRecordItemRequest;
import com.yyigou.dsrp.gcs.client.executeRecord.request.BatchSaveExecuteRecordRequest;
import com.yyigou.dsrp.gcs.client.executeRecord.request.SaveExecuteRecordRequest;
import com.yyigou.dsrp.gcs.client.executeRecord.request.UpdateExecuteRecordRequest;
import com.yyigou.dsrp.gcs.client.executeRecord.response.ExecuteRecordResponse;
import com.yyigou.dsrp.gcs.common.enums.DeletedEnum;
import com.yyigou.dsrp.gcs.common.enums.StatusEnum;
import com.yyigou.dsrp.gcs.common.enums.executeRecord.ExecuteRecordDataTypeEnum;
import com.yyigou.dsrp.gcs.common.enums.executeRecord.ExecuteRecordExcuteTypeEnum;
import com.yyigou.dsrp.gcs.common.enums.executeRecord.ExecuteRecordExecuteResultEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.activemq.command.ActiveMQQueue;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class MasterDataSyncService {

    @Autowired
    private CatalogInfoService catalogInfoService;
    @Autowired
    private CustomerService customerService;
    @Autowired
    private SupplierService supplierService;

    @Autowired
    private SupplyExtendService supplyExtendService;

    @Autowired
    private CustomerExtendService customerExtendService;

    @Autowired
    private SupplierCategoryService supplierCategoryService;
    @Autowired
    private CompanyService companyService;
    @Autowired
    private CustomerCategoryService customerCategoryService;
    @Autowired
    private CdcLogService cdcLogService;
    @Autowired
    private UimTenantService uimTenantService;
    @Autowired
    private MasterDataExecuteService masterDataExecuteService;
    @Autowired
    private CompanyQuoteRecordService companyQuoteRecordService;
    @Autowired
    private NumberCenterService numberCenterService;
    @Autowired
    private CompanyQuoteRecordDAO companyQuoteRecordDAO;


    private final CustomerDAO customerDAO;
    private final CompanyDAO companyDAO;
    private final SupplierCustomerUseInfoDAO supplierCustomerUseInfoDAO;
    private final BizCodeGeneratorService bizCodeGeneratorService;
    private final SupplierDAO supplierDAO;
    private final CdcLogService ccLogService;
    private final MessageQueueManager messageQueueManager;


    /**
     * 同步集团客户档案信息给子租户
     *
     * @param groupEnterpriseNo
     * @param customerCode
     * @param operationModel
     * @return:
     */
    public void saveSyncCustomerArchive(String groupEnterpriseNo, String customerCode, OperationModel operationModel) {
        // 查询集团客户信息
        Customer groupCustomer = customerService.getCustomerByCustomerCode(groupEnterpriseNo, customerCode);
        if (Objects.isNull(groupCustomer)) {
            log.warn("没有查到集团库客户信息，集团租户编号={},客户编码={}", groupEnterpriseNo, customerCode);
            return;
        }
        List<String> assignOrgEnterpriseNoList = customerService.findCustomerAssignOrgEnterpriseNo(groupEnterpriseNo, customerCode);
        if (CollectionUtils.isEmpty(assignOrgEnterpriseNoList)) {
            log.warn("没有查到集团库客户关联的子租户信息，集团租户编号={},客户编码={}", groupEnterpriseNo, customerCode);
            return;
        }
        operationModel.setEmployerNo("集团");
        operationModel.setUserName("集团");
        for (String enterpriseNo : assignOrgEnterpriseNoList) {
            try {
                Customer subCustomer = customerService.getCustomerByCustomerCode(enterpriseNo, customerCode);
                if (Objects.isNull(subCustomer)) {
                    continue;
                }
                // 变更更新子租户客户信息
                this.updateSyncCustomer(subCustomer, groupCustomer, operationModel);

                // 保存修改日志
                cdcLogService.saveCustomerLog(subCustomer.getCustomerNo(), operationModel, "集团变更更新");

                // 插入执行记录
                OrganizationTreeVo gradedInfoResponse = uimTenantService.getOrganizationTreeVo(enterpriseNo, groupEnterpriseNo);
                BatchSaveExecuteRecordItemRequest batchSaveExecuteRecordItemRest = new BatchSaveExecuteRecordItemRequest();
                batchSaveExecuteRecordItemRest.setObjNo(customerCode);
                batchSaveExecuteRecordItemRest.setObjCode(customerCode);
                batchSaveExecuteRecordItemRest.setObjName(groupCustomer.getCustomerName());
                List<BatchSaveExecuteRecordItemRequest> items = Lists.newArrayList();
                items.add(batchSaveExecuteRecordItemRest);
                List<ExecuteRecordResponse> executeRecordResponseList = this.saveExecuteRecord(gradedInfoResponse.getOrgNo(), gradedInfoResponse.getOrgName(),
                        enterpriseNo, groupEnterpriseNo, operationModel.getEmployerNo(), operationModel.getUserName(),
                        ExecuteRecordDataTypeEnum.CUSTOMER, items, ExecuteRecordExcuteTypeEnum.CHANGE_UPDATE, ExecuteRecordExecuteResultEnum.EXECUTE_SUCCESS);
                if (!CollectionUtils.isEmpty(executeRecordResponseList)){
                    executeRecordResponseList.forEach(executeRecordResponse -> {
                        // 更新执行记录状态
                        customerExtendService.startCustomerAssignTask(executeRecordResponse);
                    });
                }
            } catch (Exception e) {
                log.error("集团租户更新分派客户信息失败，集团租户编号={},子租户编号={},客户编码={}", groupEnterpriseNo, enterpriseNo, customerCode, e);
            }
        }
    }

    /**
     * 集团更新子租户客户信息（基本信息+标准信息）
     *
     * @param subCustomer
     * @param groupCustomer
     * @param operationModel
     * @return:
     */
    public void updateSyncCustomer(Customer subCustomer, Customer groupCustomer, OperationModel operationModel) {
        updateSyncCustomerNoExecute(subCustomer, groupCustomer, operationModel);
        // 更新子租户客户信息
        customerService.updateCustomer(subCustomer);
    }

    public void updateSyncCustomerNoExecute(Customer subCustomer, Customer groupCustomer, OperationModel operationModel) {
        if (subCustomer == null || groupCustomer == null){
            log.error("updateSyncCustomerNoExecute 参数异常 {} {}" , JSON.toJSONString(subCustomer) , JSON.toJSONString(groupCustomer));
            return;
        }
        // TODO 临时添加 2024-07-26 为了触发分派引用调用
        catalogInfoService.companyDispatch(groupCustomer.getEnterpriseNo(), subCustomer.getEnterpriseNo(), groupCustomer.getCompanyNo(), subCustomer.getCompanyNo());

        // 客户编码（基本信息）
        subCustomer.setCustomerCode(groupCustomer.getCustomerCode());
        // 客户名称（基本信息）
        subCustomer.setCustomerName(groupCustomer.getCustomerName());

        // 同步客户分类（基本信息）
        CustomerCategory customerCategory = saveSyncCustomerCategory(subCustomer.getEnterpriseNo(), groupCustomer.getEnterpriseNo(), groupCustomer.getCustomerCategoryNo(), operationModel);
        if (null != customerCategory) {
            subCustomer.setCustomerCategoryNo(customerCategory.getNo());
        }

        // 统一社会信用代码
        subCustomer.setUnifiedSocialCode(groupCustomer.getUnifiedSocialCode());
        // 备注
        subCustomer.setRemark(groupCustomer.getRemark());

        // 备注（基本信息）
        subCustomer.setRemark(groupCustomer.getRemark());
        // 业务状态（基本信息）：0-潜在，1-正式
        subCustomer.setBusinessFlag(groupCustomer.getBusinessFlag());
        // 管控状态（基本信息）：1-启用，2-停用
//        subCustomer.setControlStatus(groupCustomer.getControlStatus());
        // 来源渠道（基本信息）
        subCustomer.setSourceChannel(groupCustomer.getSourceChannel());

        // 客户英文名称（标准信息）
        subCustomer.setCustomerNameEn(groupCustomer.getCustomerNameEn());
        // 助记码（标准信息）
        subCustomer.setMnemonicCode(groupCustomer.getMnemonicCode());
        // scs协同（标准信息）
        // 归属公司（标准信息）
        subCustomer.setOwnerCompany(groupCustomer.getOwnerCompany());
        // 需gsp首营（标准信息）
        subCustomer.setIsGspControl(groupCustomer.getIsGspControl());
//        subCustomer.setGspAuditStatus(groupCompany.getGspAuditStatus());
        // 简称（标准信息）
        subCustomer.setShortName(groupCustomer.getShortName());
        // 企业类型（标准信息）
        subCustomer.setEnterpriseType(groupCustomer.getEnterpriseType());
        // 机构类型（标准信息）
        subCustomer.setInstitutionalType(groupCustomer.getInstitutionalType());
        // 医院类型（标准信息）
        subCustomer.setHospitalType(groupCustomer.getHospitalType());
        // 医院等级（标准信息）
        subCustomer.setHospitalClass(groupCustomer.getHospitalClass());
        // 修改人
        subCustomer.setOperateNo(operationModel.getEmployerNo());
        subCustomer.setOperateTime(operationModel.getUserName());
        subCustomer.setOperateTime(DateUtil.getCurrentDate());
        subCustomer.setOpTimestamp(DateUtil.getCurDate());
        //是否散户
        subCustomer.setRetailInvestors(groupCustomer.getRetailInvestors());
        //交易类型
        subCustomer.setTransactionType(groupCustomer.getTransactionType());
        // 版本号
        subCustomer.setOpRevsion(1);
        subCustomer.setVersion(subCustomer.getVersion() + 1);
        // 初始化同步状态
        subCustomer.setIsSyncWms(0);
        subCustomer.setIsSyncErp(0);
        subCustomer.setIsSyncScs(0);
        subCustomer.setYsSyncFlag("0");
        subCustomer.setTcSyncFlag(0);
    }


    /**
     * 同步集团客户分类给子租户
     *
     * @param subTenantEnterpriseNo
     * @param groupEnterpriseNo
     * @param no
     * @param operationModel
     * @return: {@link CustomerCategory}
     */
    public CustomerCategory saveSyncCustomerCategory(String subTenantEnterpriseNo, String groupEnterpriseNo, String no, OperationModel operationModel) {
        CustomerCategory customerCategoryNew = null;
        CustomerCategory groupCustomerCategory = customerCategoryService.getByNo(groupEnterpriseNo, no);
        ValidatorUtils.checkEmptyThrowEx(groupCustomerCategory, "客户分类在集团库中不存在");
        CustomerCategory subCustomerCategory = customerCategoryService.getByGroupNo(subTenantEnterpriseNo, no);
        if (null == subCustomerCategory) {
            // 新建客户分类给子租户
            LinkedList<CustomerCategory> groupCustomerCategoryList = customerCategoryService.getParentNoLinkedList(groupEnterpriseNo, no);
            String parentNo = SystemConstant.COMMON_TOP_CATEGORY_NO;
            for (CustomerCategory current : groupCustomerCategoryList) {
                CustomerCategory currentCustomerCategory = customerCategoryService.findByGroupNoAndParentNo(subTenantEnterpriseNo, current.getNo(), parentNo);
                if (null == currentCustomerCategory) {
                    // 新增
                    CustomerCategory customerCategoryCopy = new CustomerCategory();
                    BeanUtils.copyProperties(current, customerCategoryCopy);
                    customerCategoryCopy.setEnterpriseNo(subTenantEnterpriseNo);
                    customerCategoryCopy.setNo(numberCenterService.createTreeNumber(NumberCenterConstant.CUSTOMER_NO_CATEGORY_KEY, ServiceConstant.TOP_PARENT_NO));
                    customerCategoryCopy.setParentNo(parentNo);
                    // 插入集团分类编号，作为映射使用
                    customerCategoryCopy.setGroupNo(current.getNo());
                    customerCategoryCopy.setYsSyncFlag("0");
                    CommonUtil.fillCreatInfo(operationModel, customerCategoryCopy);
                    customerCategoryService.save(customerCategoryCopy);
                    customerCategoryNew = customerCategoryCopy;
                    Optional<CustomerCategory> nextOptional = groupCustomerCategoryList.stream().filter(t -> current.getNo().equals(t.getParentNo())).findAny();
                    if (!nextOptional.isPresent()) {
                        break;
                    }
                    parentNo = customerCategoryCopy.getNo();
                } else {
                    parentNo = currentCustomerCategory.getNo();
                    customerCategoryNew = currentCustomerCategory;
                }
            }
        } else {
            // 更新子租户客户分类信息
            subCustomerCategory.setCategoryCode(groupCustomerCategory.getCategoryCode());
            subCustomerCategory.setCategoryName(groupCustomerCategory.getCategoryName());
            subCustomerCategory.setMnemonicCode(groupCustomerCategory.getMnemonicCode());
            customerCategoryService.updateById(subCustomerCategory);
            customerCategoryNew = subCustomerCategory;
        }
        return customerCategoryNew;
    }


    /**
     * 保存主数据分派执行记录
     *
     * @param applyOrgNo        子租户业务单元编码
     * @param applyOrgName      子租户业务单元名称
     * @param applyEnterpriseNo 子租户租户编号
     * @param groupEnterpriseNo 集团组织租户编号
     * @param createNo          创建人编码
     * @param createName        创建人名称
     * @param dataType          执行档案
     * @param items             执行数据记录
     * @param executeType       执行操作类型
     * @param executeResult     执行结果
     * @return:
     */
    private List<ExecuteRecordResponse> saveExecuteRecord(String applyOrgNo, String applyOrgName,
                                                         String applyEnterpriseNo, String groupEnterpriseNo,
                                                         String createNo, String createName,
                                                         ExecuteRecordDataTypeEnum dataType, List<BatchSaveExecuteRecordItemRequest> items,
                                                         ExecuteRecordExcuteTypeEnum executeType, ExecuteRecordExecuteResultEnum executeResult) {
        if (CollectionUtils.isEmpty(items)) {
            return Collections.emptyList();
        }
        BatchSaveExecuteRecordRequest batchSaveExecuteRecordRest = new BatchSaveExecuteRecordRequest();
        batchSaveExecuteRecordRest.setDataType(dataType);
        batchSaveExecuteRecordRest.setItems(items);
        batchSaveExecuteRecordRest.setExecuteType(executeType);
        batchSaveExecuteRecordRest.setExecuteResult(executeResult);
        batchSaveExecuteRecordRest.setApplyOrgNo(applyOrgNo);
        batchSaveExecuteRecordRest.setApplyOrgName(applyOrgName);
        batchSaveExecuteRecordRest.setApplySource("DSRP");
        batchSaveExecuteRecordRest.setApplyEnterpriseNo(applyEnterpriseNo);
        batchSaveExecuteRecordRest.setGroupEnterpriseNo(groupEnterpriseNo);
        batchSaveExecuteRecordRest.setManageEnterpriseNo(groupEnterpriseNo);
        batchSaveExecuteRecordRest.setExecuteTime(DateUtil.getCurrentDate());
        batchSaveExecuteRecordRest.setCreateNo(createNo);
        batchSaveExecuteRecordRest.setCreateName(createName);

        List<ExecuteRecordResponse> executeRecordResponseList = masterDataExecuteService.saveExecuteRecordNoExecute(batchSaveExecuteRecordRest);
        return executeRecordResponseList;
    }

    /**
     * 同步集团供应商档案信息给子租户
     *
     * @param groupEnterpriseNo
     * @param supplierCode
     * @param operationModel
     * @return:
     */
    public void saveSyncSupplierArchive(String groupEnterpriseNo, String supplierCode, OperationModel operationModel) {

        // 查询集团供应商信息
        Supplier groupSupplier = supplierService.getSupplierBySupplierCode(groupEnterpriseNo, supplierCode);
        if (Objects.isNull(groupSupplier)) {
            log.warn("没有查到集团库客户信息，集团租户编号={},客户编码={}", groupEnterpriseNo, supplierCode);
            return;
        }
        List<String> assignOrgEnterpriseNoList = supplierService.findSupplierAssignOrgEnterpriseNo(groupEnterpriseNo, supplierCode);
        if (CollectionUtils.isEmpty(assignOrgEnterpriseNoList)) {
            return;
        }
        operationModel.setEmployerNo("集团");
        operationModel.setUserName("集团");
        for (String enterpriseNo : assignOrgEnterpriseNoList) {
            try {
                Supplier subSupplier = supplierService.getSupplierBySupplierCode(enterpriseNo, supplierCode);
                if (Objects.isNull(subSupplier)) {
                    continue;
                }
                // 变更更新子租户供应商信息
                this.updateSyncSupplier(subSupplier, groupSupplier, operationModel);

                // 保存修改日志
                cdcLogService.saveSupplierLog(subSupplier.getSupplierNo(), operationModel, "集团变更更新");

                // 插入执行记录
                OrganizationTreeVo gradedInfoResponse = uimTenantService.getOrganizationTreeVo(enterpriseNo, groupEnterpriseNo);
                BatchSaveExecuteRecordItemRequest batchSaveExecuteRecordItemRest = new BatchSaveExecuteRecordItemRequest();
                batchSaveExecuteRecordItemRest.setObjNo(supplierCode);
                batchSaveExecuteRecordItemRest.setObjCode(supplierCode);
                batchSaveExecuteRecordItemRest.setObjName(groupSupplier.getSupplierName());
                List<BatchSaveExecuteRecordItemRequest> items = Lists.newArrayList();
                items.add(batchSaveExecuteRecordItemRest);
                List<ExecuteRecordResponse> executeRecordResponseList = this.saveExecuteRecord(gradedInfoResponse.getOrgNo(), gradedInfoResponse.getOrgName(),
                        enterpriseNo, groupEnterpriseNo, operationModel.getEmployerNo(), operationModel.getUserName(),
                        ExecuteRecordDataTypeEnum.SUPPLIER, items, ExecuteRecordExcuteTypeEnum.CHANGE_UPDATE, ExecuteRecordExecuteResultEnum.EXECUTE_SUCCESS);

                if (CollectionUtils.isNotEmpty(executeRecordResponseList)){
                    executeRecordResponseList.forEach(executeRecordResponse -> {
                        supplyExtendService.startSupplierAssignTask(executeRecordResponse);
                    });
                }
            } catch (Exception e) {
                log.error("集团租户更新分派客户信息失败，集团租户编号={},子租户编号={},客户编码={}", groupEnterpriseNo, enterpriseNo, supplierCode, e);
            }
        }
    }

    /**
     * 同步集团供应商信息给子租户
     *
     * @param subSupplier
     * @param groupSupplier
     * @param operationModel
     * @return:
     */
    private void updateSyncSupplier(Supplier subSupplier, Supplier groupSupplier, OperationModel operationModel) {
        updateSyncSupplierNoExecute(subSupplier, groupSupplier, operationModel);
        // 更新子租户供应商信息
        supplierService.updateSupplier(subSupplier);
    }


    /**
     * 同步集团供应商信息给子租户
     *
     * @param subSupplier
     * @param groupSupplier
     * @param operationModel
     * @return:
     */
    private void updateSyncSupplierNoExecute(Supplier subSupplier, Supplier groupSupplier, OperationModel operationModel) {

        // TODO 临时添加 2024-07-26 为了触发分派引用调用
        catalogInfoService.companyDispatch(groupSupplier.getEnterpriseNo(), subSupplier.getEnterpriseNo(), groupSupplier.getCompanyNo(), subSupplier.getCompanyNo());

        subSupplier.setSupplierCode(groupSupplier.getSupplierCode());
        // 供应商名称（基本信息）
        subSupplier.setSupplierName(groupSupplier.getSupplierName());
        // 供应商分类（基本信息）
        SupplierCategory supplierCategory = saveSyncSupplierCategory(subSupplier.getEnterpriseNo(), groupSupplier.getEnterpriseNo(), groupSupplier.getSupplierCategoryNo(), operationModel);
        if (null != supplierCategory) {
            subSupplier.setSupplierCategoryNo(supplierCategory.getNo());
        }
        // 统一社会信用代码
        subSupplier.setUnifiedSocialCode(groupSupplier.getUnifiedSocialCode());
        // 备注
        subSupplier.setRemark(groupSupplier.getRemark());
        // 备注（基本信息）
        subSupplier.setRemark(groupSupplier.getRemark());
        // 业务状态（基本信息）：0-潜在，1-正式
        subSupplier.setBusinessFlag(groupSupplier.getBusinessFlag());
        // 管控状态（基本信息）：1-启用，2-停用
//        subSupplier.setControlStatus(supplierBasicStandardsInfo.getControlStatus());
        // 来源渠道（基本信息）
        subSupplier.setSourceChannel(groupSupplier.getSourceChannel());

        // 客户英文名称（标准信息）
        subSupplier.setSupplierNameEn(groupSupplier.getSupplierNameEn());

        // 助记码（标准信息）
        subSupplier.setMnemonicCode(groupSupplier.getMnemonicCode());
        // scs协同（标准信息）
        // 归属公司（标准信息）
        subSupplier.setOwnerCompany(groupSupplier.getOwnerCompany());
        // 需gsp首营（标准信息）
        subSupplier.setIsGspControl(groupSupplier.getIsGspControl());
//        subSupplier.setGspAuditStatus(groupCompany.getGspAuditStatus());
        // 企业类型（标准信息）
        subSupplier.setEnterpriseType(groupSupplier.getEnterpriseType());
        // 付款协议（mdm是账期）
        subSupplier.setPaymentAgreementCode(groupSupplier.getPaymentAgreementCode());
        subSupplier.setPaymentAgreementName(groupSupplier.getPaymentAgreementName());
        // 修改人
        subSupplier.setOperateNo(operationModel.getEmployerNo());
        subSupplier.setOperateName(operationModel.getUserName());
        subSupplier.setOperateTime(DateUtil.getCurrentDate());
        subSupplier.setOpTimestamp(DateUtil.getCurDate());

        // 版本号
        subSupplier.setOpRevsion(1);
        subSupplier.setVersion(subSupplier.getVersion() + 1);
        // 初始化同步状态
        subSupplier.setIsSyncWms(0);
        subSupplier.setIsSyncErp(0);
        subSupplier.setIsSyncScs(0);
        subSupplier.setYsSyncFlag("0");
        subSupplier.setTcSyncFlag(0);
    }

    /**
     * 同步供应商分类
     *
     * @param applyEnterpriseNo
     * @param groupEnterpriseNo
     * @param no
     * @param operationModel
     * @return:
     */
    public SupplierCategory saveSyncSupplierCategory(String applyEnterpriseNo, String groupEnterpriseNo, String no, OperationModel operationModel) {
        SupplierCategory supplierCategoryNew = null;
        SupplierCategory groupSupplierCategory = supplierCategoryService.getByNo(groupEnterpriseNo, no);
        ValidatorUtils.checkEmptyThrowEx(groupSupplierCategory, "供应商分类在集团库中不存在");
        SupplierCategory subSupplierCategory = supplierCategoryService.getByGroupNo(applyEnterpriseNo, no);
        if (null == subSupplierCategory) {
            // 新建供应商分类给子租户
            LinkedList<SupplierCategory> groupSupplierCategoryList = supplierCategoryService.getParentNoLinkedList(groupEnterpriseNo, no);
            String parentNo = SystemConstant.COMMON_TOP_CATEGORY_NO;
            for (SupplierCategory current : groupSupplierCategoryList) {
                SupplierCategory currentSupplierCategory = supplierCategoryService.findByGroupNoAndParentNo(applyEnterpriseNo, current.getNo(), parentNo);
                if (null == currentSupplierCategory) {
                    // 新增
                    SupplierCategory supplierCategoryCopy = new SupplierCategory();
                    BeanUtils.copyProperties(current, supplierCategoryCopy);
                    supplierCategoryCopy.setEnterpriseNo(applyEnterpriseNo);
                    supplierCategoryCopy.setNo(numberCenterService.createTreeNumber(NumberCenterConstant.SUPPLY_NO_CATEGORY_KEY, ServiceConstant.TOP_PARENT_NO));
                    supplierCategoryCopy.setParentNo(parentNo);
                    // 插入集团分类编号，作为映射使用
                    supplierCategoryCopy.setGroupNo(current.getNo());
                    supplierCategoryCopy.setYsSyncFlag("0");
                    CommonUtil.fillCreatInfo(operationModel, supplierCategoryCopy);
                    supplierCategoryService.save(supplierCategoryCopy);
                    supplierCategoryNew = supplierCategoryCopy;
                    Optional<SupplierCategory> nextOptional = groupSupplierCategoryList.stream().filter(t -> current.getNo().equals(t.getParentNo())).findAny();
                    if (!nextOptional.isPresent()) {
                        break;
                    }
                    parentNo = supplierCategoryCopy.getNo();
                } else {
                    parentNo = currentSupplierCategory.getNo();
                    supplierCategoryNew = currentSupplierCategory;
                }
            }
        } else {
            // 更新子租户客户分类信息
            subSupplierCategory.setCategoryCode(groupSupplierCategory.getCategoryCode());
            subSupplierCategory.setCategoryName(groupSupplierCategory.getCategoryName());
            subSupplierCategory.setMnemonicCode(groupSupplierCategory.getMnemonicCode());
            supplierCategoryService.updateById(subSupplierCategory);
            supplierCategoryNew = subSupplierCategory;
        }
        return supplierCategoryNew;
    }

    /**
     * 同步集团企业档案信息给子租户
     *
     * @param groupEnterpriseNo
     * @param oldCompany
     * @param newCompany
     * @param operationModel
     * @return:
     */
    public void updateSyncSubTenantCompanyInfo(String groupEnterpriseNo, Company oldCompany, Company newCompany, OperationModel operationModel) {
        // 查询集团租户企业被子租户使用情况
        List<CompanyQuoteRecord> quoteRecordVOList = companyQuoteRecordService.findCompanyAssignOrgList(groupEnterpriseNo, groupEnterpriseNo, Lists.newArrayList(newCompany.getCompanyNo()));

        if (CollectionUtils.isEmpty(quoteRecordVOList)) {
            return;
        }
        Map<String, CompanyQuoteRecord> useCompanyMap = quoteRecordVOList.stream().collect(Collectors.toMap(CompanyQuoteRecord::getUseCompanyNo, Function.identity(), (k1, k2) -> k1));

        Set<String> useEnterpriseNoSet = new HashSet<>();
        for (String useCompanyNo : useCompanyMap.keySet()) {
            CompanyQuoteRecord quoteRecordVO = useCompanyMap.get(useCompanyNo);
            String useEnterpriseNo = quoteRecordVO.getUseEnterpriseNo();
            if (!useEnterpriseNoSet.add(useEnterpriseNo)) {
                continue;
            }
            CompanyBasicStandardsInfoModel oldCompanyInfo = new CompanyBasicStandardsInfoModel();
            BeanUtils.copyProperties(oldCompany, oldCompanyInfo);
            CompanyBasicStandardsInfoModel currCompanyInfo = new CompanyBasicStandardsInfoModel();
            BeanUtils.copyProperties(newCompany, currCompanyInfo);
            if (oldCompanyInfo.hashCode() != currCompanyInfo.hashCode()) {
                List<Company> subCompanyList = companyService.findByEnterpriseNoAndCompanyNoList(useEnterpriseNo, Lists.newArrayList(useCompanyNo));
                // 同步企业档案给子租户
                this.updateSyncCompany(subCompanyList.get(0), newCompany, operationModel);
            }
        }
    }

    /**
     * 集团租户企业档案基本信息+标准信息同步给子租户
     *
     * @param company        子租户企业档案
     * @param groupCompany   集团租户企业档案
     * @param operationModel 操作人相关信息
     * @return:
     */
    public void updateSyncCompany(Company company, Company groupCompany, OperationModel operationModel) {
        // 企业名称
        company.setCompanyName(groupCompany.getCompanyName());
        // 统一社会信用代码
        company.setUnifiedSocialCode(groupCompany.getUnifiedSocialCode());
        // 法定代表人
        company.setLegalPerson(groupCompany.getLegalPerson());
        //国家地区
        company.setCountry(groupCompany.getCountry());
        //经济类型
        company.setEconomicType(groupCompany.getEconomicType());
        // 是否医疗机构
        company.setIsMedicalInstitution(groupCompany.getIsMedicalInstitution());
        // 机构类型
        company.setInstitutionalType(groupCompany.getInstitutionalType());
        // 医院类型
        company.setHospitalType(groupCompany.getHospitalType());
        // 医院等级
        company.setHospitalClass(groupCompany.getHospitalClass());
        // 联系人
//        company.setLinkMan(groupCompany.getLinkMan());
//        // 联系电话
//        company.setLinkPhone(groupCompany.getLinkPhone());
//        // 联系人职务
//        company.setLinkManPosition(groupCompany.getLinkManPosition());
        // 企业类型
        company.setEnterpriseType(groupCompany.getEnterpriseType());
        // 企业注册地域：1-境内，2-境外
        company.setFactoryType(groupCompany.getFactoryType());
        // 合作关系
        company.setPartnership(groupCompany.getPartnership());
        // 经营状态
        company.setBusinessStatus(groupCompany.getBusinessStatus());
        // 成立日期
        company.setEstablishmentDate(groupCompany.getEstablishmentDate());
        // 营业期限开始日期
        company.setBusinessStartTime(groupCompany.getBusinessStartTime());
        // 营业期限结束日期
        company.setBusinessEndTime(groupCompany.getBusinessEndTime());
        // 是否长期
        company.setBusinessLongTerm(groupCompany.getBusinessLongTerm());
        // 注册资本
        company.setRegistedCapital(groupCompany.getRegistedCapital());
        // 实缴资本
        company.setPaidCapital(groupCompany.getPaidCapital());
        // 类型
        company.setCompanyBusinessType(groupCompany.getCompanyBusinessType());
        // 所属行业
        company.setIndustry(groupCompany.getIndustry());
        // 工商注册号
        company.setBusinessRegistNo(groupCompany.getBusinessRegistNo());
        // 组织机构代码
        company.setOrganizationNo(groupCompany.getOrganizationNo());
        // 纳税人识别号
        company.setTaxpayerNo(groupCompany.getTaxpayerNo());
        // 纳税人资质
        company.setTaxpayerQualification(groupCompany.getTaxpayerQualification());
        // 核准日期
        company.setApprovalDate(groupCompany.getApprovalDate());
        // 登记机关
        company.setRegistrationAuthority(groupCompany.getRegistrationAuthority());
        // 所在地区区域编码
        company.setRegionCode(groupCompany.getRegionCode());
        // 所在地区区域名称
        company.setRegionName(groupCompany.getRegionName());
        // 详细地址
        company.setAddress(groupCompany.getAddress());
        // 曾用名
        company.setLastName(groupCompany.getLastName());
        // 参保人数
        company.setInsuredNumber(groupCompany.getInsuredNumber());
        // WEB网站
        company.setWebSite(groupCompany.getWebSite());
        // 企业邮箱
        company.setEmail(groupCompany.getEmail());
        // 传真
        company.setFax(groupCompany.getFax());
        // 经营范围
        company.setManageScope(groupCompany.getManageScope());
        // 纳税类别
        company.setTaxCategory(groupCompany.getTaxCategory());
        // 纳税类别名称
        company.setTaxCategoryName(groupCompany.getTaxCategoryName());
        // 是否上市
        company.setIsListed(groupCompany.getIsListed());
        // 是否关联企业
        company.setIsAssociatedEnterprise(groupCompany.getIsAssociatedEnterprise());
        // 关联组织信息
        company.setAssociatedOrgCode(groupCompany.getAssociatedOrgCode());
        company.setAssociatedOrgName(groupCompany.getAssociatedOrgName());
        company.setAssociatedOrgNo(groupCompany.getAssociatedOrgNo());
        // 企业状态:0-草稿，1-正式（现在只有正式，正式说明企业在供应商,客户,厂商至少存在一个正式业务）
        company.setStatus(groupCompany.getStatus());


        // 修改人
        company.setOperateNo(operationModel.getEmployerNo());
        company.setOperateTime(operationModel.getUserName());
        company.setOperateTime(DateUtil.getCurrentDate());
        company.setOpTimestamp(DateUtil.getCurDate());

        // 更新企业信息
        companyService.updateCompany(operationModel, company);
    }


    /**
     * * 处理直接引用逻辑：
     * * 1. 若存在使用信息，则更新数据
     * * 2. 若不存在，新增数据，新增使用关系，新增执行记录
     *
     * @param params
     */
    @Transactional
    public String handCustomer(CustomerAssignModel params) {
        String customerCode = params.getCustomerCode();
        OperationModel operationModel = params.getOperationModel();
        String groupEnterpriseNo = params.getGroupEnterpriseNo();
        String subEnterpriseNo = params.getSubEnterpriseNo();
        ExecuteRecordExcuteTypeEnum executeRecordExecuteTypeEnum = params.getExecuteRecordExecuteTypeEnum();
        final Customer groupCustomer = customerDAO.getCustomerByCustomerCode(groupEnterpriseNo, customerCode);
        if (groupCustomer == null){
            throw new BusinessException("客户不存在" + groupEnterpriseNo + ":" + customerCode);
        }
        //获取集团库的企业信息
        List<CompanyQuoteRecord> quoteRecordVOList = companyQuoteRecordService.findCompanyByUseEnterprise(groupEnterpriseNo, subEnterpriseNo, Collections.singletonList(groupCustomer.getCompanyNo()));
        final Company groupCompany = companyDAO.findByEnterpriseNoAndCompanyNo(groupEnterpriseNo, groupCustomer.getCompanyNo());
        Company subCompany = null;
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(quoteRecordVOList)) {
            final List<String> subComapnyNoList = quoteRecordVOList.stream().map(CompanyQuoteRecord::getUseCompanyNo).collect(Collectors.toList());
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(subComapnyNoList)) {
                final List<Company> subComapnyList = companyDAO.findByEnterpriseNoAndCompanyNoList(subEnterpriseNo, subComapnyNoList);
                subCompany = subComapnyList.get(0);
            }
        }
        String companyNo = null;
        //企业信息分派
        if (subCompany == null) {
            //企业信息分派
            subCompany = new Company();
            CompanyBasicStandardsInfoModel subCompanyInfo = new CompanyBasicStandardsInfoModel();
            BeanUtils.copyProperties(groupCompany, subCompanyInfo);
            BeanUtils.copyProperties(subCompanyInfo, subCompany);
            subCompany.setEnterpriseNo(subEnterpriseNo);
            subCompany.setCompanyNo(numberCenterService.createNumber(NumberCenterConstant.COMPANY_NO_GENERATE_KEY));
            CommonUtil.fillCreatInfo(operationModel, subCompany);
            companyDAO.insert(subCompany);
            companyNo = subCompany.getCompanyNo();

            CompanyQuoteRecord companyQuoteRecord = new CompanyQuoteRecord();
            companyQuoteRecord.setGroupEnterpriseNo(groupEnterpriseNo);
            companyQuoteRecord.setManageEnterpriseNo(groupEnterpriseNo);
            companyQuoteRecord.setManageCompanyNo(groupCompany.getCompanyNo());
            companyQuoteRecord.setUseEnterpriseNo(subEnterpriseNo);
            companyQuoteRecord.setUseCompanyNo(subCompany.getCompanyNo());
            companyQuoteRecord.setStatus(StatusEnum.EFFECTIVE.getValue());
            companyQuoteRecord.setDeleted(DeletedEnum.UN_DELETE.getValue());
            CommonUtil.fillCreatInfo(operationModel, companyQuoteRecord);
            companyQuoteRecordDAO.insert(companyQuoteRecord);
            catalogInfoService.companyDispatch(groupEnterpriseNo, subEnterpriseNo, groupCompany.getCompanyNo(), subCompany.getCompanyNo());
        } else {
            //企业信息存在引用记录 自动更新
            companyNo = subCompany.getCompanyNo();
            updateSyncCompany(subCompany, groupCompany, operationModel);
            catalogInfoService.companyDispatch(groupEnterpriseNo, subEnterpriseNo, groupCompany.getCompanyNo(), subCompany.getCompanyNo());
        }
        // 查询这个子租户是否存在使用记录
        List<SupplierCustomerUseInfo> useInfoList = supplierCustomerUseInfoDAO.findGroupSupplierCustomerBySubTenant(subEnterpriseNo, Lists.newArrayList(customerCode), CompanyTypeEnum.CUSTOMER.getValue());

        String genCustomerNo = null;
        //无使用记录走分派逻辑
        if (org.apache.commons.collections.CollectionUtils.isEmpty(useInfoList)) {
            Customer subCustomer = new Customer();
            subCustomer.setEnterpriseNo(subEnterpriseNo);
            genCustomerNo = numberCenterService.createNumber(NumberCenterConstant.CUSTOMER_NO_KEY);
            subCustomer.setCustomerNo(genCustomerNo);
            subCustomer.setVersion(0);
            subCustomer.setCompanyNo(companyNo);
            updateSyncCustomerNoExecute(subCustomer, groupCustomer, operationModel);
            CommonUtil.fillCreatInfo(operationModel, subCustomer);
            customerDAO.insert(subCustomer);
            //保存分派信息
            SupplierCustomerUseInfo supplierCustomerUseInfo = new SupplierCustomerUseInfo();
            supplierCustomerUseInfo.setCompanyType(CompanyRoleEnum.CUSTOMER.getValue());
            supplierCustomerUseInfo.setCompanyName(subCustomer.getCustomerName());
            supplierCustomerUseInfo.setCompanyCode(subCustomer.getCustomerCode());
            supplierCustomerUseInfo.setCompanyNo(subCustomer.getCompanyNo());
            supplierCustomerUseInfo.setGroupEnterpriseNo(groupEnterpriseNo);
            supplierCustomerUseInfo.setManageEnterpriseNo(groupEnterpriseNo);
            supplierCustomerUseInfo.setUseEnterpriseNo(subEnterpriseNo);
            supplierCustomerUseInfo.setStatus(StatusEnum.EFFECTIVE.getValue());
            supplierCustomerUseInfo.setDeleted(DeletedEnum.UN_DELETE.getValue());
            CommonUtil.fillCreatInfo(operationModel, supplierCustomerUseInfo);
            supplierCustomerUseInfoDAO.insert(supplierCustomerUseInfo);
            ccLogService.saveCustomerLog(subCustomer.getCustomerNo(), operationModel, "引用集团库创建");
        } else {
            //更新逻辑
            final Customer subCustomer = customerDAO.getCustomerByCustomerCode(subEnterpriseNo, customerCode);
            updateSyncCustomer(subCustomer, groupCustomer, operationModel);
            ccLogService.saveCustomerLog(subCustomer.getCustomerNo(), operationModel, "引用集团库更新");

            genCustomerNo = subCustomer.getCustomerNo();
        }
        if (params.getRecordId() != null) {
            UpdateExecuteRecordRequest updateExecuteRecordRequest = new UpdateExecuteRecordRequest();
            updateExecuteRecordRequest.setId(params.getRecordId());
            updateExecuteRecordRequest.setExecuteResult(ExecuteRecordExecuteResultEnum.EXECUTE_SUCCESS);
            updateExecuteRecordRequest.setErrorMsg("");
            updateExecuteRecordRequest.setModifyNo(operationModel.getEmployerNo());
            updateExecuteRecordRequest.setModifyName(operationModel.getUserName());
            masterDataExecuteService.updateExecuteRecord(updateExecuteRecordRequest);
        }
        //需要生成执行记录的
        if (Boolean.TRUE.equals(params.getGenerateExecutionRecord())) {
            final OrganizationTreeVo organizationTreeVo = uimTenantService.getOrganizationTreeVo(params.getSubEnterpriseNo(), params.getGroupEnterpriseNo());
            SaveExecuteRecordRequest saveExecuteRecordRequest = new SaveExecuteRecordRequest();
            saveExecuteRecordRequest.setDataType(ExecuteRecordDataTypeEnum.CUSTOMER);
            saveExecuteRecordRequest.setExecuteType(executeRecordExecuteTypeEnum);
            saveExecuteRecordRequest.setApplySourceId(params.getApplySourceId());
            saveExecuteRecordRequest.setApplyOrgNo(organizationTreeVo.getOrgNo());
            saveExecuteRecordRequest.setApplyOrgName(organizationTreeVo.getOrgName());
            saveExecuteRecordRequest.setApplySource(params.getApplySource());
            saveExecuteRecordRequest.setApplyEnterpriseNo(subEnterpriseNo);
            saveExecuteRecordRequest.setGroupEnterpriseNo(groupEnterpriseNo);
            saveExecuteRecordRequest.setManageEnterpriseNo(groupEnterpriseNo);
            saveExecuteRecordRequest.setObjNo(groupCustomer.getCustomerNo());
            saveExecuteRecordRequest.setObjCode(groupCustomer.getCustomerCode());
            saveExecuteRecordRequest.setObjName(groupCustomer.getCustomerName());
            saveExecuteRecordRequest.setExecuteTime(DateUtil.getCurrentDate(DateUtil.DATE_TIME));
            saveExecuteRecordRequest.setExecuteResult(ExecuteRecordExecuteResultEnum.EXECUTE_SUCCESS);
            saveExecuteRecordRequest.setErrorMsg("");
            saveExecuteRecordRequest.setCreateNo(operationModel.getEmployerNo());
            saveExecuteRecordRequest.setCreateName(operationModel.getUserName());
            masterDataExecuteService.saveExecuteRecordNoExecute(saveExecuteRecordRequest);
        }

        return genCustomerNo;
    }


    /**
     * 处理直接引用逻辑：
     * 1. 若存在使用信息，则更新数据
     * 2. 若不存在，新增数据，新增使用关系，新增执行记录
     *
     * @param params 操作信息
     */
    @Transactional
    public String handSupplier(SupplierAssignModel params) {
        String supplierCode = params.getSupplierCode();
        OperationModel operationModel = params.getOperationModel();
        String groupEnterpriseNo = params.getGroupEnterpriseNo();
        String subEnterpriseNo = params.getSubEnterpriseNo();
        ExecuteRecordExcuteTypeEnum executeRecordExecuteTypeEnum = params.getExecuteRecordExecuteTypeEnum();
        Integer recordId = params.getRecordId();
        final Supplier groupSupplier = supplierDAO.getSupplierBySupplierCode(groupEnterpriseNo, supplierCode);
        if (groupSupplier == null){
            throw new BusinessException("供应商不存在" + groupEnterpriseNo + ":" + supplierCode);
        }
        //获取集团库的企业信息
        List<CompanyQuoteRecord> quoteRecordVOList = companyQuoteRecordService.findCompanyByUseEnterprise(groupEnterpriseNo, subEnterpriseNo, Collections.singletonList(groupSupplier.getCompanyNo()));
        final Company groupCompany = companyDAO.findByEnterpriseNoAndCompanyNo(groupEnterpriseNo, groupSupplier.getCompanyNo());
        Company subCompany = null;
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(quoteRecordVOList)) {
            final List<String> subComapnyNoList = quoteRecordVOList.stream().map(CompanyQuoteRecord::getUseCompanyNo).collect(Collectors.toList());
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(subComapnyNoList)) {
                final List<Company> subComapnyList = companyDAO.findByEnterpriseNoAndCompanyNoList(subEnterpriseNo, subComapnyNoList);
                subCompany = subComapnyList.get(0);
            }
        }
        String companyNo = null;
        //企业信息分派
        if (subCompany == null) {
            //企业信息分派
            subCompany = new Company();
            CompanyBasicStandardsInfoModel subCompanyInfo = new CompanyBasicStandardsInfoModel();
            BeanUtils.copyProperties(groupCompany, subCompanyInfo);
            BeanUtils.copyProperties(subCompanyInfo, subCompany);
            subCompany.setEnterpriseNo(subEnterpriseNo);
            subCompany.setCompanyNo(numberCenterService.createNumber(NumberCenterConstant.COMPANY_NO_GENERATE_KEY));
            CommonUtil.fillCreatInfo(operationModel, subCompany);
            companyDAO.insert(subCompany);
            companyNo = subCompany.getCompanyNo();

            CompanyQuoteRecord companyQuoteRecord = new CompanyQuoteRecord();
            companyQuoteRecord.setGroupEnterpriseNo(groupEnterpriseNo);
            companyQuoteRecord.setManageEnterpriseNo(groupEnterpriseNo);
            companyQuoteRecord.setManageCompanyNo(groupCompany.getCompanyNo());
            companyQuoteRecord.setUseEnterpriseNo(subEnterpriseNo);
            companyQuoteRecord.setUseCompanyNo(subCompany.getCompanyNo());
            companyQuoteRecord.setStatus(StatusEnum.EFFECTIVE.getValue());
            companyQuoteRecord.setDeleted(DeletedEnum.UN_DELETE.getValue());
            CommonUtil.fillCreatInfo(operationModel, companyQuoteRecord);
            companyQuoteRecordDAO.insert(companyQuoteRecord);
            catalogInfoService.companyDispatch(groupEnterpriseNo, subEnterpriseNo, groupCompany.getCompanyNo(), subCompany.getCompanyNo());
        } else {
            //企业信息存在引用记录 自动更新
            companyNo = subCompany.getCompanyNo();
            updateSyncCompany(subCompany, groupCompany, operationModel);
            catalogInfoService.companyDispatch(groupEnterpriseNo, subEnterpriseNo, groupCompany.getCompanyNo(), subCompany.getCompanyNo());
        }
        // 查询这个子租户是否存在使用记录
        List<SupplierCustomerUseInfo> useInfoList = supplierCustomerUseInfoDAO.findGroupSupplierCustomerBySubTenant(subEnterpriseNo, Lists.newArrayList(supplierCode), CompanyTypeEnum.SUPPLIER.getValue());

        String genSupplierNo = null;
        //无使用记录走分派逻辑
        if (org.apache.commons.collections.CollectionUtils.isEmpty(useInfoList)) {
            Supplier subSupplier = new Supplier();
            subSupplier.setEnterpriseNo(subEnterpriseNo);
            genSupplierNo = numberCenterService.createNumber(NumberCenterConstant.SUPPLY_NO_KEY);
            subSupplier.setSupplierNo(genSupplierNo);
            subSupplier.setVersion(0);
            subSupplier.setCompanyNo(companyNo);
            updateSyncSupplierNoExecute(subSupplier, groupSupplier, operationModel);
            CommonUtil.fillCreatInfo(operationModel, subSupplier);
            supplierDAO.insert(subSupplier);
            //保存分派信息
            SupplierCustomerUseInfo supplierCustomerUseInfo = new SupplierCustomerUseInfo();
            supplierCustomerUseInfo.setCompanyType(CompanyRoleEnum.SUPPLIER.getValue());
            supplierCustomerUseInfo.setCompanyName(subSupplier.getSupplierName());
            supplierCustomerUseInfo.setCompanyCode(subSupplier.getSupplierCode());
            supplierCustomerUseInfo.setCompanyNo(subSupplier.getCompanyNo());
            supplierCustomerUseInfo.setGroupEnterpriseNo(groupEnterpriseNo);
            supplierCustomerUseInfo.setManageEnterpriseNo(groupEnterpriseNo);
            supplierCustomerUseInfo.setUseEnterpriseNo(subEnterpriseNo);
            supplierCustomerUseInfo.setStatus(StatusEnum.EFFECTIVE.getValue());
            supplierCustomerUseInfo.setDeleted(DeletedEnum.UN_DELETE.getValue());
            CommonUtil.fillCreatInfo(operationModel, supplierCustomerUseInfo);
            supplierCustomerUseInfoDAO.insert(supplierCustomerUseInfo);
        } else {
            //更新逻辑
            final Supplier subSupplier = supplierDAO.getSupplierBySupplierCode(subEnterpriseNo, supplierCode);
            updateSyncSupplier(subSupplier, groupSupplier, operationModel);

            genSupplierNo = subSupplier.getSupplierNo();
        }
        if (recordId != null) {
            UpdateExecuteRecordRequest updateExecuteRecordRequest = new UpdateExecuteRecordRequest();
            updateExecuteRecordRequest.setId(recordId);
            updateExecuteRecordRequest.setExecuteResult(ExecuteRecordExecuteResultEnum.EXECUTE_SUCCESS);
            updateExecuteRecordRequest.setErrorMsg("");
            updateExecuteRecordRequest.setModifyNo(operationModel.getEmployerNo());
            updateExecuteRecordRequest.setModifyName(operationModel.getUserName());
            masterDataExecuteService.updateExecuteRecord(updateExecuteRecordRequest);
        }
        //需要生成执行记录的
        if (Boolean.TRUE.equals(params.getGenerateExecutionRecord())) {
            final OrganizationTreeVo organizationTreeVo = uimTenantService.getOrganizationTreeVo(params.getSubEnterpriseNo(), params.getGroupEnterpriseNo());
            SaveExecuteRecordRequest saveExecuteRecordRequest = new SaveExecuteRecordRequest();
            saveExecuteRecordRequest.setDataType(ExecuteRecordDataTypeEnum.SUPPLIER);
            saveExecuteRecordRequest.setExecuteType(executeRecordExecuteTypeEnum);
            saveExecuteRecordRequest.setApplySourceId(params.getApplySourceId());
            saveExecuteRecordRequest.setApplyOrgNo(organizationTreeVo.getOrgNo());
            saveExecuteRecordRequest.setApplyOrgName(organizationTreeVo.getOrgName());
            saveExecuteRecordRequest.setApplySource(params.getApplySource());
            saveExecuteRecordRequest.setApplyEnterpriseNo(subEnterpriseNo);
            saveExecuteRecordRequest.setGroupEnterpriseNo(groupEnterpriseNo);
            saveExecuteRecordRequest.setManageEnterpriseNo(groupEnterpriseNo);
            saveExecuteRecordRequest.setObjNo(groupSupplier.getSupplierNo());
            saveExecuteRecordRequest.setObjCode(groupSupplier.getSupplierCode());
            saveExecuteRecordRequest.setObjName(groupSupplier.getSupplierName());
            saveExecuteRecordRequest.setExecuteTime(DateUtil.getCurrentDate(DateUtil.DATE_TIME));
            saveExecuteRecordRequest.setExecuteResult(ExecuteRecordExecuteResultEnum.EXECUTE_SUCCESS);
            saveExecuteRecordRequest.setErrorMsg("");
            saveExecuteRecordRequest.setCreateNo(operationModel.getEmployerNo());
            saveExecuteRecordRequest.setCreateName(operationModel.getUserName());
            masterDataExecuteService.saveExecuteRecordNoExecute(saveExecuteRecordRequest);
        }
        //在事务提交之后 给协同端发送消息 王豪处理
        Company finalSubCompany = subCompany;
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                JSONObject paramsJson = new JSONObject();
                paramsJson.put("customerNo", subEnterpriseNo);
                paramsJson.put("supplierCode", groupSupplier.getSupplierCode());
                paramsJson.put("supplierName", groupSupplier.getSupplierName());
                paramsJson.put("unifiedSocialCode", finalSubCompany.getUnifiedSocialCode());
                try {
                    messageQueueManager.produceMessage(new ActiveMQQueue(CdcMqConstant.DDC_SUPPLIER_RELATION_CREATE_QUEUE), JSON.toJSONString(paramsJson), null, new JmsHeadersDto());
                } catch (MessageQueueException e) {
                    log.error("发送协同关系变更信息失败,当前参数" + JSON.toJSONString(paramsJson), e);
                }
            }
        });

        return genSupplierNo;
    }


    /**
     * 处理新增申请
     *
     * @param applyJson
     * @return
     */
    @Transactional
    public String handleCustomerApply(String applyJson) {
        final ApplyPersonModel applyPersonModel = JSON.parseObject(applyJson, ApplyPersonModel.class);
        final SaveCustomerDTO applyParams = JSON.parseObject(applyJson, SaveCustomerDTO.class);
        //在等待审批的过程中可能集团库已经手动新增/导入/或者同意了其他公司的新增申请 这边查询下客户是否存在
        final Customer customer = customerDAO.getCustomerByCustomerName(applyPersonModel.getManageEnterpriseNo(), applyParams.getCustomerName());
        OperationModel operationModel = new OperationModel();
        operationModel.setEnterpriseNo(applyPersonModel.getManageEnterpriseNo());
        //如果客户已经存在则直接进入分派逻辑即可
        if (customer == null) {
            if (new Integer(1).equals(applyParams.getIsAssociatedEnterprise()) && StringUtils.isNotBlank(applyParams.getAssociatedOrgCode())) {
                applyParams.setCustomerCode(applyParams.getAssociatedOrgCode());
            } else {
                AtomicInteger generateCount = new AtomicInteger(0);
                while (true) {
                    //防止各种配置配置错误等,导致的问题最多尝试100次防止死循环
                    ValidatorUtils.checkTrueThrowEx(generateCount.incrementAndGet() >= 100, "客户编码生成失败");
                    String tempCustomerCode = bizCodeGeneratorService.getBizCode(applyPersonModel.getManageEnterpriseNo(), SystemConstant.KH_CODE_GENERATE);
                    //生成一个可用的CustomerCode 因为存在导入的情况所以可能得多生成几次
                    final Customer tempCustomer = customerDAO.getCustomerByCustomerCode(applyPersonModel.getManageEnterpriseNo(), tempCustomerCode);
                    //这个编码是可以使用的
                    if (tempCustomer == null) {
                        applyParams.setCustomerCode(tempCustomerCode);
                        break;
                    }
                }
            }
            SaveCustomerDTO params = new SaveCustomerDTO();
            params.setCustomerCode(applyParams.getCustomerCode());
            params.setCustomerName(applyParams.getCustomerName());
            params.setCustomerNameEn(applyParams.getCustomerNameEn());
            params.setMnemonicCode(applyParams.getMnemonicCode());
            params.setCustomerType(applyParams.getCustomerType());
            params.setCustomerCategoryNo(applyParams.getCustomerCategoryNo());
            params.setCompanyName(applyParams.getCompanyName());
            params.setUnifiedSocialCode(applyParams.getUnifiedSocialCode());
            params.setFactoryType(applyParams.getFactoryType());
            params.setCountry(applyParams.getCountry());
            params.setTaxCategory(applyParams.getTaxCategory());
            params.setEconomicType(applyParams.getEconomicType());
            params.setOwnerCompany(applyParams.getOwnerCompany());
            params.setRetailInvestors(applyParams.getRetailInvestors());
            params.setIsMedicalInstitution(applyParams.getIsMedicalInstitution());
            params.setInstitutionalType(applyParams.getInstitutionalType());
            params.setHospitalType(applyParams.getHospitalType());
            params.setHospitalClass(applyParams.getHospitalClass());
            params.setRemark(applyParams.getRemark());
            //给集团创建客户档案
            customerService.saveCustomer(operationModel, params, "子租户新增客户档案");
        } else {
            applyParams.setCustomerCode(customer.getCustomerCode());
        }
        CustomerAssignModel customerAssignModel = new CustomerAssignModel();
        customerAssignModel.setCustomerCode(applyParams.getCustomerCode());
        customerAssignModel.setOperationModel(operationModel);
        customerAssignModel.setGroupEnterpriseNo(applyPersonModel.getManageEnterpriseNo());
        customerAssignModel.setSubEnterpriseNo(applyPersonModel.getApplyEnterpriseNo());
        customerAssignModel.setExecuteRecordExecuteTypeEnum(ExecuteRecordExcuteTypeEnum.ADD_APPLY);
        customerAssignModel.setGenerateExecutionRecord(Boolean.TRUE);
        customerAssignModel.setApplySourceId(applyParams.getId());
        customerAssignModel.setApplySource("DSRP");
        //把集团的物料档案同步到子租户
        handCustomer(customerAssignModel);
        //给子租户的客户档案保存特有信息
        final Customer subCustomer = customerDAO.getCustomerByCustomerCode(applyPersonModel.getApplyEnterpriseNo(), applyParams.getCustomerCode());
        subCustomer.setControlId(applyParams.getControlId());
        subCustomer.setControlTypeName(applyParams.getControlTypeName());
        subCustomer.setCooperationMode(applyParams.getCooperationMode());
        subCustomer.setBusinessType(applyParams.getBusinessType());
        subCustomer.setPriceCategoryCode(applyParams.getPriceCategoryCode());
        subCustomer.setCurrency(applyParams.getCurrency());
        subCustomer.setSettlementModes(applyParams.getSettlementModes());
        subCustomer.setSettlementModesName(applyParams.getSettlementModesName());
        subCustomer.setPaymentCondition(applyParams.getPaymentCondition());
        subCustomer.setPaymentAgreement(applyParams.getPaymentAgreement());
        subCustomer.setCreditDates(applyParams.getCreditDates());
        subCustomer.setCreditAmount(applyParams.getCreditAmount());
        subCustomer.setCoopStartTime(applyParams.getCoopStartTime());
        subCustomer.setCoopEndTime(applyParams.getCoopEndTime());
        customerDAO.updateById(subCustomer);
        return applyParams.getCustomerCode();
    }

    /**
     * 处理新增申请
     *
     * @param applyJson
     * @return
     */
    @Transactional
    public String handleSupplierApply(String applyJson) {
        final ApplyPersonModel applyPersonModel = JSON.parseObject(applyJson, ApplyPersonModel.class);
        final SaveSupplierDTO applyParams = JSON.parseObject(applyJson, SaveSupplierDTO.class);
        //在等待审批的过程中可能集团库已经手动新增/导入/或者同意了其他公司的新增申请 这边查询下客户是否存在
        final Supplier supplier = supplierDAO.getSupplierBySupplierName(applyPersonModel.getManageEnterpriseNo(), applyParams.getSupplierName());
        OperationModel operationModel = new OperationModel();
        operationModel.setEnterpriseNo(applyPersonModel.getManageEnterpriseNo());
        //如果客户已经存在则直接进入分派逻辑即可
        if (supplier == null) {
            if (new Integer(1).equals(applyParams.getIsAssociatedEnterprise()) && StringUtils.isNotBlank(applyParams.getAssociatedOrgCode())) {
                applyParams.setSupplierCode(applyParams.getAssociatedOrgCode());
            } else {
                AtomicInteger generateCount = new AtomicInteger(0);
                while (true) {
                    //防止各种配置配置错误等,导致的问题最多尝试100次防止死循环
                    ValidatorUtils.checkTrueThrowEx(generateCount.incrementAndGet() >= 100, "供应商编码生成失败");
                    String tempCustomerCode = bizCodeGeneratorService.getBizCode(applyPersonModel.getManageEnterpriseNo(), SystemConstant.GYS_CODE_GENERATE);
                    //生成一个可用的CustomerCode 因为存在导入的情况所以可能得多生成几次
                    final Customer tempCustomer = customerDAO.getCustomerByCustomerCode(applyPersonModel.getManageEnterpriseNo(), tempCustomerCode);
                    //这个编码是可以使用的
                    if (tempCustomer == null) {
                        applyParams.setSupplierCode(tempCustomerCode);
                        break;
                    }
                }
            }
            SaveSupplierDTO params = new SaveSupplierDTO();
            params.setSupplierCode(applyParams.getSupplierCode());
            params.setSupplierName(applyParams.getSupplierName());
            params.setSupplierNameEn(applyParams.getSupplierNameEn());
            params.setMnemonicCode(applyParams.getMnemonicCode());
            params.setSupplierCategoryNo(applyParams.getSupplierCategoryNo());
            params.setCompanyName(applyParams.getCompanyName());
            params.setUnifiedSocialCode(applyParams.getUnifiedSocialCode());
            params.setFactoryType(applyParams.getFactoryType());
            params.setCountry(applyParams.getCountry());
            params.setTaxCategory(applyParams.getTaxCategory());
            params.setEconomicType(applyParams.getEconomicType());
            params.setOwnerCompany(applyParams.getOwnerCompany());
            params.setRetailInvestors(applyParams.getRetailInvestors());
            params.setRemark(applyParams.getRemark());
            //给集团创建客户档案
            supplierService.saveSupplier(operationModel, params, "子租户新增供应商档案");
        } else {
            applyParams.setSupplierCode(supplier.getSupplierCode());
        }
        SupplierAssignModel supplierAssignModel = new SupplierAssignModel();
        supplierAssignModel.setSupplierCode(applyParams.getSupplierCode());
        supplierAssignModel.setOperationModel(operationModel);
        supplierAssignModel.setGroupEnterpriseNo(applyPersonModel.getManageEnterpriseNo());
        supplierAssignModel.setSubEnterpriseNo(applyPersonModel.getApplyEnterpriseNo());
        supplierAssignModel.setExecuteRecordExecuteTypeEnum(ExecuteRecordExcuteTypeEnum.ADD_APPLY);
        supplierAssignModel.setGenerateExecutionRecord(Boolean.TRUE);
        supplierAssignModel.setApplySourceId(applyParams.getId());
        supplierAssignModel.setApplySource("DSRP");
        //把集团的物料档案同步到子租户
        handSupplier(supplierAssignModel);
        //给子租户的客户档案保存特有信息
        final Supplier subSupplier = supplierDAO.getSupplierBySupplierCode(applyPersonModel.getApplyEnterpriseNo(), applyParams.getSupplierCode());
        subSupplier.setControlId(applyParams.getControlId());
        subSupplier.setControlTypeName(applyParams.getControlTypeName());
        subSupplier.setCooperationMode(applyParams.getCooperationMode());
        subSupplier.setCurrency(applyParams.getCurrency());
        subSupplier.setSettlementModes(applyParams.getSettlementModes());
        subSupplier.setSettlementModesName(applyParams.getSettlementModesName());
        subSupplier.setCreditAmount(applyParams.getCreditAmount());
        subSupplier.setPeriodDays(applyParams.getPeriodDays());
        subSupplier.setCoopStartTime(applyParams.getCoopStartTime());
        subSupplier.setCoopEndTime(applyParams.getCoopEndTime());
        subSupplier.setPaymentAgreementId(applyParams.getPaymentAgreementId());
        subSupplier.setPaymentAgreementCode(applyParams.getPaymentAgreementCode());
        subSupplier.setPaymentAgreementName(applyParams.getPaymentAgreementName());
        subSupplier.setPaymentAgreementYsId(applyParams.getPaymentAgreementYsId());
        subSupplier.setPaymentTerm(applyParams.getPaymentTerm());
        supplierDAO.updateById(subSupplier);
        return applyParams.getSupplierCode();
    }
}
