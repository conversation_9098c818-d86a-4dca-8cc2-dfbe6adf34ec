package com.yyigou.dsrp.cdc.service.v2.customer;

import com.yyigou.dsrp.cdc.api.customer.dto.CustomerExternalSaveDTO;
import com.yyigou.dsrp.cdc.api.customer.vo.CustomerExternalSaveVO;
import com.yyigou.dsrp.cdc.api.customer.vo.CustomerInternalSaveVO;
import com.yyigou.dsrp.cdc.common.util.OperationModel;

import java.util.List;

public interface CustomerV2ExternalService {
    List<CustomerExternalSaveVO> batchSaveForDA(OperationModel operationModel, List<CustomerExternalSaveDTO> params);

    void batchSaveMdmCompatible(OperationModel operationModel, List<CustomerExternalSaveDTO> params, List<CustomerInternalSaveVO> resultList);
}
