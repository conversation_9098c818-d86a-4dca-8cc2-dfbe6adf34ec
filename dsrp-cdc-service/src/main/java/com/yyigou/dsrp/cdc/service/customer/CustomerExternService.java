package com.yyigou.dsrp.cdc.service.customer;

import com.yyigou.dsrp.cdc.api.customer.dto.CustomerExternalAssignDTO;
import com.yyigou.dsrp.cdc.api.customer.dto.CustomerExternalSaveDTO;
import com.yyigou.dsrp.cdc.api.customer.vo.CustomerExternalSaveVO;
import com.yyigou.dsrp.cdc.common.util.OperationModel;

import java.util.List;

public interface CustomerExternService {

    List<CustomerExternalSaveVO> batchSave(OperationModel operationModel, List<CustomerExternalSaveDTO> params);

    Boolean customerAssign(OperationModel operationModel, CustomerExternalAssignDTO params);
}
