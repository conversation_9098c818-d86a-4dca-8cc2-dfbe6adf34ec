package com.yyigou.dsrp.cdc.service.v2.supplier.impl;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.yyigou.ddc.common.error.ErrorCode;
import com.yyigou.ddc.common.exception.BusinessException;
import com.yyigou.ddc.common.page.PageDto;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.ddc.common.treegrid.TreeGrid;
import com.yyigou.ddc.common.treegrid.TreeGridUtils;
import com.yyigou.ddc.common.util.ValidatorUtils;
import com.yyigou.ddc.services.ddc.uim.vo.OrganizationVo;
import com.yyigou.ddc.services.dlog.dto.DLogLevel;
import com.yyigou.dsrp.cdc.api.supply.dto.SupplierExternalSaveDTO;
import com.yyigou.dsrp.cdc.api.v2.supplier.vo.SupplierCategoryTreeVO;
import com.yyigou.dsrp.cdc.api.v2.supplier.vo.SupplierCategoryUseOrgVO;
import com.yyigou.dsrp.cdc.api.v2.supplier.vo.SupplierCategoryVO;
import com.yyigou.dsrp.cdc.common.enums.CommonIfEnum;
import com.yyigou.dsrp.cdc.common.enums.CommonStatusEnum;
import com.yyigou.dsrp.cdc.common.enums.DeletedEnum;
import com.yyigou.dsrp.cdc.common.enums.HierarchyQueryEnum;
import com.yyigou.dsrp.cdc.common.util.CommonUtil;
import com.yyigou.dsrp.cdc.common.util.OperationModel;
import com.yyigou.dsrp.cdc.dao.v2.supplier.SupplierCategoryBaseDAO;
import com.yyigou.dsrp.cdc.dao.v2.supplier.SupplierCategoryV2DAO;
import com.yyigou.dsrp.cdc.dao.v2.supplier.entity.SupplierCategoryBase;
import com.yyigou.dsrp.cdc.dao.v2.supplier.entity.SupplierCategoryV2;
import com.yyigou.dsrp.cdc.dao.v2.supplier.entity.SupplierV2;
import com.yyigou.dsrp.cdc.manager.integration.dlog.BusinessLogService;
import com.yyigou.dsrp.cdc.manager.integration.numberCenter.NumberCenterService;
import com.yyigou.dsrp.cdc.manager.integration.uap.GradeControlService;
import com.yyigou.dsrp.cdc.manager.integration.uap.res.GradeCancelAssignRes;
import com.yyigou.dsrp.cdc.manager.integration.uim.OrganizationService;
import com.yyigou.dsrp.cdc.model.constant.BillNameConstant;
import com.yyigou.dsrp.cdc.model.constant.NumberCenterConstant;
import com.yyigou.dsrp.cdc.model.constant.ServiceConstant;
import com.yyigou.dsrp.cdc.model.constant.ViewNameConstant;
import com.yyigou.dsrp.cdc.model.v2.supplier.req.*;
import com.yyigou.dsrp.cdc.service.utils.PageUtils;
import com.yyigou.dsrp.cdc.service.v2.supplier.SupplierCategoryV2Service;
import com.yyigou.dsrp.cdc.service.v2.supplier.SupplierV2Service;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service("supplierCategoryV2Service")
@RequiredArgsConstructor
@Slf4j
public class SupplierCategoryV2ServiceImpl extends ServiceImpl<SupplierCategoryV2DAO, SupplierCategoryV2> implements SupplierCategoryV2Service {
    private static final int MAX_RECURSION_DEPTH = 10; // 设置最大递归深度为10层

    private static final String FIELD_USE_ORG_NO = "use_org_no";

    private final NumberCenterService numberCenterService;
    @Resource
    private SupplierCategoryV2DAO supplierCategoryV2DAO;
    @Resource
    private SupplierCategoryBaseDAO supplierCategoryBaseDAO;
    @Resource
    private SupplierV2Service supplierV2Service;

    @Resource
    private BusinessLogService businessLogService;

    @Resource
    private OrganizationService organizationService;

    @Resource
    private GradeControlService gradeControlService;

    private List<SupplierCategoryTreeVO> queryEligibleSupplierCategory(OperationModel operationModel, SupplierCategoryQueryTreeReq queryTreeReq) {
        log.warn("queryEligibleSupplierCategoryReq，operationModel={},queryTreeReq={}", operationModel, queryTreeReq);

        //单租户附上默认的useOrgNo
        if (StringUtils.isBlank(queryTreeReq.getUseOrgNo())) {
            queryTreeReq.setUseOrgNo(operationModel.getOrgNo());
        }

        ValidatorUtils.checkEmptyThrowEx(queryTreeReq.getUseOrgNo(), "使用组织编号不能为空");

        //单组织和多组织保持一样的逻辑
        //获取被分派的分类节点
        List<SupplierCategoryV2> assignCategoryList = supplierCategoryV2DAO.selectListByUseOrgNo(operationModel.getEnterpriseNo(), queryTreeReq.getUseOrgNo(), DeletedEnum.UN_DELETE.getValue());

        log.warn("assignCategoryList={}", assignCategoryList);

        if (CollectionUtils.isEmpty(assignCategoryList)) {
            return Collections.emptyList();
        }

        //被分派的分类节点-按管理组织进行分组
        Map<String, List<SupplierCategoryV2>> authedManageOrgNo2CategoryList = assignCategoryList.stream().collect(Collectors.groupingBy(SupplierCategoryV2::getManageOrgNo));

        //所有管理组织的分类集合
        List<SupplierCategoryV2> categoryListByManageOrgNo = supplierCategoryV2DAO.getByManageOrgNoList(operationModel.getEnterpriseNo(), new ArrayList<>(authedManageOrgNo2CategoryList.keySet()));

        log.warn("categoryListByManageOrgNo={}", categoryListByManageOrgNo);

        //所有分类-按管理组织进行分组
        Map<String, List<SupplierCategoryV2>> allManageOrgNo2CategoryList = categoryListByManageOrgNo.stream().collect(Collectors.groupingBy(SupplierCategoryV2::getManageOrgNo));

        List<SupplierCategoryTreeVO> treeList = new ArrayList<>();

        //按管理组织进行分类树的构建
        authedManageOrgNo2CategoryList.forEach((manageOrgNo, authedCategoryListByManageOrgNoList) -> {
            //分派的是叶子节点，需要把叶子节点的祖先节点也加入到结果中
            List<SupplierCategoryV2> allNodes = allManageOrgNo2CategoryList.get(manageOrgNo);

            ValidatorUtils.checkTrueThrowEx(hasCircularDependency(allNodes), "供应商分类存在循环依赖");

            Map<String, SupplierCategoryV2> no2SupplierCategoryV2Map = allNodes.stream().collect(Collectors.toMap(SupplierCategoryV2::getNo, supplierCategoryV2 -> supplierCategoryV2));

            List<SupplierCategoryV2> authorizedNodes = findAuthorizedNodes(allNodes, authedCategoryListByManageOrgNoList);

            log.warn("findAuthorizedNodes,allNodes={},authedCategoryListByManageOrgNoList={},authorizedNodes={}", allNodes, authedCategoryListByManageOrgNoList, authorizedNodes);

            List<OrganizationVo> organizationVoList = organizationService.findListNoAuth(operationModel.getEnterpriseNo(), new ArrayList<>(authorizedNodes.stream().map(SupplierCategoryV2::getManageOrgNo).collect(Collectors.toSet())));
            final Map<String, OrganizationVo> orgNo2OrganizationVo = organizationVoList.stream().collect(Collectors.toMap(OrganizationVo::getOrgNo, Function.identity()));

            authorizedNodes.forEach(supplierCategoryV2 -> {
                SupplierCategoryTreeVO tree = new SupplierCategoryTreeVO();
                BeanUtils.copyProperties(supplierCategoryV2, tree);

                // 填充上级分类名称
                if (ServiceConstant.TOP_PARENT_NO.equals(supplierCategoryV2.getParentNo())) {
                    tree.setParentName("");
                } else {
                    SupplierCategoryV2 parentNode = no2SupplierCategoryV2Map.get(supplierCategoryV2.getParentNo());
                    if (null != parentNode) {
                        tree.setParentName(parentNode.getCategoryName());
                    }
                }

                // 填充管理组织名称
                if (orgNo2OrganizationVo.containsKey(supplierCategoryV2.getManageOrgNo())) {
                    OrganizationVo organizationVo = orgNo2OrganizationVo.get(supplierCategoryV2.getManageOrgNo());
                    tree.setManageOrgName(organizationVo.getOrgName());
                    tree.setManageOrgCode(organizationVo.getOrgCode());
                }

                treeList.add(tree);
            });
        });

        log.warn("treeList={}", treeList);

        return treeList;
    }

    @Override
    public List<SupplierCategoryTreeVO> queryTree(OperationModel operationModel, SupplierCategoryQueryTreeReq queryTreeReq) {
        return TreeGridUtils.getChildCategoryTrees(queryEligibleSupplierCategory(operationModel, queryTreeReq), ServiceConstant.TOP_PARENT_NO);
    }

    @Override
    public List<SupplierCategoryTreeVO> queryManageTree(OperationModel operationModel, SupplierCategoryQueryCategoryTreeReq queryTreeReq) {
        ValidatorUtils.checkEmptyThrowEx(queryTreeReq.getManageOrgNo(), "管理组织编号不能为空");

        //单组织和多组织保持一样的逻辑
        List<SupplierCategoryV2> categoryList = supplierCategoryV2DAO.selectList(Wrappers.<SupplierCategoryV2>lambdaQuery()
                .eq(SupplierCategoryV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(SupplierCategoryV2::getManageOrgNo, queryTreeReq.getManageOrgNo())
                .eq(SupplierCategoryV2::getDeleted, DeletedEnum.UN_DELETE.getValue())
        );

        if (CollectionUtils.isEmpty(categoryList)) {
            return Collections.emptyList();
        }

        Map<String, SupplierCategoryV2> no2SupplierCategoryV2Map = categoryList.stream().collect(Collectors.toMap(SupplierCategoryV2::getNo, supplierCategoryV2 -> supplierCategoryV2));

        List<SupplierCategoryTreeVO> treeList = new ArrayList<>();

        List<OrganizationVo> organizationVoList = organizationService.findListNoAuth(operationModel.getEnterpriseNo(), new ArrayList<>(categoryList.stream().map(SupplierCategoryV2::getManageOrgNo).collect(Collectors.toSet())));
        Map<String, OrganizationVo> orgNo2OrganizationVo = organizationVoList.stream().collect(Collectors.toMap(OrganizationVo::getOrgNo, Function.identity()));

        for (SupplierCategoryV2 supplierCategoryV2 : categoryList) {
            SupplierCategoryTreeVO tree = new SupplierCategoryTreeVO();
            BeanUtils.copyProperties(supplierCategoryV2, tree);

            // 填充上级分类名称
            if (ServiceConstant.TOP_PARENT_NO.equals(supplierCategoryV2.getParentNo())) {
                tree.setParentName("");
            } else {
                SupplierCategoryV2 parentNode = no2SupplierCategoryV2Map.get(supplierCategoryV2.getParentNo());
                if (null != parentNode) {
                    tree.setParentName(parentNode.getCategoryName());
                }
            }

            // 填充管理组织名称
            if (orgNo2OrganizationVo.containsKey(supplierCategoryV2.getManageOrgNo())) {
                OrganizationVo organizationVo = orgNo2OrganizationVo.get(supplierCategoryV2.getManageOrgNo());
                tree.setManageOrgName(organizationVo.getOrgName());
                tree.setManageOrgCode(organizationVo.getOrgCode());
            }

            // 填充使用组织名称
            List<SupplierCategoryBase> supplierCategoryBases = supplierCategoryBaseDAO.selectList(Wrappers.<SupplierCategoryBase>lambdaQuery()
                    .eq(SupplierCategoryBase::getEnterpriseNo, operationModel.getEnterpriseNo())
                    .eq(SupplierCategoryBase::getManageOrgNo, supplierCategoryV2.getManageOrgNo())
                    .eq(SupplierCategoryBase::getCategoryNo, supplierCategoryV2.getNo())
                    .eq(SupplierCategoryBase::getDeleted, DeletedEnum.UN_DELETE.getValue()));
            if (CollectionUtils.isNotEmpty(supplierCategoryBases)) {
                List<String> names = new ArrayList<>();
                List<SupplierCategoryUseOrgVO> supplierCategoryUseOrgVOList = new ArrayList<>();

                organizationVoList = organizationService.findListNoAuth(operationModel.getEnterpriseNo(), new ArrayList<>(supplierCategoryBases.stream().map(SupplierCategoryBase::getUseOrgNo).collect(Collectors.toSet())));
                orgNo2OrganizationVo = organizationVoList.stream().collect(Collectors.toMap(OrganizationVo::getOrgNo, Function.identity()));

                for (SupplierCategoryBase supplierCategoryBase : supplierCategoryBases) {
                    if (orgNo2OrganizationVo.containsKey(supplierCategoryBase.getUseOrgNo())) {
                        OrganizationVo organizationVo = orgNo2OrganizationVo.get(supplierCategoryBase.getUseOrgNo());
                        SupplierCategoryUseOrgVO supplierCategoryUseOrgVO = new SupplierCategoryUseOrgVO();
                        supplierCategoryUseOrgVO.setId(supplierCategoryBase.getId());
                        supplierCategoryUseOrgVO.setUseOrgNo(organizationVo.getOrgNo());
                        supplierCategoryUseOrgVO.setUseOrgCode(organizationVo.getOrgCode());
                        supplierCategoryUseOrgVO.setUseOrgName(organizationVo.getOrgName());
                        supplierCategoryUseOrgVOList.add(supplierCategoryUseOrgVO);

                        names.add(organizationVo.getOrgName());

                    }
                }
                tree.setUseOrgNames(String.join("，", names));

                tree.setUseOrgList(supplierCategoryUseOrgVOList);
            }

            treeList.add(tree);
        }

        return TreeGridUtils.getChildCategoryTrees(treeList, ServiceConstant.TOP_PARENT_NO);
    }

    @Override
    public List<SupplierCategoryTreeVO> queryUseTree(OperationModel operationModel, SupplierCategoryQueryUseCategoryTreeReq queryTreeReq) {
        SupplierCategoryQueryTreeReq supplierCategoryQueryTreeReq = new SupplierCategoryQueryTreeReq();
        supplierCategoryQueryTreeReq.setEnterpriseNo(operationModel.getEnterpriseNo());
        supplierCategoryQueryTreeReq.setUseOrgNo(queryTreeReq.getUseOrgNo());

        return TreeGridUtils.getChildCategoryTrees(queryEligibleSupplierCategory(operationModel, supplierCategoryQueryTreeReq), ServiceConstant.TOP_PARENT_NO);
    }

    /**
     * 分类树对接参照
     *
     * @param operationModel
     * @param queryTreeReq
     * @return
     */
    @Override
    public List<SupplierCategoryTreeVO> queryReferTree(OperationModel operationModel, SupplierCategoryQuerySolutionTreeReq queryTreeReq) {
        ValidatorUtils.checkEmptyThrowEx(queryTreeReq.getQueryConditionList(), "查询条件为空");

        String[] useOrgNoArray = null;
        for (SupplierCategoryQuerySolutionTreeReq.QueryCondition queryCondition : queryTreeReq.getQueryConditionList()) {
            if (FIELD_USE_ORG_NO.equals(queryCondition.getColumn())) {
                if (StringUtils.isNotEmpty(queryCondition.getValue())) {
                    useOrgNoArray = StringUtils.split(queryCondition.getValue(), ",");
                }
                break;
            }
        }

        if (!operationModel.getSingleOrgFlag()) {
            ValidatorUtils.checkTrueThrowEx(ArrayUtils.isEmpty(useOrgNoArray), "使用组织编号为空");
        } else {
            useOrgNoArray = new String[]{operationModel.getOrgNo()};
        }

        List<SupplierCategoryTreeVO> supplierCategoryTreeVOS = queryReferTree(operationModel, useOrgNoArray);

        return TreeGridUtils.getChildCategoryTrees(supplierCategoryTreeVOS, ServiceConstant.TOP_PARENT_NO);
    }

    private List<SupplierCategoryTreeVO> queryReferTree(OperationModel operationModel, String[] useOrgNoArray) {
        log.warn("queryReferTree，operationModel={},useOrgNoArray={}", operationModel, useOrgNoArray);

        //单组织和多组织保持一样的逻辑
        //获取被分派的分类节点
        List<SupplierCategoryV2> assignCategoryList = supplierCategoryV2DAO.selectListByUseOrgNoList(operationModel.getEnterpriseNo(), Arrays.asList(useOrgNoArray), DeletedEnum.UN_DELETE.getValue());

        log.warn("assignCategoryList={}", assignCategoryList);

        if (CollectionUtils.isEmpty(assignCategoryList)) {
            return Collections.emptyList();
        }

        //被分派的分类节点-按管理组织进行分组
        Map<String, List<SupplierCategoryV2>> authedManageOrgNo2CategoryList = assignCategoryList.stream().collect(Collectors.groupingBy(SupplierCategoryV2::getManageOrgNo));

        //所有管理组织的分类集合
        List<SupplierCategoryV2> categoryListByManageOrgNo = supplierCategoryV2DAO.getByManageOrgNoList(operationModel.getEnterpriseNo(), new ArrayList<>(authedManageOrgNo2CategoryList.keySet()));

        log.warn("categoryListByManageOrgNo={}", categoryListByManageOrgNo);

        //所有分类-按管理组织进行分组
        Map<String, List<SupplierCategoryV2>> allManageOrgNo2CategoryList = categoryListByManageOrgNo.stream().collect(Collectors.groupingBy(SupplierCategoryV2::getManageOrgNo));

        List<SupplierCategoryTreeVO> treeList = new ArrayList<>();

        //按管理组织进行分类树的构建
        authedManageOrgNo2CategoryList.forEach((manageOrgNo, authedCategoryListByManageOrgNoList) -> {
            //分派的是叶子节点，需要把叶子节点的祖先节点也加入到结果中
            List<SupplierCategoryV2> allNodes = allManageOrgNo2CategoryList.get(manageOrgNo);

            ValidatorUtils.checkTrueThrowEx(hasCircularDependency(allNodes), "供应商分类存在循环依赖");

            Map<String, SupplierCategoryV2> no2SupplierCategoryV2Map = allNodes.stream().collect(Collectors.toMap(SupplierCategoryV2::getNo, supplierCategoryV2 -> supplierCategoryV2));

            List<SupplierCategoryV2> authorizedNodes = findAuthorizedNodes(allNodes, authedCategoryListByManageOrgNoList);

            log.warn("findAuthorizedNodes,allNodes={},authedCategoryListByManageOrgNoList={},authorizedNodes={}", allNodes, authedCategoryListByManageOrgNoList, authorizedNodes);

            List<OrganizationVo> organizationVoList = organizationService.findListNoAuth(operationModel.getEnterpriseNo(), new ArrayList<>(authorizedNodes.stream().map(SupplierCategoryV2::getManageOrgNo).collect(Collectors.toSet())));
            final Map<String, OrganizationVo> orgNo2OrganizationVo = organizationVoList.stream().collect(Collectors.toMap(OrganizationVo::getOrgNo, Function.identity()));

            authorizedNodes.forEach(supplierCategoryV2 -> {
                SupplierCategoryTreeVO tree = new SupplierCategoryTreeVO();
                BeanUtils.copyProperties(supplierCategoryV2, tree);

                // 填充上级分类名称
                if (ServiceConstant.TOP_PARENT_NO.equals(supplierCategoryV2.getParentNo())) {
                    tree.setParentName("");
                } else {
                    SupplierCategoryV2 parentNode = no2SupplierCategoryV2Map.get(supplierCategoryV2.getParentNo());
                    if (null != parentNode) {
                        tree.setParentName(parentNode.getCategoryName());
                    }
                }

                // 填充管理组织名称
                if (orgNo2OrganizationVo.containsKey(supplierCategoryV2.getManageOrgNo())) {
                    OrganizationVo organizationVo = orgNo2OrganizationVo.get(supplierCategoryV2.getManageOrgNo());
                    tree.setManageOrgName(organizationVo.getOrgName());
                    tree.setManageOrgCode(organizationVo.getOrgCode());
                }

                treeList.add(tree);
            });
        });

        log.warn("treeList={}", treeList);

        return treeList;
    }


    @Override
    public PageVo<SupplierCategoryVO> queryListPage(OperationModel operationModel, SupplierCategoryQueryListPageReq queryListReq, PageDto pageDTO) {
        if (StringUtils.isBlank(queryListReq.getUseOrgNo())) {
            ValidatorUtils.checkEmptyThrowEx(operationModel.getOrgCode(), "使用组织编号不能为空");
            queryListReq.setUseOrgNo(operationModel.getOrgNo());
        }

        if (StringUtils.isBlank(queryListReq.getParentNo())) {
            queryListReq.setParentNo(ServiceConstant.TOP_PARENT_NO);
        }

        SupplierCategoryQueryTreeReq queryTreeReq = new SupplierCategoryQueryTreeReq();
        queryTreeReq.setEnterpriseNo(operationModel.getEnterpriseNo());
        queryTreeReq.setUseOrgNo(queryListReq.getUseOrgNo());

        List<SupplierCategoryTreeVO> supplierCategoryTreeVOS = queryEligibleSupplierCategory(operationModel, queryTreeReq);

        List<SupplierCategoryTreeVO> treeVoList = TreeGridUtils.getChildCategoryTrees(supplierCategoryTreeVOS, ServiceConstant.TOP_PARENT_NO);

        List<String> allNos = new ArrayList<>();

        if (HierarchyQueryEnum.SUB_LEVEL.getValue().equals(queryListReq.getShowType())) {// 查下级节点
            findNextLevels(treeVoList, queryListReq.getParentNo(), allNos, 0);

            log.warn("findNextLevels，treeVoList={},parentNo={},allNos={}", treeVoList, queryListReq.getParentNo(), allNos);

        } else {// 查所有子孙节点
            if (ServiceConstant.TOP_PARENT_NO.equals(queryListReq.getParentNo())) {
                findAllDescendants(treeVoList, allNos, 0);

                log.warn("findAllDescendants，treeVoList={},allNos={}", treeVoList, allNos);

            } else {
                //找到指定的节点
                SupplierCategoryTreeVO specificNo = findSpecificNode(treeVoList, queryListReq.getParentNo(), 0);

                log.warn("findSpecificNode，treeVoList={},parentNo={}", treeVoList, queryListReq.getParentNo());

                if (null != specificNo) {
                    allNos.add(queryListReq.getParentNo());

                    findAllDescendants(specificNo.getChildren(), allNos, 0);

                    log.warn("findAllDescendants，treeVoList={},allNos={}", specificNo.getChildren(), allNos);
                }
            }
        }

        if (CollectionUtils.isEmpty(allNos)) {
            return new PageVo<>();
        }

        Page<SupplierCategoryV2> page = PageHelper.startPage(pageDTO.getPageIndex(), pageDTO.getPageSize(), pageDTO.getOrderBy());
        LambdaQueryWrapper<SupplierCategoryV2> wrapper = Wrappers.<SupplierCategoryV2>lambdaQuery()
                .eq(SupplierCategoryV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(SupplierCategoryV2::getDeleted, DeletedEnum.UN_DELETE.getValue())
                .eq(null != queryListReq.getStatus(), SupplierCategoryV2::getStatus, queryListReq.getStatus())
                .nested(StringUtils.isNotBlank(queryListReq.getKeywords()), w ->
                        w.like(SupplierCategoryV2::getCategoryCode, queryListReq.getKeywords())
                                .or()
                                .like(SupplierCategoryV2::getCategoryName, queryListReq.getKeywords()))
                .in(SupplierCategoryV2::getNo, allNos)
                .in(CollectionUtils.isNotEmpty(queryListReq.getIdList()), SupplierCategoryV2::getNo, queryListReq.getIdList())
                .last("order by level asc, sort_num is null asc, sort_num asc");

        supplierCategoryV2DAO.selectList(wrapper);

        return PageUtils.convertPageVo(page, data -> {
            List<SupplierCategoryVO> detailVOList = new ArrayList<>();
            if (Objects.nonNull(data)) {
                Map<String, SupplierCategoryTreeVO> no2CategoryTreeVOMap = supplierCategoryTreeVOS.stream().collect(Collectors.toMap(SupplierCategoryTreeVO::getNo, Function.identity()));
                Set<String> parentNoSet = supplierCategoryTreeVOS.stream().map(SupplierCategoryTreeVO::getParentNo).filter(parentNo -> !ServiceConstant.TOP_PARENT_NO.equals(parentNo)).collect(Collectors.toSet());

                data.forEach(supplierCategoryV2 -> {
                    SupplierCategoryVO supplierCategoryVO = new SupplierCategoryVO();
                    BeanUtils.copyProperties(supplierCategoryV2, supplierCategoryVO);

                    // 填充管理组织名称
                    supplierCategoryVO.setManageOrgName(no2CategoryTreeVOMap.get(supplierCategoryV2.getNo()).getManageOrgName());
                    supplierCategoryVO.setManageOrgCode(no2CategoryTreeVOMap.get(supplierCategoryV2.getNo()).getManageOrgCode());

                    // 填充使用组织名称
                    supplierCategoryVO.setUseOrgNames(no2CategoryTreeVOMap.get(supplierCategoryV2.getNo()).getUseOrgNames());
                    supplierCategoryVO.setUseOrgList(no2CategoryTreeVOMap.get(supplierCategoryV2.getNo()).getUseOrgList());

                    // 填充子孙节点标记
                    supplierCategoryVO.setDescendantsFlag(parentNoSet.contains(supplierCategoryV2.getNo()) ? CommonIfEnum.YES.getValue() : CommonIfEnum.NO.getValue());

                    // 填充请求的使用组织
                    supplierCategoryVO.setInputUseOrgNo(queryListReq.getUseOrgNo());
                    supplierCategoryVO.setInputUseOrgName(queryListReq.getUseOrgName());

                    // 填充上级分类名称
                    if (ServiceConstant.TOP_PARENT_NO.equals(no2CategoryTreeVOMap.get(supplierCategoryV2.getNo()).getParentNo())) {
                        supplierCategoryVO.setParentNo("");
                        supplierCategoryVO.setParentName("");
                    } else {
                        supplierCategoryVO.setParentName(no2CategoryTreeVOMap.get(supplierCategoryV2.getNo()).getParentName());
                    }

                    supplierCategoryVO.setStatusName(CommonStatusEnum.INVALID.getValue().toString().equals(supplierCategoryV2.getStatus()) ? "停用" : "启用");

                    detailVOList.add(supplierCategoryVO);
                });
            }
            return detailVOList;
        });
    }

    @Override
    public Boolean checkUniqueName(OperationModel operationModel, String no, String name, String parentNo) {
        return supplierCategoryV2DAO.selectCount(new LambdaQueryWrapper<SupplierCategoryV2>()
                .eq(SupplierCategoryV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(SupplierCategoryV2::getCategoryName, name)
                .eq(SupplierCategoryV2::getParentNo, parentNo)
                .eq(SupplierCategoryV2::getDeleted, DeletedEnum.UN_DELETE.getValue())
                .ne(StringUtils.isNotEmpty(no), SupplierCategoryV2::getNo, no)) <= 0;
    }

    @Override
    public Boolean checkUniqueCode(OperationModel operationModel, String no, String code) {
        return supplierCategoryV2DAO.selectCount(new LambdaQueryWrapper<SupplierCategoryV2>()
                .eq(SupplierCategoryV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(SupplierCategoryV2::getCategoryCode, code)
                .eq(SupplierCategoryV2::getDeleted, DeletedEnum.UN_DELETE.getValue())
                .ne(StringUtils.isNotEmpty(no), SupplierCategoryV2::getNo, no)) <= 0;
    }

    @Override
    public Boolean checkUseOrgRemoval(OperationModel operationModel, SupplierCategoryCheckUseOrgRemovalReq params) {
        List<SupplierV2> supplierV2s = supplierV2Service.queryByManageOrgNoList(operationModel, params.getNo(), Collections.singletonList(params.getUseOrgNo()));
        return CollectionUtils.isEmpty(supplierV2s);
    }

    @Override
    @Transactional
    public SupplierCategoryVO saveSupplierCategory(OperationModel operationModel, SupplierCategorySaveReq supplierCategorySaveReq) {
        // 校验必填参数
        ValidatorUtils.checkEmptyThrowEx(operationModel.getEnterpriseNo(), "租户编号不能为空");
        ValidatorUtils.checkEmptyThrowEx(supplierCategorySaveReq.getCategoryCode(), "分类编码不能为空");
        ValidatorUtils.checkEmptyThrowEx(supplierCategorySaveReq.getCategoryName(), "分类名称不能为空");
        ValidatorUtils.checkEmptyThrowEx(supplierCategorySaveReq.getManageOrgNo(), "管理组织不能为空");

        List<String> manageNoList = gradeControlService.listMgrOrgNos(operationModel.getEnterpriseNo(), BillNameConstant.BDC_SUPPLIER_CATEGORY_BILL);
        ValidatorUtils.checkEmptyThrowEx(manageNoList, "无档案管理权");
        ValidatorUtils.checkTrueThrowEx(!manageNoList.contains(supplierCategorySaveReq.getManageOrgNo()), "无档案管理权");

        ValidatorUtils.checkTrueThrowEx(supplierCategorySaveReq.getCategoryCode().length() > 50, "分类编码长度不能超过50");

        //校验参数合法性
        if (supplierCategorySaveReq.getSortNum() != null) {
            ValidatorUtils.checkTrueThrowEx(supplierCategorySaveReq.getSortNum() < 1, "排序不能小于1");
            ValidatorUtils.checkTrueThrowEx(supplierCategorySaveReq.getSortNum() > 127, "排序不能大于127");
        }
        if (StringUtils.isNotEmpty(supplierCategorySaveReq.getMnemonicCode())) {
            ValidatorUtils.checkTrueThrowEx(supplierCategorySaveReq.getMnemonicCode().length() > 50, "助记码长度不能超过50");
        }
        if (StringUtils.isNotEmpty(supplierCategorySaveReq.getRemark())) {
            ValidatorUtils.checkTrueThrowEx(supplierCategorySaveReq.getRemark().length() > 300, "描述长度不能超过300");
        }

        // 停用态不允许添加使用组织
//        if (CommonStatusEnum.INVALID.getValue().toString().equals(supplierCategorySaveReq.getStatus())) {
//            if (CollectionUtils.isNotEmpty(supplierCategorySaveReq.getUseOrgNoList())) {
//                ValidatorUtils.checkTrueThrowEx(supplierCategorySaveReq.getUseOrgNoList().size() > 1, "停用态供应商分类不允许添加使用组织");
//                if (supplierCategorySaveReq.getUseOrgNoList().size() == 1) {
//                    ValidatorUtils.checkTrueThrowEx(!supplierCategorySaveReq.getUseOrgNoList().get(0).equals(supplierCategorySaveReq.getManageOrgNo()), "停用态供应商分类不允许添加使用组织");
//                }
//            }
//        }

        String parentNo = null;
        SupplierCategoryV2 parentSupplierCategoryV2 = null;

        if (ServiceConstant.TOP_PARENT_NO.equals(supplierCategorySaveReq.getParentNo()) || StringUtils.isBlank(supplierCategorySaveReq.getParentNo())) {
            parentNo = ServiceConstant.TOP_PARENT_NO;
        } else {
            parentNo = supplierCategorySaveReq.getParentNo();

            parentSupplierCategoryV2 = supplierCategoryV2DAO.selectOne(new LambdaQueryWrapper<SupplierCategoryV2>()
                    .eq(SupplierCategoryV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                    .eq(SupplierCategoryV2::getNo, parentNo)
                    .eq(SupplierCategoryV2::getDeleted, DeletedEnum.UN_DELETE.getValue())
            );
            ValidatorUtils.checkEmptyThrowEx(parentSupplierCategoryV2, "上级供应商分类不存在");
            ValidatorUtils.checkTrueThrowEx(parentSupplierCategoryV2.getLevel() >= 5, "上级分类不能选择第五级的节点");
            ValidatorUtils.checkTrueThrowEx(CommonStatusEnum.INVALID.getValue().toString().equals(parentSupplierCategoryV2.getStatus()), "上级供应商分类已停用");

            //上级节点是否被引用
            if (!ServiceConstant.TOP_PARENT_NO.equals(parentNo)) {
                boolean supplierRef = supplierV2Service.checkQuoteCategory(operationModel, parentNo);
                ValidatorUtils.checkTrueThrowEx(supplierRef, "上级供应商分类已被供应商引用");
            }

            boolean hasAssign = supplierCategoryBaseDAO.exists(new LambdaQueryWrapper<SupplierCategoryBase>()
                    .eq(SupplierCategoryBase::getEnterpriseNo, operationModel.getEnterpriseNo())
                    .eq(SupplierCategoryBase::getCategoryNo, parentNo)
                    .eq(SupplierCategoryBase::getManageOrgNo, supplierCategorySaveReq.getManageOrgNo())
                    .ne(SupplierCategoryBase::getUseOrgNo, supplierCategorySaveReq.getManageOrgNo())
                    .eq(SupplierCategoryBase::getDeleted, DeletedEnum.UN_DELETE.getValue())
            );
            ValidatorUtils.checkTrueThrowEx(hasAssign, "上级供应商分类已被分配");
        }

        //唯一性校验
        ValidatorUtils.checkTrueThrowEx(!this.checkUniqueName(operationModel, null, supplierCategorySaveReq.getCategoryName(), parentNo), "同一层级下已存在供应商分类名称");
        ValidatorUtils.checkTrueThrowEx(!this.checkUniqueCode(operationModel, null, supplierCategorySaveReq.getCategoryCode()), "企业已存在供应商分类编码");

        // 保存供应商分类信息
        SupplierCategoryV2 supplierCategoryV2 = new SupplierCategoryV2();
        BeanUtils.copyProperties(supplierCategorySaveReq, supplierCategoryV2);
        supplierCategoryV2.setEnterpriseNo(operationModel.getEnterpriseNo());

        String supplierCategoryNo = numberCenterService.createNumber(NumberCenterConstant.SUPPLY_NO_CATEGORY_KEY_V2);
        supplierCategoryV2.setNo(supplierCategoryNo);
        supplierCategoryV2.setCategoryCode(supplierCategorySaveReq.getCategoryCode());
        supplierCategoryV2.setParentNo(parentNo);
        supplierCategoryV2.setDeleted(DeletedEnum.UN_DELETE.getValue());

        if (ServiceConstant.TOP_PARENT_NO.equals(parentNo)) {
            supplierCategoryV2.setLevel(1);
            supplierCategoryV2.setTopCategoryNo(supplierCategoryNo);
            supplierCategoryV2.setPath(supplierCategoryNo);
        } else {
            supplierCategoryV2.setLevel(Optional.ofNullable(parentSupplierCategoryV2.getLevel()).orElseGet(() -> 0) + 1);
            supplierCategoryV2.setTopCategoryNo(parentSupplierCategoryV2.getTopCategoryNo());
            supplierCategoryV2.setPath(parentSupplierCategoryV2.getPath() + "/" + supplierCategoryNo);
        }

        CommonUtil.fillCreatInfo(operationModel, supplierCategoryV2);
        CommonUtil.fillOperateInfo(operationModel, supplierCategoryV2);
        supplierCategoryV2DAO.insert(supplierCategoryV2);

        // 移除上级节点的分配记录（仅管理组织一条记录）
        List<SupplierCategoryBase> supplierCategoryBases = supplierCategoryBaseDAO.selectList(Wrappers.<SupplierCategoryBase>lambdaQuery()
                .eq(SupplierCategoryBase::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(SupplierCategoryBase::getManageOrgNo, supplierCategorySaveReq.getManageOrgNo())
                .eq(SupplierCategoryBase::getDeleted, DeletedEnum.UN_DELETE.getValue())
                .eq(SupplierCategoryBase::getCategoryNo, parentNo));
        for (SupplierCategoryBase supplierCategoryBase : supplierCategoryBases) {
            supplierCategoryBase.setDeleted(DeletedEnum.DELETED.getValue());
        }
        supplierCategoryBaseDAO.updateByIdBatch(supplierCategoryBases);

        SupplierCategoryBase supplierCategoryBase = new SupplierCategoryBase();
        supplierCategoryBase.setEnterpriseNo(operationModel.getEnterpriseNo());
        supplierCategoryBase.setCategoryNo(supplierCategoryNo);
        supplierCategoryBase.setCategoryCode(supplierCategorySaveReq.getCategoryCode());
        supplierCategoryBase.setManageOrgNo(supplierCategorySaveReq.getManageOrgNo());
        supplierCategoryBase.setUseOrgNo(supplierCategorySaveReq.getManageOrgNo());
        supplierCategoryBase.setDeleted(DeletedEnum.UN_DELETE.getValue());
        CommonUtil.fillCreatInfo(operationModel, supplierCategoryBase);
        CommonUtil.fillOperateInfo(operationModel, supplierCategoryBase);
        supplierCategoryBaseDAO.insert(supplierCategoryBase);

        SupplierCategoryVO supplierCategoryVO = new SupplierCategoryVO();
        BeanUtils.copyProperties(supplierCategoryV2, supplierCategoryVO);

        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                try {
                    businessLogService.saveLog(DLogLevel.INFO.name(), ViewNameConstant.BDC_SUPPLIER_CATEGORY_VIEW, supplierCategoryNo, "新增供应商分类", "新增供应商分类", JSON.toJSONString(supplierCategoryV2), "");
                } catch (Exception e) {
                    log.error("新增供应商分类日志保存失败", e);
                }
            }
        });

        return supplierCategoryVO;
    }

    private void getDescendants(OperationModel operationModel, String entterpriseNo, String parentNo, List<SupplierCategoryV2> supplierCategoryV2List, int depth) {
        if (depth >= MAX_RECURSION_DEPTH) {
            log.error("递归深度超过{}层，停止递归", MAX_RECURSION_DEPTH);
            throw new BusinessException("分类层级异常");
        }

        List<SupplierCategoryV2> supplierCategoryV2s = supplierCategoryV2DAO.selectList(Wrappers.<SupplierCategoryV2>lambdaQuery()
                .eq(SupplierCategoryV2::getEnterpriseNo, entterpriseNo)
                .eq(SupplierCategoryV2::getParentNo, parentNo)
                .eq(SupplierCategoryV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));

        if (CollectionUtils.isNotEmpty(supplierCategoryV2s)) {
            supplierCategoryV2List.addAll(supplierCategoryV2s);

            for (SupplierCategoryV2 supplierCategoryV2 : supplierCategoryV2s) {
                getDescendants(operationModel, entterpriseNo, supplierCategoryV2.getNo(), supplierCategoryV2List, depth + 1);
            }
        }
    }

    @Override
    public SupplierCategoryVO getSupplierCategory(OperationModel operationModel, SupplierCategoryGetReq supplierCategoryGetReq) {
        if (StringUtils.isBlank(supplierCategoryGetReq.getUseOrgNo())) {
            ValidatorUtils.checkEmptyThrowEx(operationModel.getOrgCode(), "使用组织编号不能为空");
            supplierCategoryGetReq.setUseOrgNo(operationModel.getOrgNo());
        }
        // 校验必填参数
        ValidatorUtils.checkEmptyThrowEx(operationModel.getEnterpriseNo(), "租户编号不能为空");
        ValidatorUtils.checkEmptyThrowEx(supplierCategoryGetReq.getNo(), "供应商分类编号不能为空");

        SupplierCategoryV2 supplierCategoryV2 = supplierCategoryV2DAO.getByNo(operationModel.getEnterpriseNo(), supplierCategoryGetReq.getNo());

        SupplierCategoryVO supplierCategoryVO = new SupplierCategoryVO();
        BeanUtils.copyProperties(supplierCategoryV2, supplierCategoryVO);

        // 填充上级分类名称
        if (ServiceConstant.TOP_PARENT_NO.equals(supplierCategoryV2.getParentNo())) {
            supplierCategoryVO.setParentNo("");
            supplierCategoryVO.setParentName("");
        } else {
            SupplierCategoryV2 parentSupplierCategoryV2 = supplierCategoryV2DAO.getByNo(operationModel.getEnterpriseNo(), supplierCategoryV2.getParentNo());
            if (null != parentSupplierCategoryV2) {
                supplierCategoryVO.setParentName(parentSupplierCategoryV2.getCategoryName());
            }
        }

        Set<String> orgNos = new HashSet<>();
        orgNos.add(supplierCategoryV2.getManageOrgNo());

        List<SupplierCategoryBase> supplierCategoryBases = supplierCategoryBaseDAO.selectList(Wrappers.<SupplierCategoryBase>lambdaQuery()
                .eq(SupplierCategoryBase::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(SupplierCategoryBase::getManageOrgNo, supplierCategoryV2.getManageOrgNo())
                .eq(SupplierCategoryBase::getCategoryNo, supplierCategoryV2.getNo())
                .eq(SupplierCategoryBase::getDeleted, DeletedEnum.UN_DELETE.getValue()));

        if (CollectionUtils.isNotEmpty(supplierCategoryBases)) {
            orgNos.addAll(supplierCategoryBases.stream().map(SupplierCategoryBase::getUseOrgNo).collect(Collectors.toSet()));
        }

        List<OrganizationVo> organizationVoList = organizationService.findListNoAuth(operationModel.getEnterpriseNo(), new ArrayList<>(orgNos));
        final Map<String, OrganizationVo> orgNo2OrganizationVo = organizationVoList.stream().collect(Collectors.toMap(OrganizationVo::getOrgNo, Function.identity()));

        if (orgNo2OrganizationVo.containsKey(supplierCategoryV2.getManageOrgNo())) {
            OrganizationVo organizationVo = orgNo2OrganizationVo.get(supplierCategoryV2.getManageOrgNo());
            supplierCategoryVO.setManageOrgName(organizationVo.getOrgName());
            supplierCategoryVO.setManageOrgCode(organizationVo.getOrgCode());
        }

        if (CollectionUtils.isNotEmpty(supplierCategoryBases)) {
            List<String> names = new ArrayList<>();
            List<SupplierCategoryUseOrgVO> useOrgVOS = new ArrayList<>();
            for (SupplierCategoryBase supplierCategoryBase : supplierCategoryBases) {
                if (orgNo2OrganizationVo.containsKey(supplierCategoryBase.getUseOrgNo())) {
                    OrganizationVo organizationVo = orgNo2OrganizationVo.get(supplierCategoryBase.getUseOrgNo());

                    SupplierCategoryUseOrgVO supplierCategoryUseOrgVO = new SupplierCategoryUseOrgVO();
                    supplierCategoryUseOrgVO.setId(supplierCategoryBase.getId());
                    supplierCategoryUseOrgVO.setUseOrgNo(supplierCategoryBase.getUseOrgNo());
                    supplierCategoryUseOrgVO.setUseOrgCode(organizationVo.getOrgCode());
                    supplierCategoryUseOrgVO.setUseOrgName(organizationVo.getOrgName());

                    useOrgVOS.add(supplierCategoryUseOrgVO);

                    names.add(organizationVo.getOrgName());
                }
            }

            supplierCategoryVO.setUseOrgNames(String.join("，", names));

            supplierCategoryVO.setUseOrgList(useOrgVOS);
        }

        // 填充请求的使用组织
        supplierCategoryVO.setInputUseOrgNo(supplierCategoryGetReq.getUseOrgNo());
        supplierCategoryVO.setInputUseOrgName(supplierCategoryGetReq.getUseOrgName());

        // 填充子孙节点标记
        boolean exists = supplierCategoryV2DAO.exists(Wrappers.<SupplierCategoryV2>lambdaQuery()
                .eq(SupplierCategoryV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(SupplierCategoryV2::getParentNo, supplierCategoryV2.getNo())
                .eq(SupplierCategoryV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
        supplierCategoryVO.setDescendantsFlag(exists ? CommonIfEnum.YES.getValue() : CommonIfEnum.NO.getValue());

        return supplierCategoryVO;
    }

    private String getPath(Map<String, SupplierCategoryV2> finalNo2CategoryMap, String currentNo) {
        Deque<String> pathStack = new ArrayDeque<>();
        pathStack.push(currentNo);

        String tmpNo = currentNo;
        int loop = 0;
        while (true) {
            if (loop > MAX_RECURSION_DEPTH) {
                throw new BusinessException("供应商分类树结构异常");
            }

            if (!finalNo2CategoryMap.containsKey(tmpNo)) {
                break;
            }

            SupplierCategoryV2 supplierCategoryV2 = finalNo2CategoryMap.get(tmpNo);
            if (null == supplierCategoryV2.getParentNo() || ServiceConstant.TOP_PARENT_NO.equals(supplierCategoryV2.getParentNo())) {
                break;
            }
            pathStack.push(supplierCategoryV2.getParentNo());

            tmpNo = supplierCategoryV2.getParentNo();

            loop++;
        }

        StringBuilder path = new StringBuilder();

        int size = pathStack.size();
        for (int i = 0; i < size; i++) {
            try {
                path.append(pathStack.pop()).append("/");
            } catch (NoSuchElementException e) {
                break;
            }
        }

        return path.deleteCharAt(path.length() - 1).toString();
    }

    @Override
    @Transactional
    public SupplierCategoryVO updateSupplierCategory(OperationModel operationModel, SupplierCategoryUpdateReq supplierCategoryUpdateReq) {
        // 校验必填参数
        ValidatorUtils.checkEmptyThrowEx(operationModel.getEnterpriseNo(), "租户编号不能为空");
        ValidatorUtils.checkEmptyThrowEx(supplierCategoryUpdateReq.getManageOrgNo(), "管理组织不能为空");
        ValidatorUtils.checkEmptyThrowEx(supplierCategoryUpdateReq.getNo(), "供应商分类编号不能为空");

        //校验参数合法性
        if (supplierCategoryUpdateReq.getSortNum() != null) {
            ValidatorUtils.checkTrueThrowEx(supplierCategoryUpdateReq.getSortNum() < 1, "排序不能小于1");
            ValidatorUtils.checkTrueThrowEx(supplierCategoryUpdateReq.getSortNum() > 127, "排序不能大于127");
        }
        if (StringUtils.isNotEmpty(supplierCategoryUpdateReq.getMnemonicCode())) {
            ValidatorUtils.checkTrueThrowEx(supplierCategoryUpdateReq.getMnemonicCode().length() > 50, "助记码长度不能超过50");
        }
        if (StringUtils.isNotEmpty(supplierCategoryUpdateReq.getRemark())) {
            ValidatorUtils.checkTrueThrowEx(supplierCategoryUpdateReq.getRemark().length() > 300, "描述长度不能超过300");
        }

        SupplierCategoryV2 dbSupplierCategoryV2 = supplierCategoryV2DAO.selectOne(new LambdaQueryWrapper<SupplierCategoryV2>()
                .eq(SupplierCategoryV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(SupplierCategoryV2::getManageOrgNo, supplierCategoryUpdateReq.getManageOrgNo())
                .eq(SupplierCategoryV2::getNo, supplierCategoryUpdateReq.getNo())
                .eq(SupplierCategoryV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
        ValidatorUtils.checkEmptyThrowEx(dbSupplierCategoryV2, "供应商分类不存在");

        String parentNo = null;


        if (ServiceConstant.TOP_PARENT_NO.equals(supplierCategoryUpdateReq.getParentNo()) || StringUtils.isBlank(supplierCategoryUpdateReq.getParentNo())) {
            parentNo = ServiceConstant.TOP_PARENT_NO;
        } else {
            parentNo = supplierCategoryUpdateReq.getParentNo();
        }

        SupplierCategoryV2 parentSupplierCategoryV2 = null;
        Map<Integer, List<SupplierCategoryV2>> lv2Node = null;

        // 提前构造树，列表存放了所有的分类节点记录
        List<SupplierCategoryV2> finalSupplierCategoryV2List = supplierCategoryV2DAO.selectList(new LambdaQueryWrapper<SupplierCategoryV2>()
                .eq(SupplierCategoryV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(SupplierCategoryV2::getManageOrgNo, supplierCategoryUpdateReq.getManageOrgNo())
                .eq(SupplierCategoryV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
        for (SupplierCategoryV2 supplierCategoryV2 : finalSupplierCategoryV2List) {
            if (supplierCategoryV2.getNo().equals(supplierCategoryUpdateReq.getNo())) {
                supplierCategoryV2.setParentNo(parentNo);
                break;
            }
        }
        Map<String, SupplierCategoryV2> finalNo2CategoryMap = finalSupplierCategoryV2List.stream().collect(Collectors.toMap(SupplierCategoryV2::getNo, supplierCategoryV2 -> supplierCategoryV2));

        if (!ServiceConstant.TOP_PARENT_NO.equals(parentNo)) {
            // 判断是否成环
            ValidatorUtils.checkTrueThrowEx(hasCircularDependency(finalSupplierCategoryV2List), "供应商分类存在循环依赖");

            parentSupplierCategoryV2 = supplierCategoryV2DAO.selectOne(new LambdaQueryWrapper<SupplierCategoryV2>()
                    .eq(SupplierCategoryV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                    .eq(SupplierCategoryV2::getNo, parentNo)
                    .eq(SupplierCategoryV2::getDeleted, DeletedEnum.UN_DELETE.getValue())
            );
            ValidatorUtils.checkEmptyThrowEx(parentSupplierCategoryV2, "上级供应商分类不存在");
            ValidatorUtils.checkTrueThrowEx(parentSupplierCategoryV2.getLevel() >= 5, "上级分类不能选择第五级的节点");

            //获取当前节点的所有子孙节点
            List<SupplierCategoryV2> allDescendants = new ArrayList<>();
            recursiveGetDescendantSupplierCategoryV2s(operationModel.getEnterpriseNo(), supplierCategoryUpdateReq.getManageOrgNo(), Collections.singletonList(dbSupplierCategoryV2.getNo()), allDescendants, 0);
            if (CollectionUtils.isNotEmpty(allDescendants)) {
                lv2Node = allDescendants.stream().collect(Collectors.groupingBy(SupplierCategoryV2::getLevel));

                ValidatorUtils.checkTrueThrowEx((parentSupplierCategoryV2.getLevel() + 1 + lv2Node.size()) > 5, "分类不能超过五级");
            }

            boolean isInvalid = CommonStatusEnum.INVALID.getValue().toString().equals(parentSupplierCategoryV2.getStatus())
                    && CommonStatusEnum.EFFECTIVE.getValue().toString().equals(supplierCategoryUpdateReq.getStatus());
            ValidatorUtils.checkTrueThrowEx(isInvalid, "上级供应商分类已停用，不允许启用");

            //上级节点是否被引用
            boolean supplierRef = supplierV2Service.checkQuoteCategory(operationModel, parentNo);
            ValidatorUtils.checkTrueThrowEx(supplierRef, "上级供应商分类已被供应商引用");

            // 上级节点是否被分配
            boolean hasAssign = supplierCategoryBaseDAO.exists(new LambdaQueryWrapper<SupplierCategoryBase>()
                    .eq(SupplierCategoryBase::getEnterpriseNo, operationModel.getEnterpriseNo())
                    .eq(SupplierCategoryBase::getCategoryNo, parentNo)
                    .eq(SupplierCategoryBase::getManageOrgNo, supplierCategoryUpdateReq.getManageOrgNo())
                    .ne(SupplierCategoryBase::getUseOrgNo, supplierCategoryUpdateReq.getManageOrgNo())
                    .eq(SupplierCategoryBase::getDeleted, DeletedEnum.UN_DELETE.getValue()));
            ValidatorUtils.checkTrueThrowEx(hasAssign, "上级供应商分类已被分配");
        }

        //唯一性校验
        ValidatorUtils.checkTrueThrowEx(!this.checkUniqueName(operationModel, supplierCategoryUpdateReq.getNo(), supplierCategoryUpdateReq.getCategoryName(), parentNo), "同一层级下已存在供应商分类名称");
        ValidatorUtils.checkTrueThrowEx(!this.checkUniqueCode(operationModel, supplierCategoryUpdateReq.getNo(), supplierCategoryUpdateReq.getCategoryCode()), "企业已存在供应商分类编码");


        String enterpriseNo = operationModel.getEnterpriseNo();
        String supplierCategoryNo = supplierCategoryUpdateReq.getNo();

        // 更新供应商分类信息
        SupplierCategoryV2 newSupplierCategoryV2 = new SupplierCategoryV2();
        newSupplierCategoryV2.setParentNo(parentNo);
        newSupplierCategoryV2.setMnemonicCode(supplierCategoryUpdateReq.getMnemonicCode());
        newSupplierCategoryV2.setCategoryCode(supplierCategoryUpdateReq.getCategoryCode());
        newSupplierCategoryV2.setCategoryName(supplierCategoryUpdateReq.getCategoryName());
        newSupplierCategoryV2.setRemark(supplierCategoryUpdateReq.getRemark());
        newSupplierCategoryV2.setStatus(supplierCategoryUpdateReq.getStatus());
        newSupplierCategoryV2.setSortNum(supplierCategoryUpdateReq.getSortNum());

        if (ServiceConstant.TOP_PARENT_NO.equals(parentNo)) {
            newSupplierCategoryV2.setLevel(1);
            newSupplierCategoryV2.setTopCategoryNo(supplierCategoryNo);
            newSupplierCategoryV2.setPath(supplierCategoryNo);
        } else {
            newSupplierCategoryV2.setLevel(parentSupplierCategoryV2.getLevel() + 1);
            newSupplierCategoryV2.setTopCategoryNo(parentSupplierCategoryV2.getTopCategoryNo());
            newSupplierCategoryV2.setPath(getPath(finalNo2CategoryMap, supplierCategoryUpdateReq.getNo()));
        }

        CommonUtil.fillOperateInfo(operationModel, newSupplierCategoryV2);

        LambdaUpdateWrapper<SupplierCategoryV2> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.eq(SupplierCategoryV2::getEnterpriseNo, enterpriseNo)
                .eq(SupplierCategoryV2::getManageOrgNo, supplierCategoryUpdateReq.getManageOrgNo())
                .eq(SupplierCategoryV2::getNo, supplierCategoryNo);
        supplierCategoryV2DAO.update(newSupplierCategoryV2, updateWrapper);

        //更新所有子孙节点的level
        if (null != lv2Node) {
            List<Integer> lvs = new ArrayList<>(lv2Node.keySet());
            lvs.sort(Comparator.comparingInt(o -> o));
            Integer level = newSupplierCategoryV2.getLevel();

            int nextlv = level + 1;
            for (Integer lv : lvs) {
                List<SupplierCategoryV2> supplierCategoryV2s = lv2Node.get(lv);
                for (SupplierCategoryV2 supplierCategoryV2 : supplierCategoryV2s) {
                    SupplierCategoryV2 updateSupplierCategoryV2 = new SupplierCategoryV2();
                    updateSupplierCategoryV2.setLevel(nextlv);
                    updateSupplierCategoryV2.setTopCategoryNo(newSupplierCategoryV2.getTopCategoryNo());
                    updateSupplierCategoryV2.setPath(getPath(finalNo2CategoryMap, supplierCategoryV2.getNo()));
                    supplierCategoryV2DAO.update(updateSupplierCategoryV2, Wrappers.<SupplierCategoryV2>lambdaUpdate()
                            .eq(SupplierCategoryV2::getEnterpriseNo, enterpriseNo)
                            .eq(SupplierCategoryV2::getManageOrgNo, supplierCategoryUpdateReq.getManageOrgNo())
                            .eq(SupplierCategoryV2::getNo, supplierCategoryV2.getNo()));
                }

                nextlv++;
            }
        }

        // 移除非叶子节点的分派记录
        // 补全叶子节点的分派记录
        List<SupplierCategoryBase> supplierCategoryBases = supplierCategoryBaseDAO.selectList(Wrappers.<SupplierCategoryBase>lambdaUpdate()
                .eq(SupplierCategoryBase::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(SupplierCategoryBase::getManageOrgNo, supplierCategoryUpdateReq.getManageOrgNo())
                .eq(SupplierCategoryBase::getDeleted, DeletedEnum.UN_DELETE.getValue()));
        Map<String, List<SupplierCategoryBase>> no2BasesMap = supplierCategoryBases.stream().collect(Collectors.groupingBy(SupplierCategoryBase::getCategoryNo));

        Map<String, SupplierCategoryV2> leafNo2Category = findAllLeavis(finalSupplierCategoryV2List).stream().collect(Collectors.toMap(SupplierCategoryV2::getNo, Function.identity()));

        List<String> toDeleteBaseCategoryNoList = new ArrayList<>();
        List<SupplierCategoryV2> toAddCategoryList = new ArrayList<>();

        no2BasesMap.forEach((no, bases) -> {
            if (!leafNo2Category.containsKey(no)) {
                toDeleteBaseCategoryNoList.add(no);
            }
        });

        if (CollectionUtils.isNotEmpty(toDeleteBaseCategoryNoList)) {
            SupplierCategoryBase updateSupplierCategoryBase = new SupplierCategoryBase();
            updateSupplierCategoryBase.setDeleted(DeletedEnum.DELETED.getValue());
            supplierCategoryBaseDAO.update(updateSupplierCategoryBase, Wrappers.<SupplierCategoryBase>lambdaUpdate()
                    .eq(SupplierCategoryBase::getEnterpriseNo, operationModel.getEnterpriseNo())
                    .eq(SupplierCategoryBase::getManageOrgNo, supplierCategoryUpdateReq.getManageOrgNo())
                    .eq(SupplierCategoryBase::getDeleted, DeletedEnum.UN_DELETE.getValue())
                    .in(SupplierCategoryBase::getCategoryNo, toDeleteBaseCategoryNoList));
        }

        leafNo2Category.forEach((no, category) -> {
            if (!no2BasesMap.containsKey(no)) {
                toAddCategoryList.add(category);
            }
        });

        for (SupplierCategoryV2 supplierCategoryV2 : toAddCategoryList) {
            SupplierCategoryBase insertSupplierCategoryBase = new SupplierCategoryBase();
            insertSupplierCategoryBase.setEnterpriseNo(operationModel.getEnterpriseNo());
            insertSupplierCategoryBase.setCategoryNo(supplierCategoryV2.getNo());
            insertSupplierCategoryBase.setCategoryCode(supplierCategoryV2.getCategoryCode());
            insertSupplierCategoryBase.setManageOrgNo(supplierCategoryV2.getManageOrgNo());
            insertSupplierCategoryBase.setUseOrgNo(supplierCategoryV2.getManageOrgNo());
            insertSupplierCategoryBase.setDeleted(DeletedEnum.UN_DELETE.getValue());
            CommonUtil.fillCreatInfo(operationModel, insertSupplierCategoryBase);
            supplierCategoryBaseDAO.insert(insertSupplierCategoryBase);
        }

        log.warn("toDeleteBaseCategoryNoList={}", toDeleteBaseCategoryNoList);
        log.warn("toAddCategoryList={}", toAddCategoryList);

        // 如果移动的是叶子节点，需要删除被ui删掉的使用组织
        if (leafNo2Category.containsKey(supplierCategoryUpdateReq.getNo())) {
            Set<String> orgNoSet = null;
            if (CollectionUtils.isNotEmpty(supplierCategoryUpdateReq.getUseOrgList())) {
                orgNoSet = supplierCategoryUpdateReq.getUseOrgList().stream().map(SupplierCategoryUseOrgReq::getUseOrgNo).collect(Collectors.toSet());
            } else {
                orgNoSet = new HashSet<>();
            }

            log.warn("orgNoSet={}", orgNoSet);

            List<SupplierCategoryBase> dbSupplierCategoryBases = supplierCategoryBaseDAO.selectList(Wrappers.<SupplierCategoryBase>lambdaQuery()
                    .eq(SupplierCategoryBase::getEnterpriseNo, operationModel.getEnterpriseNo())
                    .eq(SupplierCategoryBase::getManageOrgNo, supplierCategoryUpdateReq.getManageOrgNo())
                    .eq(SupplierCategoryBase::getCategoryNo, supplierCategoryUpdateReq.getNo())
                    .eq(SupplierCategoryBase::getDeleted, DeletedEnum.UN_DELETE.getValue()));

            log.warn("dbSupplierCategoryBaseV2s={}", dbSupplierCategoryBases);

            List<String> deleteOrgNoList = new ArrayList<>();
            for (SupplierCategoryBase dbSupplierCategoryBase : dbSupplierCategoryBases) {
                if (supplierCategoryUpdateReq.getManageOrgNo().equals(dbSupplierCategoryBase.getUseOrgNo())) {
                    continue;
                }
                if (!orgNoSet.contains(dbSupplierCategoryBase.getUseOrgNo())) {
                    deleteOrgNoList.add(dbSupplierCategoryBase.getUseOrgNo());
                }
            }

            log.warn("deleteOrgNoList={}", deleteOrgNoList);

            if (CollectionUtils.isNotEmpty(deleteOrgNoList)) {
                // 分派列表中如果移除已经使用过的组织，需拒绝
                // 管理组织G1创建了供应商分类F1，并把供应商分类F1分派给使用组织U1，当U1创建了供应商档案并引用了F1，此时管理组织G1去修改供应商分类F1的使用范围时，不能移除U1。
                List<SupplierCategoryBase> myAssignList = supplierCategoryBaseDAO.selectList(Wrappers.<SupplierCategoryBase>lambdaQuery()
                        .eq(SupplierCategoryBase::getEnterpriseNo, operationModel.getEnterpriseNo())
                        .eq(SupplierCategoryBase::getManageOrgNo, supplierCategoryUpdateReq.getManageOrgNo())
                        .ne(SupplierCategoryBase::getUseOrgNo, supplierCategoryUpdateReq.getManageOrgNo())
                        .eq(SupplierCategoryBase::getDeleted, DeletedEnum.UN_DELETE.getValue())
                        .eq(SupplierCategoryBase::getCategoryNo, supplierCategoryUpdateReq.getNo()));
                if (CollectionUtils.isNotEmpty(myAssignList)) {
                    List<SupplierV2> supplierV2s = supplierV2Service.queryByManageOrgNoList(operationModel, supplierCategoryUpdateReq.getNo(), myAssignList.stream().map(SupplierCategoryBase::getUseOrgNo).collect(Collectors.toList()));
                    if (CollectionUtils.isNotEmpty(supplierV2s)) {
                        Map<String, List<SupplierV2>> orgNo2SupplierV2Map = supplierV2s.stream().collect(Collectors.groupingBy(SupplierV2::getManageOrgNo));
                        List<String> orgRefedList = new ArrayList<>();
                        for (String useOrgNo : deleteOrgNoList) {
                            if (useOrgNo.equals(supplierCategoryUpdateReq.getManageOrgNo())) {
                                continue;
                            }

                            if (orgNo2SupplierV2Map.containsKey(useOrgNo)) {
                                List<SupplierV2> supplierV2List = orgNo2SupplierV2Map.get(useOrgNo);
                                if (CollectionUtils.isNotEmpty(supplierV2List)) {
//                                    orgNameList.addAll(supplierV2List.stream().map(SupplierV2::getSupplierName).collect(Collectors.toSet()));

                                    orgRefedList.add(useOrgNo);
                                }
                            }
                        }

                        if (CollectionUtils.isNotEmpty(orgRefedList)) {
                            List<OrganizationVo> listNoAuth = organizationService.findListNoAuth(operationModel.getEnterpriseNo(), orgRefedList);
                            if (CollectionUtils.isNotEmpty(listNoAuth)) {
                                throw new BusinessException("使用组织【" + listNoAuth.stream().map(OrganizationVo::getOrgName).collect(Collectors.joining(",")) + "】已引用当前分类，不允许删除。");
                            }
                        }
                    }
                }

                TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                    @Override
                    public void afterCommit() {
                        try {
                            log.warn("executeCancelAssignDocReq={},{},{},{}", operationModel.getEnterpriseNo(), ViewNameConstant.BDC_SUPPLIER_CATEGORY_VIEW, supplierCategoryUpdateReq.getNo(), deleteOrgNoList);

                            Map<String, GradeCancelAssignRes> executeCancelAssignDoc = gradeControlService.executeCancelAssignDoc(operationModel.getEnterpriseNo(), BillNameConstant.BDC_SUPPLIER_CATEGORY_BILL, supplierCategoryUpdateReq.getNo(), deleteOrgNoList);

                            log.warn("executeCancelAssignDocResp={}", executeCancelAssignDoc);
                        } catch (Exception e) {
                            log.error("取消分派异常", e);
                        }
                    }
                });
            }
        }

        Map<String, Object> businessValue = new HashMap<>();
        businessValue.put("newSupplierCategory", newSupplierCategoryV2);
        businessValue.put("useOrgNoList", supplierCategoryUpdateReq.getUseOrgList());

        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                try {
                    businessLogService.saveLog(DLogLevel.INFO.name(), ViewNameConstant.BDC_SUPPLIER_CATEGORY_VIEW, supplierCategoryNo, "更新供应商分类", "更新供应商分类", JSON.toJSONString(businessValue), "");
                } catch (Exception e) {
                    log.error("编辑供应商分类日志保存失败", e);
                }
            }
        });

        return packNewSupplierCategoryVO(dbSupplierCategoryV2, newSupplierCategoryV2);
    }

    //根据SupplierCategoryV2的no和parentNo查询出所有的叶子并返回
    private List<SupplierCategoryV2> findAllLeavis(List<SupplierCategoryV2> supplierCategoryList) {
        if (CollectionUtils.isEmpty(supplierCategoryList)) {
            return Collections.emptyList();
        }

        Set<String> parentNoSet = supplierCategoryList.stream().map(SupplierCategoryV2::getParentNo)
                .collect(Collectors.toSet());

        // 存储所有叶子节点
        List<SupplierCategoryV2> leaves = new ArrayList<>();

        // 遍历所有节点,找出叶子节点(没有子节点的节点)
        for (SupplierCategoryV2 category : supplierCategoryList) {
            String categoryNo = category.getNo();
            if (!parentNoSet.contains(categoryNo)) {
                leaves.add(category);
            }
        }

        return leaves;
    }

    /**
     * 判断供应商分类列表是否存在环形依赖
     *
     * @param supplierCategoryList 供应商分类列表
     * @return true表示存在环, false表示不存在环
     */
    private boolean hasCircularDependency(List<SupplierCategoryV2> supplierCategoryList) {
        if (CollectionUtils.isEmpty(supplierCategoryList)) {
            return false;
        }

        // 构建分类编号到分类对象的映射
        Map<String, SupplierCategoryV2> categoryMap = supplierCategoryList.stream()
                .collect(Collectors.toMap(SupplierCategoryV2::getNo, Function.identity()));

        // 记录已访问的节点
        Set<String> visited = new HashSet<>();
        // 记录当前路径上的节点
        Set<String> path = new HashSet<>();

        // 对每个节点进行DFS遍历
        for (SupplierCategoryV2 category : supplierCategoryList) {
            if (dfs(category.getNo(), categoryMap, visited, path)) {
                return true;
            }
        }
        return false;
    }

    /**
     * DFS遍历检查是否存在环
     */
    private boolean dfs(String categoryNo, Map<String, SupplierCategoryV2> categoryMap,
                        Set<String> visited, Set<String> path) {
        // 当前节点已在路径中,说明成环
        if (path.contains(categoryNo)) {
            return true;
        }

        // 已访问过的节点不需要重复访问
        if (visited.contains(categoryNo)) {
            return false;
        }

        SupplierCategoryV2 category = categoryMap.get(categoryNo);
        if (category == null) {
            return false;
        }

        visited.add(categoryNo);
        path.add(categoryNo);

        // 递归检查父节点
        if (StringUtils.isNotBlank(category.getParentNo())) {
            if (dfs(category.getParentNo(), categoryMap, visited, path)) {
                return true;
            }
        }

        path.remove(categoryNo);
        return false;
    }

    private SupplierCategoryVO packNewSupplierCategoryVO(SupplierCategoryV2 dbSupplierCategoryV2, SupplierCategoryV2 newSupplierCategoryV2) {
        dbSupplierCategoryV2.setParentNo(newSupplierCategoryV2.getParentNo());
        dbSupplierCategoryV2.setMnemonicCode(newSupplierCategoryV2.getMnemonicCode());
        dbSupplierCategoryV2.setCategoryCode(newSupplierCategoryV2.getCategoryCode());
        dbSupplierCategoryV2.setCategoryName(newSupplierCategoryV2.getCategoryName());
        dbSupplierCategoryV2.setLevel(newSupplierCategoryV2.getLevel());
        dbSupplierCategoryV2.setTopCategoryNo(newSupplierCategoryV2.getTopCategoryNo());
        dbSupplierCategoryV2.setPath(newSupplierCategoryV2.getPath());
        dbSupplierCategoryV2.setRemark(newSupplierCategoryV2.getRemark());
        dbSupplierCategoryV2.setStatus(newSupplierCategoryV2.getStatus());
        dbSupplierCategoryV2.setSortNum(newSupplierCategoryV2.getSortNum());
        dbSupplierCategoryV2.setOperateNo(newSupplierCategoryV2.getOperateNo());
        dbSupplierCategoryV2.setOperateName(newSupplierCategoryV2.getOperateName());
        dbSupplierCategoryV2.setOperateTime(newSupplierCategoryV2.getOperateTime());

        SupplierCategoryVO supplierCategoryVO = new SupplierCategoryVO();
        BeanUtils.copyProperties(dbSupplierCategoryV2, supplierCategoryVO);

        return supplierCategoryVO;
    }

    @Override
    @Transactional
    public Boolean deleteSupplierCategory(OperationModel operationModel, SupplierCategoryDeleteReq supplierCategoryDeleteReq) {
        // 校验必填参数
        ValidatorUtils.checkEmptyThrowEx(operationModel.getEnterpriseNo(), "租户编号不能为空");
        ValidatorUtils.checkEmptyThrowEx(supplierCategoryDeleteReq.getManageOrgNo(), "管理组织不能为空");
        ValidatorUtils.checkEmptyThrowEx(supplierCategoryDeleteReq.getNo(), "供应商分类编号不能为空");

        SupplierCategoryV2 dbSupplierCategoryV2 = supplierCategoryV2DAO.selectOne(new LambdaQueryWrapper<SupplierCategoryV2>()
                .eq(SupplierCategoryV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(SupplierCategoryV2::getManageOrgNo, supplierCategoryDeleteReq.getManageOrgNo())
                .eq(SupplierCategoryV2::getNo, supplierCategoryDeleteReq.getNo())
                .eq(SupplierCategoryV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
        ValidatorUtils.checkEmptyThrowEx(dbSupplierCategoryV2, "供应商分类不存在");

        boolean hasDescendants = supplierCategoryV2DAO.exists(new LambdaQueryWrapper<SupplierCategoryV2>()
                .eq(SupplierCategoryV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(SupplierCategoryV2::getManageOrgNo, supplierCategoryDeleteReq.getManageOrgNo())
                .eq(SupplierCategoryV2::getParentNo, supplierCategoryDeleteReq.getNo())
                .eq(SupplierCategoryV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
        ValidatorUtils.checkTrueThrowEx(hasDescendants, "存在子供应商分类，不能删除");

//        boolean hasAssign = supplierCategoryBaseV2DAO.exists(new LambdaQueryWrapper<SupplierCategoryBaseV2>()
//                .eq(SupplierCategoryBaseV2::getEnterpriseNo, operationModel.getEnterpriseNo())
//                .eq(SupplierCategoryBaseV2::getCategoryNo, supplierCategoryDeleteReq.getNo())
//                .eq(SupplierCategoryBaseV2::getManageOrgNo, supplierCategoryDeleteReq.getManageOrgNo())
//                .ne(SupplierCategoryBaseV2::getUseOrgNo, supplierCategoryDeleteReq.getManageOrgNo())
//                .eq(SupplierCategoryBaseV2::getDeleted, DeletedEnum.UN_DELETE.getValue())
//        );
//        ValidatorUtils.checkTrueThrowEx(hasAssign, "供应商分类已被分配");

        //本节点是否被引用
        boolean supplierRef = supplierV2Service.checkQuoteCategory(operationModel, supplierCategoryDeleteReq.getNo());
        ValidatorUtils.checkTrueThrowEx(supplierRef, "供应商分类已被供应商引用");

        String enterpriseNo = operationModel.getEnterpriseNo();
        String supplierCategoryNo = supplierCategoryDeleteReq.getNo();

        // 删除供应商分类信息
        SupplierCategoryV2 newSupplierCategoryV2 = new SupplierCategoryV2();
//        newSupplierCategoryV2.setEnterpriseNo(enterpriseNo);
        newSupplierCategoryV2.setNo(supplierCategoryNo);
        newSupplierCategoryV2.setDeleted(DeletedEnum.DELETED.getValue());
        CommonUtil.fillOperateInfo(operationModel, newSupplierCategoryV2);

        LambdaUpdateWrapper<SupplierCategoryV2> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.eq(SupplierCategoryV2::getEnterpriseNo, enterpriseNo)
                .eq(SupplierCategoryV2::getManageOrgNo, supplierCategoryDeleteReq.getManageOrgNo())
                .eq(SupplierCategoryV2::getNo, supplierCategoryNo);
        supplierCategoryV2DAO.update(newSupplierCategoryV2, updateWrapper);

        List<SupplierCategoryBase> supplierCategoryBases = supplierCategoryBaseDAO.selectList(new LambdaQueryWrapper<SupplierCategoryBase>()
                .eq(SupplierCategoryBase::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(SupplierCategoryBase::getManageOrgNo, supplierCategoryDeleteReq.getManageOrgNo())
                .eq(SupplierCategoryBase::getCategoryNo, supplierCategoryDeleteReq.getNo())
                .eq(SupplierCategoryBase::getDeleted, DeletedEnum.UN_DELETE.getValue()));

        if (CollectionUtils.isNotEmpty(supplierCategoryBases)) {
            for (SupplierCategoryBase supplierCategoryBase : supplierCategoryBases) {
                SupplierCategoryBase updateSupplierCategoryBase = new SupplierCategoryBase();
                updateSupplierCategoryBase.setDeleted(DeletedEnum.DELETED.getValue());
                supplierCategoryBaseDAO.update(updateSupplierCategoryBase, Wrappers.<SupplierCategoryBase>lambdaUpdate()
                        .eq(SupplierCategoryBase::getId, supplierCategoryBase.getId()));
            }
        }


        // 非第一级节点，并且上级节点没有子节点，则需要补充上级节点的分派记录
        if (!ServiceConstant.TOP_PARENT_NO.equals(dbSupplierCategoryV2.getParentNo())) {
            boolean parentNodeHasDescendants = supplierCategoryV2DAO.exists(new LambdaQueryWrapper<SupplierCategoryV2>()
                    .eq(SupplierCategoryV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                    .eq(SupplierCategoryV2::getManageOrgNo, supplierCategoryDeleteReq.getManageOrgNo())
                    .eq(SupplierCategoryV2::getParentNo, dbSupplierCategoryV2.getParentNo())
                    .eq(SupplierCategoryV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
            if (!parentNodeHasDescendants) {
                SupplierCategoryV2 parentSupplierCategoryV2 = supplierCategoryV2DAO.selectOne(new LambdaQueryWrapper<SupplierCategoryV2>()
                        .eq(SupplierCategoryV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                        .eq(SupplierCategoryV2::getManageOrgNo, supplierCategoryDeleteReq.getManageOrgNo())
                        .eq(SupplierCategoryV2::getNo, dbSupplierCategoryV2.getParentNo())
                        .eq(SupplierCategoryV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));

                SupplierCategoryBase supplierCategoryBase = new SupplierCategoryBase();
                supplierCategoryBase.setEnterpriseNo(operationModel.getEnterpriseNo());
                supplierCategoryBase.setManageOrgNo(supplierCategoryDeleteReq.getManageOrgNo());
                supplierCategoryBase.setUseOrgNo(supplierCategoryDeleteReq.getManageOrgNo());
                supplierCategoryBase.setCategoryNo(parentSupplierCategoryV2.getNo());
                supplierCategoryBase.setCategoryCode(parentSupplierCategoryV2.getCategoryCode());
                supplierCategoryBase.setDeleted(DeletedEnum.UN_DELETE.getValue());
                CommonUtil.fillCreatInfo(operationModel, supplierCategoryBase);
                CommonUtil.fillOperateInfo(operationModel, supplierCategoryBase);
                supplierCategoryBaseDAO.insert(supplierCategoryBase);
            }
        }

        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                try {
                    businessLogService.saveLog(DLogLevel.INFO.name(), ViewNameConstant.BDC_SUPPLIER_CATEGORY_VIEW, supplierCategoryNo, "删除供应商分类", "删除供应商分类", JSON.toJSONString(newSupplierCategoryV2), "");
                } catch (Exception e) {
                    log.error("删除供应商分类日志保存失败", e);
                }
            }
        });

        return true;
    }

    @Override
    @Transactional
    public Boolean changeSupplierCategoryStatus(OperationModel operationModel, SupplierCategoryChangeStatusReq supplierCategoryChangeStatusReq) {
        // 校验必填参数
        ValidatorUtils.checkEmptyThrowEx(operationModel.getEnterpriseNo(), "租户编号不能为空");
        ValidatorUtils.checkEmptyThrowEx(supplierCategoryChangeStatusReq.getManageOrgNo(), "管理组织不能为空");
        ValidatorUtils.checkEmptyThrowEx(supplierCategoryChangeStatusReq.getNo(), "供应商分类编号不能为空");

        SupplierCategoryV2 dbSupplierCategoryV2 = supplierCategoryV2DAO.selectOne(new LambdaQueryWrapper<SupplierCategoryV2>()
                .eq(SupplierCategoryV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(SupplierCategoryV2::getManageOrgNo, supplierCategoryChangeStatusReq.getManageOrgNo())
                .eq(SupplierCategoryV2::getNo, supplierCategoryChangeStatusReq.getNo())
                .eq(SupplierCategoryV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
        ValidatorUtils.checkEmptyThrowEx(dbSupplierCategoryV2, "供应商分类不存在");

        SupplierCategoryV2 dbParentSupplierCategoryV2 = null;
        if (!ServiceConstant.TOP_PARENT_NO.equals(dbSupplierCategoryV2.getParentNo())) {
            dbParentSupplierCategoryV2 = supplierCategoryV2DAO.selectOne(new LambdaQueryWrapper<SupplierCategoryV2>()
                    .eq(SupplierCategoryV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                    .eq(SupplierCategoryV2::getManageOrgNo, supplierCategoryChangeStatusReq.getManageOrgNo())
                    .eq(SupplierCategoryV2::getNo, dbSupplierCategoryV2.getParentNo())
                    .eq(SupplierCategoryV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
            ValidatorUtils.checkEmptyThrowEx(dbParentSupplierCategoryV2, "上级供应商分类不存在");
        }


        String enterpriseNo = operationModel.getEnterpriseNo();
        String supplierCategoryNo = supplierCategoryChangeStatusReq.getNo();
        String manageOrgNo = supplierCategoryChangeStatusReq.getManageOrgNo();

        if (CommonStatusEnum.EFFECTIVE.getValue().toString().equals(supplierCategoryChangeStatusReq.getStatus())) {// 启用分类
            if (null != dbParentSupplierCategoryV2) {
                ValidatorUtils.checkTrueThrowEx(CommonStatusEnum.INVALID.getValue().toString().equals(dbParentSupplierCategoryV2.getStatus()), "上级供应商分类已停用");
            }

            SupplierCategoryV2 newSupplierCategoryV2 = new SupplierCategoryV2();
//            newSupplierCategoryV2.setEnterpriseNo(enterpriseNo);
//            newSupplierCategoryV2.setManageOrgNo(manageOrgNo);
//            newSupplierCategoryV2.setNo(supplierCategoryNo);
            newSupplierCategoryV2.setStatus(CommonStatusEnum.EFFECTIVE.getValue().toString());
            CommonUtil.fillOperateInfo(operationModel, newSupplierCategoryV2);
            LambdaUpdateWrapper<SupplierCategoryV2> updateWrapper = Wrappers.lambdaUpdate();
            updateWrapper.eq(SupplierCategoryV2::getEnterpriseNo, enterpriseNo)
                    .eq(SupplierCategoryV2::getManageOrgNo, manageOrgNo)
                    .eq(SupplierCategoryV2::getNo, supplierCategoryNo);
            supplierCategoryV2DAO.update(newSupplierCategoryV2, updateWrapper);

            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                @Override
                public void afterCommit() {
                    try {
                        businessLogService.saveLog(DLogLevel.INFO.name(), ViewNameConstant.BDC_SUPPLIER_CATEGORY_VIEW, supplierCategoryNo, "启用供应商分类", "启用供应商分类", JSON.toJSONString(newSupplierCategoryV2), "");
                    } catch (Exception e) {
                        log.error("启用供应商分类日志保存失败", e);
                    }
                }
            });
        } else { // 停用分类
            List<String> allNos = new ArrayList<>();
            allNos.add(dbSupplierCategoryV2.getNo());

            recursiveGetDescendants(enterpriseNo, manageOrgNo, Collections.singletonList(dbSupplierCategoryV2.getNo()), allNos, 0);

            List<SupplierCategoryV2> newSupplierCategoryV2List = new ArrayList<>(allNos.size());
            for (String no : allNos) {
                SupplierCategoryV2 newSupplierCategoryV2 = new SupplierCategoryV2();
                newSupplierCategoryV2.setEnterpriseNo(enterpriseNo);
                newSupplierCategoryV2.setManageOrgNo(manageOrgNo);
                newSupplierCategoryV2.setNo(no);
                newSupplierCategoryV2.setStatus(CommonStatusEnum.INVALID.getValue().toString());
                CommonUtil.fillOperateInfo(operationModel, newSupplierCategoryV2);

                newSupplierCategoryV2List.add(newSupplierCategoryV2);
            }
            supplierCategoryV2DAO.batchStatusUpdate(newSupplierCategoryV2List);

            for (SupplierCategoryV2 supplierCategoryV2 : newSupplierCategoryV2List) {
                TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                    @Override
                    public void afterCommit() {
                        try {
                            businessLogService.saveLog(DLogLevel.INFO.name(), ViewNameConstant.BDC_SUPPLIER_CATEGORY_VIEW, supplierCategoryV2.getNo(), "停用供应商分类", "停用供应商分类", JSON.toJSONString(supplierCategoryV2), "");
                        } catch (Exception e) {
                            log.error("停用供应商分类日志保存失败", e);
                        }
                    }
                });
            }
        }

        return true;
    }

    @Override
    public SupplierCategoryV2 getByNo(String enterpriseNo, String no) {
        return supplierCategoryV2DAO.getByNo(enterpriseNo, no);
    }


    @Override
    public List<SupplierCategoryV2> getByNoList(String enterpriseNo, List<String> noList) {
        return supplierCategoryV2DAO.getByNoList(enterpriseNo, noList);
    }

    private SupplierCategoryV2 createAndUpdateCategoryPerRecordForDA(OperationModel operationModel, int lv, SupplierCategoryV2 preCurrentCategory, SupplierExternalSaveDTO supplierExternalSaveDTO, List<SupplierCategoryV2> supplierCategoryList) {
        if (!(lv >= 1 && lv <= 5)) {
            throw new BusinessException(ErrorCode.param_invalid_code, "供应商分类层级不正确");
        }

        Optional<SupplierCategoryV2> categoryOptional = null;

        if (1 == lv) {
            Map<String, List<SupplierCategoryV2>> parentNoMap = supplierCategoryList.stream().collect(Collectors.groupingBy(SupplierCategoryV2::getParentNo));
            List<SupplierCategoryV2> topNodeList = parentNoMap.getOrDefault(ServiceConstant.TOP_PARENT_NO, new ArrayList<>());

            categoryOptional = topNodeList.stream()
                    .filter(supplierCategory ->
                            supplierExternalSaveDTO.getSupplierCategoryCode_1().equals(supplierCategory.getCategoryCode())).findAny();
        } else if (2 == lv) {
            categoryOptional = supplierCategoryList.stream().filter(s -> s.getParentNo().equals(preCurrentCategory.getNo())
                    && supplierExternalSaveDTO.getSupplierCategoryCode_2().equals(s.getCategoryCode())).findAny();
        } else if (3 == lv) {
            categoryOptional = supplierCategoryList.stream().filter(s -> s.getParentNo().equals(preCurrentCategory.getNo())
                    && supplierExternalSaveDTO.getSupplierCategoryCode_3().equals(s.getCategoryCode())).findAny();
        } else if (4 == lv) {
            categoryOptional = supplierCategoryList.stream().filter(s -> s.getParentNo().equals(preCurrentCategory.getNo())
                    && supplierExternalSaveDTO.getSupplierCategoryCode_4().equals(s.getCategoryCode())).findAny();
        } else if (5 == lv) {
            categoryOptional = supplierCategoryList.stream().filter(s -> s.getParentNo().equals(preCurrentCategory.getNo())
                    && supplierExternalSaveDTO.getSupplierCategoryCode_5().equals(s.getCategoryCode())).findAny();
        }

        if (categoryOptional.isPresent()) {
            SupplierCategoryV2 category = categoryOptional.get();

            SupplierCategoryV2 newSupplierCategoryV2 = new SupplierCategoryV2();
            if (1 == lv) {
                newSupplierCategoryV2.setCategoryName(supplierExternalSaveDTO.getSupplierCategoryName_1());
            } else if (2 == lv) {
                newSupplierCategoryV2.setCategoryName(supplierExternalSaveDTO.getSupplierCategoryName_2());
            } else if (3 == lv) {
                newSupplierCategoryV2.setCategoryName(supplierExternalSaveDTO.getSupplierCategoryName_3());
            } else if (4 == lv) {
                newSupplierCategoryV2.setCategoryName(supplierExternalSaveDTO.getSupplierCategoryName_4());
            } else if (5 == lv) {
                newSupplierCategoryV2.setCategoryName(supplierExternalSaveDTO.getSupplierCategoryName_5());
            }
            CommonUtil.fillUpdateInfo(operationModel, newSupplierCategoryV2);

            supplierCategoryV2DAO.update(newSupplierCategoryV2, Wrappers.<SupplierCategoryV2>lambdaUpdate()
                    .eq(SupplierCategoryV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                    .eq(SupplierCategoryV2::getNo, category.getNo()));

            // 回填分类编号
            supplierExternalSaveDTO.setSupplierCategoryNo(category.getNo());

            return category;
        } else {
            //新增逻辑
            SupplierCategoryV2 category = new SupplierCategoryV2();
            category.setParentNo(ServiceConstant.TOP_PARENT_NO);
            category.setNo(numberCenterService.createNumber(NumberCenterConstant.SUPPLY_NO_CATEGORY_KEY_V2));
            category.setEnterpriseNo(operationModel.getEnterpriseNo());
            if (1 == lv) {
                category.setCategoryName(supplierExternalSaveDTO.getSupplierCategoryName_1());
                category.setCategoryCode(supplierExternalSaveDTO.getSupplierCategoryCode_1());
                category.setMnemonicCode(supplierExternalSaveDTO.getSupplierCategoryCode_1());
            } else if (2 == lv) {
                category.setCategoryName(supplierExternalSaveDTO.getSupplierCategoryName_2());
                category.setCategoryCode(supplierExternalSaveDTO.getSupplierCategoryCode_2());
                category.setMnemonicCode(supplierExternalSaveDTO.getSupplierCategoryCode_2());
            } else if (3 == lv) {
                category.setCategoryName(supplierExternalSaveDTO.getSupplierCategoryName_3());
                category.setCategoryCode(supplierExternalSaveDTO.getSupplierCategoryCode_3());
                category.setMnemonicCode(supplierExternalSaveDTO.getSupplierCategoryCode_3());
            } else if (4 == lv) {
                category.setCategoryName(supplierExternalSaveDTO.getSupplierCategoryName_4());
                category.setCategoryCode(supplierExternalSaveDTO.getSupplierCategoryCode_4());
                category.setMnemonicCode(supplierExternalSaveDTO.getSupplierCategoryCode_4());
            } else if (5 == lv) {
                category.setCategoryName(supplierExternalSaveDTO.getSupplierCategoryName_5());
                category.setCategoryCode(supplierExternalSaveDTO.getSupplierCategoryCode_5());
                category.setMnemonicCode(supplierExternalSaveDTO.getSupplierCategoryCode_5());
            }
            category.setStatus(CommonStatusEnum.EFFECTIVE.getValue().toString());
            category.setDeleted(DeletedEnum.UN_DELETE.getValue());
            CommonUtil.fillCreatInfo(operationModel, category);
            supplierCategoryV2DAO.insert(category);

            // 回填分类编号
            supplierExternalSaveDTO.setSupplierCategoryNo(category.getNo());

            supplierCategoryList.add(category);

            return category;
        }
    }

    @Override
    @Transactional
    public void createAndUpdateCategoryForDA(OperationModel operationModel, List<SupplierExternalSaveDTO> params) {
        List<SupplierCategoryV2> supplierCategoryList = supplierCategoryV2DAO.selectList(Wrappers.lambdaQuery(SupplierCategoryV2.class)
                .eq(SupplierCategoryV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(SupplierCategoryV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));


        params.forEach(t -> {
            if (StringUtils.isBlank(t.getSupplierCategoryCode_1())) {
                return;
            }

            SupplierCategoryV2 preSupplierCategory = createAndUpdateCategoryPerRecordForDA(operationModel, 1, null, t, supplierCategoryList);

            if (StringUtils.isNotBlank(t.getSupplierCategoryCode_2())) {
                preSupplierCategory = createAndUpdateCategoryPerRecordForDA(operationModel, 2, preSupplierCategory, t, supplierCategoryList);
            }

            if (StringUtils.isNotBlank(t.getSupplierCategoryCode_3())) {
                preSupplierCategory = createAndUpdateCategoryPerRecordForDA(operationModel, 3, preSupplierCategory, t, supplierCategoryList);
            }

            if (StringUtils.isNotBlank(t.getSupplierCategoryCode_4())) {
                preSupplierCategory = createAndUpdateCategoryPerRecordForDA(operationModel, 4, preSupplierCategory, t, supplierCategoryList);
            }
            if (StringUtils.isNotBlank(t.getSupplierCategoryCode_5())) {
                createAndUpdateCategoryPerRecordForDA(operationModel, 5, preSupplierCategory, t, supplierCategoryList);
            }
        });
    }

    private void recursiveGetDescendantSupplierCategoryV2s(String enterpriseNo, String manageOrgNo, List<String> parentNos, List<SupplierCategoryV2> allDescendants, int depth) {
        if (depth >= MAX_RECURSION_DEPTH) {
            log.error("递归深度超过{}层，停止递归", MAX_RECURSION_DEPTH);
            throw new BusinessException("分类层级异常");
        }

        LambdaQueryWrapper<SupplierCategoryV2> wrapper = Wrappers.<SupplierCategoryV2>lambdaQuery()
                .eq(SupplierCategoryV2::getEnterpriseNo, enterpriseNo)
                .eq(SupplierCategoryV2::getManageOrgNo, manageOrgNo)
                .in(SupplierCategoryV2::getParentNo, parentNos)
                .eq(SupplierCategoryV2::getDeleted, DeletedEnum.UN_DELETE.getValue());
        List<SupplierCategoryV2> supplierCategoryV2s = supplierCategoryV2DAO.selectList(wrapper);

        if (CollectionUtils.isNotEmpty(supplierCategoryV2s)) {
            List<String> parentNosNew = supplierCategoryV2s.stream().map(SupplierCategoryV2::getNo).collect(Collectors.toList());
            allDescendants.addAll(supplierCategoryV2s);

            recursiveGetDescendantSupplierCategoryV2s(enterpriseNo, manageOrgNo, parentNosNew, allDescendants, depth + 1);
        }
    }

    private void recursiveGetDescendants(String enterpriseNo, String manageOrgNo, List<String> parentNos, List<String> allNos, int depth) {
        if (depth >= MAX_RECURSION_DEPTH) {
            log.error("递归深度超过{}层，停止递归", MAX_RECURSION_DEPTH);
            throw new BusinessException("分类层级异常");
        }

        LambdaQueryWrapper<SupplierCategoryV2> wrapper = Wrappers.<SupplierCategoryV2>lambdaQuery()
                .eq(SupplierCategoryV2::getEnterpriseNo, enterpriseNo)
                .eq(SupplierCategoryV2::getManageOrgNo, manageOrgNo)
                .in(SupplierCategoryV2::getParentNo, parentNos)
                .eq(SupplierCategoryV2::getDeleted, DeletedEnum.UN_DELETE.getValue());
        List<SupplierCategoryV2> supplierCategoryV2s = supplierCategoryV2DAO.selectList(wrapper);

        if (CollectionUtils.isNotEmpty(supplierCategoryV2s)) {
            List<String> parentNosNew = supplierCategoryV2s.stream().map(SupplierCategoryV2::getNo).collect(Collectors.toList());
            allNos.addAll(parentNosNew);

            recursiveGetDescendants(enterpriseNo, manageOrgNo, parentNosNew, allNos, depth + 1);
        }
    }

    private void findAllDescendants(List<? extends TreeGrid> treeVoList, List<String> allNos, int depth) {
        if (depth >= MAX_RECURSION_DEPTH) {
            log.error("递归深度超过{}层，停止递归", MAX_RECURSION_DEPTH);
            throw new BusinessException("分类层级异常");
        }

        if (CollectionUtils.isEmpty(treeVoList)) {
            return;
        }
        for (TreeGrid treeGrid : treeVoList) {
            SupplierCategoryTreeVO supplierCategoryTreeVO = (SupplierCategoryTreeVO) treeGrid;
            allNos.add(supplierCategoryTreeVO.getNo());

            if (CollectionUtils.isNotEmpty(supplierCategoryTreeVO.getChildren())) {
                findAllDescendants(supplierCategoryTreeVO.getChildren(), allNos, depth + 1);
            }
        }
    }

    private SupplierCategoryTreeVO findSpecificNode(List<? extends TreeGrid> treeVoList, String parentNo, int depth) {
        if (depth >= MAX_RECURSION_DEPTH) {
            log.error("递归深度超过{}层，停止递归", MAX_RECURSION_DEPTH);
            throw new BusinessException("分类层级异常");
        }

        if (CollectionUtils.isEmpty(treeVoList)) {
            return null;
        }

        for (TreeGrid treeGrid : treeVoList) {
            SupplierCategoryTreeVO supplierCategoryTreeVO = (SupplierCategoryTreeVO) treeGrid;
            if (parentNo.equals(supplierCategoryTreeVO.getNo())) {
                return supplierCategoryTreeVO;
            }

            if (CollectionUtils.isNotEmpty(supplierCategoryTreeVO.getChildren())) {
                SupplierCategoryTreeVO result = findSpecificNode(supplierCategoryTreeVO.getChildren(), parentNo, depth + 1);
                if (result != null) {
                    return result;
                }
            }
        }

        return null;
    }

    private void findNextLevels(List<? extends TreeGrid> treeVoList, String parentCategoryNo, List<String> allNos, int depth) {
        if (depth >= MAX_RECURSION_DEPTH) {
            log.error("递归深度超过{}层，停止递归", MAX_RECURSION_DEPTH);
            throw new BusinessException("分类层级异常");
        }

        if (null == treeVoList) {
            return;
        }

        if (ServiceConstant.TOP_PARENT_NO.equals(parentCategoryNo)) {
            for (TreeGrid treeGrid : treeVoList) {
                SupplierCategoryTreeVO supplierCategoryTreeVO = (SupplierCategoryTreeVO) treeGrid;
                allNos.add(supplierCategoryTreeVO.getNo());
            }
            return;
        }

        for (TreeGrid treeGrid : treeVoList) {
            SupplierCategoryTreeVO supplierCategoryTreeVO = (SupplierCategoryTreeVO) treeGrid;
            if (parentCategoryNo.equals(supplierCategoryTreeVO.getNo())) {
                if (null != supplierCategoryTreeVO.getChildren()) {
                    for (TreeGrid child : supplierCategoryTreeVO.getChildren()) {
                        SupplierCategoryTreeVO node = (SupplierCategoryTreeVO) child;
                        allNos.add(node.getNo());
                    }
                }
                return;
            } else {
                findNextLevels(supplierCategoryTreeVO.getChildren(), parentCategoryNo, allNos, depth + 1);
            }
        }
    }

    //查询所有祖先节点
    private Set<String> findAllAncestors(Map<String, SupplierCategoryV2> nodeMap, String no) {
        Set<String> ancestors = new HashSet<>();
        SupplierCategoryV2 current = nodeMap.get(no);
        while (current != null && !ServiceConstant.TOP_PARENT_NO.equals(current.getParentNo())) {
            ancestors.add(current.getParentNo());
            current = nodeMap.get(current.getParentNo());
        }
        return ancestors;
    }

    // 找出有权限的节点列表
    private List<SupplierCategoryV2> findAuthorizedNodes(List<SupplierCategoryV2> allNodes, List<SupplierCategoryV2> authorizedNodes) {
        Map<String, SupplierCategoryV2> nodeMap = new HashMap<>();
        Set<String> authorizedNos = new HashSet<>();

        // 将所有节点存储到映射中
        for (SupplierCategoryV2 node : allNodes) {
            nodeMap.put(node.getNo(), node);
        }

        // 获取所有有权限的节点及其祖先节点的编码
        for (SupplierCategoryV2 node : authorizedNodes) {
            authorizedNos.add(node.getNo());
            authorizedNos.addAll(findAllAncestors(nodeMap, node.getNo()));
        }

        // 根据编码筛选出有权限的节点
        List<SupplierCategoryV2> result = new ArrayList<>();
        for (SupplierCategoryV2 node : allNodes) {
            if (authorizedNos.contains(node.getNo())) {
                result.add(node);
            }
        }

        return result;
    }


    @Override
    public PageVo<SupplierCategoryVO> queryFLatListPage(OperationModel operationModel, SupplierCategoryQueryFlagListPageReq queryListReq, PageDto pageDTO) {
        if (StringUtils.isBlank(queryListReq.getUseOrgNo())) {
            ValidatorUtils.checkEmptyThrowEx(operationModel.getOrgCode(), "使用组织编号不能为空");
            queryListReq.setUseOrgNo(operationModel.getOrgNo());
        }

        SupplierCategoryQueryTreeReq queryTreeReq = new SupplierCategoryQueryTreeReq();
        queryTreeReq.setEnterpriseNo(operationModel.getEnterpriseNo());
        queryTreeReq.setUseOrgNo(queryListReq.getUseOrgNo());
        List<SupplierCategoryTreeVO> supplierCategoryTreeVOS = queryEligibleSupplierCategory(operationModel, queryTreeReq);
        if (CollectionUtils.isEmpty(supplierCategoryTreeVOS)) {
            return new PageVo<>();
        }


        List<String> allNos = supplierCategoryTreeVOS.stream().map(SupplierCategoryTreeVO::getNo).collect(Collectors.toList());


        Page<SupplierCategoryV2> page = PageHelper.startPage(pageDTO.getPageIndex(), pageDTO.getPageSize(), pageDTO.getOrderBy());
        LambdaQueryWrapper<SupplierCategoryV2> wrapper = Wrappers.<SupplierCategoryV2>lambdaQuery()
                .eq(SupplierCategoryV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(SupplierCategoryV2::getDeleted, DeletedEnum.UN_DELETE.getValue())
                .eq(null != queryListReq.getStatus(), SupplierCategoryV2::getStatus, queryListReq.getStatus())
                .nested(StringUtils.isNotBlank(queryListReq.getKeywords()), w ->
                        w.like(SupplierCategoryV2::getCategoryCode, queryListReq.getKeywords())
                                .or()
                                .like(SupplierCategoryV2::getCategoryName, queryListReq.getKeywords()))
                .in(SupplierCategoryV2::getNo, allNos)
                .in(CollectionUtils.isNotEmpty(queryListReq.getSupplierCategoryNoList()), SupplierCategoryV2::getNo, queryListReq.getSupplierCategoryNoList())
                .last("order by level asc, sort_num is null asc, sort_num asc");
        supplierCategoryV2DAO.selectList(wrapper);

        return PageUtils.convertPageVo(page, data -> {
            List<SupplierCategoryVO> detailVOList = new ArrayList<>();
            if (Objects.nonNull(data)) {
                Map<String, SupplierCategoryTreeVO> no2CategoryTreeVOMap = supplierCategoryTreeVOS.stream().collect(Collectors.toMap(SupplierCategoryTreeVO::getNo, Function.identity()));
//                Set<String> parentNoSet = supplierCategoryTreeVOS.stream().map(SupplierCategoryTreeVO::getParentNo).filter(parentNo -> !ServiceConstant.TOP_PARENT_NO.equals(parentNo)).collect(Collectors.toSet());

                data.forEach(supplierCategoryV2 -> {
                    SupplierCategoryVO supplierCategoryVO = new SupplierCategoryVO();
                    BeanUtils.copyProperties(supplierCategoryV2, supplierCategoryVO);

                    // 填充管理组织名称
                    supplierCategoryVO.setManageOrgName(no2CategoryTreeVOMap.get(supplierCategoryV2.getNo()).getManageOrgName());
                    supplierCategoryVO.setManageOrgCode(no2CategoryTreeVOMap.get(supplierCategoryV2.getNo()).getManageOrgCode());

                    // 填充使用组织名称
                    supplierCategoryVO.setUseOrgNames(no2CategoryTreeVOMap.get(supplierCategoryV2.getNo()).getUseOrgNames());
                    supplierCategoryVO.setUseOrgList(no2CategoryTreeVOMap.get(supplierCategoryV2.getNo()).getUseOrgList());

                    // 填充子孙节点标记
//                    supplierCategoryVO.setDescendantsFlag(parentNoSet.contains(supplierCategoryV2.getNo()) ? CommonIfEnum.YES.getValue() : CommonIfEnum.NO.getValue());

                    // 填充请求的使用组织
//                    supplierCategoryVO.setInputUseOrgNo(queryListReq.getUseOrgNo());
//                    supplierCategoryVO.setInputUseOrgName(queryListReq.getUseOrgName());

                    // 填充上级分类名称
                    if (ServiceConstant.TOP_PARENT_NO.equals(no2CategoryTreeVOMap.get(supplierCategoryV2.getNo()).getParentNo())) {
                        supplierCategoryVO.setParentNo("");
                        supplierCategoryVO.setParentName("");
                    } else {
                        supplierCategoryVO.setParentName(no2CategoryTreeVOMap.get(supplierCategoryV2.getNo()).getParentName());
                    }

                    supplierCategoryVO.setStatusName(CommonStatusEnum.INVALID.getValue().toString().equals(supplierCategoryV2.getStatus()) ? "停用" : "启用");

                    detailVOList.add(supplierCategoryVO);
                });
            }
            return detailVOList;
        });
    }
}

