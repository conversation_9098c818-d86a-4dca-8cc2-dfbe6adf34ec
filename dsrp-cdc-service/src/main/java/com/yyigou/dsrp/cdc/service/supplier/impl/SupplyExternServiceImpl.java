package com.yyigou.dsrp.cdc.service.supplier.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.yyigou.ddc.common.util.DateUtil;
import com.yyigou.ddc.common.util.ValidatorUtils;
import com.yyigou.ddc.services.ddc.uim.vo.OrganizationTreeVo;
import com.yyigou.ddc.services.dlog.api.ULogAPI;
import com.yyigou.ddc.services.dlog.dto.IntegrationLogDTO;
import com.yyigou.ddc.services.dlog.enums.integration.IntegrationError;
import com.yyigou.ddc.services.dlog.enums.integration.IntegrationRequestType;
import com.yyigou.ddc.services.dlog.enums.integration.MdmBillType;
import com.yyigou.ddc.services.mq.manager.MessageQueueManager;
import com.yyigou.dsrp.cdc.api.company.dto.BankDTO;
import com.yyigou.dsrp.cdc.api.company.dto.CompanyFileDTO;
import com.yyigou.dsrp.cdc.api.company.dto.CompanyLinkmanDTO;
import com.yyigou.dsrp.cdc.api.company.dto.CompanyShippingAddressDTO;
import com.yyigou.dsrp.cdc.api.supply.dto.SupplierExternalAssignDTO;
import com.yyigou.dsrp.cdc.api.supply.dto.SupplierExternalSaveDTO;
import com.yyigou.dsrp.cdc.api.supply.dto.SupplierSalesManDTO;
import com.yyigou.dsrp.cdc.api.supply.vo.SupplierExternalSaveVO;
import com.yyigou.dsrp.cdc.api.supply.vo.SupplierInternalSaveVO;
import com.yyigou.dsrp.cdc.common.enums.*;
import com.yyigou.dsrp.cdc.common.enums.company.HospitalClassEnum;
import com.yyigou.dsrp.cdc.common.enums.company.HospitalTypeEnum;
import com.yyigou.dsrp.cdc.common.enums.company.InstitutionalTypeEnum;
import com.yyigou.dsrp.cdc.common.util.CommonUtil;
import com.yyigou.dsrp.cdc.common.util.OperationModel;
import com.yyigou.dsrp.cdc.dao.common.BankTypeDAO;
import com.yyigou.dsrp.cdc.dao.common.entity.BankType;
import com.yyigou.dsrp.cdc.dao.company.CompanyDAO;
import com.yyigou.dsrp.cdc.dao.company.CompanyLinkmanDAO;
import com.yyigou.dsrp.cdc.dao.company.CompanyShippingAddressDAO;
import com.yyigou.dsrp.cdc.dao.company.entity.Company;
import com.yyigou.dsrp.cdc.dao.company.entity.CompanyLinkman;
import com.yyigou.dsrp.cdc.dao.company.entity.CompanyShippingAddress;
import com.yyigou.dsrp.cdc.dao.gradedcontrol.SupplierCustomerUseInfoDAO;
import com.yyigou.dsrp.cdc.dao.gradedcontrol.entity.SupplierCustomerUseInfo;
import com.yyigou.dsrp.cdc.dao.supplier.CompanyBankDAO;
import com.yyigou.dsrp.cdc.dao.supplier.SupplierDAO;
import com.yyigou.dsrp.cdc.dao.supplier.SupplierOrderManDAO;
import com.yyigou.dsrp.cdc.dao.supplier.entity.CompanyBank;
import com.yyigou.dsrp.cdc.dao.supplier.entity.Supplier;
import com.yyigou.dsrp.cdc.dao.supplier.entity.SupplierOrderMan;
import com.yyigou.dsrp.cdc.manager.integration.dccert.CatalogInfoService;
import com.yyigou.dsrp.cdc.manager.integration.exeresult.MasterDataExecuteService;
import com.yyigou.dsrp.cdc.manager.integration.numberCenter.NumberCenterService;
import com.yyigou.dsrp.cdc.manager.integration.org.UimTenantService;
import com.yyigou.dsrp.cdc.manager.integration.uim.CustomDocService;
import com.yyigou.dsrp.cdc.model.constant.NumberCenterConstant;
import com.yyigou.dsrp.cdc.model.constant.SystemConstant;
import com.yyigou.dsrp.cdc.service.company.CompanyService;
import com.yyigou.dsrp.cdc.service.supplier.SupplierCategoryService;
import com.yyigou.dsrp.cdc.service.supplier.SupplyExtendService;
import com.yyigou.dsrp.cdc.service.supplier.SupplyExternService;
import com.yyigou.dsrp.cdc.service.v2.supplier.SupplierV2ExternalService;
import com.yyigou.dsrp.cert.client.certbase.req.BaseFileClientReq;
import com.yyigou.dsrp.cert.client.certbase.req.CatalogInfoBigClientReq;
import com.yyigou.dsrp.cert.client.certbase.req.CatalogInfoClientReq;
import com.yyigou.dsrp.cert.client.certbase.req.CertCompanyClientReq;
import com.yyigou.dsrp.cert.client.certbase.res.CatalogInfoClientRes;
import com.yyigou.dsrp.gcs.client.executeRecord.request.SaveExecuteRecordRequest;
import com.yyigou.dsrp.gcs.client.executeRecord.response.ExecuteRecordResponse;
import com.yyigou.dsrp.gcs.common.enums.executeRecord.ExecuteRecordDataTypeEnum;
import com.yyigou.dsrp.gcs.common.enums.executeRecord.ExecuteRecordExcuteTypeEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class SupplyExternServiceImpl implements SupplyExternService {
    private final SupplierDAO supplierDAO;
    private final CompanyLinkmanDAO companyLinkmanDAO;
    private final CompanyShippingAddressDAO companyShippingAddressDAO;
    private final CompanyBankDAO companyBankDAO;
    private final SupplierOrderManDAO supplierOrderManDAO;
    private final CompanyDAO companyDAO;
    private final CompanyService companyService;
    private final NumberCenterService numberCenterService;
    private final SupplierCategoryService supplierCategoryService;
    private final BankTypeDAO bankTypeDAO;
    private final CatalogInfoService catalogInfoService;
    private final UimTenantService uimTenantService;
    private final MessageQueueManager messageQueueManager;
    private final MasterDataExecuteService masterDataExecuteService;
    private final CustomDocService customDocService;
    private final SupplyExtendService supplyExtendService;
    private final SupplierCustomerUseInfoDAO supplierCustomerUseInfoDAO;

    private final ULogAPI uLogAPI;

    @Resource
    private SupplierV2ExternalService supplierV2ExternalService;

    @Override
    public List<SupplierExternalSaveVO> batchSave(OperationModel operationModel, List<SupplierExternalSaveDTO> params) {
        ValidatorUtils.checkTrueThrowEx(CollectionUtils.isEmpty(params), "数据不能为空");
        ValidatorUtils.checkTrueThrowEx(params.size() > 20, "推送数据不能超过20条");
        final List<OrganizationTreeVo> organizationTreeList = uimTenantService.getOrganizationTreeList(operationModel.getEnterpriseNo());
        final Map<String, OrganizationTreeVo> organizationMap = organizationTreeList.stream().collect(Collectors.toMap(OrganizationTreeVo::getOrgCode, Function.identity(), (k, v) -> k));
        // 供应商性质（合作性质）
        Map<String, String> cooperationModeMap = new HashMap<>();
        List<SupplierInternalSaveVO> resultList = new ArrayList<>();

        // 提前查询供应商档案是否存在，因为后续需要校验禁用的档案在系统中是否存在
        final List<String> supplierCodeList = params.stream().map(SupplierExternalSaveDTO::getSupplierCode).filter(supplierCode -> !StringUtils.isEmpty(supplierCode)).collect(Collectors.toList());
        List<Supplier> localSupplierList = new ArrayList<>();
        final Map<String, Supplier> localSupplierMap = new HashMap<>();
        if(!CollectionUtils.isEmpty(supplierCodeList)) {
            localSupplierList = supplierDAO.getSupplierByCodeList(operationModel.getEnterpriseNo(), supplierCodeList);
            localSupplierMap.putAll(localSupplierList.stream().collect(Collectors.toMap(Supplier::getSupplierCode, Function.identity())));
        }
        String enableStatus = EnableEnum.ENABLE.getValue() + "";
        String disableStatus = EnableEnum.DISABLE.getValue() + "";
        params.removeIf(t -> {
            //set default
            if (t.getIsMedicalInstitution() == null) {
                t.setIsMedicalInstitution(0);
            }
            //默认非关联企业
            if (t.getIsAssociatedEnterprise() == null) {
                t.setIsAssociatedEnterprise(0);
            }
            if (StringUtils.isEmpty(t.getSupplierCode())) {
                resultList.add(new SupplierInternalSaveVO(t.getSupplierCode(), t.getSupplierName(), false,
                        "供应商编码不能为空", operationModel, IntegrationError.PARAMS_EMPTY_ERR));
                return Boolean.TRUE;
            }
            if (StringUtils.isEmpty(t.getControlStatus())) {
                resultList.add(new SupplierInternalSaveVO(t.getSupplierCode(), t.getSupplierName(), false,
                        "供应商生效状态不能为空", operationModel, IntegrationError.PARAMS_EMPTY_ERR));
                return Boolean.TRUE;
            }
            if(disableStatus.equals(t.getControlStatus())) {
                // 如果禁用的档案不存在则报错
                if (localSupplierMap.get(t.getSupplierCode()) == null) {
                    resultList.add(new SupplierInternalSaveVO(t.getSupplierCode(), t.getSupplierName(), false,
                            "禁用的供应商编码不存在", operationModel, IntegrationError.PARAMS_EMPTY_ERR));
                    return Boolean.TRUE;
                }
            } else if (!enableStatus.equals(t.getControlStatus())) {
                resultList.add(new SupplierInternalSaveVO(t.getSupplierCode(), t.getSupplierName(), false,
                        "供应商生效状态不正确", operationModel, IntegrationError.PARAMS_EMPTY_ERR));
                return Boolean.TRUE;
            }
            if (StringUtils.isEmpty(t.getSupplierName())) {
                resultList.add(new SupplierInternalSaveVO(t.getSupplierCode(), t.getSupplierName(), false,
                        "供应商名称不能为空", operationModel, IntegrationError.PARAMS_EMPTY_ERR));
                return Boolean.TRUE;
            }
            if (StringUtils.isEmpty(t.getUnifiedSocialCode())) {
                resultList.add(new SupplierInternalSaveVO(t.getSupplierCode(), t.getSupplierName(), false,
                        "统一社会信用代码不能为空", operationModel, IntegrationError.PARAMS_EMPTY_ERR));
                return Boolean.TRUE;
            }
            if (StringUtils.isEmpty(t.getCompanyName())) {
                resultList.add(new SupplierInternalSaveVO(t.getSupplierCode(), t.getSupplierName(), false,
                        "企业名称不能为空", operationModel, IntegrationError.PARAMS_EMPTY_ERR));
                return Boolean.TRUE;
            }
            if (StringUtils.isEmpty(t.getSupplierCategoryCode_1())) {
                resultList.add(new SupplierInternalSaveVO(t.getSupplierCode(), t.getSupplierName(), false,
                        "供应商分类不能为空", operationModel, IntegrationError.PARAMS_EMPTY_ERR));
                return Boolean.TRUE;
            }
            if (t.getIsMedicalInstitution() == null || (!new Integer(1).equals(t.getIsMedicalInstitution()) && !new Integer(0).equals(t.getIsMedicalInstitution()))) {
                resultList.add(new SupplierInternalSaveVO(t.getSupplierCode(), t.getSupplierName(), false,
                        "是否医疗机构不合法", operationModel, IntegrationError.PARAMS_INVALID_ERR));
                return Boolean.TRUE;
            }
            if (new Integer(1).equals(t.getIsMedicalInstitution())) {
                if (StringUtils.isEmpty(t.getInstitutionalType())) {
                    resultList.add(new SupplierInternalSaveVO(t.getSupplierCode(), t.getSupplierName(), false,
                            "医疗机构类型不能为空", operationModel, IntegrationError.PARAMS_EMPTY_ERR));
                    return Boolean.TRUE;
                } else if (InstitutionalTypeEnum.getByType(t.getInstitutionalType()) == null) {
                    resultList.add(new SupplierInternalSaveVO(t.getSupplierCode(), t.getSupplierName(), false,
                            "医疗机构类型不合法", operationModel, IntegrationError.PARAMS_INVALID_ERR));
                    return Boolean.TRUE;
                }
                if (StringUtils.isEmpty(t.getHospitalType())) {
                    resultList.add(new SupplierInternalSaveVO(t.getSupplierCode(), t.getSupplierName(), false,
                            "医疗机构性质不能为空", operationModel, IntegrationError.PARAMS_EMPTY_ERR));
                    return Boolean.TRUE;
                } else if (HospitalTypeEnum.getByType(t.getHospitalType()) == null) {
                    resultList.add(new SupplierInternalSaveVO(t.getSupplierCode(), t.getSupplierName(), false,
                            "医疗机构性质不合法", operationModel, IntegrationError.PARAMS_INVALID_ERR));
                    return Boolean.TRUE;
                }
                if (t.getHospitalClass() == null) {
                    resultList.add(new SupplierInternalSaveVO(t.getSupplierCode(), t.getSupplierName(), false,
                            "医疗机构等级不能为空", operationModel, IntegrationError.PARAMS_EMPTY_ERR));
                    return Boolean.TRUE;
                } else if (HospitalClassEnum.getByType(t.getHospitalClass()) == null) {
                    resultList.add(new SupplierInternalSaveVO(t.getSupplierCode(), t.getSupplierName(), false,
                            "医疗机构等级不合法", operationModel, IntegrationError.PARAMS_INVALID_ERR));
                    return Boolean.TRUE;
                }
            }
            if (new Integer(1).equals(t.getIsAssociatedEnterprise())) {
                if (StringUtils.isEmpty(t.getAssociatedOrgCode())) {
                    resultList.add(new SupplierInternalSaveVO(t.getSupplierCode(), t.getSupplierName(), false,
                            "关联企业编码不能为空", operationModel, IntegrationError.PARAMS_EMPTY_ERR));
                    return Boolean.TRUE;
                }
//                if (!organizationMap.containsKey(t.getAssociatedOrgCode())) {
//                    resultList.add(new SupplierExternalSaveVO(t.getSupplierCode(), false, "关联组织" + t.getAssociatedOrgCode() + "在DSRP中不存在"));
//                    return Boolean.TRUE;
//                }

            }
            //
            //方法学分类不为空
            if (StringUtils.isNotBlank(t.getCooperationMode()) && StringUtils.isNotBlank(t.getCooperationModeName())) {
                if (cooperationModeMap.containsKey(t.getCooperationMode())) {
                    if (!cooperationModeMap.get(t.getCooperationMode()).equals(t.getCooperationModeName())) {
                        resultList.add(new SupplierInternalSaveVO(t.getSupplierCode(), t.getSupplierName(), false,
                                "供应商性质编码和名称不统一", operationModel, IntegrationError.PARAMS_INVALID_ERR));
                        return Boolean.TRUE;
                    }
                } else {
                    cooperationModeMap.put(t.getCooperationMode(), t.getCooperationModeName());
                }
            }
            return Boolean.FALSE;
        });
        if (CollectionUtils.isEmpty(params)) {
            return saveBusinessLogAndRetResult(resultList);
        }
        // 只留下校验通过的供应商档案
        Set<String> validSupplierCodeSet = params.stream().map(SupplierExternalSaveDTO::getSupplierCode).collect(Collectors.toSet());
        localSupplierList = localSupplierList.stream().filter(t -> validSupplierCodeSet.contains(t.getSupplierCode())).collect(Collectors.toList());
        Set<String> localSupplierCodeSetCopy = new HashSet<>(localSupplierMap.keySet());
        for (String localSupplierCode : localSupplierCodeSetCopy) {
            if(!validSupplierCodeSet.contains(localSupplierCode)) {
                localSupplierMap.remove(localSupplierCode);
            }
        }

        //查询已经存在的供应商信息
        Map<String, List<CompanyBank>> supplierBankGroup;
        Map<String, List<CompanyLinkman>> companyLinkmanGroup;
        Map<String, List<CompanyShippingAddress>> companyShippingAddressMap;
        Map<String, List<SupplierOrderMan>> supplierOrderManMap;
        if (!CollectionUtils.isEmpty(localSupplierList)) {
            final List<String> localSupplierNoList = localSupplierList.stream().map(Supplier::getSupplierNo).collect(Collectors.toList());
            //查询银行信息
            final List<CompanyBank> companyBankList = companyBankDAO.getCompanyBankListBySourceNoList(operationModel.getEnterpriseNo(), LinkmanTypeEnum.SUPPLY.getValue(), localSupplierNoList);
            supplierBankGroup = companyBankList.stream().collect(Collectors.groupingBy(CompanyBank::getSupplierNo));
            //联系人
            final List<CompanyLinkman> companyLinkmanList = companyLinkmanDAO.getCompanyLinkmanListBySourceNoList(operationModel.getEnterpriseNo(), LinkmanTypeEnum.SUPPLY.getValue(), localSupplierNoList);
            companyLinkmanGroup = companyLinkmanList.stream().collect(Collectors.groupingBy(CompanyLinkman::getSourceNo));
            //查询联系地址
            final List<CompanyShippingAddress> companyShippingAddressList = companyShippingAddressDAO.getCompanyShippingAddressBySourceNoList(operationModel.getEnterpriseNo(), LinkmanTypeEnum.SUPPLY.getValue(), localSupplierNoList);
            companyShippingAddressMap = companyShippingAddressList.stream().collect(Collectors.groupingBy(CompanyShippingAddress::getSourceNo));
            //负责人
            final List<SupplierOrderMan> supplierOrderManList = supplierOrderManDAO.getSupplierOrderManListBySupplierNoList(operationModel.getEnterpriseNo(), localSupplierNoList);
            supplierOrderManMap = supplierOrderManList.stream().collect(Collectors.groupingBy(SupplierOrderMan::getSupplierNo));
        } else {
            companyLinkmanGroup = new HashMap<>();
            companyShippingAddressMap = new HashMap<>();
            supplierOrderManMap = new HashMap<>();
            supplierBankGroup = new HashMap<>();
        }
        final List<SupplierExternalSaveDTO> updateSupplyList = params.stream().filter(t -> localSupplierMap.containsKey(t.getSupplierCode())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(updateSupplyList)) {
            final List<SupplierExternalSaveDTO> deleteBankList = updateSupplyList.stream().filter(t -> CollectionUtils.isEmpty(t.getBankList())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(deleteBankList)) {
                companyBankDAO.deleteCompanyBankListBySourceNoList(operationModel.getEnterpriseNo(), LinkmanTypeEnum.SUPPLY.getValue(), deleteBankList.stream().map(t -> localSupplierMap.get(t.getSupplierCode()).getSupplierNo()).collect(Collectors.toList()));
            }
            final List<SupplierExternalSaveDTO> deleteResponsibleManList = updateSupplyList.stream().filter(t -> CollectionUtils.isEmpty(t.getResponsibleManList())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(deleteResponsibleManList)) {
                supplierOrderManDAO.deleteSupplierOrderManListBySupplierNoList(operationModel.getEnterpriseNo(), deleteResponsibleManList.stream().map(t -> localSupplierMap.get(t.getSupplierCode()).getSupplierNo()).collect(Collectors.toList()));
            }
            final List<SupplierExternalSaveDTO> deleteLinkmanList = updateSupplyList.stream().filter(t -> CollectionUtils.isEmpty(t.getLinkmanList())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(deleteLinkmanList)) {
                companyLinkmanDAO.deleteCompanyLinkmanListByCompanyNo(operationModel.getEnterpriseNo(), LinkmanTypeEnum.SUPPLY.getValue(), deleteLinkmanList.stream().map(t -> localSupplierMap.get(t.getSupplierCode()).getSupplierNo()).collect(Collectors.toList()));
            }
            final List<SupplierExternalSaveDTO> deleteLinkAddressList = updateSupplyList.stream().filter(t -> CollectionUtils.isEmpty(t.getLinkAddressList())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(deleteLinkAddressList)) {
                companyShippingAddressDAO.deleteShippingAddressListBySourceNoList(operationModel.getEnterpriseNo(), LinkmanTypeEnum.SUPPLY.getValue(), deleteLinkAddressList.stream().map(t -> localSupplierMap.get(t.getSupplierCode()).getSupplierNo()).collect(Collectors.toList()));
            }
        }
        //create and update supplierCategory 创建供应商分类
        final List<SupplierExternalSaveDTO> supplierCategoryList = params.stream().filter(t -> StringUtils.isNotBlank(t.getSupplierCategoryCode_1())).collect(Collectors.toList());
        supplierCategoryService.createAndUpdateCategory(operationModel, supplierCategoryList);
        // 新增供应商性质
        if (MapUtils.isNotEmpty(cooperationModeMap)) {
            customDocService.saveAndUpdate(operationModel.getEnterpriseNo(), "dsrp_supplier_cooperation_mode", cooperationModeMap, operationModel.getEmployerNo(), operationModel.getUserName());
        }
        //create and update company
        final List<String> unifiedSocialCodeList = params.stream().map(SupplierExternalSaveDTO::getUnifiedSocialCode).filter(t -> !SystemConstant.DEFAULT_UNIFIED_SOCIAL_CODE.equalsIgnoreCase(t)).collect(Collectors.toList());
        final List<String> companyNameList = params.stream().map(SupplierExternalSaveDTO::getCompanyName).collect(Collectors.toList());

        Map<String, Company> localUnifiedSocialCompanyMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(unifiedSocialCodeList)) {
            List<Company> localUnifiedSocialCompanyList = companyDAO.findByEnterpriseNoAndUnifiedSocialCodeList(operationModel.getEnterpriseNo(), unifiedSocialCodeList);
            localUnifiedSocialCompanyMap = localUnifiedSocialCompanyList.stream().collect(Collectors.toMap(Company::getUnifiedSocialCode, Function.identity(), (v1, v2) -> v1));
        }
        List<Company> locaCompanyNameList = companyDAO.findByEnterpriseNoAndCompanyName(operationModel.getEnterpriseNo(), companyNameList);
        Map<String, Company> locaCompanyNameMap = locaCompanyNameList.stream().collect(Collectors.toMap(Company::getCompanyName, Function.identity(), (v1, v2) -> v1));

        final List<BankType> bankList = bankTypeDAO.getList();
        final Map<String, BankType> bankMap = bankList.stream().collect(Collectors.toMap(BankType::getBankName, Function.identity(), (v1, v2) -> v1));
        //企业不存在创建企业
        List<Company> companyList = new ArrayList<>();
        for (SupplierExternalSaveDTO t : params) {
            //没有社会信用代码也没有一样企业名称的企业 应该新增企业
            if (locaCompanyNameMap.containsKey(t.getCompanyName())) {
                //说明企业名称能匹配上
                Company company = locaCompanyNameMap.get(t.getCompanyName());
                company.setUnifiedSocialCode(t.getUnifiedSocialCode());
                company.setInstitutionalType(t.getInstitutionalType());
                company.setHospitalType(t.getHospitalType());
                company.setHospitalClass(t.getHospitalClass());
                company.setLegalPerson(t.getLegalPerson());
                company.setRegionCode(t.getRegionCode());
                company.setIsAssociatedEnterprise(t.getIsAssociatedEnterprise());
                company.setAssociatedOrgCode(t.getAssociatedOrgCode());
                if (new Integer(1).equals(t.getIsAssociatedEnterprise())) {
                    final OrganizationTreeVo organizationTreeVo = organizationMap.get(t.getAssociatedOrgCode());
                    if (organizationTreeVo != null) {
                        company.setAssociatedOrgNo(organizationTreeVo.getOrgNo());
                        company.setAssociatedOrgName(organizationTreeVo.getOrgName());
                    }
                }
                companyService.updateCompany(operationModel, company);
            } else if (localUnifiedSocialCompanyMap.containsKey(t.getUnifiedSocialCode())) {
                //说明有企业信用代码能匹配上
                Company company = localUnifiedSocialCompanyMap.get(t.getUnifiedSocialCode());
                company.setCompanyName(t.getCompanyName());
                company.setInstitutionalType(t.getInstitutionalType());
                company.setHospitalType(t.getHospitalType());
                company.setHospitalClass(t.getHospitalClass());
                company.setLegalPerson(t.getLegalPerson());
                company.setRegionCode(t.getRegionCode());
                company.setIsAssociatedEnterprise(t.getIsAssociatedEnterprise());
                company.setAssociatedOrgCode(t.getAssociatedOrgCode());
                if (new Integer(1).equals(t.getIsAssociatedEnterprise())) {
                    final OrganizationTreeVo organizationTreeVo = organizationMap.get(t.getAssociatedOrgCode());
                    if (organizationTreeVo != null) {
                        company.setAssociatedOrgNo(organizationTreeVo.getOrgNo());
                        company.setAssociatedOrgName(organizationTreeVo.getOrgName());
                    }
                }
                companyService.updateCompany(operationModel, company);
            } else if (!localUnifiedSocialCompanyMap.containsKey(t.getUnifiedSocialCode()) && !locaCompanyNameMap.containsKey(t.getCompanyName())) {
                Company company = new Company();
                company.setEnterpriseNo(operationModel.getEnterpriseNo());
                company.setCompanyCode(SystemConstant.DEFAULT_UNIFIED_SOCIAL_CODE.equalsIgnoreCase(t.getUnifiedSocialCode()) ? t.getSupplierCode() : t.getUnifiedSocialCode());
                company.setCompanyName(t.getCompanyName());
                company.setUnifiedSocialCode(t.getUnifiedSocialCode());
                company.setFactoryType(t.getFactoryType());
                company.setInstitutionalType(t.getInstitutionalType());
                company.setHospitalType(t.getHospitalType());
                company.setHospitalClass(t.getHospitalClass());
                company.setLegalPerson(t.getLegalPerson());
                company.setRegionCode(t.getRegionCode());
                company.setIsAssociatedEnterprise(t.getIsAssociatedEnterprise());
                company.setAssociatedOrgCode(t.getAssociatedOrgCode());
                if (new Integer(1).equals(t.getIsAssociatedEnterprise())) {
                    final OrganizationTreeVo organizationTreeVo = organizationMap.get(t.getAssociatedOrgCode());
                    if (organizationTreeVo != null) {
                        company.setAssociatedOrgNo(organizationTreeVo.getOrgNo());
                        company.setAssociatedOrgName(organizationTreeVo.getOrgName());
                    }
                }
                CommonUtil.fillCreatInfo(operationModel, company);
                CommonUtil.fillOperateInfo(operationModel, company);
                company.setStatus(CommonStatusEnum.EFFECTIVE.getValue());
                company.setDeleted(DeletedEnum.UN_DELETE.getValue());
                companyList.add(company);
            }
        }
        //创建企业
        if (!CollectionUtils.isEmpty(companyList)) {
            final List<String> companyNoList = numberCenterService.batchGenerateNoList(NumberCenterConstant.COMPANY_NO_GENERATE_KEY, companyList.size());
            AtomicInteger companyNoindex = new AtomicInteger(-1);
            companyList.forEach(t -> t.setCompanyNo(companyNoList.get(companyNoindex.incrementAndGet())));
            companyDAO.addBatch(companyList);
            locaCompanyNameList = companyDAO.findByEnterpriseNoAndCompanyName(operationModel.getEnterpriseNo(), companyNameList);
            locaCompanyNameMap = locaCompanyNameList.stream().collect(Collectors.toMap(Company::getCompanyName, Function.identity(), (v1, v2) -> v1));
        }
        //本次新增的供应商
        final long count = params.stream().filter(t -> !localSupplierMap.containsKey(t.getSupplierCode())).count();
        List<String> supplyNoList;
        AtomicInteger supplyNoIndex = new AtomicInteger(-1);
        if (count > 0) {
            supplyNoList = numberCenterService.batchGenerateNoList(NumberCenterConstant.SUPPLY_NO_KEY, (int) count);
        } else {
            supplyNoList = new ArrayList<>();
        }
        List<Supplier> saveSupplierList = new ArrayList<>();
        List<CompanyBank> saveCompanyBankList = new ArrayList<>();
        List<SupplierOrderMan> saveResponsibleManListList = new ArrayList<>();
        List<CompanyShippingAddress> saveShippingAddressListList = new ArrayList<>();
        List<CompanyLinkman> saveLinkmanListList = new ArrayList<>();
        Map<String, Company> finalLocaCompanyMap = locaCompanyNameMap;
        List<SaveExecuteRecordRequest> executeRecordList = new ArrayList<>();
        params.forEach(t -> {
            Supplier supplier = new Supplier();
            //修改
            if (localSupplierMap.containsKey(t.getSupplierCode())) {
                supplier = localSupplierMap.get(t.getSupplierCode());
                supplier.setSupplierName(t.getSupplierName());
                supplier.setMnemonicCode(t.getMnemonicCode());
                supplier.setSupplierNameEn(t.getSupplierNameEn());
                supplier.setOwnerCompany(t.getOwnerCompany());
                supplier.setRemark(t.getRemark());
                //信用天数额度
                supplier.setCreditAmount(t.getCreditAmount());
                supplier.setPeriodDays(t.getPeriodDays());
                //合作起止日期
                supplier.setCoopStartTime(t.getCoopStartTime());
                supplier.setCoopEndTime(t.getCoopEndTime());
                // 供应商性质（合作性质）
                supplier.setCooperationMode(t.getCooperationMode());
                supplier.setSupplierCategoryNo(t.getSupplierCategoryNo());
                supplier.setControlStatus(t.getControlStatus());
                supplier.setPaymentAgreementCode(t.getPaymentAgreementCode());
                supplier.setPaymentAgreementName(t.getPaymentAgreementName());
                supplierDAO.updateById(supplier);
                // 如果是禁用已存在的集团供应商档案，则需要将所有已经分派的档案都禁用；如果是启用已存在的集团供应商档案，组织的档案状态后续单独处理,这里不处理
                if(disableStatus.equals(t.getControlStatus())) {
                    List<SupplierCustomerUseInfo> useInfoList = supplierCustomerUseInfoDAO.findGroupSupplierCustomerUseList(operationModel.getEnterpriseNo(), Lists.newArrayList(t.getSupplierCode()), CompanyTypeEnum.SUPPLIER.getValue());
                    if(!CollectionUtils.isEmpty(useInfoList)) {
                        supplierDAO.batchUpdateMultiOrganizeSupplierStatus(useInfoList.stream().map(SupplierCustomerUseInfo::getUseEnterpriseNo).collect(Collectors.toList()), t.getSupplierCode(),disableStatus);
                    }
                }
                resultList.add(new SupplierInternalSaveVO(t.getSupplierCode(), t.getSupplierName(), true, "供应商档案更新成功", operationModel));
            } else {
                //新增 。新增的供应商档案只能是生效状态，组织的档案状态后续单独处理
                supplier.setEnterpriseNo(operationModel.getEnterpriseNo());
                supplier.setSupplierNo(supplyNoList.get(supplyNoIndex.incrementAndGet()));
                supplier.setCompanyNo(finalLocaCompanyMap.get(t.getCompanyName()).getCompanyNo());
                supplier.setUnifiedSocialCode(t.getUnifiedSocialCode());
                supplier.setSupplierCode(t.getSupplierCode());
                supplier.setSupplierName(t.getSupplierName());
                supplier.setMnemonicCode(t.getMnemonicCode());
                supplier.setSupplierNameEn(t.getSupplierNameEn());
                supplier.setOwnerCompany(t.getOwnerCompany());
                supplier.setRemark(t.getRemark());
                //信用天数额度
                supplier.setCreditAmount(t.getCreditAmount());
                supplier.setPeriodDays(t.getPeriodDays());
                //合作起止日期
                supplier.setCoopStartTime(t.getCoopStartTime());
                supplier.setCoopEndTime(t.getCoopEndTime());
                // 供应商性质（合作性质）
                supplier.setCooperationMode(t.getCooperationMode());
                supplier.setBusinessFlag(1);
                supplier.setSupplierCategoryNo(t.getSupplierCategoryNo());
                supplier.setPaymentAgreementCode(t.getPaymentAgreementCode());
                supplier.setPaymentAgreementName(t.getPaymentAgreementName());
                saveSupplierList.add(supplier);
                resultList.add(new SupplierInternalSaveVO(t.getSupplierCode(), t.getSupplierName(), true, "供应商档案新增成功", operationModel));
            }
            //银行
            if (!CollectionUtils.isEmpty(t.getBankList())) {
                List<CompanyBank> localBankList = supplierBankGroup.get(supplier.getSupplierNo());
                Map<String, CompanyBank> localBankMap;
                if (!CollectionUtils.isEmpty(localBankList)) {
                    localBankMap = localBankList.stream().collect(Collectors.toMap(CompanyBank::getBankCode, Function.identity()));
                } else {
                    localBankMap = new HashMap<>();
                }
                for (BankDTO b : t.getBankList()) {
                    final BankType bankType = bankMap.getOrDefault(b.getBankType(), new BankType());
                    if (localBankMap.containsKey(b.getBankCode())) {
                        CompanyBank companyBank = localBankMap.get(b.getBankCode());
                        companyBank.setBankType(bankType.getBankCode());
                        companyBank.setBankTypeName(bankType.getBankName());
                        companyBank.setOpenBank(b.getOpenBank());
                        companyBank.setAccountName(b.getAccountName());
                        companyBank.setAccountNo(b.getAccountNo());
                        companyBank.setAccountType(b.getAccountType());
                        companyBank.setLinkPerson(b.getLinkPerson());
                        companyBank.setLinkPhone(b.getLinkPhone());
                        companyBankDAO.updateById(companyBank);
                    } else {
                        CompanyBank companyBank = new CompanyBank();
                        companyBank.setEnterpriseNo(operationModel.getEnterpriseNo());
                        companyBank.setSupplierNo(supplier.getSupplierNo());
                        companyBank.setCompanyNo(supplier.getCompanyNo());
                        companyBank.setSourceNo(supplier.getSupplierNo());
                        companyBank.setBankCode(b.getBankCode());
                        companyBank.setBankType(bankType.getBankCode());
                        companyBank.setBankTypeName(bankType.getBankName());
                        companyBank.setOpenBank(b.getOpenBank());
                        companyBank.setAccountName(b.getAccountName());
                        companyBank.setAccountNo(b.getAccountNo());
                        companyBank.setAccountType(b.getAccountType());
                        companyBank.setLinkPerson(b.getLinkPerson());
                        companyBank.setLinkPhone(b.getLinkPhone());
                        companyBank.setStatus(b.getStatus());
                        companyBank.setIsDefault(b.getIsDefault());
                        saveCompanyBankList.add(companyBank);
                    }
                }
            }
            //负责人
            if (!CollectionUtils.isEmpty(t.getResponsibleManList())) {
                final List<SupplierOrderMan> orderManList = supplierOrderManMap.get(supplier.getSupplierNo());
                Map<String, SupplierOrderMan> localOrderManMap;
                if (!CollectionUtils.isEmpty(orderManList)) {
                    localOrderManMap = orderManList.stream().collect(Collectors.toMap(SupplierOrderMan::getManCode, Function.identity()));
                } else {
                    localOrderManMap = new HashMap<>();
                }
                for (SupplierSalesManDTO r : t.getResponsibleManList()) {
                    if (localOrderManMap.containsKey(r.getManCode())) {
                        SupplierOrderMan supplierOrderMan = localOrderManMap.get(r.getManCode());
                        supplierOrderMan.setSupplierNo(supplier.getSupplierNo());
                        supplierOrderMan.setManCode(r.getManCode());
                        supplierOrderMan.setOrgNo(r.getDeptNo());
                        supplierOrderMan.setOrderManNo(r.getOrderManNo());
                        supplierOrderMan.setOrderManName(r.getOrderManName());
                        supplierOrderMan.setPost(r.getPost());
                        supplierOrderMan.setIsDefault(r.getIsDefault());
                        supplierOrderMan.setOrderSpecialist(r.getOrderSpecialist());
                        supplierOrderMan.setPost(r.getPost());
                        supplierOrderManDAO.updateById(supplierOrderMan);
                    } else {
                        SupplierOrderMan supplierOrderMan = new SupplierOrderMan();
                        supplierOrderMan.setSupplierNo(supplier.getSupplierNo());
                        supplierOrderMan.setManCode(r.getManCode());
                        supplierOrderMan.setOrgNo(r.getDeptNo());
                        supplierOrderMan.setOrderManNo(r.getOrderManNo());
                        supplierOrderMan.setOrderManName(r.getOrderManName());
                        supplierOrderMan.setPost(r.getPost());
                        supplierOrderMan.setIsDefault(r.getIsDefault());
                        supplierOrderMan.setOrderSpecialist(r.getOrderSpecialist());
                        supplierOrderMan.setPost(r.getPost());
                        saveResponsibleManListList.add(supplierOrderMan);
                    }
                }
            }
            //联系人
            if (!CollectionUtils.isEmpty(t.getLinkmanList())) {
                final List<CompanyLinkman> companyLinkmanList = companyLinkmanGroup.get(supplier.getSupplierNo());
                Map<String, CompanyLinkman> localCompanyLinkmanMap;
                if (!CollectionUtils.isEmpty(companyLinkmanList)) {
                    localCompanyLinkmanMap = companyLinkmanList.stream().collect(Collectors.toMap(CompanyLinkman::getLinkCode, Function.identity()));
                } else {
                    localCompanyLinkmanMap = new HashMap<>();
                }
                for (CompanyLinkmanDTO r : t.getLinkmanList()) {
                    if (localCompanyLinkmanMap.containsKey(r.getLinkCode())) {
                        CompanyLinkman companyLinkman = localCompanyLinkmanMap.get(r.getLinkCode());
                        companyLinkman.setLinkman(r.getLinkman());
                        companyLinkman.setPosition(r.getPosition());
                        companyLinkman.setMobilePhone(r.getMobilePhone());
                        companyLinkman.setSex(r.getSex());
                        companyLinkman.setFixedPhone(r.getFixedPhone());
                        companyLinkman.setQq(r.getQq());
                        companyLinkman.setWx(r.getWx());
                        companyLinkman.setLinkmanType(LinkmanTypeEnum.SUPPLY.getValue());
                        companyLinkman.setStatus(CommonStatusEnum.EFFECTIVE.getValue());
                        companyLinkman.setSourceNo(supplier.getSupplierNo());
                        companyLinkman.setEmail(r.getEmail());
                        companyLinkman.setIsDefault(r.getIsDefault());
                        companyLinkmanDAO.updateById(companyLinkman);
                    } else {
                        CompanyLinkman companyLinkman = new CompanyLinkman();
                        companyLinkman.setEnterpriseNo(operationModel.getEnterpriseNo());
                        companyLinkman.setCompanyNo(supplier.getCompanyNo());
                        companyLinkman.setLinkCode(r.getLinkCode());
                        companyLinkman.setLinkman(r.getLinkman());
                        companyLinkman.setPosition(r.getPosition());
                        companyLinkman.setMobilePhone(r.getMobilePhone());
                        companyLinkman.setSex(r.getSex());
                        companyLinkman.setFixedPhone(r.getFixedPhone());
                        companyLinkman.setQq(r.getQq());
                        companyLinkman.setWx(r.getWx());
                        companyLinkman.setLinkmanType(LinkmanTypeEnum.SUPPLY.getValue());
                        companyLinkman.setStatus(CommonStatusEnum.EFFECTIVE.getValue());
                        companyLinkman.setSourceNo(supplier.getSupplierNo());
                        companyLinkman.setEmail(r.getEmail());
                        companyLinkman.setIsDefault(r.getIsDefault());
                        saveLinkmanListList.add(companyLinkman);
                    }
                }
            }
            //联系地址
            if (!CollectionUtils.isEmpty(t.getLinkAddressList())) {
                final List<CompanyShippingAddress> shippingAddressList = companyShippingAddressMap.get(supplier.getSupplierNo());
                Map<String, CompanyShippingAddress> localShippingAddressMap;
                if (!CollectionUtils.isEmpty(shippingAddressList)) {
                    localShippingAddressMap = shippingAddressList.stream().collect(Collectors.toMap(CompanyShippingAddress::getLinkAddressCode, Function.identity()));
                } else {
                    localShippingAddressMap = new HashMap<>();
                }
                for (CompanyShippingAddressDTO r : t.getLinkAddressList()) {
                    if (localShippingAddressMap.containsKey(r.getLinkAddressCode())) {
                        CompanyShippingAddress shippingAddress = localShippingAddressMap.get(r.getLinkAddressCode());
                        shippingAddress.setReceiveUser(r.getReceiveUser());
                        shippingAddress.setReceivePhone(r.getReceivePhone());
                        shippingAddress.setRegionCode(r.getRegionCode());
                        shippingAddress.setReceiveAddr(r.getReceiveAddr());
                        shippingAddress.setIsDefault(r.getIsDefault());
                        shippingAddress.setAddressType(r.getAddressType());
                        companyShippingAddressDAO.updateById(shippingAddress);
                    } else {
                        CompanyShippingAddress shippingAddress = new CompanyShippingAddress();
                        shippingAddress.setEnterpriseNo(operationModel.getEnterpriseNo());
                        shippingAddress.setCompanyNo(supplier.getCompanyNo());
                        shippingAddress.setLinkAddressCode(r.getLinkAddressCode());
                        shippingAddress.setSourceNo(supplier.getSupplierNo());
                        shippingAddress.setStatus(CommonStatusEnum.EFFECTIVE.getValue());
                        shippingAddress.setLinkaddType(LinkmanTypeEnum.SUPPLY.getValue());

                        shippingAddress.setReceiveUser(r.getReceiveUser());
                        shippingAddress.setReceivePhone(r.getReceivePhone());
                        shippingAddress.setRegionCode(r.getRegionCode());
                        shippingAddress.setReceiveAddr(r.getReceiveAddr());
                        shippingAddress.setIsDefault(r.getIsDefault());
                        shippingAddress.setAddressType(r.getAddressType());
                        saveShippingAddressListList.add(shippingAddress);
                    }
                }
            }
            CatalogInfoBigClientReq catalogInfoBigClientReq = new CatalogInfoBigClientReq();
            catalogInfoBigClientReq.setEnterpriseNo(operationModel.getEnterpriseNo());
            catalogInfoBigClientReq.setUserNo(operationModel.getUserNo());
            catalogInfoBigClientReq.setEmployeeNo(operationModel.getEmployerNo());
            catalogInfoBigClientReq.setUserName(operationModel.getUserName());
            List<CatalogInfoClientRes> catalogInfoList = catalogInfoService.findCompanyCatalogInfoList(catalogInfoBigClientReq, Collections.singletonList(supplier.getCompanyNo()));
            final Map<String, CatalogInfoClientRes> catalogInfoMap = catalogInfoList.stream().collect(Collectors.toMap(CatalogInfoClientRes::getCertType, Function.identity()));
            if (CollectionUtils.isEmpty(t.getCertList()) && !CollectionUtils.isEmpty(catalogInfoList)) {
                //本次没有文件但是存在历史文件删除逻辑
                catalogInfoBigClientReq.setDeleteIdList(catalogInfoList.stream().map(CatalogInfoClientRes::getId).collect(Collectors.toList()));
                catalogInfoService.saveOrUpdateCertBatch(catalogInfoBigClientReq);
            } else if (!CollectionUtils.isEmpty(t.getCertList())) {
                final List<String> itemCertTypeList = t.getCertList().stream().map(CompanyFileDTO::getCertTypeCode).collect(Collectors.toList());
                //如果文件存在 并且本次没有走删除逻辑
                catalogInfoBigClientReq.setDeleteIdList(catalogInfoList.stream().filter(i -> !itemCertTypeList.contains(i.getCertType())).map(CatalogInfoClientRes::getId).collect(Collectors.toList()));
                List<CatalogInfoClientReq> catalogInfoClientList = new ArrayList<>();
                catalogInfoBigClientReq.setCatalogInfoClientList(catalogInfoClientList);
                for (CompanyFileDTO companyFileDTO : t.getCertList()) {
                    CatalogInfoClientReq catalogInfoClientReq = new CatalogInfoClientReq();
                    catalogInfoClientReq.setUserNo(operationModel.getUserNo());
                    catalogInfoClientReq.setEmployeeNo(operationModel.getEmployerNo());
                    catalogInfoClientReq.setUserName(operationModel.getUserName());
                    catalogInfoClientReq.setCertDataTypeCode("company");
                    catalogInfoClientReq.setObjCode("company");
                    catalogInfoClientReq.setEnterpriseNo(operationModel.getEnterpriseNo());
                    catalogInfoClientReq.setId(catalogInfoMap.containsKey(companyFileDTO.getCertTypeCode()) ? catalogInfoMap.get(companyFileDTO.getCertTypeCode()).getId() : null);
                    catalogInfoClientReq.setCertType(companyFileDTO.getCertTypeCode());
                    catalogInfoClientReq.setCertCode(companyFileDTO.getCertCode());
                    catalogInfoClientReq.setCertName(companyFileDTO.getCertName());

                    if (StringUtils.isEmpty(catalogInfoClientReq.getCertName())){
                        log.warn("企业证照文件名称为空，不存入电子资料库 {} {}", operationModel.getEnterpriseNo(), JSON.toJSONString(companyFileDTO));
                        continue;
                    }

                    catalogInfoClientReq.setLongTerm(companyFileDTO.getLongTerm());
                    catalogInfoClientReq.setStartTime(companyFileDTO.getStartTime());
                    catalogInfoClientReq.setEndTime(companyFileDTO.getEndTime());
                    catalogInfoClientReq.setApprovalDate(companyFileDTO.getApprovalDate());
                    catalogInfoClientReq.setRemark(companyFileDTO.getRemark());
                    CertCompanyClientReq certCompanyClientReq = new CertCompanyClientReq();
                    certCompanyClientReq.setCompanyNo(supplier.getCompanyNo());
                    certCompanyClientReq.setObjCode("company");
                    catalogInfoClientReq.setCertCompanyClientReq(certCompanyClientReq);
                    catalogInfoClientList.add(catalogInfoClientReq);
                    if (!CollectionUtils.isEmpty(companyFileDTO.getBaseFileList())) {
                        List<BaseFileClientReq> baseFileList = new ArrayList<>();
                        companyFileDTO.getBaseFileList().forEach(file -> {
                            BaseFileClientReq clientReq = new BaseFileClientReq();
                            clientReq.setFileUrl(file);
                            clientReq.setOuterFile(1);
                            clientReq.setFileName(companyFileDTO.getCertName());
                            baseFileList.add(clientReq);
                        });
                        catalogInfoClientReq.setBaseFileList(baseFileList);
                    }
                }
                if (!CollectionUtils.isEmpty(catalogInfoBigClientReq.getCatalogInfoClientList())) {
                    catalogInfoService.saveOrUpdateCertBatch(catalogInfoBigClientReq);
                }
            }
            // 只需要处理启用状态。不用处理controlStatus为失效状态的情况，因为上面已经处理过了。
            if (!CollectionUtils.isEmpty(t.getAssignOrgList()) && enableStatus.equals(t.getControlStatus())) {
                Supplier finalSupplier = supplier;
                List<SupplierCustomerUseInfo> useInfoList = supplierCustomerUseInfoDAO.findGroupSupplierCustomerUseList(operationModel.getEnterpriseNo(), Lists.newArrayList(t.getSupplierCode()), CompanyTypeEnum.SUPPLIER.getValue());
                Set<String> usedEnterpriseNoSet = useInfoList.stream().map(SupplierCustomerUseInfo::getUseEnterpriseNo).collect(Collectors.toSet());
                List<String> needDisableEnterpriseNoList = new ArrayList<>();
                List<String> needEnableEnterpriseNoList = new ArrayList<>();
                t.getAssignOrgList().forEach(assignItem -> {
                    OrganizationTreeVo organizationTreeVo = organizationMap.get(assignItem.getOrgCode());
                    if (organizationTreeVo != null && StringUtils.isNotEmpty(organizationTreeVo.getBindingEnterpriseNo())) {
                        if("assign".equals(assignItem.getStatus())) {
                            // 如果已经分派了，则修改状态;如果未分派，则新增分派
                            if(!usedEnterpriseNoSet.contains(organizationTreeVo.getBindingEnterpriseNo())) {
                                // 新增分派
                                SaveExecuteRecordRequest recordRequest = new SaveExecuteRecordRequest();
                                recordRequest.setDataType(ExecuteRecordDataTypeEnum.SUPPLIER);
                                recordRequest.setExecuteType(ExecuteRecordExcuteTypeEnum.AUTOMATIC_DISPATCH);
                                recordRequest.setApplyOrgNo(organizationTreeVo.getOrgNo());
                                recordRequest.setApplyOrgName(organizationTreeVo.getOrgName());
                                recordRequest.setApplySource("MDM->DSRP");
                                recordRequest.setApplyEnterpriseNo(organizationTreeVo.getBindingEnterpriseNo());
                                recordRequest.setGroupEnterpriseNo(operationModel.getEnterpriseNo());
                                recordRequest.setManageEnterpriseNo(operationModel.getEnterpriseNo());
                                recordRequest.setObjNo(finalSupplier.getSupplierNo());
                                recordRequest.setObjCode(finalSupplier.getSupplierCode());
                                recordRequest.setObjName(finalSupplier.getSupplierName());
                                recordRequest.setExecuteTime(DateUtil.getCurrentDate());
                                recordRequest.setCreateNo(operationModel.getEmployerNo());
                                recordRequest.setCreateName(operationModel.getUserName());
                                executeRecordList.add(recordRequest);
                            } else {
                                // 已经分派的是否需要修改状态
                                needEnableEnterpriseNoList.add(organizationTreeVo.getBindingEnterpriseNo());
                            }
                        } else if("disableAssign".equals(assignItem.getStatus())) {
                            // 如果未分派，则不处理；如果已分派，则禁用
                            if(usedEnterpriseNoSet.contains(organizationTreeVo.getBindingEnterpriseNo())) {
                                // 后续批量禁用
                                needDisableEnterpriseNoList.add(organizationTreeVo.getBindingEnterpriseNo());
                            }
                        }
                    }
                });
                if(!CollectionUtils.isEmpty(needDisableEnterpriseNoList)) {
                    supplierDAO.batchUpdateMultiOrganizeSupplierStatus(needDisableEnterpriseNoList, t.getSupplierCode(),disableStatus);
                }
                if(!CollectionUtils.isEmpty(needEnableEnterpriseNoList)) {
                    supplierDAO.batchUpdateMultiOrganizeSupplierStatus(needEnableEnterpriseNoList, t.getSupplierCode(),enableStatus);
                }
            }
        });
        if (!CollectionUtils.isEmpty(saveSupplierList)) {
            saveSupplierList.forEach(t -> CommonUtil.fillCreatInfo(operationModel, t));
            supplierDAO.addBatch(saveSupplierList);
        }
        if (!CollectionUtils.isEmpty(saveCompanyBankList)) {
            saveCompanyBankList.forEach(t -> CommonUtil.fillCreatInfo(operationModel, t));
            companyBankDAO.addBatch(saveCompanyBankList);
        }
        if (!CollectionUtils.isEmpty(saveResponsibleManListList)) {
            saveResponsibleManListList.forEach(t -> CommonUtil.fillOperateInfo(operationModel, t));
            supplierOrderManDAO.addBatch(saveResponsibleManListList);
        }
        if (!CollectionUtils.isEmpty(saveLinkmanListList)) {
            saveLinkmanListList.forEach(t -> CommonUtil.fillCreatInfo(operationModel, t));
            companyLinkmanDAO.addBatch(saveLinkmanListList);
        }
        if (!CollectionUtils.isEmpty(saveShippingAddressListList)) {
            saveShippingAddressListList.forEach(t -> CommonUtil.fillCreatInfo(operationModel, t));
            companyShippingAddressDAO.addBatch(saveShippingAddressListList);
        }

        // 迪安供应商批量保存接口(兼容MDM)，会在多组织的表里也插入一份数据
        supplierV2ExternalService.batchSaveMdmCompatible(operationModel, params, resultList);

        //分派逻辑
        if (!CollectionUtils.isEmpty(executeRecordList)) {
            saveExecuteRecordListAndStartExecute(executeRecordList);
        }


//        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
//            @Override
//            public void afterCommit() {
//                try {
                    // 迪安供应商批量保存接口(兼容MDM)，会在多组织的表里也插入一份数据
//                    supplierV2ExternalService.batchSaveMdmCompatible(operationModel, params, resultList);
//                } catch (Exception e) {
//                    log.error("mdm接口兼容多组织失败", e);
//                }
//            }
//        });


        return saveBusinessLogAndRetResult(resultList);
    }

    /**
     * 保存执行记录并启动执行
     * @param executeRecordList
     */
    private void saveExecuteRecordListAndStartExecute(List<SaveExecuteRecordRequest> executeRecordList) {
        List<ExecuteRecordResponse> executeRecordResponseList = masterDataExecuteService.saveExecuteRecordListNoExecute(executeRecordList);
        executeRecordResponseList.forEach(executeRecord -> {
            supplyExtendService.startSupplierAssignTask(executeRecord);
        });
    }

    /**
     * 保存业务日志，并返回转换后的对象
     * @param resultList
     * @return
     */
    private List<SupplierExternalSaveVO> saveBusinessLogAndRetResult(List<SupplierInternalSaveVO> resultList) {
        List<SupplierExternalSaveVO> retResultList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(resultList)){
            List<SupplierInternalSaveVO> successList = new ArrayList<>();
            List<SupplierInternalSaveVO> failList = new ArrayList<>();
            for (SupplierInternalSaveVO supplierInternalSaveVO : resultList){
                SupplierExternalSaveVO supplierExternalSaveVO = new SupplierExternalSaveVO();
                BeanUtils.copyProperties(supplierInternalSaveVO, supplierExternalSaveVO);
                retResultList.add(supplierExternalSaveVO);
                if (supplierInternalSaveVO.getSuccess() != null && !supplierInternalSaveVO.getSuccess()
                        && !StringUtils.isEmpty(supplierInternalSaveVO.getSupplyCode())){
                    failList.add(supplierInternalSaveVO);
                }else if (supplierInternalSaveVO.getSuccess() != null && supplierInternalSaveVO.getSuccess()){
                    successList.add(supplierInternalSaveVO);
                }
            }
            if (!CollectionUtils.isEmpty(successList)){
                mdmSupplierBusinessSuccessLog(successList);
            }
            if (!CollectionUtils.isEmpty(failList)){
                mdmSupplierBusinessFailLog(failList);
            }
        }
        return retResultList;
    }

    /**
     * mdm客户保存失败日志
     * @param failList
     */
    private void mdmSupplierBusinessFailLog(List<SupplierInternalSaveVO> failList) {
        if (CollectionUtils.isEmpty(failList)){
            return;
        }
        List<IntegrationLogDTO> errorList = new ArrayList<>();
        for (SupplierInternalSaveVO supplierInternalSaveVO : failList) {
            IntegrationLogDTO integrationLogDTO = new IntegrationLogDTO();
            // 上游请求id，open推进来的不用管
//                integrationLogDTO.setRequestId(UUID.randomUUID().toString());
            integrationLogDTO.setRequestType(IntegrationRequestType.OPEN.getRequestType());
            integrationLogDTO.setSuccess(false); // 是否成功
            integrationLogDTO.setErrorCode(supplierInternalSaveVO.getIntegrationError().getErrorCode()); // 错误编码
            integrationLogDTO.setErrorMsg(supplierInternalSaveVO.getMessage()); // 错误信息
            integrationLogDTO.setBusinessBillType(MdmBillType.ZSGYS.name());

            integrationLogDTO.setBusinessBillNo(supplierInternalSaveVO.getSupplyCode());
            integrationLogDTO.setBusinessBillName(supplierInternalSaveVO.getSupplyName());

            OperationModel operationModel = supplierInternalSaveVO.getOperationModel();
            integrationLogDTO.setEnterpriseNo(operationModel.getEnterpriseNo());
            integrationLogDTO.setSubjectSystem(MdmBillType.systemCode); // 对接系统

            integrationLogDTO.setBusinessOrgNo(operationModel.getOrgNo());
            integrationLogDTO.setBusinessOrgName(operationModel.getOrgName());
            // 交易对象
//                integrationLogDTO.setTradeObject(t.getCustomerName());
            errorList.add(integrationLogDTO);
        }
        uLogAPI.saveBatchForRpc(errorList);
    }

    /**
     * 推送保存成功日志
     * @param successList
     */
    private void mdmSupplierBusinessSuccessLog(List<SupplierInternalSaveVO> successList) {
        if (CollectionUtils.isEmpty(successList)) {
            return;
        }
        List<IntegrationLogDTO> successLogDTOList = new ArrayList<>();
        for (SupplierInternalSaveVO supplierInternalSaveVO : successList) {
            IntegrationLogDTO integrationLogDTO = new IntegrationLogDTO();
            // 上游请求id，open推进来的不用管
//                integrationLogDTO.setRequestId(UUID.randomUUID().toString());
            integrationLogDTO.setRequestType(IntegrationRequestType.OPEN.getRequestType());
            integrationLogDTO.setSuccess(true); // 是否成功
            integrationLogDTO.setBusinessBillType(MdmBillType.ZSGYS.name());

            integrationLogDTO.setBusinessBillNo(supplierInternalSaveVO.getSupplyCode());
            integrationLogDTO.setBusinessBillName(supplierInternalSaveVO.getSupplyName());
            OperationModel operationModel = supplierInternalSaveVO.getOperationModel();
            integrationLogDTO.setEnterpriseNo(operationModel.getEnterpriseNo());
            integrationLogDTO.setSubjectSystem(MdmBillType.systemCode); // 对接系统

            integrationLogDTO.setBusinessOrgNo(operationModel.getOrgNo());
            integrationLogDTO.setBusinessOrgName(operationModel.getOrgName());
            // 交易对象
//                integrationLogDTO.setTradeObject(t.getCustomerName());
            successLogDTOList.add(integrationLogDTO);
        }
        uLogAPI.saveBatchForRpc(successLogDTOList);
    }


    @Override
    public Boolean supplyAssign(OperationModel operationModel, SupplierExternalAssignDTO params) {
        ValidatorUtils.checkTrueThrowEx(CollectionUtils.isEmpty(params.getAssignOrgList()), "分派组织不为空");
        ValidatorUtils.checkTrueThrowEx(params.getAssignOrgList().size() > 20, "分派组织不能超过20个");
        //组织编码
        final List<OrganizationTreeVo> treeList = uimTenantService.getOrganizationTreeList(operationModel.getEnterpriseNo());
        final Map<String, OrganizationTreeVo> treeMap = treeList.stream().collect(Collectors.toMap(OrganizationTreeVo::getOrgCode, Function.identity()));
        final Supplier supplier = supplierDAO.getSupplierBySupplierCode(operationModel.getEnterpriseNo(), params.getSupplyCode());
        List<SaveExecuteRecordRequest> recordList = new ArrayList<>();
        params.getAssignOrgList().forEach(t -> {
            if (treeMap.containsKey(t.getOrgCode())) {
                final OrganizationTreeVo organizationTreeVo = treeMap.get(t.getOrgCode());
                SaveExecuteRecordRequest recordRequest = new SaveExecuteRecordRequest();
                recordRequest.setDataType(ExecuteRecordDataTypeEnum.SUPPLIER);
                recordRequest.setExecuteType(ExecuteRecordExcuteTypeEnum.AUTOMATIC_DISPATCH);
                recordRequest.setApplyOrgNo(organizationTreeVo.getOrgNo());
                recordRequest.setApplyOrgName(organizationTreeVo.getOrgName());
                recordRequest.setApplySource("MDM->DSRP");
                recordRequest.setApplyEnterpriseNo(organizationTreeVo.getBindingEnterpriseNo());
                recordRequest.setGroupEnterpriseNo(operationModel.getEnterpriseNo());
                recordRequest.setManageEnterpriseNo(operationModel.getEnterpriseNo());
                recordRequest.setObjNo(supplier.getSupplierNo());
                recordRequest.setObjCode(supplier.getSupplierCode());
                recordRequest.setObjName(supplier.getSupplierName());
                recordRequest.setExecuteTime(DateUtil.getCurrentDate());
                recordRequest.setCreateNo(operationModel.getEmployerNo());
                recordRequest.setCreateName(operationModel.getUserName());
                recordList.add(recordRequest);
            }
        });
        if (!CollectionUtils.isEmpty(recordList)) {
            saveExecuteRecordListAndStartExecute(recordList);
        }
        return Boolean.TRUE;
    }
}
