package com.yyigou.dsrp.cdc.service.supplier.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.util.TypeUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.yyigou.ddc.common.exception.BusinessException;
import com.yyigou.ddc.common.message.MessageUtil;
import com.yyigou.ddc.common.page.PageDto;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.ddc.common.util.DateUtil;
import com.yyigou.ddc.common.util.ValidatorUtils;
import com.yyigou.ddc.services.ddc.dict.vo.AreaCodeVo;
import com.yyigou.ddc.services.ddc.ecs.api.EnterpriseAPI;
import com.yyigou.ddc.services.ddc.ecs.dto.EnterpriseDto;
import com.yyigou.ddc.services.ddc.ecs.vo.EnterpriseVo;
import com.yyigou.ddc.services.ddc.psr.api.CustomerAPI;
import com.yyigou.ddc.services.ddc.psr.dto.CustomerRelationDto;
import com.yyigou.ddc.services.ddc.psr.vo.CustomerRelationVo;
import com.yyigou.ddc.services.ddc.uim.enums.TenantTypeEnum;
import com.yyigou.ddc.services.ddc.uim.vo.EmployeeVo;
import com.yyigou.ddc.services.ddc.uim.vo.OrganizationTreeVo;
import com.yyigou.ddc.services.ddc.uim.vo.OrganizationVo;
import com.yyigou.ddc.services.ddc.uim.vo.TenantVo;
import com.yyigou.ddc.services.dsrp.gsp.vo.CertControlTypeVo;
import com.yyigou.ddc.services.dw.doris.enums.DorisReadModeEnum;
import com.yyigou.ddc.services.dw.doris.util.DorisHelper;
import com.yyigou.ddc.services.openlink.api.SupplierMappingAPI;
import com.yyigou.ddc.services.openlink.dto.SupplierMappingDbDto;
import com.yyigou.ddc.services.openlink.enums.BindStatusEnum;
import com.yyigou.ddc.services.openlink.vo.SupplierMappingDbVo;
import com.yyigou.dsrp.cdc.api.common.vo.OrganizationVO;
import com.yyigou.dsrp.cdc.api.company.dto.BankDTO;
import com.yyigou.dsrp.cdc.api.company.dto.CompanyLinkmanDTO;
import com.yyigou.dsrp.cdc.api.company.dto.CompanyShippingAddressDTO;
import com.yyigou.dsrp.cdc.api.company.vo.BankVO;
import com.yyigou.dsrp.cdc.api.company.vo.CompanyLinkmanVO;
import com.yyigou.dsrp.cdc.api.company.vo.CompanyShippingAddressVO;
import com.yyigou.dsrp.cdc.api.supply.dto.*;
import com.yyigou.dsrp.cdc.api.supply.vo.*;
import com.yyigou.dsrp.cdc.client.common.response.QueryUseInfoResponse;
import com.yyigou.dsrp.cdc.client.supplier.request.SupplierComponentRequest;
import com.yyigou.dsrp.cdc.client.supplier.request.SupplierFindRequest;
import com.yyigou.dsrp.cdc.client.supplier.request.SupplierNameRequest;
import com.yyigou.dsrp.cdc.client.supplier.request.SupplierNoRequest;
import com.yyigou.dsrp.cdc.client.supplier.response.SupplierComponentResponse;
import com.yyigou.dsrp.cdc.client.supplier.response.SupplierInfoResponse;
import com.yyigou.dsrp.cdc.client.supplier.response.SupplierResponse;
import com.yyigou.dsrp.cdc.client.supplier.response.SupplierSimpleComponentResponse;
import com.yyigou.dsrp.cdc.common.enums.*;
import com.yyigou.dsrp.cdc.common.enums.customer.*;
import com.yyigou.dsrp.cdc.common.enums.supplier.SupplierBusinessFlagEnum;
import com.yyigou.dsrp.cdc.common.enums.supplier.SupplierControlStatusEnum;
import com.yyigou.dsrp.cdc.common.util.BeanUtil;
import com.yyigou.dsrp.cdc.common.util.CommonUtil;
import com.yyigou.dsrp.cdc.common.util.OperationModel;
import com.yyigou.dsrp.cdc.dao.common.BankTypeDAO;
import com.yyigou.dsrp.cdc.dao.common.entity.BankType;
import com.yyigou.dsrp.cdc.dao.company.CompanyDAO;
import com.yyigou.dsrp.cdc.dao.company.CompanyLinkmanDAO;
import com.yyigou.dsrp.cdc.dao.company.CompanyShippingAddressDAO;
import com.yyigou.dsrp.cdc.dao.company.entity.Company;
import com.yyigou.dsrp.cdc.dao.company.entity.CompanyLinkman;
import com.yyigou.dsrp.cdc.dao.company.entity.CompanyShippingAddress;
import com.yyigou.dsrp.cdc.dao.customer.CustomerDAO;
import com.yyigou.dsrp.cdc.dao.customer.entity.Customer;
import com.yyigou.dsrp.cdc.dao.gradedcontrol.SupplierCustomerUseInfoDAO;
import com.yyigou.dsrp.cdc.dao.gradedcontrol.entity.SupplierCustomerUseInfo;
import com.yyigou.dsrp.cdc.dao.supplier.CompanyBankDAO;
import com.yyigou.dsrp.cdc.dao.supplier.SupplierCategoryDAO;
import com.yyigou.dsrp.cdc.dao.supplier.SupplierDAO;
import com.yyigou.dsrp.cdc.dao.supplier.SupplierOrderManDAO;
import com.yyigou.dsrp.cdc.dao.supplier.entity.CompanyBank;
import com.yyigou.dsrp.cdc.dao.supplier.entity.Supplier;
import com.yyigou.dsrp.cdc.dao.supplier.entity.SupplierCategory;
import com.yyigou.dsrp.cdc.dao.supplier.entity.SupplierOrderMan;
import com.yyigou.dsrp.cdc.manager.integration.certControl.CertControlService;
import com.yyigou.dsrp.cdc.manager.integration.dict.DictEnterpriseService;
import com.yyigou.dsrp.cdc.manager.integration.dict.enums.DictNumberEnum;
import com.yyigou.dsrp.cdc.manager.integration.numberCenter.NumberCenterService;
import com.yyigou.dsrp.cdc.manager.integration.org.UimTenantService;
import com.yyigou.dsrp.cdc.manager.integration.org.res.OrganizationRes;
import com.yyigou.dsrp.cdc.manager.integration.uim.CustomDocService;
import com.yyigou.dsrp.cdc.manager.integration.uim.EmployeeService;
import com.yyigou.dsrp.cdc.manager.integration.uim.TenantService;
import com.yyigou.dsrp.cdc.manager.integration.uim.TransactionTypeService;
import com.yyigou.dsrp.cdc.manager.integration.uim.response.CustomDocResponse;
import com.yyigou.dsrp.cdc.manager.integration.uim.response.TransactionTypeResponse;
import com.yyigou.dsrp.cdc.model.constant.NumberCenterConstant;
import com.yyigou.dsrp.cdc.model.constant.SystemConstant;
import com.yyigou.dsrp.cdc.model.constant.ViewNameConstant;
import com.yyigou.dsrp.cdc.model.enums.FactoryTypeEnum;
import com.yyigou.dsrp.cdc.model.supplier.req.SupplierMultiOrgQueryReq;
import com.yyigou.dsrp.cdc.service.common.CdcLogService;
import com.yyigou.dsrp.cdc.service.common.MasterDataSyncExtService;
import com.yyigou.dsrp.cdc.service.company.CompanyService;
import com.yyigou.dsrp.cdc.service.supplier.SupplierService;
import com.yyigou.dsrp.cdc.service.utils.PageUtils;
import com.yyigou.dsrp.gcs.common.enums.StatusEnum;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class SupplierServiceImpl extends ServiceImpl<SupplierDAO, Supplier> implements SupplierService {

    private final CompanyDAO companyDAO;
    private final NumberCenterService numberCenterService;
    private final CompanyShippingAddressDAO companyShippingAddressDAO;
    private final CompanyBankDAO companyBankDAO;
    private final CompanyLinkmanDAO companyLinkmanDAO;
    private final SupplierOrderManDAO supplierOrderManDAO;
    private final CustomDocService customDocService;
    private final TransactionTypeService transactionTypeService;
    private final DictEnterpriseService dictEnterpriseService;
    private final SupplierCategoryDAO supplierCategoryDAO;
    private final CdcLogService ccLogService;
    private final MasterDataSyncExtService masterDataSyncExtService;
    private final TenantService tenantService;
    private final CompanyService companyService;
    private final BankTypeDAO bankTypeDAO;
    private final EmployeeService employeeService;
    private final UimTenantService uimTenantService;
    @Resource
    private SupplierDAO supplierDAO;
    @Resource
    private SupplierCustomerUseInfoDAO supplierCustomerUseInfoDAO;
    @Resource
    private SupplierMappingAPI supplierMappingAPI;
    @Resource
    private EnterpriseAPI ecsEnterpriseAPI;
    @Resource
    private CustomerAPI ddcCustomerAPI;
    @Autowired
    private CustomerDAO customerDAO;
    private final CertControlService certControlService;

    /**
     * 根据供应商名称查询供应商信息
     *
     * @param request
     * @return: {@link List< SupplierInfoResponse>}
     */
    @Override
    public List<SupplierInfoResponse> findSupplierByName(SupplierNameRequest request) {
        CommonUtil.checkEmptyThrowEx(request.getEnterpriseNo(), "租户编号不能为空");
        if (StringUtils.isBlank(request.getSupplierName()) && StringUtils.isBlank(request.getSupplierNameKeyword())) {
            throw new BusinessException("供应商名称不能为空");
        }
        LambdaQueryWrapper<Supplier> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(Supplier::getEnterpriseNo, request.getEnterpriseNo());
        if (StringUtils.isNotBlank(request.getSupplierNameKeyword())) {
            lambdaQueryWrapper.and(
                    i -> i.like(StringUtils.isNotBlank(request.getSupplierNameKeyword()), Supplier::getSupplierNo, request.getSupplierNameKeyword())
                            .or().like(StringUtils.isNotBlank(request.getSupplierNameKeyword()), Supplier::getSupplierName, request.getSupplierNameKeyword())
                            .or().like(StringUtils.isNotBlank(request.getSupplierNameKeyword()), Supplier::getSupplierCode, request.getSupplierNameKeyword())
                            .or().like(StringUtils.isNotBlank(request.getSupplierNameKeyword()), Supplier::getMnemonicCode, request.getSupplierNameKeyword())
            );
        }
        lambdaQueryWrapper.eq(StringUtils.isNotBlank(request.getSupplierName()), Supplier::getSupplierName, request.getSupplierName())
                .eq(Supplier::getControlStatus, TypeUtils.castToString(ControlStatusEnum.ENABLE.getValue()))
                .eq(Supplier::getBusinessFlag, SupplierBusinessFlagEnum.FORMAL.getValue()).eq(Supplier::getDeleted, DeletedEnum.UN_DELETE.getValue());
        List<Supplier> supplierList = supplierDAO.selectList(lambdaQueryWrapper);
        if (CollectionUtils.isEmpty(supplierList)) {
            return Collections.emptyList();
        }
        return supplierList.stream().map(supplier -> {
            SupplierInfoResponse response = new SupplierInfoResponse();
            BeanUtils.copyProperties(supplier, response);
            return response;
        }).collect(Collectors.toList());
    }

    /**
     * 根据供应商编号集合查询供应商信息
     *
     * @param request
     * @return: {@link List< SupplierInfoResponse>}
     */
    @Override
    public List<SupplierInfoResponse> findSupplierByNo(SupplierNoRequest request) {
        CommonUtil.checkEmptyThrowEx(request.getEnterpriseNo(), "租户编号不能为空");
        if (StringUtils.isBlank(request.getSupplierNo()) && StringUtils.isBlank(request.getSupplierCode())
                && CollectionUtils.isEmpty(request.getSupplierNoList()) && CollectionUtils.isEmpty(request.getSupplierCodeList())) {
            throw new BusinessException("供应商编码不能为空");
        }
        LambdaQueryWrapper<Supplier> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(Supplier::getEnterpriseNo, request.getEnterpriseNo())
                .eq(StringUtils.isNotBlank(request.getSupplierNo()), Supplier::getSupplierNo, request.getSupplierNo())
                .eq(StringUtils.isNotBlank(request.getSupplierCode()), Supplier::getSupplierCode, request.getSupplierCode())
                .in(CollectionUtils.isNotEmpty(request.getSupplierNoList()), Supplier::getSupplierNo, request.getSupplierNoList())
                .in(CollectionUtils.isNotEmpty(request.getSupplierCodeList()), Supplier::getSupplierCode, request.getSupplierCodeList())
                .eq(Supplier::getControlStatus, TypeUtils.castToString(ControlStatusEnum.ENABLE.getValue()))
//                .eq(Supplier::getBusinessFlag, SupplierBusinessFlagEnum.FORMAL.getValue())
                .eq(Supplier::getDeleted, DeletedEnum.UN_DELETE.getValue());
        List<Supplier> supplierList = supplierDAO.selectList(lambdaQueryWrapper);
        if (CollectionUtils.isEmpty(supplierList)) {
            return Collections.emptyList();
        }
        return supplierList.stream().map(supplier -> {
            SupplierInfoResponse response = new SupplierInfoResponse();
            BeanUtils.copyProperties(supplier, response);
            return response;
        }).collect(Collectors.toList());
    }

    /**
     * 按条件查询供应商信息
     *
     * @param enterpriseNo
     * @param request
     * @return
     */
    @Override
    public List<SupplierResponse> findSupplier(String enterpriseNo, SupplierFindRequest request) {
        CommonUtil.checkEmptyThrowEx(enterpriseNo, "租户编号不得为空");

        LambdaQueryWrapper<Supplier> lambdaQueryWrapper = Wrappers.lambdaQuery(Supplier.class)
                .eq(Supplier::getEnterpriseNo, enterpriseNo)
                .eq(Supplier::getDeleted, 0);

        if (CollectionUtils.isNotEmpty(request.getSupplierNoList())) {
            lambdaQueryWrapper = lambdaQueryWrapper.in(Supplier::getSupplierNo, request.getSupplierNoList());
        }
        if (CollectionUtils.isNotEmpty(request.getNotInSupplierNoList())) {
            lambdaQueryWrapper = lambdaQueryWrapper.notIn(Supplier::getSupplierNo, request.getNotInSupplierNoList());
        }
        if (StringUtils.isNotEmpty(request.getSupplierName())) {
            lambdaQueryWrapper = lambdaQueryWrapper.eq(Supplier::getSupplierName, request.getSupplierName());
        }

        if (StringUtils.isNotEmpty(request.getSupplierNameKeyword())) {
            lambdaQueryWrapper = lambdaQueryWrapper.and(o -> o.like(Supplier::getSupplierName, request.getSupplierNameKeyword()));
        }

        if (CollectionUtils.isNotEmpty(request.getBusinessFlagList())) {
            lambdaQueryWrapper = lambdaQueryWrapper.and(o -> o.in(Supplier::getBusinessFlag, request.getBusinessFlagList()));
        }


        if (StringUtils.isNotEmpty(request.getSupplierKeywords())) {
            lambdaQueryWrapper = lambdaQueryWrapper.and(
                    o -> o.like(Supplier::getSupplierName, request.getSupplierKeywords()).or()
                            .like(Supplier::getSupplierCode, request.getSupplierKeywords()).or()
                            .like(Supplier::getSupplierNo, request.getSupplierKeywords())
            );
        }
        List<Supplier> suppliers = supplierDAO.selectList(lambdaQueryWrapper);
        return BeanUtil.copyFieldsList(suppliers, SupplierResponse.class);
    }

    @Override
    public List<String> findSupplierUserList(String enterpriseNo, String supplierCode) {
        CommonUtil.checkEmptyThrowEx(enterpriseNo, "租户编号不能为空");
        CommonUtil.checkEmptyThrowEx(supplierCode, "供应商编码不能为空");
        List<SupplierCustomerUseInfo> useInfoList = supplierCustomerUseInfoDAO.findGroupSupplierCustomerBySubTenant(enterpriseNo, Lists.newArrayList(supplierCode), CompanyTypeEnum.SUPPLIER.getValue());
        if (CollectionUtils.isEmpty(useInfoList)) {
            return new ArrayList<>();
        }
        SupplierCustomerUseInfo useInfo = useInfoList.get(0);
        List<SupplierCustomerUseInfo> usedSkuCodeList = supplierCustomerUseInfoDAO.findGroupSupplierCustomerUseList(useInfo.getGroupEnterpriseNo(), Lists.newArrayList(supplierCode), CompanyTypeEnum.SUPPLIER.getValue());
        return usedSkuCodeList.stream().map(SupplierCustomerUseInfo::getUseEnterpriseNo).collect(Collectors.toList());
    }

    /**
     * 根据企业编号查询供应商档案
     *
     * @param enterpriseNo
     * @param companyNo
     * @return: {@link List<  Supplier >}
     */
    @Override
    public List<Supplier> findSupplierByCompanyNo(String enterpriseNo, String companyNo) {
        ValidatorUtils.checkEmptyThrowEx(enterpriseNo, "租户编号不能为空");
        ValidatorUtils.checkEmptyThrowEx(companyNo, "企业编号不能为空");
        return supplierDAO.findSupplierByCompanyNo(enterpriseNo, companyNo);
    }

    @Override
    public int updateSupplier(Supplier supplier) {
        ValidatorUtils.checkEmptyThrowEx(supplier, "修改供应商档案，参数不能为空");
        return supplierDAO.updateById(supplier);
    }

    /**
     * 根据供应商编码查询供应商信息
     *
     * @param enterpriseNo
     * @param supplierCode
     * @return: {@link Supplier}
     */
    @Override
    public Supplier getSupplierBySupplierCode(String enterpriseNo, String supplierCode) {
        ValidatorUtils.checkEmptyThrowEx(enterpriseNo, "租户编号不能为空");
        ValidatorUtils.checkEmptyThrowEx(supplierCode, "供应商编码不能为空");
        return supplierDAO.getSupplierBySupplierCode(enterpriseNo, supplierCode);
    }

    @Override
    public List<SupplierSimpleComponentResponse> querySupplierList(List<String> enterpriseNoList, String supplierCode) {
        final List<Supplier> supplierList = supplierDAO.selectList(new LambdaQueryWrapper<Supplier>().eq(Supplier::getSupplierCode, supplierCode).in(Supplier::getEnterpriseNo, enterpriseNoList).eq(Supplier::getDeleted, DeletedEnum.UN_DELETE.getValue()));
        List<SupplierSimpleComponentResponse> result = new ArrayList<>();
        supplierList.forEach(t -> {
            SupplierSimpleComponentResponse response = new SupplierSimpleComponentResponse();
            BeanUtils.copyProperties(t, response);
            result.add(response);
        });
        return result;
    }

    /**
     * 根据供应商编码查询供应商档案分配组织租户编号
     *
     * @param groupEnterpriseNo
     * @param supplierCode
     * @return: {@link List< String>}
     */
    @Override
    public List<String> findSupplierAssignOrgEnterpriseNo(String groupEnterpriseNo, String supplierCode) {
        ValidatorUtils.checkEmptyThrowEx(groupEnterpriseNo, "集团租户编号不能为空");
        ValidatorUtils.checkEmptyThrowEx(supplierCode, "供应商编码不能为空");
        List<SupplierCustomerUseInfo> customerUseList = supplierCustomerUseInfoDAO.findGroupSupplierCustomerUseList(groupEnterpriseNo, Lists.newArrayList(supplierCode), CompanyTypeEnum.SUPPLIER.getValue());
        return customerUseList.stream().map(SupplierCustomerUseInfo::getUseEnterpriseNo).collect(Collectors.toList());
    }

    @Override
    public String saveSupplier(OperationModel operationModel, SaveSupplierDTO params, String logRecord) {
        ValidatorUtils.checkTrueThrowEx(supplierDAO.selectCount(new LambdaQueryWrapper<Supplier>().eq(Supplier::getSupplierCode, params.getSupplierCode()).eq(Supplier::getEnterpriseNo, operationModel.getEnterpriseNo()).eq(Supplier::getDeleted, DeletedEnum.UN_DELETE.getValue())) > 0, "供应商编码重复");
        ValidatorUtils.checkTrueThrowEx(supplierDAO.selectCount(new LambdaQueryWrapper<Supplier>().eq(Supplier::getSupplierName, params.getSupplierName()).eq(Supplier::getEnterpriseNo, operationModel.getEnterpriseNo()).eq(Supplier::getDeleted, DeletedEnum.UN_DELETE.getValue())) > 0, "供应商名称重复");

        //先处理企业
//        通过企业名称和统一社会信用代码判断企业档案是否存在
//        1、不存在企业档案，创建供应商档案和企业档案，并做关联
//        2、存在企业档案，创建供应商档案并关联企业档案
//        3、企业名称或者统一社会信用代码不一致，校验报错
        Company company = new Company();
        List<Company> companyList = companyDAO.findByEnterpriseNoAndUnifiedSocialCodeAndCompanyName(operationModel.getEnterpriseNo(), params.getUnifiedSocialCode(), params.getCompanyName());
        if (CollectionUtils.isNotEmpty(companyList)) {
            company = companyList.get(0);
        } else {
            //不存在有2中情况 1.企业存在 2.统一社会信用代码存在  3.都不存在得新创建了
            if (StringUtils.isNotEmpty(params.getUnifiedSocialCode())) {
                //统一社会信用代码存在
                companyList = companyDAO.findByEnterpriseNoAndUnifiedSocialCode(operationModel.getEnterpriseNo(), params.getUnifiedSocialCode());
                if (CollectionUtils.isNotEmpty(companyList)) {
                    company = companyList.get(0);
                    CommonUtil.checkThrowEx(CollectionUtils.isNotEmpty(companyList), String.format("企业名称和统一社会信用代码重复,企业名称:%s,统一社会信用代码:%s", company.getCompanyName(), company.getUnifiedSocialCode()));
                }
                companyList = companyDAO.findByEnterpriseNoAndCompanyName(operationModel.getEnterpriseNo(), params.getCompanyName());
                if (CollectionUtils.isNotEmpty(companyList)) {
                    company = companyList.get(0);
                    CommonUtil.checkThrowEx(CollectionUtils.isNotEmpty(companyList), String.format("企业名称和统一社会信用代码重复,企业名称:%s,统一社会信用代码:%s", company.getCompanyName(), company.getUnifiedSocialCode()));
                }
                //创建企业档案
                company.setEnterpriseNo(operationModel.getEnterpriseNo());
                company.setCompanyNo(numberCenterService.createNumber(NumberCenterConstant.COMPANY_NO_GENERATE_KEY));
                company.setCompanyCode(company.getCompanyCode());
                company.setCompanyName(params.getCompanyName());
                company.setUnifiedSocialCode(params.getUnifiedSocialCode());
                company.setFactoryType(params.getFactoryType());
                company.setCountry(params.getCountry());
                company.setTaxCategory(params.getTaxCategory());
                company.setEconomicType(params.getEconomicType());
                company.setIsAssociatedEnterprise(params.getIsAssociatedEnterprise());
                company.setAssociatedOrgNo(params.getAssociatedOrgNo());
                company.setAssociatedOrgCode(params.getAssociatedOrgCode());
                company.setAssociatedOrgName(params.getAssociatedOrgName());
                //通过供应商创建的企业默认都是非医疗机构
                company.setIsMedicalInstitution(0);
                CommonUtil.fillCreatInfo(operationModel, company);
                company.setStatus(StatusEnum.EFFECTIVE.getValue());
                company.setDeleted(DeletedEnum.UN_DELETE.getValue());
                companyDAO.insert(company);
            }
        }

        Supplier supplier = new Supplier();
        supplier.setCompanyNo(company.getCompanyNo());
        supplier.setSupplierNo(numberCenterService.createNumber(NumberCenterConstant.SUPPLY_NO_KEY));
        supplier.setEnterpriseNo(operationModel.getEnterpriseNo());
        supplier.setUnifiedSocialCode(params.getUnifiedSocialCode());
        // 供应商基本信息
        supplier.setSupplierCode(params.getSupplierCode());
        supplier.setSupplierName(params.getSupplierName());
        supplier.setSupplierCategoryNo(params.getSupplierCategoryNo());
        supplier.setMnemonicCode(params.getMnemonicCode());
        supplier.setSupplierNameEn(params.getSupplierNameEn());
        supplier.setOwnerCompany(params.getOwnerCompany());
        supplier.setIsGspControl(params.getGspStatus());
        supplier.setRemark(params.getRemark());
        supplier.setTransactionType(params.getTransactionType());
        supplier.setRetailInvestors(params.getRetailInvestors());
        //业务信息
        supplier.setControlId(params.getControlId());
        supplier.setControlTypeName(params.getControlTypeName());
        supplier.setCooperationMode(params.getCooperationMode());
        supplier.setCurrency(params.getCurrency());
        supplier.setSettlementModes(params.getSettlementModes());
        supplier.setSettlementModesName(params.getSettlementModesName());
        supplier.setCreditAmount(params.getCreditAmount());
        supplier.setPeriodDays(params.getPeriodDays());
        supplier.setCoopStartTime(params.getCoopStartTime());
        supplier.setCoopEndTime(params.getCoopEndTime());
        supplier.setPaymentAgreementId(params.getPaymentAgreementId());
        supplier.setPaymentAgreementCode(params.getPaymentAgreementCode());
        supplier.setPaymentAgreementName(params.getPaymentAgreementName());
        supplier.setPaymentAgreementYsId(params.getPaymentAgreementYsId());
        supplier.setPaymentTerm(params.getPaymentTerm());
        supplier.setBusinessFlag(CustomerBusinessFlagEnum.DRAFT.getValue().equals(params.getBusinessFlag()) ? CustomerBusinessFlagEnum.DRAFT.getValue() : CustomerBusinessFlagEnum.FORMAL.getValue());
        supplier.setDeleted(DeletedEnum.UN_DELETE.getValue());
        supplier.setCreateNo(operationModel.getEmployerNo());
        supplier.setCreateName(operationModel.getUserName());
        supplier.setCreateTime(DateUtil.getCurrentDate());
        supplier.setIsGspControl(1);
        //业务区域
        supplierDAO.insert(supplier);
        //供应商联系人
        if (CollectionUtils.isNotEmpty(params.getLinkmanList())) {
            List<CompanyLinkman> companyLinkmanList = new ArrayList<>();
            params.getLinkmanList().forEach(r -> {
                //应该走新增逻辑
                CompanyLinkman companyLinkman = new CompanyLinkman();
                companyLinkman.setEnterpriseNo(operationModel.getEnterpriseNo());
                companyLinkman.setCompanyNo(supplier.getCompanyNo());
                companyLinkman.setLinkCode(r.getLinkCode());
                companyLinkman.setLinkman(r.getLinkman());
                companyLinkman.setPosition(r.getPosition());
                companyLinkman.setMobilePhone(r.getMobilePhone());
                companyLinkman.setSex(r.getSex());
                companyLinkman.setFixedPhone(r.getFixedPhone());
                companyLinkman.setQq(r.getQq());
                companyLinkman.setWx(r.getWx());
                companyLinkman.setLinkmanType(LinkmanTypeEnum.SUPPLY.getValue());
                companyLinkman.setStatus(CommonStatusEnum.EFFECTIVE.getValue());
                companyLinkman.setSourceNo(supplier.getSupplierNo());
                companyLinkman.setEmail(r.getEmail());
                companyLinkman.setIsDefault(r.getIsDefault());
                companyLinkmanList.add(companyLinkman);
            });
            companyLinkmanDAO.addBatch(companyLinkmanList);
        }
        //联系地址
        if (CollectionUtils.isNotEmpty(params.getLinkAddressList())) {
            List<CompanyShippingAddress> saveShippingAddressListList = new ArrayList<>();
            for (CompanyShippingAddressDTO r : params.getLinkAddressList()) {
                CompanyShippingAddress shippingAddress = new CompanyShippingAddress();
                shippingAddress.setEnterpriseNo(operationModel.getEnterpriseNo());
                shippingAddress.setCompanyNo(supplier.getCompanyNo());
                shippingAddress.setLinkAddressCode(r.getLinkAddressCode());
                shippingAddress.setSourceNo(supplier.getSupplierNo());
                shippingAddress.setStatus(CommonStatusEnum.EFFECTIVE.getValue());
                shippingAddress.setLinkaddType(LinkmanTypeEnum.SUPPLY.getValue());
                shippingAddress.setReceiveUser(r.getReceiveUser());
                shippingAddress.setReceivePhone(r.getReceivePhone());
                shippingAddress.setRegionCode(r.getRegionCode());
                shippingAddress.setRegionName(r.getRegionName());
                shippingAddress.setReceiveAddr(r.getReceiveAddr());
                shippingAddress.setIsDefault(r.getIsDefault());
                shippingAddress.setAddressDesc(r.getAddressDesc());
                shippingAddress.setAddressType(r.getAddressType());
                saveShippingAddressListList.add(shippingAddress);
            }
            if (CollectionUtils.isNotEmpty(saveShippingAddressListList)) {
                companyShippingAddressDAO.addBatch(saveShippingAddressListList);
            }
        }
        //银行信息
        if (CollectionUtils.isNotEmpty(params.getBankList())) {
            List<CompanyBank> companyBankList = new ArrayList<>();
            params.getBankList().forEach(b -> {
                CompanyBank companyBank = new CompanyBank();
                companyBank.setEnterpriseNo(operationModel.getEnterpriseNo());
                companyBank.setLinkaddType(LinkmanTypeEnum.SUPPLY.getValue());
                companyBank.setCompanyNo(supplier.getCompanyNo());
                companyBank.setSourceNo(supplier.getSupplierNo());
                companyBank.setBankCode(b.getBankCode());
                companyBank.setBankType(b.getBankType());
                companyBank.setOpenBank(b.getOpenBank());
                companyBank.setAccountName(b.getAccountName());
                companyBank.setAccountNo(b.getAccountNo());
                companyBank.setAccountType(b.getAccountType());
                companyBank.setLinkPerson(b.getLinkPerson());
                companyBank.setLinkPhone(b.getLinkPhone());
                companyBank.setStatus(b.getStatus());
                companyBank.setIsDefault(b.getIsDefault());
                companyBankList.add(companyBank);
            });
            if (CollectionUtils.isNotEmpty(companyBankList)) {
                companyBankDAO.addBatch(companyBankList);
            }
        }
        //负责人信息
        if (CollectionUtils.isNotEmpty(params.getSupplierManList())) {
            List<SupplierOrderMan> supplierOrderManList = new ArrayList<>();
            for (SupplierSalesManDTO r : params.getSupplierManList()) {
                SupplierOrderMan supplierOrderMan = new SupplierOrderMan();
                supplierOrderMan.setSupplierNo(supplier.getSupplierNo());
                supplierOrderMan.setManCode(UUID.randomUUID().toString());
                supplierOrderMan.setOrderManNo(r.getOrderManNo());
                supplierOrderMan.setOrderManName(r.getOrderManName());
                supplierOrderMan.setOrgNo(r.getDeptNo());
                supplierOrderMan.setOrgName(r.getDeptName());
                supplierOrderMan.setPost(r.getPost());
                supplierOrderMan.setIsDefault(r.getIsDefault());
                supplierOrderMan.setOrderSpecialist(r.getOrderSpecialist());
                supplierOrderMan.setPost(r.getPost());
                supplierOrderManList.add(supplierOrderMan);
            }
            if (CollectionUtils.isNotEmpty(supplierOrderManList)) {
                supplierOrderManDAO.addBatch(supplierOrderManList);
            }
        }
        // 保存资料文件
        companyService.saveOrUpdateCert(params.getCatalogInfoBigClientReq(), operationModel.getEnterpriseNo(), company.getCompanyNo(), operationModel);
        ccLogService.saveSupplierLog(supplier.getSupplierNo(), operationModel, logRecord);
        return supplier.getSupplierNo();

    }

    @Override
    public Boolean updateSupplier(OperationModel operationModel, SaveSupplierDTO params) {
        Supplier supplier = supplierDAO.getSupplierByNo(operationModel.getEnterpriseNo(), params.getSupplierNo());
        ValidatorUtils.checkTrueThrowEx(supplier == null, "供应商不存在");

        final Company company = companyDAO.findByEnterpriseNoAndCompanyNo(operationModel.getEnterpriseNo(), supplier.getCompanyNo());
        //更新企业信息
        if (company != null) {
            company.setCountry(params.getCountry());
            company.setFactoryType(params.getFactoryType());
            company.setEconomicType(params.getEconomicType());
            company.setTaxCategory(params.getTaxCategory());
            company.setIsAssociatedEnterprise(params.getIsAssociatedEnterprise());
            company.setAssociatedOrgNo(params.getAssociatedOrgNo());
            company.setAssociatedOrgCode(params.getAssociatedOrgCode());
            company.setAssociatedOrgName(params.getAssociatedOrgName());
            companyDAO.updateById(company);
        }
        supplier.setUnifiedSocialCode(params.getUnifiedSocialCode());
        // 供应商基本信息
        supplier.setSupplierName(params.getSupplierName());
        supplier.setSupplierCategoryNo(params.getSupplierCategoryNo());
        supplier.setMnemonicCode(params.getMnemonicCode());
        supplier.setSupplierNameEn(params.getSupplierNameEn());
        supplier.setOwnerCompany(params.getOwnerCompany());
        supplier.setIsGspControl(params.getGspStatus());
        supplier.setRemark(params.getRemark());
        supplier.setTransactionType(params.getTransactionType());
        supplier.setRetailInvestors(params.getRetailInvestors());
        //业务信息
        supplier.setControlId(params.getControlId());
        supplier.setControlTypeName(params.getControlTypeName());
        supplier.setCooperationMode(params.getCooperationMode());
        supplier.setCurrency(params.getCurrency());
        supplier.setSettlementModes(params.getSettlementModes());
        supplier.setSettlementModesName(params.getSettlementModesName());
        supplier.setCreditAmount(params.getCreditAmount());
        supplier.setPeriodDays(params.getPeriodDays());
        supplier.setCoopStartTime(params.getCoopStartTime());
        supplier.setCoopEndTime(params.getCoopEndTime());
        supplier.setBusinessFlag(params.getBusinessFlag());
        supplier.setPaymentAgreementId(params.getPaymentAgreementId());
        supplier.setPaymentAgreementCode(params.getPaymentAgreementCode());
        supplier.setPaymentAgreementName(params.getPaymentAgreementName());
        supplier.setPaymentAgreementYsId(params.getPaymentAgreementYsId());
        supplier.setPaymentTerm(params.getPaymentTerm());
        //供应商联系人
        if (CollectionUtils.isEmpty(params.getLinkmanList())) {
            //删除联系人
            companyLinkmanDAO.deleteCompanyLinkmanListByCompanyNo(operationModel.getEnterpriseNo(), LinkmanTypeEnum.SUPPLY.getValue(), Collections.singletonList(supplier.getSupplierNo()));
        } else {
            //编辑了联系人 说明肯定至少有一个使用uuid决定是否修改或者新增
            List<CompanyLinkman> customerLinkmanList = companyLinkmanDAO.getCompanyLinkmanListByCompanyNo(operationModel.getEnterpriseNo(), supplier.getCompanyNo(), LinkmanTypeEnum.SUPPLY.getValue(), supplier.getSupplierNo());
            final Map<Long, CompanyLinkman> customerLinkmanMap = customerLinkmanList.stream().collect(Collectors.toMap(CompanyLinkman::getId, Function.identity()));
            final Set<Long> localCustomerIdLinkmanList = customerLinkmanList.stream().map(CompanyLinkman::getId).collect(Collectors.toSet());
            final Set<Long> itemCustomerIdLinkmanList = params.getLinkmanList().stream().map(CompanyLinkmanDTO::getId).collect(Collectors.toSet());
            Set<Long> difference = Sets.difference(localCustomerIdLinkmanList, itemCustomerIdLinkmanList);
            if (!difference.isEmpty()) {
                companyLinkmanDAO.deleteCompanyLinkmanListByCompanyNo(operationModel.getEnterpriseNo(), LinkmanTypeEnum.SUPPLY.getValue(), Collections.singletonList(supplier.getSupplierNo()), new ArrayList<>(difference));
            }
            params.getLinkmanList().forEach(r -> {
                if (customerLinkmanMap.containsKey(r.getId())) {
                    final CompanyLinkman companyLinkman = customerLinkmanMap.get(r.getId());
                    companyLinkman.setLinkman(r.getLinkman());
                    companyLinkman.setPosition(r.getPosition());
                    companyLinkman.setMobilePhone(r.getMobilePhone());
                    companyLinkman.setSex(r.getSex());
                    companyLinkman.setFixedPhone(r.getFixedPhone());
                    companyLinkman.setQq(r.getQq());
                    companyLinkman.setWx(r.getWx());
                    companyLinkman.setLinkmanType(LinkmanTypeEnum.SUPPLY.getValue());
                    companyLinkman.setStatus(CommonStatusEnum.EFFECTIVE.getValue());
                    companyLinkman.setSourceNo(supplier.getSupplierNo());
                    companyLinkman.setEmail(r.getEmail());
                    companyLinkman.setIsDefault(r.getIsDefault());
                    companyLinkmanDAO.updateById(companyLinkman);
                } else {
                    //应该走新增逻辑
                    CompanyLinkman companyLinkman = new CompanyLinkman();
                    companyLinkman.setEnterpriseNo(operationModel.getEnterpriseNo());
                    companyLinkman.setCompanyNo(supplier.getCompanyNo());
                    companyLinkman.setLinkCode(r.getLinkCode());
                    companyLinkman.setLinkman(r.getLinkman());
                    companyLinkman.setPosition(r.getPosition());
                    companyLinkman.setMobilePhone(r.getMobilePhone());
                    companyLinkman.setSex(r.getSex());
                    companyLinkman.setFixedPhone(r.getFixedPhone());
                    companyLinkman.setQq(r.getQq());
                    companyLinkman.setWx(r.getWx());
                    companyLinkman.setLinkmanType(LinkmanTypeEnum.SUPPLY.getValue());
                    companyLinkman.setStatus(CommonStatusEnum.EFFECTIVE.getValue());
                    companyLinkman.setSourceNo(supplier.getSupplierNo());
                    companyLinkman.setEmail(r.getEmail());
                    companyLinkman.setIsDefault(r.getIsDefault());
                    companyLinkmanDAO.insert(companyLinkman);
                }
            });
        }
        //联系地址
        if (CollectionUtils.isEmpty(params.getLinkAddressList())) {
            //删除开票信息
            companyShippingAddressDAO.deleteShippingAddressListBySourceNoList(operationModel.getEnterpriseNo(), LinkmanTypeEnum.SUPPLY.getValue(), Collections.singletonList(supplier.getSupplierNo()));
        } else {
            final List<CompanyShippingAddress> shippingAddressList = companyShippingAddressDAO.getCompanyShippingAddressByCompanyNo(operationModel.getEnterpriseNo(), supplier.getCompanyNo(), LinkmanTypeEnum.SUPPLY.getValue(), supplier.getSupplierNo());
            final Map<Long, CompanyShippingAddress> shippingAddressMap = shippingAddressList.stream().collect(Collectors.toMap(CompanyShippingAddress::getId, Function.identity()));

            final Set<Long> localAddressList = shippingAddressList.stream().map(CompanyShippingAddress::getId).collect(Collectors.toSet());
            final Set<Long> itemAddressList = params.getLinkAddressList().stream().map(CompanyShippingAddressDTO::getId).collect(Collectors.toSet());
            Set<Long> difference = Sets.difference(localAddressList, itemAddressList);
            if (!difference.isEmpty()) {
                companyShippingAddressDAO.deleteShippingAddressListBySourceNoList(operationModel.getEnterpriseNo(), LinkmanTypeEnum.SUPPLY.getValue(), Collections.singletonList(supplier.getSupplierNo()), new ArrayList<>(difference));
            }
            List<CompanyShippingAddress> saveShippingAddressListList = new ArrayList<>();
            for (CompanyShippingAddressDTO r : params.getLinkAddressList()) {
                if (shippingAddressMap.containsKey(r.getId())) {
                    CompanyShippingAddress shippingAddress = shippingAddressMap.get(r.getId());
                    shippingAddress.setReceiveUser(r.getReceiveUser());
                    shippingAddress.setReceivePhone(r.getReceivePhone());
                    shippingAddress.setRegionCode(r.getRegionCode());
                    shippingAddress.setRegionName(r.getRegionName());
                    shippingAddress.setReceiveAddr(r.getReceiveAddr());
                    shippingAddress.setIsDefault(r.getIsDefault());
                    shippingAddress.setAddressDesc(r.getAddressDesc());
                    shippingAddress.setAddressType(r.getAddressType());
                    companyShippingAddressDAO.updateById(shippingAddress);
                } else {
                    CompanyShippingAddress shippingAddress = new CompanyShippingAddress();
                    shippingAddress.setEnterpriseNo(operationModel.getEnterpriseNo());
                    shippingAddress.setCompanyNo(supplier.getCompanyNo());
                    shippingAddress.setLinkAddressCode(r.getLinkAddressCode());
                    shippingAddress.setSourceNo(supplier.getSupplierNo());
                    shippingAddress.setStatus(CommonStatusEnum.EFFECTIVE.getValue());
                    shippingAddress.setLinkaddType(LinkmanTypeEnum.SUPPLY.getValue());
                    shippingAddress.setReceiveUser(r.getReceiveUser());
                    shippingAddress.setReceivePhone(r.getReceivePhone());
                    shippingAddress.setRegionCode(r.getRegionCode());
                    shippingAddress.setRegionName(r.getRegionName());
                    shippingAddress.setReceiveAddr(r.getReceiveAddr());
                    shippingAddress.setIsDefault(r.getIsDefault());
                    shippingAddress.setAddressDesc(r.getAddressDesc());
                    shippingAddress.setAddressType(r.getAddressType());
                    saveShippingAddressListList.add(shippingAddress);
                }
            }
            if (CollectionUtils.isNotEmpty(saveShippingAddressListList)) {
                companyShippingAddressDAO.addBatch(saveShippingAddressListList);
            }
        }
        //银行信息
        if (CollectionUtils.isEmpty(params.getBankList())) {
            //删除联系地址
            companyBankDAO.deleteCompanyBankListBySourceNoList(operationModel.getEnterpriseNo(), LinkmanTypeEnum.SUPPLY.getValue(), Collections.singletonList(supplier.getSupplierNo()));
        } else {
            final List<CompanyBank> bankList = companyBankDAO.getCompanyBankListBySourceNoList(operationModel.getEnterpriseNo(), LinkmanTypeEnum.SUPPLY.getValue(), Collections.singletonList(supplier.getSupplierNo()));
            final Map<Long, CompanyBank> bankMap = bankList.stream().collect(Collectors.toMap(CompanyBank::getId, Function.identity()));

            final Set<Long> localBankList = bankList.stream().map(CompanyBank::getId).collect(Collectors.toSet());
            final Set<Long> itemBankList = params.getBankList().stream().map(BankDTO::getId).collect(Collectors.toSet());
            Set<Long> difference = Sets.difference(localBankList, itemBankList);
            if (!difference.isEmpty()) {
                companyBankDAO.deleteCompanyBankListBySourceNoList(operationModel.getEnterpriseNo(), LinkmanTypeEnum.SUPPLY.getValue(), Collections.singletonList(supplier.getSupplierNo()), new ArrayList<>(difference));
            }

            List<CompanyBank> companyBankList = new ArrayList<>();
            params.getBankList().forEach(b -> {
                if (bankMap.containsKey(b.getId())) {
                    CompanyBank companyBank = bankMap.get(b.getId());
                    companyBank.setBankType(b.getBankType());
                    companyBank.setOpenBank(b.getOpenBank());
                    companyBank.setAccountName(b.getAccountName());
                    companyBank.setAccountNo(b.getAccountNo());
                    companyBank.setAccountType(b.getAccountType());
                    companyBank.setLinkPerson(b.getLinkPerson());
                    companyBank.setLinkPhone(b.getLinkPhone());
                    companyBank.setStatus(b.getStatus());
                    companyBankDAO.updateById(companyBank);
                } else {
                    CompanyBank companyBank = new CompanyBank();
                    companyBank.setEnterpriseNo(operationModel.getEnterpriseNo());
                    companyBank.setLinkaddType(LinkmanTypeEnum.SUPPLY.getValue());
                    companyBank.setCompanyNo(supplier.getCompanyNo());
                    companyBank.setSourceNo(supplier.getSupplierNo());
                    companyBank.setBankCode(b.getBankCode());
                    companyBank.setBankType(b.getBankType());
                    companyBank.setOpenBank(b.getOpenBank());
                    companyBank.setAccountName(b.getAccountName());
                    companyBank.setAccountNo(b.getAccountNo());
                    companyBank.setAccountType(b.getAccountType());
                    companyBank.setLinkPerson(b.getLinkPerson());
                    companyBank.setLinkPhone(b.getLinkPhone());
                    companyBank.setStatus(b.getStatus());
                    companyBank.setIsDefault(b.getIsDefault());
                    companyBankList.add(companyBank);
                }
            });
            if (CollectionUtils.isNotEmpty(companyBankList)) {
                companyBankDAO.addBatch(companyBankList);
            }
        }
        //负责人信息
        if (CollectionUtils.isEmpty(params.getSupplierManList())) {
            //删除联系地址
            supplierOrderManDAO.deleteSupplierOrderManListBySupplierNoList(operationModel.getEnterpriseNo(), Collections.singletonList(supplier.getSupplierNo()));
        } else {
            final List<SupplierOrderMan> salesManList = supplierOrderManDAO.getSupplierOrderManListBySupplierNoList(operationModel.getEnterpriseNo(), Collections.singletonList(supplier.getSupplierNo()));
            final Map<Long, SupplierOrderMan> salesManMap = salesManList.stream().collect(Collectors.toMap(SupplierOrderMan::getId, Function.identity()));

            final Set<Long> localSalesManIdList = salesManList.stream().map(SupplierOrderMan::getId).collect(Collectors.toSet());
            final Set<Long> itemSalesManIdList = params.getSupplierManList().stream().map(SupplierSalesManDTO::getId).collect(Collectors.toSet());
            Set<Long> difference = Sets.difference(localSalesManIdList, itemSalesManIdList);
            if (!difference.isEmpty()) {
                supplierOrderManDAO.deleteSupplierOrderManListBySupplierNoList(operationModel.getEnterpriseNo(), Collections.singletonList(supplier.getSupplierNo()), new ArrayList<>(difference));
            }

            List<SupplierOrderMan> supplierOrderManList = new ArrayList<>();
            for (SupplierSalesManDTO r : params.getSupplierManList()) {
                if (salesManMap.containsKey(r.getId())) {
                    SupplierOrderMan supplierOrderMan = salesManMap.get(r.getId());
                    supplierOrderMan.setOrderManNo(r.getOrderManNo());
                    supplierOrderMan.setOrderManName(r.getOrderManName());
                    supplierOrderMan.setOrgNo(r.getDeptNo());
                    supplierOrderMan.setOrgName(r.getDeptName());
                    supplierOrderMan.setPost(r.getPost());
                    supplierOrderMan.setIsDefault(r.getIsDefault());
                    supplierOrderMan.setOrderSpecialist(r.getOrderSpecialist());
                    supplierOrderMan.setPost(r.getPost());
                    supplierOrderManDAO.updateById(supplierOrderMan);
                } else {
                    SupplierOrderMan supplierOrderMan = new SupplierOrderMan();
                    supplierOrderMan.setSupplierNo(supplier.getSupplierNo());
                    supplierOrderMan.setManCode(UUID.randomUUID().toString());
                    supplierOrderMan.setOrderManNo(r.getOrderManNo());
                    supplierOrderMan.setOrderManName(r.getOrderManName());
                    supplierOrderMan.setOrgNo(r.getDeptNo());
                    supplierOrderMan.setOrgName(r.getDeptName());
                    supplierOrderMan.setPost(r.getPost());
                    supplierOrderMan.setIsDefault(r.getIsDefault());
                    supplierOrderMan.setOrderSpecialist(r.getOrderSpecialist());
                    supplierOrderMan.setPost(r.getPost());
                    supplierOrderManList.add(supplierOrderMan);
                }
            }
            if (CollectionUtils.isNotEmpty(supplierOrderManList)) {
                supplierOrderManDAO.addBatch(supplierOrderManList);
            }
        }
        // 保存资料文件
        companyService.saveOrUpdateCert(params.getCatalogInfoBigClientReq(), operationModel.getEnterpriseNo(), company.getCompanyNo(), operationModel);
        supplier.setYsSyncFlag("0");
        supplier.setIsSyncScs(0);
        supplier.setIsSyncWms(0);
        supplier.setTcSyncFlag(0);
        supplier.setWmsPushResult("");
        supplier.setYsPushResult("");
        supplier.setScsPushResult("");
        supplierDAO.updateById(supplier);
        //判定如果是集团租户
        TenantVo tenantVo = tenantService.getEnterpriseInfo(operationModel.getEnterpriseNo());

        if (TenantTypeEnum.GROUP.getValue().equals(tenantVo.getTenantType())) {
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                @Override
                public void afterCommit() {
                    // 如果是集团租户更新，异步更新有使用关系的子租户
                    masterDataSyncExtService.updateSyncSubTenantSupplier(supplier.getSupplierCode(), supplier, operationModel);
                }
            });
        }
        ccLogService.saveSupplierLog(supplier.getSupplierNo(), operationModel, "编辑供应商档案");
        return Boolean.TRUE;
    }


    @Override
    public SupplierVO getSupplier(String enterpriseNo, String supplierNo) {
        Supplier supplier = supplierDAO.getSupplierByNo(enterpriseNo, supplierNo);
        ValidatorUtils.checkTrueThrowEx(supplier == null, "供应商不存在");
        SupplierVO supplierVO = new SupplierVO();
        BeanUtils.copyProperties(supplier, supplierVO);
        supplierVO.setRetailInvestorsName(CommonIfEnum.getNameByValue(supplier.getRetailInvestors()));
        supplierVO.setControlStatusName(CustomerControlStatusEnum.getByType(supplier.getControlStatus()) == null ? null : CustomerControlStatusEnum.getByType(supplier.getControlStatus()).getName());
        final Company company = companyDAO.findByEnterpriseNoAndCompanyNo(enterpriseNo, supplier.getCompanyNo());
        //企业信息补充
        if (company != null) {
            supplierVO.setCompanyName(company.getCompanyName());
            supplierVO.setUnifiedSocialCode(company.getUnifiedSocialCode());
            supplierVO.setCountry(company.getCountry());
            supplierVO.setFactoryType(company.getFactoryType());
            supplierVO.setEconomicType(company.getEconomicType());
            supplierVO.setIsAssociatedEnterprise(company.getIsAssociatedEnterprise());
            supplierVO.setAssociatedOrgNo(company.getAssociatedOrgNo());
            supplierVO.setAssociatedOrgCode(company.getAssociatedOrgCode());
            supplierVO.setAssociatedOrgName(company.getAssociatedOrgName());
            supplierVO.setAddress(company.getAddress());
            if (StringUtils.isNotBlank(company.getTaxCategory())) {
                final Map<String, CustomDocResponse> itemByCustomDocCode = customDocService.getItemByCustomDocCode(enterpriseNo, SystemConstant.TAX_CATEGORY);
                supplierVO.setTaxCategory(company.getTaxCategory());
                supplierVO.setTaxCategoryName(itemByCustomDocCode.getOrDefault(company.getTaxCategory(), new CustomDocResponse()).getDocItemName());
            }
            if (StringUtils.isNotBlank(company.getEconomicType())) {
                final Map<String, CustomDocResponse> itemByCustomDocCode = customDocService.getItemByCustomDocCode(enterpriseNo, SystemConstant.ECONOMIC_TYPE);
                supplierVO.setEconomicTypeName(itemByCustomDocCode.getOrDefault(company.getEconomicType(), new CustomDocResponse()).getDocItemName());
            }
        }
        //供应商分类
        if (StringUtils.isNotBlank(supplier.getSupplierCategoryNo())) {
            final SupplierCategory supplierCategory = supplierCategoryDAO.getByNo(enterpriseNo, supplier.getSupplierCategoryNo());
            if (supplierCategory != null) {
                supplierVO.setSupplierCategoryName(supplierCategory.getCategoryName());
            }
        }
        //交易币种
        if (StringUtils.isNotBlank(supplier.getCurrency())) {
            final Map<String, String> currencyMap = dictEnterpriseService.getValMapByNumber(enterpriseNo, DictNumberEnum.DSRP_CURRENCY);
            supplierVO.setCurrencyName(currencyMap.getOrDefault(supplier.getCurrency(), ""));
        }
        //合作性质
        if (StringUtils.isNotBlank(supplier.getCooperationMode())) {
            final Map<String, CustomDocResponse> cooperationMap = customDocService.getItemByCustomDocCode(enterpriseNo, SystemConstant.SUPPLIER_COOPERATION_NUMBER);
            supplierVO.setCooperationModeName(cooperationMap.getOrDefault(supplier.getCooperationMode(), new CustomDocResponse()).getDocItemName());
        }
        //交易类型
        if (StringUtils.isNotBlank(supplier.getTransactionType())) {
            final List<TransactionTypeResponse> transactionTypeList = transactionTypeService.getTransactionTypeList(enterpriseNo, ViewNameConstant.BDC_SUPPLIER_VIEW);
            final Map<String, TransactionTypeResponse> transactionTypeMap = transactionTypeList.stream().collect(Collectors.toMap(TransactionTypeResponse::getTransactionTypeCode, Function.identity()));
            supplierVO.setTransactionTypeName(transactionTypeMap.getOrDefault(supplier.getTransactionType(), new TransactionTypeResponse()).getTransactionTypeName());
        }
        //付款协议
        if (StringUtils.isNotBlank(supplier.getPaymentTerm())) {
            final Map<String, CustomDocResponse> paymentTermMap = customDocService.getItemByCustomDocCode(enterpriseNo, SystemConstant.DSRP_CUSTOMER_PAYMENT_TERM);
            supplierVO.setPaymentTermName(paymentTermMap.getOrDefault(supplier.getPaymentTerm(), new CustomDocResponse()).getDocItemName());
        }
        if (supplier.getControlId() != null) {
            final CertControlTypeVo type = certControlService.getId(enterpriseNo, supplier.getControlId());
            if (type != null) {
                supplierVO.setControlTypeName(type.getControlTypeName());
            }
        }
        //供应商联系人
        List<CompanyLinkman> customerLinkmanList = companyLinkmanDAO.getCompanyLinkmanListByCompanyNo(enterpriseNo, supplier.getCompanyNo(), LinkmanTypeEnum.SUPPLY.getValue(), supplier.getSupplierNo());
        if (CollectionUtils.isNotEmpty(customerLinkmanList)) {
            List<CompanyLinkmanVO> customerLinkmanVoList = new ArrayList<>();
            customerLinkmanList.forEach(t -> {
                CompanyLinkmanVO vo = new CompanyLinkmanVO();
                BeanUtils.copyProperties(t, vo);
                customerLinkmanVoList.add(vo);
            });
            supplierVO.setLinkmanList(customerLinkmanVoList);
        }
        //负责人信息
        List<SupplierOrderMan> salesManList = supplierOrderManDAO.getSupplierOrderManListBySupplierNoList(enterpriseNo, Collections.singletonList(supplier.getSupplierNo()));
        if (CollectionUtils.isNotEmpty(salesManList)) {
            final List<String> salesManNoList = salesManList.stream().map(SupplierOrderMan::getOrderManNo).collect(Collectors.toList());
            Map<String, EmployeeVo> employeeMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(salesManNoList)) {
                final List<EmployeeVo> employeeList = employeeService.getEmployeeList(enterpriseNo, salesManNoList);
                employeeMap = employeeList.stream().collect(Collectors.toMap(EmployeeVo::getEmployeeNo, Function.identity()));
            }
            List<SupplierSalesManVO> salesManVoList = new ArrayList<>();
            Map<String, EmployeeVo> finalEmployeeMap = employeeMap;
            salesManList.forEach(t -> {
                SupplierSalesManVO supplierSalesManVO = new SupplierSalesManVO();
                BeanUtils.copyProperties(t, supplierSalesManVO);
                supplierSalesManVO.setDeptNo(t.getOrgNo());
                supplierSalesManVO.setDeptName(t.getOrgName());
                if (finalEmployeeMap.containsKey(t.getOrderManNo())) {
                    supplierSalesManVO.setMobile(finalEmployeeMap.get(t.getOrderManNo()).getMobile());
                }
                salesManVoList.add(supplierSalesManVO);
            });
            supplierVO.setSupplierManList(salesManVoList);
        }
        final List<CompanyBank> bankList = companyBankDAO.getCompanyBankListBySourceNoList(enterpriseNo, LinkmanTypeEnum.SUPPLY.getValue(), Collections.singletonList(supplierNo));
        if (CollectionUtils.isNotEmpty(bankList)) {
            final List<BankType> bankTypeList = bankTypeDAO.getList();
            final Map<String, BankType> bankTypeMap = bankTypeList.stream().collect(Collectors.toMap(BankType::getBankCode, Function.identity(), (v1, v2) -> v1));
            List<BankVO> bankVoList = new ArrayList<>();
            bankList.forEach(t -> {
                BankVO bankVO = new BankVO();
                BeanUtils.copyProperties(t, bankVO);
                bankVO.setBankTypeName(bankTypeMap.getOrDefault(t.getBankType(), new BankType()).getBankName());
                bankVoList.add(bankVO);
            });
            supplierVO.setBankList(bankVoList);
        }
        //联系地址
        List<CompanyShippingAddress> companyShippingAddressList = companyShippingAddressDAO.getCompanyShippingAddressByCompanyNo(enterpriseNo, supplier.getCompanyNo(), LinkmanTypeEnum.SUPPLY.getValue(), supplierNo);
        if (CollectionUtils.isNotEmpty(companyShippingAddressList)) {
            List<CompanyShippingAddressVO> salesManVoList = new ArrayList<>();
            companyShippingAddressList.forEach(t -> {
                final List<String> regionCodeList = companyShippingAddressList.stream().map(CompanyShippingAddress::getRegionCode).filter(s -> !StringUtils.isEmpty(s)).collect(Collectors.toList());
                Map<String, AreaCodeVo> areaMap = new HashMap<>();
                if (CollectionUtils.isNotEmpty(regionCodeList)) {
                    List<AreaCodeVo> areaCodeList = dictEnterpriseService.findAreaByCodes(regionCodeList);
                    areaMap = areaCodeList.stream().collect(Collectors.toMap(AreaCodeVo::getAreaCode, Function.identity()));
                }
                Map<String, AreaCodeVo> finalAreaMap = areaMap;
                CompanyShippingAddressVO companyShippingAddressVO = new CompanyShippingAddressVO();
                BeanUtils.copyProperties(t, companyShippingAddressVO);
                if (finalAreaMap.containsKey(t.getRegionCode())) {
//                    companyShippingAddressVO.setRegionName(finalAreaMap.get(t.getRegionCode()).getAreaName());
                    companyShippingAddressVO.setRegionFullName(finalAreaMap.get(t.getRegionCode()).getAreaName());
                }
                salesManVoList.add(companyShippingAddressVO);
            });
            supplierVO.setLinkAddressList(salesManVoList);
        }
        //对应供应商
        final List<Customer> supplierList = customerDAO.selectList(Wrappers.lambdaQuery(Customer.class).eq(Customer::getEnterpriseNo, enterpriseNo)
                .eq(Customer::getCompanyNo, supplier.getCompanyNo())
                .eq(Customer::getDeleted, DeletedEnum.UN_DELETE.getValue()));
        supplierVO.setCustomerCode(supplierList.stream().map(Customer::getCustomerCode).collect(Collectors.joining(",")));
        supplierVO.setCustomerName(supplierList.stream().map(Customer::getCustomerName).collect(Collectors.joining(",")));
        return supplierVO;
    }


    @Override
    public SupplierVO getSupplierByOrg(OperationModel operationModel, String supplierNo) {
        //获取有权限的组织列表
        List<OrganizationVo> organizationList = uimTenantService.getAuthEnterpriseList(operationModel.getEnterpriseNo(), operationModel.getUserNo(), operationModel.getEmployerNo());
        Supplier supplier = supplierDAO.getSupplierByNoEnterpriseNo(supplierNo);
        ValidatorUtils.checkTrueThrowEx(supplier == null, "供应商不存在");
        final Optional<OrganizationVo> optional = organizationList.stream().filter(t -> supplier.getEnterpriseNo().equals(t.getBindingEnterpriseNo())).findAny();
        ValidatorUtils.checkTrueThrowEx(!optional.isPresent(), "无权限查询供应商");
        return getSupplier(supplier.getEnterpriseNo(), supplierNo);
    }

    @Override
    public Boolean checkOnlyCode(OperationModel operationModel, String no, String code) {
        return supplierDAO.selectCount(new LambdaQueryWrapper<Supplier>().eq(Supplier::getSupplierCode, code).eq(Supplier::getEnterpriseNo, operationModel.getEnterpriseNo()).ne(StringUtils.isNotEmpty(no), Supplier::getSupplierNo, no)) <= 0;

    }

    @Override
    public Boolean checkOnlyName(OperationModel operationModel, String no, String name) {
        return supplierDAO.selectCount(new LambdaQueryWrapper<Supplier>().eq(Supplier::getSupplierCode, name).eq(Supplier::getEnterpriseNo, operationModel.getEnterpriseNo()).ne(StringUtils.isNotEmpty(no), Supplier::getSupplierNo, no)) <= 0;
    }

    @Override
    public Boolean deleteSupplier(OperationModel operationModel, List<String> supplierNoList) {
        List<Supplier> supplierList = supplierDAO.getSupplierByNoList(operationModel.getEnterpriseNo(), supplierNoList);
        ValidatorUtils.checkTrueThrowEx(CollectionUtils.isEmpty(supplierList), "供应商不存在");
        final Optional<Supplier> anySupplier = supplierList.stream().filter(supplier -> CustomerBusinessFlagEnum.FORMAL.getValue().equals(supplier.getBusinessFlag())).findAny();
        if (anySupplier.isPresent()) {
            ValidatorUtils.checkTrueThrowEx(anySupplier.isPresent(), "正式供应商" + anySupplier.get().getSupplierName() + "不允许删除");
        }
        supplierList.forEach(supplier -> {
            supplier.setDeleted(DeletedEnum.DELETED.getValue());
            supplier.setOperateTime(DateUtil.getCurrentDate());
            supplier.setOperateNo(operationModel.getEmployerNo());
            supplier.setOperateName(operationModel.getUserName());
            supplierDAO.updateById(supplier);
            ccLogService.saveSupplierLog(supplier.getSupplierNo(), operationModel, "删除供应商");
        });
        return Boolean.TRUE;
    }

    public PageVo<SupplierSyncQueryVO> findListPageForSync(OperationModel operationModel, SupplierSyncQueryDTO params, PageDto pageDto) {
        String enterpriseNo = operationModel.getEnterpriseNo();
        // 如果是子租户，需要用集团的编号去进行查询
        if (operationModel.getSubTenant()) {
            enterpriseNo = operationModel.getGroupTenantNo();
        }

        Map<String, SupplierMappingDbVo> supplierMappingVoMap = new HashMap<>();
        Map<String, String> enterpriseNameMap = new HashMap<>();
        LambdaQueryWrapper<Supplier> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        // 如果搜索条件绑定状态不为空，则需要先查询供应商对照数据，进行in/notIn
        if (null != params.getBindStatus()) {
            SupplierMappingDbDto supplierMappingDbDto = new SupplierMappingDbDto();
            supplierMappingDbDto.setEnterpriseNo(enterpriseNo);
            supplierMappingDbDto.setBindStatus(BindStatusEnum.binded.getType());
            CallResult<List<SupplierMappingDbVo>> supplierMappingVoCallResult = supplierMappingAPI.findDbList(supplierMappingDbDto);
            MessageUtil.ensureCallResultSuccess(supplierMappingVoCallResult);
            List<SupplierMappingDbVo> supplierMappingVoList = supplierMappingVoCallResult.getData();
            List<String> supplierCodeList = new ArrayList<>();
            if (!CollectionUtils.isEmpty(supplierMappingVoList)) {
                supplierCodeList = supplierMappingVoList.stream().map(SupplierMappingDbVo::getOuterSupplierNo).collect(Collectors.toList());
                supplierMappingVoMap = supplierMappingVoList.stream().collect(Collectors.toMap(SupplierMappingDbVo::getOuterSupplierNo, Function.identity(), (v1, v2) -> v1));
                List<String> supplierNoList = supplierMappingVoList.stream().map(SupplierMappingDbVo::getSupplierNo).collect(Collectors.toList());
                EnterpriseDto enterpriseDto = new EnterpriseDto();
                enterpriseDto.setEnterpriseNos(supplierNoList);
                CallResult<List<EnterpriseVo>> enterpriseCallResult = ecsEnterpriseAPI.findList(enterpriseDto);
                MessageUtil.ensureCallResultSuccess(enterpriseCallResult);
                List<EnterpriseVo> enterpriseVoList = enterpriseCallResult.getData();
                if (!CollectionUtils.isEmpty(enterpriseVoList)) {
                    enterpriseNameMap = enterpriseVoList.stream().collect(Collectors.toMap(EnterpriseVo::getEnterpriseNo, EnterpriseVo::getEnterpriseName, (v1, v2) -> v1));
                }
            }
            supplierCodeList.add("-1");
            if (1 == params.getBindStatus()) {
                lambdaQueryWrapper.in(Supplier::getSupplierCode, supplierCodeList);
            } else {
                lambdaQueryWrapper.notIn(Supplier::getSupplierCode, supplierCodeList);
            }
        }
        // 组装查询sql
        lambdaQueryWrapper.eq(Supplier::getEnterpriseNo, enterpriseNo);
        if (StringUtils.isNotBlank(params.getKeywords())) {
            lambdaQueryWrapper.and(
                    i -> i.like(StringUtils.isNotBlank(params.getKeywords()), Supplier::getSupplierNo, params.getKeywords())
                            .or().like(StringUtils.isNotBlank(params.getKeywords()), Supplier::getSupplierName, params.getKeywords())
                            .or().like(StringUtils.isNotBlank(params.getKeywords()), Supplier::getSupplierCode, params.getKeywords())
                            .or().like(StringUtils.isNotBlank(params.getKeywords()), Supplier::getMnemonicCode, params.getKeywords())
            );
        }
        if (!CollectionUtils.isEmpty(params.getSupplierNoList())) {
            lambdaQueryWrapper.in(Supplier::getSupplierNo, params.getSupplierNoList());
        }
        lambdaQueryWrapper.eq(Supplier::getControlStatus, TypeUtils.castToString(ControlStatusEnum.ENABLE.getValue()))
                .eq(Supplier::getBusinessFlag, SupplierBusinessFlagEnum.FORMAL.getValue()).eq(Supplier::getDeleted, DeletedEnum.UN_DELETE.getValue());
        Page<Supplier> page = PageHelper.startPage(pageDto.getPageIndex(), pageDto.getPageSize(), pageDto.getOrderBy());
        List<Supplier> supplierList = supplierDAO.selectList(lambdaQueryWrapper);
        if (CollectionUtils.isEmpty(supplierList)) {
            return new PageVo<>();
        }
        // 如果上边没有查询供应商对照，这里需要查询一次，下边组装数据用
        if (supplierMappingVoMap.isEmpty()) {
            List<String> supplierCode = supplierList.stream().map(Supplier::getSupplierCode).collect(Collectors.toList());
            SupplierMappingDbDto supplierMappingDbDto = new SupplierMappingDbDto();
            supplierMappingDbDto.setOuterSupplierNoList(supplierCode);
            supplierMappingDbDto.setEnterpriseNo(enterpriseNo);
            CallResult<List<SupplierMappingDbVo>> supplierMappingVoCallResult = supplierMappingAPI.findDbList(supplierMappingDbDto);
            MessageUtil.ensureCallResultSuccess(supplierMappingVoCallResult);
            List<SupplierMappingDbVo> supplierMappingVoList = supplierMappingVoCallResult.getData();
            if (!CollectionUtils.isEmpty(supplierMappingVoList)) {
                supplierMappingVoMap = supplierMappingVoList.stream().collect(Collectors.toMap(SupplierMappingDbVo::getOuterSupplierNo, Function.identity(), (v1, v2) -> v1));
                List<String> supplierNoList = supplierMappingVoList.stream().map(SupplierMappingDbVo::getSupplierNo).collect(Collectors.toList());
                EnterpriseDto enterpriseDto = new EnterpriseDto();
                enterpriseDto.setEnterpriseNos(supplierNoList);
                CallResult<List<EnterpriseVo>> enterpriseCallResult = ecsEnterpriseAPI.findList(enterpriseDto);
                MessageUtil.ensureCallResultSuccess(enterpriseCallResult);
                List<EnterpriseVo> enterpriseVoList = enterpriseCallResult.getData();
                if (!CollectionUtils.isEmpty(enterpriseVoList)) {
                    enterpriseNameMap = enterpriseVoList.stream().collect(Collectors.toMap(EnterpriseVo::getEnterpriseNo, EnterpriseVo::getEnterpriseName, (v1, v2) -> v1));
                }
            }

        }

        List<SupplierSyncQueryVO> supplierSyncQueryVOList = new ArrayList<>();
        for (Supplier supplier : supplierList) {
            SupplierSyncQueryVO supplierSyncQueryVO = new SupplierSyncQueryVO();
            supplierSyncQueryVO.setSupplierNo(supplier.getSupplierNo());
            supplierSyncQueryVO.setSupplierCode(supplier.getSupplierCode());
            supplierSyncQueryVO.setSupplierName(supplier.getSupplierName());
            supplierSyncQueryVO.setUnifiedSocialCode(supplier.getUnifiedSocialCode());
            SupplierMappingDbVo supplierMappingDbVo = supplierMappingVoMap.get(supplier.getSupplierCode());
            if (supplierMappingDbVo != null) {
                if (BindStatusEnum.binded.getType().equals(supplierMappingDbVo.getBindStatus())) {
                    supplierSyncQueryVO.setSupplierEnterpriseNo(supplierMappingDbVo.getSupplierNo());
                    String supplierEnterpriseName = enterpriseNameMap.get(supplierMappingDbVo.getSupplierNo());
                    if (StringUtils.isNotBlank(supplierEnterpriseName)) {
                        supplierSyncQueryVO.setSupplierEnterpriseName(supplierEnterpriseName);
                    }
                    supplierSyncQueryVO.setSupplierUnifiedSocialCode(supplierMappingDbVo.getOuterSupplierCreditIdentifier());
                    supplierSyncQueryVO.setBindStatusName(BindStatusEnum.binded.getName());
                    supplierSyncQueryVO.setBindStatus(BindStatusEnum.binded.getType());
                } else {
                    supplierSyncQueryVO.setBindStatusName(BindStatusEnum.unbind.getName());
                    supplierSyncQueryVO.setBindStatus(BindStatusEnum.unbind.getType());
                }
                supplierSyncQueryVO.setId(supplierMappingDbVo.getId());
            } else {
                supplierSyncQueryVO.setBindStatusName(BindStatusEnum.unbind.getName());
                supplierSyncQueryVO.setBindStatus(BindStatusEnum.unbind.getType());
            }
            supplierSyncQueryVOList.add(supplierSyncQueryVO);
        }
        return PageUtils.convertPageVo(page, supplierSyncQueryVOList);
    }

    public SupplierSyncCountVO findSupplierSyncCount(OperationModel operationModel) {
        String enterpriseNo = operationModel.getEnterpriseNo();
        // 如果是子租户，需要用集团的编号去进行查询
        if (operationModel.getSubTenant()) {
            enterpriseNo = operationModel.getGroupTenantNo();
        }
        LambdaQueryWrapper<Supplier> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(Supplier::getEnterpriseNo, enterpriseNo);
        lambdaQueryWrapper.eq(Supplier::getControlStatus, TypeUtils.castToString(ControlStatusEnum.ENABLE.getValue()))
                .eq(Supplier::getBusinessFlag, SupplierBusinessFlagEnum.FORMAL.getValue()).eq(Supplier::getDeleted, DeletedEnum.UN_DELETE.getValue());
        List<Supplier> supplierList = supplierDAO.selectList(lambdaQueryWrapper);
        SupplierSyncCountVO supplierSyncCountVO = new SupplierSyncCountVO();
        supplierSyncCountVO.setFormalNum(supplierList.size());
        if (CollectionUtils.isEmpty(supplierList)) {
            return supplierSyncCountVO;
        }
        List<String> supplierCodeList = supplierList.stream().map(Supplier::getSupplierCode).collect(Collectors.toList());
        SupplierMappingDbDto supplierMappingDbDto = new SupplierMappingDbDto();
        supplierMappingDbDto.setOuterSupplierNoList(supplierCodeList);
        supplierMappingDbDto.setEnterpriseNo(enterpriseNo);
        supplierMappingDbDto.setBindStatus(BindStatusEnum.binded.getType());
        CallResult<List<SupplierMappingDbVo>> supplierMappingVoCallResult = supplierMappingAPI.findDbList(supplierMappingDbDto);
        MessageUtil.ensureCallResultSuccess(supplierMappingVoCallResult);
        List<SupplierMappingDbVo> supplierMappingVoList = supplierMappingVoCallResult.getData();
        if (!CollectionUtils.isEmpty(supplierMappingVoList)) {
            supplierSyncCountVO.setBindNum(supplierMappingVoList.size());
        }
        supplierSyncCountVO.setUnBindNum(supplierSyncCountVO.getFormalNum() - supplierSyncCountVO.getBindNum());
        return supplierSyncCountVO;
    }

    /**
     * 待准入供应商-准入弹框列表
     */
    public PageVo<SupplierSyncQueryVO> findListPageForPendingAdmission(OperationModel operationModel, SupplierSyncQueryDTO params, PageDto pageDto) {
        CustomerRelationDto customerRelationDto = new CustomerRelationDto();
        customerRelationDto.setCustomerNo(operationModel.getEnterpriseNo());
        customerRelationDto.setIsNotNullDsrpSupplierNo("1");
        CallResult<List<CustomerRelationVo>> ddcCallResult = ddcCustomerAPI.list(customerRelationDto);
        MessageUtil.ensureCallResultSuccess(ddcCallResult);
        List<CustomerRelationVo> customerRelationVoList = ddcCallResult.getData();
        List<String> supplierNoList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(customerRelationVoList)) {
            supplierNoList = customerRelationVoList.stream().map(CustomerRelationVo::getDsrpSupplierNo).distinct().collect(Collectors.toList());
        }
        LambdaQueryWrapper<Supplier> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(Supplier::getEnterpriseNo, operationModel.getEnterpriseNo());
        if (StringUtils.isNotBlank(params.getKeywords())) {
            lambdaQueryWrapper.and(
                    i -> i.like(StringUtils.isNotBlank(params.getKeywords()), Supplier::getSupplierNo, params.getKeywords())
                            .or().like(StringUtils.isNotBlank(params.getKeywords()), Supplier::getSupplierName, params.getKeywords())
                            .or().like(StringUtils.isNotBlank(params.getKeywords()), Supplier::getSupplierCode, params.getKeywords())
                            .or().like(StringUtils.isNotBlank(params.getKeywords()), Supplier::getMnemonicCode, params.getKeywords())
            );
        }
        if (!CollectionUtils.isEmpty(supplierNoList)) {
            lambdaQueryWrapper.notIn(Supplier::getSupplierNo, supplierNoList);
        }
        lambdaQueryWrapper.eq(Supplier::getControlStatus, TypeUtils.castToString(ControlStatusEnum.ENABLE.getValue()))
                .eq(Supplier::getBusinessFlag, SupplierBusinessFlagEnum.FORMAL.getValue()).eq(Supplier::getDeleted, DeletedEnum.UN_DELETE.getValue());
        Page<Supplier> page = PageHelper.startPage(pageDto.getPageIndex(), pageDto.getPageSize(), pageDto.getOrderBy());
        supplierDAO.selectList(lambdaQueryWrapper);
        List<SupplierSyncQueryVO> supplierSyncQueryVOList = new ArrayList<>();
        for (Supplier supplier : page.getResult()) {
            SupplierSyncQueryVO supplierSyncQueryVO = new SupplierSyncQueryVO();
            supplierSyncQueryVO.setSupplierCode(supplier.getSupplierCode());
            supplierSyncQueryVO.setSupplierName(supplier.getSupplierName());
            supplierSyncQueryVO.setSupplierNo(supplier.getSupplierNo());
            supplierSyncQueryVO.setUnifiedSocialCode(supplier.getUnifiedSocialCode());
            supplierSyncQueryVOList.add(supplierSyncQueryVO);
        }
        return PageUtils.convertPageVo(page, supplierSyncQueryVOList);
    }


    @Override
    public PageVo<SupplierPageVO> queryPageSupplier(OperationModel operationModel, QueryPageSupplierDTO params, PageDto pageDto) {
        LambdaQueryWrapper<Supplier> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(Supplier::getEnterpriseNo, operationModel.getEnterpriseNo()).eq(Supplier::getDeleted, DeletedEnum.UN_DELETE.getValue());
        lambdaQueryWrapper.in(CollectionUtils.isNotEmpty(params.getSupplierNoList()), Supplier::getSupplierNo, params.getSupplierNoList());
        lambdaQueryWrapper.and(StringUtils.isNotBlank(params.getKeywords()),
                i -> i.like(Supplier::getSupplierCode, params.getKeywords())
                        .or().like(Supplier::getSupplierName, params.getKeywords())
        );
        Page<Supplier> page = PageHelper.startPage(pageDto.getPageIndex(), pageDto.getPageSize(), StringUtils.isBlank(pageDto.getOrderBy()) ? "create_time desc" : pageDto.getOrderBy());
        supplierDAO.selectList(lambdaQueryWrapper);
        return PageUtils.convertPageVo(page, data -> completeSupplementPageVO(operationModel.getEnterpriseNo(), data));
    }

    @Override
    public Long findExamCount(OperationModel operationModel) {
        LambdaQueryWrapper<Supplier> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(Supplier::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(Supplier::getDeleted, DeletedEnum.UN_DELETE.getValue())
                .eq(Supplier::getBusinessFlag, SupplierBusinessFlagEnum.DRAFT.getValue());
        return supplierDAO.selectCount(lambdaQueryWrapper);
    }


    @Override
    public PageVo<SupplierPageVO> queryPageSupplierByOrg(OperationModel operationModel, QueryPageSupplierByOrgDTO params, PageDto pageDto) {
        List<OrganizationVo> organizationList = uimTenantService.getAuthEnterpriseList(operationModel.getEnterpriseNo(), operationModel.getUserNo(), operationModel.getEmployerNo());
        if (CollectionUtils.isNotEmpty(params.getOrgNoList())) {
            organizationList = organizationList.stream().filter(t -> params.getOrgNoList().contains(t.getOrgNo())).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(organizationList)) {
            return new PageVo<>();
        }
        List<String> assignEnterprsieNoList = organizationList.stream().map(OrganizationVo::getBindingEnterpriseNo).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(assignEnterprsieNoList)) {
            return new PageVo<>();
        }
        final String groupEnterpriseNo = tenantService.getGroupEnterpriseNo(operationModel.getEnterpriseNo());
        SupplierMultiOrgQueryReq req = new SupplierMultiOrgQueryReq();
        BeanUtils.copyProperties(params, req);
        // 多组织查询以集团维度去查
        req.setEnterpriseNo(groupEnterpriseNo);
        req.setManagerEnterpriseNo(groupEnterpriseNo);
        req.setAssignEnterprsieNoList(assignEnterprsieNoList);

//        LambdaQueryWrapper<Supplier> lambdaQueryWrapper = new LambdaQueryWrapper<>();
//        lambdaQueryWrapper.eq(Supplier::getEnterpriseNo, groupEnterpriseNo).eq(Supplier::getDeleted, DeletedEnum.UN_DELETE.getValue());
//        lambdaQueryWrapper.in(CollectionUtils.isNotEmpty(params.getSupplierNoList()), Supplier::getSupplierNo, params.getSupplierNoList());
//        lambdaQueryWrapper.and(StringUtils.isNotBlank(params.getKeywords()),
//                i -> i.like(Supplier::getSupplierCode, params.getKeywords())
//                        .or().like(Supplier::getSupplierName, params.getKeywords())
//        );
//        lambdaQueryWrapper.inSql(Supplier::getSupplierCode, "select supplier_code  from  bdc_supplier where deleted=0 and enterprise_no in ('" + organizationList.stream().map(OrganizationVo::getBindingEnterpriseNo).collect(Collectors.joining("','")) + "')");
//        Page<Supplier> page = PageHelper.startPage(pageDto.getPageIndex(), pageDto.getPageSize(), StringUtils.isBlank(pageDto.getOrderBy()) ? "a.create_time desc" : pageDto.getOrderBy());
//        supplierDAO.selectList(lambdaQueryWrapper);

        PageVo<Supplier> page = DorisHelper.startDorisPage(pageDto.getPageIndex(), pageDto.getPageSize(), StringUtils.isBlank(pageDto.getOrderBy()) ? "a.create_time desc" : pageDto.getOrderBy());
        DorisHelper.startDoris(DorisReadModeEnum.ODBC, SystemConstant.DATA_BASE, Supplier.class);
        supplierDAO.selectMultiOrgSupplierList(req);
        return PageUtils.convertPageVo(page, data -> completeSupplementPageVO(groupEnterpriseNo, data));
    }

    @Override
    public PageVo<SupplierPageVO> queryPageSupplierBySpecifyOrg(OperationModel operationModel, QueryPageSupplierBySpecifyOrgDTO params, PageDto pageDto) {
        //获取有权限的组织列表
        List<OrganizationVo> organizationList = uimTenantService.getAuthEnterpriseList(operationModel.getEnterpriseNo(), operationModel.getUserNo(), operationModel.getEmployerNo());
        ValidatorUtils.checkEmptyThrowEx(params.getOrgNo(), "指定组织不能为空");
        final Optional<OrganizationVo> optional = organizationList.stream().filter(t -> params.getOrgNo().equals(t.getOrgNo())).findAny();
        if (!optional.isPresent()) {
            return new PageVo<>();
        }
        final String specifyEnterpriseNo = optional.get().getBindingEnterpriseNo();
        final String groupEnterpriseNo = tenantService.getGroupEnterpriseNo(operationModel.getEnterpriseNo());
        LambdaQueryWrapper<Supplier> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.select(Supplier::getSupplierCode);
        lambdaQueryWrapper.eq(Supplier::getEnterpriseNo, groupEnterpriseNo).eq(Supplier::getDeleted, DeletedEnum.UN_DELETE.getValue());
        lambdaQueryWrapper.and(StringUtils.isNotBlank(params.getKeywords()),
                i -> i.like(Supplier::getSupplierCode, params.getKeywords())
                        .or().like(Supplier::getSupplierName, params.getKeywords())
        );
        DorisHelper.startDoris(DorisReadModeEnum.ODBC, SystemConstant.DATA_BASE, Supplier.class);
        List<Supplier> supplierList = supplierDAO.selectList(lambdaQueryWrapper);
        if (CollectionUtils.isEmpty(supplierList)) {
            return new PageVo<>();
        }
        Page<Supplier> page = PageHelper.startPage(pageDto.getPageIndex(), pageDto.getPageSize(), StringUtils.isBlank(pageDto.getOrderBy()) ? "create_time desc" : pageDto.getOrderBy());
        LambdaQueryWrapper<Supplier> specifyQueryWrapper = new LambdaQueryWrapper<>();
        specifyQueryWrapper.eq(Supplier::getEnterpriseNo, specifyEnterpriseNo).eq(Supplier::getDeleted, DeletedEnum.UN_DELETE.getValue());
        specifyQueryWrapper.in(Supplier::getSupplierCode, supplierList.stream().map(Supplier::getSupplierCode).collect(Collectors.toList()));
        supplierDAO.selectList(specifyQueryWrapper);
        return PageUtils.convertPageVo(page, data -> completeSupplementPageVO(specifyEnterpriseNo, data));
    }

    @Override
    public PageVo<SupplierOperationPlatformPageVO> queryOperationPlatformPageSupplier(OperationModel operationModel, QueryPageOperationPlatformSupplierDTO params, PageDto pageDto) {
        List<OrganizationVo> organizationList = uimTenantService.getAuthEnterpriseList(operationModel.getEnterpriseNo(), operationModel.getUserNo(), operationModel.getEmployerNo());
        if (CollectionUtils.isNotEmpty(params.getOrgNoList())) {
            organizationList = organizationList.stream().filter(t -> params.getOrgNoList().contains(t.getOrgNo())).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(organizationList)) {
            return new PageVo<>();
        }
        final Map<String, OrganizationVo> organizationMap = organizationList.stream().collect(Collectors.toMap(OrganizationVo::getBindingEnterpriseNo, Function.identity()));
        LambdaQueryWrapper<Supplier> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(Supplier::getEnterpriseNo, organizationList.stream().map(OrganizationVo::getBindingEnterpriseNo).collect(Collectors.toList())).eq(Supplier::getDeleted, DeletedEnum.UN_DELETE.getValue())
                .eq(Supplier::getBusinessFlag, SupplierBusinessFlagEnum.FORMAL.getValue())
                .eq(StringUtils.isNotEmpty(params.getUnifiedSocialCode()), Supplier::getUnifiedSocialCode, params.getUnifiedSocialCode())
                .like(StringUtils.isNotEmpty(params.getUnifiedSocialCodeKeywords()), Supplier::getUnifiedSocialCode, params.getUnifiedSocialCodeKeywords())
                .like(StringUtils.isNotBlank(params.getSupplierCodeKeywords()), Supplier::getSupplierCode, params.getSupplierCodeKeywords())
                .like(StringUtils.isNotBlank(params.getSupplierNameKeywords()), Supplier::getSupplierName, params.getSupplierNameKeywords());
        Page<Supplier> page = PageHelper.startPage(pageDto.getPageIndex(), pageDto.getPageSize(), StringUtils.isBlank(pageDto.getOrderBy()) ? "enterprise_no,supplier_code,create_time desc" : pageDto.getOrderBy());
        supplierDAO.selectList(lambdaQueryWrapper);
        return PageUtils.convertPageVo(page, data -> {
            List<SupplierOperationPlatformPageVO> result = new ArrayList<>();
            final Map<String, List<Supplier>> supplierEnterpriseList = data.stream().collect(Collectors.groupingBy(Supplier::getEnterpriseNo));
            supplierEnterpriseList.forEach((enterpriseNo, supplierList) -> {
                final OrganizationVo organizationVo = organizationMap.get(enterpriseNo);
                final List<SupplierPageVO> tempSupplierList = completeSupplementPageVO(enterpriseNo, supplierList);
                tempSupplierList.forEach(t -> {
                    SupplierOperationPlatformPageVO pageVO = new SupplierOperationPlatformPageVO();
                    BeanUtils.copyProperties(t, pageVO);
                    pageVO.setOrgNo(organizationVo.getOrgNo());
                    pageVO.setOrgName(organizationVo.getOrgName());
                    result.add(pageVO);
                });
            });
            return result;
        });
    }

    @Override
    public PageVo<SupplierPageVO> queryPageSupplierByGroup(OperationModel operationModel, QueryPageSupplierDTO params, PageDto pageDto) {
        final String groupEnterpriseNo = tenantService.getGroupEnterpriseNo(operationModel.getEnterpriseNo());
        LambdaQueryWrapper<Supplier> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(Supplier::getEnterpriseNo, groupEnterpriseNo).eq(Supplier::getDeleted, DeletedEnum.UN_DELETE.getValue());
        lambdaQueryWrapper.in(CollectionUtils.isNotEmpty(params.getSupplierNoList()), Supplier::getSupplierNo, params.getSupplierNoList());
        lambdaQueryWrapper.and(StringUtils.isNotBlank(params.getKeywords()),
                i -> i.like(Supplier::getSupplierCode, params.getKeywords())
                        .or().like(Supplier::getSupplierName, params.getKeywords())
        );
        Page<Supplier> page = PageHelper.startPage(pageDto.getPageIndex(), pageDto.getPageSize(), StringUtils.isBlank(pageDto.getOrderBy()) ? "create_time desc" : pageDto.getOrderBy());
        supplierDAO.selectList(lambdaQueryWrapper);
        return PageUtils.convertPageVo(page, data -> completeSupplementPageVO(groupEnterpriseNo, data));
    }

    public List<SupplierPageVO> completeSupplementPageVO(String enterpriseNo, List<Supplier> data) {
        final List<String> categoryNoList = data.stream().map(Supplier::getSupplierCategoryNo).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
        final List<String> companyNoList = data.stream().map(Supplier::getCompanyNo).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
        final List<String> supplierNoList = data.stream().map(Supplier::getSupplierNo).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
        Map<String, SupplierCategory> categoryMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(categoryNoList)) {
            final List<SupplierCategory> categoryList = supplierCategoryDAO.getByNoList(enterpriseNo, categoryNoList);
            categoryMap = categoryList.stream().collect(Collectors.toMap(SupplierCategory::getNo, Function.identity()));
        }
        Map<String, Company> companyMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(companyNoList)) {
            final List<Company> companyList = companyDAO.findByEnterpriseNoAndCompanyNoList(enterpriseNo, companyNoList);
            companyMap = companyList.stream().collect(Collectors.toMap(Company::getCompanyNo, Function.identity()));
        }
        //税收分类
        final Map<String, CustomDocResponse> taxCategoryMap = customDocService.getItemByCustomDocCode(enterpriseNo, SystemConstant.TAX_CATEGORY);
        //供应商经济类型
        final Map<String, CustomDocResponse> economicTypeMap = customDocService.getItemByCustomDocCode(enterpriseNo, SystemConstant.ECONOMIC_TYPE);
        //交易币种
        final Map<String, String> currencyMap = dictEnterpriseService.getValMapByNumber(enterpriseNo, DictNumberEnum.DSRP_CURRENCY);
        //合作性质
        final Map<String, CustomDocResponse> cooperationMap = customDocService.getItemByCustomDocCode(enterpriseNo, SystemConstant.SUPPLIER_COOPERATION_NUMBER);
        //付款条件
        final Map<String, CustomDocResponse> paymentTermMap = customDocService.getItemByCustomDocCode(enterpriseNo, SystemConstant.DSRP_CUSTOMER_PAYMENT_TERM);
        //交易类型
        final List<TransactionTypeResponse> transactionTypeList = transactionTypeService.getTransactionTypeList(enterpriseNo, ViewNameConstant.BDC_SUPPLIER_VIEW);
        //默认联系人
        final List<CompanyLinkman> companyDefaultLinkmanList = companyLinkmanDAO.getCompanyDefaultLinkmanListBySourceNoList(enterpriseNo, LinkmanTypeEnum.SUPPLY.getValue(), supplierNoList);
        final Map<String, CompanyLinkman> companyDefaultLinkmanMap = companyDefaultLinkmanList.stream().collect(Collectors.toMap(t -> t.getSourceNo(), Function.identity(), (k, v) -> k));
        final Map<String, TransactionTypeResponse> transactionTypeMap = transactionTypeList.stream().collect(Collectors.toMap(TransactionTypeResponse::getTransactionTypeCode, Function.identity()));

        Map<Long, CertControlTypeVo> controlTypeMap = new HashMap<>();
        final List<Long> controlIdList = data.stream().map(Supplier::getControlId).filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(controlIdList)) {
            final List<CertControlTypeVo> controlTypeList = certControlService.getIdList(enterpriseNo, controlIdList);
            controlTypeMap = controlTypeList.stream().collect(Collectors.toMap(CertControlTypeVo::getId, Function.identity()));
        }

        List<SupplierPageVO> result = new ArrayList<>();
        Map<String, SupplierCategory> finalCategoryMap = categoryMap;
        Map<String, Company> finalCompanyMap = companyMap;
        Map<Long, CertControlTypeVo> finalControlTypeMap = controlTypeMap;
        data.forEach(supplier -> {
            SupplierPageVO supplierVO = new SupplierPageVO();
            BeanUtils.copyProperties(supplier, supplierVO);
            if (finalCompanyMap.containsKey(supplier.getCompanyNo())) {
                final Company company = finalCompanyMap.get(supplier.getCompanyNo());
                supplierVO.setTaxCategoryName(taxCategoryMap.getOrDefault(company.getTaxCategory(), new CustomDocResponse()).getDocItemName());
                supplierVO.setEconomicTypeName(economicTypeMap.getOrDefault(company.getEconomicType(), new CustomDocResponse()).getDocItemName());
                supplierVO.setCompanyName(company.getCompanyName());
                supplierVO.setUnifiedSocialCode(company.getUnifiedSocialCode());
                supplierVO.setCountry(company.getCountry());
                supplierVO.setFactoryType(company.getFactoryType());
                supplierVO.setFactoryTypeName(FactoryTypeEnum.getByType(company.getFactoryType()) == null ? null : FactoryTypeEnum.getByType(company.getFactoryType()).getName());
                supplierVO.setEconomicType(company.getEconomicType());
                supplierVO.setIsAssociatedEnterprise(company.getIsAssociatedEnterprise());
                supplierVO.setAssociatedOrgNo(company.getAssociatedOrgNo());
                supplierVO.setAssociatedOrgCode(company.getAssociatedOrgCode());
                supplierVO.setAssociatedOrgName(company.getAssociatedOrgName());
            }
            supplierVO.setControlStatusName(supplier.getControlStatus() == null ? null : ControlStatusEnum.getNameByValue(Integer.valueOf(supplier.getControlStatus())));
            supplierVO.setSupplierCategoryName(finalCategoryMap.getOrDefault(supplier.getSupplierCategoryNo(), new SupplierCategory()).getCategoryName());
            supplierVO.setCurrencyName(currencyMap.getOrDefault(supplier.getCurrency(), ""));
            supplierVO.setCooperationModeName(cooperationMap.getOrDefault(supplier.getCooperationMode(), new CustomDocResponse()).getDocItemName());
            supplierVO.setTransactionTypeName(transactionTypeMap.getOrDefault(supplier.getTransactionType(), new TransactionTypeResponse()).getTransactionTypeName());
            supplierVO.setPaymentTermName(paymentTermMap.getOrDefault(supplier.getPaymentTerm(), new CustomDocResponse()).getDocItemName());
            supplierVO.setIsGspControlName(CommonIfEnum.getNameByValue(supplier.getIsGspControl()));
            supplierVO.setGspAuditStatusName(CustomerGspAuditStatusEnum.getByType(supplier.getGspAuditStatus()) != null ? CustomerGspAuditStatusEnum.getByType(supplier.getGspAuditStatus()).getName() : "");
            supplierVO.setIsSyncWmsName(CommonIfEnum.getNameByValue(supplier.getIsSyncScs()));
            supplierVO.setYsSyncFlagName(CustomerYsSyncEnum.getByType(supplier.getYsSyncFlag()) == null ? null : CustomerYsSyncEnum.getByType(supplier.getYsSyncFlag()).getName());
            supplierVO.setIsSyncScsName(CustomerScsSyncEnum.getByType(supplier.getIsSyncScs()) == null ? null : CustomerScsSyncEnum.getByType(supplier.getIsSyncScs()).getName());
            if (companyDefaultLinkmanMap.containsKey(supplier.getSupplierNo())) {
                supplierVO.setLinkMan(companyDefaultLinkmanMap.get(supplier.getSupplierNo()).getLinkman());
                supplierVO.setLinkPhone(companyDefaultLinkmanMap.get(supplier.getSupplierNo()).getMobilePhone());
            }
            if (finalControlTypeMap.containsKey(supplier.getControlId())) {
                supplierVO.setControlTypeName(finalControlTypeMap.get(supplier.getControlId()).getControlTypeName());
            }
            result.add(supplierVO);
        });
        return result;
    }


    @Override
    public PageVo<SupplierComponentResponse> selectSupplierPageForCommonComponent(SupplierComponentRequest request, PageDto pageDto) {
        Page<Supplier> page = PageHelper.startPage(pageDto.getPageIndex(), pageDto.getPageSize(), pageDto.getOrderBy());
        supplierDAO.selectList(handleQueryCondition(request));
        return PageUtils.convertPageVo(page, data -> completeSupplementCommonQueryVO(request.getEnterpriseNo(), data));

    }

    @Override
    public List<SupplierComponentResponse> selectSupplierListForCommonComponent(SupplierComponentRequest request) {
        List<Supplier> supplierList = supplierDAO.selectList(handleQueryCondition(request));
        return completeSupplementCommonQueryVO(request.getEnterpriseNo(), supplierList);
    }

    private LambdaQueryWrapper handleQueryCondition(SupplierComponentRequest request) {
        LambdaQueryWrapper<Supplier> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(Supplier::getEnterpriseNo, request.getEnterpriseNo())
                .eq(StringUtils.isNotBlank(request.getSupplierNo()), Supplier::getSupplierNo, request.getSupplierNo())
                .in(CollectionUtils.isNotEmpty(request.getSupplierNoList()), Supplier::getSupplierNo, request.getSupplierNoList())
                .notIn(CollectionUtils.isNotEmpty(request.getNotInSupplierNoList()), Supplier::getSupplierNo, request.getNotInSupplierNoList())
                .eq(StringUtils.isNotBlank(request.getSupplierCode()), Supplier::getSupplierCode, request.getSupplierCode())
                .in(CollectionUtils.isNotEmpty(request.getSupplierCodeList()), Supplier::getSupplierCode, request.getSupplierCodeList())
                .eq(StringUtils.isNotBlank(request.getSupplierName()), Supplier::getSupplierName, request.getSupplierName())
                .like(StringUtils.isNotBlank(request.getSupplierNameKeyword()), Supplier::getSupplierName, request.getSupplierNameKeyword())
                .and(StringUtils.isNotBlank(request.getSupplierKeywords()), i -> i.like(Supplier::getSupplierCode, request.getSupplierKeywords())
                        .or().like(Supplier::getSupplierName, request.getSupplierKeywords())
                )
                .in(CollectionUtils.isNotEmpty(request.getControlStatusList()), Supplier::getControlStatus, request.getControlStatusList())
                .in(CollectionUtils.isNotEmpty(request.getCompanyNoList()), Supplier::getCompanyNo, request.getCompanyNoList())
                .like(StringUtils.isNotBlank(request.getSupplierCodeKeywords()), Supplier::getSupplierCode, request.getSupplierCodeKeywords())
                .in(CollectionUtils.isNotEmpty(request.getSupplierCategoryNoList()), Supplier::getSupplierCategoryNo, request.getSupplierCategoryNoList())
                .eq(StringUtils.isNotBlank(request.getUnifiedSocialCode()), Supplier::getUnifiedSocialCode, request.getUnifiedSocialCode())
                .in(CollectionUtils.isNotEmpty(request.getCooperationModeList()), Supplier::getCooperationMode, request.getCooperationModeList())
                .eq(Supplier::getBusinessFlag, SupplierBusinessFlagEnum.FORMAL.getValue())
                .eq(Supplier::getDeleted, DeletedEnum.UN_DELETE.getValue());
        return lambdaQueryWrapper;
    }

    private List<SupplierComponentResponse> completeSupplementCommonQueryVO(String enterpriseNo, List<Supplier> supplierList) {
        List<SupplierComponentResponse> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(supplierList)) {
            return result;
        }
        //合作性质
        final Map<String, CustomDocResponse> cooperationMap = customDocService.getItemByCustomDocCode(enterpriseNo, SystemConstant.SUPPLIER_COOPERATION_NUMBER);
        List<String> companyNoList = supplierList.stream().map(Supplier::getCompanyNo).filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());
        List<String> categoryNoList = supplierList.stream().map(Supplier::getSupplierCategoryNo).filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());
        Map<String, Company> companyMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(categoryNoList)) {
            List<Company> companyList = companyDAO.findByEnterpriseNoAndCompanyNoList(enterpriseNo, companyNoList);
            companyMap = companyList.stream().collect(Collectors.toMap(Company::getCompanyNo, Function.identity()));
        }
        Map<String, SupplierCategory> categoryMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(categoryNoList)) {
            List<SupplierCategory> categoryList = supplierCategoryDAO.getByNoList(enterpriseNo, categoryNoList);
            categoryMap = categoryList.stream().collect(Collectors.toMap(SupplierCategory::getNo, Function.identity()));
        }
        Map<String, Company> finalCompanyMap = companyMap;
        Map<String, SupplierCategory> finalCategoryMap = categoryMap;
        supplierList.forEach(t -> {
            SupplierComponentResponse response = new SupplierComponentResponse();
            BeanUtils.copyProperties(t, response);
            response.setControlStatusName(SupplierControlStatusEnum.getByType(t.getControlStatus()) == null ? null : SupplierControlStatusEnum.getByType(t.getControlStatus()).getName());
            response.setCooperationModeName(cooperationMap.getOrDefault(t.getCooperationMode(), new CustomDocResponse()).getDocItemName());
            if (finalCompanyMap.containsKey(t.getCompanyNo())) {
                Company company = finalCompanyMap.get(t.getCompanyNo());
                response.setCompanyName(company.getCompanyName());
                response.setTaxCategory(company.getTaxCategory());
            }
            if (finalCategoryMap.containsKey(t.getSupplierCategoryNo())) {
                SupplierCategory category = finalCategoryMap.get(t.getSupplierCategoryNo());
                response.setSupplierCategoryName(category.getCategoryName());
            }
            result.add(response);
        });
        return result;
    }

    @Override
    public SupplierInfoResponse selectSupplierInfo(String enterpriseNo, String supplierNo) {
        final SupplierVO supplierVo = getSupplier(enterpriseNo, supplierNo);
        if (supplierVo == null) {
            return null;
        }
        return JSON.parseObject(JSON.toJSONString(supplierVo), SupplierInfoResponse.class);
    }

    @Override
    public List<QueryUseInfoResponse> queryUseInfo(String enterpriseNo, String supplierCode) {
        // 获取集团租户编号
        String groupEnterpriseNo = tenantService.getGroupEnterpriseNo(enterpriseNo);
        List<String> useEnterpriseNo = new ArrayList<>();
        if (!StringUtils.isEmpty(supplierCode)) {
            List<SupplierCustomerUseInfo> customerUseList = supplierCustomerUseInfoDAO.findGroupSupplierCustomerUseList(groupEnterpriseNo, Lists.newArrayList(supplierCode), CompanyTypeEnum.SUPPLIER.getValue());
            useEnterpriseNo = customerUseList.stream().map(SupplierCustomerUseInfo::getUseEnterpriseNo).distinct().collect(Collectors.toList());
        }
        List<OrganizationTreeVo> orgList = uimTenantService.getOrganizationTreeList(groupEnterpriseNo);
        // 收集数据
        List<QueryUseInfoResponse> queryResList = new ArrayList<>();
        for (OrganizationTreeVo vo : orgList) {
            QueryUseInfoResponse queryRes = new QueryUseInfoResponse();
            queryRes.setOrgNo(vo.getOrgNo());
            queryRes.setOrgCode(vo.getOrgCode());
            queryRes.setOrgName(vo.getOrgName());
            queryRes.setUseStatus(useEnterpriseNo.contains(vo.getBindingEnterpriseNo()) ? 1 : 0);
            queryRes.setBindingEnterpriseNo(vo.getBindingEnterpriseNo());
            queryRes.setGroupEnterpriseNo(groupEnterpriseNo);
            queryResList.add(queryRes);
        }
        return queryResList;
    }


    @Override
    public List<OrganizationVO> findOrgInfoBySupplier(OperationModel operationModel, String groupEnterpriseNo, OrgInfoBySupplierDTO params) {

       List<String> useEnterpriseNoList =  supplierCustomerUseInfoDAO.findUseEnterpriseNoBySupplier(groupEnterpriseNo,params.getSupplierCode());

        List<OrganizationRes> organizationResList =
                CollectionUtils.isEmpty(useEnterpriseNoList)? new ArrayList<>(): uimTenantService.findOrgByBindEnterpriseNoList(groupEnterpriseNo, useEnterpriseNoList);

        return BeanUtil.copyFieldsList(organizationResList,OrganizationVO.class);
    }
}
