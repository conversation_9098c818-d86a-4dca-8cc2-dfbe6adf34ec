package com.yyigou.dsrp.cdc.service.v2.customer;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yyigou.ddc.common.page.PageDto;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.ddc.services.ddc.uim.vo.OrganizationVo;
import com.yyigou.dsrp.cdc.api.v2.company.vo.CompanyLinkmanVO;
import com.yyigou.dsrp.cdc.api.v2.customer.vo.*;
import com.yyigou.dsrp.cdc.client.v2.customer.request.CustomerAssignExeRequest;
import com.yyigou.dsrp.cdc.client.v2.customer.request.CustomerAssignMdmExeRequest;
import com.yyigou.dsrp.cdc.client.v2.customer.response.CustomerAssignExeResponse;
import com.yyigou.dsrp.cdc.client.v2.customer.response.CustomerBasicResponse;
import com.yyigou.dsrp.cdc.client.v2.customer.response.CustomerBizResponse;
import com.yyigou.dsrp.cdc.client.v2.customer.response.CustomerCoordinationCreateResponse;
import com.yyigou.dsrp.cdc.common.util.OperationModel;
import com.yyigou.dsrp.cdc.dao.v2.company.entity.CompanyLinkmanV2;
import com.yyigou.dsrp.cdc.dao.v2.company.entity.CompanyShippingAddressV2;
import com.yyigou.dsrp.cdc.dao.v2.customer.entity.*;
import com.yyigou.dsrp.cdc.model.v2.customer.req.*;

import java.util.List;
import java.util.Map;

public interface CustomerV2Service extends IService<CustomerV2> {
    PageVo<CustomerPageVO> manageFindListPage(OperationModel operationModel, CustomerManageQueryListPageReq queryReq, PageDto pageDTO);

    PageVo<CustomerPageVO> useFindListPage(OperationModel operationModel, CustomerUseQueryListPageReq queryReq, PageDto pageDTO);

    PageVo<CustomerPageVO> findListPageForTenant(CustomerFindQueryListPageForTenantReq queryReq, PageDto pageDTO);

    PageVo<CustomerReversePageVO> reverseUseFindListPageNoAuthZ(OperationModel operationModel, CustomerReverseUseQueryListPageNoAuthZReq queryReq, PageDto pageDTO);

    Boolean manageDeleteCustomer(OperationModel operationModel, CustomerDeleteReq params);

    CustomerVO getCustomer(OperationModel operationModel, CustomerGetReq params);

    CustomerV2 getCustomerByName(OperationModel operationModel, String customerName);

    CustomerV2 getCustomerByCode(OperationModel operationModel, String customerCode);

    CustomerVO getCustomerByCode(String enterprise, String customerCode);

    CustomerVO getDetailCustomerByName(OperationModel operationModel, String useOrgNo, String customerName);

    CustomerVO getDetailCustomerByCode(OperationModel operationModel, String useOrgNo, String customerCode);

    Boolean hasAssignCustomer(OperationModel operationModel, CustomerBase customerBase);

    CustomerBase getCustomerBaseByCodeAndUseOrgNo(String enterpriseNo, String customerCode, String useOrgNo);

    Boolean changeCustomerStatus(OperationModel operationModel, CustomerChangeStatusReq params);

    List<CustomerAssignVO> getAssignCustomer(OperationModel operationModel, CustomerGetAssignReq params);

    Long getPendingCount(OperationModel operationModel);

    Boolean checkOnlyCode(OperationModel operationModel, String no, String code);

    Boolean checkOnlyName(OperationModel operationModel, String no, String name);

    Boolean checkQuoteCategory(OperationModel operationModel, String customerCategoryNo);

    List<CustomerV2> queryByManageOrgNoList(OperationModel operationModel, String customerCategoryNo, List<String> manageOrgNoList);

    List<CustomerBasicResponse> queryStandardInfo(CustomerStandardQueryReq customerStandardQueryReq);

    List<CustomerBizResponse> queryWithBizInfo(CustomerStandardQueryReq customerStandardQueryReq);

    List<CustomerBizResponse> selectCustomerBizByCodeOrgPair(CustomerCodeOrgPairListReq customerCodeOrgPairListReq);

    PageVo<CustomerBizResponse> queryPageWithBizInfo(CustomerStandardQueryReq customerStandardQueryReq, PageDto pageDTO);

    CustomerCoordinationCreateResponse createCoordinationCustomer(String enterpriseNo, String orgNo, String customerName, String unifiedSocialCode, String omsCustomerNo);

    List<CustomerV2> queryByCompanyNo(String enterpriseNo, String companyNo);

    Boolean syncCustomerNameByCompanyName(String enterpriseNo, String customerCode, CustomerV2 customerV2);

    List<CustomerAssignExeResponse> assignCustomer(CustomerAssignExeRequest req);

    List<CustomerAssignExeResponse> assignMdmCustomer(CustomerAssignMdmExeRequest req);

    List<CustomerAssignExeResponse> deAssignCustomer(CustomerAssignExeRequest req);

    PageVo<CustomerPageVO> queryPageCustomer(OperationModel operationModel, CustomerPageQueryReq queryReq, PageDto pageDTO);

    PageVo<CustomerPageVO> querySpecifyOrgPageCustomer(OperationModel operationModel, CustomerPageQueryBySpecifyOrgReq params, PageDto pageDto);

    PageVo<CustomerFormalPageVO> findListPageByFormal(OperationModel operationModel, CustomerPageByFormalQueryReq queryReq, PageDto pageDTO);

    PageVo<CustomerPageVO> findNotInPageList(OperationModel operationModel, CustomerNotInPageListQueryReq queryReq, PageDto pageDTO);


    String manageSaveCustomerBasicAndBiz(OperationModel operationModel, CustomerSaveBasicAndBizReq params);

    String preValidateManageSaveCustomerBasicAndBiz(OperationModel operationModel, CustomerSaveBasicAndBizReq params);

    Boolean editCustomerBasicAndBiz(OperationModel operationModel, CustomerEditBasicAndBizReq params);

    Boolean assumeManageEditCustomerBasicAndBiz(OperationModel operationModel, CustomerAssumeManageEditBasicAndBizReq params);

    String preValidateAssumeManageEditCustomerBasicAndBiz(OperationModel operationModel, CustomerAssumeManageEditBasicAndBizReq params);

    Boolean editCustomerBasic(OperationModel operationModel, CustomerEditBasicReq params);

    String preValidateEditCustomerBasic(OperationModel operationModel, CustomerEditBasicReq params);

    List<Long> editCustomerAddressList(OperationModel operationModel, CustomerAddressListEditReq params);

    List<CompanyShippingAddressV2> overwriteCustomerAddressList(OperationModel operationModel, CustomerAddressListEditReq params);

    List<Long> saveCustomerAddressList(OperationModel operationModel, CustomerAddressListSaveReq params);

    Boolean updateCustomerAddressList(OperationModel operationModel, CustomerAddressListUpdateReq params);

    Boolean deleteCustomerAddressList(OperationModel operationModel, CustomerAddressListDeleteReq params);

    void preValidateEditCustomerAddressList(OperationModel operationModel, CustomerAddressListEditReq params);

    PageVo<CustomerAddressVO> findCustomerAddressPageList(OperationModel operationModel, CustomerAddressQueryReq params, PageDto pageDto);

    List<CustomerAddressVO> findCustomerAddressListByIds(String enterpriseNo, List<Long> addressIdList);

    List<Long> editCustomerLinkmanList(OperationModel operationModel, CustomerLinkmanListEditReq params);

    List<CompanyLinkmanV2> overwriteCustomerLinkmanList(OperationModel operationModel, CustomerLinkmanListEditReq params);

    List<Long> saveCustomerLinkmanList(OperationModel operationModel, CustomerLinkmanListSaveReq params);

    Boolean updateCustomerLinkmanList(OperationModel operationModel, CustomerLinkmanListUpdateReq params);

    Boolean deleteCustomerLinkmanList(OperationModel operationModel, CustomerLinkmanListDeleteReq params);

    void preValidateEditCustomerLinkmanList(OperationModel operationModel, CustomerLinkmanListEditReq params);

    List<Long> editCustomerSalesmanList(OperationModel operationModel, CustomerSalesmanListEditReq params);

    List<CustomerSalesManV2> overwriteCustomerSalesmanList(OperationModel operationModel, CustomerSalesmanListEditReq params);

    void preValidateEditCustomerSalesmanList(OperationModel operationModel, CustomerSalesmanListEditReq params);

    List<CustomerSalesManVO> findCustomerSalesManList(OperationModel operationModel, CustomerSalesManQueryReq params);

    PageVo<CustomerSalesManVO> findCustomerSalesManListPage(OperationModel operationModel, CustomerSalesManQueryReq params, PageDto pageDto);

    PageVo<CompanyLinkmanVO> findCustomerLinkmanListPage(OperationModel operationModel, CustomerLinkmanQueryReq params, PageDto pageDto);

    List<CompanyLinkmanVO> findCustomerLinkmanListByIds(String enterpriseNo, List<Long> linkmanIdList);

    List<CompanyLinkmanVO> findCustomerLinkmanList(OperationModel operationModel, CustomerLinkmanQueryReq params);

    List<CompanyLinkmanVO> findCustomerLinkmanList(String enterpriseNo, CustomerLinkmanQueryReq params);

    List<CustomerSalesManVO> findCustomerSalesManByIds(String enterpriseNo, List<Long> salesManIdList);

    List<CustomerInvoiceVO> findCustomerInvoiceList(String enterpriseNo, CustomerInvoiceQueryReq params);

    List<Long> editCustomerInvoiceList(OperationModel operationModel, CustomerInvoiceListEditReq params);

    List<CustomerInvoiceV2> overwriteCustomerInvoiceList(OperationModel operationModel, CustomerInvoiceListEditReq params);

    void preValidateEditCustomerInvoiceList(OperationModel operationModel, CustomerInvoiceListEditReq params);

    List<Long> editCustomerBankList(OperationModel operationModel, CustomerBankListEditReq params);

    void preValidateEditCustomerBankList(OperationModel operationModel, CustomerBankListEditReq params);

    List<CustomerV2> findByInternalOrgNo(OperationModel operationModel, String customerCode, String internalOrgNo);

    PageVo<OrganizationVo> findOrgListPageBySetting(OperationModel operationModel, CustomerEligibleOrgListQueryReq params, PageDto pageDto);

    Map<String, Object> manageApprove(OperationModel operationModel, CustomerApply customerApply, CustomerApplyItem customerApplyItem, CustomerApplyFormReq customerApplyFormReq, CustomerApplyApproveReq req);

    Map<String, String> preValidateManageApprove(OperationModel operationModel, CustomerApply customerApply, CustomerApplyItem customerApplyItem, CustomerApplyFormReq customerApplyFormReq, CustomerApplyApproveReq req);


    void validateSaveBasicAndInfoReqByApply(OperationModel operationModel, String applyContent);

    void validateEditBasicReqByApply(OperationModel operationModel, String applyContent);


    Boolean changeCustomerGspStatus(CustomerGspStatusReq params);

    Boolean checkConvertToSupplier(OperationModel operationModel, String customerCode);

}
