package com.yyigou.dsrp.cdc.service.company.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yyigou.dsrp.cdc.client.company.request.CompanyCertFindRequest;
import com.yyigou.dsrp.cdc.client.company.response.CompanyCertFindResponse;
import com.yyigou.dsrp.cdc.common.enums.DeletedEnum;
import com.yyigou.dsrp.cdc.common.util.CommonUtil;
import com.yyigou.dsrp.cdc.dao.company.CompanyCertDAO;
import com.yyigou.dsrp.cdc.dao.company.entity.CompanyCert;
import com.yyigou.dsrp.cdc.service.company.CompanyCertService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class CompanyCertServiceImpl implements CompanyCertService {

    @Resource
    private CompanyCertDAO companyCertDao;

    @Override
    public List<CompanyCertFindResponse> findCompanyCertList(CompanyCertFindRequest request) {
        CommonUtil.checkEmptyThrowEx(request.getEnterpriseNo(), "租户编号不能为空");
        CommonUtil.checkEmptyThrowEx(request.getCompanyNoList(), "企业编号不能为空");
        LambdaQueryWrapper<CompanyCert> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CompanyCert::getEnterpriseNo, request.getEnterpriseNo()).in(CompanyCert::getCompanyNo, request.getCompanyNoList())
                .in(CollectionUtils.isNotEmpty(request.getCertTypeList()), CompanyCert::getCertType, request.getCertTypeList()).eq(CompanyCert::getDeleted, DeletedEnum.UN_DELETE.getValue()).eq(CompanyCert::getStatus, new Integer(1));

        List<CompanyCert> certList = companyCertDao.selectList(queryWrapper);
        List<CompanyCertFindResponse> result = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(certList)) {
            certList.forEach(t -> {
                CompanyCertFindResponse response = new CompanyCertFindResponse();
                BeanUtils.copyProperties(t, response);
                result.add(response);
            });
        }
        return result;
    }

}
