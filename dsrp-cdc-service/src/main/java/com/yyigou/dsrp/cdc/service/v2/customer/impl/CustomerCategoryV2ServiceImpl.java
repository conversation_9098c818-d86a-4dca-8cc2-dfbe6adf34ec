package com.yyigou.dsrp.cdc.service.v2.customer.impl;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.yyigou.ddc.common.error.ErrorCode;
import com.yyigou.ddc.common.exception.BusinessException;
import com.yyigou.ddc.common.page.PageDto;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.ddc.common.treegrid.TreeGrid;
import com.yyigou.ddc.common.treegrid.TreeGridUtils;
import com.yyigou.ddc.common.util.ValidatorUtils;
import com.yyigou.ddc.services.ddc.uim.vo.OrganizationVo;
import com.yyigou.ddc.services.dlog.dto.DLogLevel;
import com.yyigou.dsrp.cdc.api.customer.dto.CustomerExternalSaveDTO;
import com.yyigou.dsrp.cdc.api.v2.customer.vo.CustomerCategoryTreeVO;
import com.yyigou.dsrp.cdc.api.v2.customer.vo.CustomerCategoryUseOrgVO;
import com.yyigou.dsrp.cdc.api.v2.customer.vo.CustomerCategoryVO;
import com.yyigou.dsrp.cdc.common.enums.CommonIfEnum;
import com.yyigou.dsrp.cdc.common.enums.CommonStatusEnum;
import com.yyigou.dsrp.cdc.common.enums.DeletedEnum;
import com.yyigou.dsrp.cdc.common.enums.HierarchyQueryEnum;
import com.yyigou.dsrp.cdc.common.util.CommonUtil;
import com.yyigou.dsrp.cdc.common.util.OperationModel;
import com.yyigou.dsrp.cdc.dao.v2.customer.CustomerCategoryBaseDAO;
import com.yyigou.dsrp.cdc.dao.v2.customer.CustomerCategoryV2DAO;
import com.yyigou.dsrp.cdc.dao.v2.customer.entity.CustomerCategoryBase;
import com.yyigou.dsrp.cdc.dao.v2.customer.entity.CustomerCategoryV2;
import com.yyigou.dsrp.cdc.dao.v2.customer.entity.CustomerV2;
import com.yyigou.dsrp.cdc.manager.integration.dlog.BusinessLogService;
import com.yyigou.dsrp.cdc.manager.integration.numberCenter.NumberCenterService;
import com.yyigou.dsrp.cdc.manager.integration.uap.GradeControlService;
import com.yyigou.dsrp.cdc.manager.integration.uap.res.GradeCancelAssignRes;
import com.yyigou.dsrp.cdc.manager.integration.uim.OrganizationService;
import com.yyigou.dsrp.cdc.model.constant.BillNameConstant;
import com.yyigou.dsrp.cdc.model.constant.NumberCenterConstant;
import com.yyigou.dsrp.cdc.model.constant.ServiceConstant;
import com.yyigou.dsrp.cdc.model.constant.ViewNameConstant;
import com.yyigou.dsrp.cdc.model.v2.customer.req.*;
import com.yyigou.dsrp.cdc.service.utils.PageUtils;
import com.yyigou.dsrp.cdc.service.v2.customer.CustomerCategoryV2Service;
import com.yyigou.dsrp.cdc.service.v2.customer.CustomerV2Service;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service("customerCategoryV2Service")
@RequiredArgsConstructor
@Slf4j
public class CustomerCategoryV2ServiceImpl extends ServiceImpl<CustomerCategoryV2DAO, CustomerCategoryV2> implements CustomerCategoryV2Service {
    private static final int MAX_RECURSION_DEPTH = 10; // 设置最大递归深度为10层

    private static final String FIELD_USE_ORG_NO = "use_org_no";

    private final NumberCenterService numberCenterService;
    @Resource
    private CustomerCategoryV2DAO customerCategoryV2DAO;
    @Resource
    private CustomerCategoryBaseDAO customerCategoryBaseDAO;
    @Resource
    private CustomerV2Service customerV2Service;

    @Resource
    private BusinessLogService businessLogService;

    @Resource
    private OrganizationService organizationService;

    @Resource
    private GradeControlService gradeControlService;

    private List<CustomerCategoryTreeVO> queryEligibleCustomerCategory(OperationModel operationModel, CustomerCategoryQueryTreeReq queryTreeReq) {
        log.warn("queryEligibleCustomerCategoryReq，operationModel={},queryTreeReq={}", operationModel, queryTreeReq);

        //单租户附上默认的useOrgNo
        if (StringUtils.isBlank(queryTreeReq.getUseOrgNo())) {
            queryTreeReq.setUseOrgNo(operationModel.getOrgNo());
        }

        ValidatorUtils.checkEmptyThrowEx(queryTreeReq.getUseOrgNo(), "使用组织编号不能为空");

        //单组织和多组织保持一样的逻辑
        //获取被分派的分类节点
        List<CustomerCategoryV2> assignCategoryList = customerCategoryV2DAO.selectListByUseOrgNo(operationModel.getEnterpriseNo(), queryTreeReq.getUseOrgNo(), DeletedEnum.UN_DELETE.getValue());

        log.warn("assignCategoryList={}", assignCategoryList);

        if (CollectionUtils.isEmpty(assignCategoryList)) {
            return Collections.emptyList();
        }

        //被分派的分类节点-按管理组织进行分组
        Map<String, List<CustomerCategoryV2>> authedManageOrgNo2CategoryList = assignCategoryList.stream().collect(Collectors.groupingBy(CustomerCategoryV2::getManageOrgNo));

        //所有管理组织的分类集合
        List<CustomerCategoryV2> categoryListByManageOrgNo = customerCategoryV2DAO.getByManageOrgNoList(operationModel.getEnterpriseNo(), new ArrayList<>(authedManageOrgNo2CategoryList.keySet()));

        log.warn("categoryListByManageOrgNo={}", categoryListByManageOrgNo);

        //所有分类-按管理组织进行分组
        Map<String, List<CustomerCategoryV2>> allManageOrgNo2CategoryList = categoryListByManageOrgNo.stream().collect(Collectors.groupingBy(CustomerCategoryV2::getManageOrgNo));

        List<CustomerCategoryTreeVO> treeList = new ArrayList<>();

        //按管理组织进行分类树的构建
        authedManageOrgNo2CategoryList.forEach((manageOrgNo, authedCategoryListByManageOrgNoList) -> {
            //分派的是叶子节点，需要把叶子节点的祖先节点也加入到结果中
            List<CustomerCategoryV2> allNodes = allManageOrgNo2CategoryList.get(manageOrgNo);

            ValidatorUtils.checkTrueThrowEx(hasCircularDependency(allNodes), "客户分类存在循环依赖");

            Map<String, CustomerCategoryV2> no2CustomerCategoryV2Map = allNodes.stream().collect(Collectors.toMap(CustomerCategoryV2::getNo, customerCategoryV2 -> customerCategoryV2));

            List<CustomerCategoryV2> authorizedNodes = findAuthorizedNodes(allNodes, authedCategoryListByManageOrgNoList);

            log.warn("findAuthorizedNodes,allNodes={},authedCategoryListByManageOrgNoList={},authorizedNodes={}", allNodes, authedCategoryListByManageOrgNoList, authorizedNodes);

            List<OrganizationVo> organizationVoList = organizationService.findListNoAuth(operationModel.getEnterpriseNo(), new ArrayList<>(authorizedNodes.stream().map(CustomerCategoryV2::getManageOrgNo).collect(Collectors.toSet())));
            final Map<String, OrganizationVo> orgNo2OrganizationVo = organizationVoList.stream().collect(Collectors.toMap(OrganizationVo::getOrgNo, Function.identity()));

            authorizedNodes.forEach(customerCategoryV2 -> {
                CustomerCategoryTreeVO tree = new CustomerCategoryTreeVO();
                BeanUtils.copyProperties(customerCategoryV2, tree);

                // 填充上级分类名称
                if (ServiceConstant.TOP_PARENT_NO.equals(customerCategoryV2.getParentNo())) {
                    tree.setParentName("");
                } else {
                    CustomerCategoryV2 parentNode = no2CustomerCategoryV2Map.get(customerCategoryV2.getParentNo());
                    if (null != parentNode) {
                        tree.setParentName(parentNode.getCategoryName());
                    }
                }

                // 填充管理组织名称
                if (orgNo2OrganizationVo.containsKey(customerCategoryV2.getManageOrgNo())) {
                    OrganizationVo organizationVo = orgNo2OrganizationVo.get(customerCategoryV2.getManageOrgNo());
                    tree.setManageOrgName(organizationVo.getOrgName());
                    tree.setManageOrgCode(organizationVo.getOrgCode());
                }

                treeList.add(tree);
            });
        });

        log.warn("treeList={}", treeList);

        return treeList;
    }

    @Override
    public List<CustomerCategoryTreeVO> queryTree(OperationModel operationModel, CustomerCategoryQueryTreeReq queryTreeReq) {
        return TreeGridUtils.getChildCategoryTrees(queryEligibleCustomerCategory(operationModel, queryTreeReq), ServiceConstant.TOP_PARENT_NO);
    }

    @Override
    public List<CustomerCategoryTreeVO> queryManageTree(OperationModel operationModel, CustomerCategoryQueryCategoryTreeReq queryTreeReq) {
        ValidatorUtils.checkEmptyThrowEx(queryTreeReq.getManageOrgNo(), "管理组织编号不能为空");

        //单组织和多组织保持一样的逻辑
        List<CustomerCategoryV2> categoryList = customerCategoryV2DAO.selectList(Wrappers.<CustomerCategoryV2>lambdaQuery()
                .eq(CustomerCategoryV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CustomerCategoryV2::getManageOrgNo, queryTreeReq.getManageOrgNo())
                .eq(CustomerCategoryV2::getDeleted, DeletedEnum.UN_DELETE.getValue())
        );

        if (CollectionUtils.isEmpty(categoryList)) {
            return Collections.emptyList();
        }

        Map<String, CustomerCategoryV2> no2CustomerCategoryV2Map = categoryList.stream().collect(Collectors.toMap(CustomerCategoryV2::getNo, customerCategoryV2 -> customerCategoryV2));

        List<CustomerCategoryTreeVO> treeList = new ArrayList<>();

        List<OrganizationVo> organizationVoList = organizationService.findListNoAuth(operationModel.getEnterpriseNo(), new ArrayList<>(categoryList.stream().map(CustomerCategoryV2::getManageOrgNo).collect(Collectors.toSet())));
        Map<String, OrganizationVo> orgNo2OrganizationVo = organizationVoList.stream().collect(Collectors.toMap(OrganizationVo::getOrgNo, Function.identity()));

        for (CustomerCategoryV2 customerCategoryV2 : categoryList) {
            CustomerCategoryTreeVO tree = new CustomerCategoryTreeVO();
            BeanUtils.copyProperties(customerCategoryV2, tree);

            // 填充上级分类名称
            if (ServiceConstant.TOP_PARENT_NO.equals(customerCategoryV2.getParentNo())) {
                tree.setParentName("");
            } else {
                CustomerCategoryV2 parentNode = no2CustomerCategoryV2Map.get(customerCategoryV2.getParentNo());
                if (null != parentNode) {
                    tree.setParentName(parentNode.getCategoryName());
                }
            }

            // 填充管理组织名称
            if (orgNo2OrganizationVo.containsKey(customerCategoryV2.getManageOrgNo())) {
                OrganizationVo organizationVo = orgNo2OrganizationVo.get(customerCategoryV2.getManageOrgNo());
                tree.setManageOrgName(organizationVo.getOrgName());
                tree.setManageOrgCode(organizationVo.getOrgCode());
            }

            // 填充使用组织名称
            List<CustomerCategoryBase> customerCategoryBases = customerCategoryBaseDAO.selectList(Wrappers.<CustomerCategoryBase>lambdaQuery()
                    .eq(CustomerCategoryBase::getEnterpriseNo, operationModel.getEnterpriseNo())
                    .eq(CustomerCategoryBase::getManageOrgNo, customerCategoryV2.getManageOrgNo())
                    .eq(CustomerCategoryBase::getCategoryNo, customerCategoryV2.getNo())
                    .eq(CustomerCategoryBase::getDeleted, DeletedEnum.UN_DELETE.getValue()));
            if (CollectionUtils.isNotEmpty(customerCategoryBases)) {
                List<String> names = new ArrayList<>();
                List<CustomerCategoryUseOrgVO> customerCategoryUseOrgVOList = new ArrayList<>();

                organizationVoList = organizationService.findListNoAuth(operationModel.getEnterpriseNo(), new ArrayList<>(customerCategoryBases.stream().map(CustomerCategoryBase::getUseOrgNo).collect(Collectors.toSet())));
                orgNo2OrganizationVo = organizationVoList.stream().collect(Collectors.toMap(OrganizationVo::getOrgNo, Function.identity()));

                for (CustomerCategoryBase customerCategoryBase : customerCategoryBases) {
                    if (orgNo2OrganizationVo.containsKey(customerCategoryBase.getUseOrgNo())) {
                        OrganizationVo organizationVo = orgNo2OrganizationVo.get(customerCategoryBase.getUseOrgNo());
                        CustomerCategoryUseOrgVO customerCategoryUseOrgVO = new CustomerCategoryUseOrgVO();
                        customerCategoryUseOrgVO.setId(customerCategoryBase.getId());
                        customerCategoryUseOrgVO.setUseOrgNo(organizationVo.getOrgNo());
                        customerCategoryUseOrgVO.setUseOrgCode(organizationVo.getOrgCode());
                        customerCategoryUseOrgVO.setUseOrgName(organizationVo.getOrgName());
                        customerCategoryUseOrgVOList.add(customerCategoryUseOrgVO);

                        names.add(organizationVo.getOrgName());

                    }
                }
                tree.setUseOrgNames(String.join("，", names));

                tree.setUseOrgList(customerCategoryUseOrgVOList);
            }

            treeList.add(tree);
        }

        return TreeGridUtils.getChildCategoryTrees(treeList, ServiceConstant.TOP_PARENT_NO);
    }

    @Override
    public List<CustomerCategoryTreeVO> queryUseTree(OperationModel operationModel, CustomerCategoryQueryUseCategoryTreeReq queryTreeReq) {
        CustomerCategoryQueryTreeReq customerCategoryQueryTreeReq = new CustomerCategoryQueryTreeReq();
        customerCategoryQueryTreeReq.setEnterpriseNo(operationModel.getEnterpriseNo());
        customerCategoryQueryTreeReq.setUseOrgNo(queryTreeReq.getUseOrgNo());

        return TreeGridUtils.getChildCategoryTrees(queryEligibleCustomerCategory(operationModel, customerCategoryQueryTreeReq), ServiceConstant.TOP_PARENT_NO);
    }

    /**
     * 分类树对接参照
     *
     * @param operationModel
     * @param queryTreeReq
     * @return
     */
    @Override
    public List<CustomerCategoryTreeVO> queryReferTree(OperationModel operationModel, CustomerCategoryQuerySolutionTreeReq queryTreeReq) {
        ValidatorUtils.checkEmptyThrowEx(queryTreeReq.getQueryConditionList(), "查询条件为空");

        String[] useOrgNoArray = null;
        for (CustomerCategoryQuerySolutionTreeReq.QueryCondition queryCondition : queryTreeReq.getQueryConditionList()) {
            if (FIELD_USE_ORG_NO.equals(queryCondition.getColumn())) {
                if (StringUtils.isNotEmpty(queryCondition.getValue())) {
                    useOrgNoArray = StringUtils.split(queryCondition.getValue(), ",");
                }
                break;
            }
        }

        if (!operationModel.getSingleOrgFlag()) {
            ValidatorUtils.checkTrueThrowEx(ArrayUtils.isEmpty(useOrgNoArray), "使用组织编号为空");
        } else {
            useOrgNoArray = new String[]{operationModel.getOrgNo()};
        }

        List<CustomerCategoryTreeVO> customerCategoryTreeVOS = queryReferTree(operationModel, useOrgNoArray);

        return TreeGridUtils.getChildCategoryTrees(customerCategoryTreeVOS, ServiceConstant.TOP_PARENT_NO);
    }

    private List<CustomerCategoryTreeVO> queryReferTree(OperationModel operationModel, String[] useOrgNoArray) {
        log.warn("queryReferTree，operationModel={},useOrgNoArray={}", operationModel, useOrgNoArray);

        //单组织和多组织保持一样的逻辑
        //获取被分派的分类节点
        List<CustomerCategoryV2> assignCategoryList = customerCategoryV2DAO.selectListByUseOrgNoList(operationModel.getEnterpriseNo(), Arrays.asList(useOrgNoArray), DeletedEnum.UN_DELETE.getValue());

        log.warn("assignCategoryList={}", assignCategoryList);

        if (CollectionUtils.isEmpty(assignCategoryList)) {
            return Collections.emptyList();
        }

        //被分派的分类节点-按管理组织进行分组
        Map<String, List<CustomerCategoryV2>> authedManageOrgNo2CategoryList = assignCategoryList.stream().collect(Collectors.groupingBy(CustomerCategoryV2::getManageOrgNo));

        //所有管理组织的分类集合
        List<CustomerCategoryV2> categoryListByManageOrgNo = customerCategoryV2DAO.getByManageOrgNoList(operationModel.getEnterpriseNo(), new ArrayList<>(authedManageOrgNo2CategoryList.keySet()));

        log.warn("categoryListByManageOrgNo={}", categoryListByManageOrgNo);

        //所有分类-按管理组织进行分组
        Map<String, List<CustomerCategoryV2>> allManageOrgNo2CategoryList = categoryListByManageOrgNo.stream().collect(Collectors.groupingBy(CustomerCategoryV2::getManageOrgNo));

        List<CustomerCategoryTreeVO> treeList = new ArrayList<>();

        //按管理组织进行分类树的构建
        authedManageOrgNo2CategoryList.forEach((manageOrgNo, authedCategoryListByManageOrgNoList) -> {
            //分派的是叶子节点，需要把叶子节点的祖先节点也加入到结果中
            List<CustomerCategoryV2> allNodes = allManageOrgNo2CategoryList.get(manageOrgNo);

            ValidatorUtils.checkTrueThrowEx(hasCircularDependency(allNodes), "客户分类存在循环依赖");

            Map<String, CustomerCategoryV2> no2CustomerCategoryV2Map = allNodes.stream().collect(Collectors.toMap(CustomerCategoryV2::getNo, customerCategoryV2 -> customerCategoryV2));

            List<CustomerCategoryV2> authorizedNodes = findAuthorizedNodes(allNodes, authedCategoryListByManageOrgNoList);

            log.warn("findAuthorizedNodes,allNodes={},authedCategoryListByManageOrgNoList={},authorizedNodes={}", allNodes, authedCategoryListByManageOrgNoList, authorizedNodes);

            List<OrganizationVo> organizationVoList = organizationService.findListNoAuth(operationModel.getEnterpriseNo(), new ArrayList<>(authorizedNodes.stream().map(CustomerCategoryV2::getManageOrgNo).collect(Collectors.toSet())));
            final Map<String, OrganizationVo> orgNo2OrganizationVo = organizationVoList.stream().collect(Collectors.toMap(OrganizationVo::getOrgNo, Function.identity()));

            authorizedNodes.forEach(customerCategoryV2 -> {
                CustomerCategoryTreeVO tree = new CustomerCategoryTreeVO();
                BeanUtils.copyProperties(customerCategoryV2, tree);

                // 填充上级分类名称
                if (ServiceConstant.TOP_PARENT_NO.equals(customerCategoryV2.getParentNo())) {
                    tree.setParentName("");
                } else {
                    CustomerCategoryV2 parentNode = no2CustomerCategoryV2Map.get(customerCategoryV2.getParentNo());
                    if (null != parentNode) {
                        tree.setParentName(parentNode.getCategoryName());
                    }
                }

                // 填充管理组织名称
                if (orgNo2OrganizationVo.containsKey(customerCategoryV2.getManageOrgNo())) {
                    OrganizationVo organizationVo = orgNo2OrganizationVo.get(customerCategoryV2.getManageOrgNo());
                    tree.setManageOrgName(organizationVo.getOrgName());
                    tree.setManageOrgCode(organizationVo.getOrgCode());
                }

                treeList.add(tree);
            });
        });

        log.warn("treeList={}", treeList);

        return treeList;
    }


    @Override
    public PageVo<CustomerCategoryVO> queryListPage(OperationModel operationModel, CustomerCategoryQueryListPageReq queryListReq, PageDto pageDTO) {
        if (StringUtils.isBlank(queryListReq.getUseOrgNo())) {
            ValidatorUtils.checkEmptyThrowEx(operationModel.getOrgCode(), "使用组织编号不能为空");
            queryListReq.setUseOrgNo(operationModel.getOrgNo());
        }

        if (StringUtils.isBlank(queryListReq.getParentNo())) {
            queryListReq.setParentNo(ServiceConstant.TOP_PARENT_NO);
        }

        CustomerCategoryQueryTreeReq queryTreeReq = new CustomerCategoryQueryTreeReq();
        queryTreeReq.setEnterpriseNo(operationModel.getEnterpriseNo());
        queryTreeReq.setUseOrgNo(queryListReq.getUseOrgNo());

        List<CustomerCategoryTreeVO> customerCategoryTreeVOS = queryEligibleCustomerCategory(operationModel, queryTreeReq);

        List<CustomerCategoryTreeVO> treeVoList = TreeGridUtils.getChildCategoryTrees(customerCategoryTreeVOS, ServiceConstant.TOP_PARENT_NO);

        List<String> allNos = new ArrayList<>();

        if (HierarchyQueryEnum.SUB_LEVEL.getValue().equals(queryListReq.getShowType())) {// 查下级节点
            findNextLevels(treeVoList, queryListReq.getParentNo(), allNos, 0);

            log.warn("findNextLevels，treeVoList={},parentNo={},allNos={}", treeVoList, queryListReq.getParentNo(), allNos);

        } else {// 查所有子孙节点
            if (ServiceConstant.TOP_PARENT_NO.equals(queryListReq.getParentNo())) {
                findAllDescendants(treeVoList, allNos, 0);

                log.warn("findAllDescendants，treeVoList={},allNos={}", treeVoList, allNos);

            } else {
                //找到指定的节点
                CustomerCategoryTreeVO specificNo = findSpecificNode(treeVoList, queryListReq.getParentNo(), 0);

                log.warn("findSpecificNode，treeVoList={},parentNo={}", treeVoList, queryListReq.getParentNo());

                if (null != specificNo) {
                    allNos.add(queryListReq.getParentNo());

                    findAllDescendants(specificNo.getChildren(), allNos, 0);

                    log.warn("findAllDescendants，treeVoList={},allNos={}", specificNo.getChildren(), allNos);
                }
            }
        }

        if (CollectionUtils.isEmpty(allNos)) {
            return new PageVo<>();
        }

        Page<CustomerCategoryV2> page = PageHelper.startPage(pageDTO.getPageIndex(), pageDTO.getPageSize(), pageDTO.getOrderBy());
        LambdaQueryWrapper<CustomerCategoryV2> wrapper = Wrappers.<CustomerCategoryV2>lambdaQuery()
                .eq(CustomerCategoryV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CustomerCategoryV2::getDeleted, DeletedEnum.UN_DELETE.getValue())
                .eq(null != queryListReq.getStatus(), CustomerCategoryV2::getStatus, queryListReq.getStatus())
                .nested(StringUtils.isNotBlank(queryListReq.getKeywords()), w ->
                        w.like(CustomerCategoryV2::getCategoryCode, queryListReq.getKeywords())
                                .or()
                                .like(CustomerCategoryV2::getCategoryName, queryListReq.getKeywords()))
                .in(CustomerCategoryV2::getNo, allNos)
                .in(CollectionUtils.isNotEmpty(queryListReq.getIdList()), CustomerCategoryV2::getNo, queryListReq.getIdList())
                .last("order by level asc, sort_num is null asc, sort_num asc");

        customerCategoryV2DAO.selectList(wrapper);

        return PageUtils.convertPageVo(page, data -> {
            List<CustomerCategoryVO> detailVOList = new ArrayList<>();
            if (Objects.nonNull(data)) {
                Map<String, CustomerCategoryTreeVO> no2CategoryTreeVOMap = customerCategoryTreeVOS.stream().collect(Collectors.toMap(CustomerCategoryTreeVO::getNo, Function.identity()));
                Set<String> parentNoSet = customerCategoryTreeVOS.stream().map(CustomerCategoryTreeVO::getParentNo).filter(parentNo -> !ServiceConstant.TOP_PARENT_NO.equals(parentNo)).collect(Collectors.toSet());

                data.forEach(customerCategoryV2 -> {
                    CustomerCategoryVO customerCategoryVO = new CustomerCategoryVO();
                    BeanUtils.copyProperties(customerCategoryV2, customerCategoryVO);

                    // 填充管理组织名称
                    customerCategoryVO.setManageOrgName(no2CategoryTreeVOMap.get(customerCategoryV2.getNo()).getManageOrgName());
                    customerCategoryVO.setManageOrgCode(no2CategoryTreeVOMap.get(customerCategoryV2.getNo()).getManageOrgCode());

                    // 填充使用组织名称
                    customerCategoryVO.setUseOrgNames(no2CategoryTreeVOMap.get(customerCategoryV2.getNo()).getUseOrgNames());
                    customerCategoryVO.setUseOrgList(no2CategoryTreeVOMap.get(customerCategoryV2.getNo()).getUseOrgList());

                    // 填充子孙节点标记
                    customerCategoryVO.setDescendantsFlag(parentNoSet.contains(customerCategoryV2.getNo()) ? CommonIfEnum.YES.getValue() : CommonIfEnum.NO.getValue());

                    // 填充请求的使用组织
                    customerCategoryVO.setInputUseOrgNo(queryListReq.getUseOrgNo());
                    customerCategoryVO.setInputUseOrgName(queryListReq.getUseOrgName());

                    // 填充上级分类名称
                    if (ServiceConstant.TOP_PARENT_NO.equals(no2CategoryTreeVOMap.get(customerCategoryV2.getNo()).getParentNo())) {
                        customerCategoryVO.setParentNo("");
                        customerCategoryVO.setParentName("");
                    } else {
                        customerCategoryVO.setParentName(no2CategoryTreeVOMap.get(customerCategoryV2.getNo()).getParentName());
                    }

                    customerCategoryVO.setStatusName(CommonStatusEnum.INVALID.getValue().toString().equals(customerCategoryVO.getStatus()) ? "停用" : "启用");

                    detailVOList.add(customerCategoryVO);
                });
            }
            return detailVOList;
        });
    }

    @Override
    public List<CustomerCategoryVO> queryList(CustomerCategoryQueryListPageReq queryListReq) {
        ValidatorUtils.checkEmptyThrowEx(queryListReq.getEnterpriseNo(), "租户编号不能为空");
        ValidatorUtils.checkEmptyThrowEx(queryListReq.getUseOrgNo(), "使用组织编号不能为空");
        ValidatorUtils.checkEmptyThrowEx(queryListReq.getIdList(), "客户分类编号不能为空");


        OperationModel operationModel = new OperationModel();
        operationModel.setEnterpriseNo(queryListReq.getEnterpriseNo());
        operationModel.setOrgNo(queryListReq.getUseOrgNo());

        if (StringUtils.isBlank(queryListReq.getUseOrgNo())) {
            ValidatorUtils.checkEmptyThrowEx(operationModel.getOrgCode(), "使用组织编号不能为空");
            queryListReq.setUseOrgNo(operationModel.getOrgNo());
        }

        if (StringUtils.isBlank(queryListReq.getParentNo())) {
            queryListReq.setParentNo(ServiceConstant.TOP_PARENT_NO);
        }

        CustomerCategoryQueryTreeReq queryTreeReq = new CustomerCategoryQueryTreeReq();
        queryTreeReq.setEnterpriseNo(operationModel.getEnterpriseNo());
        queryTreeReq.setUseOrgNo(queryListReq.getUseOrgNo());

        List<CustomerCategoryTreeVO> customerCategoryTreeVOS = queryEligibleCustomerCategory(operationModel, queryTreeReq);

        List<CustomerCategoryTreeVO> treeVoList = TreeGridUtils.getChildCategoryTrees(customerCategoryTreeVOS, ServiceConstant.TOP_PARENT_NO);

        List<String> allNos = new ArrayList<>();

        if (HierarchyQueryEnum.SUB_LEVEL.getValue().equals(queryListReq.getShowType())) {// 查下级节点
            findNextLevels(treeVoList, queryListReq.getParentNo(), allNos, 0);

            log.warn("findNextLevels，treeVoList={},parentNo={},allNos={}", treeVoList, queryListReq.getParentNo(), allNos);

        } else {// 查所有子孙节点
            if (ServiceConstant.TOP_PARENT_NO.equals(queryListReq.getParentNo())) {
                findAllDescendants(treeVoList, allNos, 0);

                log.warn("findAllDescendants，treeVoList={},allNos={}", treeVoList, allNos);

            } else {
                //找到指定的节点
                CustomerCategoryTreeVO specificNo = findSpecificNode(treeVoList, queryListReq.getParentNo(), 0);

                log.warn("findSpecificNode，treeVoList={},parentNo={}", treeVoList, queryListReq.getParentNo());

                if (null != specificNo) {
                    allNos.add(queryListReq.getParentNo());

                    findAllDescendants(specificNo.getChildren(), allNos, 0);

                    log.warn("findAllDescendants，treeVoList={},allNos={}", specificNo.getChildren(), allNos);
                }
            }
        }

        if (CollectionUtils.isEmpty(allNos)) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<CustomerCategoryV2> wrapper = Wrappers.<CustomerCategoryV2>lambdaQuery()
                .eq(CustomerCategoryV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CustomerCategoryV2::getDeleted, DeletedEnum.UN_DELETE.getValue())
                .eq(null != queryListReq.getStatus(), CustomerCategoryV2::getStatus, queryListReq.getStatus())
                .nested(StringUtils.isNotBlank(queryListReq.getKeywords()), w ->
                        w.like(CustomerCategoryV2::getCategoryCode, queryListReq.getKeywords())
                                .or()
                                .like(CustomerCategoryV2::getCategoryName, queryListReq.getKeywords()))
                .in(CustomerCategoryV2::getNo, allNos)
                .in(CollectionUtils.isNotEmpty(queryListReq.getIdList()), CustomerCategoryV2::getNo, queryListReq.getIdList())
                .last("order by level asc, sort_num is null asc, sort_num asc");

        List<CustomerCategoryV2> customerCategoryV2s = customerCategoryV2DAO.selectList(wrapper);

        if (CollectionUtils.isEmpty(customerCategoryV2s)) {
            return Collections.emptyList();
        }

        List<CustomerCategoryVO> detailVOList = new ArrayList<>(customerCategoryV2s.size());
        Map<String, CustomerCategoryTreeVO> no2CategoryTreeVOMap = customerCategoryTreeVOS.stream().collect(Collectors.toMap(CustomerCategoryTreeVO::getNo, Function.identity()));
        Set<String> parentNoSet = customerCategoryTreeVOS.stream().map(CustomerCategoryTreeVO::getParentNo).filter(parentNo -> !ServiceConstant.TOP_PARENT_NO.equals(parentNo)).collect(Collectors.toSet());

        for (CustomerCategoryV2 customerCategoryV2 : customerCategoryV2s) {

            CustomerCategoryVO customerCategoryVO = new CustomerCategoryVO();
            BeanUtils.copyProperties(customerCategoryV2, customerCategoryVO);

            // 填充管理组织名称
            customerCategoryVO.setManageOrgName(no2CategoryTreeVOMap.get(customerCategoryV2.getNo()).getManageOrgName());
            customerCategoryVO.setManageOrgCode(no2CategoryTreeVOMap.get(customerCategoryV2.getNo()).getManageOrgCode());

            // 填充使用组织名称
            customerCategoryVO.setUseOrgNames(no2CategoryTreeVOMap.get(customerCategoryV2.getNo()).getUseOrgNames());
            customerCategoryVO.setUseOrgList(no2CategoryTreeVOMap.get(customerCategoryV2.getNo()).getUseOrgList());

            // 填充子孙节点标记
            customerCategoryVO.setDescendantsFlag(parentNoSet.contains(customerCategoryV2.getNo()) ? CommonIfEnum.YES.getValue() : CommonIfEnum.NO.getValue());

            // 填充请求的使用组织
            customerCategoryVO.setInputUseOrgNo(queryListReq.getUseOrgNo());
            customerCategoryVO.setInputUseOrgName(queryListReq.getUseOrgName());

            // 填充上级分类名称
            if (ServiceConstant.TOP_PARENT_NO.equals(no2CategoryTreeVOMap.get(customerCategoryV2.getNo()).getParentNo())) {
                customerCategoryVO.setParentNo("");
                customerCategoryVO.setParentName("");
            } else {
                customerCategoryVO.setParentName(no2CategoryTreeVOMap.get(customerCategoryV2.getNo()).getParentName());
            }

            customerCategoryVO.setStatusName(CommonStatusEnum.INVALID.getValue().toString().equals(customerCategoryVO.getStatus()) ? "停用" : "启用");

            detailVOList.add(customerCategoryVO);

        }

        return detailVOList;
    }

    @Override
    public Boolean checkUniqueName(OperationModel operationModel, String no, String name, String parentNo) {
        return customerCategoryV2DAO.selectCount(new LambdaQueryWrapper<CustomerCategoryV2>()
                .eq(CustomerCategoryV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CustomerCategoryV2::getCategoryName, name)
                .eq(CustomerCategoryV2::getParentNo, parentNo)
                .eq(CustomerCategoryV2::getDeleted, DeletedEnum.UN_DELETE.getValue())
                .ne(StringUtils.isNotEmpty(no), CustomerCategoryV2::getNo, no)) <= 0;
    }

    @Override
    public Boolean checkUniqueCode(OperationModel operationModel, String no, String code) {
        return customerCategoryV2DAO.selectCount(new LambdaQueryWrapper<CustomerCategoryV2>()
                .eq(CustomerCategoryV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CustomerCategoryV2::getCategoryCode, code)
                .eq(CustomerCategoryV2::getDeleted, DeletedEnum.UN_DELETE.getValue())
                .ne(StringUtils.isNotEmpty(no), CustomerCategoryV2::getNo, no)) <= 0;
    }

    @Override
    public Boolean checkUseOrgRemoval(OperationModel operationModel, CustomerCategoryCheckUseOrgRemovalReq params) {
        List<CustomerV2> customerV2s = customerV2Service.queryByManageOrgNoList(operationModel, params.getNo(), Collections.singletonList(params.getUseOrgNo()));
        return CollectionUtils.isEmpty(customerV2s);
    }

    @Override
    @Transactional
    public CustomerCategoryVO saveCustomerCategory(OperationModel operationModel, CustomerCategorySaveReq customerCategorySaveReq) {
        // 校验必填参数
        ValidatorUtils.checkEmptyThrowEx(operationModel.getEnterpriseNo(), "租户编号不能为空");
        ValidatorUtils.checkEmptyThrowEx(customerCategorySaveReq.getCategoryCode(), "分类编码不能为空");
        ValidatorUtils.checkEmptyThrowEx(customerCategorySaveReq.getCategoryName(), "分类名称不能为空");
        ValidatorUtils.checkEmptyThrowEx(customerCategorySaveReq.getManageOrgNo(), "管理组织不能为空");

        // 多组织时才需要校验档案管理权，目前集团和子租户都属于多组织架构，否则不需要校验；uap统一处理
        List<String> manageNoList = gradeControlService.listMgrOrgNos(operationModel.getEnterpriseNo(), BillNameConstant.BDC_CUSTOMER_CATEGORY_BILL);
        ValidatorUtils.checkEmptyThrowEx(manageNoList, "无档案管理权");
        ValidatorUtils.checkTrueThrowEx(!manageNoList.contains(customerCategorySaveReq.getManageOrgNo()), "无档案管理权");

        ValidatorUtils.checkTrueThrowEx(customerCategorySaveReq.getCategoryCode().length() > 50, "分类编码长度不能超过50");

        //校验参数合法性
        if (customerCategorySaveReq.getSortNum() != null) {
            ValidatorUtils.checkTrueThrowEx(customerCategorySaveReq.getSortNum() < 1, "排序不能小于1");
            ValidatorUtils.checkTrueThrowEx(customerCategorySaveReq.getSortNum() > 127, "排序不能大于127");
        }
        if (StringUtils.isNotEmpty(customerCategorySaveReq.getMnemonicCode())) {
            ValidatorUtils.checkTrueThrowEx(customerCategorySaveReq.getMnemonicCode().length() > 50, "助记码长度不能超过50");
        }
        if (StringUtils.isNotEmpty(customerCategorySaveReq.getRemark())) {
            ValidatorUtils.checkTrueThrowEx(customerCategorySaveReq.getRemark().length() > 300, "描述长度不能超过300");
        }

        // 停用态不允许添加使用组织
//        if (CommonStatusEnum.INVALID.getValue().toString().equals(customerCategorySaveReq.getStatus())) {
//            if (CollectionUtils.isNotEmpty(customerCategorySaveReq.getUseOrgNoList())) {
//                ValidatorUtils.checkTrueThrowEx(customerCategorySaveReq.getUseOrgNoList().size() > 1, "停用态客户分类不允许添加使用组织");
//                if (customerCategorySaveReq.getUseOrgNoList().size() == 1) {
//                    ValidatorUtils.checkTrueThrowEx(!customerCategorySaveReq.getUseOrgNoList().get(0).equals(customerCategorySaveReq.getManageOrgNo()), "停用态客户分类不允许添加使用组织");
//                }
//            }
//        }

        String parentNo = null;
        CustomerCategoryV2 parentCustomerCategoryV2 = null;

        if (ServiceConstant.TOP_PARENT_NO.equals(customerCategorySaveReq.getParentNo()) || StringUtils.isBlank(customerCategorySaveReq.getParentNo())) {
            parentNo = ServiceConstant.TOP_PARENT_NO;
        } else {
            parentNo = customerCategorySaveReq.getParentNo();

            parentCustomerCategoryV2 = customerCategoryV2DAO.selectOne(new LambdaQueryWrapper<CustomerCategoryV2>()
                    .eq(CustomerCategoryV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                    .eq(CustomerCategoryV2::getNo, parentNo)
                    .eq(CustomerCategoryV2::getDeleted, DeletedEnum.UN_DELETE.getValue())
            );
            ValidatorUtils.checkEmptyThrowEx(parentCustomerCategoryV2, "上级客户分类不存在");
            ValidatorUtils.checkTrueThrowEx(parentCustomerCategoryV2.getLevel() >= 5, "上级分类不能选择第五级的节点");
            ValidatorUtils.checkTrueThrowEx(CommonStatusEnum.INVALID.getValue().toString().equals(parentCustomerCategoryV2.getStatus()), "上级客户分类已停用");

            //上级节点是否被引用
            if (!ServiceConstant.TOP_PARENT_NO.equals(parentNo)) {
                boolean customerRef = customerV2Service.checkQuoteCategory(operationModel, parentNo);
                ValidatorUtils.checkTrueThrowEx(customerRef, "上级客户分类已被客户引用");
            }

            boolean hasAssign = customerCategoryBaseDAO.exists(new LambdaQueryWrapper<CustomerCategoryBase>()
                    .eq(CustomerCategoryBase::getEnterpriseNo, operationModel.getEnterpriseNo())
                    .eq(CustomerCategoryBase::getCategoryNo, parentNo)
                    .eq(CustomerCategoryBase::getManageOrgNo, customerCategorySaveReq.getManageOrgNo())
                    .ne(CustomerCategoryBase::getUseOrgNo, customerCategorySaveReq.getManageOrgNo())
                    .eq(CustomerCategoryBase::getDeleted, DeletedEnum.UN_DELETE.getValue())
            );
            ValidatorUtils.checkTrueThrowEx(hasAssign, "上级客户分类已被分配");
        }

        //唯一性校验
        ValidatorUtils.checkTrueThrowEx(!this.checkUniqueName(operationModel, null, customerCategorySaveReq.getCategoryName(), parentNo), "同一层级下已存在客户分类名称");
        ValidatorUtils.checkTrueThrowEx(!this.checkUniqueCode(operationModel, null, customerCategorySaveReq.getCategoryCode()), "企业已存在客户分类编码");

        // 保存客户分类信息
        CustomerCategoryV2 customerCategoryV2 = new CustomerCategoryV2();
        BeanUtils.copyProperties(customerCategorySaveReq, customerCategoryV2);
        customerCategoryV2.setEnterpriseNo(operationModel.getEnterpriseNo());

        String customerCategoryNo = numberCenterService.createNumber(NumberCenterConstant.CUSTOMER_NO_CATEGORY_KEY_V2);
        customerCategoryV2.setNo(customerCategoryNo);
        customerCategoryV2.setCategoryCode(customerCategorySaveReq.getCategoryCode());
        customerCategoryV2.setParentNo(parentNo);
        customerCategoryV2.setDeleted(DeletedEnum.UN_DELETE.getValue());

        if (ServiceConstant.TOP_PARENT_NO.equals(parentNo)) {
            customerCategoryV2.setLevel(1);
            customerCategoryV2.setTopCategoryNo(customerCategoryNo);
            customerCategoryV2.setPath(customerCategoryNo);
        } else {
            customerCategoryV2.setLevel(Optional.ofNullable(parentCustomerCategoryV2.getLevel()).orElseGet(() -> 0) + 1);
            customerCategoryV2.setTopCategoryNo(parentCustomerCategoryV2.getTopCategoryNo());
            customerCategoryV2.setPath(parentCustomerCategoryV2.getPath() + "/" + customerCategoryNo);
        }

        CommonUtil.fillCreatInfo(operationModel, customerCategoryV2);
        CommonUtil.fillOperateInfo(operationModel, customerCategoryV2);
        customerCategoryV2DAO.insert(customerCategoryV2);

        // 移除上级节点的分配记录（仅管理组织一条记录）
        List<CustomerCategoryBase> customerCategoryBases = customerCategoryBaseDAO.selectList(Wrappers.<CustomerCategoryBase>lambdaQuery()
                .eq(CustomerCategoryBase::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CustomerCategoryBase::getManageOrgNo, customerCategorySaveReq.getManageOrgNo())
                .eq(CustomerCategoryBase::getDeleted, DeletedEnum.UN_DELETE.getValue())
                .eq(CustomerCategoryBase::getCategoryNo, parentNo));
        for (CustomerCategoryBase customerCategoryBase : customerCategoryBases) {
            customerCategoryBase.setDeleted(DeletedEnum.DELETED.getValue());
        }
        customerCategoryBaseDAO.updateByIdBatch(customerCategoryBases);

        CustomerCategoryBase customerCategoryBase = new CustomerCategoryBase();
        customerCategoryBase.setEnterpriseNo(operationModel.getEnterpriseNo());
        customerCategoryBase.setCategoryNo(customerCategoryNo);
        customerCategoryBase.setCategoryCode(customerCategorySaveReq.getCategoryCode());
        customerCategoryBase.setManageOrgNo(customerCategorySaveReq.getManageOrgNo());
        customerCategoryBase.setUseOrgNo(customerCategorySaveReq.getManageOrgNo());
        customerCategoryBase.setDeleted(DeletedEnum.UN_DELETE.getValue());
        CommonUtil.fillCreatInfo(operationModel, customerCategoryBase);
        CommonUtil.fillOperateInfo(operationModel, customerCategoryBase);
        customerCategoryBaseDAO.insert(customerCategoryBase);

        CustomerCategoryVO customerCategoryVO = new CustomerCategoryVO();
        BeanUtils.copyProperties(customerCategoryV2, customerCategoryVO);

        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                try {
                    businessLogService.saveLog(DLogLevel.INFO.name(), ViewNameConstant.BDC_CUSTOMER_CATEGORY_VIEW, customerCategoryNo, "新增客户分类", "新增客户分类", JSON.toJSONString(customerCategoryV2), "");
                } catch (Exception e) {
                    log.error("新增客户分类日志保存失败", e);
                }
            }
        });

        return customerCategoryVO;
    }

    private void getDescendants(OperationModel operationModel, String entterpriseNo, String parentNo, List<CustomerCategoryV2> customerCategoryV2List, int depth) {
        if (depth >= MAX_RECURSION_DEPTH) {
            log.error("递归深度超过{}层，停止递归", MAX_RECURSION_DEPTH);
            throw new BusinessException("分类层级异常");
        }

        List<CustomerCategoryV2> customerCategoryV2s = customerCategoryV2DAO.selectList(Wrappers.<CustomerCategoryV2>lambdaQuery()
                .eq(CustomerCategoryV2::getEnterpriseNo, entterpriseNo)
                .eq(CustomerCategoryV2::getParentNo, parentNo)
                .eq(CustomerCategoryV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));

        if (CollectionUtils.isNotEmpty(customerCategoryV2s)) {
            customerCategoryV2List.addAll(customerCategoryV2s);

            for (CustomerCategoryV2 customerCategoryV2 : customerCategoryV2s) {
                getDescendants(operationModel, entterpriseNo, customerCategoryV2.getNo(), customerCategoryV2List, depth + 1);
            }
        }
    }

    @Override
    public CustomerCategoryVO getCustomerCategory(OperationModel operationModel, CustomerCategoryGetReq customerCategoryGetReq) {
        if (StringUtils.isBlank(customerCategoryGetReq.getUseOrgNo())) {
            ValidatorUtils.checkEmptyThrowEx(operationModel.getOrgCode(), "使用组织编号不能为空");
            customerCategoryGetReq.setUseOrgNo(operationModel.getOrgNo());
        }
        // 校验必填参数
        ValidatorUtils.checkEmptyThrowEx(operationModel.getEnterpriseNo(), "租户编号不能为空");
        ValidatorUtils.checkEmptyThrowEx(customerCategoryGetReq.getNo(), "客户分类编号不能为空");

        CustomerCategoryV2 customerCategoryV2 = customerCategoryV2DAO.getByNo(operationModel.getEnterpriseNo(), customerCategoryGetReq.getNo());

        CustomerCategoryVO customerCategoryVO = new CustomerCategoryVO();
        BeanUtils.copyProperties(customerCategoryV2, customerCategoryVO);

        // 填充上级分类名称
        if (ServiceConstant.TOP_PARENT_NO.equals(customerCategoryV2.getParentNo())) {
            customerCategoryVO.setParentNo("");
            customerCategoryVO.setParentName("");
        } else {
            CustomerCategoryV2 parentCustomerCategoryV2 = customerCategoryV2DAO.getByNo(operationModel.getEnterpriseNo(), customerCategoryV2.getParentNo());
            if (null != parentCustomerCategoryV2) {
                customerCategoryVO.setParentName(parentCustomerCategoryV2.getCategoryName());
            }
        }

        Set<String> orgNos = new HashSet<>();
        orgNos.add(customerCategoryV2.getManageOrgNo());

        List<CustomerCategoryBase> customerCategoryBases = customerCategoryBaseDAO.selectList(Wrappers.<CustomerCategoryBase>lambdaQuery()
                .eq(CustomerCategoryBase::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CustomerCategoryBase::getManageOrgNo, customerCategoryV2.getManageOrgNo())
                .eq(CustomerCategoryBase::getCategoryNo, customerCategoryV2.getNo())
                .eq(CustomerCategoryBase::getDeleted, DeletedEnum.UN_DELETE.getValue()));

        if (CollectionUtils.isNotEmpty(customerCategoryBases)) {
            orgNos.addAll(customerCategoryBases.stream().map(CustomerCategoryBase::getUseOrgNo).collect(Collectors.toSet()));
        }

        List<OrganizationVo> organizationVoList = organizationService.findListNoAuth(operationModel.getEnterpriseNo(), new ArrayList<>(orgNos));
        final Map<String, OrganizationVo> orgNo2OrganizationVo = organizationVoList.stream().collect(Collectors.toMap(OrganizationVo::getOrgNo, Function.identity()));

        if (orgNo2OrganizationVo.containsKey(customerCategoryV2.getManageOrgNo())) {
            OrganizationVo organizationVo = orgNo2OrganizationVo.get(customerCategoryV2.getManageOrgNo());
            customerCategoryVO.setManageOrgName(organizationVo.getOrgName());
            customerCategoryVO.setManageOrgCode(organizationVo.getOrgCode());
        }

        if (CollectionUtils.isNotEmpty(customerCategoryBases)) {
            List<String> names = new ArrayList<>();
            List<CustomerCategoryUseOrgVO> useOrgVOS = new ArrayList<>();
            for (CustomerCategoryBase customerCategoryBase : customerCategoryBases) {
                if (orgNo2OrganizationVo.containsKey(customerCategoryBase.getUseOrgNo())) {
                    OrganizationVo organizationVo = orgNo2OrganizationVo.get(customerCategoryBase.getUseOrgNo());

                    CustomerCategoryUseOrgVO customerCategoryUseOrgVO = new CustomerCategoryUseOrgVO();
                    customerCategoryUseOrgVO.setId(customerCategoryBase.getId());
                    customerCategoryUseOrgVO.setUseOrgNo(customerCategoryBase.getUseOrgNo());
                    customerCategoryUseOrgVO.setUseOrgCode(organizationVo.getOrgCode());
                    customerCategoryUseOrgVO.setUseOrgName(organizationVo.getOrgName());

                    useOrgVOS.add(customerCategoryUseOrgVO);

                    names.add(organizationVo.getOrgName());
                }
            }

            customerCategoryVO.setUseOrgNames(String.join("，", names));

            customerCategoryVO.setUseOrgList(useOrgVOS);
        }

        // 填充请求的使用组织
        customerCategoryVO.setInputUseOrgNo(customerCategoryGetReq.getUseOrgNo());
        customerCategoryVO.setInputUseOrgName(customerCategoryGetReq.getUseOrgName());

        // 填充子孙节点标记
        boolean exists = customerCategoryV2DAO.exists(Wrappers.<CustomerCategoryV2>lambdaQuery()
                .eq(CustomerCategoryV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CustomerCategoryV2::getParentNo, customerCategoryV2.getNo())
                .eq(CustomerCategoryV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
        customerCategoryVO.setDescendantsFlag(exists ? CommonIfEnum.YES.getValue() : CommonIfEnum.NO.getValue());

        return customerCategoryVO;
    }

    private String getPath(Map<String, CustomerCategoryV2> finalNo2CategoryMap, String currentNo) {
        Deque<String> pathStack = new ArrayDeque<>();
        pathStack.push(currentNo);

        String tmpNo = currentNo;
        int loop = 0;
        while (true) {
            if (loop > MAX_RECURSION_DEPTH) {
                throw new BusinessException("供应商分类树结构异常");
            }

            if (!finalNo2CategoryMap.containsKey(tmpNo)) {
                break;
            }

            CustomerCategoryV2 customerCategoryV2 = finalNo2CategoryMap.get(tmpNo);
            if (null == customerCategoryV2.getParentNo() || ServiceConstant.TOP_PARENT_NO.equals(customerCategoryV2.getParentNo())) {
                break;
            }
            pathStack.push(customerCategoryV2.getParentNo());

            tmpNo = customerCategoryV2.getParentNo();

            loop++;
        }

        StringBuilder path = new StringBuilder();

        int size = pathStack.size();
        for (int i = 0; i < size; i++) {
            try {
                path.append(pathStack.pop()).append("/");
            } catch (NoSuchElementException e) {
                break;
            }
        }

        return path.deleteCharAt(path.length() - 1).toString();
    }

    @Override
    @Transactional
    public CustomerCategoryVO updateCustomerCategory(OperationModel operationModel, CustomerCategoryUpdateReq customerCategoryUpdateReq) {
        // 校验必填参数
        ValidatorUtils.checkEmptyThrowEx(operationModel.getEnterpriseNo(), "租户编号不能为空");
        ValidatorUtils.checkEmptyThrowEx(customerCategoryUpdateReq.getManageOrgNo(), "管理组织不能为空");
        ValidatorUtils.checkEmptyThrowEx(customerCategoryUpdateReq.getNo(), "客户分类编号不能为空");

        //校验参数合法性
        if (customerCategoryUpdateReq.getSortNum() != null) {
            ValidatorUtils.checkTrueThrowEx(customerCategoryUpdateReq.getSortNum() < 1, "排序不能小于1");
            ValidatorUtils.checkTrueThrowEx(customerCategoryUpdateReq.getSortNum() > 127, "排序不能大于127");
        }
        if (StringUtils.isNotEmpty(customerCategoryUpdateReq.getMnemonicCode())) {
            ValidatorUtils.checkTrueThrowEx(customerCategoryUpdateReq.getMnemonicCode().length() > 50, "助记码长度不能超过50");
        }
        if (StringUtils.isNotEmpty(customerCategoryUpdateReq.getRemark())) {
            ValidatorUtils.checkTrueThrowEx(customerCategoryUpdateReq.getRemark().length() > 300, "描述长度不能超过300");
        }

        CustomerCategoryV2 dbCustomerCategoryV2 = customerCategoryV2DAO.selectOne(new LambdaQueryWrapper<CustomerCategoryV2>()
                .eq(CustomerCategoryV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CustomerCategoryV2::getManageOrgNo, customerCategoryUpdateReq.getManageOrgNo())
                .eq(CustomerCategoryV2::getNo, customerCategoryUpdateReq.getNo())
                .eq(CustomerCategoryV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
        ValidatorUtils.checkEmptyThrowEx(dbCustomerCategoryV2, "客户分类不存在");

        String parentNo = null;


        if (ServiceConstant.TOP_PARENT_NO.equals(customerCategoryUpdateReq.getParentNo()) || StringUtils.isBlank(customerCategoryUpdateReq.getParentNo())) {
            parentNo = ServiceConstant.TOP_PARENT_NO;
        } else {
            parentNo = customerCategoryUpdateReq.getParentNo();
        }

        CustomerCategoryV2 parentCustomerCategoryV2 = null;
        Map<Integer, List<CustomerCategoryV2>> lv2Node = null;

        // 提前构造树，列表存放了所有的分类节点记录
        List<CustomerCategoryV2> finalCustomerCategoryV2List = customerCategoryV2DAO.selectList(new LambdaQueryWrapper<CustomerCategoryV2>()
                .eq(CustomerCategoryV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CustomerCategoryV2::getManageOrgNo, customerCategoryUpdateReq.getManageOrgNo())
                .eq(CustomerCategoryV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
        for (CustomerCategoryV2 customerCategoryV2 : finalCustomerCategoryV2List) {
            if (customerCategoryV2.getNo().equals(customerCategoryUpdateReq.getNo())) {
                customerCategoryV2.setParentNo(parentNo);
                break;
            }
        }
        Map<String, CustomerCategoryV2> finalNo2CategoryMap = finalCustomerCategoryV2List.stream().collect(Collectors.toMap(CustomerCategoryV2::getNo, customerCategoryV2 -> customerCategoryV2));

        if (!ServiceConstant.TOP_PARENT_NO.equals(parentNo)) {
            // 判断是否成环
            ValidatorUtils.checkTrueThrowEx(hasCircularDependency(finalCustomerCategoryV2List), "客户分类存在循环依赖");

            parentCustomerCategoryV2 = customerCategoryV2DAO.selectOne(new LambdaQueryWrapper<CustomerCategoryV2>()
                    .eq(CustomerCategoryV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                    .eq(CustomerCategoryV2::getNo, parentNo)
                    .eq(CustomerCategoryV2::getDeleted, DeletedEnum.UN_DELETE.getValue())
            );
            ValidatorUtils.checkEmptyThrowEx(parentCustomerCategoryV2, "上级客户分类不存在");
            ValidatorUtils.checkTrueThrowEx(parentCustomerCategoryV2.getLevel() >= 5, "上级分类不能选择第五级的节点");

            //获取当前节点的所有子孙节点
            List<CustomerCategoryV2> allDescendants = new ArrayList<>();
            recursiveGetDescendantCustomerCategoryV2s(operationModel.getEnterpriseNo(), customerCategoryUpdateReq.getManageOrgNo(), Collections.singletonList(dbCustomerCategoryV2.getNo()), allDescendants, 0);
            if (CollectionUtils.isNotEmpty(allDescendants)) {
                lv2Node = allDescendants.stream().collect(Collectors.groupingBy(CustomerCategoryV2::getLevel));

                ValidatorUtils.checkTrueThrowEx((parentCustomerCategoryV2.getLevel() + 1 + lv2Node.size()) > 5, "分类不能超过五级");
            }

            boolean isInvalid = CommonStatusEnum.INVALID.getValue().toString().equals(parentCustomerCategoryV2.getStatus())
                    && CommonStatusEnum.EFFECTIVE.getValue().toString().equals(customerCategoryUpdateReq.getStatus());
            ValidatorUtils.checkTrueThrowEx(isInvalid, "上级客户分类已停用，不允许启用");

            //上级节点是否被引用
            boolean customerRef = customerV2Service.checkQuoteCategory(operationModel, parentNo);
            ValidatorUtils.checkTrueThrowEx(customerRef, "上级客户分类已被客户引用");

            // 上级节点是否被分配
            boolean hasAssign = customerCategoryBaseDAO.exists(new LambdaQueryWrapper<CustomerCategoryBase>()
                    .eq(CustomerCategoryBase::getEnterpriseNo, operationModel.getEnterpriseNo())
                    .eq(CustomerCategoryBase::getCategoryNo, parentNo)
                    .eq(CustomerCategoryBase::getManageOrgNo, customerCategoryUpdateReq.getManageOrgNo())
                    .ne(CustomerCategoryBase::getUseOrgNo, customerCategoryUpdateReq.getManageOrgNo())
                    .eq(CustomerCategoryBase::getDeleted, DeletedEnum.UN_DELETE.getValue()));
            ValidatorUtils.checkTrueThrowEx(hasAssign, "上级客户分类已被分配");
        }

        //唯一性校验
        ValidatorUtils.checkTrueThrowEx(!this.checkUniqueName(operationModel, customerCategoryUpdateReq.getNo(), customerCategoryUpdateReq.getCategoryName(), parentNo), "同一层级下已存在客户分类名称");
        ValidatorUtils.checkTrueThrowEx(!this.checkUniqueCode(operationModel, customerCategoryUpdateReq.getNo(), customerCategoryUpdateReq.getCategoryCode()), "企业已存在客户分类编码");


        String enterpriseNo = operationModel.getEnterpriseNo();
        String customerCategoryNo = customerCategoryUpdateReq.getNo();

        // 更新客户分类信息
        CustomerCategoryV2 newCustomerCategoryV2 = new CustomerCategoryV2();
        newCustomerCategoryV2.setParentNo(parentNo);
        newCustomerCategoryV2.setMnemonicCode(customerCategoryUpdateReq.getMnemonicCode());
        newCustomerCategoryV2.setCategoryCode(customerCategoryUpdateReq.getCategoryCode());
        newCustomerCategoryV2.setCategoryName(customerCategoryUpdateReq.getCategoryName());
        newCustomerCategoryV2.setRemark(customerCategoryUpdateReq.getRemark());
        newCustomerCategoryV2.setStatus(customerCategoryUpdateReq.getStatus());
        newCustomerCategoryV2.setSortNum(customerCategoryUpdateReq.getSortNum());

        if (ServiceConstant.TOP_PARENT_NO.equals(parentNo)) {
            newCustomerCategoryV2.setLevel(1);
            newCustomerCategoryV2.setTopCategoryNo(customerCategoryNo);
            newCustomerCategoryV2.setPath(getPath(finalNo2CategoryMap, customerCategoryNo));
        } else {
            newCustomerCategoryV2.setLevel(parentCustomerCategoryV2.getLevel() + 1);
            newCustomerCategoryV2.setTopCategoryNo(parentCustomerCategoryV2.getTopCategoryNo());
            newCustomerCategoryV2.setPath(getPath(finalNo2CategoryMap, customerCategoryNo));
        }

        CommonUtil.fillOperateInfo(operationModel, newCustomerCategoryV2);

        LambdaUpdateWrapper<CustomerCategoryV2> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.eq(CustomerCategoryV2::getEnterpriseNo, enterpriseNo)
                .eq(CustomerCategoryV2::getManageOrgNo, customerCategoryUpdateReq.getManageOrgNo())
                .eq(CustomerCategoryV2::getNo, customerCategoryNo);
        customerCategoryV2DAO.update(newCustomerCategoryV2, updateWrapper);

        //更新所有子孙节点的level
        if (null != lv2Node) {
            List<Integer> lvs = new ArrayList<>(lv2Node.keySet());
            lvs.sort(Comparator.comparingInt(o -> o));
            Integer level = newCustomerCategoryV2.getLevel();

            int nextlv = level + 1;
            for (Integer lv : lvs) {
                List<CustomerCategoryV2> customerCategoryV2s = lv2Node.get(lv);
                for (CustomerCategoryV2 customerCategoryV2 : customerCategoryV2s) {
                    CustomerCategoryV2 updateCustomerCategoryV2 = new CustomerCategoryV2();
                    updateCustomerCategoryV2.setLevel(nextlv);
                    updateCustomerCategoryV2.setTopCategoryNo(newCustomerCategoryV2.getTopCategoryNo());
                    updateCustomerCategoryV2.setPath(getPath(finalNo2CategoryMap, customerCategoryV2.getNo()));
                    customerCategoryV2DAO.update(updateCustomerCategoryV2, Wrappers.<CustomerCategoryV2>lambdaUpdate()
                            .eq(CustomerCategoryV2::getEnterpriseNo, enterpriseNo)
                            .eq(CustomerCategoryV2::getManageOrgNo, customerCategoryUpdateReq.getManageOrgNo())
                            .eq(CustomerCategoryV2::getNo, customerCategoryV2.getNo()));
                }

                nextlv++;
            }
        }

        // 移除非叶子节点的分派记录
        // 补全叶子节点的分派记录
        List<CustomerCategoryBase> customerCategoryBases = customerCategoryBaseDAO.selectList(Wrappers.<CustomerCategoryBase>lambdaUpdate()
                .eq(CustomerCategoryBase::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CustomerCategoryBase::getManageOrgNo, customerCategoryUpdateReq.getManageOrgNo())
                .eq(CustomerCategoryBase::getDeleted, DeletedEnum.UN_DELETE.getValue()));
        Map<String, List<CustomerCategoryBase>> no2BasesMap = customerCategoryBases.stream().collect(Collectors.groupingBy(CustomerCategoryBase::getCategoryNo));

        Map<String, CustomerCategoryV2> leafNo2Category = findAllLeavis(finalCustomerCategoryV2List).stream().collect(Collectors.toMap(CustomerCategoryV2::getNo, Function.identity()));

        List<String> toDeleteBaseCategoryNoList = new ArrayList<>();
        List<CustomerCategoryV2> toAddCategoryList = new ArrayList<>();

        no2BasesMap.forEach((no, bases) -> {
            if (!leafNo2Category.containsKey(no)) {
                toDeleteBaseCategoryNoList.add(no);
            }
        });

        if (CollectionUtils.isNotEmpty(toDeleteBaseCategoryNoList)) {
            CustomerCategoryBase updateCustomerCategoryBase = new CustomerCategoryBase();
            updateCustomerCategoryBase.setDeleted(DeletedEnum.DELETED.getValue());
            customerCategoryBaseDAO.update(updateCustomerCategoryBase, Wrappers.<CustomerCategoryBase>lambdaUpdate()
                    .eq(CustomerCategoryBase::getEnterpriseNo, operationModel.getEnterpriseNo())
                    .eq(CustomerCategoryBase::getManageOrgNo, customerCategoryUpdateReq.getManageOrgNo())
                    .eq(CustomerCategoryBase::getDeleted, DeletedEnum.UN_DELETE.getValue())
                    .in(CustomerCategoryBase::getCategoryNo, toDeleteBaseCategoryNoList));
        }

        leafNo2Category.forEach((no, category) -> {
            if (!no2BasesMap.containsKey(no)) {
                toAddCategoryList.add(category);
            }
        });

        for (CustomerCategoryV2 customerCategoryV2 : toAddCategoryList) {
            CustomerCategoryBase insertCustomerCategoryBase = new CustomerCategoryBase();
            insertCustomerCategoryBase.setEnterpriseNo(operationModel.getEnterpriseNo());
            insertCustomerCategoryBase.setCategoryNo(customerCategoryV2.getNo());
            insertCustomerCategoryBase.setCategoryCode(customerCategoryV2.getCategoryCode());
            insertCustomerCategoryBase.setManageOrgNo(customerCategoryV2.getManageOrgNo());
            insertCustomerCategoryBase.setUseOrgNo(customerCategoryV2.getManageOrgNo());
            insertCustomerCategoryBase.setDeleted(DeletedEnum.UN_DELETE.getValue());
            CommonUtil.fillCreatInfo(operationModel, insertCustomerCategoryBase);
            customerCategoryBaseDAO.insert(insertCustomerCategoryBase);
        }

        log.warn("toDeleteBaseCategoryNoList={}", toDeleteBaseCategoryNoList);
        log.warn("toAddCategoryList={}", toAddCategoryList);

        // 如果移动的是叶子节点，需要删除被ui删掉的使用组织
        if (leafNo2Category.containsKey(customerCategoryUpdateReq.getNo())) {
            Set<String> orgNoSet = null;
            if (CollectionUtils.isNotEmpty(customerCategoryUpdateReq.getUseOrgList())) {
                orgNoSet = customerCategoryUpdateReq.getUseOrgList().stream().map(CustomerCategoryUseOrgReq::getUseOrgNo).collect(Collectors.toSet());
            } else {
                orgNoSet = new HashSet<>();
            }

            log.warn("orgNoSet={}", orgNoSet);

            List<CustomerCategoryBase> dbCustomerCategoryBases = customerCategoryBaseDAO.selectList(Wrappers.<CustomerCategoryBase>lambdaQuery()
                    .eq(CustomerCategoryBase::getEnterpriseNo, operationModel.getEnterpriseNo())
                    .eq(CustomerCategoryBase::getManageOrgNo, customerCategoryUpdateReq.getManageOrgNo())
                    .eq(CustomerCategoryBase::getCategoryNo, customerCategoryUpdateReq.getNo())
                    .eq(CustomerCategoryBase::getDeleted, DeletedEnum.UN_DELETE.getValue()));

            log.warn("dbCustomerCategoryBaseV2s={}", dbCustomerCategoryBases);

            List<String> deleteOrgNoList = new ArrayList<>();
            for (CustomerCategoryBase dbCustomerCategoryBase : dbCustomerCategoryBases) {
                if (customerCategoryUpdateReq.getManageOrgNo().equals(dbCustomerCategoryBase.getUseOrgNo())) {
                    continue;
                }
                if (!orgNoSet.contains(dbCustomerCategoryBase.getUseOrgNo())) {
                    deleteOrgNoList.add(dbCustomerCategoryBase.getUseOrgNo());
                }
            }

            log.warn("deleteOrgNoList={}", deleteOrgNoList);

            if (CollectionUtils.isNotEmpty(deleteOrgNoList)) {
                // 分派列表中如果移除已经使用过的组织，需拒绝
                // 管理组织G1创建了客户分类F1，并把客户分类F1分派给使用组织U1，当U1创建了客户档案并引用了F1，此时管理组织G1去修改客户分类F1的使用范围时，不能移除U1。
                List<CustomerCategoryBase> myAssignList = customerCategoryBaseDAO.selectList(Wrappers.<CustomerCategoryBase>lambdaQuery()
                        .eq(CustomerCategoryBase::getEnterpriseNo, operationModel.getEnterpriseNo())
                        .eq(CustomerCategoryBase::getManageOrgNo, customerCategoryUpdateReq.getManageOrgNo())
                        .ne(CustomerCategoryBase::getUseOrgNo, customerCategoryUpdateReq.getManageOrgNo())
                        .eq(CustomerCategoryBase::getDeleted, DeletedEnum.UN_DELETE.getValue())
                        .eq(CustomerCategoryBase::getCategoryNo, customerCategoryUpdateReq.getNo()));
                if (CollectionUtils.isNotEmpty(myAssignList)) {
                    List<CustomerV2> customerV2s = customerV2Service.queryByManageOrgNoList(operationModel, customerCategoryUpdateReq.getNo(), myAssignList.stream().map(CustomerCategoryBase::getUseOrgNo).collect(Collectors.toList()));
                    if (CollectionUtils.isNotEmpty(customerV2s)) {
                        Map<String, List<CustomerV2>> orgNo2CustomerV2Map = customerV2s.stream().collect(Collectors.groupingBy(CustomerV2::getManageOrgNo));
                        List<String> orgRefedList = new ArrayList<>();
                        for (String useOrgNo : deleteOrgNoList) {
                            if (useOrgNo.equals(customerCategoryUpdateReq.getManageOrgNo())) {
                                continue;
                            }

                            if (orgNo2CustomerV2Map.containsKey(useOrgNo)) {
                                List<CustomerV2> customerV2List = orgNo2CustomerV2Map.get(useOrgNo);
                                if (CollectionUtils.isNotEmpty(customerV2List)) {
//                                    orgNameList.addAll(customerV2List.stream().map(CustomerV2::getCustomerName).collect(Collectors.toSet()));

                                    orgRefedList.add(useOrgNo);
                                }
                            }
                        }

                        if (CollectionUtils.isNotEmpty(orgRefedList)) {
                            List<OrganizationVo> listNoAuth = organizationService.findListNoAuth(operationModel.getEnterpriseNo(), orgRefedList);
                            if (CollectionUtils.isNotEmpty(listNoAuth)) {
                                throw new BusinessException("使用组织【" + listNoAuth.stream().map(OrganizationVo::getOrgName).collect(Collectors.joining(",")) + "】已引用当前分类，不允许删除。");
                            }
                        }
                    }
                }

                TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                    @Override
                    public void afterCommit() {
                        try {
                            log.warn("executeCancelAssignDocReq={},{},{},{}", operationModel.getEnterpriseNo(), ViewNameConstant.BDC_CUSTOMER_CATEGORY_VIEW, customerCategoryUpdateReq.getNo(), deleteOrgNoList);

                            Map<String, GradeCancelAssignRes> executeCancelAssignDoc = gradeControlService.executeCancelAssignDoc(operationModel.getEnterpriseNo(), BillNameConstant.BDC_CUSTOMER_CATEGORY_BILL, customerCategoryUpdateReq.getNo(), deleteOrgNoList);

                            log.warn("executeCancelAssignDocResp={}", executeCancelAssignDoc);
                        } catch (Exception e) {
                            log.error("取消分派异常", e);
                        }
                    }
                });
            }
        }

        Map<String, Object> businessValue = new HashMap<>();
        businessValue.put("newCustomerCategory", newCustomerCategoryV2);
        businessValue.put("useOrgNoList", customerCategoryUpdateReq.getUseOrgList());

        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                try {
                    businessLogService.saveLog(DLogLevel.INFO.name(), ViewNameConstant.BDC_CUSTOMER_CATEGORY_VIEW, customerCategoryNo, "更新客户分类", "更新客户分类", JSON.toJSONString(businessValue), "");
                } catch (Exception e) {
                    log.error("编辑客户分类日志保存失败", e);
                }
            }
        });

        return packNewCustomerCategoryVO(dbCustomerCategoryV2, newCustomerCategoryV2);
    }

    //根据CustomerCategoryV2的no和parentNo查询出所有的叶子并返回
    private List<CustomerCategoryV2> findAllLeavis(List<CustomerCategoryV2> customerCategoryList) {
        if (CollectionUtils.isEmpty(customerCategoryList)) {
            return Collections.emptyList();
        }

        Set<String> parentNoSet = customerCategoryList.stream().map(CustomerCategoryV2::getParentNo)
                .collect(Collectors.toSet());

        // 存储所有叶子节点
        List<CustomerCategoryV2> leaves = new ArrayList<>();

        // 遍历所有节点,找出叶子节点(没有子节点的节点)
        for (CustomerCategoryV2 category : customerCategoryList) {
            String categoryNo = category.getNo();
            if (!parentNoSet.contains(categoryNo)) {
                leaves.add(category);
            }
        }

        return leaves;
    }

    /**
     * 判断客户分类列表是否存在环形依赖
     *
     * @param customerCategoryList 客户分类列表
     * @return true表示存在环, false表示不存在环
     */
    private boolean hasCircularDependency(List<CustomerCategoryV2> customerCategoryList) {
        if (CollectionUtils.isEmpty(customerCategoryList)) {
            return false;
        }

        // 构建分类编号到分类对象的映射
        Map<String, CustomerCategoryV2> categoryMap = customerCategoryList.stream()
                .collect(Collectors.toMap(CustomerCategoryV2::getNo, Function.identity()));

        // 记录已访问的节点
        Set<String> visited = new HashSet<>();
        // 记录当前路径上的节点
        Set<String> path = new HashSet<>();

        // 对每个节点进行DFS遍历
        for (CustomerCategoryV2 category : customerCategoryList) {
            if (dfs(category.getNo(), categoryMap, visited, path)) {
                return true;
            }
        }
        return false;
    }

    /**
     * DFS遍历检查是否存在环
     */
    private boolean dfs(String categoryNo, Map<String, CustomerCategoryV2> categoryMap,
                        Set<String> visited, Set<String> path) {
        // 当前节点已在路径中,说明成环
        if (path.contains(categoryNo)) {
            return true;
        }

        // 已访问过的节点不需要重复访问
        if (visited.contains(categoryNo)) {
            return false;
        }

        CustomerCategoryV2 category = categoryMap.get(categoryNo);
        if (category == null) {
            return false;
        }

        visited.add(categoryNo);
        path.add(categoryNo);

        // 递归检查父节点
        if (StringUtils.isNotBlank(category.getParentNo())) {
            if (dfs(category.getParentNo(), categoryMap, visited, path)) {
                return true;
            }
        }

        path.remove(categoryNo);
        return false;
    }

    private CustomerCategoryVO packNewCustomerCategoryVO(CustomerCategoryV2 dbCustomerCategoryV2, CustomerCategoryV2 newCustomerCategoryV2) {
        dbCustomerCategoryV2.setParentNo(newCustomerCategoryV2.getParentNo());
        dbCustomerCategoryV2.setMnemonicCode(newCustomerCategoryV2.getMnemonicCode());
        dbCustomerCategoryV2.setCategoryCode(newCustomerCategoryV2.getCategoryCode());
        dbCustomerCategoryV2.setCategoryName(newCustomerCategoryV2.getCategoryName());
        dbCustomerCategoryV2.setLevel(newCustomerCategoryV2.getLevel());
        dbCustomerCategoryV2.setTopCategoryNo(newCustomerCategoryV2.getTopCategoryNo());
        dbCustomerCategoryV2.setPath(newCustomerCategoryV2.getPath());
        dbCustomerCategoryV2.setRemark(newCustomerCategoryV2.getRemark());
        dbCustomerCategoryV2.setStatus(newCustomerCategoryV2.getStatus());
        dbCustomerCategoryV2.setSortNum(newCustomerCategoryV2.getSortNum());
        dbCustomerCategoryV2.setOperateNo(newCustomerCategoryV2.getOperateNo());
        dbCustomerCategoryV2.setOperateName(newCustomerCategoryV2.getOperateName());
        dbCustomerCategoryV2.setOperateTime(newCustomerCategoryV2.getOperateTime());

        CustomerCategoryVO customerCategoryVO = new CustomerCategoryVO();
        BeanUtils.copyProperties(dbCustomerCategoryV2, customerCategoryVO);

        return customerCategoryVO;
    }

    @Override
    @Transactional
    public Boolean deleteCustomerCategory(OperationModel operationModel, CustomerCategoryDeleteReq customerCategoryDeleteReq) {
        // 校验必填参数
        ValidatorUtils.checkEmptyThrowEx(operationModel.getEnterpriseNo(), "租户编号不能为空");
        ValidatorUtils.checkEmptyThrowEx(customerCategoryDeleteReq.getManageOrgNo(), "管理组织不能为空");
        ValidatorUtils.checkEmptyThrowEx(customerCategoryDeleteReq.getNo(), "客户分类编号不能为空");

        CustomerCategoryV2 dbCustomerCategoryV2 = customerCategoryV2DAO.selectOne(new LambdaQueryWrapper<CustomerCategoryV2>()
                .eq(CustomerCategoryV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CustomerCategoryV2::getManageOrgNo, customerCategoryDeleteReq.getManageOrgNo())
                .eq(CustomerCategoryV2::getNo, customerCategoryDeleteReq.getNo())
                .eq(CustomerCategoryV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
        ValidatorUtils.checkEmptyThrowEx(dbCustomerCategoryV2, "客户分类不存在");

        boolean hasDescendants = customerCategoryV2DAO.exists(new LambdaQueryWrapper<CustomerCategoryV2>()
                .eq(CustomerCategoryV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CustomerCategoryV2::getManageOrgNo, customerCategoryDeleteReq.getManageOrgNo())
                .eq(CustomerCategoryV2::getParentNo, customerCategoryDeleteReq.getNo())
                .eq(CustomerCategoryV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
        ValidatorUtils.checkTrueThrowEx(hasDescendants, "存在子客户分类，不能删除");

//        boolean hasAssign = customerCategoryBaseV2DAO.exists(new LambdaQueryWrapper<CustomerCategoryBaseV2>()
//                .eq(CustomerCategoryBaseV2::getEnterpriseNo, operationModel.getEnterpriseNo())
//                .eq(CustomerCategoryBaseV2::getCategoryNo, customerCategoryDeleteReq.getNo())
//                .eq(CustomerCategoryBaseV2::getManageOrgNo, customerCategoryDeleteReq.getManageOrgNo())
//                .ne(CustomerCategoryBaseV2::getUseOrgNo, customerCategoryDeleteReq.getManageOrgNo())
//                .eq(CustomerCategoryBaseV2::getDeleted, DeletedEnum.UN_DELETE.getValue())
//        );
//        ValidatorUtils.checkTrueThrowEx(hasAssign, "客户分类已被分配");

        //本节点是否被引用
        boolean customerRef = customerV2Service.checkQuoteCategory(operationModel, customerCategoryDeleteReq.getNo());
        ValidatorUtils.checkTrueThrowEx(customerRef, "客户分类已被客户引用");

        String enterpriseNo = operationModel.getEnterpriseNo();
        String customerCategoryNo = customerCategoryDeleteReq.getNo();

        // 删除客户分类信息
        CustomerCategoryV2 newCustomerCategoryV2 = new CustomerCategoryV2();
//        newCustomerCategoryV2.setEnterpriseNo(enterpriseNo);
        newCustomerCategoryV2.setNo(customerCategoryNo);
        newCustomerCategoryV2.setDeleted(DeletedEnum.DELETED.getValue());
        CommonUtil.fillOperateInfo(operationModel, newCustomerCategoryV2);

        LambdaUpdateWrapper<CustomerCategoryV2> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.eq(CustomerCategoryV2::getEnterpriseNo, enterpriseNo)
                .eq(CustomerCategoryV2::getManageOrgNo, customerCategoryDeleteReq.getManageOrgNo())
                .eq(CustomerCategoryV2::getNo, customerCategoryNo);
        customerCategoryV2DAO.update(newCustomerCategoryV2, updateWrapper);

        List<CustomerCategoryBase> customerCategoryBases = customerCategoryBaseDAO.selectList(new LambdaQueryWrapper<CustomerCategoryBase>()
                .eq(CustomerCategoryBase::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CustomerCategoryBase::getManageOrgNo, customerCategoryDeleteReq.getManageOrgNo())
                .eq(CustomerCategoryBase::getCategoryNo, customerCategoryDeleteReq.getNo())
                .eq(CustomerCategoryBase::getDeleted, DeletedEnum.UN_DELETE.getValue()));

        if (CollectionUtils.isNotEmpty(customerCategoryBases)) {
            for (CustomerCategoryBase customerCategoryBase : customerCategoryBases) {
                CustomerCategoryBase updateCustomerCategoryBase = new CustomerCategoryBase();
                updateCustomerCategoryBase.setDeleted(DeletedEnum.DELETED.getValue());
                customerCategoryBaseDAO.update(updateCustomerCategoryBase, Wrappers.<CustomerCategoryBase>lambdaUpdate()
                        .eq(CustomerCategoryBase::getId, customerCategoryBase.getId()));
            }
        }


        // 非第一级节点，并且上级节点没有子节点，则需要补充上级节点的分派记录
        if (!ServiceConstant.TOP_PARENT_NO.equals(dbCustomerCategoryV2.getParentNo())) {
            boolean parentNodeHasDescendants = customerCategoryV2DAO.exists(new LambdaQueryWrapper<CustomerCategoryV2>()
                    .eq(CustomerCategoryV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                    .eq(CustomerCategoryV2::getManageOrgNo, customerCategoryDeleteReq.getManageOrgNo())
                    .eq(CustomerCategoryV2::getParentNo, dbCustomerCategoryV2.getParentNo())
                    .eq(CustomerCategoryV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
            if (!parentNodeHasDescendants) {
                CustomerCategoryV2 parentCustomerCategoryV2 = customerCategoryV2DAO.selectOne(new LambdaQueryWrapper<CustomerCategoryV2>()
                        .eq(CustomerCategoryV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                        .eq(CustomerCategoryV2::getManageOrgNo, customerCategoryDeleteReq.getManageOrgNo())
                        .eq(CustomerCategoryV2::getNo, dbCustomerCategoryV2.getParentNo())
                        .eq(CustomerCategoryV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));

                CustomerCategoryBase customerCategoryBase = new CustomerCategoryBase();
                customerCategoryBase.setEnterpriseNo(operationModel.getEnterpriseNo());
                customerCategoryBase.setManageOrgNo(customerCategoryDeleteReq.getManageOrgNo());
                customerCategoryBase.setUseOrgNo(customerCategoryDeleteReq.getManageOrgNo());
                customerCategoryBase.setCategoryNo(parentCustomerCategoryV2.getNo());
                customerCategoryBase.setCategoryCode(parentCustomerCategoryV2.getCategoryCode());
                customerCategoryBase.setDeleted(DeletedEnum.UN_DELETE.getValue());
                CommonUtil.fillCreatInfo(operationModel, customerCategoryBase);
                CommonUtil.fillOperateInfo(operationModel, customerCategoryBase);
                customerCategoryBaseDAO.insert(customerCategoryBase);
            }
        }

        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                try {
                    businessLogService.saveLog(DLogLevel.INFO.name(), ViewNameConstant.BDC_CUSTOMER_CATEGORY_VIEW, customerCategoryNo, "删除客户分类", "删除客户分类", JSON.toJSONString(newCustomerCategoryV2), "");
                } catch (Exception e) {
                    log.error("删除客户分类日志保存失败", e);
                }
            }
        });

        return true;
    }

    @Override
    @Transactional
    public Boolean changeCustomerCategoryStatus(OperationModel operationModel, CustomerCategoryChangeStatusReq customerCategoryChangeStatusReq) {
        // 校验必填参数
        ValidatorUtils.checkEmptyThrowEx(operationModel.getEnterpriseNo(), "租户编号不能为空");
        ValidatorUtils.checkEmptyThrowEx(customerCategoryChangeStatusReq.getManageOrgNo(), "管理组织不能为空");
        ValidatorUtils.checkEmptyThrowEx(customerCategoryChangeStatusReq.getNo(), "客户分类编号不能为空");

        CustomerCategoryV2 dbCustomerCategoryV2 = customerCategoryV2DAO.selectOne(new LambdaQueryWrapper<CustomerCategoryV2>()
                .eq(CustomerCategoryV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CustomerCategoryV2::getManageOrgNo, customerCategoryChangeStatusReq.getManageOrgNo())
                .eq(CustomerCategoryV2::getNo, customerCategoryChangeStatusReq.getNo())
                .eq(CustomerCategoryV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
        ValidatorUtils.checkEmptyThrowEx(dbCustomerCategoryV2, "客户分类不存在");

        CustomerCategoryV2 dbParentCustomerCategoryV2 = null;
        if (!ServiceConstant.TOP_PARENT_NO.equals(dbCustomerCategoryV2.getParentNo())) {
            dbParentCustomerCategoryV2 = customerCategoryV2DAO.selectOne(new LambdaQueryWrapper<CustomerCategoryV2>()
                    .eq(CustomerCategoryV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                    .eq(CustomerCategoryV2::getManageOrgNo, customerCategoryChangeStatusReq.getManageOrgNo())
                    .eq(CustomerCategoryV2::getNo, dbCustomerCategoryV2.getParentNo())
                    .eq(CustomerCategoryV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
            ValidatorUtils.checkEmptyThrowEx(dbParentCustomerCategoryV2, "上级客户分类不存在");
        }


        String enterpriseNo = operationModel.getEnterpriseNo();
        String customerCategoryNo = customerCategoryChangeStatusReq.getNo();
        String manageOrgNo = customerCategoryChangeStatusReq.getManageOrgNo();

        if (CommonStatusEnum.EFFECTIVE.getValue().toString().equals(customerCategoryChangeStatusReq.getStatus())) {// 启用分类
            if (null != dbParentCustomerCategoryV2) {
                ValidatorUtils.checkTrueThrowEx(CommonStatusEnum.INVALID.getValue().toString().equals(dbParentCustomerCategoryV2.getStatus()), "上级客户分类已停用");
            }

            CustomerCategoryV2 newCustomerCategoryV2 = new CustomerCategoryV2();
//            newCustomerCategoryV2.setEnterpriseNo(enterpriseNo);
//            newCustomerCategoryV2.setManageOrgNo(manageOrgNo);
//            newCustomerCategoryV2.setNo(customerCategoryNo);
            newCustomerCategoryV2.setStatus(CommonStatusEnum.EFFECTIVE.getValue().toString());
            CommonUtil.fillOperateInfo(operationModel, newCustomerCategoryV2);
            LambdaUpdateWrapper<CustomerCategoryV2> updateWrapper = Wrappers.lambdaUpdate();
            updateWrapper.eq(CustomerCategoryV2::getEnterpriseNo, enterpriseNo)
                    .eq(CustomerCategoryV2::getManageOrgNo, manageOrgNo)
                    .eq(CustomerCategoryV2::getNo, customerCategoryNo);
            customerCategoryV2DAO.update(newCustomerCategoryV2, updateWrapper);

            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                @Override
                public void afterCommit() {
                    try {
                        businessLogService.saveLog(DLogLevel.INFO.name(), ViewNameConstant.BDC_CUSTOMER_CATEGORY_VIEW, customerCategoryNo, "启用客户分类", "启用客户分类", JSON.toJSONString(newCustomerCategoryV2), "");
                    } catch (Exception e) {
                        log.error("启用客户分类日志保存失败", e);
                    }
                }
            });
        } else { // 停用分类
            List<String> allNos = new ArrayList<>();
            allNos.add(dbCustomerCategoryV2.getNo());

            recursiveGetDescendants(enterpriseNo, manageOrgNo, Collections.singletonList(dbCustomerCategoryV2.getNo()), allNos, 0);

            List<CustomerCategoryV2> newCustomerCategoryV2List = new ArrayList<>(allNos.size());
            for (String no : allNos) {
                CustomerCategoryV2 newCustomerCategoryV2 = new CustomerCategoryV2();
                newCustomerCategoryV2.setEnterpriseNo(enterpriseNo);
                newCustomerCategoryV2.setManageOrgNo(manageOrgNo);
                newCustomerCategoryV2.setNo(no);
                newCustomerCategoryV2.setStatus(CommonStatusEnum.INVALID.getValue().toString());
                CommonUtil.fillOperateInfo(operationModel, newCustomerCategoryV2);

                newCustomerCategoryV2List.add(newCustomerCategoryV2);
            }
            customerCategoryV2DAO.batchStatusUpdate(newCustomerCategoryV2List);

            for (CustomerCategoryV2 customerCategoryV2 : newCustomerCategoryV2List) {
                TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                    @Override
                    public void afterCommit() {
                        try {
                            businessLogService.saveLog(DLogLevel.INFO.name(), ViewNameConstant.BDC_CUSTOMER_CATEGORY_VIEW, customerCategoryV2.getNo(), "停用客户分类", "停用客户分类", JSON.toJSONString(customerCategoryV2), "");
                        } catch (Exception e) {
                            log.error("停用客户分类日志保存失败", e);
                        }
                    }
                });
            }
        }

        return true;
    }

    @Override
    public CustomerCategoryV2 getByNo(String enterpriseNo, String no) {
        return customerCategoryV2DAO.getByNo(enterpriseNo, no);
    }

    @Override
    public List<CustomerCategoryV2> getByNoList(String enterpriseNo, List<String> noList) {
        return customerCategoryV2DAO.getByNoList(enterpriseNo, noList);
    }

    private void recursiveGetDescendantCustomerCategoryV2s(String enterpriseNo, String
            manageOrgNo, List<String> parentNos, List<CustomerCategoryV2> allDescendants, int depth) {
        if (depth >= MAX_RECURSION_DEPTH) {
            log.error("递归深度超过{}层，停止递归", MAX_RECURSION_DEPTH);
            throw new BusinessException("分类层级异常");
        }

        LambdaQueryWrapper<CustomerCategoryV2> wrapper = Wrappers.<CustomerCategoryV2>lambdaQuery()
                .eq(CustomerCategoryV2::getEnterpriseNo, enterpriseNo)
                .eq(CustomerCategoryV2::getManageOrgNo, manageOrgNo)
                .in(CustomerCategoryV2::getParentNo, parentNos)
                .eq(CustomerCategoryV2::getDeleted, DeletedEnum.UN_DELETE.getValue());
        List<CustomerCategoryV2> customerCategoryV2s = customerCategoryV2DAO.selectList(wrapper);

        if (CollectionUtils.isNotEmpty(customerCategoryV2s)) {
            List<String> parentNosNew = customerCategoryV2s.stream().map(CustomerCategoryV2::getNo).collect(Collectors.toList());
            allDescendants.addAll(customerCategoryV2s);

            recursiveGetDescendantCustomerCategoryV2s(enterpriseNo, manageOrgNo, parentNosNew, allDescendants, depth + 1);
        }
    }

    private void recursiveGetDescendants(String enterpriseNo, String
            manageOrgNo, List<String> parentNos, List<String> allNos, int depth) {
        if (depth >= MAX_RECURSION_DEPTH) {
            log.error("递归深度超过{}层，停止递归", MAX_RECURSION_DEPTH);
            throw new BusinessException("分类层级异常");
        }

        LambdaQueryWrapper<CustomerCategoryV2> wrapper = Wrappers.<CustomerCategoryV2>lambdaQuery()
                .eq(CustomerCategoryV2::getEnterpriseNo, enterpriseNo)
                .eq(CustomerCategoryV2::getManageOrgNo, manageOrgNo)
                .in(CustomerCategoryV2::getParentNo, parentNos)
                .eq(CustomerCategoryV2::getDeleted, DeletedEnum.UN_DELETE.getValue());
        List<CustomerCategoryV2> customerCategoryV2s = customerCategoryV2DAO.selectList(wrapper);

        if (CollectionUtils.isNotEmpty(customerCategoryV2s)) {
            List<String> parentNosNew = customerCategoryV2s.stream().map(CustomerCategoryV2::getNo).collect(Collectors.toList());
            allNos.addAll(parentNosNew);

            recursiveGetDescendants(enterpriseNo, manageOrgNo, parentNosNew, allNos, depth + 1);
        }
    }

    private void findAllDescendants(List<? extends TreeGrid> treeVoList, List<String> allNos, int depth) {
        if (depth >= MAX_RECURSION_DEPTH) {
            log.error("递归深度超过{}层，停止递归", MAX_RECURSION_DEPTH);
            throw new BusinessException("分类层级异常");
        }

        if (CollectionUtils.isEmpty(treeVoList)) {
            return;
        }
        for (TreeGrid treeGrid : treeVoList) {
            CustomerCategoryTreeVO customerCategoryTreeVO = (CustomerCategoryTreeVO) treeGrid;
            allNos.add(customerCategoryTreeVO.getNo());

            if (CollectionUtils.isNotEmpty(customerCategoryTreeVO.getChildren())) {
                findAllDescendants(customerCategoryTreeVO.getChildren(), allNos, depth + 1);
            }
        }
    }

    private CustomerCategoryTreeVO findSpecificNode(List<? extends TreeGrid> treeVoList, String parentNo,
                                                    int depth) {
        if (depth >= MAX_RECURSION_DEPTH) {
            log.error("递归深度超过{}层，停止递归", MAX_RECURSION_DEPTH);
            throw new BusinessException("分类层级异常");
        }

        if (CollectionUtils.isEmpty(treeVoList)) {
            return null;
        }

        for (TreeGrid treeGrid : treeVoList) {
            CustomerCategoryTreeVO customerCategoryTreeVO = (CustomerCategoryTreeVO) treeGrid;
            if (parentNo.equals(customerCategoryTreeVO.getNo())) {
                return customerCategoryTreeVO;
            }

            if (CollectionUtils.isNotEmpty(customerCategoryTreeVO.getChildren())) {
                CustomerCategoryTreeVO result = findSpecificNode(customerCategoryTreeVO.getChildren(), parentNo, depth + 1);
                if (result != null) {
                    return result;
                }
            }
        }

        return null;
    }

    private void findNextLevels(List<? extends TreeGrid> treeVoList, String
            parentCategoryNo, List<String> allNos, int depth) {
        if (depth >= MAX_RECURSION_DEPTH) {
            log.error("递归深度超过{}层，停止递归", MAX_RECURSION_DEPTH);
            throw new BusinessException("分类层级异常");
        }

        if (null == treeVoList) {
            return;
        }

        if (ServiceConstant.TOP_PARENT_NO.equals(parentCategoryNo)) {
            for (TreeGrid treeGrid : treeVoList) {
                CustomerCategoryTreeVO customerCategoryTreeVO = (CustomerCategoryTreeVO) treeGrid;
                allNos.add(customerCategoryTreeVO.getNo());
            }
            return;
        }

        for (TreeGrid treeGrid : treeVoList) {
            CustomerCategoryTreeVO customerCategoryTreeVO = (CustomerCategoryTreeVO) treeGrid;
            if (parentCategoryNo.equals(customerCategoryTreeVO.getNo())) {
                if (null != customerCategoryTreeVO.getChildren()) {
                    for (TreeGrid child : customerCategoryTreeVO.getChildren()) {
                        CustomerCategoryTreeVO node = (CustomerCategoryTreeVO) child;
                        allNos.add(node.getNo());
                    }
                }
                return;
            } else {
                findNextLevels(customerCategoryTreeVO.getChildren(), parentCategoryNo, allNos, depth + 1);
            }
        }
    }

    //查询所有祖先节点
    private Set<String> findAllAncestors(Map<String, CustomerCategoryV2> nodeMap, String no) {
        Set<String> ancestors = new HashSet<>();
        CustomerCategoryV2 current = nodeMap.get(no);
        while (current != null && !ServiceConstant.TOP_PARENT_NO.equals(current.getParentNo())) {
            ancestors.add(current.getParentNo());
            current = nodeMap.get(current.getParentNo());
        }
        return ancestors;
    }

    // 找出有权限的节点列表
    private List<CustomerCategoryV2> findAuthorizedNodes
    (List<CustomerCategoryV2> allNodes, List<CustomerCategoryV2> authorizedNodes) {
        Map<String, CustomerCategoryV2> nodeMap = new HashMap<>();
        Set<String> authorizedNos = new HashSet<>();

        // 将所有节点存储到映射中
        for (CustomerCategoryV2 node : allNodes) {
            nodeMap.put(node.getNo(), node);
        }

        // 获取所有有权限的节点及其祖先节点的编码
        for (CustomerCategoryV2 node : authorizedNodes) {
            authorizedNos.add(node.getNo());
            authorizedNos.addAll(findAllAncestors(nodeMap, node.getNo()));
        }

        // 根据编码筛选出有权限的节点
        List<CustomerCategoryV2> result = new ArrayList<>();
        for (CustomerCategoryV2 node : allNodes) {
            if (authorizedNos.contains(node.getNo())) {
                result.add(node);
            }
        }

        return result;
    }

    private CustomerCategoryV2 createAndUpdateCategoryPerRecordForDA(OperationModel operationModel, int lv, CustomerCategoryV2 preCurrentCategory, CustomerExternalSaveDTO customerExternalSaveDTO, List<CustomerCategoryV2> customerCategoryList) {
        if (!(lv >= 1 && lv <= 5)) {
            throw new BusinessException(ErrorCode.param_invalid_code, "客户分类层级不正确");
        }

        Optional<CustomerCategoryV2> categoryOptional = null;

        if (1 == lv) {
            Map<String, List<CustomerCategoryV2>> parentNoMap = customerCategoryList.stream().collect(Collectors.groupingBy(CustomerCategoryV2::getParentNo));
            List<CustomerCategoryV2> topNodeList = parentNoMap.getOrDefault(ServiceConstant.TOP_PARENT_NO, new ArrayList<>());

            categoryOptional = topNodeList.stream()
                    .filter(customerCategory ->
                            customerExternalSaveDTO.getCustomerCategoryCode_1().equals(customerCategory.getCategoryCode())).findAny();
        } else if (2 == lv) {
            categoryOptional = customerCategoryList.stream().filter(s -> s.getParentNo().equals(preCurrentCategory.getNo())
                    && customerExternalSaveDTO.getCustomerCategoryCode_2().equals(s.getCategoryCode())).findAny();
        } else if (3 == lv) {
            categoryOptional = customerCategoryList.stream().filter(s -> s.getParentNo().equals(preCurrentCategory.getNo())
                    && customerExternalSaveDTO.getCustomerCategoryCode_3().equals(s.getCategoryCode())).findAny();
        } else if (4 == lv) {
            categoryOptional = customerCategoryList.stream().filter(s -> s.getParentNo().equals(preCurrentCategory.getNo())
                    && customerExternalSaveDTO.getCustomerCategoryCode_4().equals(s.getCategoryCode())).findAny();
        } else if (5 == lv) {
            categoryOptional = customerCategoryList.stream().filter(s -> s.getParentNo().equals(preCurrentCategory.getNo())
                    && customerExternalSaveDTO.getCustomerCategoryCode_5().equals(s.getCategoryCode())).findAny();
        }

        if (categoryOptional.isPresent()) {
            CustomerCategoryV2 category = categoryOptional.get();

            CustomerCategoryV2 newCustomerCategoryV2 = new CustomerCategoryV2();
            if (1 == lv) {
                newCustomerCategoryV2.setCategoryName(customerExternalSaveDTO.getCustomerCategoryName_1());
            } else if (2 == lv) {
                newCustomerCategoryV2.setCategoryName(customerExternalSaveDTO.getCustomerCategoryName_2());
            } else if (3 == lv) {
                newCustomerCategoryV2.setCategoryName(customerExternalSaveDTO.getCustomerCategoryName_3());
            } else if (4 == lv) {
                newCustomerCategoryV2.setCategoryName(customerExternalSaveDTO.getCustomerCategoryName_4());
            } else if (5 == lv) {
                newCustomerCategoryV2.setCategoryName(customerExternalSaveDTO.getCustomerCategoryName_5());
            }
            CommonUtil.fillUpdateInfo(operationModel, newCustomerCategoryV2);

            customerCategoryV2DAO.update(newCustomerCategoryV2, Wrappers.<CustomerCategoryV2>lambdaUpdate()
                    .eq(CustomerCategoryV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                    .eq(CustomerCategoryV2::getNo, category.getNo()));

            // 回填分类编号
            customerExternalSaveDTO.setCustomerCategoryNo(category.getNo());

            return category;
        } else {
            //新增逻辑
            CustomerCategoryV2 category = new CustomerCategoryV2();
            category.setParentNo(ServiceConstant.TOP_PARENT_NO);
            category.setNo(numberCenterService.createNumber(NumberCenterConstant.CUSTOMER_NO_CATEGORY_KEY_V2));
            category.setEnterpriseNo(operationModel.getEnterpriseNo());
            if (1 == lv) {
                category.setCategoryName(customerExternalSaveDTO.getCustomerCategoryName_1());
                category.setCategoryCode(customerExternalSaveDTO.getCustomerCategoryCode_1());
                category.setMnemonicCode(customerExternalSaveDTO.getCustomerCategoryCode_1());
            } else if (2 == lv) {
                category.setCategoryName(customerExternalSaveDTO.getCustomerCategoryName_2());
                category.setCategoryCode(customerExternalSaveDTO.getCustomerCategoryCode_2());
                category.setMnemonicCode(customerExternalSaveDTO.getCustomerCategoryCode_2());
            } else if (3 == lv) {
                category.setCategoryName(customerExternalSaveDTO.getCustomerCategoryName_3());
                category.setCategoryCode(customerExternalSaveDTO.getCustomerCategoryCode_3());
                category.setMnemonicCode(customerExternalSaveDTO.getCustomerCategoryCode_3());
            } else if (4 == lv) {
                category.setCategoryName(customerExternalSaveDTO.getCustomerCategoryName_4());
                category.setCategoryCode(customerExternalSaveDTO.getCustomerCategoryCode_4());
                category.setMnemonicCode(customerExternalSaveDTO.getCustomerCategoryCode_4());
            } else if (5 == lv) {
                category.setCategoryName(customerExternalSaveDTO.getCustomerCategoryName_5());
                category.setCategoryCode(customerExternalSaveDTO.getCustomerCategoryCode_5());
                category.setMnemonicCode(customerExternalSaveDTO.getCustomerCategoryCode_5());
            }
            category.setStatus(CommonStatusEnum.EFFECTIVE.getValue().toString());
            category.setDeleted(DeletedEnum.UN_DELETE.getValue());
            CommonUtil.fillCreatInfo(operationModel, category);
            customerCategoryV2DAO.insert(category);

            // 回填分类编号
            customerExternalSaveDTO.setCustomerCategoryNo(category.getNo());

            customerCategoryList.add(category);

            return category;
        }
    }

    @Override
    @Transactional
    public void createAndUpdateCategoryForDA(OperationModel operationModel, List<CustomerExternalSaveDTO> params) {
        List<CustomerCategoryV2> customerCategoryList = customerCategoryV2DAO.selectList(Wrappers.lambdaQuery(CustomerCategoryV2.class)
                .eq(CustomerCategoryV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CustomerCategoryV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));


        params.forEach(t -> {
            if (StringUtils.isBlank(t.getCustomerCategoryCode_1())) {
                return;
            }

            CustomerCategoryV2 preCustomerCategory = createAndUpdateCategoryPerRecordForDA(operationModel, 1, null, t, customerCategoryList);

            if (StringUtils.isNotBlank(t.getCustomerCategoryCode_2())) {
                preCustomerCategory = createAndUpdateCategoryPerRecordForDA(operationModel, 2, preCustomerCategory, t, customerCategoryList);
            }

            if (StringUtils.isNotBlank(t.getCustomerCategoryCode_3())) {
                preCustomerCategory = createAndUpdateCategoryPerRecordForDA(operationModel, 3, preCustomerCategory, t, customerCategoryList);
            }

            if (StringUtils.isNotBlank(t.getCustomerCategoryCode_4())) {
                preCustomerCategory = createAndUpdateCategoryPerRecordForDA(operationModel, 4, preCustomerCategory, t, customerCategoryList);
            }
            if (StringUtils.isNotBlank(t.getCustomerCategoryCode_5())) {
                createAndUpdateCategoryPerRecordForDA(operationModel, 5, preCustomerCategory, t, customerCategoryList);
            }
        });
    }
}

