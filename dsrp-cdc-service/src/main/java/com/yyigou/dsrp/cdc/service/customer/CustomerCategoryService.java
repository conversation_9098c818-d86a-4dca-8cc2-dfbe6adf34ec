package com.yyigou.dsrp.cdc.service.customer;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yyigou.dsrp.cdc.api.customer.dto.CustomerExternalSaveDTO;
import com.yyigou.dsrp.cdc.api.customer.vo.CustomerCategoryTree;
import com.yyigou.dsrp.cdc.common.util.OperationModel;
import com.yyigou.dsrp.cdc.dao.customer.entity.CustomerCategory;

import java.util.LinkedList;
import java.util.List;

/**
 * 客户分类Service
 *
 * @author: Moore
 * @date: 2024/7/17 15:26
 * @version: 1.0.0
 */
public interface CustomerCategoryService extends IService<CustomerCategory> {

    /**
     * 根据客户分类编号查询客户分类信息
     *
     * @author: Moore
     * @date: 2024/7/17 15:30
     * @version: 1.0.0
     */
    CustomerCategory getByNo(String enterpriseNo, String no);

    CustomerCategory getByGroupNo(String enterpriseNo, String groupNo);


    /**
     * 根据客户分类编号集合查询客户分类信息
     *
     * @author: Moore
     * @date: 2024/7/17 15:30
     * @version: 1.0.0
     */
    List<CustomerCategory> getCategoryNoList(String enterpriseNo, List<String> categoryNoList);

    /**
     * 根据客户分类编号查询父级分类信息
     *
     * @author: Moore
     * @date: 2024/7/17 15:30
     * @version: 1.0.0
     */
    LinkedList<CustomerCategory> getParentNoLinkedList(String enterpriseNo, String no);

    /**
     * 根据租户编号+集团租户分类编号+父级分类编号查询分类信息
     *
     * @param enterpriseNo
     * @param groupNo
     * @param parentNo
     * @return: {@link CustomerCategory}
     */
    CustomerCategory findByGroupNoAndParentNo(String enterpriseNo, String groupNo, String parentNo);

    void createAndUpdateCategory(OperationModel operationModel, List<CustomerExternalSaveDTO> params);


    List<CustomerCategoryTree> queryTree(OperationModel operationModel);

    List<CustomerCategoryTree> queryGroupTree(OperationModel operationModel);

    List<CustomerCategoryTree> queryOrgTree(OperationModel operationModel, String orgNo);

}
