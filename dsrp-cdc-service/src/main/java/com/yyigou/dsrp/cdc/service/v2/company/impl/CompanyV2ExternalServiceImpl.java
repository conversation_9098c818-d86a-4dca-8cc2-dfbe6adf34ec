package com.yyigou.dsrp.cdc.service.v2.company.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yyigou.ddc.services.ddc.uim.vo.OrganizationTreeVo;
import com.yyigou.dsrp.cdc.api.company.dto.BankDTO;
import com.yyigou.dsrp.cdc.api.customer.dto.CustomerExternalSaveDTO;
import com.yyigou.dsrp.cdc.api.supply.dto.SupplierExternalSaveDTO;
import com.yyigou.dsrp.cdc.common.enums.CommonStatusEnum;
import com.yyigou.dsrp.cdc.common.enums.DeletedEnum;
import com.yyigou.dsrp.cdc.common.enums.company.CompanyPartnershipEnum;
import com.yyigou.dsrp.cdc.common.util.BeanUtil;
import com.yyigou.dsrp.cdc.common.util.CommonUtil;
import com.yyigou.dsrp.cdc.common.util.OperationModel;
import com.yyigou.dsrp.cdc.dao.common.BankTypeDAO;
import com.yyigou.dsrp.cdc.dao.common.entity.BankType;
import com.yyigou.dsrp.cdc.dao.v2.company.CompanyBankV2DAO;
import com.yyigou.dsrp.cdc.dao.v2.company.CompanyLinkmanV2DAO;
import com.yyigou.dsrp.cdc.dao.v2.company.CompanyShippingAddressV2DAO;
import com.yyigou.dsrp.cdc.dao.v2.company.CompanyV2DAO;
import com.yyigou.dsrp.cdc.dao.v2.company.entity.CompanyBankV2;
import com.yyigou.dsrp.cdc.dao.v2.company.entity.CompanyV2;
import com.yyigou.dsrp.cdc.manager.integration.dict.DictEnterpriseService;
import com.yyigou.dsrp.cdc.manager.integration.dlog.BusinessLogService;
import com.yyigou.dsrp.cdc.manager.integration.numberCenter.NumberCenterService;
import com.yyigou.dsrp.cdc.manager.integration.org.UimTenantService;
import com.yyigou.dsrp.cdc.manager.integration.uim.CustomDocService;
import com.yyigou.dsrp.cdc.model.constant.NumberCenterConstant;
import com.yyigou.dsrp.cdc.model.constant.SystemConstant;
import com.yyigou.dsrp.cdc.service.v2.company.CompanyV2ExternalService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service("companyV2ExternalService")
@Slf4j
public class CompanyV2ExternalServiceImpl implements CompanyV2ExternalService {
    @Resource
    private CompanyV2DAO companyV2Dao;

    @Resource
    private CompanyBankV2DAO companyBankV2DAO;

    @Resource
    private CompanyLinkmanV2DAO companyLinkmanV2DAO;

    @Resource
    private CompanyShippingAddressV2DAO companyShippingAddressV2DAO;

    @Resource
    private BankTypeDAO bankTypeDAO;

    @Resource
    private NumberCenterService numberCenterService;

    @Resource
    private CustomDocService customDocService;

    @Resource
    private DictEnterpriseService dictEnterpriseService;

    @Autowired
    private BusinessLogService businessLogService;

    @Resource
    private UimTenantService uimTenantService;

    private List<CompanyBankV2> convertToCompanyBankV2ForDA(OperationModel operationModel, Map<String, BankType> bankMap, String companyNo, List<BankDTO> bankDTOList) {
        List<CompanyBankV2> result = new ArrayList<>(bankDTOList.size());

        for (BankDTO bankDTO : bankDTOList) {
            final BankType bankType = bankMap.getOrDefault(bankDTO.getBankType(), new BankType());

            CompanyBankV2 companyBank = new CompanyBankV2();

            companyBank.setEnterpriseNo(operationModel.getEnterpriseNo());
            companyBank.setCompanyNo(companyNo);
            companyBank.setBankCode(bankDTO.getBankCode());
            companyBank.setBankType(bankType.getBankCode());
            companyBank.setBankTypeName(bankType.getBankName());
            companyBank.setOpenBank(bankDTO.getOpenBank());
            companyBank.setAccountNo(bankDTO.getAccountNo());
            companyBank.setAccountName(bankDTO.getAccountName());
            companyBank.setAccountType(bankDTO.getAccountType());
            companyBank.setLinkPerson(bankDTO.getLinkPerson());
            companyBank.setLinkPhone(bankDTO.getLinkPhone());
            companyBank.setStatus(bankDTO.getStatus());
            companyBank.setIsDefault(bankDTO.getIsDefault());

            CommonUtil.fillCreatInfo(operationModel, companyBank);

            companyBank.setManageOrgNo(operationModel.getOrgNo());

            // 没有币种
//            companyBank.setCurrency();

            result.add(companyBank);
        }

        return result;
    }

    private CompanyV2 packCompleteCompanyForDA(CompanyV2 newCompany, CompanyV2 oldCompany, String flag) {
        if ("name".equals(flag)) {
            oldCompany.setUnifiedSocialCode(newCompany.getUnifiedSocialCode());
        } else {
            oldCompany.setCompanyName(newCompany.getCompanyName());
        }
        oldCompany.setInstitutionalType(newCompany.getInstitutionalType());
        oldCompany.setHospitalType(newCompany.getHospitalType());
        oldCompany.setHospitalClass(newCompany.getHospitalClass());
        oldCompany.setLegalPerson(newCompany.getLegalPerson());
        oldCompany.setRegionCode(newCompany.getRegionCode());
//        oldCompany.setIsAssociatedEnterprise(newCompany.getIsAssociatedEnterprise());
//        oldCompany.setAssociatedOrgCode(newCompany.getAssociatedOrgCode());
//        oldCompany.setAssociatedOrgNo(newCompany.getAssociatedOrgNo());
//        oldCompany.setAssociatedOrgName(newCompany.getAssociatedOrgName());

        oldCompany.setOperateTime(newCompany.getOperateTime());
        oldCompany.setOperateNo(newCompany.getOperateNo());
        oldCompany.setOperateName(newCompany.getOperateName());

        return oldCompany;
    }

    public Map<String, List<?>> cudBankPerCompanyForDA(OperationModel operationModel, Map<String, BankType> bankMap, List<BankDTO> companyBankReqList, String companyNo, String manageOrgNo) {
        List<CompanyBankV2> companyBankV2s = companyBankV2DAO.selectList(Wrappers.<CompanyBankV2>lambdaQuery()
                .eq(CompanyBankV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CompanyBankV2::getCompanyNo, companyNo)
                .eq(CompanyBankV2::getDeleted, DeletedEnum.UN_DELETE.getValue())
        );
        Map<String, List<?>> diffResultMap = BeanUtil.diffCUD(companyBankV2s, companyBankReqList);

        List<BankDTO> addList = (List<BankDTO>) diffResultMap.get("addList");
        List<BankDTO> updateList = (List<BankDTO>) diffResultMap.get("updateList");
        List<CompanyBankV2> deleteList = (List<CompanyBankV2>) diffResultMap.get("deleteList");

        if (CollectionUtils.isNotEmpty(addList)) {
            List<CompanyBankV2> addBankList = new ArrayList<>();
            for (BankDTO paramBank : addList) {
                final BankType bankType = bankMap.getOrDefault(paramBank.getBankType(), new BankType());
                CompanyBankV2 companyBank = new CompanyBankV2();
                companyBank.setEnterpriseNo(operationModel.getEnterpriseNo());
                companyBank.setCompanyNo(companyNo);
                companyBank.setBankCode(paramBank.getBankCode());
                companyBank.setBankType(bankType.getBankCode());
                companyBank.setBankTypeName(bankType.getBankName());
                companyBank.setOpenBank(paramBank.getOpenBank());
                companyBank.setAccountName(paramBank.getAccountName());
                companyBank.setAccountNo(paramBank.getAccountNo());
                companyBank.setAccountType(paramBank.getAccountType());
                companyBank.setLinkPerson(paramBank.getLinkPerson());
                companyBank.setLinkPhone(paramBank.getLinkPhone());
                companyBank.setStatus(paramBank.getStatus());
                companyBank.setIsDefault(paramBank.getIsDefault());
                companyBank.setManageOrgNo(manageOrgNo);
                // 没有币种
//                companyBank.setCurrency();
                addBankList.add(companyBank);
            }
            companyBankV2DAO.addBatch(addBankList);
        }

        if (CollectionUtils.isNotEmpty(updateList)) {
            List<CompanyBankV2> updateBankList = new ArrayList<>();
            for (BankDTO paramBank : updateList) {
                final BankType bankType = bankMap.getOrDefault(paramBank.getBankType(), new BankType());
                CompanyBankV2 companyBank = new CompanyBankV2();
                companyBank.setId(paramBank.getId());
                companyBank.setEnterpriseNo(operationModel.getEnterpriseNo());
                companyBank.setCompanyNo(companyNo);
                companyBank.setBankCode(paramBank.getBankCode());
                companyBank.setBankType(bankType.getBankCode());
                companyBank.setBankTypeName(bankType.getBankName());
                companyBank.setOpenBank(paramBank.getOpenBank());
                companyBank.setAccountName(paramBank.getAccountName());
                companyBank.setAccountNo(paramBank.getAccountNo());
                companyBank.setAccountType(paramBank.getAccountType());
                companyBank.setLinkPerson(paramBank.getLinkPerson());
                companyBank.setLinkPhone(paramBank.getLinkPhone());
                companyBank.setStatus(paramBank.getStatus());
                companyBank.setIsDefault(paramBank.getIsDefault());
                companyBank.setManageOrgNo(manageOrgNo);

                // 没有币种
//                companyBank.setCurrency();
                updateBankList.add(companyBank);
            }
            companyBankV2DAO.updateByIdBatch(updateBankList);
        }

        if (CollectionUtils.isNotEmpty(deleteList)) {
            CompanyBankV2 companyBank = new CompanyBankV2();
            companyBank.setDeleted(DeletedEnum.DELETED.getValue());
            companyBankV2DAO.update(companyBank, Wrappers.<CompanyBankV2>lambdaQuery()
                    .eq(CompanyBankV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                    .in(CompanyBankV2::getId, deleteList.stream().map(CompanyBankV2::getId).collect(Collectors.toList())));
        }

        return diffResultMap;
    }

    @Override
    @Transactional
    public Map<String, List<CompanyV2>> saveOrUpdateCompanyBySupplierForDA(OperationModel operationModel, List<SupplierExternalSaveDTO> params) {
        CompanyPartnershipEnum companyPartnershipEnum = CompanyPartnershipEnum.SUPPLIER;

//        final List<OrganizationTreeVo> organizationTreeList = uimTenantService.getOrganizationTreeList(operationModel.getEnterpriseNo());
//        final Map<String, OrganizationTreeVo> organizationMap = organizationTreeList.stream().collect(Collectors.toMap(OrganizationTreeVo::getOrgCode, Function.identity(), (k, v) -> k));

        final List<String> unifiedSocialCodeList = params.stream().map(SupplierExternalSaveDTO::getUnifiedSocialCode).filter(t -> !SystemConstant.DEFAULT_UNIFIED_SOCIAL_CODE.equalsIgnoreCase(t)).collect(Collectors.toList());
        final List<String> companyNameList = params.stream().map(SupplierExternalSaveDTO::getCompanyName).collect(Collectors.toList());

        Map<String, CompanyV2> localUnifiedSocialCompanyMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(unifiedSocialCodeList)) {
            List<CompanyV2> localUnifiedSocialCompanyList = companyV2Dao.selectList(new LambdaQueryWrapper<CompanyV2>()
                    .eq(CompanyV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                    .in(CompanyV2::getUnifiedSocialCode, unifiedSocialCodeList)
                    .eq(CompanyV2::getStatus, CommonStatusEnum.EFFECTIVE.getValue())
                    .eq(CompanyV2::getDeleted, DeletedEnum.UN_DELETE.getValue())
            );
            localUnifiedSocialCompanyMap.putAll(localUnifiedSocialCompanyList.stream().collect(Collectors.toMap(CompanyV2::getUnifiedSocialCode, Function.identity(), (v1, v2) -> v1)));
        }

        Map<String, CompanyV2> locaCompanyNameMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(companyNameList)) {
            List<CompanyV2> locaCompanyNameList = companyV2Dao.selectList(new LambdaQueryWrapper<CompanyV2>()
                    .eq(CompanyV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                    .in(CompanyV2::getCompanyName, companyNameList)
                    .eq(CompanyV2::getStatus, CommonStatusEnum.EFFECTIVE.getValue())
                    .eq(CompanyV2::getDeleted, DeletedEnum.UN_DELETE.getValue())
            );
            locaCompanyNameMap.putAll(locaCompanyNameList.stream().collect(Collectors.toMap(CompanyV2::getCompanyName, Function.identity(), (v1, v2) -> v1)));
        }

        final List<BankType> bankList = bankTypeDAO.getList();
        final Map<String, BankType> bankMap = bankList.stream().collect(Collectors.toMap(BankType::getBankName, Function.identity(), (v1, v2) -> v1));

        List<CompanyV2> updateCompanyList = new ArrayList<>();
        List<CompanyV2> addCompanyList = new ArrayList<>();
        List<CompanyV2> packedUpdateCompanyList = new ArrayList<>(); //用于返回更新后的值

        long count = params.stream().filter(t -> !locaCompanyNameMap.containsKey(t.getCompanyName()) && !localUnifiedSocialCompanyMap.containsKey(t.getUnifiedSocialCode())).count();
        final List<String> companyNoList = numberCenterService.batchGenerateNoList(NumberCenterConstant.COMPANY_NO_GENERATE_KEY, (int) count);

        int index = 0;
        for (SupplierExternalSaveDTO t : params) {
            //没有社会信用代码也没有一样企业名称的企业 应该新增企业
            if (locaCompanyNameMap.containsKey(t.getCompanyName()) || localUnifiedSocialCompanyMap.containsKey(t.getUnifiedSocialCode())) {
                String flag = "name";
                CompanyV2 newCompany = new CompanyV2();
                //说明企业名称能匹配上
                CompanyV2 oldCompany = locaCompanyNameMap.get(t.getCompanyName());
                if (null != oldCompany) {
                    newCompany.setUnifiedSocialCode(t.getUnifiedSocialCode());
                } else {
                    oldCompany = localUnifiedSocialCompanyMap.get(t.getUnifiedSocialCode());
                    flag = "unifiedSocialCode";

                    newCompany.setCompanyName(t.getCompanyName());
                }

                newCompany.setInstitutionalType(t.getInstitutionalType());
                newCompany.setHospitalType(t.getHospitalType());
                newCompany.setHospitalClass(t.getHospitalClass());
                newCompany.setLegalPerson(t.getLegalPerson());
                newCompany.setRegionCode(t.getRegionCode());

                List<String> partnerShipList = null;
                if (StringUtils.isNotBlank(oldCompany.getPartnership())) {
                    partnerShipList = JSONArray.parseArray(oldCompany.getPartnership(), String.class);
                } else {
                    partnerShipList = new ArrayList<>();
                }
                if (!partnerShipList.contains(companyPartnershipEnum.getType())) {
                    partnerShipList.add(companyPartnershipEnum.getType());
                }
                newCompany.setPartnership(JSONArray.toJSONString(partnerShipList));

//                newCompany.setIsAssociatedEnterprise(t.getIsAssociatedEnterprise());
//                newCompany.setAssociatedOrgCode(t.getAssociatedOrgCode());
//                if (CommonIfEnum.YES.getValue().equals(t.getIsAssociatedEnterprise())) {
//                    final OrganizationTreeVo organizationTreeVo = organizationMap.get(t.getAssociatedOrgCode());
//                    if (organizationTreeVo != null) {
//                        newCompany.setAssociatedOrgNo(organizationTreeVo.getOrgNo());
//                        newCompany.setAssociatedOrgName(organizationTreeVo.getOrgName());
//                    }
//                }
                CommonUtil.fillOperateInfo(operationModel, newCompany);

                //where条件
                newCompany.setEnterpriseNo(operationModel.getEnterpriseNo());
                newCompany.setCompanyNo(oldCompany.getCompanyNo());

                updateCompanyList.add(newCompany);
//                companyV2Dao.update(newCompany, Wrappers.<CompanyV2>lambdaUpdate().eq(CompanyV2::getCompanyNo, oldCompany.getCompanyNo()));

                cudBankPerCompanyForDA(operationModel, bankMap, CollectionUtils.isEmpty(t.getBankList()) ? new ArrayList<>() : t.getBankList(), oldCompany.getCompanyNo(), operationModel.getOrgNo());

                packedUpdateCompanyList.add(packCompleteCompanyForDA(newCompany, oldCompany, flag));
            } else if (!localUnifiedSocialCompanyMap.containsKey(t.getUnifiedSocialCode()) && !locaCompanyNameMap.containsKey(t.getCompanyName())) {
                CompanyV2 company = new CompanyV2();
                company.setCompanyNo(companyNoList.get(index++));
                company.setEnterpriseNo(operationModel.getEnterpriseNo());
                company.setCompanyCode(SystemConstant.DEFAULT_UNIFIED_SOCIAL_CODE.equalsIgnoreCase(t.getUnifiedSocialCode()) ? t.getSupplierCode() : t.getUnifiedSocialCode());
                company.setCompanyName(t.getCompanyName());
                company.setUnifiedSocialCode(t.getUnifiedSocialCode());
                company.setFactoryType(t.getFactoryType());
                company.setInstitutionalType(t.getInstitutionalType());
                company.setHospitalType(t.getHospitalType());
                company.setHospitalClass(t.getHospitalClass());
                company.setLegalPerson(t.getLegalPerson());
                company.setRegionCode(t.getRegionCode());

                company.setPartnership(JSON.toJSONString(Collections.singletonList(companyPartnershipEnum.getType())));

//                company.setIsAssociatedEnterprise(t.getIsAssociatedEnterprise());
//                company.setAssociatedOrgCode(t.getAssociatedOrgCode());
//                if (CommonIfEnum.YES.getValue().equals(t.getIsAssociatedEnterprise())) {
//                    final OrganizationTreeVo organizationTreeVo = organizationMap.get(t.getAssociatedOrgCode());
//                    if (organizationTreeVo != null) {
//                        company.setAssociatedOrgNo(organizationTreeVo.getOrgNo());
//                        company.setAssociatedOrgName(organizationTreeVo.getOrgName());
//                    }
//                }
                CommonUtil.fillCreatInfo(operationModel, company);
                CommonUtil.fillOperateInfo(operationModel, company);
                company.setStatus(CommonStatusEnum.EFFECTIVE.getValue());
                company.setDeleted(DeletedEnum.UN_DELETE.getValue());

                addCompanyList.add(company);

                locaCompanyNameMap.put(company.getCompanyName(), company);
                localUnifiedSocialCompanyMap.put(company.getUnifiedSocialCode(), company);

                if (CollectionUtils.isNotEmpty(t.getBankList())) {
                    List<CompanyBankV2> addBankList = convertToCompanyBankV2ForDA(operationModel, bankMap, company.getCompanyNo(), t.getBankList());
                    companyBankV2DAO.addBatch(addBankList);

                }
            }
        }

        if (CollectionUtils.isNotEmpty(addCompanyList)) {
            companyV2Dao.addBatch(addCompanyList);
        }

        if (CollectionUtils.isNotEmpty(updateCompanyList)) {
            companyV2Dao.batchUpdateForDA(updateCompanyList);
        }

        HashMap<String, List<CompanyV2>> result = new HashMap<>();
        result.put("addList", addCompanyList);
        result.put("updateList", packedUpdateCompanyList);
        return result;
    }

    @Override
    @Transactional
    public Map<String, List<CompanyV2>> saveOrUpdateCompanyByCustomerForDA(OperationModel operationModel, List<CustomerExternalSaveDTO> params) {
        CompanyPartnershipEnum companyPartnershipEnum = CompanyPartnershipEnum.CUSTOMER;

//        final List<OrganizationTreeVo> organizationTreeList = uimTenantService.getOrganizationTreeList(operationModel.getEnterpriseNo());
//        final Map<String, OrganizationTreeVo> organizationMap = organizationTreeList.stream().collect(Collectors.toMap(OrganizationTreeVo::getOrgCode, Function.identity(), (k, v) -> k));

        final List<String> unifiedSocialCodeList = params.stream().map(CustomerExternalSaveDTO::getUnifiedSocialCode).filter(t -> !SystemConstant.DEFAULT_UNIFIED_SOCIAL_CODE.equalsIgnoreCase(t)).collect(Collectors.toList());
        final List<String> companyNameList = params.stream().map(CustomerExternalSaveDTO::getCompanyName).collect(Collectors.toList());

        Map<String, CompanyV2> localUnifiedSocialCompanyMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(unifiedSocialCodeList)) {
            List<CompanyV2> localUnifiedSocialCompanyList = companyV2Dao.selectList(new LambdaQueryWrapper<CompanyV2>()
                    .eq(CompanyV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                    .in(CompanyV2::getUnifiedSocialCode, unifiedSocialCodeList)
                    .eq(CompanyV2::getStatus, CommonStatusEnum.EFFECTIVE.getValue())
                    .eq(CompanyV2::getDeleted, DeletedEnum.UN_DELETE.getValue())
            );
            localUnifiedSocialCompanyMap.putAll(localUnifiedSocialCompanyList.stream().collect(Collectors.toMap(CompanyV2::getUnifiedSocialCode, Function.identity(), (v1, v2) -> v1)));
        }

        Map<String, CompanyV2> locaCompanyNameMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(companyNameList)) {
            List<CompanyV2> locaCompanyNameList = companyV2Dao.selectList(new LambdaQueryWrapper<CompanyV2>()
                    .eq(CompanyV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                    .in(CompanyV2::getCompanyName, companyNameList)
                    .eq(CompanyV2::getStatus, CommonStatusEnum.EFFECTIVE.getValue())
                    .eq(CompanyV2::getDeleted, DeletedEnum.UN_DELETE.getValue())
            );
            locaCompanyNameMap.putAll(locaCompanyNameList.stream().collect(Collectors.toMap(CompanyV2::getCompanyName, Function.identity(), (v1, v2) -> v1)));
        }

        final List<BankType> bankList = bankTypeDAO.getList();
        final Map<String, BankType> bankMap = bankList.stream().collect(Collectors.toMap(BankType::getBankName, Function.identity(), (v1, v2) -> v1));

        List<CompanyV2> updateCompanyList = new ArrayList<>();
        List<CompanyV2> addCompanyList = new ArrayList<>();
        List<CompanyV2> packedUpdateCompanyList = new ArrayList<>(); //用于返回更新后的值

        long count = params.stream().filter(t -> !locaCompanyNameMap.containsKey(t.getCompanyName()) && !localUnifiedSocialCompanyMap.containsKey(t.getUnifiedSocialCode())).count();
        final List<String> companyNoList = numberCenterService.batchGenerateNoList(NumberCenterConstant.COMPANY_NO_GENERATE_KEY, (int) count);

        int index = 0;
        for (CustomerExternalSaveDTO t : params) {
            //没有社会信用代码也没有一样企业名称的企业 应该新增企业
            if (locaCompanyNameMap.containsKey(t.getCompanyName()) || localUnifiedSocialCompanyMap.containsKey(t.getUnifiedSocialCode())) {
                String flag = "name";
                CompanyV2 newCompany = new CompanyV2();
                //说明企业名称能匹配上
                CompanyV2 oldCompany = locaCompanyNameMap.get(t.getCompanyName());
                if (null != oldCompany) {
                    newCompany.setUnifiedSocialCode(t.getUnifiedSocialCode());
                } else {
                    oldCompany = localUnifiedSocialCompanyMap.get(t.getUnifiedSocialCode());
                    flag = "unifiedSocialCode";

                    newCompany.setCompanyName(t.getCompanyName());
                }

                newCompany.setInstitutionalType(t.getInstitutionalType());
                newCompany.setHospitalType(t.getHospitalType());
                newCompany.setHospitalClass(t.getHospitalClass());
                newCompany.setLegalPerson(t.getLegalPerson());
                newCompany.setRegionCode(t.getRegionCode());

                List<String> partnerShipList = null;
                if (StringUtils.isNotBlank(oldCompany.getPartnership())) {
                    partnerShipList = JSONArray.parseArray(oldCompany.getPartnership(), String.class);
                } else {
                    partnerShipList = new ArrayList<>();
                }
                if (!partnerShipList.contains(companyPartnershipEnum.getType())) {
                    partnerShipList.add(companyPartnershipEnum.getType());
                }
                newCompany.setPartnership(JSONArray.toJSONString(partnerShipList));

//                newCompany.setIsAssociatedEnterprise(t.getIsAssociatedEnterprise());
//                newCompany.setAssociatedOrgCode(t.getAssociatedOrgCode());
//                if (CommonIfEnum.YES.getValue().equals(t.getIsAssociatedEnterprise())) {
//                    final OrganizationTreeVo organizationTreeVo = organizationMap.get(t.getAssociatedOrgCode());
//                    if (organizationTreeVo != null) {
//                        newCompany.setAssociatedOrgNo(organizationTreeVo.getOrgNo());
//                        newCompany.setAssociatedOrgName(organizationTreeVo.getOrgName());
//                    }
//                }
                CommonUtil.fillOperateInfo(operationModel, newCompany);

                //where条件
                newCompany.setEnterpriseNo(operationModel.getEnterpriseNo());
                newCompany.setCompanyNo(oldCompany.getCompanyNo());

                updateCompanyList.add(newCompany);
//                companyV2Dao.update(newCompany, Wrappers.<CompanyV2>lambdaUpdate().eq(CompanyV2::getCompanyNo, oldCompany.getCompanyNo()));

                cudBankPerCompanyForDA(operationModel, bankMap, CollectionUtils.isEmpty(t.getBankList()) ? new ArrayList<>() : t.getBankList(), oldCompany.getCompanyNo(), operationModel.getOrgNo());

                packedUpdateCompanyList.add(packCompleteCompanyForDA(newCompany, oldCompany, flag));
            } else if (!localUnifiedSocialCompanyMap.containsKey(t.getUnifiedSocialCode()) && !locaCompanyNameMap.containsKey(t.getCompanyName())) {
                CompanyV2 company = new CompanyV2();
                company.setCompanyNo(companyNoList.get(index++));
                company.setEnterpriseNo(operationModel.getEnterpriseNo());
                company.setCompanyCode(SystemConstant.DEFAULT_UNIFIED_SOCIAL_CODE.equalsIgnoreCase(t.getUnifiedSocialCode()) ? t.getCustomerCode() : t.getUnifiedSocialCode());
                company.setCompanyName(t.getCompanyName());
                company.setUnifiedSocialCode(t.getUnifiedSocialCode());
                company.setFactoryType(t.getFactoryType());
                company.setInstitutionalType(t.getInstitutionalType());
                company.setHospitalType(t.getHospitalType());
                company.setHospitalClass(t.getHospitalClass());
                company.setLegalPerson(t.getLegalPerson());
                company.setRegionCode(t.getRegionCode());

                company.setPartnership(JSON.toJSONString(Collections.singletonList(companyPartnershipEnum.getType())));

//                company.setIsAssociatedEnterprise(t.getIsAssociatedEnterprise());
//                company.setAssociatedOrgCode(t.getAssociatedOrgCode());
//                if (CommonIfEnum.YES.getValue().equals(t.getIsAssociatedEnterprise())) {
//                    final OrganizationTreeVo organizationTreeVo = organizationMap.get(t.getAssociatedOrgCode());
//                    if (organizationTreeVo != null) {
//                        company.setAssociatedOrgNo(organizationTreeVo.getOrgNo());
//                        company.setAssociatedOrgName(organizationTreeVo.getOrgName());
//                    }
//                }
                CommonUtil.fillCreatInfo(operationModel, company);
                CommonUtil.fillOperateInfo(operationModel, company);
                company.setStatus(CommonStatusEnum.EFFECTIVE.getValue());
                company.setDeleted(DeletedEnum.UN_DELETE.getValue());

                addCompanyList.add(company);

                locaCompanyNameMap.put(company.getCompanyName(), company);
                localUnifiedSocialCompanyMap.put(company.getUnifiedSocialCode(), company);

                if (CollectionUtils.isNotEmpty(t.getBankList())) {
                    List<CompanyBankV2> addBankList = convertToCompanyBankV2ForDA(operationModel, bankMap, company.getCompanyNo(), t.getBankList());
                    companyBankV2DAO.addBatch(addBankList);
                }
            }
        }

        if (CollectionUtils.isNotEmpty(addCompanyList)) {
            companyV2Dao.addBatch(addCompanyList);
        }

        if (CollectionUtils.isNotEmpty(updateCompanyList)) {
            companyV2Dao.batchUpdateForDA(updateCompanyList);
        }

        HashMap<String, List<CompanyV2>> result = new HashMap<>();
        result.put("addList", addCompanyList);
        result.put("updateList", packedUpdateCompanyList);
        return result;
    }
}
