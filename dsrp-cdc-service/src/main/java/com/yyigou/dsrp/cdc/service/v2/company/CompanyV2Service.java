package com.yyigou.dsrp.cdc.service.v2.company;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yyigou.ddc.common.page.PageDto;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.dsrp.cdc.api.v2.company.vo.CompanyBankVO;
import com.yyigou.dsrp.cdc.api.v2.company.vo.CompanyBasicVO;
import com.yyigou.dsrp.cdc.api.v2.company.vo.CompanyDetailVO;
import com.yyigou.dsrp.cdc.client.company.response.CompanyLinkmanResponse;
import com.yyigou.dsrp.cdc.client.company.response.CompanyShippingAddressResponse;
import com.yyigou.dsrp.cdc.client.v2.company.response.Company2InfoResponse;
import com.yyigou.dsrp.cdc.client.v2.company.response.CompanyAssociatedOrgResponse;
import com.yyigou.dsrp.cdc.client.v2.company.response.CompanyBankResponse;
import com.yyigou.dsrp.cdc.client.v2.company.response.CompanyBasicResponse;
import com.yyigou.dsrp.cdc.common.enums.LinkmanTypeEnum;
import com.yyigou.dsrp.cdc.common.enums.company.CompanyPartnershipEnum;
import com.yyigou.dsrp.cdc.common.util.OperationModel;
import com.yyigou.dsrp.cdc.dao.common.entity.BankType;
import com.yyigou.dsrp.cdc.dao.v2.company.entity.CompanyBankV2;
import com.yyigou.dsrp.cdc.dao.v2.company.entity.CompanyV2;
import com.yyigou.dsrp.cdc.model.v2.company.req.*;
import com.yyigou.dsrp.cdc.service.listener.model.v2.CompanyUpdateModel;
import com.yyigou.dsrp.cdc.service.listener.model.v2.TenantCompanySaveModel;

import java.util.List;
import java.util.Map;

public interface CompanyV2Service extends IService<CompanyV2> {
    PageVo<CompanyDetailVO> findCompanyListPage(CompanyQueryListPageReq queryReq, PageDto pageDTO);

    PageVo<CompanyDetailVO> findCompanyListPageForGoods(CompanyQueryListPageForGoodsReq queryReq, PageDto pageDTO);

    Boolean checkName(OperationModel operationModel, String no, String name);

    Boolean checkUnifiedSocialCode(OperationModel operationModel, String no, String unifiedSocialCode, Integer factoryType);

    Boolean checkUniqueCompany(OperationModel operationModel, String no, String name, String unifiedSocialCode, Integer factoryType);
    /**
     * 保存企业档案信息
     *
     * @param operationModel
     * @param companySaveReq
     * @return: {@link CompanyBasicVO}
     */
    CompanyBasicVO saveCompany(OperationModel operationModel, CompanySaveReq companySaveReq);


    CompanyBasicVO createSimpleCompany(String enterpriseNo, CompanySaveReq companySaveReq);

    /**
     * 根据租户编号和企业编号获取企业档案详情
     *
     * @param enterpriseNo
     * @param companyNo
     * @return: {@link CompanyDetailVO}
     */
    CompanyDetailVO getDetailByEnterpriseAndCompanyNo(String enterpriseNo, String companyNo);

    CompanyDetailVO getTenantCompany(String enterpriseNo);

    CompanyDetailVO getDetailByEnterpriseAndCompanyName(String enterpriseNo, String companyName);

    CompanyV2 getByEnterpriseAndCompanyName(String enterpriseNo, String companyName);

    CompanyV2 getByEnterpriseAndCompanyNo(String enterpriseNo, String companyNo);

    CompanyV2 getByEnterpriseAndUnifiedSocialCodeDomestic(String enterpriseNo, String unifiedSocialCode);

    /**
     * 修改企业档案
     *
     * @param operationModel
     * @param companyUpdateReq
     * @return: {@link CompanyUpdateModel}
     */
    CompanyUpdateModel updateCompany(OperationModel operationModel, CompanyUpdateReq companyUpdateReq);

    CompanyV2 saveOrUpdateCompany(OperationModel operationModel, String supCusCode, CompanySaveOrUpdateReq companySaveOrUpdateReq, CompanyPartnershipEnum companyPartnershipEnum);

    void saveSupCusAssociatedOrg(String companyNo ,String enterpriseNo, String supCusCode, CompanySaveOrUpdateReq companySaveOrUpdateReq, CompanyPartnershipEnum companyPartnershipEnum);

    /**
     * 预校验企业档案
     *
     * @param operationModel
     * @param supCusCode
     * @param companySaveOrUpdateReq
     * @param companyPartnershipEnum
     */
    String preValidateSaveOrUpdateCompany(OperationModel operationModel, String supCusCode, CompanySaveOrUpdateReq companySaveOrUpdateReq, CompanyPartnershipEnum companyPartnershipEnum);


    List<Company2InfoResponse> findListByCompanyNoList(String enterpriseNo, List<String> companyNoList);

    List<BankType> getBankTypeList();
    
    /**
     * 根据企业编号和供应商/客户类型获取银行信息
     * @param enterpriseNo
     * @param companyNo
     * @return
     */
    List<CompanyBankVO> findBankList(String enterpriseNo, String companyNo);

    List<CompanyBankV2> findBankList(String enterpriseNo, List<String> companyNoList);

    /**
     * 添加或更新银行信息
     * @param operationModel
     * @param companyBankReqList
     * @param companyNo
     * @param manageOrgNo
     * @return
     */
    Map<String, List<?>> saveOrUpdateBank(OperationModel operationModel, List<CompanyBankReq> companyBankReqList, String companyNo, String manageOrgNo);

    List<CompanyBankV2> overwriteBank(OperationModel operationModel, List<CompanyBankReq> companyBankReqList, String companyNo, String manageOrgNo);

    CompanyV2 findByEnterpriseNoAndCompanyNo(String enterpriseNo, String companyNo);

    List<CompanyV2> findByEnterpriseNoAndCompanyNoList(String enterpriseNo, List<String> companyNoList);


    List<CompanyShippingAddressResponse> getLinkAddressListBySourceNoList(String enterpriseNo, String useOrgNo, LinkmanTypeEnum typeEnum, List<String> sourceList);

    List<CompanyLinkmanResponse> getLinkmanListBySourceNoList(String enterpriseNo, String useOrgNo, LinkmanTypeEnum typeEnum, List<String> sourceList);

    List<CompanyBankResponse> convert2BankResponse(Map<String, String> currencyMap, List<CompanyBankV2> companyBankV2s);


    List<Company2InfoResponse> findCompanyList(CompanyQueryListPageReq pageReq);

    String generateCompanyNo();

    /**
     * 通过组织查询企业相关信息
     *
     * @param params
     * @return
     */
    List<CompanyBasicResponse> findCompanyBasicInfoByOrgList(CompanyAssociateOrgReq params);

    List<CompanyBasicResponse> findCompanyBasicInfo(CompanyCertQueryReq params);

    TenantCompanySaveModel saveTenantCompany(OperationModel operationModel, CompanyUpdateReq companyUpdateReq);

    List<CompanyAssociatedOrgResponse> findCompanyAssociatedOrgList(CompanyAssociateOrgReq associateOrgReq);
}
