package com.yyigou.dsrp.cdc.service.customer;

import com.yyigou.dsrp.cdc.api.company.vo.CompanyLinkmanVO;
import com.yyigou.dsrp.cdc.api.customer.dto.CustomerLinkmanByOrgDTO;
import com.yyigou.dsrp.cdc.common.util.OperationModel;

import java.util.List;

public interface CustomerLinkManService {
    /**
     * 跨组织获取客户联系人列表
     *
     * @param operationModel 操作人信息
     * @param supplierCode
     * @param orgNo
     * @return
     */
    List<CompanyLinkmanVO> getCustomerLinkmanListByOrg(OperationModel operationModel, String supplierCode, String orgNo);

    /**
     * 跨组织保存客户联系人列表
     *
     * @param params
     * @return
     */
    CompanyLinkmanVO saveCustomerLinkmanByOrg(OperationModel operationModel, CustomerLinkmanByOrgDTO params);

    /**
     * 跨组织删除联系人
     *
     * @param params
     * @param
     * @return
     */
    CompanyLinkmanVO editCustomerLinkmanByOrg(OperationModel operationModel, CustomerLinkmanByOrgDTO params);

    /**
     * 跨组织删除联系人
     *
     * @param linkmanId
     * @param
     * @return
     */
    Boolean deleteCustomerLinkmanByOrg(OperationModel operationModel, Long linkmanId);
}
