package com.yyigou.dsrp.cdc.service.v2.supplier.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yyigou.ddc.common.util.ValidatorUtils;
import com.yyigou.ddc.services.ddc.uim.vo.OrganizationTreeVo;
import com.yyigou.ddc.services.dlog.dto.IntegrationLogDTO;
import com.yyigou.ddc.services.dlog.enums.integration.IntegrationError;
import com.yyigou.ddc.services.dlog.enums.integration.IntegrationRequestType;
import com.yyigou.ddc.services.dlog.enums.integration.MdmBillType;
import com.yyigou.ddc.services.dlog.vo.IntegrationLogSaveVO;
import com.yyigou.ddc.services.dsrp.bdc.vo.PaymentAgreementVo;
import com.yyigou.dsrp.cdc.api.company.dto.BankDTO;
import com.yyigou.dsrp.cdc.api.company.dto.CompanyFileDTO;
import com.yyigou.dsrp.cdc.api.company.dto.CompanyLinkmanDTO;
import com.yyigou.dsrp.cdc.api.company.dto.CompanyShippingAddressDTO;
import com.yyigou.dsrp.cdc.api.supply.dto.SupplierExternalAssignItemDTO;
import com.yyigou.dsrp.cdc.api.supply.dto.SupplierExternalSaveDTO;
import com.yyigou.dsrp.cdc.api.supply.dto.SupplierSalesManDTO;
import com.yyigou.dsrp.cdc.api.supply.vo.SupplierExternalSaveVO;
import com.yyigou.dsrp.cdc.api.supply.vo.SupplierInternalSaveVO;
import com.yyigou.dsrp.cdc.client.v2.supplier.request.SupplierAssignDocExeRequest;
import com.yyigou.dsrp.cdc.client.v2.supplier.request.SupplierAssignExeRequest;
import com.yyigou.dsrp.cdc.common.enums.*;
import com.yyigou.dsrp.cdc.common.enums.company.*;
import com.yyigou.dsrp.cdc.common.enums.supplier.SupplierBusinessFlagEnum;
import com.yyigou.dsrp.cdc.common.util.BeanUtil;
import com.yyigou.dsrp.cdc.common.util.CommonUtil;
import com.yyigou.dsrp.cdc.common.util.OperationModel;
import com.yyigou.dsrp.cdc.dao.common.entity.BankType;
import com.yyigou.dsrp.cdc.dao.v2.company.CompanyLinkmanV2DAO;
import com.yyigou.dsrp.cdc.dao.v2.company.CompanyShippingAddressV2DAO;
import com.yyigou.dsrp.cdc.dao.v2.company.entity.CompanyLinkmanV2;
import com.yyigou.dsrp.cdc.dao.v2.company.entity.CompanyShippingAddressV2;
import com.yyigou.dsrp.cdc.dao.v2.company.entity.CompanyV2;
import com.yyigou.dsrp.cdc.dao.v2.supplier.*;
import com.yyigou.dsrp.cdc.dao.v2.supplier.entity.*;
import com.yyigou.dsrp.cdc.manager.integration.cert.CertService;
import com.yyigou.dsrp.cdc.manager.integration.cert.req.CompanyCertBasicRequest;
import com.yyigou.dsrp.cdc.manager.integration.cert.req.CompanyCertFileRequest;
import com.yyigou.dsrp.cdc.manager.integration.cert.req.CompanyCertUpsertRequest;
import com.yyigou.dsrp.cdc.manager.integration.dict.DictEnterpriseService;
import com.yyigou.dsrp.cdc.manager.integration.numberCenter.NumberCenterService;
import com.yyigou.dsrp.cdc.manager.integration.org.UimTenantService;
import com.yyigou.dsrp.cdc.manager.integration.paymentAgreement.PaymentAgreementService;
import com.yyigou.dsrp.cdc.manager.integration.uim.CustomDocService;
import com.yyigou.dsrp.cdc.manager.integration.uim.response.CustomDocResponse;
import com.yyigou.dsrp.cdc.manager.integration.ulog.ULogService;
import com.yyigou.dsrp.cdc.model.constant.NumberCenterConstant;
import com.yyigou.dsrp.cdc.model.constant.SystemConstant;
import com.yyigou.dsrp.cdc.model.v2.company.req.CompanyBankReq;
import com.yyigou.dsrp.cdc.model.v2.company.req.CompanyLinkmanReq;
import com.yyigou.dsrp.cdc.model.v2.company.req.CompanySaveOrUpdateReq;
import com.yyigou.dsrp.cdc.model.v2.company.req.CompanyShippingAddressReq;
import com.yyigou.dsrp.cdc.model.v2.supplier.req.SupplierAddressListEditReq;
import com.yyigou.dsrp.cdc.model.v2.supplier.req.SupplierLinkmanListEditReq;
import com.yyigou.dsrp.cdc.model.v2.supplier.req.SupplierOrderManReq;
import com.yyigou.dsrp.cdc.model.v2.supplier.req.SupplierOrdermanListEditReq;
import com.yyigou.dsrp.cdc.service.v2.MdmGray;
import com.yyigou.dsrp.cdc.service.v2.company.CompanyV2ExternalService;
import com.yyigou.dsrp.cdc.service.v2.company.CompanyV2Service;
import com.yyigou.dsrp.cdc.service.v2.supplier.SupplierCategoryV2Service;
import com.yyigou.dsrp.cdc.service.v2.supplier.SupplierV2ExternalService;
import com.yyigou.dsrp.cdc.service.v2.supplier.SupplierV2Service;
import com.yyigou.dsrp.cert.common.enums.CompanyCertSourceTypeEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service("supplierV2ExternalService")
@RequiredArgsConstructor
@Slf4j
public class SupplierV2ExternalServiceImpl implements SupplierV2ExternalService {
    private final NumberCenterService numberCenterService;

    @Resource
    private SupplierV2DAO supplierV2DAO;

    @Resource
    private SupplierBizDAO supplierBizDAO;

    @Resource
    private SupplierBaseDAO supplierBaseDAO;

    @Resource
    private SupplierCategoryV2Service supplierCategoryV2Service;

    @Resource
    private SupplierCategoryBaseDAO supplierCategoryBaseDAO;

    @Resource
    private CompanyV2ExternalService companyV2ExternalService;

    @Resource
    private CustomDocService customDocService;

    @Resource
    private DictEnterpriseService dictEnterpriseService;

    @Resource
    private CompanyLinkmanV2DAO companyLinkmanV2DAO;

    @Resource
    private CompanyShippingAddressV2DAO companyShippingAddressV2DAO;

    @Resource
    private SupplierOrderManV2DAO supplierOrderManV2DAO;

    @Resource
    private ULogService uLogService;

    @Resource
    private UimTenantService uimTenantService;

    @Resource
    private MdmGray mdmGray;

    @Resource
    private CompanyV2Service companyV2Service;

    @Resource
    private SupplierV2Service supplierV2Service;

    @Resource
    private CertService certService;

    @Resource
    private PaymentAgreementService paymentAgreementService;


    private Map<String, List<?>> cudAddressPerSupplierForDA(OperationModel operationModel, List<CompanyShippingAddressV2> oldList, List<CompanyShippingAddressDTO> newList, String useOrgNo, SupplierV2 futureSupplier) {
        Map<String, List<?>> diffResultMap = BeanUtil.diffCUD(oldList, newList);

        List<CompanyShippingAddressDTO> addList = (List<CompanyShippingAddressDTO>) diffResultMap.get("addList");
        List<CompanyShippingAddressDTO> updateList = (List<CompanyShippingAddressDTO>) diffResultMap.get("updateList");
        List<CompanyShippingAddressV2> deleteList = (List<CompanyShippingAddressV2>) diffResultMap.get("deleteList");

        if (CollectionUtils.isNotEmpty(addList)) {
            List<CompanyShippingAddressV2> addCompanyShippingAddressList = new ArrayList<>();
            for (CompanyShippingAddressDTO companyLinkmanReq : addList) {
                CompanyShippingAddressV2 shippingAddress = new CompanyShippingAddressV2();
                shippingAddress.setEnterpriseNo(operationModel.getEnterpriseNo());
                shippingAddress.setUseOrgNo(useOrgNo);
                shippingAddress.setCompanyNo(futureSupplier.getCompanyNo());
                shippingAddress.setLinkAddressCode(companyLinkmanReq.getLinkAddressCode());
                shippingAddress.setSourceNo(futureSupplier.getSupplierCode());
                shippingAddress.setStatus(CommonStatusEnum.EFFECTIVE.getValue());
                shippingAddress.setLinkaddType(LinkmanTypeEnum.SUPPLY.getValue());
                shippingAddress.setReceiveUser(companyLinkmanReq.getReceiveUser());
                shippingAddress.setReceivePhone(companyLinkmanReq.getReceivePhone());
                shippingAddress.setRegionCode(companyLinkmanReq.getRegionCode());
                shippingAddress.setRegionName(companyLinkmanReq.getRegionName());
                shippingAddress.setReceiveAddr(companyLinkmanReq.getReceiveAddr());
                shippingAddress.setIsDefault(companyLinkmanReq.getIsDefault());
                shippingAddress.setAddressDesc(companyLinkmanReq.getAddressDesc());
                shippingAddress.setAddressType(companyLinkmanReq.getAddressType());
                CommonUtil.fillCreatInfo(operationModel, shippingAddress);

                addCompanyShippingAddressList.add(shippingAddress);
            }
            companyShippingAddressV2DAO.addBatch(addCompanyShippingAddressList);
        }

        if (CollectionUtils.isNotEmpty(updateList)) {
            List<CompanyShippingAddressV2> addCompanyShippingAddressList = new ArrayList<>();
            for (CompanyShippingAddressDTO companyLinkmanReq : updateList) {
                CompanyShippingAddressV2 shippingAddress = new CompanyShippingAddressV2();
                shippingAddress.setId(companyLinkmanReq.getId());
                shippingAddress.setEnterpriseNo(operationModel.getEnterpriseNo());
                shippingAddress.setUseOrgNo(useOrgNo);
                shippingAddress.setCompanyNo(futureSupplier.getCompanyNo());
                shippingAddress.setLinkAddressCode(companyLinkmanReq.getLinkAddressCode());
                shippingAddress.setSourceNo(futureSupplier.getSupplierCode());
                shippingAddress.setStatus(CommonStatusEnum.EFFECTIVE.getValue());
                shippingAddress.setLinkaddType(LinkmanTypeEnum.SUPPLY.getValue());
                shippingAddress.setReceiveUser(companyLinkmanReq.getReceiveUser());
                shippingAddress.setReceivePhone(companyLinkmanReq.getReceivePhone());
                shippingAddress.setRegionCode(companyLinkmanReq.getRegionCode());
                shippingAddress.setRegionName(companyLinkmanReq.getRegionName());
                shippingAddress.setReceiveAddr(companyLinkmanReq.getReceiveAddr());
                shippingAddress.setIsDefault(companyLinkmanReq.getIsDefault());
                shippingAddress.setAddressDesc(companyLinkmanReq.getAddressDesc());
                shippingAddress.setAddressType(companyLinkmanReq.getAddressType());
                CommonUtil.fillOperateInfo(operationModel, shippingAddress);

                addCompanyShippingAddressList.add(shippingAddress);
            }
            companyShippingAddressV2DAO.updateByIdBatch(addCompanyShippingAddressList);
        }

        if (CollectionUtils.isNotEmpty(deleteList)) {
            CompanyShippingAddressV2 companyShippingAddressV2 = new CompanyShippingAddressV2();
            companyShippingAddressV2.setDeleted(DeletedEnum.DELETED.getValue());
            CommonUtil.fillOperateInfo(operationModel, companyShippingAddressV2);

            companyShippingAddressV2DAO.update(companyShippingAddressV2, Wrappers.<CompanyShippingAddressV2>lambdaQuery()
                    .eq(CompanyShippingAddressV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                    .in(CompanyShippingAddressV2::getId, deleteList.stream().map(CompanyShippingAddressV2::getId).collect(Collectors.toList())));
        }

        return diffResultMap;
    }

    private Map<String, List<?>> cudOrdermanPerSupplierForDA(OperationModel operationModel, List<SupplierOrderManV2> oldList, List<SupplierSalesManDTO> newList, String useOrgNo, SupplierV2 futureSupplier) {
        Map<String, List<?>> diffResultMap = BeanUtil.diffCUD(oldList, newList);

        List<SupplierSalesManDTO> addList = (List<SupplierSalesManDTO>) diffResultMap.get("addList");
        List<SupplierSalesManDTO> updateList = (List<SupplierSalesManDTO>) diffResultMap.get("updateList");
        List<SupplierOrderManV2> deleteList = (List<SupplierOrderManV2>) diffResultMap.get("deleteList");

        if (CollectionUtils.isNotEmpty(addList)) {
            List<SupplierOrderManV2> addOrderManList = new ArrayList<>();

            for (SupplierSalesManDTO supplierOrderManReq : addList) {
                SupplierOrderManV2 supplierOrderMan = new SupplierOrderManV2();
                supplierOrderMan.setEnterpriseNo(operationModel.getEnterpriseNo());
                supplierOrderMan.setUseOrgNo(useOrgNo);
//                supplierOrderMan.setSupplierNo();
                supplierOrderMan.setSupplierCode(futureSupplier.getSupplierCode());
                supplierOrderMan.setManCode(supplierOrderManReq.getManCode());
                supplierOrderMan.setOrderManNo(supplierOrderManReq.getOrderManNo());
                supplierOrderMan.setOrderManName(supplierOrderManReq.getOrderManName());
                supplierOrderMan.setDeptNo(supplierOrderManReq.getDeptNo());
                supplierOrderMan.setDeptName(supplierOrderManReq.getDeptName());
                supplierOrderMan.setPost(supplierOrderManReq.getPost());
                supplierOrderMan.setIsDefault(supplierOrderManReq.getIsDefault());
                supplierOrderMan.setOrderSpecialist(supplierOrderManReq.getOrderSpecialist());
                supplierOrderMan.setPost(supplierOrderManReq.getPost());
                CommonUtil.fillCreatInfo(operationModel, supplierOrderMan);

                addOrderManList.add(supplierOrderMan);
            }
            supplierOrderManV2DAO.addBatch(addOrderManList);
        }

        if (CollectionUtils.isNotEmpty(updateList)) {
            List<SupplierOrderManV2> updateOrderManList = new ArrayList<>();
            for (SupplierSalesManDTO supplierOrderManReq : updateList) {
                SupplierOrderManV2 supplierOrderMan = new SupplierOrderManV2();
                supplierOrderMan.setId(supplierOrderManReq.getId());
                supplierOrderMan.setEnterpriseNo(operationModel.getEnterpriseNo());
                supplierOrderMan.setUseOrgNo(useOrgNo);
//                supplierOrderMan.setSupplierNo();
                supplierOrderMan.setSupplierCode(futureSupplier.getSupplierCode());
                supplierOrderMan.setManCode(supplierOrderManReq.getManCode());
                supplierOrderMan.setOrderManNo(supplierOrderManReq.getOrderManNo());
                supplierOrderMan.setOrderManName(supplierOrderManReq.getOrderManName());
                supplierOrderMan.setDeptNo(supplierOrderManReq.getDeptNo());
                supplierOrderMan.setDeptName(supplierOrderManReq.getDeptName());
                supplierOrderMan.setPost(supplierOrderManReq.getPost());
                supplierOrderMan.setIsDefault(supplierOrderManReq.getIsDefault());
                supplierOrderMan.setOrderSpecialist(supplierOrderManReq.getOrderSpecialist());
                supplierOrderMan.setPost(supplierOrderManReq.getPost());
                CommonUtil.fillOperateInfo(operationModel, supplierOrderMan);

                updateOrderManList.add(supplierOrderMan);
            }
            supplierOrderManV2DAO.updateByIdBatch(updateOrderManList);
        }

        if (CollectionUtils.isNotEmpty(deleteList)) {
            SupplierOrderManV2 supplierOrderMan = new SupplierOrderManV2();
            supplierOrderMan.setDeleted(DeletedEnum.DELETED.getValue());
            CommonUtil.fillOperateInfo(operationModel, supplierOrderMan);

            supplierOrderManV2DAO.update(supplierOrderMan, Wrappers.<SupplierOrderManV2>lambdaQuery()
                    .eq(SupplierOrderManV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                    .in(SupplierOrderManV2::getId, deleteList.stream().map(SupplierOrderManV2::getId).collect(Collectors.toList())));
        }

        return diffResultMap;
    }

    private void batchSaveValidateAndPreProcessForDA(OperationModel operationModelIn, List<SupplierExternalSaveDTO> paramsIn, Map<String, SupplierV2> localSupplierMapIn, List<SupplierInternalSaveVO> invalidatedListOut, Map<String, String> cooperationModeMapOut) {
        paramsIn.removeIf(supplierExternalSaveDTO -> {
            //set default
            if (supplierExternalSaveDTO.getIsMedicalInstitution() == null) {
                supplierExternalSaveDTO.setIsMedicalInstitution(CommonIfEnum.NO.getValue());
            }
            //默认非关联企业
            if (supplierExternalSaveDTO.getIsAssociatedEnterprise() == null) {
                supplierExternalSaveDTO.setIsAssociatedEnterprise(CommonIfEnum.NO.getValue());
            }

            if (StringUtils.isEmpty(supplierExternalSaveDTO.getSupplierCode())) {
                invalidatedListOut.add(new SupplierInternalSaveVO(supplierExternalSaveDTO.getSupplierCode(), supplierExternalSaveDTO.getSupplierName(), false,
                        "供应商编码不能为空", operationModelIn, IntegrationError.PARAMS_EMPTY_ERR));
                return true;
            }

            if (StringUtils.isEmpty(supplierExternalSaveDTO.getControlStatus())) {
                invalidatedListOut.add(new SupplierInternalSaveVO(supplierExternalSaveDTO.getSupplierCode(), supplierExternalSaveDTO.getSupplierName(), false,
                        "供应商生效状态不能为空", operationModelIn, IntegrationError.PARAMS_EMPTY_ERR));
                return true;
            }

            EnableEnum controlStatusEnum = null;
            try {
                int controlStatus = Integer.parseInt(supplierExternalSaveDTO.getControlStatus());
                controlStatusEnum = EnableEnum.getByValue(controlStatus);
                if (null == controlStatusEnum) {
                    invalidatedListOut.add(new SupplierInternalSaveVO(supplierExternalSaveDTO.getSupplierCode(), supplierExternalSaveDTO.getSupplierName(), false,
                            "供应商状态不正确", operationModelIn, IntegrationError.PARAMS_EMPTY_ERR));
                    return true;
                }
            } catch (NumberFormatException e) {
                invalidatedListOut.add(new SupplierInternalSaveVO(supplierExternalSaveDTO.getSupplierCode(), supplierExternalSaveDTO.getSupplierName(), false,
                        "供应商状态不正确", operationModelIn, IntegrationError.PARAMS_EMPTY_ERR));
                return true;
            }


            // 如果禁用的档案不存在则报错
            if (controlStatusEnum == EnableEnum.DISABLE) {
                if (null == localSupplierMapIn.get(supplierExternalSaveDTO.getSupplierCode())) {
                    invalidatedListOut.add(new SupplierInternalSaveVO(supplierExternalSaveDTO.getSupplierCode(), supplierExternalSaveDTO.getSupplierName(), false,
                            "禁用的供应商编码不存在", operationModelIn, IntegrationError.PARAMS_EMPTY_ERR));
                    return true;
                }
            }

            if (StringUtils.isEmpty(supplierExternalSaveDTO.getSupplierName())) {
                invalidatedListOut.add(new SupplierInternalSaveVO(supplierExternalSaveDTO.getSupplierCode(), supplierExternalSaveDTO.getSupplierName(), false, "供应商名称不能为空", operationModelIn, IntegrationError.PARAMS_EMPTY_ERR));
                return true;
            }

            if (StringUtils.isEmpty(supplierExternalSaveDTO.getUnifiedSocialCode())) {
                invalidatedListOut.add(new SupplierInternalSaveVO(supplierExternalSaveDTO.getSupplierCode(), supplierExternalSaveDTO.getSupplierName(), false, "统一社会信用代码不能为空", operationModelIn, IntegrationError.PARAMS_EMPTY_ERR));
                return true;
            }
            if (StringUtils.isEmpty(supplierExternalSaveDTO.getCompanyName())) {
                invalidatedListOut.add(new SupplierInternalSaveVO(supplierExternalSaveDTO.getSupplierCode(), supplierExternalSaveDTO.getSupplierName(), false, "企业名称不能为空", operationModelIn, IntegrationError.PARAMS_EMPTY_ERR));
                return true;
            }
            if (StringUtils.isEmpty(supplierExternalSaveDTO.getSupplierCategoryCode_1())) {
                invalidatedListOut.add(new SupplierInternalSaveVO(supplierExternalSaveDTO.getSupplierCode(), supplierExternalSaveDTO.getSupplierName(), false, "供应商分类不能为空", operationModelIn, IntegrationError.PARAMS_EMPTY_ERR));
                return true;
            }

            if (null == supplierExternalSaveDTO.getIsMedicalInstitution() ||
                    null == CommonIfEnum.getByValue(supplierExternalSaveDTO.getIsMedicalInstitution())) {
                invalidatedListOut.add(new SupplierInternalSaveVO(supplierExternalSaveDTO.getSupplierCode(), supplierExternalSaveDTO.getSupplierName(), false, "是否医疗机构不合法", operationModelIn, IntegrationError.PARAMS_EMPTY_ERR));
                return true;
            }

            if (CommonIfEnum.YES.getValue().equals(supplierExternalSaveDTO.getIsMedicalInstitution())) {
                if (StringUtils.isEmpty(supplierExternalSaveDTO.getInstitutionalType())) {
                    invalidatedListOut.add(new SupplierInternalSaveVO(supplierExternalSaveDTO.getSupplierCode(), supplierExternalSaveDTO.getSupplierName(), false,
                            "医疗机构类型不能为空", operationModelIn, IntegrationError.PARAMS_EMPTY_ERR));
                    return true;
                } else if (null == InstitutionalTypeEnum.getByType(supplierExternalSaveDTO.getInstitutionalType())) {
                    invalidatedListOut.add(new SupplierInternalSaveVO(supplierExternalSaveDTO.getSupplierCode(), supplierExternalSaveDTO.getSupplierName(), false,
                            "医疗机构类型不合法", operationModelIn, IntegrationError.PARAMS_INVALID_ERR));
                    return true;
                }
                if (InstitutionalTypeEnum.yy.getType().equals(supplierExternalSaveDTO.getInstitutionalType())) {
                    if (StringUtils.isEmpty(supplierExternalSaveDTO.getHospitalType())) {
                        invalidatedListOut.add(new SupplierInternalSaveVO(supplierExternalSaveDTO.getSupplierCode(), supplierExternalSaveDTO.getSupplierName(), false,
                                "医疗机构性质不能为空", operationModelIn, IntegrationError.PARAMS_EMPTY_ERR));
                        return true;
                    } else if (null == HospitalTypeEnum.getByType(supplierExternalSaveDTO.getHospitalType())) {
                        invalidatedListOut.add(new SupplierInternalSaveVO(supplierExternalSaveDTO.getSupplierCode(), supplierExternalSaveDTO.getSupplierName(), false,
                                "医疗机构性质不合法", operationModelIn, IntegrationError.PARAMS_INVALID_ERR));
                        return true;
                    }
                    if (null == supplierExternalSaveDTO.getHospitalClass()) {
                        invalidatedListOut.add(new SupplierInternalSaveVO(supplierExternalSaveDTO.getSupplierCode(), supplierExternalSaveDTO.getSupplierName(), false,
                                "医疗机构等级不能为空", operationModelIn, IntegrationError.PARAMS_EMPTY_ERR));
                        return true;
                    } else if (null == HospitalClassEnum.getByType(supplierExternalSaveDTO.getHospitalClass())) {
                        invalidatedListOut.add(new SupplierInternalSaveVO(supplierExternalSaveDTO.getSupplierCode(), supplierExternalSaveDTO.getSupplierName(), false,
                                "医疗机构等级不合法", operationModelIn, IntegrationError.PARAMS_INVALID_ERR));
                        return true;
                    }
                }
            }

            if (CommonIfEnum.YES.getValue().equals(supplierExternalSaveDTO.getIsAssociatedEnterprise())) {
                if (StringUtils.isEmpty(supplierExternalSaveDTO.getAssociatedOrgCode())) {
                    invalidatedListOut.add(new SupplierInternalSaveVO(supplierExternalSaveDTO.getSupplierCode(), supplierExternalSaveDTO.getSupplierName(), false,
                            "关联企业编码不能为空", operationModelIn, IntegrationError.PARAMS_EMPTY_ERR));
                    return true;
                }
//                if (!organizationMap.containsKey(t.getAssociatedOrgCode())) {
//                    resultList.add(new SupplierExternalSaveVO(t.getSupplierCode(), false, "关联组织" + t.getAssociatedOrgCode() + "在DSRP中不存在"));
//                    return Boolean.TRUE;
//                }
            }

            if (StringUtils.isNotBlank(supplierExternalSaveDTO.getCooperationMode()) && StringUtils.isNotBlank(supplierExternalSaveDTO.getCooperationModeName())) {
                if (cooperationModeMapOut.containsKey(supplierExternalSaveDTO.getCooperationMode())) {
                    if (!cooperationModeMapOut.get(supplierExternalSaveDTO.getCooperationMode()).equals(supplierExternalSaveDTO.getCooperationModeName())) {
                        invalidatedListOut.add(new SupplierInternalSaveVO(supplierExternalSaveDTO.getSupplierCode(), supplierExternalSaveDTO.getSupplierName(), false,
                                "供应商性质编码和名称不统一", operationModelIn, IntegrationError.PARAMS_INVALID_ERR));
                        return true;
                    }
                } else {
                    cooperationModeMapOut.put(supplierExternalSaveDTO.getCooperationMode(), supplierExternalSaveDTO.getCooperationModeName());
                }
            }


            return false;
        });
    }

    @Override
    public List<SupplierExternalSaveVO> batchSaveForDA(OperationModel operationModel, List<SupplierExternalSaveDTO> params) {
        // 提前查询供应商档案是否存在，因为后续需要校验禁用的档案在系统中是否存在
        Map<String, SupplierV2> localSupplierMap = new HashMap<>();
        List<String> supplierCodeList = params.stream().map(SupplierExternalSaveDTO::getSupplierCode).filter(supplierCode -> !StringUtils.isEmpty(supplierCode)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(supplierCodeList)) {
            List<SupplierV2> localSupplierList = supplierV2DAO.selectList(Wrappers.<SupplierV2>lambdaQuery()
                    .eq(SupplierV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                    .in(SupplierV2::getSupplierCode, supplierCodeList)
                    .eq(SupplierV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
            localSupplierMap.putAll(localSupplierList.stream().collect(Collectors.toMap(SupplierV2::getSupplierCode, Function.identity())));
        }

        // 合作性质
        Map<String, String> cooperationModeMap = new HashMap<>();

        // 非法入参的记录
        List<SupplierInternalSaveVO> invalidatedList = new ArrayList<>();

        // 参数合法性校验和参数预处理，过滤不合法的记录
        batchSaveValidateAndPreProcessForDA(operationModel, params, localSupplierMap, invalidatedList, cooperationModeMap);

        if (CollectionUtils.isEmpty(params)) {
            batchSaveBusinessLogForDA(invalidatedList);

            return convert2ExternalVoForDA(invalidatedList, null);
        }

        // 开始处理合法的记录
        Set<String> validSupplierCodeSet = params.stream().map(SupplierExternalSaveDTO::getSupplierCode).collect(Collectors.toSet());

        // 只保留合法的记录
        localSupplierMap.entrySet().removeIf(
                entry -> !validSupplierCodeSet.contains(entry.getKey()));

        // 处理企业档案和银行信息
        Map<String, List<CompanyV2>> companySaveOrUpdateListMap = companyV2ExternalService.saveOrUpdateCompanyBySupplierForDA(operationModel, params);
        List<CompanyV2> addCompanyList = companySaveOrUpdateListMap.get("addList");
        List<CompanyV2> updateCompanyList = companySaveOrUpdateListMap.get("updateList");
        Map<String, CompanyV2> companyNameMap = new HashMap<>(addCompanyList.size() + updateCompanyList.size());
        companyNameMap.putAll(addCompanyList.stream().collect(Collectors.toMap(CompanyV2::getCompanyName, Function.identity())));
        companyNameMap.putAll(updateCompanyList.stream().collect(Collectors.toMap(CompanyV2::getCompanyName, Function.identity())));

        // 处理供应商分类
        supplierCategoryV2Service.createAndUpdateCategoryForDA(operationModel, params);

        // 处理合作性质
        if (MapUtils.isNotEmpty(cooperationModeMap)) {
            customDocService.saveAndUpdate(operationModel.getEnterpriseNo(), SystemConstant.SUPPLIER_COOPERATION_NUMBER, cooperationModeMap, operationModel.getEmployerNo(), operationModel.getUserName());
        }

        // 处理供应商档案、业务信息、资质证照、负责人、联系人、联系地址，分派记录
        final List<SupplierInternalSaveVO> handledList = saveOrUpdateSupplierV2ForDA(operationModel, companyNameMap, localSupplierMap, params);

        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                try {
                    batchSaveBusinessLogForDA(handledList);
                } catch (Exception e) {
                    log.error("供应商档案批量更新日志保存失败", e);
                }
            }
        });

        return convert2ExternalVoForDA(invalidatedList, handledList);
    }

    public void handSupplierCertListMdmCompatible(OperationModel operationModel, SupplierExternalSaveDTO supplierExternalSaveDTO, SupplierV2 supplierV2) {
        String manageOrgNo = operationModel.getOrgNo();
        String companyNo = supplierV2.getCompanyNo();
        String supplierCode = supplierV2.getSupplierCode();

        CompanyCertUpsertRequest companyCertUpsertRequest = new CompanyCertUpsertRequest();
        companyCertUpsertRequest.setEnterpriseNo(operationModel.getEnterpriseNo());
        companyCertUpsertRequest.setOrgNo(manageOrgNo);
        companyCertUpsertRequest.setCompanyNo(companyNo);
        companyCertUpsertRequest.setSourceDocType(CompanyCertSourceTypeEnum.SUPPLIER.getCode());
        companyCertUpsertRequest.setSourceDocCode(supplierCode);

        List<CompanyCertBasicRequest> companyCertList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(supplierExternalSaveDTO.getCertList())) {
            for (CompanyFileDTO companyFileDTO : supplierExternalSaveDTO.getCertList()) {
                CompanyCertBasicRequest companyCertBasicRequest = new CompanyCertBasicRequest();
                companyCertBasicRequest.setCertTypeCode(companyFileDTO.getCertTypeCode());
                companyCertBasicRequest.setCertCode(companyFileDTO.getCertCode());
                companyCertBasicRequest.setCertName(companyFileDTO.getCertName());
                companyCertBasicRequest.setStartTime(companyFileDTO.getStartTime());
                companyCertBasicRequest.setEndTime(companyFileDTO.getEndTime());
                companyCertBasicRequest.setLongTerm(companyFileDTO.getLongTerm());
//                    companyCertBasicRequest.setIssuingAuthority();
                companyCertBasicRequest.setRemark(companyFileDTO.getRemark());
                List<CompanyCertFileRequest> companyCertFileRequestList = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(companyFileDTO.getBaseFileList())) {
                    for (String file : companyFileDTO.getBaseFileList()) {
                        CompanyCertFileRequest companyCertFileRequest = new CompanyCertFileRequest();

                        companyCertFileRequest.setFilePath(file);
                        companyCertFileRequest.setFileName(file);

                        companyCertFileRequestList.add(companyCertFileRequest);
                    }
                }
                companyCertBasicRequest.setFileList(companyCertFileRequestList);
//                    companyCertBasicRequest.setBusinessScopeList();
            }
        }
        companyCertUpsertRequest.setCompanyCertList(companyCertList);
        certService.upsert(companyCertUpsertRequest);
    }

    public void handSupplierOrderManListMdmCompatible(OperationModel operationModel, SupplierExternalSaveDTO supplierExternalSaveDTO, SupplierV2 supplierV2) {
        String manageOrgNo = operationModel.getOrgNo();
        String supplierCode = supplierV2.getSupplierCode();

        SupplierOrdermanListEditReq supplierOrdermanListEditReq = new SupplierOrdermanListEditReq();
        supplierOrdermanListEditReq.setEnterpriseNo(operationModel.getEnterpriseNo());
        supplierOrdermanListEditReq.setUseOrgNo(manageOrgNo);
        supplierOrdermanListEditReq.setSupplierCode(supplierCode);
        List<SupplierOrderManReq> orderManList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(supplierExternalSaveDTO.getResponsibleManList())) {
            for (SupplierSalesManDTO supplierSalesManDTO : supplierExternalSaveDTO.getResponsibleManList()) {
                SupplierOrderManReq orderManReq = new SupplierOrderManReq();
                orderManReq.setDeptNo(supplierSalesManDTO.getDeptNo());
                orderManReq.setDeptName(supplierSalesManDTO.getDeptName());
                orderManReq.setOrderManNo(supplierSalesManDTO.getOrderManNo());
                orderManReq.setOrderManName(supplierSalesManDTO.getOrderManName());
                orderManReq.setPost(supplierSalesManDTO.getPost());
                orderManReq.setOrderSpecialist(supplierSalesManDTO.getOrderSpecialist());
                orderManReq.setIsDefault(supplierSalesManDTO.getIsDefault());
                orderManReq.setOrderSpecialistName(null != supplierSalesManDTO.getOrderSpecialist() ? CommonIfEnum.getNameByValue(supplierSalesManDTO.getOrderSpecialist()) : null);
                orderManReq.setIsDefaultName(null != supplierSalesManDTO.getIsDefault() ? CommonIfEnum.getNameByValue(supplierSalesManDTO.getIsDefault()) : null);

                orderManList.add(orderManReq);
            }
        }

        supplierOrdermanListEditReq.setSupplierManList(orderManList);
        supplierV2Service.overwriteSupplierOrdermanList(operationModel, supplierOrdermanListEditReq);
    }

    public void handSupplierAddressListMdmCompatible(OperationModel operationModel, SupplierExternalSaveDTO supplierExternalSaveDTO, SupplierV2 supplierV2, Map<String, String> countryRegionMap) {
        String manageOrgNo = operationModel.getOrgNo();
        String supplierCode = supplierV2.getSupplierCode();

        SupplierAddressListEditReq supplierAddressListEditReq = new SupplierAddressListEditReq();
        supplierAddressListEditReq.setEnterpriseNo(operationModel.getEnterpriseNo());
        supplierAddressListEditReq.setUseOrgNo(manageOrgNo);
        supplierAddressListEditReq.setSupplierCode(supplierCode);
        if (CollectionUtils.isNotEmpty(supplierExternalSaveDTO.getLinkAddressList())) {
            List<CompanyShippingAddressReq> addressList = new ArrayList<>();

            for (CompanyShippingAddressDTO companyShippingAddressDTO : supplierExternalSaveDTO.getLinkAddressList()) {
                CompanyShippingAddressReq addressReq = new CompanyShippingAddressReq();
                addressReq.setLinkAddressCode(companyShippingAddressDTO.getLinkAddressCode());
                addressReq.setReceiveUser(companyShippingAddressDTO.getReceiveUser());
                addressReq.setReceivePhone(companyShippingAddressDTO.getReceivePhone());
                addressReq.setRegionCode(companyShippingAddressDTO.getRegionCode());
                addressReq.setRegionName(companyShippingAddressDTO.getRegionName());
                addressReq.setReceiveAddr(companyShippingAddressDTO.getReceiveAddr());
                addressReq.setIsDefault(companyShippingAddressDTO.getIsDefault());
                addressReq.setAddressType(companyShippingAddressDTO.getAddressType());
                addressReq.setAddressDesc(companyShippingAddressDTO.getAddressDesc());
                addressReq.setAddressTypeName(StringUtils.isNotEmpty(companyShippingAddressDTO.getAddressType()) ? AddressEnum.getByType(companyShippingAddressDTO.getAddressType()).getName() : null);
                addressReq.setRegionFullName(StringUtils.isNotEmpty(companyShippingAddressDTO.getRegionCode()) ? countryRegionMap.get(companyShippingAddressDTO.getRegionCode()) : null);
                addressReq.setIsDefaultName(null != companyShippingAddressDTO.getIsDefault() ? CommonIfEnum.getNameByValue(companyShippingAddressDTO.getIsDefault()) : null);

                addressList.add(addressReq);
            }

            supplierAddressListEditReq.setLinkAddressList(addressList);
        }
        supplierV2Service.overwriteSupplierAddressList(operationModel, supplierAddressListEditReq);
    }

    public void handSupplierLinkmanListMdmCompatible(OperationModel operationModel, SupplierExternalSaveDTO supplierExternalSaveDTO, SupplierV2 supplierV2) {
        String manageOrgNo = operationModel.getOrgNo();
        String supplierCode = supplierV2.getSupplierCode();

        SupplierLinkmanListEditReq supplierLinkmanListEditReq = new SupplierLinkmanListEditReq();
        supplierLinkmanListEditReq.setEnterpriseNo(operationModel.getEnterpriseNo());
        supplierLinkmanListEditReq.setUseOrgNo(manageOrgNo);
        supplierLinkmanListEditReq.setSupplierCode(supplierCode);
        if (CollectionUtils.isNotEmpty(supplierExternalSaveDTO.getLinkmanList())) {
            List<CompanyLinkmanReq> linkmanList = new ArrayList<>();

            for (CompanyLinkmanDTO companyLinkmanDTO : supplierExternalSaveDTO.getLinkmanList()) {
                CompanyLinkmanReq linkmanReq = new CompanyLinkmanReq();
                linkmanReq.setLinkman(companyLinkmanDTO.getLinkman());
                linkmanReq.setPosition(companyLinkmanDTO.getPosition());
                linkmanReq.setMobilePhone(companyLinkmanDTO.getMobilePhone());
                linkmanReq.setFixedPhone(companyLinkmanDTO.getFixedPhone());
                linkmanReq.setSex(companyLinkmanDTO.getSex());
                linkmanReq.setEmail(companyLinkmanDTO.getEmail());
                linkmanReq.setQq(companyLinkmanDTO.getQq());
                linkmanReq.setWx(companyLinkmanDTO.getWx());
                linkmanReq.setStatus(companyLinkmanDTO.getStatus());
                linkmanReq.setIsDefault(companyLinkmanDTO.getIsDefault());
                linkmanReq.setLinkCode(companyLinkmanDTO.getLinkCode());
                linkmanReq.setSexName(StringUtils.isNotEmpty(companyLinkmanDTO.getSex()) ? (null != SexEnum.getByType(companyLinkmanDTO.getSex()) ? SexEnum.getByType(companyLinkmanDTO.getSex()).getName() : null) : null);
                linkmanReq.setStatusName(null != companyLinkmanDTO.getStatus() ? CommonStatusEnum.getNameByValue(companyLinkmanDTO.getStatus()) : null);
                linkmanReq.setIsDefaultName(null != companyLinkmanDTO.getIsDefault() ? CommonIfEnum.getNameByValue(companyLinkmanDTO.getIsDefault()) : null);

                linkmanList.add(linkmanReq);

            }

            supplierLinkmanListEditReq.setLinkmanList(linkmanList);
        }
        supplierV2Service.overwriteSupplierLinkmanList(operationModel, supplierLinkmanListEditReq);
    }

    public void handSupplierCategoryMdmCompatible(OperationModel operationModel, SupplierExternalSaveDTO supplierExternalSaveDTO, SupplierV2 supplierV2) {
        String manageOrgNo = operationModel.getOrgNo();

        if (StringUtils.isNotEmpty(supplierV2.getSupplierCategoryNo())) {
            SupplierCategoryV2 supplierCategoryV2 = supplierCategoryV2Service.getByNo(operationModel.getEnterpriseNo(), supplierV2.getSupplierCategoryNo());
            ValidatorUtils.checkTrueThrowEx(null == supplierCategoryV2, "供应商分类不存在");

            boolean categoryBaseExists = supplierCategoryBaseDAO.exists(Wrappers.<SupplierCategoryBase>lambdaQuery()
                    .eq(SupplierCategoryBase::getEnterpriseNo, operationModel.getEnterpriseNo())
                    .eq(SupplierCategoryBase::getUseOrgNo, manageOrgNo)
                    .eq(SupplierCategoryBase::getCategoryNo, supplierV2.getSupplierCategoryNo())
                    .eq(SupplierCategoryBase::getDeleted, DeletedEnum.UN_DELETE.getValue()));
//            ValidatorUtils.checkTrueThrowEx(categoryBaseExists, "供应商分类分派信息已经存在");
            if (categoryBaseExists) {
                log.warn("供应商分类分派信息已经存在,supplierCategoryNo={},manageOrgNo={}", supplierV2.getSupplierCategoryNo(), manageOrgNo);
                return;
            }

            SupplierCategoryBase supplierCategoryBase = new SupplierCategoryBase();
            supplierCategoryBase.setEnterpriseNo(operationModel.getEnterpriseNo());
            supplierCategoryBase.setCategoryNo(supplierV2.getSupplierCategoryNo());
            supplierCategoryBase.setCategoryCode(supplierCategoryV2.getCategoryCode());
            supplierCategoryBase.setManageOrgNo(supplierCategoryV2.getManageOrgNo());
            supplierCategoryBase.setUseOrgNo(manageOrgNo);
            CommonUtil.fillCreatInfo(operationModel, supplierCategoryBase);
            CommonUtil.fillOperateInfo(operationModel, supplierCategoryBase);

            supplierCategoryBaseDAO.insert(supplierCategoryBase);
        }
    }

    public void handSupplierBizMdmCompatible(OperationModel operationModel, SupplierExternalSaveDTO supplierExternalSaveDTO, SupplierV2 supplierV2, Map<String, CustomDocResponse> paymentTermMap, Map<String, PaymentAgreementVo> paymentAgreementMap) {
        String manageOrgNo = operationModel.getOrgNo();
        String supplierCode = supplierExternalSaveDTO.getSupplierCode();

        boolean bizExists = supplierBizDAO.exists(Wrappers.<SupplierBiz>lambdaQuery()
                .eq(SupplierBiz::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(SupplierBiz::getUseOrgNo, manageOrgNo)
                .eq(SupplierBiz::getSupplierCode, supplierCode)
                .eq(SupplierBiz::getDeleted, DeletedEnum.UN_DELETE.getValue()));
//        ValidatorUtils.checkTrueThrowEx(bizExists, "业务信息已经存在");
        if (bizExists) {
            log.warn("业务信息已经存在,supplierCode={},manageOrgNo={}", supplierCode, manageOrgNo);
            return;
        }


        SupplierBiz supplierBiz = new SupplierBiz();
        supplierBiz.setSupplierNo(supplierV2.getSupplierNo());
        supplierBiz.setEnterpriseNo(supplierV2.getEnterpriseNo());
        supplierBiz.setUseOrgNo(manageOrgNo);
        supplierBiz.setUseOrgModifyFlag(CommonIfEnum.NO.getValue());
        supplierBiz.setCooperationMode(supplierExternalSaveDTO.getCooperationMode());
        supplierBiz.setOwnerCompany(supplierExternalSaveDTO.getOwnerCompany());

        supplierBiz.setSupplierCode(supplierV2.getSupplierCode());
        supplierBiz.setCreditAmount(supplierExternalSaveDTO.getCreditAmount());
        supplierBiz.setSettlementModes(supplierExternalSaveDTO.getSettlementModesCode());
        supplierBiz.setPaymentTerm(supplierExternalSaveDTO.getPaymentTermCode());
        supplierBiz.setPaymentTermName(StringUtils.isNotEmpty(supplierExternalSaveDTO.getPaymentTermCode()) ? paymentTermMap.get(supplierExternalSaveDTO.getPaymentTermCode()).getDocItemName() : "");
        supplierBiz.setCoopStartTime(supplierExternalSaveDTO.getCoopStartTime());
        supplierBiz.setCoopEndTime(supplierExternalSaveDTO.getCoopEndTime());
        supplierBiz.setPaymentAgreementId(StringUtils.isNotEmpty(supplierExternalSaveDTO.getPaymentAgreementCode()) ? paymentAgreementMap.getOrDefault(supplierExternalSaveDTO.getPaymentAgreementCode(), new PaymentAgreementVo()).getId() : null);
//                supplierBiz.setPaymentAgreementYsId();
        supplierBiz.setPaymentAgreementCode(supplierExternalSaveDTO.getPaymentAgreementCode());
        supplierBiz.setPaymentAgreementName(supplierExternalSaveDTO.getPaymentAgreementName());
        supplierBiz.setPeriodDays(supplierExternalSaveDTO.getPeriodDays());
//                supplierBiz.setOmsSupplierNo();
//                supplierBiz.setCurrencyId();

        CommonUtil.fillCreatInfo(operationModel, supplierBiz);
        CommonUtil.fillOperateInfo(operationModel, supplierBiz);

        supplierBizDAO.insert(supplierBiz);
    }

    public void handSupplierBaseMdmCompatible(OperationModel operationModel, SupplierExternalSaveDTO supplierExternalSaveDTO, SupplierV2 supplierV2) {
        String manageOrgNo = operationModel.getOrgNo();
        String supplierCode = supplierExternalSaveDTO.getSupplierCode();

        boolean baseExists = supplierBaseDAO.exists(Wrappers.<SupplierBase>lambdaQuery()
                .eq(SupplierBase::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(SupplierBase::getManageOrgNo, manageOrgNo)
                .eq(SupplierBase::getUseOrgNo, manageOrgNo)
                .eq(SupplierBase::getSupplierCode, supplierCode)
                .eq(SupplierBase::getDeleted, DeletedEnum.UN_DELETE.getValue()));
//        ValidatorUtils.checkTrueThrowEx(baseExists, "分派信息已经存在");
        if (baseExists) {
            log.warn("分派信息已经存在,supplierCode={},manageOrgNo={}", supplierCode, manageOrgNo);
            return;
        }

        SupplierBase supplierBase = new SupplierBase();
        supplierBase.setEnterpriseNo(supplierV2.getEnterpriseNo());
        supplierBase.setSupplierNo(supplierV2.getSupplierNo());
        supplierBase.setSupplierCode(supplierV2.getSupplierCode());
        supplierBase.setManageOrgNo(manageOrgNo);
        supplierBase.setUseOrgNo(manageOrgNo);
        supplierBase.setControlStatus(supplierExternalSaveDTO.getControlStatus());
        CommonUtil.fillCreatInfo(operationModel, supplierBase);
        CommonUtil.fillOperateInfo(operationModel, supplierBase);

        supplierBaseDAO.insert(supplierBase);
    }

    public void handCompanyBankMdmCompatible(OperationModel operationModel, SupplierExternalSaveDTO supplierExternalSaveDTO, SupplierV2 supplierV2, Map<String, BankType> bankTypeMap) {
        String manageOrgNo = operationModel.getOrgNo();
        String companyNo = supplierV2.getCompanyNo();

        List<CompanyBankReq> companyBankReqList = null;
        if (CollectionUtils.isNotEmpty(supplierExternalSaveDTO.getBankList())) {
            companyBankReqList = new ArrayList<>();
            for (BankDTO bankDTO : supplierExternalSaveDTO.getBankList()) {
                CompanyBankReq companyBankReq = new CompanyBankReq();
                companyBankReq.setBankCode(bankDTO.getBankCode());
                companyBankReq.setBankType(bankDTO.getBankType());
                companyBankReq.setOpenBank(bankDTO.getOpenBank());
                companyBankReq.setAccountName(bankDTO.getAccountName());
                companyBankReq.setAccountNo(bankDTO.getAccountNo());
                companyBankReq.setAccountType(bankDTO.getAccountType());
                companyBankReq.setLinkPerson(bankDTO.getLinkPerson());
                companyBankReq.setLinkPhone(bankDTO.getLinkPhone());
                companyBankReq.setStatus(bankDTO.getStatus());
                companyBankReq.setIsDefault(bankDTO.getIsDefault());
//                    companyBankReq.setCurrencyId();
//                    companyBankReq.setCurrencyName();
                companyBankReq.setBankTypeName(StringUtils.isNotEmpty(bankDTO.getBankType()) ? bankTypeMap.getOrDefault(bankDTO.getBankType(), new BankType()).getBankName() : null);
                companyBankReq.setAccountTypeName(null != bankDTO.getAccountType() ? BankAccountTypeEnum.getNameByValue(bankDTO.getAccountType()) : null);
                companyBankReq.setStatusName(null != bankDTO.getStatus() ? CommonStatusEnum.getNameByValue(bankDTO.getStatus()) : null);
                companyBankReq.setIsDefaultName(null != bankDTO.getIsDefault() ? CommonIfEnum.getNameByValue(bankDTO.getIsDefault()) : null);

                companyBankReqList.add(companyBankReq);
            }
        }
        companyV2Service.overwriteBank(operationModel, companyBankReqList, companyNo, manageOrgNo);
    }


    @Override
    @Transactional
//    @Async("mdmCompatibleExecutor")
    public void batchSaveMdmCompatible(OperationModel operationModel, List<SupplierExternalSaveDTO> params, List<SupplierInternalSaveVO> resultList) {
        log.warn("batchSaveMdmCompatible start, enterpriseNo={}, manageOrgNo={}, params={}, resultList={}", operationModel.getEnterpriseNo(), operationModel.getOrgNo(), JSON.toJSONString(params), JSON.toJSONString(resultList));

        // 不在灰度范围内的租户不处理
        if (!mdmGray.isEnterpriseGray(operationModel.getEnterpriseNo())) {
            log.warn("batchSaveMdmCompatible end, not in gray");

            return;
        }

        // 管理组织
        String manageOrgNo = operationModel.getOrgNo();

        if (StringUtils.isEmpty(manageOrgNo)) {
            log.warn("batchSaveMdmCompatible end, manageOrgNo is empty");

            return;
        }

        if (CollectionUtils.isEmpty(params) || CollectionUtils.isEmpty(resultList)) {
            log.warn("batchSaveMdmCompatible end, params or resultList null");

            return;
        }

        resultList = resultList.stream().filter(r -> null != r.getSuccess() && r.getSuccess()).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(resultList)) {
            log.warn("batchSaveMdmCompatible end, successful resultList empty");

            return;
        }

        List<SupplierV2> supplierV2s = supplierV2DAO.selectList(Wrappers.<SupplierV2>lambdaQuery()
                .eq(SupplierV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .in(SupplierV2::getSupplierCode, resultList.stream().map(SupplierInternalSaveVO::getSupplyCode).collect(Collectors.toList()))
                .eq(SupplierV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
        Map<String, SupplierV2> supplierCode2SupplierV2Map = supplierV2s.stream().collect(Collectors.toMap(SupplierV2::getSupplierCode, Function.identity()));

        Map<String, SupplierExternalSaveDTO> supplierCode2SaveDTO = params.stream().collect(Collectors.toMap(SupplierExternalSaveDTO::getSupplierCode, Function.identity()));

//        Map<String, String> currencyMap = dictEnterpriseService.findCurrencyList(operationModel.getEnterpriseNo());
//        currencyMap = currencyMap.entrySet().stream().collect(Collectors.toMap(entity-> entity.getValue(),entity-> entity.getKey()));

        List<OrganizationTreeVo> organizationTreeList = uimTenantService.getOrganizationTreeList(operationModel.getEnterpriseNo());
        Map<String, OrganizationTreeVo> organizationMap = organizationTreeList.stream().collect(Collectors.toMap(OrganizationTreeVo::getOrgCode, Function.identity(), (k, v) -> k));

        List<PaymentAgreementVo> paymentAgreementVoList = paymentAgreementService.findList(operationModel.getEnterpriseNo());
        Map<String, PaymentAgreementVo> paymentAgreementMap = paymentAgreementVoList.stream().collect(Collectors.toMap(PaymentAgreementVo::getCode, Function.identity()));

        Map<String, CustomDocResponse> paymentTermMap = customDocService.getItemByCustomDocCode(operationModel.getEnterpriseNo(), SystemConstant.DSRP_CUSTOMER_PAYMENT_TERM);

        List<BankType> bankTypeList = companyV2Service.getBankTypeList();
        Map<String, BankType> bankTypeMap = bankTypeList.stream().collect(Collectors.toMap(BankType::getBankCode, Function.identity()));

        Map<String, String> countryRegionMap = dictEnterpriseService.findPlatformCountryRegionList();


        for (SupplierInternalSaveVO supplierInternalSaveVO : resultList) {
            String supplierCode = supplierInternalSaveVO.getSupplyCode();
            SupplierExternalSaveDTO supplierExternalSaveDTO = supplierCode2SaveDTO.get(supplierCode);
            SupplierV2 supplierV2 = supplierCode2SupplierV2Map.get(supplierCode);
            ValidatorUtils.checkTrueThrowEx(null == supplierExternalSaveDTO || null == supplierV2, "通过供应商编码无法找到对应供应商");

            // 关联的内部组织关系
            CompanySaveOrUpdateReq companySaveOrUpdateReq = new CompanySaveOrUpdateReq();
            companySaveOrUpdateReq.setCompanyNo(supplierV2.getCompanyNo());
            companySaveOrUpdateReq.setIsAssociatedEnterprise(supplierExternalSaveDTO.getIsAssociatedEnterprise());
            if (CommonIfEnum.YES.getValue().equals(supplierExternalSaveDTO.getIsAssociatedEnterprise())) {
                OrganizationTreeVo organizationTreeVo = organizationMap.get(supplierExternalSaveDTO.getAssociatedOrgCode());
                if (null != organizationTreeVo) {
                    companySaveOrUpdateReq.setAssociatedOrgCode(supplierExternalSaveDTO.getAssociatedOrgCode());
                    companySaveOrUpdateReq.setAssociatedOrgNo(organizationTreeVo.getOrgNo());
                    companySaveOrUpdateReq.setAssociatedOrgName(organizationTreeVo.getOrgName());
                }
            } else {
                companySaveOrUpdateReq.setAssociatedOrgCode(null);
                companySaveOrUpdateReq.setAssociatedOrgNo(null);
                companySaveOrUpdateReq.setAssociatedOrgName(null);
            }
            log.warn("saveSupCusAssociatedOrg, companyNo={}, supplierCode={}, companySaveOrUpdateReq={}",supplierV2.getCompanyNo(), supplierCode, JSON.toJSONString(companySaveOrUpdateReq));
            companyV2Service.saveSupCusAssociatedOrg(supplierV2.getCompanyNo(), operationModel.getEnterpriseNo(), supplierCode, companySaveOrUpdateReq, CompanyPartnershipEnum.SUPPLIER);

            // 处理银行信息
            log.warn("handCompanyBankMdmCompatible, supplierExternalSaveDTO={}, supplierV2={}, bankTypeMap={}", supplierExternalSaveDTO, supplierV2, bankTypeMap);
            handCompanyBankMdmCompatible(operationModel, supplierExternalSaveDTO, supplierV2, bankTypeMap);

            // 更新内部组织信息
            log.warn("update supplierV2, supplierCode={}, supplierExternalSaveDTO={}", supplierCode, supplierExternalSaveDTO);
            supplierV2DAO.update(null, Wrappers.<SupplierV2>lambdaUpdate()
                    // 更新字段
                    .set(SupplierV2::getIsAssociatedEnterprise, supplierExternalSaveDTO.getIsAssociatedEnterprise())
                    .set(StringUtils.isNotEmpty(supplierExternalSaveDTO.getAssociatedOrgCode()), SupplierV2::getAssociatedOrgCode, supplierExternalSaveDTO.getAssociatedOrgCode())
                    .set(StringUtils.isNotEmpty(supplierExternalSaveDTO.getAssociatedOrgCode()), SupplierV2::getAssociatedOrgNo, organizationMap.get(supplierExternalSaveDTO.getAssociatedOrgCode()) != null ? organizationMap.get(supplierExternalSaveDTO.getAssociatedOrgCode()).getOrgNo() : null)
                    .set(StringUtils.isNotEmpty(supplierExternalSaveDTO.getAssociatedOrgCode()), SupplierV2::getAssociatedOrgName, organizationMap.get(supplierExternalSaveDTO.getAssociatedOrgCode()) != null ? organizationMap.get(supplierExternalSaveDTO.getAssociatedOrgCode()).getOrgName() : null)
                    // where条件
                    .eq(SupplierV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                    .eq(SupplierV2::getSupplierCode, supplierCode)
            );


            // 处理分派数据
            log.warn("handSupplierBaseMdmCompatible, supplierExternalSaveDTO={}, supplierV2={}", supplierExternalSaveDTO, supplierV2);
            handSupplierBaseMdmCompatible(operationModel, supplierExternalSaveDTO, supplierV2);

            // 处理业务数据
            log.warn("handSupplierBizMdmCompatible, supplierExternalSaveDTO={}, supplierV2={}, paymentTermMap={}, paymentAgreementMap={}", supplierExternalSaveDTO, supplierV2, paymentTermMap, paymentAgreementMap);
            handSupplierBizMdmCompatible(operationModel, supplierExternalSaveDTO, supplierV2, paymentTermMap, paymentAgreementMap);

            // 处理分类分派
            log.warn("handSupplierCategoryMdmCompatible, supplierExternalSaveDTO={}, supplierV2={}", supplierExternalSaveDTO, supplierV2);
            handSupplierCategoryMdmCompatible(operationModel, supplierExternalSaveDTO, supplierV2);

            // 处理联系人
            log.warn("handSupplierLinkmanListMdmCompatible, supplierExternalSaveDTO={}, supplierV2={}", supplierExternalSaveDTO, supplierV2);
            handSupplierLinkmanListMdmCompatible(operationModel, supplierExternalSaveDTO, supplierV2);

            // 处理地址
            log.warn("handSupplierAddressListMdmCompatible, supplierExternalSaveDTO={}, supplierV2={}, countryRegionMap={}", supplierExternalSaveDTO, supplierV2, countryRegionMap);
            handSupplierAddressListMdmCompatible(operationModel, supplierExternalSaveDTO, supplierV2, countryRegionMap);

            // 处理负责人
            log.warn("handSupplierOrderManListMdmCompatible, supplierExternalSaveDTO={}, supplierV2={}", supplierExternalSaveDTO, supplierV2);
            handSupplierOrderManListMdmCompatible(operationModel, supplierExternalSaveDTO, supplierV2);

            // 处理资质信息
            log.warn("handSupplierCertListMdmCompatible, supplierExternalSaveDTO={}, supplierV2={}", supplierExternalSaveDTO, supplierV2);
            handSupplierCertListMdmCompatible(operationModel, supplierExternalSaveDTO, supplierV2);

            // 分派通过SupplierAssignListener处理
        }

        log.warn("batchSaveMdmCompatible end");
    }

    private SupplierV2 packSupplierV2(SupplierV2 oldSupplier, SupplierV2 newSupplier) {
        SupplierV2 supplier = new SupplierV2();
        BeanUtils.copyProperties(oldSupplier, supplier);

        supplier.setSupplierName(newSupplier.getSupplierName());
        supplier.setMnemonicCode(newSupplier.getMnemonicCode());
        supplier.setSupplierNameEn(newSupplier.getSupplierNameEn());
        supplier.setRemark(newSupplier.getRemark());
        supplier.setSupplierCategoryNo(newSupplier.getSupplierCategoryNo());

        return supplier;
    }

    private SupplierBiz packSupplierBiz(SupplierBiz oldSupplierBiz, SupplierBiz newSupplierBiz) {
        SupplierBiz supplierBiz = new SupplierBiz();
        BeanUtils.copyProperties(oldSupplierBiz, supplierBiz);

        supplierBiz.setOwnerCompany(newSupplierBiz.getOwnerCompany());
        //信用天数额度
        supplierBiz.setCreditAmount(newSupplierBiz.getCreditAmount());
        supplierBiz.setPeriodDays(newSupplierBiz.getPeriodDays());
        //合作起止日期
        supplierBiz.setCoopStartTime(newSupplierBiz.getCoopStartTime());
        supplierBiz.setCoopEndTime(newSupplierBiz.getCoopEndTime());
        // 供应商性质（合作性质）
        supplierBiz.setCooperationMode(newSupplierBiz.getCooperationMode());
        // 启停状态
//        supplierBiz.setControlStatus(newSupplierBiz.getControlStatus());

        return supplierBiz;
    }

    private SupplierBase packSupplierBase(SupplierBase oldSupplierBase, SupplierBase newSupplierBase) {
        SupplierBase supplierBase = new SupplierBase();
        BeanUtils.copyProperties(oldSupplierBase, supplierBase);

        // 启停状态
        supplierBase.setControlStatus(newSupplierBase.getControlStatus());

        return supplierBase;
    }

    private List<SupplierInternalSaveVO> saveOrUpdateSupplierV2ForDA(OperationModel operationModel, Map<String, CompanyV2> companyNameMap, Map<String, SupplierV2> localSupplierMap, List<SupplierExternalSaveDTO> newList) {
        String manageOrgNo = operationModel.getOrgNo();

        List<SupplierInternalSaveVO> resultList = new ArrayList<>(newList.size());

        Map<String, SupplierBiz> supplierCode2BizMap = null;
        Map<String, SupplierBase> supplierCode2BaseMap = null;
        Map<String, List<SupplierOrderManV2>> orderManMap = null;
        Map<String, List<CompanyLinkmanV2>> linkManMap = null;
        Map<String, List<CompanyShippingAddressV2>> addressMap = null;
        if (MapUtils.isEmpty(localSupplierMap)) {
            supplierCode2BizMap = new HashMap<>();
            supplierCode2BaseMap = new HashMap<>();
            orderManMap = new HashMap<>();
            linkManMap = new HashMap<>();
            addressMap = new HashMap<>();
        } else {
            Set<String> supplierCodeSet = localSupplierMap.keySet();
            // 查询出所有档案的业务信息
            List<SupplierBiz> supplierBizList = supplierBizDAO.selectList(Wrappers.<SupplierBiz>lambdaQuery()
                    .eq(SupplierBiz::getEnterpriseNo, operationModel.getEnterpriseNo())
                    .eq(SupplierBiz::getUseOrgNo, manageOrgNo)
                    .in(SupplierBiz::getSupplierCode, supplierCodeSet)
                    .eq(SupplierBiz::getDeleted, DeletedEnum.UN_DELETE.getValue()));
            supplierCode2BizMap = supplierBizList.stream().collect(Collectors.toMap(SupplierBiz::getSupplierCode, Function.identity()));

            // 查询出分派记录
            List<SupplierBase> supplierBaseList = supplierBaseDAO.selectList(Wrappers.<SupplierBase>lambdaQuery()
                    .eq(SupplierBase::getEnterpriseNo, operationModel.getEnterpriseNo())
                    .eq(SupplierBase::getUseOrgNo, manageOrgNo)
                    .in(SupplierBase::getSupplierCode, supplierCodeSet)
                    .eq(SupplierBase::getDeleted, DeletedEnum.UN_DELETE.getValue()));
            supplierCode2BaseMap = supplierBaseList.stream().collect(Collectors.toMap(SupplierBase::getSupplierCode, Function.identity()));

            // 批量查询出所有的负责人、联系人、联系地址
            List<SupplierOrderManV2> supplierOrderManV2s = supplierOrderManV2DAO.selectList(Wrappers.<SupplierOrderManV2>lambdaQuery()
                    .eq(SupplierOrderManV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                    .eq(SupplierOrderManV2::getUseOrgNo, manageOrgNo)
                    .in(SupplierOrderManV2::getSupplierCode, supplierCodeSet)
                    .eq(SupplierOrderManV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
            orderManMap = supplierOrderManV2s.stream().collect(Collectors.groupingBy(SupplierOrderManV2::getSupplierCode));

            List<CompanyLinkmanV2> supplierLinkManV2s = companyLinkmanV2DAO.selectList(Wrappers.<CompanyLinkmanV2>lambdaQuery()
                    .eq(CompanyLinkmanV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                    .eq(CompanyLinkmanV2::getUseOrgNo, manageOrgNo)
                    .in(CompanyLinkmanV2::getSourceNo, supplierCodeSet)
                    .eq(CompanyLinkmanV2::getLinkmanType, LinkmanTypeEnum.SUPPLY.getValue())
                    .eq(CompanyLinkmanV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
            linkManMap = supplierLinkManV2s.stream().collect(Collectors.groupingBy(CompanyLinkmanV2::getSourceNo));

            List<CompanyShippingAddressV2> supplierAddressV2s = companyShippingAddressV2DAO.selectList(Wrappers.<CompanyShippingAddressV2>lambdaQuery()
                    .eq(CompanyShippingAddressV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                    .eq(CompanyShippingAddressV2::getUseOrgNo, manageOrgNo)
                    .in(CompanyShippingAddressV2::getSourceNo, supplierCodeSet)
                    .eq(CompanyShippingAddressV2::getLinkaddType, LinkmanTypeEnum.SUPPLY.getValue())
                    .eq(CompanyShippingAddressV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
            addressMap = supplierAddressV2s.stream().collect(Collectors.groupingBy(CompanyShippingAddressV2::getSourceNo));
        }

        List<SupplierV2> saveManageSupplierV2List = new ArrayList<>();
        List<SupplierV2> updateManageSupplierV2List = new ArrayList<>();
        List<SupplierBiz> saveManageSupplierBizList = new ArrayList<>();
        List<SupplierBiz> updateManageSupplierBizList = new ArrayList<>();
        List<SupplierBase> saveManageSupplierBaseList = new ArrayList<>();
        List<SupplierBase> updateManageSupplierBaseList = new ArrayList<>();

        List<Map<String, Object>> futureSupplierBaseAndBizList = new ArrayList<>();

        final List<OrganizationTreeVo> organizationTreeList = uimTenantService.getOrganizationTreeList(operationModel.getEnterpriseNo());
        final Map<String, OrganizationTreeVo> organizationMap = organizationTreeList.stream().collect(Collectors.toMap(OrganizationTreeVo::getOrgCode, Function.identity(), (k, v) -> k));

        for (SupplierExternalSaveDTO t : newList) {
            SupplierV2 futureSupplier = null;
            SupplierBiz futureSupplierBiz = null;
            SupplierBase futureSupplierBase = null;

            //修改
            if (localSupplierMap.containsKey(t.getSupplierCode())) {
                // 处理基本信息
                SupplierV2 supplier = localSupplierMap.get(t.getSupplierCode());
                SupplierV2 newSupplier = new SupplierV2();
                newSupplier.setSupplierName(t.getSupplierName());
                newSupplier.setMnemonicCode(t.getMnemonicCode());
                newSupplier.setSupplierNameEn(t.getSupplierNameEn());
                newSupplier.setRemark(t.getRemark());
                newSupplier.setSupplierCategoryNo(t.getSupplierCategoryNo());

                newSupplier.setIsAssociatedEnterprise(t.getIsAssociatedEnterprise());
                newSupplier.setAssociatedOrgCode(t.getAssociatedOrgCode());
                if (CommonIfEnum.YES.getValue().equals(t.getIsAssociatedEnterprise())) {
                    final OrganizationTreeVo organizationTreeVo = organizationMap.get(t.getAssociatedOrgCode());
                    if (organizationTreeVo != null) {
                        newSupplier.setAssociatedOrgNo(organizationTreeVo.getOrgNo());
                        newSupplier.setAssociatedOrgName(organizationTreeVo.getOrgName());
                    }
                }

                CommonUtil.fillOperateInfo(operationModel, newSupplier);

                // where条件
                newSupplier.setEnterpriseNo(operationModel.getEnterpriseNo());
                newSupplier.setManageOrgNo(supplier.getManageOrgNo());
                newSupplier.setSupplierNo(supplier.getSupplierNo());

//                supplierV2DAO.update(newSupplier, Wrappers.<SupplierV2>lambdaUpdate()
//                        .eq(SupplierV2::getEnterpriseNo, operationModel.getEnterpriseNo())
//                        .eq(SupplierV2::getManageOrgNo, supplier.getManageOrgNo())
//                        .eq(SupplierV2::getSupplierCode, supplier.getSupplierCode())
//                        .eq(SupplierV2::getDeleted, DeletedEnum.UN_DELETE.getValue())
//                );
                updateManageSupplierV2List.add(newSupplier);

                futureSupplier = packSupplierV2(supplier, newSupplier);


                // 处理业务信息
                SupplierBiz supplierBiz = supplierCode2BizMap.get(supplier.getSupplierCode());
                SupplierBiz newSupplierBiz = new SupplierBiz();
                //业务归属
                newSupplierBiz.setOwnerCompany(t.getOwnerCompany());
                //信用天数额度
                newSupplierBiz.setCreditAmount(t.getCreditAmount());
                newSupplierBiz.setPeriodDays(t.getPeriodDays());
                //合作起止日期
                newSupplierBiz.setCoopStartTime(t.getCoopStartTime());
                newSupplierBiz.setCoopEndTime(t.getCoopEndTime());
                // 供应商性质（合作性质）
                newSupplierBiz.setCooperationMode(t.getCooperationMode());
                // 启停状态
//                newSupplierBiz.setControlStatus(t.getControlStatus());
                CommonUtil.fillOperateInfo(operationModel, newSupplierBiz);

                // where条件
                newSupplierBiz.setId(supplierBiz.getId());
                newSupplierBiz.setEnterpriseNo(operationModel.getEnterpriseNo());
                newSupplierBiz.setSupplierCode(supplier.getSupplierCode());
                newSupplierBiz.setUseOrgNo(supplier.getManageOrgNo());

                updateManageSupplierBizList.add(newSupplierBiz);

                futureSupplierBiz = packSupplierBiz(supplierBiz, newSupplierBiz);


//                supplierBizDAO.update(newSupplierBiz, Wrappers.<SupplierBiz>lambdaUpdate()
//                        .eq(SupplierBiz::getId, supplierBiz.getId())
//                );


                // 处理分派信息
                SupplierBase supplierBase = supplierCode2BaseMap.get(supplier.getSupplierCode());
                SupplierBase newSupplierBase = new SupplierBase();
                newSupplierBase.setControlStatus(t.getControlStatus());
                CommonUtil.fillOperateInfo(operationModel, newSupplierBase);
                // where条件
                newSupplierBiz.setId(supplierBase.getId());
                newSupplierBiz.setEnterpriseNo(operationModel.getEnterpriseNo());
                newSupplierBiz.setSupplierCode(supplier.getSupplierCode());
                newSupplierBiz.setUseOrgNo(supplier.getManageOrgNo());

                updateManageSupplierBaseList.add(newSupplierBase);

                futureSupplierBase = packSupplierBase(supplierBase, newSupplierBase);


                resultList.add(new SupplierInternalSaveVO(t.getSupplierCode(), t.getSupplierName(), true, "供应商档案更新成功", operationModel));
            } else {
                //新增 。新增的供应商档案只能是生效状态，组织的档案状态后续单独处理
                // 新增管理组织的基本信息
                SupplierV2 supplier = new SupplierV2();
                supplier.setEnterpriseNo(operationModel.getEnterpriseNo());
                supplier.setManageOrgNo(manageOrgNo);
                supplier.setSupplierNo(numberCenterService.createNumber(NumberCenterConstant.SUPPLY_NO_KEY));
                supplier.setCompanyNo(companyNameMap.get(t.getCompanyName()).getCompanyNo());
                supplier.setUnifiedSocialCode(t.getUnifiedSocialCode());
                supplier.setSupplierCode(t.getSupplierCode());
                supplier.setSupplierName(t.getSupplierName());
                supplier.setMnemonicCode(t.getMnemonicCode());
                supplier.setSupplierNameEn(t.getSupplierNameEn());
                supplier.setRemark(t.getRemark());
                supplier.setBusinessFlag(SupplierBusinessFlagEnum.FORMAL.getValue());
                supplier.setSupplierCategoryNo(t.getSupplierCategoryNo());
                supplier.setIsAssociatedEnterprise(t.getIsAssociatedEnterprise());
                supplier.setAssociatedOrgCode(t.getAssociatedOrgCode());
                if (CommonIfEnum.YES.getValue().equals(t.getIsAssociatedEnterprise())) {
                    final OrganizationTreeVo organizationTreeVo = organizationMap.get(t.getAssociatedOrgCode());
                    if (organizationTreeVo != null) {
                        supplier.setAssociatedOrgNo(organizationTreeVo.getOrgNo());
                        supplier.setAssociatedOrgName(organizationTreeVo.getOrgName());
                    }
                }
                CommonUtil.fillCreatInfo(operationModel, supplier);
                saveManageSupplierV2List.add(supplier);

                futureSupplier = supplier;


                // 新增管理组织的业务信息
                SupplierBiz supplierBiz = new SupplierBiz();
                supplierBiz.setEnterpriseNo(operationModel.getEnterpriseNo());
                supplierBiz.setUseOrgNo(supplier.getManageOrgNo());
                supplierBiz.setSupplierNo(supplier.getSupplierNo());
                supplierBiz.setSupplierCode(supplier.getSupplierCode());
                supplierBiz.setOwnerCompany(t.getOwnerCompany());
                supplierBiz.setCreditAmount(t.getCreditAmount());
                supplierBiz.setPeriodDays(t.getPeriodDays());
                supplierBiz.setCoopStartTime(t.getCoopStartTime());
                supplierBiz.setCoopEndTime(t.getCoopEndTime());
                supplierBiz.setCooperationMode(t.getCooperationMode());
                CommonUtil.fillCreatInfo(operationModel, supplierBiz);
                saveManageSupplierBizList.add(supplierBiz);

                futureSupplierBiz = supplierBiz;

                resultList.add(new SupplierInternalSaveVO(t.getSupplierCode(), t.getSupplierName(), true, "供应商档案新增成功", operationModel));


                // 新增管理组织的分派记录
                SupplierBase supplierBase = new SupplierBase();
                supplierBase.setEnterpriseNo(operationModel.getEnterpriseNo());
                supplierBase.setSupplierNo(supplier.getSupplierNo());
                supplierBase.setSupplierCode(supplier.getSupplierCode());
                supplierBase.setManageOrgNo(manageOrgNo);
                supplierBase.setUseOrgNo(manageOrgNo);
                supplierBase.setControlStatus(t.getControlStatus());
                supplierBase.setDeleted(DeletedEnum.UN_DELETE.getValue());
                CommonUtil.fillCreatInfo(operationModel, supplierBase);
                saveManageSupplierBaseList.add(supplierBase);

                futureSupplierBase = supplierBase;
            }

            cudLinkmanPerSupplierForDA(operationModel, linkManMap.get(t.getSupplierCode()), t.getLinkmanList(), manageOrgNo, futureSupplier);

            cudAddressPerSupplierForDA(operationModel, addressMap.get(t.getSupplierCode()), t.getLinkAddressList(), manageOrgNo, futureSupplier);

            cudOrdermanPerSupplierForDA(operationModel, orderManMap.get(t.getSupplierCode()), t.getResponsibleManList(), manageOrgNo, futureSupplier);


            Map<String, Object> futureSupplierBaseAndBizMap = new HashMap<>();
            futureSupplierBaseAndBizMap.put("futureSupplier", futureSupplier);
            futureSupplierBaseAndBizMap.put("futureSupplierBiz", futureSupplierBiz);
            futureSupplierBaseAndBizList.add(futureSupplierBaseAndBizMap);
        }

        if (CollectionUtils.isNotEmpty(saveManageSupplierV2List)) {
            supplierV2DAO.addBatch(saveManageSupplierV2List);
        }

        if (CollectionUtils.isNotEmpty(updateManageSupplierV2List)) {
            supplierV2DAO.batchUpdateForDA(updateManageSupplierV2List);
        }

        if (CollectionUtils.isNotEmpty(saveManageSupplierBizList)) {
            supplierBizDAO.addBatch(saveManageSupplierBizList);
        }

        if (CollectionUtils.isNotEmpty(updateManageSupplierBizList)) {
            supplierBizDAO.batchUpdateForDA(updateManageSupplierBizList);
        }

        if (CollectionUtils.isNotEmpty(saveManageSupplierBaseList)) {
            supplierBaseDAO.addBatch(saveManageSupplierBaseList);
        }

        if (CollectionUtils.isNotEmpty(updateManageSupplierBaseList)) {
            supplierBaseDAO.batchUpdateForDA(updateManageSupplierBaseList);
        }

        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                // 取消分派
                {
                    SupplierAssignExeRequest supplierAssignExeRequest = new SupplierAssignExeRequest();
                    supplierAssignExeRequest.setEnterpriseNo(operationModel.getEnterpriseNo());
                    supplierAssignExeRequest.setManageOrgNo(manageOrgNo);
                    List<SupplierAssignDocExeRequest> docList = new ArrayList<>();
                    for (SupplierExternalSaveDTO supplierExternalSaveDTO : newList) {
                        SupplierAssignDocExeRequest supplierAssignDocExeRequest = new SupplierAssignDocExeRequest();
                        supplierAssignDocExeRequest.setSupplierCode(supplierExternalSaveDTO.getSupplierCode());
                        List<String> assignOrgNoList = new ArrayList<>();
                        for (SupplierExternalAssignItemDTO supplierExternalAssignItemDTO : supplierExternalSaveDTO.getAssignOrgList()) {
                            if ("disableAssign".equals(supplierExternalAssignItemDTO.getStatus())) {
                                assignOrgNoList.add(supplierExternalAssignItemDTO.getOrgCode());
                            }
                        }
                        supplierAssignDocExeRequest.setUseOrgNoList(assignOrgNoList);
                        docList.add(supplierAssignDocExeRequest);
                    }
                    supplierAssignExeRequest.setDocList(docList);

                    supplierV2Service.deAssignSupplier(supplierAssignExeRequest);
                }

                // 分派
                {
                    SupplierAssignExeRequest supplierAssignExeRequest = new SupplierAssignExeRequest();
                    supplierAssignExeRequest.setEnterpriseNo(operationModel.getEnterpriseNo());
                    supplierAssignExeRequest.setManageOrgNo(manageOrgNo);
                    List<SupplierAssignDocExeRequest> docList = new ArrayList<>();
                    for (SupplierExternalSaveDTO supplierExternalSaveDTO : newList) {
                        SupplierAssignDocExeRequest supplierAssignDocExeRequest = new SupplierAssignDocExeRequest();
                        supplierAssignDocExeRequest.setSupplierCode(supplierExternalSaveDTO.getSupplierCode());
                        List<String> assignOrgNoList = new ArrayList<>();
                        for (SupplierExternalAssignItemDTO supplierExternalAssignItemDTO : supplierExternalSaveDTO.getAssignOrgList()) {
                            if ("assign".equals(supplierExternalAssignItemDTO.getStatus())) {
                                assignOrgNoList.add(supplierExternalAssignItemDTO.getOrgCode());
                            }
                        }
                        supplierAssignDocExeRequest.setUseOrgNoList(assignOrgNoList);
                        docList.add(supplierAssignDocExeRequest);
                    }
                    supplierAssignExeRequest.setDocList(docList);

                    supplierV2Service.assignSupplier(supplierAssignExeRequest);
                }
            }
        });

        return resultList;
    }

    private Map<String, List<?>> cudLinkmanPerSupplierForDA(OperationModel operationModel, List<CompanyLinkmanV2> oldList, List<CompanyLinkmanDTO> newList, String useOrgNo, SupplierV2 futureSupplier) {
        Map<String, List<?>> diffResultMap = BeanUtil.diffCUD(oldList, newList);

        List<CompanyLinkmanDTO> addList = (List<CompanyLinkmanDTO>) diffResultMap.get("addList");
        List<CompanyLinkmanDTO> updateList = (List<CompanyLinkmanDTO>) diffResultMap.get("updateList");
        List<CompanyLinkmanV2> deleteList = (List<CompanyLinkmanV2>) diffResultMap.get("deleteList");

        if (CollectionUtils.isNotEmpty(addList)) {
            List<CompanyLinkmanV2> addLinkmanList = new ArrayList<>();

            for (CompanyLinkmanDTO companyLinkmanReq : addList) {
                CompanyLinkmanV2 companyLinkman = new CompanyLinkmanV2();
                companyLinkman.setEnterpriseNo(operationModel.getEnterpriseNo());
                companyLinkman.setUseOrgNo(useOrgNo);
                companyLinkman.setCompanyNo(futureSupplier.getCompanyNo());
                companyLinkman.setLinkCode(companyLinkmanReq.getLinkCode());
                companyLinkman.setLinkman(companyLinkmanReq.getLinkman());
                companyLinkman.setPosition(companyLinkmanReq.getPosition());
                companyLinkman.setMobilePhone(companyLinkmanReq.getMobilePhone());
                companyLinkman.setSex(companyLinkmanReq.getSex());
                companyLinkman.setFixedPhone(companyLinkmanReq.getFixedPhone());
                companyLinkman.setQq(companyLinkmanReq.getQq());
                companyLinkman.setWx(companyLinkmanReq.getWx());
                companyLinkman.setLinkmanType(LinkmanTypeEnum.SUPPLY.getValue());
                companyLinkman.setStatus(CommonStatusEnum.EFFECTIVE.getValue());
                companyLinkman.setSourceNo(futureSupplier.getSupplierCode());
                companyLinkman.setEmail(companyLinkmanReq.getEmail());
                companyLinkman.setIsDefault(companyLinkmanReq.getIsDefault());
                companyLinkman.setStatus(companyLinkmanReq.getStatus());
                CommonUtil.fillCreatInfo(operationModel, companyLinkman);

                addLinkmanList.add(companyLinkman);
            }
            companyLinkmanV2DAO.addBatch(addLinkmanList);
        }

        if (CollectionUtils.isNotEmpty(updateList)) {
            List<CompanyLinkmanV2> updateBankList = new ArrayList<>();
            for (CompanyLinkmanDTO companyLinkmanReq : updateList) {
                CompanyLinkmanV2 companyLinkman = new CompanyLinkmanV2();
                companyLinkman.setId(companyLinkmanReq.getId());
                companyLinkman.setEnterpriseNo(operationModel.getEnterpriseNo());
                companyLinkman.setUseOrgNo(useOrgNo);
                companyLinkman.setCompanyNo(futureSupplier.getCompanyNo());
                companyLinkman.setLinkCode(companyLinkmanReq.getLinkCode());
                companyLinkman.setLinkman(companyLinkmanReq.getLinkman());
                companyLinkman.setPosition(companyLinkmanReq.getPosition());
                companyLinkman.setMobilePhone(companyLinkmanReq.getMobilePhone());
                companyLinkman.setSex(companyLinkmanReq.getSex());
                companyLinkman.setFixedPhone(companyLinkmanReq.getFixedPhone());
                companyLinkman.setQq(companyLinkmanReq.getQq());
                companyLinkman.setWx(companyLinkmanReq.getWx());
                companyLinkman.setLinkmanType(LinkmanTypeEnum.SUPPLY.getValue());
                companyLinkman.setStatus(CommonStatusEnum.EFFECTIVE.getValue());
                companyLinkman.setSourceNo(futureSupplier.getSupplierCode());
                companyLinkman.setEmail(companyLinkmanReq.getEmail());
                companyLinkman.setIsDefault(companyLinkmanReq.getIsDefault());
                companyLinkman.setStatus(companyLinkmanReq.getStatus());
                CommonUtil.fillOperateInfo(operationModel, companyLinkman);

                updateBankList.add(companyLinkman);
            }
            companyLinkmanV2DAO.updateByIdBatch(updateBankList);
        }

        if (CollectionUtils.isNotEmpty(deleteList)) {
            CompanyLinkmanV2 companyLinkmanV2 = new CompanyLinkmanV2();
            companyLinkmanV2.setDeleted(DeletedEnum.DELETED.getValue());
            CommonUtil.fillOperateInfo(operationModel, companyLinkmanV2);

            companyLinkmanV2DAO.update(companyLinkmanV2, Wrappers.<CompanyLinkmanV2>lambdaQuery()
                    .eq(CompanyLinkmanV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                    .in(CompanyLinkmanV2::getId, deleteList.stream().map(CompanyLinkmanV2::getId).collect(Collectors.toList())));
        }

        return diffResultMap;
    }

    private List<SupplierExternalSaveVO> convert2ExternalVoForDA(List<SupplierInternalSaveVO> invalidatedList, List<SupplierInternalSaveVO> handledList) {
        if (CollectionUtils.isEmpty(invalidatedList) && CollectionUtils.isEmpty(handledList)) {
            return Collections.emptyList();
        }

        List<SupplierExternalSaveVO> retResultList = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(invalidatedList)) {
            for (SupplierInternalSaveVO supplierInternalSaveVO : invalidatedList) {
                SupplierExternalSaveVO supplierExternalSaveVO = new SupplierExternalSaveVO();
                BeanUtils.copyProperties(supplierInternalSaveVO, supplierExternalSaveVO);
                retResultList.add(supplierExternalSaveVO);
            }
        }

        if (CollectionUtils.isNotEmpty(handledList)) {
            for (SupplierInternalSaveVO supplierInternalSaveVO : handledList) {
                SupplierExternalSaveVO supplierExternalSaveVO = new SupplierExternalSaveVO();
                BeanUtils.copyProperties(supplierInternalSaveVO, supplierExternalSaveVO);
                retResultList.add(supplierExternalSaveVO);
            }
        }

        return retResultList;
    }

    private void batchSaveBusinessLogForDA(List<SupplierInternalSaveVO> resultList) {
        if (!org.springframework.util.CollectionUtils.isEmpty(resultList)) {
            List<SupplierInternalSaveVO> successList = new ArrayList<>();
            List<SupplierInternalSaveVO> failList = new ArrayList<>();
            for (SupplierInternalSaveVO supplierInternalSaveVO : resultList) {
                if (supplierInternalSaveVO.getSuccess() != null && !supplierInternalSaveVO.getSuccess()
                        && !StringUtils.isEmpty(supplierInternalSaveVO.getSupplyCode())) {
                    failList.add(supplierInternalSaveVO);
                } else if (supplierInternalSaveVO.getSuccess() != null && supplierInternalSaveVO.getSuccess()) {
                    successList.add(supplierInternalSaveVO);
                }
            }

            if (!org.springframework.util.CollectionUtils.isEmpty(successList)) {
                List<IntegrationLogDTO> successLogDTOList = new ArrayList<>();
                for (SupplierInternalSaveVO supplierInternalSaveVO : successList) {
                    IntegrationLogDTO integrationLogDTO = new IntegrationLogDTO();
                    // 上游请求id，open推进来的不用管
//                integrationLogDTO.setRequestId(UUID.randomUUID().toString());
                    integrationLogDTO.setRequestType(IntegrationRequestType.OPEN.getRequestType());
                    integrationLogDTO.setSuccess(true); // 是否成功
                    integrationLogDTO.setBusinessBillType(MdmBillType.ZSGYS.name());

                    integrationLogDTO.setBusinessBillNo(supplierInternalSaveVO.getSupplyCode());
                    integrationLogDTO.setBusinessBillName(supplierInternalSaveVO.getSupplyName());
                    OperationModel operationModel = supplierInternalSaveVO.getOperationModel();
                    integrationLogDTO.setEnterpriseNo(operationModel.getEnterpriseNo());
                    integrationLogDTO.setSubjectSystem(MdmBillType.systemCode); // 对接系统

                    integrationLogDTO.setBusinessOrgNo(operationModel.getOrgNo());
                    integrationLogDTO.setBusinessOrgName(operationModel.getOrgName());
                    // 交易对象
//                integrationLogDTO.setTradeObject(t.getCustomerName());
                    successLogDTOList.add(integrationLogDTO);
                }
                log.warn("batchSaveLogReq={}", successLogDTOList);
                List<IntegrationLogSaveVO> integrationLogSaveVOS = uLogService.batchSaveLog(successLogDTOList);
                log.warn("batchSaveLogResp={}", integrationLogSaveVOS);
            }
            if (!org.springframework.util.CollectionUtils.isEmpty(failList)) {
                List<IntegrationLogDTO> errorList = new ArrayList<>();
                for (SupplierInternalSaveVO supplierInternalSaveVO : failList) {
                    IntegrationLogDTO integrationLogDTO = new IntegrationLogDTO();
                    // 上游请求id，open推进来的不用管
//                integrationLogDTO.setRequestId(UUID.randomUUID().toString());
                    integrationLogDTO.setRequestType(IntegrationRequestType.OPEN.getRequestType());
                    integrationLogDTO.setSuccess(false); // 是否成功
                    integrationLogDTO.setErrorCode(supplierInternalSaveVO.getIntegrationError().getErrorCode()); // 错误编码
                    integrationLogDTO.setErrorMsg(supplierInternalSaveVO.getMessage()); // 错误信息
                    integrationLogDTO.setBusinessBillType(MdmBillType.ZSGYS.name());

                    integrationLogDTO.setBusinessBillNo(supplierInternalSaveVO.getSupplyCode());
                    integrationLogDTO.setBusinessBillName(supplierInternalSaveVO.getSupplyName());

                    OperationModel operationModel = supplierInternalSaveVO.getOperationModel();
                    integrationLogDTO.setEnterpriseNo(operationModel.getEnterpriseNo());
                    integrationLogDTO.setSubjectSystem(MdmBillType.systemCode); // 对接系统

                    integrationLogDTO.setBusinessOrgNo(operationModel.getOrgNo());
                    integrationLogDTO.setBusinessOrgName(operationModel.getOrgName());
                    // 交易对象
//                integrationLogDTO.setTradeObject(t.getCustomerName());
                    errorList.add(integrationLogDTO);
                }
                log.warn("batchSaveLogReq={}", errorList);
                List<IntegrationLogSaveVO> integrationLogSaveVOS = uLogService.batchSaveLog(errorList);
                log.warn("batchSaveLogResp={}", integrationLogSaveVOS);
            }
        }
    }
}
