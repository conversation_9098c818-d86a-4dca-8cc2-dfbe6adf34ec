package com.yyigou.dsrp.cdc.service.listener;

import com.alibaba.fastjson.JSON;
import com.yyigou.dsrp.cdc.common.util.OperationModel;
import com.yyigou.dsrp.cdc.dao.company.entity.Company;
import com.yyigou.dsrp.cdc.dao.customer.entity.Customer;
import com.yyigou.dsrp.cdc.dao.supplier.entity.Supplier;
import com.yyigou.dsrp.cdc.model.enums.CompanyPartnershipEnum;
import com.yyigou.dsrp.cdc.service.common.CdcTenantService;
import com.yyigou.dsrp.cdc.service.common.MasterDataSyncService;
import com.yyigou.dsrp.cdc.service.customer.CustomerService;
import com.yyigou.dsrp.cdc.service.listener.model.CompanyUpdateModel;
import com.yyigou.dsrp.cdc.service.supplier.SupplierService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jms.listener.SessionAwareMessageListener;
import org.springframework.stereotype.Component;

import javax.jms.JMSException;
import javax.jms.Message;
import javax.jms.Session;
import javax.jms.TextMessage;
import java.util.List;
import java.util.Objects;

/**
 * 企业档案更新监听
 *
 * @author: Moore
 * @date: 2024/7/13 14:32
 * @version: 1.0.0
 */
@Component
@Slf4j
public class CompanyUpdateListener implements SessionAwareMessageListener {

    @Autowired
    private CdcTenantService cdcTenantService;
    @Autowired
    private CustomerService customerService;
    @Autowired
    private SupplierService supplierService;
    @Autowired
    private MasterDataSyncService masterDataSyncService;

    @Override
    public void onMessage(Message message, Session session) throws JMSException {
        //  获取消息
        TextMessage textMessage = (TextMessage) message;
        try {
//            //有部分MQ需要等待事物提交 先休眠1.5s
//            Thread.sleep(1500);
            String taskId = message.getJMSMessageID();
            log.warn("编辑企业档案消息：{}，messageId:{}，开始消费", textMessage.getText(), taskId);
            CompanyUpdateModel companyUpdateModel = JSON.parseObject(textMessage.getText(), CompanyUpdateModel.class);
            if (Objects.isNull(companyUpdateModel)) {
                log.warn("编辑企业档案消息：修改后企业档案不存在,跳过消费");
                return;
            }
            String groupEnterpriseNo = companyUpdateModel.getEnterpriseNo();
            if (StringUtils.isBlank(groupEnterpriseNo)) {
                log.error("编辑企业档案消息：集团租户编号不存在,跳过消费");
                return;
            }

            Company newCompany = companyUpdateModel.getNewCompany();
            OperationModel operationModel = companyUpdateModel.getOperationModel();
            if (Objects.isNull(newCompany)) {
                log.warn("编辑企业档案消息：修改后企业档案不存在,跳过消费");
                return;
            }
            String companyNo = newCompany.getCompanyNo();

            // 如果是集团租户或者独立租户，企业只对应一个客户或者供应商，需要联动修改客商名称
            boolean updateSubCustomerFlag = false;
            boolean updateSubSupplierFlag = false;
            String customerCode = null;
            String supplierCode = null;
            if (!cdcTenantService.getSubTenantFlag(groupEnterpriseNo) && StringUtils.isNotBlank(newCompany.getPartnership())) {
                List<String> partnershipList = JSON.parseArray(newCompany.getPartnership(), String.class);
                for (String s : partnershipList) {
                    if (CompanyPartnershipEnum.CUSTOMER.getType().equals(s)) {
                        List<Customer> customerList = customerService.findCustomerByCompanyNo(groupEnterpriseNo, companyNo);
                        if (CollectionUtils.isNotEmpty(customerList) && customerList.size() == 1) {
                            Customer customer = customerList.get(0);
                            customer.setCustomerName(newCompany.getCompanyName());
                            customerService.updateCustomer(customer);
                            // 如果是集团租户，同步更新子租户客户和企业信息
                            if (cdcTenantService.getGroupTenantFlag(groupEnterpriseNo)) {
                                updateSubCustomerFlag = true;
                                customerCode = customer.getCustomerCode();
                            }
                        }
                    } else if (CompanyPartnershipEnum.SUPPLIER.getType().equals(s)) {
                        List<Supplier> supplierList = supplierService.findSupplierByCompanyNo(groupEnterpriseNo, companyNo);
                        if (CollectionUtils.isNotEmpty(supplierList) && supplierList.size() == 1) {
                            Supplier supplier = supplierList.get(0);
                            supplier.setSupplierName(newCompany.getCompanyName());
                            supplierService.updateSupplier(supplier);
                            // 如果是集团租户，同步更新子租户供应商和企业信息
                            if (cdcTenantService.getGroupTenantFlag(groupEnterpriseNo)) {
                                updateSubSupplierFlag = true;
                                supplierCode = supplier.getSupplierCode();
                            }
                        }
                    }
                }
            }

            // 同步子租户客户信息
            if (updateSubCustomerFlag && StringUtils.isNotBlank(customerCode)) {
                try {
                    masterDataSyncService.saveSyncCustomerArchive(groupEnterpriseNo, customerCode, operationModel);
                } catch (Exception e) {
                    log.error("编辑企业档案消息：同步更新子租户客户信息失败,失败原因：{}", e.getMessage(), e);
                }
            }

            // 同步子租户供应商信息
            if (updateSubSupplierFlag && StringUtils.isNotBlank(supplierCode)) {
                try {
                    masterDataSyncService.saveSyncSupplierArchive(groupEnterpriseNo, supplierCode, operationModel);
                } catch (Exception e) {
                    log.error("编辑企业档案消息：同步更新子租户供应商信息失败,失败原因：{}", e.getMessage(), e);
                }
            }

            // 集团租户编辑，异步更新有使用关系的子租户的企业档案
            Company oldCompany = companyUpdateModel.getOldCompany();
            if (Objects.isNull(oldCompany)) {
                log.warn("编辑企业档案消息：修改前企业档案不存在,跳过消费");
                return;
            }
            if (cdcTenantService.getGroupTenantFlag(groupEnterpriseNo)) {
                try {
                    masterDataSyncService.updateSyncSubTenantCompanyInfo(groupEnterpriseNo, oldCompany, newCompany, operationModel);
                } catch (Exception e) {
                    log.error("编辑企业档案消息：同步更新子租户企业档案信息失败,失败原因：{}", e.getMessage(), e);
                }
            }

            // 主动发送消息回执, 告知broker消息已经被消费了
            message.acknowledge();
        } catch (Exception e) {
            log.error("编辑企业档案消息：消费失败,消息内容：{}, 失败原因:{}", textMessage.getText(), e.getMessage(), e);
        }

    }
}
