package com.yyigou.dsrp.cdc.service.listener.v2;

import com.alibaba.fastjson.JSON;
import com.yyigou.ddc.common.service.SessionUser;
import com.yyigou.ddc.services.dlog.dto.DLogLevel;
import com.yyigou.dsrp.cdc.dao.v2.company.entity.CompanyV2;
import com.yyigou.dsrp.cdc.dao.v2.customer.entity.CustomerV2;
import com.yyigou.dsrp.cdc.dao.v2.supplier.entity.SupplierV2;
import com.yyigou.dsrp.cdc.manager.integration.dlog.BusinessLogService;
import com.yyigou.dsrp.cdc.model.constant.ViewNameConstant;
import com.yyigou.dsrp.cdc.model.enums.CompanyPartnershipEnum;
import com.yyigou.dsrp.cdc.service.listener.model.v2.CompanyUpdateModel;
import com.yyigou.dsrp.cdc.service.v2.customer.CustomerV2Service;
import com.yyigou.dsrp.cdc.service.v2.supplier.SupplierV2Service;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jms.listener.SessionAwareMessageListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallbackWithoutResult;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import javax.jms.JMSException;
import javax.jms.Message;
import javax.jms.Session;
import javax.jms.TextMessage;
import java.util.List;
import java.util.Objects;

/**
 * 企业档案更新监听
 */
@Component("companyV2UpdateListener")
@Slf4j
public class CompanyV2UpdateListener implements SessionAwareMessageListener {
    @Autowired
    private CustomerV2Service customerV2Service;

    @Autowired
    private SupplierV2Service supplierV2Service;

    @Resource
    private TransactionTemplate transactionTemplate;

    @Resource
    private BusinessLogService businessLogService;

    @Override
    public void onMessage(Message message, Session session) throws JMSException {
        TextMessage textMessage = (TextMessage) message;

        try {
            String taskId = message.getJMSMessageID();
            log.warn("编辑企业档案消息：{}，messageId:{}，开始消费", textMessage.getText(), taskId);

            CompanyUpdateModel companyUpdateModel = JSON.parseObject(textMessage.getText(), CompanyUpdateModel.class);
            if (Objects.isNull(companyUpdateModel)) {
                log.warn("编辑企业档案消息：修改后企业档案不存在,跳过消费");
                return;
            }
            String enterpriseNo = companyUpdateModel.getEnterpriseNo();
            if (StringUtils.isBlank(enterpriseNo)) {
                log.error("编辑企业档案消息：租户编号不存在,跳过消费");
                return;
            }

            CompanyV2 newCompanyV2 = companyUpdateModel.getNewCompanyV2();
            if (Objects.isNull(newCompanyV2)) {
                log.warn("编辑企业档案消息：修改后企业档案不存在,跳过消费");
                return;
            }

            // 企业只对应一个客户或者供应商，需要联动修改客商名称
            if (StringUtils.isBlank(newCompanyV2.getPartnership())) {
                log.warn("编辑企业档案消息：不存在合作关系,跳过消费");
                return;
            }

            CustomerV2 newCustomerV2 = null;
            SupplierV2 newSupplierV2 = null;

            String customerCode = null;
            String supplierCode = null;

            String customerManageOrgNo = null;
            String supplierManageOrgNo = null;

            if (newCompanyV2.getPartnership().contains(CompanyPartnershipEnum.CUSTOMER.getType())) {
                List<CustomerV2> customerV2s = customerV2Service.queryByCompanyNo(newCompanyV2.getEnterpriseNo(), newCompanyV2.getCompanyNo());
                if (CollectionUtils.isNotEmpty(customerV2s) && customerV2s.size() == 1) {
                    newCustomerV2 = new CustomerV2();
                    newCustomerV2.setCustomerName(newCompanyV2.getCompanyName());
                    newCustomerV2.setOperateNo(newCompanyV2.getOperateNo());
                    newCustomerV2.setOperateName(newCompanyV2.getOperateName());
                    newCustomerV2.setOperateTime(newCompanyV2.getOperateTime());

                    customerCode = customerV2s.get(0).getCustomerCode();

                    customerManageOrgNo = customerV2s.get(0).getManageOrgNo();
                }
            }

            if (newCompanyV2.getPartnership().contains(CompanyPartnershipEnum.SUPPLIER.getType())) {
                List<SupplierV2> supplierV2s = supplierV2Service.queryByCompanyNo(newCompanyV2.getEnterpriseNo(), newCompanyV2.getCompanyNo());
                if (CollectionUtils.isNotEmpty(supplierV2s) && supplierV2s.size() == 1) {
                    newSupplierV2 = new SupplierV2();
                    newSupplierV2.setSupplierName(newCompanyV2.getCompanyName());
                    newSupplierV2.setOperateNo(newCompanyV2.getOperateNo());
                    newSupplierV2.setOperateName(newCompanyV2.getOperateName());
                    newSupplierV2.setOperateTime(newCompanyV2.getOperateTime());

                    supplierCode = supplierV2s.get(0).getSupplierCode();

                    supplierManageOrgNo = supplierV2s.get(0).getManageOrgNo();
                }
            }

            if ((null != newCustomerV2 && null != customerCode) || (null != newSupplierV2 && null != supplierCode)) {
                final CustomerV2 finalNewCustomerV2 = newCustomerV2;
                final SupplierV2 finalNewSupplierV2 = newSupplierV2;
                final String finalCustomerCode = customerCode;
                final String finalSupplierCode = supplierCode;

                transactionTemplate.execute(new TransactionCallbackWithoutResult() {
                    protected void doInTransactionWithoutResult(TransactionStatus status) {
                        if (null != finalNewCustomerV2 && null != finalCustomerCode) {
                            customerV2Service.syncCustomerNameByCompanyName(newCompanyV2.getEnterpriseNo(), finalCustomerCode, finalNewCustomerV2);
                        }

                        if (null != finalNewSupplierV2 && null != finalSupplierCode) {
                            supplierV2Service.syncSupplierNameByCompanyName(newCompanyV2.getEnterpriseNo(), finalSupplierCode, finalNewSupplierV2);
                        }
                    }
                });

                SessionUser sessionUser = new SessionUser();
                sessionUser.setEnterpriseNo(newCompanyV2.getEnterpriseNo());
                sessionUser.setEmployerNo(newCompanyV2.getOperateNo());
                sessionUser.setUserName(newCompanyV2.getOperateName());

                if (null != finalCustomerCode) {
                    log.warn("更新唯一的客户档案={}", finalCustomerCode);

                    businessLogService.saveLogViaBackend(sessionUser, DLogLevel.INFO.name(), ViewNameConstant.BDC_CUSTOMER_VIEW, customerCode + customerManageOrgNo, "企业更名同步编辑客户名称", "企业更名同步编辑客户名称", JSON.toJSONString(finalNewCustomerV2));
                }
                if (null != finalSupplierCode) {
                    log.warn("更新唯一的供应商档案={}", finalSupplierCode);

                    businessLogService.saveLogViaBackend(sessionUser, DLogLevel.INFO.name(), ViewNameConstant.BDC_SUPPLIER_VIEW, supplierCode + supplierManageOrgNo, "企业更名同步编辑供应商名称", "企业更名同步编辑供应商名称", JSON.toJSONString(finalNewSupplierV2));
                }
            }
        } catch (Exception e) {
            log.error("编辑企业档案消息：消费失败,消息内容：{}, 失败原因:{}", textMessage.getText(), e.getMessage(), e);
        } finally {
            // 主动发送消息回执, 告知broker消息已经被消费了
            message.acknowledge();
        }
    }
}
