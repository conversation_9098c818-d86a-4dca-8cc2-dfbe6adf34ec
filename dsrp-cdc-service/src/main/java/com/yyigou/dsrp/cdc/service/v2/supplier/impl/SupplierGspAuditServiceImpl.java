package com.yyigou.dsrp.cdc.service.v2.supplier.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yyigou.dsrp.cdc.dao.v2.supplier.SupplierGspAuditDAO;
import com.yyigou.dsrp.cdc.dao.v2.supplier.entity.SupplierGspAudit;
import com.yyigou.dsrp.cdc.service.v2.supplier.SupplierGspAuditService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service("supplierGspAuditService")
@RequiredArgsConstructor
@Slf4j
public class SupplierGspAuditServiceImpl extends ServiceImpl<SupplierGspAuditDAO, SupplierGspAudit> implements SupplierGspAuditService {


}
