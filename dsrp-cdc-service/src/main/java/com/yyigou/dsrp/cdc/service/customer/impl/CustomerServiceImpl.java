package com.yyigou.dsrp.cdc.service.customer.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.yyigou.ddc.common.exception.BusinessException;
import com.yyigou.ddc.common.page.PageDto;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.ddc.common.util.DateUtil;
import com.yyigou.ddc.common.util.ValidatorUtils;
import com.yyigou.ddc.services.ddc.dict.vo.AreaCodeVo;
import com.yyigou.ddc.services.ddc.uim.enums.TenantTypeEnum;
import com.yyigou.ddc.services.ddc.uim.vo.EmployeeVo;
import com.yyigou.ddc.services.ddc.uim.vo.OrganizationTreeVo;
import com.yyigou.ddc.services.ddc.uim.vo.OrganizationVo;
import com.yyigou.ddc.services.ddc.uim.vo.TenantVo;
import com.yyigou.ddc.services.dsrp.gsp.vo.CertControlTypeVo;
import com.yyigou.ddc.services.dw.doris.enums.DorisReadModeEnum;
import com.yyigou.ddc.services.dw.doris.util.DorisHelper;
import com.yyigou.dsrp.cdc.api.company.dto.BankDTO;
import com.yyigou.dsrp.cdc.api.company.dto.CompanyLinkmanDTO;
import com.yyigou.dsrp.cdc.api.company.dto.CompanyShippingAddressDTO;
import com.yyigou.dsrp.cdc.api.company.vo.BankVO;
import com.yyigou.dsrp.cdc.api.company.vo.CompanyLinkmanVO;
import com.yyigou.dsrp.cdc.api.company.vo.CompanyShippingAddressVO;
import com.yyigou.dsrp.cdc.api.customer.dto.*;
import com.yyigou.dsrp.cdc.api.customer.vo.*;
import com.yyigou.dsrp.cdc.client.common.response.QueryUseInfoResponse;
import com.yyigou.dsrp.cdc.client.customer.request.*;
import com.yyigou.dsrp.cdc.client.customer.response.CustomerComponentResponse;
import com.yyigou.dsrp.cdc.client.customer.response.CustomerInfoResponse;
import com.yyigou.dsrp.cdc.client.customer.response.CustomerResponse;
import com.yyigou.dsrp.cdc.client.customer.response.response.CompanyLinkmanResponse;
import com.yyigou.dsrp.cdc.common.enums.*;
import com.yyigou.dsrp.cdc.common.enums.company.InstitutionalTypeEnum;
import com.yyigou.dsrp.cdc.common.enums.customer.*;
import com.yyigou.dsrp.cdc.common.enums.supplier.SupplierBusinessFlagEnum;
import com.yyigou.dsrp.cdc.common.util.BeanUtil;
import com.yyigou.dsrp.cdc.common.util.CommonUtil;
import com.yyigou.dsrp.cdc.common.util.OperationModel;
import com.yyigou.dsrp.cdc.dao.common.BankTypeDAO;
import com.yyigou.dsrp.cdc.dao.common.entity.BankType;
import com.yyigou.dsrp.cdc.dao.company.CompanyDAO;
import com.yyigou.dsrp.cdc.dao.company.CompanyLinkmanDAO;
import com.yyigou.dsrp.cdc.dao.company.CompanyShippingAddressDAO;
import com.yyigou.dsrp.cdc.dao.company.entity.Company;
import com.yyigou.dsrp.cdc.dao.company.entity.CompanyLinkman;
import com.yyigou.dsrp.cdc.dao.company.entity.CompanyShippingAddress;
import com.yyigou.dsrp.cdc.dao.customer.*;
import com.yyigou.dsrp.cdc.dao.customer.entity.*;
import com.yyigou.dsrp.cdc.dao.gradedcontrol.SupplierCustomerUseInfoDAO;
import com.yyigou.dsrp.cdc.dao.gradedcontrol.entity.SupplierCustomerUseInfo;
import com.yyigou.dsrp.cdc.dao.supplier.CompanyBankDAO;
import com.yyigou.dsrp.cdc.dao.supplier.SupplierDAO;
import com.yyigou.dsrp.cdc.dao.supplier.entity.CompanyBank;
import com.yyigou.dsrp.cdc.dao.supplier.entity.Supplier;
import com.yyigou.dsrp.cdc.manager.integration.certControl.CertControlService;
import com.yyigou.dsrp.cdc.manager.integration.dict.DictEnterpriseService;
import com.yyigou.dsrp.cdc.manager.integration.dict.enums.DictNumberEnum;
import com.yyigou.dsrp.cdc.manager.integration.numberCenter.NumberCenterService;
import com.yyigou.dsrp.cdc.manager.integration.org.UimTenantService;
import com.yyigou.dsrp.cdc.manager.integration.uim.*;
import com.yyigou.dsrp.cdc.manager.integration.uim.response.CustomDocResponse;
import com.yyigou.dsrp.cdc.manager.integration.uim.response.TransactionTypeResponse;
import com.yyigou.dsrp.cdc.model.constant.NumberCenterConstant;
import com.yyigou.dsrp.cdc.model.constant.SystemConstant;
import com.yyigou.dsrp.cdc.model.constant.ViewNameConstant;
import com.yyigou.dsrp.cdc.model.customer.req.CustomerListPageReq;
import com.yyigou.dsrp.cdc.service.common.CdcLogService;
import com.yyigou.dsrp.cdc.service.common.MasterDataSyncExtService;
import com.yyigou.dsrp.cdc.service.company.CompanyService;
import com.yyigou.dsrp.cdc.service.customer.CustomerService;
import com.yyigou.dsrp.cdc.service.utils.PageUtils;
import com.yyigou.dsrp.gcs.common.enums.StatusEnum;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 客户表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-16
 */
@Service
@RequiredArgsConstructor
public class CustomerServiceImpl extends ServiceImpl<CustomerDAO, Customer> implements CustomerService {
    private final CompanyDAO companyDAO;
    private final CustomerDAO customerDAO;
    private final CompanyLinkmanDAO companyLinkmanDAO;
    private final CustomerCategoryDAO customerCategoryDAO;
    private final SupplierCustomerUseInfoDAO supplierCustomerUseInfoDAO;
    private final NumberCenterService numberCenterService;
    private final CustomerInvoiceDAO customerInvoiceDAO;
    private final CustomerSalesManDAO customerSalesManDAO;
    private final CompanyShippingAddressDAO companyShippingAddressDAO;
    private final CustomDocService customDocService;
    private final CompanyBankDAO companyBankDAO;
    private final CompanyService companyService;
    private final DictEnterpriseService dictEnterpriseService;
    private final CustomerAreaDAO customerAreaDAO;
    private final BankTypeDAO bankTypeDAO;
    private final TenantService tenantService;
    private final MasterDataSyncExtService masterDataSyncExtService;
    private final CdcLogService ccLogService;
    private final TransactionTypeService transactionTypeService;
    private final EmployeeService employeeService;
    private final UimTenantService uimTenantService;
    private final SupplierDAO supplierDAO;
    private final CertControlService certControlService;
    private final OrganizationService organizationService;

    /**
     * 根据客户名称查询客户信息
     *
     * @param request
     * @return: {@link CustomerInfoResponse}
     */
    @Override
    public List<CustomerInfoResponse> findCustomerByName(CustomerNameRequest request) {
        CommonUtil.checkEmptyThrowEx(request.getEnterpriseNo(), "租户编号不能为空");
        if (StringUtils.isBlank(request.getCustomerName()) && StringUtils.isBlank(request.getCustomerNameKeyword())) {
            throw new BusinessException("客户名称不能为空");
        }
        LambdaQueryWrapper<Customer> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(Customer::getEnterpriseNo, request.getEnterpriseNo())
                .and(
                        i -> i.like(StringUtils.isNotBlank(request.getCustomerNameKeyword()), Customer::getCustomerNo, request.getCustomerNameKeyword())
                                .or().like(StringUtils.isNotBlank(request.getCustomerNameKeyword()), Customer::getCustomerName, request.getCustomerNameKeyword())
                                .or().like(StringUtils.isNotBlank(request.getCustomerNameKeyword()), Customer::getCustomerCode, request.getCustomerNameKeyword())
                                .or().like(StringUtils.isNotBlank(request.getCustomerNameKeyword()), Customer::getMnemonicCode, request.getCustomerNameKeyword())
                )
                .eq(StringUtils.isNotBlank(request.getCustomerName()), Customer::getCustomerName, request.getCustomerName())
                .eq(Customer::getControlStatus, ControlStatusEnum.ENABLE.getValue())
                .eq(Customer::getBusinessFlag, CustomerBusinessFlagEnum.FORMAL.getValue()).eq(Customer::getDeleted, DeletedEnum.UN_DELETE.getValue());
        List<Customer> customerList = customerDAO.selectList(lambdaQueryWrapper);
        if (CollectionUtils.isEmpty(customerList)) {
            return Collections.emptyList();
        }
        return customerList.stream().map(customer -> {
            CustomerInfoResponse response = new CustomerInfoResponse();
            BeanUtils.copyProperties(customer, response);
            return response;
        }).collect(Collectors.toList());
    }

    /**
     * 根据客户编号集合查询客户信息
     *
     * @param request
     * @return: {@link List< CustomerInfoResponse>}
     */
    @Override
    public List<CustomerInfoResponse> findCustomerByNo(CustomerNoRequest request) {
        CommonUtil.checkEmptyThrowEx(request.getEnterpriseNo(), "租户编号不能为空");
        if (StringUtils.isBlank(request.getCustomerNo()) && StringUtils.isBlank(request.getCustomerCode())
                && CollectionUtils.isEmpty(request.getCustomerNoList()) && CollectionUtils.isEmpty(request.getCustomerCodeList())) {
            throw new BusinessException("客户编码不能为空");
        }
        LambdaQueryWrapper<Customer> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(Customer::getEnterpriseNo, request.getEnterpriseNo())
                .eq(StringUtils.isNotBlank(request.getCustomerNo()), Customer::getCustomerNo, request.getCustomerNo())
                .eq(StringUtils.isNotBlank(request.getCustomerCode()), Customer::getCustomerCode, request.getCustomerCode())
                .in(CollectionUtils.isNotEmpty(request.getCustomerNoList()), Customer::getCustomerNo, request.getCustomerNoList())
                .in(CollectionUtils.isNotEmpty(request.getCustomerCodeList()), Customer::getCustomerCode, request.getCustomerCodeList())
                .eq(Customer::getControlStatus, ControlStatusEnum.ENABLE.getValue())
//                .eq(Customer::getBusinessFlag, CustomerBusinessFlagEnum.FORMAL.getValue())
                .eq(Customer::getDeleted, DeletedEnum.UN_DELETE.getValue());
        List<Customer> customerList = customerDAO.selectList(lambdaQueryWrapper);
        if (CollectionUtils.isEmpty(customerList)) {
            return Collections.emptyList();
        }
        return customerList.stream().map(customer -> {
            CustomerInfoResponse response = new CustomerInfoResponse();
            BeanUtils.copyProperties(customer, response);
            return response;
        }).collect(Collectors.toList());
    }

    /**
     * 根据价格体系查询客户信息
     *
     * @param request
     * @return: {@link List<CustomerInfoResponse>}
     */
    @Override
    public List<CustomerInfoResponse> findCustomerByPriceCate(CustomerPriceCateRequest request) {
        CommonUtil.checkEmptyThrowEx(request.getEnterpriseNo(), "租户编号不能为空");
        if (CollectionUtils.isEmpty(request.getPriceCategoryCodeList())) {
            throw new BusinessException("价格体系不能为空");
        }
        LambdaQueryWrapper<Customer> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(Customer::getEnterpriseNo, request.getEnterpriseNo())
                .in(Customer::getPriceCategoryCode, request.getPriceCategoryCodeList())
                .eq(Customer::getControlStatus, ControlStatusEnum.ENABLE.getValue())
                .eq(Customer::getDeleted, DeletedEnum.UN_DELETE.getValue());
        List<Customer> customerList = customerDAO.selectList(lambdaQueryWrapper);
        if (CollectionUtils.isEmpty(customerList)) {
            return Collections.emptyList();
        }
        return customerList.stream().map(customer -> {
            CustomerInfoResponse response = new CustomerInfoResponse();
            BeanUtils.copyProperties(customer, response);
            return response;
        }).collect(Collectors.toList());
    }


    /**
     * 按条件查询客户信息
     *
     * @param enterpriseNo
     * @param request
     * @return
     */
    @Override
    public List<CustomerResponse> findCustomer(String enterpriseNo, CustomerFindRequest request) {
        CommonUtil.checkEmptyThrowEx(enterpriseNo, "租户编号不得为空");

        LambdaQueryWrapper<Customer> lambdaQueryWrapper = Wrappers.lambdaQuery(Customer.class)
                .eq(Customer::getEnterpriseNo, enterpriseNo)
                .eq(Customer::getDeleted, 0);

        if (CollectionUtils.isNotEmpty(request.getCustomerNoList())) {
            lambdaQueryWrapper = lambdaQueryWrapper.in(Customer::getCustomerNo, request.getCustomerNoList());
        }
        if (StringUtils.isNotEmpty(request.getCustomerName())) {
            lambdaQueryWrapper = lambdaQueryWrapper.eq(Customer::getCustomerName, request.getCustomerName());
        }
        if (StringUtils.isNotEmpty(request.getCustomerCode())) {
            lambdaQueryWrapper = lambdaQueryWrapper.eq(Customer::getCustomerCode, request.getCustomerCode());
        }
        if (CollectionUtils.isNotEmpty(request.getCustomerCodeList())) {
            lambdaQueryWrapper = lambdaQueryWrapper.in(Customer::getCustomerCode, request.getCustomerCodeList());
        }
        if (CollectionUtils.isNotEmpty(request.getPriceCategoryCodeList())) {
            lambdaQueryWrapper = lambdaQueryWrapper.in(Customer::getPriceCategoryCode, request.getPriceCategoryCodeList());
        }

        if (StringUtils.isNotEmpty(request.getCustomerNameKeyword())) {
            lambdaQueryWrapper = lambdaQueryWrapper.and(o -> o.like(Customer::getCustomerName, request.getCustomerNameKeyword()));
        }
        if (StringUtils.isNotEmpty(request.getCustomerKeywords())) {
            lambdaQueryWrapper = lambdaQueryWrapper.and(
                    o -> o.like(Customer::getCustomerName, request.getCustomerKeywords()).or()
                            .like(Customer::getCustomerCode, request.getCustomerKeywords()).or()
                            .like(Customer::getCustomerNo, request.getCustomerKeywords())
            );
        }
        List<Customer> customers = customerDAO.selectList(lambdaQueryWrapper);
        return BeanUtil.copyFieldsList(customers, CustomerResponse.class);
    }

    @Override
    public List<String> findCustomerUserList(String enterpriseNo, String customerCode) {
        CommonUtil.checkEmptyThrowEx(enterpriseNo, "租户编号不能为空");
        CommonUtil.checkEmptyThrowEx(customerCode, "客户编码不能为空");
        final String groupEnterpriseNo = tenantService.getGroupEnterpriseNo(enterpriseNo);
        List<SupplierCustomerUseInfo> usedSkuCodeList = supplierCustomerUseInfoDAO.findGroupSupplierCustomerUseList(groupEnterpriseNo, Lists.newArrayList(customerCode), CompanyTypeEnum.CUSTOMER.getValue());
        return usedSkuCodeList.stream().map(SupplierCustomerUseInfo::getUseEnterpriseNo).collect(Collectors.toList());
    }


    /**
     * 保存客户信息
     * 1.检查客户编码是否重复
     * 2.检查客户名称是否重复
     * 3.检查企业是否重复
     *
     * @param params
     * @return
     */
    @Override
    public String saveCustomer(OperationModel operationModel, SaveCustomerDTO params, String logRecord) {
        ValidatorUtils.checkTrueThrowEx(customerDAO.selectCount(new LambdaQueryWrapper<Customer>().eq(Customer::getCustomerCode, params.getCustomerCode()).eq(Customer::getEnterpriseNo, operationModel.getEnterpriseNo()).eq(Customer::getDeleted, DeletedEnum.UN_DELETE.getValue())) > 0, "客户编码重复");
        ValidatorUtils.checkTrueThrowEx(customerDAO.selectCount(new LambdaQueryWrapper<Customer>().eq(Customer::getCustomerName, params.getCustomerName()).eq(Customer::getEnterpriseNo, operationModel.getEnterpriseNo()).eq(Customer::getDeleted, DeletedEnum.UN_DELETE.getValue())) > 0, "客户名称重复");

        //先处理企业
//        通过企业名称和统一社会信用代码判断企业档案是否存在
//        1、不存在企业档案，创建客户档案和企业档案，并做关联
//        2、存在企业档案，创建客户档案并关联企业档案
//        3、企业名称或者统一社会信用代码不一致，校验报错

        Company company = new Company();
        List<Company> companyList = companyDAO.findByEnterpriseNoAndUnifiedSocialCodeAndCompanyName(operationModel.getEnterpriseNo(), params.getUnifiedSocialCode(), params.getCompanyName());
        if (CollectionUtils.isNotEmpty(companyList)) {
            company = companyList.get(0);
            company.setCountry(params.getCountry());
            company.setFactoryType(params.getFactoryType());
            company.setEconomicType(params.getEconomicType());
            company.setTaxCategory(params.getTaxCategory());
            company.setIsAssociatedEnterprise(params.getIsAssociatedEnterprise());
            company.setIsMedicalInstitution(params.getIsMedicalInstitution());
//            company.setIsMedicalInstitution(params.getIsMedicalInstitution());
            company.setInstitutionalType(params.getInstitutionalType());
            company.setHospitalType(params.getHospitalType());
            company.setHospitalClass(params.getHospitalClass());
            company.setAssociatedOrgNo(params.getAssociatedOrgNo());
            company.setAssociatedOrgCode(params.getAssociatedOrgCode());
            company.setAssociatedOrgName(params.getAssociatedOrgName());
            companyDAO.updateById(company);
        } else {
            //不存在有2中情况 1.企业存在 2.统一社会信用代码存在  3.都不存在得新创建了
            if (StringUtils.isNotEmpty(params.getUnifiedSocialCode())) {
                //统一社会信用代码存在
                companyList = companyDAO.findByEnterpriseNoAndUnifiedSocialCode(operationModel.getEnterpriseNo(), params.getUnifiedSocialCode());
                if (CollectionUtils.isNotEmpty(companyList)) {
                    company = companyList.get(0);
                    CommonUtil.checkThrowEx(CollectionUtils.isNotEmpty(companyList), String.format("企业名称和统一社会信用代码重复,企业名称:%s,统一社会信用代码:%s", company.getCompanyName(), company.getUnifiedSocialCode()));
                }
                companyList = companyDAO.findByEnterpriseNoAndCompanyName(operationModel.getEnterpriseNo(), params.getCompanyName());
                if (CollectionUtils.isNotEmpty(companyList)) {
                    company = companyList.get(0);
                    CommonUtil.checkThrowEx(CollectionUtils.isNotEmpty(companyList), String.format("企业名称和统一社会信用代码重复,企业名称:%s,统一社会信用代码:%s", company.getCompanyName(), company.getUnifiedSocialCode()));
                }
                //创建企业档案
                company.setEnterpriseNo(operationModel.getEnterpriseNo());
                company.setCompanyNo(numberCenterService.createNumber(NumberCenterConstant.COMPANY_NO_GENERATE_KEY));
                company.setCompanyCode(company.getCompanyCode());
                company.setCompanyName(params.getCompanyName());
                company.setUnifiedSocialCode(params.getUnifiedSocialCode());
                company.setFactoryType(params.getFactoryType());
                company.setCountry(params.getCountry());
                company.setTaxCategory(params.getTaxCategory());
                company.setEconomicType(params.getEconomicType());
                company.setIsAssociatedEnterprise(params.getIsAssociatedEnterprise());
                company.setAssociatedOrgNo(params.getAssociatedOrgNo());
                company.setAssociatedOrgCode(params.getAssociatedOrgCode());
                company.setAssociatedOrgName(params.getAssociatedOrgName());
                company.setIsMedicalInstitution(params.getIsMedicalInstitution());
                company.setInstitutionalType(params.getInstitutionalType());
                company.setHospitalType(params.getHospitalType());
                company.setHospitalClass(params.getHospitalClass());
                CommonUtil.fillCreatInfo(operationModel, company);
                company.setStatus(StatusEnum.EFFECTIVE.getValue());
                company.setDeleted(DeletedEnum.UN_DELETE.getValue());
                companyDAO.insert(company);
            }
        }

        Customer customer = new Customer();
        customer.setCompanyNo(company.getCompanyNo());
        customer.setCustomerNo(numberCenterService.createNumber(NumberCenterConstant.CUSTOMER_NO_KEY));
        customer.setEnterpriseNo(operationModel.getEnterpriseNo());
        customer.setUnifiedSocialCode(params.getUnifiedSocialCode());
        // 客户基本信息
        customer.setCustomerCode(params.getCustomerCode());
        customer.setCustomerName(params.getCustomerName());
        customer.setCustomerCategoryNo(params.getCustomerCategoryNo());
        customer.setMnemonicCode(params.getMnemonicCode());
        customer.setCustomerNameEn(params.getCustomerNameEn());
        customer.setOwnerCompany(params.getOwnerCompany());
        customer.setIsGspControl(params.getGspStatus());
        customer.setInstitutionalType(params.getInstitutionalType());
        customer.setHospitalType(params.getHospitalType());
        customer.setHospitalClass(params.getHospitalClass());
        customer.setRemark(params.getRemark());
        customer.setTransactionType(params.getTransactionType());
        customer.setRetailInvestors(params.getRetailInvestors());

        //业务信息
        customer.setControlId(params.getControlId());
        customer.setControlTypeName(params.getControlTypeName());
        customer.setCooperationMode(params.getCooperationMode());
        customer.setBusinessType(params.getBusinessType());
        customer.setCurrency(params.getCurrency());
        customer.setSettlementModes(params.getSettlementModes());
        customer.setSettlementModesName(params.getSettlementModesName());
        customer.setPaymentAgreement(params.getPaymentAgreement());
        customer.setPaymentCondition(params.getPaymentCondition());
        customer.setPriceCategoryCode(params.getPriceCategoryCode());
        customer.setCreditAmount(params.getCreditAmount());
        customer.setCreditDates(params.getCreditDates());
        customer.setCoopStartTime(params.getCoopStartTime());
        customer.setCoopEndTime(params.getCoopEndTime());
        customer.setBusinessFlag(CustomerBusinessFlagEnum.DRAFT.getValue().equals(params.getBusinessFlag()) ? CustomerBusinessFlagEnum.DRAFT.getValue() : CustomerBusinessFlagEnum.FORMAL.getValue());
        customer.setDeleted(DeletedEnum.UN_DELETE.getValue());
        customer.setCreateNo(operationModel.getEmployerNo());
        customer.setCreateName(operationModel.getUserName());
        customer.setCreateTime(DateUtil.getCurrentDate());
        customer.setIsGspControl(1);

        //业务区域
        customerDAO.insert(customer);
        //客户联系人
        if (CollectionUtils.isNotEmpty(params.getLinkmanList())) {
            List<CompanyLinkman> linkmanList = new ArrayList<>();
            params.getLinkmanList().forEach(t -> {
                CompanyLinkman companyLinkman = new CompanyLinkman();
                companyLinkman.setCompanyNo(customer.getCompanyNo());
                companyLinkman.setEnterpriseNo(operationModel.getEnterpriseNo());
                companyLinkman.setLinkman(t.getLinkman());
                companyLinkman.setPosition(t.getPosition());
                companyLinkman.setMobilePhone(t.getMobilePhone());
                companyLinkman.setFixedPhone(t.getFixedPhone());
                companyLinkman.setSex(t.getSex());
                companyLinkman.setEmail(t.getEmail());
                companyLinkman.setQq(t.getQq());
                companyLinkman.setWx(t.getWx());
                companyLinkman.setStatus(t.getStatus());
                companyLinkman.setIsDefault(t.getIsDefault());
                companyLinkman.setSourceNo(customer.getCustomerNo());
                companyLinkman.setDeleted(DeletedEnum.UN_DELETE.getValue());
                CommonUtil.fillCreatInfo(operationModel, companyLinkman);
                CommonUtil.fillOperateInfo(operationModel, companyLinkman);
                companyLinkman.setLinkmanType(LinkmanTypeEnum.SALE.getValue());
                linkmanList.add(companyLinkman);
            });
            companyLinkmanDAO.addBatch(linkmanList);
        }
        //开票信息
        if (CollectionUtils.isNotEmpty(params.getInvoiceList())) {
            List<CustomerInvoice> customerInvoices = new ArrayList<>();
            params.getInvoiceList().forEach(t -> {
                CustomerInvoice customerInvoice = new CustomerInvoice();
                customerInvoice.setEnterpriseNo(operationModel.getEnterpriseNo());
                customerInvoice.setCustomerNo(customer.getCustomerNo());
                customerInvoice.setType(t.getType());
                customerInvoice.setTaxNo(t.getTaxNo());
                customerInvoice.setInvoiceTitle(t.getInvoiceTitle());
                customerInvoice.setPhone(t.getPhone());
                customerInvoice.setInvoicePhone(t.getInvoicePhone());
                customerInvoice.setEmail(t.getEmail());
                customerInvoice.setAddress(t.getAddress());
                customerInvoice.setBankDeposit(t.getBankDeposit());
                customerInvoice.setBankAccount(t.getBankAccount());
                customerInvoice.setStatus(CommonStatusEnum.EFFECTIVE.getValue().toString());
                customerInvoice.setIsDefault(t.getIsDefault());
                customerInvoice.setRequirement(t.getRequirement());
                CommonUtil.fillOperateInfo(operationModel, customerInvoice);
                customerInvoices.add(customerInvoice);
            });
            customerInvoiceDAO.addBatch(customerInvoices);
        }
        //负责人信息
        if (CollectionUtils.isNotEmpty(params.getSalesManList())) {
            List<CustomerSalesMan> customerSalesManList = new ArrayList<>();
            params.getSalesManList().forEach(t -> {
                CustomerSalesMan customerSalesMan = new CustomerSalesMan();
                customerSalesMan.setCustomerNo(customer.getCustomerNo());
                customerSalesMan.setOrgNo(t.getDeptNo());
                customerSalesMan.setOrgName(t.getDeptName());
                customerSalesMan.setSalesManNo(t.getSalesManNo());
                customerSalesMan.setSalesManName(t.getSalesManName());
                customerSalesMan.setIsDefault(t.getIsDefault());
                customerSalesMan.setOrderSpecialist(t.getOrderSpecialist());
                customerSalesMan.setPost(t.getPost());
                customerSalesMan.setDeleted(DeletedEnum.UN_DELETE.getValue());
                CommonUtil.fillOperateInfo(operationModel, customerSalesMan);
                customerSalesManList.add(customerSalesMan);
            });
            customerSalesManDAO.addBatch(customerSalesManList);
        }
        //联系地址
        if (CollectionUtils.isNotEmpty(params.getLinkAddressList())) {
            List<CompanyShippingAddress> addressList = new ArrayList<>();
            params.getLinkAddressList().forEach(t -> {
                CompanyShippingAddress companyShippingAddress = new CompanyShippingAddress();
                companyShippingAddress.setEnterpriseNo(operationModel.getEnterpriseNo());
                companyShippingAddress.setCompanyNo(customer.getCompanyNo());
                companyShippingAddress.setSourceNo(customer.getCustomerNo());
                companyShippingAddress.setReceiveUser(t.getReceiveUser());
                companyShippingAddress.setReceivePhone(t.getReceivePhone());
                companyShippingAddress.setRegionCode(t.getRegionCode());
                companyShippingAddress.setRegionName(t.getRegionName());
                companyShippingAddress.setReceiveAddr(t.getReceiveAddr());
                companyShippingAddress.setIsDefault(t.getIsDefault());
                companyShippingAddress.setStatus(CommonStatusEnum.EFFECTIVE.getValue());
                companyShippingAddress.setAddressDesc(t.getAddressDesc());
                companyShippingAddress.setAddressType(t.getAddressType());
                companyShippingAddress.setLinkaddType(LinkmanTypeEnum.SALE.getValue());
                CommonUtil.fillCreatInfo(operationModel, companyShippingAddress);
                CommonUtil.fillOperateInfo(operationModel, companyShippingAddress);
                addressList.add(companyShippingAddress);
            });
            companyShippingAddressDAO.addBatch(addressList);
        }
        //银行信息
        if (CollectionUtils.isNotEmpty(params.getBankList())) {
            List<CompanyBank> companyBankList = new ArrayList<>();
            params.getBankList().forEach(b -> {
                CompanyBank companyBank = new CompanyBank();
                companyBank.setEnterpriseNo(operationModel.getEnterpriseNo());
                companyBank.setLinkaddType(LinkmanTypeEnum.SALE.getValue());
                companyBank.setCompanyNo(customer.getCompanyNo());
                companyBank.setSourceNo(customer.getCustomerNo());
                companyBank.setBankCode(b.getBankCode());
                companyBank.setBankType(b.getBankType());
                companyBank.setOpenBank(b.getOpenBank());
                companyBank.setAccountName(b.getAccountName());
                companyBank.setAccountNo(b.getAccountNo());
                companyBank.setAccountType(b.getAccountType());
                companyBank.setLinkPerson(b.getLinkPerson());
                companyBank.setLinkPhone(b.getLinkPhone());
                companyBank.setStatus(b.getStatus());
                companyBank.setIsDefault(b.getIsDefault());
                companyBankList.add(companyBank);
            });
            if (CollectionUtils.isNotEmpty(companyBankList)) {
                companyBankDAO.addBatch(companyBankList);
            }
        }
        //授权信息
        if (CollectionUtils.isNotEmpty(params.getAreaList())) {
            List<CustomerArea> customerAreaList = new ArrayList<>();
            for (SaveCustomerAreaDTO r : params.getAreaList()) {
                CustomerArea customerArea = new CustomerArea();
                customerArea.setCustomerNo(customer.getCustomerNo());
                customerArea.setCustomerName(customer.getCustomerName());
                customerArea.setAreaNo(r.getAreaNo());
                customerArea.setAreaName(r.getAreaName());
                customerArea.setAreaCode(r.getAreaCode());
                customerArea.setEnterpriseNo(operationModel.getEnterpriseNo());
                customerArea.setOperateNo(operationModel.getEmployerNo());
                customerArea.setOperateName(operationModel.getUserName());
                customerArea.setOperateTime(DateUtil.getCurrentDate());
                customerArea.setDeleted(DeletedEnum.UN_DELETE.getValue());
                customerArea.setIsDefault(r.getIsDefault());
                customerArea.setParentAreaNo(r.getParentAreaNo());
                customerAreaList.add(customerArea);
            }
            if (CollectionUtils.isNotEmpty(customerAreaList)) {
                customerAreaDAO.addBatch(customerAreaList);
            }
        }
        // 保存资料文件
        companyService.saveOrUpdateCert(params.getCatalogInfoBigClientReq(), operationModel.getEnterpriseNo(), company.getCompanyNo(), operationModel);
        ccLogService.saveCustomerLog(customer.getCustomerNo(), operationModel, logRecord);
        return customer.getCustomerNo();
    }


    @Override
    public Boolean updateCustomer(OperationModel operationModel, SaveCustomerDTO params) {
        Customer customer = customerDAO.getCustomerByNo(operationModel.getEnterpriseNo(), params.getCustomerNo());
        ValidatorUtils.checkTrueThrowEx(customer == null, "客户不存在");

        final Company company = companyDAO.findByEnterpriseNoAndCompanyNo(operationModel.getEnterpriseNo(), customer.getCompanyNo());
        //更新企业信息
        if (company != null) {
            company.setCountry(params.getCountry());
            company.setFactoryType(params.getFactoryType());
            company.setEconomicType(params.getEconomicType());
            company.setTaxCategory(params.getTaxCategory());
            company.setIsAssociatedEnterprise(params.getIsAssociatedEnterprise());
            company.setIsMedicalInstitution(params.getIsMedicalInstitution());
            company.setInstitutionalType(params.getInstitutionalType());
            company.setHospitalType(params.getHospitalType());
            company.setHospitalClass(params.getHospitalClass());
            company.setAssociatedOrgNo(params.getAssociatedOrgNo());
            company.setAssociatedOrgCode(params.getAssociatedOrgCode());
            company.setAssociatedOrgName(params.getAssociatedOrgName());
            companyDAO.updateById(company);
        }
        customer.setUnifiedSocialCode(params.getUnifiedSocialCode());
        // 客户基本信息
        customer.setCustomerName(params.getCustomerName());
        customer.setCustomerCategoryNo(params.getCustomerCategoryNo());
        customer.setMnemonicCode(params.getMnemonicCode());
        customer.setCustomerNameEn(params.getCustomerNameEn());
        customer.setOwnerCompany(params.getOwnerCompany());
        customer.setIsGspControl(params.getGspStatus());
        customer.setInstitutionalType(params.getInstitutionalType());
        customer.setHospitalType(params.getHospitalType());
        customer.setHospitalClass(params.getHospitalClass());
        customer.setRemark(params.getRemark());
        customer.setTransactionType(params.getTransactionType());
        customer.setRetailInvestors(params.getRetailInvestors());
        //业务信息
        customer.setControlId(params.getControlId());
        customer.setControlTypeName(params.getControlTypeName());
        customer.setCooperationMode(params.getCooperationMode());
        customer.setBusinessType(params.getBusinessType());
        customer.setCurrency(params.getCurrency());
        customer.setSettlementModes(params.getSettlementModes());
        customer.setSettlementModesName(params.getSettlementModesName());
        customer.setPaymentAgreement(params.getPaymentAgreement());
        customer.setPaymentCondition(params.getPaymentCondition());
        customer.setPriceCategoryCode(params.getPriceCategoryCode());
        customer.setCreditAmount(params.getCreditAmount());
        customer.setCreditDates(params.getCreditDates());
        customer.setCoopStartTime(params.getCoopStartTime());
        customer.setCoopEndTime(params.getCoopEndTime());
        customer.setBusinessFlag(params.getBusinessFlag());
        //客户联系人
        if (CollectionUtils.isEmpty(params.getLinkmanList())) {
            //删除联系人
            companyLinkmanDAO.deleteCompanyLinkmanListByCompanyNo(operationModel.getEnterpriseNo(), LinkmanTypeEnum.SALE.getValue(), Collections.singletonList(customer.getCustomerNo()));
        } else {
            //编辑了联系人 说明肯定至少有一个使用uuid决定是否修改或者新增
            List<CompanyLinkman> customerLinkmanList = companyLinkmanDAO.getCompanyLinkmanListByCompanyNo(operationModel.getEnterpriseNo(), customer.getCompanyNo(), LinkmanTypeEnum.SALE.getValue(), customer.getCustomerNo());
            final Map<Long, CompanyLinkman> customerLinkmanMap = customerLinkmanList.stream().collect(Collectors.toMap(CompanyLinkman::getId, Function.identity()));
            final Set<Long> localCustomerIdLinkmanList = customerLinkmanList.stream().map(CompanyLinkman::getId).collect(Collectors.toSet());
            final Set<Long> itemCustomerIdLinkmanList = params.getLinkmanList().stream().map(CompanyLinkmanDTO::getId).collect(Collectors.toSet());
            Set<Long> difference = Sets.difference(localCustomerIdLinkmanList, itemCustomerIdLinkmanList);
            if (!difference.isEmpty()) {
                companyLinkmanDAO.deleteCompanyLinkmanListByCompanyNo(operationModel.getEnterpriseNo(), LinkmanTypeEnum.SALE.getValue(), Collections.singletonList(customer.getCustomerNo()), new ArrayList<>(difference));
            }
            params.getLinkmanList().forEach(r -> {
                if (customerLinkmanMap.containsKey(r.getId())) {
                    final CompanyLinkman companyLinkman = customerLinkmanMap.get(r.getId());
                    companyLinkman.setLinkman(r.getLinkman());
                    companyLinkman.setPosition(r.getPosition());
                    companyLinkman.setMobilePhone(r.getMobilePhone());
                    companyLinkman.setSex(r.getSex());
                    companyLinkman.setFixedPhone(r.getFixedPhone());
                    companyLinkman.setQq(r.getQq());
                    companyLinkman.setWx(r.getWx());
                    companyLinkman.setLinkmanType(LinkmanTypeEnum.SALE.getValue());
                    companyLinkman.setStatus(CommonStatusEnum.EFFECTIVE.getValue());
                    companyLinkman.setSourceNo(customer.getCustomerNo());
                    companyLinkman.setEmail(r.getEmail());
                    companyLinkman.setIsDefault(r.getIsDefault());
                    companyLinkmanDAO.updateById(companyLinkman);
                } else {
                    //应该走新增逻辑
                    CompanyLinkman companyLinkman = new CompanyLinkman();
                    companyLinkman.setEnterpriseNo(operationModel.getEnterpriseNo());
                    companyLinkman.setCompanyNo(customer.getCompanyNo());
                    companyLinkman.setLinkCode(r.getLinkCode());
                    companyLinkman.setLinkman(r.getLinkman());
                    companyLinkman.setPosition(r.getPosition());
                    companyLinkman.setMobilePhone(r.getMobilePhone());
                    companyLinkman.setSex(r.getSex());
                    companyLinkman.setFixedPhone(r.getFixedPhone());
                    companyLinkman.setQq(r.getQq());
                    companyLinkman.setWx(r.getWx());
                    companyLinkman.setLinkmanType(LinkmanTypeEnum.SALE.getValue());
                    companyLinkman.setStatus(CommonStatusEnum.EFFECTIVE.getValue());
                    companyLinkman.setSourceNo(customer.getCustomerNo());
                    companyLinkman.setEmail(r.getEmail());
                    companyLinkman.setIsDefault(r.getIsDefault());
                    companyLinkmanDAO.insert(companyLinkman);
                }
            });
        }
        //联系地址
        if (CollectionUtils.isEmpty(params.getLinkAddressList())) {
            //删除开票信息
            companyShippingAddressDAO.deleteShippingAddressListBySourceNoList(operationModel.getEnterpriseNo(), LinkmanTypeEnum.SALE.getValue(), Collections.singletonList(customer.getCustomerNo()));
        } else {
            final List<CompanyShippingAddress> shippingAddressList = companyShippingAddressDAO.getCompanyShippingAddressByCompanyNo(operationModel.getEnterpriseNo(), customer.getCompanyNo(), LinkmanTypeEnum.SALE.getValue(), customer.getCustomerNo());
            final Map<Long, CompanyShippingAddress> shippingAddressMap = shippingAddressList.stream().collect(Collectors.toMap(CompanyShippingAddress::getId, Function.identity()));

            final Set<Long> localAddressList = shippingAddressList.stream().map(CompanyShippingAddress::getId).collect(Collectors.toSet());
            final Set<Long> itemAddressList = params.getLinkAddressList().stream().map(CompanyShippingAddressDTO::getId).collect(Collectors.toSet());
            Set<Long> difference = Sets.difference(localAddressList, itemAddressList);
            if (!difference.isEmpty()) {
                companyShippingAddressDAO.deleteShippingAddressListBySourceNoList(operationModel.getEnterpriseNo(), LinkmanTypeEnum.SALE.getValue(), Collections.singletonList(customer.getCustomerNo()), new ArrayList<>(difference));
            }
            List<CompanyShippingAddress> saveShippingAddressListList = new ArrayList<>();
            for (CompanyShippingAddressDTO r : params.getLinkAddressList()) {
                if (shippingAddressMap.containsKey(r.getId())) {
                    CompanyShippingAddress shippingAddress = shippingAddressMap.get(r.getId());
                    shippingAddress.setReceiveUser(r.getReceiveUser());
                    shippingAddress.setReceivePhone(r.getReceivePhone());
                    shippingAddress.setRegionCode(r.getRegionCode());
                    shippingAddress.setRegionName(r.getRegionName());
                    shippingAddress.setReceiveAddr(r.getReceiveAddr());
                    shippingAddress.setIsDefault(r.getIsDefault());
                    shippingAddress.setAddressDesc(r.getAddressDesc());
                    shippingAddress.setAddressType(r.getAddressType());
                    companyShippingAddressDAO.updateById(shippingAddress);
                } else {
                    CompanyShippingAddress shippingAddress = new CompanyShippingAddress();
                    shippingAddress.setEnterpriseNo(operationModel.getEnterpriseNo());
                    shippingAddress.setCompanyNo(customer.getCompanyNo());
                    shippingAddress.setLinkAddressCode(r.getLinkAddressCode());
                    shippingAddress.setSourceNo(customer.getCustomerNo());
                    shippingAddress.setStatus(CommonStatusEnum.EFFECTIVE.getValue());
                    shippingAddress.setLinkaddType(LinkmanTypeEnum.SALE.getValue());
                    shippingAddress.setReceiveUser(r.getReceiveUser());
                    shippingAddress.setReceivePhone(r.getReceivePhone());
                    shippingAddress.setRegionCode(r.getRegionCode());
                    shippingAddress.setRegionName(r.getRegionName());
                    shippingAddress.setReceiveAddr(r.getReceiveAddr());
                    shippingAddress.setIsDefault(r.getIsDefault());
                    shippingAddress.setAddressDesc(r.getAddressDesc());
                    shippingAddress.setAddressType(r.getAddressType());
                    saveShippingAddressListList.add(shippingAddress);
                }
            }
            if (CollectionUtils.isNotEmpty(saveShippingAddressListList)) {
                companyShippingAddressDAO.addBatch(saveShippingAddressListList);
            }
        }
        //银行信息
        if (CollectionUtils.isEmpty(params.getBankList())) {
            //删除联系地址
            companyBankDAO.deleteCompanyBankListBySourceNoList(operationModel.getEnterpriseNo(), LinkmanTypeEnum.SALE.getValue(), Collections.singletonList(customer.getCustomerNo()));
        } else {
            final List<CompanyBank> bankList = companyBankDAO.getCompanyBankListBySourceNoList(operationModel.getEnterpriseNo(), LinkmanTypeEnum.SALE.getValue(), Collections.singletonList(customer.getCustomerNo()));
            final Map<Long, CompanyBank> bankMap = bankList.stream().collect(Collectors.toMap(CompanyBank::getId, Function.identity()));

            final Set<Long> localBankList = bankList.stream().map(CompanyBank::getId).collect(Collectors.toSet());
            final Set<Long> itemBankList = params.getBankList().stream().map(BankDTO::getId).collect(Collectors.toSet());
            Set<Long> difference = Sets.difference(localBankList, itemBankList);
            if (!difference.isEmpty()) {
                companyBankDAO.deleteCompanyBankListBySourceNoList(operationModel.getEnterpriseNo(), LinkmanTypeEnum.SALE.getValue(), Collections.singletonList(customer.getCustomerNo()), new ArrayList<>(difference));
            }

            List<CompanyBank> companyBankList = new ArrayList<>();
            params.getBankList().forEach(b -> {
                if (bankMap.containsKey(b.getId())) {
                    CompanyBank companyBank = bankMap.get(b.getId());
                    companyBank.setBankType(b.getBankType());
                    companyBank.setOpenBank(b.getOpenBank());
                    companyBank.setAccountName(b.getAccountName());
                    companyBank.setAccountNo(b.getAccountNo());
                    companyBank.setAccountType(b.getAccountType());
                    companyBank.setLinkPerson(b.getLinkPerson());
                    companyBank.setLinkPhone(b.getLinkPhone());
                    companyBank.setStatus(b.getStatus());
                    companyBankDAO.updateById(companyBank);
                } else {
                    CompanyBank companyBank = new CompanyBank();
                    companyBank.setEnterpriseNo(operationModel.getEnterpriseNo());
                    companyBank.setLinkaddType(LinkmanTypeEnum.SALE.getValue());
                    companyBank.setCompanyNo(customer.getCompanyNo());
                    companyBank.setSourceNo(customer.getCustomerNo());
                    companyBank.setBankCode(b.getBankCode());
                    companyBank.setBankType(b.getBankType());
                    companyBank.setOpenBank(b.getOpenBank());
                    companyBank.setAccountName(b.getAccountName());
                    companyBank.setAccountNo(b.getAccountNo());
                    companyBank.setAccountType(b.getAccountType());
                    companyBank.setLinkPerson(b.getLinkPerson());
                    companyBank.setLinkPhone(b.getLinkPhone());
                    companyBank.setStatus(b.getStatus());
                    companyBank.setIsDefault(b.getIsDefault());
                    companyBankList.add(companyBank);
                }
            });
            if (CollectionUtils.isNotEmpty(companyBankList)) {
                companyBankDAO.addBatch(companyBankList);
            }
        }
        //发票信息
        if (CollectionUtils.isEmpty(params.getInvoiceList())) {
            //删除联系地址
            customerInvoiceDAO.deleteCustomerInvoiceByCustomerNo(operationModel.getEnterpriseNo(), customer.getCustomerNo());
        } else {
            final List<CustomerInvoice> invoiceList = customerInvoiceDAO.getCustomerInvoiceByCustomerNo(operationModel.getEnterpriseNo(), customer.getCustomerNo());
            final Map<Long, CustomerInvoice> invoiceMap = invoiceList.stream().collect(Collectors.toMap(CustomerInvoice::getId, Function.identity()));

            final Set<Long> localInvoiceIdList = invoiceList.stream().map(CustomerInvoice::getId).collect(Collectors.toSet());
            final Set<Long> itemInvoiceIdList = params.getInvoiceList().stream().map(CustomerInvoiceDTO::getId).collect(Collectors.toSet());
            Set<Long> difference = Sets.difference(localInvoiceIdList, itemInvoiceIdList);
            if (!difference.isEmpty()) {
                customerInvoiceDAO.deleteCustomerInvoiceByCustomerNo(operationModel.getEnterpriseNo(), customer.getCustomerNo(), new ArrayList<>(difference));
            }

            List<CustomerInvoice> saveLinkmanListList = new ArrayList<>();
            for (CustomerInvoiceDTO r : params.getInvoiceList()) {
                if (invoiceMap.containsKey(r.getId())) {
                    CustomerInvoice customerInvoice = invoiceMap.get(r.getId());
                    customerInvoice.setType(r.getType());
                    customerInvoice.setTaxNo(r.getTaxNo());
                    customerInvoice.setInvoiceTitle(r.getInvoiceTitle());
                    customerInvoice.setPhone(r.getPhone());
                    customerInvoice.setInvoicePhone(r.getInvoicePhone());
                    customerInvoice.setEmail(r.getEmail());
                    customerInvoice.setAddress(r.getAddress());
                    customerInvoice.setBankDeposit(r.getBankDeposit());
                    customerInvoice.setBankAccount(r.getBankAccount());
                    customerInvoice.setStatus(CommonStatusEnum.EFFECTIVE.getValue().toString());
                    customerInvoice.setIsDefault(r.getIsDefault());
                    customerInvoice.setRequirement(r.getRequirement());
                    customerInvoice.setOutSystemId(r.getInvoiceCode());
                    customerInvoiceDAO.updateById(customerInvoice);
                } else {
                    CustomerInvoice customerInvoice = new CustomerInvoice();
                    customerInvoice.setInvoiceCode(r.getInvoiceCode());
                    customerInvoice.setEnterpriseNo(operationModel.getEnterpriseNo());
                    customerInvoice.setCustomerNo(customer.getCustomerNo());
                    customerInvoice.setType(r.getType());
                    customerInvoice.setTaxNo(r.getTaxNo());
                    customerInvoice.setInvoiceTitle(r.getInvoiceTitle());
                    customerInvoice.setPhone(r.getPhone());
                    customerInvoice.setInvoicePhone(r.getInvoicePhone());
                    customerInvoice.setEmail(r.getEmail());
                    customerInvoice.setAddress(r.getAddress());
                    customerInvoice.setBankDeposit(r.getBankDeposit());
                    customerInvoice.setBankAccount(r.getBankAccount());
                    customerInvoice.setStatus(CommonStatusEnum.EFFECTIVE.getValue().toString());
                    customerInvoice.setIsDefault(r.getIsDefault());
                    customerInvoice.setRequirement(r.getRequirement());
                    customerInvoice.setOutSystemId(r.getInvoiceCode());
                    saveLinkmanListList.add(customerInvoice);
                }
            }
            if (CollectionUtils.isNotEmpty(saveLinkmanListList)) {
                customerInvoiceDAO.addBatch(saveLinkmanListList);
            }
        }
        //负责人信息
        if (CollectionUtils.isEmpty(params.getSalesManList())) {
            //删除联系地址
            customerSalesManDAO.deleteCustomerSalesManByCustomerNoList(operationModel.getEnterpriseNo(), Collections.singletonList(customer.getCustomerNo()));
        } else {
            final List<CustomerSalesMan> salesManList = customerSalesManDAO.getCustomerSalesManByCustomerNoList(operationModel.getEnterpriseNo(), Collections.singletonList(customer.getCustomerNo()));
            final Map<Long, CustomerSalesMan> salesManMap = salesManList.stream().collect(Collectors.toMap(CustomerSalesMan::getId, Function.identity()));

            final Set<Long> localSalesManIdList = salesManList.stream().map(CustomerSalesMan::getId).collect(Collectors.toSet());
            final Set<Long> itemSalesManIdList = params.getSalesManList().stream().map(CustomerSalesManDTO::getId).collect(Collectors.toSet());
            Set<Long> difference = Sets.difference(localSalesManIdList, itemSalesManIdList);
            if (!difference.isEmpty()) {
                customerSalesManDAO.deleteCustomerSalesManByCustomerNoList(operationModel.getEnterpriseNo(), Collections.singletonList(customer.getCustomerNo()), new ArrayList<>(difference));
            }

            List<CustomerSalesMan> saveLinkmanListList = new ArrayList<>();
            for (CustomerSalesManDTO r : params.getSalesManList()) {
                if (salesManMap.containsKey(r.getId())) {
                    CustomerSalesMan customerSalesMan = salesManMap.get(r.getId());
                    customerSalesMan.setSalesManNo(r.getSalesManNo());
                    customerSalesMan.setSalesManName(r.getSalesManName());
                    customerSalesMan.setOrgNo(r.getDeptNo());
                    customerSalesMan.setOrgName(r.getDeptName());
                    customerSalesMan.setPost(r.getPost());
                    customerSalesMan.setIsDefault(r.getIsDefault());
                    customerSalesMan.setOrderSpecialist(r.getOrderSpecialist());
                    customerSalesMan.setPost(r.getPost());
                    customerSalesManDAO.updateById(customerSalesMan);
                } else {
                    CustomerSalesMan customerSalesMan = new CustomerSalesMan();
                    customerSalesMan.setCustomerNo(customer.getCustomerNo());
                    customerSalesMan.setManCode(UUID.randomUUID().toString());
                    customerSalesMan.setSalesManNo(r.getSalesManNo());
                    customerSalesMan.setSalesManName(r.getSalesManName());
                    customerSalesMan.setOrgNo(r.getDeptNo());
                    customerSalesMan.setOrgName(r.getDeptName());
                    customerSalesMan.setPost(r.getPost());
                    customerSalesMan.setIsDefault(r.getIsDefault());
                    customerSalesMan.setOrderSpecialist(r.getOrderSpecialist());
                    customerSalesMan.setPost(r.getPost());
                    saveLinkmanListList.add(customerSalesMan);
                }
            }
            if (CollectionUtils.isNotEmpty(saveLinkmanListList)) {
                customerSalesManDAO.addBatch(saveLinkmanListList);
            }
        }
        //授权区域
        if (CollectionUtils.isEmpty(params.getAreaList())) {
            customerAreaDAO.deleteAreaByCustomerNo(operationModel.getEnterpriseNo(), customer.getCustomerNo());
        } else {
            final List<CustomerArea> areaList = customerAreaDAO.getCustomerAreaByCustomerNo(operationModel.getEnterpriseNo(), customer.getCustomerNo());
            final Map<String, CustomerArea> areaMap = areaList.stream().collect(Collectors.toMap(CustomerArea::getAreaNo, Function.identity(), (k, v) -> k));

            final Set<String> localAreaNoList = areaList.stream().map(CustomerArea::getAreaNo).collect(Collectors.toSet());
            final Set<String> itemAreaNoList = params.getAreaList().stream().map(SaveCustomerAreaDTO::getAreaNo).collect(Collectors.toSet());
            Set<String> difference = Sets.difference(localAreaNoList, itemAreaNoList);
            if (!difference.isEmpty()) {
                customerAreaDAO.deleteAreaByCustomerNo(operationModel.getEnterpriseNo(), customer.getCustomerNo(), new ArrayList<>(difference));
            }
            List<CustomerArea> customerAreaList = new ArrayList<>();
            for (SaveCustomerAreaDTO r : params.getAreaList()) {
                if (areaMap.containsKey(r.getAreaNo())) {
                    CustomerArea customerArea = areaMap.get(r.getAreaNo());
                    customerArea.setAreaNo(r.getAreaNo());
                    customerArea.setAreaName(r.getAreaName());
                    customerArea.setAreaCode(r.getAreaCode());
                    customerArea.setEnterpriseNo(operationModel.getEnterpriseNo());
                    customerArea.setOperateNo(operationModel.getEmployerNo());
                    customerArea.setOperateName(operationModel.getUserName());
                    customerArea.setOperateTime(DateUtil.getCurrentDate());
                    customerArea.setIsDefault(r.getIsDefault());
                    customerArea.setParentAreaNo(r.getParentAreaNo());
                    customerAreaDAO.updateById(customerArea);
                } else {
                    CustomerArea customerArea = new CustomerArea();
                    customerArea.setCustomerNo(customer.getCustomerNo());
                    customerArea.setCustomerName(customer.getCustomerName());
                    customerArea.setAreaNo(r.getAreaNo());
                    customerArea.setAreaName(r.getAreaName());
                    customerArea.setAreaCode(r.getAreaCode());
                    customerArea.setEnterpriseNo(operationModel.getEnterpriseNo());
                    customerArea.setOperateNo(operationModel.getEmployerNo());
                    customerArea.setOperateName(operationModel.getUserName());
                    customerArea.setOperateTime(DateUtil.getCurrentDate());
                    customerArea.setDeleted(DeletedEnum.UN_DELETE.getValue());
                    customerArea.setIsDefault(r.getIsDefault());
                    customerArea.setParentAreaNo(r.getParentAreaNo());
                    customerAreaList.add(customerArea);
                }
            }
            if (CollectionUtils.isNotEmpty(customerAreaList)) {
                customerAreaDAO.addBatch(customerAreaList);
            }
        }
        // 保存资料文件
        companyService.saveOrUpdateCert(params.getCatalogInfoBigClientReq(), operationModel.getEnterpriseNo(), company.getCompanyNo(), operationModel);

        customer.setYsSyncFlag("0");
        customer.setIsSyncScs(0);
        customer.setIsSyncWms(0);
        customer.setTcSyncFlag(0);
        customer.setWmsPushResult("");
        customer.setYsPushResult("");
        customer.setScsPushResult("");
        customerDAO.updateById(customer);


        //判定如果是集团租户
        TenantVo tenantVo = tenantService.getEnterpriseInfo(operationModel.getEnterpriseNo());

        if (TenantTypeEnum.GROUP.getValue().equals(tenantVo.getTenantType())) {
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                @Override
                public void afterCommit() {
                    // 如果是集团租户更新，异步更新有使用关系的子租户
                    masterDataSyncExtService.updateSyncSubTenantCustomer(customer.getCustomerCode(), customer, operationModel);
                }
            });
        }
        ccLogService.saveCustomerLog(customer.getCustomerNo(), operationModel, "编辑客户档案");
        return Boolean.TRUE;
    }


    @Override
    public Boolean deleteCustomer(OperationModel operationModel, List<String> customerNoList) {
        List<Customer> customerList = customerDAO.getCustomerByNoList(operationModel.getEnterpriseNo(), customerNoList);
        ValidatorUtils.checkTrueThrowEx(CollectionUtils.isEmpty(customerList), "客户不存在");
        final Optional<Customer> anyCustomer = customerList.stream().filter(customer -> CustomerBusinessFlagEnum.FORMAL.getValue().equals(customer.getBusinessFlag())).findAny();
        if (anyCustomer.isPresent()) {
            ValidatorUtils.checkTrueThrowEx(anyCustomer.isPresent(), "正式客户" + anyCustomer.get().getCustomerName() + "不允许删除");
        }
        customerList.forEach(customer -> {
            customer.setDeleted(DeletedEnum.DELETED.getValue());
            customer.setOperateTime(DateUtil.getCurrentDate());
            customer.setOperateNo(operationModel.getEmployerNo());
            customer.setOperateName(operationModel.getUserName());
            customerDAO.updateById(customer);
            ccLogService.saveCustomerLog(customer.getCustomerNo(), operationModel, "删除客户");
        });

        return Boolean.TRUE;
    }

    @Override
    public CustomerVO getCustomer(String enterpriseNo, String customerNo) {
        Customer customer = customerDAO.getCustomerByNo(enterpriseNo, customerNo);
        ValidatorUtils.checkTrueThrowEx(customer == null, "客户不存在");
        CustomerVO customerVO = new CustomerVO();
        BeanUtils.copyProperties(customer, customerVO);
        customerVO.setRetailInvestorsName(CommonIfEnum.getNameByValue(customer.getRetailInvestors()));
        customerVO.setControlStatusName(CustomerControlStatusEnum.getByType(customer.getControlStatus()) == null ? null : CustomerControlStatusEnum.getByType(customer.getControlStatus()).getName());
        final Company company = companyDAO.findByEnterpriseNoAndCompanyNo(enterpriseNo, customer.getCompanyNo());
        //企业信息补充
        if (company != null) {
            customerVO.setCompanyName(company.getCompanyName());
            customerVO.setUnifiedSocialCode(company.getUnifiedSocialCode());
            customerVO.setCountry(company.getCountry());
            customerVO.setFactoryType(company.getFactoryType());
            customerVO.setEconomicType(company.getEconomicType());
            customerVO.setIsAssociatedEnterprise(company.getIsAssociatedEnterprise());
            customerVO.setIsMedicalInstitution(company.getIsMedicalInstitution());
            customerVO.setIsMedicalInstitutionName(CommonIfEnum.getNameByValue(company.getIsMedicalInstitution()));
            customerVO.setInstitutionalType(company.getInstitutionalType());
            customerVO.setHospitalType(company.getHospitalType());
            customerVO.setHospitalClass(company.getHospitalClass());
            customerVO.setAssociatedOrgNo(company.getAssociatedOrgNo());
            customerVO.setAssociatedOrgCode(company.getAssociatedOrgCode());
            customerVO.setAssociatedOrgName(company.getAssociatedOrgName());
            customerVO.setAddress(company.getAddress());
            if (StringUtils.isNotBlank(company.getTaxCategory())) {
                final Map<String, CustomDocResponse> itemByCustomDocCode = customDocService.getItemByCustomDocCode(enterpriseNo, SystemConstant.TAX_CATEGORY);
                customerVO.setTaxCategory(company.getTaxCategory());
                customerVO.setTaxCategoryName(itemByCustomDocCode.getOrDefault(company.getTaxCategory(), new CustomDocResponse()).getDocItemName());
            }
            if (StringUtils.isNotBlank(company.getEconomicType())) {
                final Map<String, CustomDocResponse> itemByCustomDocCode = customDocService.getItemByCustomDocCode(enterpriseNo, SystemConstant.ECONOMIC_TYPE);
                customerVO.setEconomicTypeName(itemByCustomDocCode.getOrDefault(company.getEconomicType(), new CustomDocResponse()).getDocItemName());
            }
        }
        //客户分类
        if (StringUtils.isNotBlank(customer.getCustomerCategoryNo())) {
            final CustomerCategory customerCategory = customerCategoryDAO.getByNo(enterpriseNo, customer.getCustomerCategoryNo());
            if (customerCategory != null) {
                customerVO.setCustomerCategoryName(customerCategory.getCategoryName());
            }
        }
        //客户性质
        if (StringUtils.isNotBlank(customer.getBusinessType())) {
            final Map<String, CustomDocResponse> businessTypeMap = customDocService.getItemByCustomDocCode(enterpriseNo, SystemConstant.DSRP_BUSINESS_TYPE);
            customerVO.setBusinessTypeName(businessTypeMap.getOrDefault(customer.getBusinessType(), new CustomDocResponse()).getDocItemName());
        }
        //交易币种
        if (StringUtils.isNotBlank(customer.getCurrency())) {
            final Map<String, String> currencyMap = dictEnterpriseService.getValMapByNumber(enterpriseNo, DictNumberEnum.DSRP_CURRENCY);
            customerVO.setCurrencyName(currencyMap.getOrDefault(customer.getCurrency(), ""));
        }
        //合作性质
        if (StringUtils.isNotBlank(customer.getCooperationMode())) {
            final Map<String, CustomDocResponse> cooperationMap = customDocService.getItemByCustomDocCode(enterpriseNo, SystemConstant.CUSTOMER_COOPERATION_NUMBER);
            customerVO.setCooperationModeName(cooperationMap.getOrDefault(customer.getCooperationMode(), new CustomDocResponse()).getDocItemName());
        }
        //价格体系
        if (StringUtils.isNotBlank(customer.getPriceCategoryCode())) {
            final Map<String, String> priceCustomerCategoryMap = dictEnterpriseService.getValMapByNumber(enterpriseNo, DictNumberEnum.DSRP_PRICE_CUSTOMER_CATEGORY);
            customerVO.setPriceCategoryName(priceCustomerCategoryMap.getOrDefault(customer.getPriceCategoryCode(), ""));
        }
        //交易类型
        if (StringUtils.isNotBlank(customer.getTransactionType())) {
            final List<TransactionTypeResponse> transactionTypeList = transactionTypeService.getTransactionTypeList(enterpriseNo, ViewNameConstant.BDC_CUSTOMER_VIEW);
            final Map<String, TransactionTypeResponse> transactionTypeMap = transactionTypeList.stream().collect(Collectors.toMap(TransactionTypeResponse::getTransactionTypeCode, Function.identity()));
            customerVO.setTransactionTypeName(transactionTypeMap.getOrDefault(customer.getTransactionType(), new TransactionTypeResponse()).getTransactionTypeName());
        }
        if (customer.getControlId() != null) {
            final CertControlTypeVo type = certControlService.getId(enterpriseNo, customer.getControlId());
            if (type != null) {
                customerVO.setControlTypeName(type.getControlTypeName());
            }
        }
        //客户联系人
        List<CompanyLinkman> customerLinkmanList = companyLinkmanDAO.getCompanyLinkmanListByCompanyNo(enterpriseNo, customer.getCompanyNo(), LinkmanTypeEnum.SALE.getValue(), customerNo);
        if (CollectionUtils.isNotEmpty(customerLinkmanList)) {
            List<CompanyLinkmanVO> customerLinkmanVoList = new ArrayList<>();
            customerLinkmanList.forEach(t -> {
                CompanyLinkmanVO vo = new CompanyLinkmanVO();
                BeanUtils.copyProperties(t, vo);
                customerLinkmanVoList.add(vo);
            });
            customerVO.setLinkmanList(customerLinkmanVoList);
        }
        //开票信息
        List<CustomerInvoice> invoiceList = customerInvoiceDAO.getCustomerInvoiceByCustomerNo(enterpriseNo, customer.getCustomerNo());
        if (CollectionUtils.isNotEmpty(invoiceList)) {
            List<CustomerInvoiceVO> invoiceVoList = new ArrayList<>();
            invoiceList.forEach(t -> {
                CustomerInvoiceVO vo = new CustomerInvoiceVO();
                BeanUtils.copyProperties(t, vo);
                vo.setTypeName(InvoiceTypeEnum.getByType(t.getType()) == null ? null : InvoiceTypeEnum.getByType(t.getType()).getName());
                invoiceVoList.add(vo);
            });
            customerVO.setInvoiceList(invoiceVoList);
        }
        //负责人信息
        List<CustomerSalesMan> salesManList = customerSalesManDAO.getCustomerSalesManByCustomerNoList(enterpriseNo, Collections.singletonList(customer.getCustomerNo()));
        if (CollectionUtils.isNotEmpty(salesManList)) {
            final List<String> salesManNoList = salesManList.stream().map(CustomerSalesMan::getSalesManNo).collect(Collectors.toList());
            Map<String, EmployeeVo> employeeMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(salesManNoList)) {
                final List<EmployeeVo> employeeList = employeeService.getEmployeeList(enterpriseNo, salesManNoList);
                employeeMap = employeeList.stream().collect(Collectors.toMap(EmployeeVo::getEmployeeNo, Function.identity()));
            }
            Map<String, EmployeeVo> finalEmployeeMap = employeeMap;
            List<CustomerSalesManVO> salesManVoList = new ArrayList<>();
            salesManList.forEach(t -> {
                CustomerSalesManVO customerSalesManVo = new CustomerSalesManVO();
                BeanUtils.copyProperties(t, customerSalesManVo);
                customerSalesManVo.setDeptNo(t.getOrgNo());
                customerSalesManVo.setDeptName(t.getOrgName());
                if (finalEmployeeMap.containsKey(t.getSalesManNo())) {
                    customerSalesManVo.setMobile(finalEmployeeMap.get(t.getSalesManNo()).getMobile());
                }
                salesManVoList.add(customerSalesManVo);
            });
            customerVO.setSalesManList(salesManVoList);
        }
        final List<CompanyBank> bankList = companyBankDAO.getCompanyBankListBySourceNoList(enterpriseNo, LinkmanTypeEnum.SALE.getValue(), Collections.singletonList(customer.getCustomerNo()));
        if (CollectionUtils.isNotEmpty(bankList)) {
            final List<BankType> bankTypeList = bankTypeDAO.getList();
            final Map<String, BankType> bankTypeMap = bankTypeList.stream().collect(Collectors.toMap(BankType::getBankCode, Function.identity(), (v1, v2) -> v1));
            List<BankVO> bankVoList = new ArrayList<>();
            bankList.forEach(t -> {
                BankVO bankVO = new BankVO();
                BeanUtils.copyProperties(t, bankVO);
                bankVO.setBankTypeName(bankTypeMap.getOrDefault(t.getBankType(), new BankType()).getBankName());
                bankVoList.add(bankVO);
            });
            customerVO.setBankList(bankVoList);
        }
        //联系地址
        List<CompanyShippingAddress> companyShippingAddressList = companyShippingAddressDAO.getCompanyShippingAddressByCompanyNo(enterpriseNo, customer.getCompanyNo(), LinkmanTypeEnum.SALE.getValue(), customerNo);
        if (CollectionUtils.isNotEmpty(companyShippingAddressList)) {
            final List<String> regionCodeList = companyShippingAddressList.stream().map(CompanyShippingAddress::getRegionCode).filter(t -> !StringUtils.isEmpty(t)).collect(Collectors.toList());
            Map<String, AreaCodeVo> areaMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(regionCodeList)) {
                List<AreaCodeVo> areaCodeList = dictEnterpriseService.findAreaByCodes(regionCodeList);
                areaMap = areaCodeList.stream().collect(Collectors.toMap(AreaCodeVo::getAreaCode, t -> t));
            }
            Map<String, AreaCodeVo> finalAreaMap = areaMap;
            List<CompanyShippingAddressVO> salesManVoList = new ArrayList<>();
            companyShippingAddressList.forEach(t -> {
                CompanyShippingAddressVO companyShippingAddressVO = new CompanyShippingAddressVO();
                BeanUtils.copyProperties(t, companyShippingAddressVO);
                if (finalAreaMap.containsKey(t.getRegionCode())) {
//                    companyShippingAddressVO.setRegionName(finalAreaMap.get(t.getRegionCode()).getAreaName());
                    companyShippingAddressVO.setRegionFullName(finalAreaMap.get(t.getRegionCode()).getAreaName());
                }
                salesManVoList.add(companyShippingAddressVO);
            });
            customerVO.setLinkAddressList(salesManVoList);
        }
        //授权区域
        final List<CustomerArea> areaList = customerAreaDAO.getCustomerAreaByCustomerNo(enterpriseNo, customer.getCustomerNo());
        if (CollectionUtils.isNotEmpty(areaList)) {
            List<AreaVo> areaVoList = new ArrayList<>();
            areaList.forEach(t -> {
                AreaVo areaVo = new AreaVo();
                BeanUtils.copyProperties(t, areaVo);
                areaVoList.add(areaVo);
            });
            customerVO.setAreaList(areaVoList);
        }
        //对应供应商
        final List<Supplier> supplierList = supplierDAO.selectList(Wrappers.lambdaQuery(Supplier.class).eq(Supplier::getEnterpriseNo, enterpriseNo)
                .eq(Supplier::getCompanyNo, customer.getCompanyNo())
                .eq(Supplier::getDeleted, DeletedEnum.UN_DELETE.getValue()));
        customerVO.setSupplierCode(supplierList.stream().map(Supplier::getSupplierCode).collect(Collectors.joining(",")));
        customerVO.setSupplierName(supplierList.stream().map(Supplier::getSupplierName).collect(Collectors.joining(",")));
        return customerVO;
    }

    /**
     * 根据企业编号查询客户档案
     *
     * @param enterpriseNo
     * @param companyNo
     * @return: {@link List< Customer>}
     */
    @Override
    public List<Customer> findCustomerByCompanyNo(String enterpriseNo, String companyNo) {
        ValidatorUtils.checkEmptyThrowEx(enterpriseNo, "租户编号不能为空");
        ValidatorUtils.checkEmptyThrowEx(companyNo, "企业编号不能为空");
        return customerDAO.getCustomerByCompanyNo(enterpriseNo, companyNo);
    }

    @Override
    public int updateCustomer(Customer customer) {
        ValidatorUtils.checkEmptyThrowEx(customer, "修改客户档案，参数不能为空");
        return customerDAO.updateById(customer);
    }

    /**
     * 根据客户编码查询客户档案分配组织租户编号
     *
     * @param groupEnterpriseNo
     * @param customerCode
     * @return: {@link List< String>}
     */
    @Override
    public List<String> findCustomerAssignOrgEnterpriseNo(String groupEnterpriseNo, String customerCode) {
        CommonUtil.checkEmptyThrowEx(groupEnterpriseNo, "集团租户编号不能为空");
        CommonUtil.checkEmptyThrowEx(customerCode, "客户编码不能为空");
        List<SupplierCustomerUseInfo> customerUseList = supplierCustomerUseInfoDAO.findGroupSupplierCustomerUseList(groupEnterpriseNo, Lists.newArrayList(customerCode), CompanyTypeEnum.CUSTOMER.getValue());
        return customerUseList.stream().map(SupplierCustomerUseInfo::getUseEnterpriseNo).collect(Collectors.toList());
    }

    /**
     * 根据客户编码查询客户基本信息
     *
     * @param enterpriseNo
     * @param customerCode
     * @return: {@link Customer}
     */
    @Override
    public Customer getCustomerByCustomerCode(String enterpriseNo, String customerCode) {
        ValidatorUtils.checkEmptyThrowEx(enterpriseNo, "租户编号不能为空");
        ValidatorUtils.checkEmptyThrowEx(customerCode, "客户编码不能为空");
        return customerDAO.getCustomerByCustomerCode(enterpriseNo, customerCode);
    }


    @Override
    public Boolean checkOnlyName(OperationModel operationModel, String no, String name) {
        return customerDAO.selectCount(new LambdaQueryWrapper<Customer>().eq(Customer::getEnterpriseNo, operationModel.getEnterpriseNo()).eq(Customer::getEnterpriseNo, operationModel.getEnterpriseNo()).eq(Customer::getCustomerName, name).ne(StringUtils.isNotEmpty(no), Customer::getCustomerNo, no)) <= 0;
    }

    @Override
    public Boolean checkOnlyCode(OperationModel operationModel, String no, String code) {
        return customerDAO.selectCount(new LambdaQueryWrapper<Customer>().eq(Customer::getEnterpriseNo, operationModel.getEnterpriseNo()).eq(Customer::getEnterpriseNo, operationModel.getEnterpriseNo()).eq(Customer::getCustomerCode, code).ne(StringUtils.isNotEmpty(no), Customer::getCustomerNo, no)) <= 0;
    }

    @Override
    public PageVo<CustomerPageVO> queryPageCustomer(OperationModel operationModel, QueryPageCustomerDTO params, PageDto pageDto) {
        ValidatorUtils.checkTrueThrowEx(Objects.isNull(operationModel) || StringUtils.isBlank(operationModel.getEnterpriseNo()), "租户信息不能为空");
        CustomerListPageReq req = new CustomerListPageReq();
        BeanUtils.copyProperties(params, req);
        req.setEnterpriseNo(operationModel.getEnterpriseNo());
        req.setManagerEnterpriseNo(operationModel.getEnterpriseNo());
        if (CollectionUtils.isNotEmpty(params.getAssignEnterprsieNoList())) {
            req.setAssignEnterprsieNoList(params.getAssignEnterprsieNoList());
        }
        Page<Customer> page = PageHelper.startPage(pageDto.getPageIndex(), pageDto.getPageSize(), StringUtils.isBlank(pageDto.getOrderBy()) ? "a.create_time desc" : pageDto.getOrderBy());
//        LambdaQueryWrapper<Customer> lambdaQueryWrapper = new LambdaQueryWrapper<>();
//        lambdaQueryWrapper.eq(Customer::getEnterpriseNo, operationModel.getEnterpriseNo()).eq(Customer::getDeleted, DeletedEnum.UN_DELETE.getValue());
//        handleQueryParams(lambdaQueryWrapper, params);
//        customerDAO.selectList(lambdaQueryWrapper);
        customerDAO.selectCustomerList(req);
        return PageUtils.convertPageVo(page, data -> completeSupplementPageVO(operationModel.getEnterpriseNo(), data));
    }

    @Override
    public Long findExamCount(OperationModel operationModel) {
        LambdaQueryWrapper<Customer> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(Customer::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(Customer::getDeleted, DeletedEnum.UN_DELETE.getValue())
                .eq(Customer::getBusinessFlag, CustomerBusinessFlagEnum.DRAFT.getValue());
        return customerDAO.selectCount(lambdaQueryWrapper);
    }

    @Override
    public PageVo<CustomerPageVO> queryPageCustomerByOrg(OperationModel operationModel, QueryPageCustomerByOrgDTO params, PageDto pageDto) {
        List<OrganizationVo> organizationList = uimTenantService.getAuthEnterpriseList(operationModel.getEnterpriseNo(), operationModel.getUserNo(), operationModel.getEmployerNo());
        if (CollectionUtils.isNotEmpty(params.getOrgNoList())) {
            organizationList = organizationList.stream().filter(t -> params.getOrgNoList().contains(t.getOrgNo())).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(organizationList)) {
            return new PageVo<>();
        }
        final String groupEnterpriseNo = tenantService.getGroupEnterpriseNo(operationModel.getEnterpriseNo());
        Page<Customer> page = PageHelper.startPage(pageDto.getPageIndex(), pageDto.getPageSize(), StringUtils.isBlank(pageDto.getOrderBy()) ? "create_time desc" : pageDto.getOrderBy());
        LambdaQueryWrapper<Customer> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(Customer::getEnterpriseNo, groupEnterpriseNo).eq(Customer::getDeleted, DeletedEnum.UN_DELETE.getValue());
        handleQueryParams(lambdaQueryWrapper, params);
        lambdaQueryWrapper.inSql(Customer::getCustomerCode, "select customer_code  from  bdc_customer where deleted=0 and enterprise_no in ('" + organizationList.stream().map(OrganizationVo::getBindingEnterpriseNo).collect(Collectors.joining("','")) + "')");
        customerDAO.selectList(lambdaQueryWrapper);
        return PageUtils.convertPageVo(page, data -> completeSupplementPageVO(groupEnterpriseNo, data));
    }

    @Override
    public PageVo<CustomerPageVO> queryPageCustomerByGroup(OperationModel operationModel, QueryPageCustomerDTO params, PageDto pageDto) {
        final String groupEnterpriseNo = tenantService.getGroupEnterpriseNo(operationModel.getEnterpriseNo());
        Page<Customer> page = PageHelper.startPage(pageDto.getPageIndex(), pageDto.getPageSize(), StringUtils.isBlank(pageDto.getOrderBy()) ? "create_time desc" : pageDto.getOrderBy());
        LambdaQueryWrapper<Customer> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(Customer::getEnterpriseNo, groupEnterpriseNo).eq(Customer::getDeleted, DeletedEnum.UN_DELETE.getValue());
        handleQueryParams(lambdaQueryWrapper, params);
        customerDAO.selectList(lambdaQueryWrapper);
        return PageUtils.convertPageVo(page, data -> completeSupplementPageVO(groupEnterpriseNo, data));
    }

    @Override
    public PageVo<CustomerPageVO> queryPageCustomerBySpecifyOrg(OperationModel operationModel, QueryPageCustomerBySpecifyOrgDTO params, PageDto pageDto) {
        //获取有权限的组织列表
        List<OrganizationVo> organizationList = uimTenantService.getAuthEnterpriseList(operationModel.getEnterpriseNo(), operationModel.getUserNo(), operationModel.getEmployerNo());
        ValidatorUtils.checkEmptyThrowEx(params.getOrgNo(), "指定组织不能为空");
        final Optional<OrganizationVo> optional = organizationList.stream().filter(t -> params.getOrgNo().equals(t.getOrgNo())).findAny();
        if (!optional.isPresent()) {
            return new PageVo<>();
        }
        final String specifyEnterpriseNo = optional.get().getBindingEnterpriseNo();
        final String groupEnterpriseNo = tenantService.getGroupEnterpriseNo(operationModel.getEnterpriseNo());
        LambdaQueryWrapper<Customer> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.select(Customer::getCustomerCode);
        lambdaQueryWrapper.eq(Customer::getEnterpriseNo, groupEnterpriseNo).eq(Customer::getDeleted, DeletedEnum.UN_DELETE.getValue());

        handleQueryParams(lambdaQueryWrapper, params);
        DorisHelper.startDoris(DorisReadModeEnum.ODBC, SystemConstant.DATA_BASE, Customer.class);
        List<Customer> customerList = customerDAO.selectList(lambdaQueryWrapper);
        if (CollectionUtils.isEmpty(customerList)) {
            return new PageVo<>();
        }
        Page<Customer> page = PageHelper.startPage(pageDto.getPageIndex(), pageDto.getPageSize(), StringUtils.isBlank(pageDto.getOrderBy()) ? "create_time desc" : pageDto.getOrderBy());
        LambdaQueryWrapper<Customer> specifyQueryWrapper = new LambdaQueryWrapper<>();
        specifyQueryWrapper.eq(Customer::getEnterpriseNo, specifyEnterpriseNo).eq(Customer::getDeleted, DeletedEnum.UN_DELETE.getValue());
        specifyQueryWrapper.in(Customer::getCustomerCode, customerList.stream().map(Customer::getCustomerCode).collect(Collectors.toList()));
        customerDAO.selectList(specifyQueryWrapper);
        return PageUtils.convertPageVo(page, data -> completeSupplementPageVO(specifyEnterpriseNo, data));
    }


    @Override
    public PageVo<CustomerPageVO> queryPageCustomerByCompatibleOrg(OperationModel operationModel, QueryPageCustomerBySpecifyOrgDTO params, PageDto pageDto) {
        ValidatorUtils.checkEmptyThrowEx(params.getOrgNo(), "指定组织不能为空");

        //查询集团租户编号
        String groupEnterpriseNo = tenantService.getGroupEnterpriseNo(operationModel.getEnterpriseNo());

        //查询组织编号
        String specifyEnterpriseNo = organizationService.getEnterpriseNoByOrgNo(groupEnterpriseNo, params.getOrgNo());

        LambdaQueryWrapper<Customer> lambdaQueryWrapper = new LambdaQueryWrapper<>();

        handleQueryParams(lambdaQueryWrapper, params);
        Page<Customer> page = PageHelper.startPage(pageDto.getPageIndex(), pageDto.getPageSize(), StringUtils.isBlank(pageDto.getOrderBy()) ? "create_time desc" : pageDto.getOrderBy());
        lambdaQueryWrapper.eq(Customer::getEnterpriseNo, specifyEnterpriseNo).eq(Customer::getDeleted, DeletedEnum.UN_DELETE.getValue());
        customerDAO.selectList(lambdaQueryWrapper);
        return PageUtils.convertPageVo(page, data -> completeSupplementPageVO(specifyEnterpriseNo, data));
    }

    private void handleQueryParams(LambdaQueryWrapper<Customer> wrapper, QueryPageCustomerDTO params) {
        wrapper.eq(StringUtils.isNotEmpty(params.getControlStatus()), Customer::getControlStatus, params.getControlStatus())
                .in(CollectionUtils.isNotEmpty(params.getControlStatusList()), Customer::getControlStatus, params.getControlStatusList())
                .eq(params.getBusinessFlag() != null, Customer::getBusinessFlag, params.getBusinessFlag())
                .in(CollectionUtils.isNotEmpty(params.getBusinessFlags()), Customer::getBusinessFlag, params.getBusinessFlags())
                .in(CollectionUtils.isNotEmpty(params.getCustomerNos()), Customer::getCustomerNo, params.getCustomerNos())
                .notIn(CollectionUtils.isNotEmpty(params.getNoCustomerNos()), Customer::getCustomerNo, params.getNoCustomerNos())
                .and(StringUtils.isNotBlank(params.getCustomerExactKeywords()),
                        i -> i.like(Customer::getCustomerNo, params.getCustomerExactKeywords())
                                .or().like(Customer::getCustomerName, params.getCustomerExactKeywords())
                                .or().like(Customer::getCustomerCode, params.getCustomerExactKeywords())
                                .or().like(Customer::getMnemonicCode, params.getCustomerExactKeywords())
                )
                .and(StringUtils.isNotBlank(params.getCustomerKeywords()),
                        i -> i.like(Customer::getCustomerNameEn, params.getCustomerKeywords())
                                .or().like(Customer::getCustomerName, params.getCustomerKeywords())
                                .or().like(Customer::getCustomerCode, params.getCustomerKeywords())
                                .or().like(Customer::getMnemonicCode, params.getCustomerKeywords())
                )
                .and(StringUtils.isNotBlank(params.getKeywords()),
                        i -> i.like(Customer::getCustomerCode, params.getKeywords())
                                .or().like(Customer::getCustomerName, params.getKeywords())
                )
                .in(CollectionUtils.isNotEmpty(params.getCooperationModes()), Customer::getCooperationMode, params.getCooperationModes())
                .notIn(CollectionUtils.isNotEmpty(params.getExcludeCustomerCodeList()), Customer::getCustomerCode, params.getExcludeCustomerCodeList())
                .in(CollectionUtils.isNotEmpty(params.getPriceCategoryCodeList()), Customer::getPriceCategoryCode, params.getPriceCategoryCodeList())
                .in(CollectionUtils.isNotEmpty(params.getCustomerCodeList()), Customer::getCustomerCode, params.getCustomerCodeList())
                .in(CollectionUtils.isNotEmpty(params.getCustomerCategoryNos()), Customer::getCustomerCategoryNo, params.getCustomerCategoryNos())
                .in(CollectionUtils.isNotEmpty(params.getControlIdList()), Customer::getControlId, params.getControlIdList())
                .like(StringUtils.isNotEmpty(params.getUnifiedSocialCodeKeywords()), Customer::getUnifiedSocialCode, params.getUnifiedSocialCodeKeywords())
                .in(CollectionUtils.isNotEmpty(params.getBusinessTypeList()), Customer::getBusinessType, params.getBusinessTypeList())
                .eq(StringUtils.isNotEmpty(params.getCustomerCode()), Customer::getCustomerCode, params.getCustomerCode())
                .eq(StringUtils.isNotEmpty(params.getPriceCategoryCode()), Customer::getPriceCategoryCode, params.getPriceCategoryCode())
                .inSql(StringUtils.isNotEmpty(params.getOwnerCompanyKeywords()), Customer::getOwnerCompany, params.getOwnerCompanyKeywords())
                .exists(StringUtils.isNotEmpty(params.getLinkManAndPhoneKeywords()), " SELECT 1 FROM bdc_company_linkman linkman WHERE linkman.source_no=bdc_customer.customer_no and linkman.linkman_type='is_sale' and (linkman.mobile_phone like '%" + params.getLinkManAndPhoneKeywords() + "%' or  linkman.linkman like '%" + params.getLinkManAndPhoneKeywords() + "%' ) ")
        ;

        if(!CollectionUtils.isEmpty(params.getControlStatusList())){
            wrapper.in(Customer::getControlStatus, params.getControlStatusList());
        }
        if(!StringUtils.isEmpty(params.getPriceCategoryCode())){
            wrapper.eq(Customer::getPriceCategoryCode, params.getPriceCategoryCode());
        }
    }

    public List<CustomerPageVO> completeSupplementPageVO(String enterpriseNo, List<Customer> data) {
        final List<String> categoryNoList = data.stream().map(Customer::getCustomerCategoryNo).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
        final List<String> companyNoList = data.stream().map(Customer::getCompanyNo).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
        final List<String> customerNoList = data.stream().map(Customer::getCustomerNo).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
        Map<String, CustomerCategory> categoryMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(categoryNoList)) {
            final List<CustomerCategory> categoryList = customerCategoryDAO.getByNoList(enterpriseNo, categoryNoList);
            categoryMap = categoryList.stream().collect(Collectors.toMap(CustomerCategory::getNo, Function.identity()));
        }
        Map<String, Company> companyMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(companyNoList)) {
            final List<Company> companyList = companyDAO.findByEnterpriseNoAndCompanyNoList(enterpriseNo, companyNoList);
            companyMap = companyList.stream().collect(Collectors.toMap(Company::getCompanyNo, Function.identity()));
        }
        //税收分类
        final Map<String, CustomDocResponse> taxCategoryMap = customDocService.getItemByCustomDocCode(enterpriseNo, SystemConstant.TAX_CATEGORY);
        //客户经济类型
        final Map<String, CustomDocResponse> economicTypeMap = customDocService.getItemByCustomDocCode(enterpriseNo, SystemConstant.ECONOMIC_TYPE);
        //客户性质
        final Map<String, CustomDocResponse> businessTypeMap = customDocService.getItemByCustomDocCode(enterpriseNo, SystemConstant.DSRP_BUSINESS_TYPE);
        //交易币种
        final Map<String, String> currencyMap = dictEnterpriseService.getValMapByNumber(enterpriseNo, DictNumberEnum.DSRP_CURRENCY);
        //合作性质
        final Map<String, CustomDocResponse> cooperationMap = customDocService.getItemByCustomDocCode(enterpriseNo, SystemConstant.CUSTOMER_COOPERATION_NUMBER);
        //价格体系
        final Map<String, String> priceCustomerCategoryMap = dictEnterpriseService.getValMapByNumber(enterpriseNo, DictNumberEnum.DSRP_PRICE_CUSTOMER_CATEGORY);
        //交易类型
        final List<TransactionTypeResponse> transactionTypeList = transactionTypeService.getTransactionTypeList(enterpriseNo, ViewNameConstant.BDC_CUSTOMER_VIEW);
        final Map<String, TransactionTypeResponse> transactionTypeMap = transactionTypeList.stream().collect(Collectors.toMap(TransactionTypeResponse::getTransactionTypeCode, Function.identity()));
        //默认联系人
        final List<CompanyLinkman> companyDefaultLinkmanList = companyLinkmanDAO.getCompanyDefaultLinkmanListBySourceNoList(enterpriseNo, LinkmanTypeEnum.SALE.getValue(), customerNoList);
        final Map<String, CompanyLinkman> companyDefaultLinkmanMap = companyDefaultLinkmanList.stream().collect(Collectors.toMap(CompanyLinkman::getSourceNo, Function.identity(), (k, v) -> k));


        Map<Long, CertControlTypeVo> controlTypeMap = new HashMap<>();
        final List<Long> controlIdList = data.stream().map(Customer::getControlId).filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(controlIdList)) {
            final List<CertControlTypeVo> controlTypeList = certControlService.getIdList(enterpriseNo, controlIdList);
            controlTypeMap = controlTypeList.stream().collect(Collectors.toMap(CertControlTypeVo::getId, Function.identity()));
        }

        List<CustomerPageVO> result = new ArrayList<>();
        Map<String, CustomerCategory> finalCategoryMap = categoryMap;
        Map<String, Company> finalCompanyMap = companyMap;
        Map<Long, CertControlTypeVo> finalControlTypeMap = controlTypeMap;
        data.forEach(customer -> {
            CustomerPageVO customerVO = new CustomerPageVO();
            BeanUtils.copyProperties(customer, customerVO);
            if (finalCompanyMap.containsKey(customer.getCompanyNo())) {
                final Company company = finalCompanyMap.get(customer.getCompanyNo());
                customerVO.setTaxCategoryName(taxCategoryMap.getOrDefault(company.getTaxCategory(), new CustomDocResponse()).getDocItemName());
                customerVO.setEconomicTypeName(economicTypeMap.getOrDefault(company.getEconomicType(), new CustomDocResponse()).getDocItemName());
                customerVO.setCompanyName(company.getCompanyName());
                customerVO.setUnifiedSocialCode(company.getUnifiedSocialCode());
                customerVO.setCountry(company.getCountry());
                customerVO.setFactoryType(company.getFactoryType());
                customerVO.setEconomicType(company.getEconomicType());
                customerVO.setIsAssociatedEnterprise(company.getIsAssociatedEnterprise());
                customerVO.setIsMedicalInstitution(company.getIsMedicalInstitution());
                customerVO.setIsMedicalInstitutionName(CommonIfEnum.getNameByValue(company.getIsMedicalInstitution()));
                customerVO.setHospitalType(company.getHospitalType());
                customerVO.setHospitalClass(company.getHospitalClass());
                customerVO.setAssociatedOrgNo(company.getAssociatedOrgNo());
                customerVO.setAssociatedOrgCode(company.getAssociatedOrgCode());
                customerVO.setAssociatedOrgName(company.getAssociatedOrgName());
            }
            customerVO.setCustomerCategoryName(finalCategoryMap.getOrDefault(customer.getCustomerCategoryNo(), new CustomerCategory()).getCategoryName());
            customerVO.setBusinessTypeName(businessTypeMap.getOrDefault(customer.getBusinessType(), new CustomDocResponse()).getDocItemName());
            customerVO.setCurrencyName(currencyMap.getOrDefault(customer.getCurrency(), ""));
            customerVO.setCooperationModeName(cooperationMap.getOrDefault(customer.getCooperationMode(), new CustomDocResponse()).getDocItemName());
            customerVO.setPriceCategoryName(priceCustomerCategoryMap.getOrDefault(customer.getPriceCategoryCode(), ""));
            customerVO.setTransactionTypeName(transactionTypeMap.getOrDefault(customer.getTransactionType(), new TransactionTypeResponse()).getTransactionTypeName());
            customerVO.setIsGspControlName(CommonIfEnum.getNameByValue(customer.getIsGspControl()));
            customerVO.setInstitutionalTypeName(InstitutionalTypeEnum.getByType(customer.getInstitutionalType()) != null ? InstitutionalTypeEnum.getByType(customer.getInstitutionalType()).getName() : "");
            customerVO.setGspAuditStatusName(CustomerGspAuditStatusEnum.getByType(customer.getGspAuditStatus()) != null ? CustomerGspAuditStatusEnum.getByType(customer.getGspAuditStatus()).getName() : "");
            customerVO.setIsSyncWmsName(CommonIfEnum.getNameByValue(customer.getIsSyncScs()));
            customerVO.setYsSyncFlagName(CustomerYsSyncEnum.getByType(customer.getYsSyncFlag()) == null ? null : CustomerYsSyncEnum.getByType(customer.getYsSyncFlag()).getName());
            customerVO.setIsSyncScsName(CustomerScsSyncEnum.getByType(customer.getIsSyncScs()) == null ? null : CustomerScsSyncEnum.getByType(customer.getIsSyncScs()).getName());
            if (companyDefaultLinkmanMap.containsKey(customer.getCustomerNo())) {
                customerVO.setLinkMan(companyDefaultLinkmanMap.get(customer.getCustomerNo()).getLinkman());
                customerVO.setLinkPhone(companyDefaultLinkmanMap.get(customer.getCustomerNo()).getMobilePhone());
            }
            if (finalControlTypeMap.containsKey(customer.getControlId())) {
                customerVO.setControlTypeName(finalControlTypeMap.get(customer.getControlId()).getControlTypeName());
            }
            result.add(customerVO);
        });
        return result;
    }


    /**
     * 获取该客户的联系地址
     *
     * @param enterpriseNo 租户信息
     * @param customerNo   客户编号
     * @return
     */
    public List<CompanyLinkmanResponse> getLinkmanListByCustomerNo(String enterpriseNo, String customerNo) {
        CommonUtil.checkEmptyThrowEx(enterpriseNo, "租户编号不能为空");
        CommonUtil.checkEmptyThrowEx(customerNo, "客户编号不能为空");
        Customer customer = customerDAO.getCustomerByNo(enterpriseNo, customerNo);
        CommonUtil.checkEmptyThrowEx(customer, "客户不存在");
        final List<CompanyShippingAddress> companyShippingAddressList = companyShippingAddressDAO.getCompanyShippingAddressByCompanyNo(enterpriseNo, customer.getCompanyNo(), LinkmanTypeEnum.SALE.getValue(), customer.getCustomerNo());
        List<CompanyLinkmanResponse> salesManVoList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(companyShippingAddressList)) {
            companyShippingAddressList.forEach(t -> {
                CompanyLinkmanResponse companyLinkmanResponse = new CompanyLinkmanResponse();
                BeanUtils.copyProperties(t, companyLinkmanResponse);
                salesManVoList.add(companyLinkmanResponse);
            });
        }
        return salesManVoList;
    }

    @Override
    public List<CompanyLinkmanResponse> getLinkmanListByCustomerNoList(String enterpriseNo, List<String> customerNoList) {
        CommonUtil.checkEmptyThrowEx(enterpriseNo, "租户编号不能为空");
        CommonUtil.checkEmptyThrowEx(customerNoList, "客户编号不能为空");
        final List<CompanyShippingAddress> companyShippingAddressList = companyShippingAddressDAO.getCompanyShippingAddressBySourceNoList(enterpriseNo, LinkmanTypeEnum.SALE.getValue(), customerNoList);
        List<CompanyLinkmanResponse> salesManVoList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(companyShippingAddressList)) {
            companyShippingAddressList.forEach(t -> {
                CompanyLinkmanResponse companyLinkmanResponse = new CompanyLinkmanResponse();
                BeanUtils.copyProperties(t, companyLinkmanResponse);
                salesManVoList.add(companyLinkmanResponse);
            });
        }
        return salesManVoList;
    }


    @Override
    public PageVo<CustomerComponentResponse> selectCustomerPageForCommonComponent(CustomerComponentRequest request, PageDto pageDto) {
        Page<Customer> page = PageHelper.startPage(pageDto.getPageIndex(), pageDto.getPageSize(), pageDto.getOrderBy());
        customerDAO.selectList(handleQueryCondition(request));
        return PageUtils.convertPageVo(page, data -> completeSupplementCommonQueryVO(request.getEnterpriseNo(), data));
    }


    @Override
    public List<CustomerComponentResponse> selectCustomerListForCommonComponent(CustomerComponentRequest params) {
        List<Customer> customerList = customerDAO.selectList(handleQueryCondition(params));
        return completeSupplementCommonQueryVO(params.getEnterpriseNo(), customerList);
    }


    private LambdaQueryWrapper handleQueryCondition(CustomerComponentRequest request) {
        LambdaQueryWrapper<Customer> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(Customer::getEnterpriseNo, request.getEnterpriseNo())
                .eq(StringUtils.isNotBlank(request.getCustomerNo()), Customer::getCustomerNo, request.getCustomerNo())
                .in(CollectionUtils.isNotEmpty(request.getCustomerNoList()), Customer::getCustomerNo, request.getCustomerNoList())
                .notIn(CollectionUtils.isNotEmpty(request.getNotInCustomerNoList()), Customer::getCustomerNo, request.getNotInCustomerNoList())
                .eq(StringUtils.isNotBlank(request.getCustomerCode()), Customer::getCustomerCode, request.getCustomerCode())
                .in(CollectionUtils.isNotEmpty(request.getCustomerCodeList()), Customer::getCustomerCode, request.getCustomerCodeList())
                .eq(StringUtils.isNotBlank(request.getCustomerName()), Customer::getCustomerName, request.getCustomerName())
                .in(CollectionUtils.isNotEmpty(request.getCustomerNameList()), Customer::getCustomerName, request.getCustomerNameList())
                .like(StringUtils.isNotBlank(request.getCustomerNameKeyword()), Customer::getCustomerName, request.getCustomerNameKeyword())
                .and(StringUtils.isNotBlank(request.getCustomerKeywords()),
                        i -> i.like(Customer::getCustomerNameEn, request.getCustomerKeywords())
                                .or().like(Customer::getCustomerName, request.getCustomerKeywords())
                                .or().like(Customer::getCustomerCode, request.getCustomerKeywords())
                                .or().like(Customer::getMnemonicCode, request.getCustomerKeywords())
                )
                .in(CollectionUtils.isNotEmpty(request.getControlStatusList()), Customer::getControlStatus, request.getControlStatusList())
                .eq(StringUtils.isNotBlank(request.getUnifiedSocialCode()), Customer::getUnifiedSocialCode, request.getUnifiedSocialCode())
                .like(StringUtils.isNotBlank(request.getUnifiedSocialCodeKeywords()), Customer::getUnifiedSocialCode, request.getUnifiedSocialCodeKeywords())
                .like(StringUtils.isNotBlank(request.getOwnerCompanyKeywords()), Customer::getOwnerCompany, request.getOwnerCompanyKeywords())
                .in(CollectionUtils.isNotEmpty(request.getControlIdList()), Customer::getControlId, request.getControlIdList())
                .in(CollectionUtils.isNotEmpty(request.getBusinessTypeList()), Customer::getBusinessType, request.getBusinessTypeList())
                .exists(StringUtils.isNotEmpty(request.getLinkManAndPhoneKeywords()), " SELECT 1 FROM bdc_company_linkman linkman WHERE linkman.source_no=bdc_customer.customer_no and linkman.linkman_type='is_sale' and (linkman.mobile_phone like '%" + request.getLinkManAndPhoneKeywords() + "%' or  linkman.linkman like '%" + request.getLinkManAndPhoneKeywords() + "%' ) ")
                .in(CollectionUtils.isNotEmpty(request.getCompanyNoList()), Customer::getCompanyNo, request.getCompanyNoList())
                .in(CollectionUtils.isNotEmpty(request.getCooperationModeList()), Customer::getCooperationMode, request.getCooperationModeList())
                .in(CollectionUtils.isNotEmpty(request.getPriceCategoryCodeList()), Customer::getPriceCategoryCode, request.getPriceCategoryCodeList())
                .in(CollectionUtils.isNotEmpty(request.getCustomerCategoryNoList()), Customer::getCustomerCategoryNo, request.getCustomerCategoryNoList())
                .in(CollectionUtils.isNotEmpty(request.getOmsCustomerNoList()), Customer::getOmsCustomerNo, request.getOmsCustomerNoList())
                .eq(Customer::getBusinessFlag, SupplierBusinessFlagEnum.FORMAL.getValue())
                .eq(Customer::getDeleted, DeletedEnum.UN_DELETE.getValue());
        if (CollectionUtils.isNotEmpty(request.getRegionCodeList())) {
            lambdaQueryWrapper.exists(" SELECT 1 FROM bdc_company company WHERE company.company_no=bdc_customer.company_no  and company.region_code in(' " + String.join("','", request.getRegionCodeList()) + "' )");
        }
        return lambdaQueryWrapper;
    }

    private List<CustomerComponentResponse> completeSupplementCommonQueryVO(String enterpriseNo, List<Customer> customerList) {
        List<CustomerComponentResponse> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(customerList)) {
            return result;
        }
        List<String> companyNoList = customerList.stream().map(Customer::getCompanyNo).filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());
        List<String> categoryNoList = customerList.stream().map(Customer::getCustomerCategoryNo).filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());
        List<String> customerNoList = customerList.stream().map(Customer::getCustomerNo).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
        Map<String, Company> companyMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(categoryNoList)) {
            List<Company> companyList = companyDAO.findByEnterpriseNoAndCompanyNoList(enterpriseNo, companyNoList);
            companyMap = companyList.stream().collect(Collectors.toMap(Company::getCompanyNo, Function.identity()));
        }
        Map<String, CustomerCategory> categoryMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(categoryNoList)) {
            List<CustomerCategory> categoryList = customerCategoryDAO.getByNoList(enterpriseNo, categoryNoList);
            categoryMap = categoryList.stream().collect(Collectors.toMap(CustomerCategory::getNo, Function.identity()));
        }
        final Map<String, CustomDocResponse> cooperationMap = customDocService.getItemByCustomDocCode(enterpriseNo, SystemConstant.CUSTOMER_COOPERATION_NUMBER);
        //默认联系人
        final List<CompanyLinkman> companyDefaultLinkmanList = companyLinkmanDAO.getCompanyDefaultLinkmanListBySourceNoList(enterpriseNo, LinkmanTypeEnum.SALE.getValue(), customerNoList);
        final Map<String, CompanyLinkman> companyDefaultLinkmanMap = companyDefaultLinkmanList.stream().collect(Collectors.toMap(CompanyLinkman::getSourceNo, Function.identity(), (k, v) -> k));
        Map<String, Company> finalCompanyMap = companyMap;
        Map<String, CustomerCategory> finalCategoryMap = categoryMap;
        customerList.forEach(t -> {
            CustomerComponentResponse response = new CustomerComponentResponse();
            BeanUtils.copyProperties(t, response);
            if (finalCompanyMap.containsKey(t.getCompanyNo())) {
                Company company = finalCompanyMap.get(t.getCompanyNo());
                response.setCompanyName(company.getCompanyName());
                response.setTaxCategory(company.getTaxCategory());
            }
            if (finalCategoryMap.containsKey(t.getCustomerCategoryNo())) {
                CustomerCategory category = finalCategoryMap.get(t.getCustomerCategoryNo());
                response.setCustomerCategoryName(category.getCategoryName());
            }
            response.setCooperationModeName(cooperationMap.getOrDefault(response.getCooperationMode(), new CustomDocResponse()).getDocItemName());
            if (companyDefaultLinkmanMap.containsKey(t.getCustomerNo())) {
                response.setLinkMan(companyDefaultLinkmanMap.get(t.getCustomerNo()).getLinkman());
                response.setLinkPhone(companyDefaultLinkmanMap.get(t.getCustomerNo()).getMobilePhone());
            }
            result.add(response);
        });
        return result;
    }

    @Override
    public CustomerInfoResponse selectCustomerInfo(String enterpriseNo, String customerNo) {
        final CustomerVO customerVO = getCustomer(enterpriseNo, customerNo);
        if (customerVO == null) {
            return null;
        }
        return JSON.parseObject(JSON.toJSONString(customerVO), CustomerInfoResponse.class);
    }

    @Override
    public List<QueryUseInfoResponse> queryUseInfo(String enterpriseNo, String customerCode) {
        // 获取集团租户编号
        String groupEnterpriseNo = tenantService.getGroupEnterpriseNo(enterpriseNo);
        List<String> useEnterpriseNo = new ArrayList<>();
        if (!StringUtils.isEmpty(customerCode)) {
            List<SupplierCustomerUseInfo> customerUseList = supplierCustomerUseInfoDAO.findGroupSupplierCustomerUseList(groupEnterpriseNo, Lists.newArrayList(customerCode), CompanyTypeEnum.CUSTOMER.getValue());
            useEnterpriseNo = customerUseList.stream().map(SupplierCustomerUseInfo::getUseEnterpriseNo).distinct().collect(Collectors.toList());
        }
        List<OrganizationTreeVo> orgList = uimTenantService.getOrganizationTreeList(groupEnterpriseNo);
        // 收集数据
        List<QueryUseInfoResponse> queryResList = new ArrayList<>();
        for (OrganizationTreeVo vo : orgList) {
            QueryUseInfoResponse queryRes = new QueryUseInfoResponse();
            queryRes.setOrgNo(vo.getOrgNo());
            queryRes.setOrgCode(vo.getOrgCode());
            queryRes.setOrgName(vo.getOrgName());
            queryRes.setUseStatus(useEnterpriseNo.contains(vo.getBindingEnterpriseNo()) ? 1 : 0);
            queryRes.setBindingEnterpriseNo(vo.getBindingEnterpriseNo());
            queryRes.setGroupEnterpriseNo(groupEnterpriseNo);
            queryResList.add(queryRes);
        }
        return queryResList;
    }
}
