package com.yyigou.dsrp.cdc.service.listener.config;

import com.yyigou.dsrp.cdc.model.constant.CdcMqConstant;
import com.yyigou.dsrp.cdc.service.listener.CompanyCertUpdateListener;
import com.yyigou.dsrp.cdc.service.listener.CompanyUpdateListener;
import com.yyigou.dsrp.cdc.service.listener.CustomerAssignListener;
import com.yyigou.dsrp.cdc.service.listener.SupplierAssignListener;
import com.yyigou.dsrp.cdc.service.listener.v2.CompanyProcessTaxPayerOrgListener;
import com.yyigou.dsrp.cdc.service.listener.v2.CompanyV2UpdateListener;
import com.yyigou.dsrp.cdc.service.listener.v2.CustomerV2AssignListener;
import org.apache.activemq.ActiveMQConnectionFactory;
import org.apache.activemq.command.ActiveMQTopic;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.jms.listener.DefaultMessageListenerContainer;
import org.springframework.stereotype.Component;
import org.springframework.util.backoff.BackOff;

import javax.annotation.Resource;

@Component
public class CdcListenerConfig {

    @Autowired
    private ActiveMQConnectionFactory activeMQConnectionFactory;
    @Autowired
    private CompanyCertUpdateListener companyCertUpdateListener;
    @Autowired
    private CompanyUpdateListener companyUpdateListener;

    @Resource(name = "companyV2UpdateListener")
    private CompanyV2UpdateListener companyV2UpdateListener;

    @Resource(name = "customerV2AssignListener")
    private CustomerV2AssignListener customerV2AssignListener;

    @Autowired
    private CustomerAssignListener customerAssignListener;
    @Autowired
    private SupplierAssignListener supplierAssignListener;
    @Autowired
    private BackOff backOff;

    @Autowired
    private CompanyProcessTaxPayerOrgListener companyProcessTaxPayerOrgListener;


    @Bean(name = "companyCertUpdateForCustomerSupplierListener")
    public DefaultMessageListenerContainer companyCertUpdateForCustomerSupplierListener() {
        DefaultMessageListenerContainer defaultMessageListenerContainer = new DefaultMessageListenerContainer();
        defaultMessageListenerContainer.setConnectionFactory(activeMQConnectionFactory);
        defaultMessageListenerContainer.setSubscriptionDurable(true);
        String clientId = "companyCertUpdateForCustomerSupplierListener";
        defaultMessageListenerContainer.setClientId(clientId);
        defaultMessageListenerContainer.setSubscriptionName(clientId);
        defaultMessageListenerContainer.setDestination(new ActiveMQTopic(CdcMqConstant.BDC_COMPANY_CERT_UPDATE_TOPIC));
        defaultMessageListenerContainer.setMessageListener(companyCertUpdateListener);
        defaultMessageListenerContainer.setSessionAcknowledgeMode(2);
        defaultMessageListenerContainer.setBackOff(backOff);
        return defaultMessageListenerContainer;
    }

    @Bean(name = "companyArchiveUpdateListener")
    public DefaultMessageListenerContainer companyArchiveUpdateListener() {
        DefaultMessageListenerContainer defaultMessageListenerContainer = new DefaultMessageListenerContainer();
        defaultMessageListenerContainer.setConnectionFactory(activeMQConnectionFactory);
        defaultMessageListenerContainer.setSubscriptionDurable(true);
        String clientId = "companyArchiveUpdateListener";
        defaultMessageListenerContainer.setClientId(clientId);
        defaultMessageListenerContainer.setSubscriptionName(clientId);
        defaultMessageListenerContainer.setDestination(new ActiveMQTopic(CdcMqConstant.CDC_COMPANY_UPDATE_TOPIC));
        defaultMessageListenerContainer.setMessageListener(companyUpdateListener);
        defaultMessageListenerContainer.setSessionAcknowledgeMode(2);
        defaultMessageListenerContainer.setBackOff(backOff);
        return defaultMessageListenerContainer;
    }

    @Bean(name = "companyV2ArchiveUpdateListener")
    public DefaultMessageListenerContainer companyV2ArchiveUpdateListener() {
        DefaultMessageListenerContainer defaultMessageListenerContainer = new DefaultMessageListenerContainer();
        defaultMessageListenerContainer.setConnectionFactory(activeMQConnectionFactory);
        defaultMessageListenerContainer.setSubscriptionDurable(true);
        String clientId = "companyV2ArchiveUpdateListener";
        defaultMessageListenerContainer.setClientId(clientId);
        defaultMessageListenerContainer.setSubscriptionName(clientId);
        defaultMessageListenerContainer.setDestination(new ActiveMQTopic(CdcMqConstant.CDC_COMPANY_UPDATE_TOPIC_V2));
        defaultMessageListenerContainer.setMessageListener(companyV2UpdateListener);
        defaultMessageListenerContainer.setSessionAcknowledgeMode(2);
        defaultMessageListenerContainer.setBackOff(backOff);
        return defaultMessageListenerContainer;
    }

    @Bean(name = "customerV2AssignMQListener")
    public DefaultMessageListenerContainer customerV2AssignMQListener() {
        DefaultMessageListenerContainer defaultMessageListenerContainer = new DefaultMessageListenerContainer();
        defaultMessageListenerContainer.setConnectionFactory(activeMQConnectionFactory);
        defaultMessageListenerContainer.setSubscriptionDurable(true);
        String clientId = "customerV2AssignMQListener";
        defaultMessageListenerContainer.setClientId(clientId);
        defaultMessageListenerContainer.setSubscriptionName(clientId);
        defaultMessageListenerContainer.setDestination(new ActiveMQTopic(CdcMqConstant.CDC_CUSTOMER_ASSIGN_TOPIC_V2));
        defaultMessageListenerContainer.setMessageListener(customerV2AssignListener);
        defaultMessageListenerContainer.setSessionAcknowledgeMode(2);
        defaultMessageListenerContainer.setBackOff(backOff);
        return defaultMessageListenerContainer;
    }


    @Bean(name = "customerArchiveAssignListener")
    public DefaultMessageListenerContainer customerAssignListener() {
        DefaultMessageListenerContainer defaultMessageListenerContainer = new DefaultMessageListenerContainer();
        defaultMessageListenerContainer.setConnectionFactory(activeMQConnectionFactory);
        defaultMessageListenerContainer.setSubscriptionDurable(true);
        String clientId = "customerAssignListener";
        defaultMessageListenerContainer.setClientId(clientId);
        defaultMessageListenerContainer.setSubscriptionName(clientId);
        defaultMessageListenerContainer.setDestination(new ActiveMQTopic(CdcMqConstant.CDC_CUSTOMER_ASSIGN_TOPIC));
        defaultMessageListenerContainer.setMessageListener(customerAssignListener);
        defaultMessageListenerContainer.setSessionAcknowledgeMode(2);
        defaultMessageListenerContainer.setBackOff(backOff);
        return defaultMessageListenerContainer;
    }

    @Bean(name = "supplierArchiveAssignListener")
    public DefaultMessageListenerContainer supplierAssignListener() {
        DefaultMessageListenerContainer defaultMessageListenerContainer = new DefaultMessageListenerContainer();
        defaultMessageListenerContainer.setConnectionFactory(activeMQConnectionFactory);
        defaultMessageListenerContainer.setSubscriptionDurable(true);
        String clientId = "supplierAssignListener";
        defaultMessageListenerContainer.setClientId(clientId);
        defaultMessageListenerContainer.setSubscriptionName(clientId);
        defaultMessageListenerContainer.setDestination(new ActiveMQTopic(CdcMqConstant.CDC_SUPPLIER_ASSIGN_TOPIC));
        defaultMessageListenerContainer.setMessageListener(supplierAssignListener);
        defaultMessageListenerContainer.setSessionAcknowledgeMode(2);
        defaultMessageListenerContainer.setBackOff(backOff);
        return defaultMessageListenerContainer;
    }


    @Bean(name = "createOrUpdateTaxPayerOrgListener")
    public DefaultMessageListenerContainer createOrUpdateTaxPayerOrgListener() {
        DefaultMessageListenerContainer defaultMessageListenerContainer = new DefaultMessageListenerContainer();
        defaultMessageListenerContainer.setConnectionFactory(activeMQConnectionFactory);
        defaultMessageListenerContainer.setSubscriptionDurable(true);
        String clientId = "createOrUpdateTaxPayerOrgListenerForCompany";
        defaultMessageListenerContainer.setClientId(clientId);
        defaultMessageListenerContainer.setSubscriptionName(clientId);
        defaultMessageListenerContainer.setDestination(new ActiveMQTopic(CdcMqConstant.CREATE_TAXPAYER_ORG_QUEUE));
        defaultMessageListenerContainer.setMessageListener(companyProcessTaxPayerOrgListener);
        defaultMessageListenerContainer.setSessionAcknowledgeMode(2);
        defaultMessageListenerContainer.setBackOff(backOff);
        return defaultMessageListenerContainer;
    }


}

