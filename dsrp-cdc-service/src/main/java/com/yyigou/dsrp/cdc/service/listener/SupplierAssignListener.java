package com.yyigou.dsrp.cdc.service.listener;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yyigou.ddc.services.ddc.uim.vo.OrganizationVo;
import com.yyigou.dsrp.cdc.client.v2.supplier.request.SupplierAssignDocExeRequest;
import com.yyigou.dsrp.cdc.client.v2.supplier.request.SupplierAssignMdmExeRequest;
import com.yyigou.dsrp.cdc.client.v2.supplier.response.SupplierAssignExeResponse;
import com.yyigou.dsrp.cdc.manager.integration.exeresult.MasterDataExecuteService;
import com.yyigou.dsrp.cdc.manager.integration.org.UimTenantService;
import com.yyigou.dsrp.cdc.service.common.MasterDataSyncService;
import com.yyigou.dsrp.cdc.service.listener.model.SupplierAssignModel;
import com.yyigou.dsrp.cdc.service.v2.MdmGray;
import com.yyigou.dsrp.cdc.service.v2.supplier.SupplierV2Service;
import com.yyigou.dsrp.gcs.client.executeRecord.request.UpdateExecuteRecordRequest;
import com.yyigou.dsrp.gcs.common.enums.executeRecord.ExecuteRecordExecuteResultEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jms.listener.SessionAwareMessageListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.jms.JMSException;
import javax.jms.Message;
import javax.jms.Session;
import javax.jms.TextMessage;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
@Slf4j
public class SupplierAssignListener implements SessionAwareMessageListener {
    @Resource
    private MasterDataSyncService masterDataSyncService;

    @Resource
    private MasterDataExecuteService masterDataExecuteService;

    @Resource
    private MdmGray mdmGray;

    @Resource
    private SupplierV2Service supplierV2Service;

    @Resource
    private UimTenantService uimTenantService;

    /**
     * 企业
     *
     * @param message
     * @param session
     * @throws JMSException
     */
    @Override
    public void onMessage(Message message, Session session) throws JMSException {
        //  获取消息
        ExecuteRecordExecuteResultEnum executeResultEnum = ExecuteRecordExecuteResultEnum.EXECUTE_SUCCESS;
        String errorMsg = "";
        SupplierAssignModel model = null;
        try {
            TextMessage textMessage = (TextMessage) message;
            log.warn("供应商分派消息处理,参数: {}" , textMessage.getText());
            //1、企业证照更换 修改影响的产品档案
            model = JSONObject.parseObject(textMessage.getText(), SupplierAssignModel.class);
            String genSupplierNo = masterDataSyncService.handSupplier(model);
            // 执行多组织兼容逻辑的分派
            try {
                log.warn("供应商分派消息处理,开始执行多组织兼容逻辑的分派,groupEnterpriseNo={},genSupplierNo={}", model.getGroupEnterpriseNo(), genSupplierNo);
                if (mdmGray.isEnterpriseGray(model.getGroupEnterpriseNo()) && StringUtils.isNotEmpty(genSupplierNo)) {
                    List<OrganizationVo> orgListByGroupEnterpriseNo = uimTenantService.findOrgListByGroupEnterpriseNo(model.getGroupEnterpriseNo());
                    Map<String, OrganizationVo> organizationMap = orgListByGroupEnterpriseNo.stream().collect(Collectors.toMap(OrganizationVo::getBindingEnterpriseNo, Function.identity()));
                    OrganizationVo manageOrganizationTreeVo = organizationMap.get(model.getGroupEnterpriseNo());
                    OrganizationVo useOrganizationTreeVo = organizationMap.get(model.getSubEnterpriseNo());
                    log.warn("供应商分派消息处理,打印管理组织和使用组织,manageOrganizationTreeVo={},useOrganizationTreeVo={}", manageOrganizationTreeVo, useOrganizationTreeVo);
                    if (null != manageOrganizationTreeVo && null != useOrganizationTreeVo) {
                        SupplierAssignMdmExeRequest supplierAssignExeRequest = new SupplierAssignMdmExeRequest();
                        supplierAssignExeRequest.setEnterpriseNo(model.getGroupEnterpriseNo());
                        supplierAssignExeRequest.setManageOrgNo(manageOrganizationTreeVo.getOrgNo());
                        List<SupplierAssignDocExeRequest> docList = new ArrayList<>();
                        SupplierAssignDocExeRequest supplierAssignDocExeRequest = new SupplierAssignDocExeRequest();
                        supplierAssignDocExeRequest.setSupplierCode(model.getSupplierCode());
                        supplierAssignDocExeRequest.setUseOrgNoList(Collections.singletonList(useOrganizationTreeVo.getOrgNo()));
                        docList.add(supplierAssignDocExeRequest);
                        supplierAssignExeRequest.setDocList(docList);

                        Map<String, String> orgNo2SupplierNoMap = new HashMap<>();
                        orgNo2SupplierNoMap.put(useOrganizationTreeVo.getOrgNo(), genSupplierNo);
                        supplierAssignExeRequest.setOrgNo2SupplierNoMap(orgNo2SupplierNoMap);

                        log.warn("供应商分派消息处理,开始执行多组织兼容逻辑的分派,请求参数: {}", JSON.toJSONString(supplierAssignExeRequest));
                        List<SupplierAssignExeResponse> supplierAssignExeResponses = supplierV2Service.assignMdmSupplier(supplierAssignExeRequest);
                        log.warn("供应商分派消息处理,执行多组织兼容逻辑的分派结果: {}", JSON.toJSONString(supplierAssignExeResponses));
                    }
                }
                log.warn("供应商分派消息处理,结束执行多组织兼容逻辑的分派");
            } catch (Exception e) {
                log.error("mdm接口兼容多组织供应商分派失败", e);
            }
        } catch (Exception e) {
            log.error("供应商分派消息处理,处理失败", e);
            executeResultEnum = ExecuteRecordExecuteResultEnum.EXECUTE_FAIL;
            errorMsg = e.getMessage();
            if (e instanceof NullPointerException){
                errorMsg = "空指针";
            }
        } finally {
            log.warn("供应商分派消息处理,处理结果: {} {}" , JSON.toJSONString(model), executeResultEnum.getName());
            // 主动发送消息回执, 告知broker消息已经被消费了
            try{
                if (StringUtils.isNotEmpty(errorMsg)) {
                    if (errorMsg.length() > 500) {
                        errorMsg = errorMsg.substring(0, 500);
                    }
                }
                if (ExecuteRecordExecuteResultEnum.EXECUTE_FAIL.equals(executeResultEnum) &&
                        model != null && model.getRecordId() != null && (model.getGenerateExecutionRecord() == null || !model.getGenerateExecutionRecord())){
                    UpdateExecuteRecordRequest updateExecuteRecordRequest = new UpdateExecuteRecordRequest();
                    updateExecuteRecordRequest.setId(model.getRecordId());
                    updateExecuteRecordRequest.setErrorMsg(errorMsg);
                    updateExecuteRecordRequest.setExecuteResult(executeResultEnum);
                    updateExecuteRecordRequest.setModifyNo("-1");
                    updateExecuteRecordRequest.setModifyName("system");
                    masterDataExecuteService.updateExecuteRecord(updateExecuteRecordRequest);
                }
            }catch (Exception e){
                log.error(e.getMessage(), e);
            }
            try {
                message.acknowledge();
            } catch (JMSException e) {
                log.error(e.getMessage(), e);
            }
        }

    }
}
