package com.yyigou.dsrp.cdc.service.supplier.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.yyigou.ddc.common.page.PageDto;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.ddc.common.util.ValidatorUtils;
import com.yyigou.dsrp.cdc.api.supply.dto.SupplierBankQueryDTO;
import com.yyigou.dsrp.cdc.api.supply.vo.SupplierBankPageVO;
import com.yyigou.dsrp.cdc.common.enums.LinkmanTypeEnum;
import com.yyigou.dsrp.cdc.common.util.OperationModel;
import com.yyigou.dsrp.cdc.dao.common.BankTypeDAO;
import com.yyigou.dsrp.cdc.dao.common.entity.BankType;
import com.yyigou.dsrp.cdc.dao.supplier.CompanyBankDAO;
import com.yyigou.dsrp.cdc.dao.supplier.entity.CompanyBank;
import com.yyigou.dsrp.cdc.service.supplier.SupplierBankService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@Service
@RequiredArgsConstructor
public class SupplierBankServiceImpl implements SupplierBankService {


    private final CompanyBankDAO companyBankDAO;

    private final BankTypeDAO bankTypeDAO;



    /**
     * 分页查询指定供应商的银行信息
     * @param operationModel
     * @param params
     * @param pageDto
     * @return
     */
    @Override
    public PageVo<SupplierBankPageVO> findBankPage(OperationModel operationModel, SupplierBankQueryDTO params, PageDto pageDto) {
        ValidatorUtils.checkTrueThrowEx(StringUtils.isEmpty(params.getSupplierNo()), "供应商编号不存在");

        Page<CompanyBank> pageInfo = PageHelper.startPage(pageDto.getPageIndex(), pageDto.getPageSize(), StringUtils.isBlank(pageDto.getOrderBy()) ? "create_time desc" : pageDto.getOrderBy());
        companyBankDAO.getCompanyBankListBySourceNoList(operationModel.getEnterpriseNo(), LinkmanTypeEnum.SUPPLY.getValue(), Collections.singletonList(params.getSupplierNo()));

        List<SupplierBankPageVO> supplierBankPageVOList = companyBankResultToVo(pageInfo.getResult());
        return new PageVo<>(pageInfo.getPageNum(), pageInfo.getPageSize(), pageInfo.getTotal(), supplierBankPageVOList);
    }

    private List<SupplierBankPageVO> companyBankResultToVo(List<CompanyBank> result) {
        if(CollectionUtils.isEmpty(result)) {
            return new ArrayList<>();
        }
        final List<BankType> bankTypeList = bankTypeDAO.getList();
        final Map<String, String> bankTypeMap = bankTypeList.stream().collect(Collectors.toMap(BankType::getBankCode, BankType::getBankName, (v1, v2) -> v1));
        return result.stream().map(entity -> {
                SupplierBankPageVO vo = new SupplierBankPageVO();
                BeanUtils.copyProperties(entity, vo);
                vo.setBankTypeName(bankTypeMap.get(vo.getBankType()));
                return vo;
        }).collect(Collectors.toList());
    }
}
