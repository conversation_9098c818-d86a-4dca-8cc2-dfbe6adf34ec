package com.yyigou.dsrp.cdc.service.company.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.yyigou.ddc.common.cache.redisson3.RedisUtils;
import com.yyigou.ddc.common.message.MessageUtil;
import com.yyigou.ddc.common.page.PageDto;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.ddc.common.service.SessionUser;
import com.yyigou.ddc.common.util.DateUtil;
import com.yyigou.ddc.common.util.StringUtil;
import com.yyigou.ddc.common.util.ValidatorUtils;
import com.yyigou.ddc.services.ddc.task.state.ExcelRowCheckResultEnum;
import com.yyigou.ddc.services.ddc.uap.enums.UapExcelRowCheckResultEnum;
import com.yyigou.ddc.services.dsrp.bdc.dto.CompanyCertDto;
import com.yyigou.ddc.services.dsrp.bdc.vo.CertControlVo;
import com.yyigou.dsrp.cdc.api.company.dto.CompanyImportDTO;
import com.yyigou.dsrp.cdc.api.company.vo.CompanyBasicVO;
import com.yyigou.dsrp.cdc.api.company.vo.CompanyDetailVO;
import com.yyigou.dsrp.cdc.client.company.response.CompanyInfoResponse;
import com.yyigou.dsrp.cdc.common.enums.CommonIfEnum;
import com.yyigou.dsrp.cdc.common.enums.CommonStatusEnum;
import com.yyigou.dsrp.cdc.common.enums.CompanyLogTypeEnum;
import com.yyigou.dsrp.cdc.common.enums.company.CompanyPartnershipEnum;
import com.yyigou.dsrp.cdc.common.util.CommonUtil;
import com.yyigou.dsrp.cdc.common.util.OperationModel;
import com.yyigou.dsrp.cdc.dao.company.CompanyDAO;
import com.yyigou.dsrp.cdc.dao.company.entity.Company;
import com.yyigou.dsrp.cdc.manager.integration.certControl.CertControlService;
import com.yyigou.dsrp.cdc.manager.integration.dccert.CatalogInfoService;
import com.yyigou.dsrp.cdc.manager.integration.dict.DictEnterpriseService;
import com.yyigou.dsrp.cdc.manager.integration.numberCenter.NumberCenterService;
import com.yyigou.dsrp.cdc.manager.integration.uim.CustomDocService;
import com.yyigou.dsrp.cdc.manager.integration.uim.response.CustomDocResponse;
import com.yyigou.dsrp.cdc.model.company.req.CompanyNameValidReq;
import com.yyigou.dsrp.cdc.model.company.req.CompanyQueryListPageReq;
import com.yyigou.dsrp.cdc.model.company.req.CompanySaveReq;
import com.yyigou.dsrp.cdc.model.company.req.CompanyUnionSocialCodeValidReq;
import com.yyigou.dsrp.cdc.model.constant.NumberCenterConstant;
import com.yyigou.dsrp.cdc.model.constant.ServiceConstant;
import com.yyigou.dsrp.cdc.model.constant.SystemConstant;
import com.yyigou.dsrp.cdc.model.enums.FactoryTypeEnum;
import com.yyigou.dsrp.cdc.service.common.CdcLogService;
import com.yyigou.dsrp.cdc.service.company.CompanyCheckService;
import com.yyigou.dsrp.cdc.service.company.CompanyService;
import com.yyigou.dsrp.cdc.service.listener.model.CompanyUpdateModel;
import com.yyigou.dsrp.cdc.service.utils.PageUtils;
import com.yyigou.dsrp.cert.client.certbase.CatalogInfoClient;
import com.yyigou.dsrp.cert.client.certbase.req.CatalogInfoBigClientReq;
import com.yyigou.dsrp.cert.client.certbase.req.CatalogInfoClientReq;
import com.yyigou.dsrp.cert.client.certbase.req.CatalogInfoRelationClientReq;
import com.yyigou.dsrp.cert.client.certbase.req.CertCompanyClientReq;
import com.yyigou.dsrp.cert.client.certbase.res.CatalogInfoClientRes;
import com.yyigou.dsrp.cert.common.enums.YesNoEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CompanyServiceImpl extends ServiceImpl<CompanyDAO, Company> implements CompanyService {

    @Resource
    private CompanyDAO companyDao;
    @Resource
    private CompanyCheckService companyCheckService;
    @Resource
    private NumberCenterService numberCenterService;
    @Resource
    private CatalogInfoService catalogInfoService;
    @Resource
    private CdcLogService cdcLogService;
    @Resource
    private CustomDocService customDocService;
    @Reference(check = false)
    private CatalogInfoClient catalogInfoClient;
    @Resource
    private DictEnterpriseService dictEnterpriseService;
    @Resource
    private CertControlService certControlService;
    @Resource
    private RedisUtils redisUtils;


    @Override
    public List<CompanyInfoResponse> findListByCompanyNoListIgnoringEnterpriseNo(List<String> companyNoList) {
        ValidatorUtils.checkEmptyThrowEx(companyNoList, "企业编号不能为空");
        LambdaQueryWrapper<Company> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(Company::getCompanyNo, companyNoList)
                .eq(Company::getStatus, CommonStatusEnum.EFFECTIVE.getValue());
        List<Company> customerList = companyDao.selectList(lambdaQueryWrapper);
        return convert(customerList);
    }

    @Override
    public List<CompanyInfoResponse> findListByCompanyNoList(String enterpriseNo, List<String> companyNoList) {
        ValidatorUtils.checkEmptyThrowEx(enterpriseNo, "企业编号不能为空");
        ValidatorUtils.checkEmptyThrowEx(companyNoList, "企业编号不能为空");
        LambdaQueryWrapper<Company> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(Company::getEnterpriseNo, enterpriseNo)
                .in(Company::getCompanyNo, companyNoList)
                .eq(Company::getStatus, CommonStatusEnum.EFFECTIVE.getValue());
        List<Company> customerList = companyDao.selectList(lambdaQueryWrapper);
        return convert(customerList);
    }

    @Override
    public List<CompanyInfoResponse> findListByCompanyCodeList(String enterpriseNo, List<String> companyCodeList) {
        ValidatorUtils.checkEmptyThrowEx(enterpriseNo, "企业编号不能为空");
        ValidatorUtils.checkEmptyThrowEx(companyCodeList, "企业编码不能为空");
        LambdaQueryWrapper<Company> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(Company::getEnterpriseNo, enterpriseNo)
                .in(Company::getCompanyCode, companyCodeList)
                .eq(Company::getStatus, CommonStatusEnum.EFFECTIVE.getValue());
        List<Company> customerList = companyDao.selectList(lambdaQueryWrapper);
        return convert(customerList);
    }

    @Override
    public List<CompanyInfoResponse> findListByCompanyNameList(String enterpriseNo, List<String> companyNameList) {
        ValidatorUtils.checkEmptyThrowEx(enterpriseNo, "企业编号不能为空");
        ValidatorUtils.checkEmptyThrowEx(companyNameList, "企业名称不能为空");
        LambdaQueryWrapper<Company> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(Company::getEnterpriseNo, enterpriseNo)
                .in(Company::getCompanyName, companyNameList)
                .eq(Company::getStatus, CommonStatusEnum.EFFECTIVE.getValue());
        List<Company> customerList = companyDao.selectList(lambdaQueryWrapper);
        return convert(customerList);
    }


    private List<CompanyInfoResponse> convert(List<Company> companyList) {
        List<CompanyInfoResponse> result = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(companyList)) {
            companyList.forEach(t -> {
                CompanyInfoResponse companyInfoResponse = new CompanyInfoResponse();
                BeanUtils.copyProperties(t, companyInfoResponse);
                result.add(companyInfoResponse);
                if (!StringUtil.isEmpty(t.getPartnership())) {
                    try {
                        List<CompanyPartnershipEnum> partnershipEnumList = new ArrayList<>();
                        List<String> partnershipList = JSON.parseArray(t.getPartnership(), String.class);
                        for (String s : partnershipList) {
                            partnershipEnumList.add(CompanyPartnershipEnum.getByType(s));
                        }
                        companyInfoResponse.setPartnershipText(String.join(",", partnershipEnumList.stream().map(CompanyPartnershipEnum::getName).collect(Collectors.toList())));
                    } catch (Exception e) {
                        log.error("合作关系解析类型失败" + JSON.toJSONString(e));
                    }

                }
            });
        }
        return result;
    }

    @Override
    public CompanyBasicVO saveCompany(OperationModel operationModel, CompanySaveReq companySaveReq) {
        String enterpriseNo = operationModel.getEnterpriseNo();
        ValidatorUtils.checkEmptyThrowEx(enterpriseNo, "租户编号不能为空");
        // 校验必填参数
        CompanyCheckService.validateRequiredField(companySaveReq);
        // 校验企业名称唯一
        CompanyNameValidReq companyNameValidReq = new CompanyNameValidReq();
        companyNameValidReq.setEnterpriseNo(enterpriseNo);
        companyNameValidReq.setCompanyName(companySaveReq.getCompanyName());
        companyNameValidReq.setCompanyNo(companySaveReq.getCompanyNo());
        if (!companyCheckService.validateCompanyNameUnique(companyNameValidReq)) {
            throw new RuntimeException("企业名称已存在");
        }
        // 校验社会信用代码唯一
        CompanyUnionSocialCodeValidReq unionSocialCodeValidReq = new CompanyUnionSocialCodeValidReq();
        unionSocialCodeValidReq.setEnterpriseNo(enterpriseNo);
        unionSocialCodeValidReq.setUnifiedSocialCode(companySaveReq.getUnifiedSocialCode());
        unionSocialCodeValidReq.setCompanyNo(companySaveReq.getCompanyNo());
        if (!companyCheckService.validateCompanyUnionSocialCodeUnique(unionSocialCodeValidReq)) {
            throw new RuntimeException("该统一社会信用代码已存在对应企业");
        }

        // 保存企业信息
        Company company = new Company();
        BeanUtils.copyProperties(companySaveReq, company);
        company.setEnterpriseNo(enterpriseNo);
        String companyNo = numberCenterService.createNumber(NumberCenterConstant.COMPANY_NO_GENERATE_KEY);
        company.setCompanyNo(companyNo);
        company.setCompanyCode(companyNo);
        CommonUtil.fillCreatInfo(operationModel, company);
        CommonUtil.fillOperateInfo(operationModel, company);
        companyDao.insert(company);

        // 保存资料文件
        saveOrUpdateCert(companySaveReq.getCatalogInfoBigClientReq(), enterpriseNo, companyNo, operationModel);

        // 记录日志
        cdcLogService.saveCompanyLog(operationModel, companyNo, CompanyLogTypeEnum.COMPANY_ADD.getType());

        CompanyBasicVO companyBasicVO = new CompanyBasicVO();
        BeanUtils.copyProperties(company, companyBasicVO);

        return companyBasicVO;
    }

    /**
     * 根据租户编号和企业编号获取企业档案详情
     *
     * @param enterpriseNo
     * @param companyNo
     * @return: {@link CompanyDetailVO}
     */
    @Override
    public CompanyDetailVO getDetailByEnterpriseAndCompanyNo(String enterpriseNo, String companyNo) {
        ValidatorUtils.checkEmptyThrowEx(enterpriseNo, "租户编号不能为空");
        ValidatorUtils.checkEmptyThrowEx(companyNo, "企业编号不能为空");
        Company company = companyDao.findByEnterpriseNoAndCompanyNo(enterpriseNo, companyNo);
        ValidatorUtils.checkEmptyThrowEx(company, "企业档案不存在");
        CompanyDetailVO companyDetailVO = new CompanyDetailVO();
        BeanUtils.copyProperties(company, companyDetailVO);
        // 填充信息
        fillCompanyDetailVO(enterpriseNo, companyDetailVO);

        return companyDetailVO;
    }

    @Override
    public CompanyDetailVO getDetailByEnterpriseAndCompanyName(String enterpriseNo, String companyName) {
        ValidatorUtils.checkEmptyThrowEx(enterpriseNo, "租户编号不能为空");
        ValidatorUtils.checkEmptyThrowEx(companyName, "企业名称不能为空");
        List<Company> companyList = companyDao.findByEnterpriseNoAndCompanyList(enterpriseNo, companyName);
        if (CollectionUtil.isEmpty(companyList)) {
            return null;
        }
        return getDetailByEnterpriseAndCompanyNo(enterpriseNo, companyList.get(0).getCompanyNo());
    }

    /**
     * 填充企业枚举项、自定义档案等信息
     *
     * @param enterpriseNo
     * @param companyDetailVO
     * @return:
     */
    private void fillCompanyDetailVO(String enterpriseNo, CompanyDetailVO companyDetailVO) {
        // 合作关系
        companyDetailVO.setPartnershipText(convertPartnership(companyDetailVO.getPartnership()));
        // 纳税类别
        if (StringUtils.isNotBlank(companyDetailVO.getTaxCategory())) {
            final Map<String, CustomDocResponse> itemByCustomDocCode = customDocService.getItemByCustomDocCode(enterpriseNo, SystemConstant.TAX_CATEGORY);
            companyDetailVO.setTaxCategoryName(itemByCustomDocCode.getOrDefault(companyDetailVO.getTaxCategory(), new CustomDocResponse()).getDocItemName());
        }
        // 经济类型
        if (StringUtils.isNotBlank(companyDetailVO.getEconomicType())) {
            final Map<String, CustomDocResponse> itemByCustomDocCode = customDocService.getItemByCustomDocCode(enterpriseNo, SystemConstant.ECONOMIC_TYPE);
            companyDetailVO.setEconomicTypeName(itemByCustomDocCode.getOrDefault(companyDetailVO.getEconomicType(), new CustomDocResponse()).getDocItemName());
        }
        // 企业注册地域
        if (Objects.nonNull(companyDetailVO.getFactoryType())) {
            companyDetailVO.setFactoryTypeName(FactoryTypeEnum.getByType(companyDetailVO.getFactoryType()).getName());
        }
        // 营业期限
        if (StringUtils.isNotBlank(companyDetailVO.getBusinessEndTime())) {
            companyDetailVO.setBusinessEndTimeName(companyDetailVO.getBusinessEndTime());
        } else {
            if (CommonIfEnum.YES.getValue().equals(companyDetailVO.getBusinessLongTerm())) {
                companyDetailVO.setBusinessEndTimeName("长期");
            }
        }
        // 是否关联企业
        if (Objects.nonNull(companyDetailVO.getIsAssociatedEnterprise())) {
            companyDetailVO.setIsAssociatedEnterpriseName(CommonIfEnum.getNameByValue(companyDetailVO.getIsAssociatedEnterprise()));
        }
        // 是否医疗机构
        companyDetailVO.setIsMedicalInstitutionName(CommonIfEnum.YES.getValue().equals(companyDetailVO.getIsMedicalInstitution()) ? CommonIfEnum.YES.getName() : CommonIfEnum.NO.getName());
        // 所在区域
        if (StringUtils.isNotBlank(companyDetailVO.getRegionCode())) {
            Map<String, String> areaMap = dictEnterpriseService.findAreaMapByCodes(Lists.newArrayList(companyDetailVO.getRegionCode()));
            if (MapUtils.isNotEmpty(areaMap)) {
                companyDetailVO.setRegionName(areaMap.get(companyDetailVO.getRegionCode()));
            }
        }
    }

    private String convertPartnership(String partnership) {
        if (StringUtils.isNotBlank(partnership)) {
            List<CompanyPartnershipEnum> partnershipEnumList = new ArrayList<>();
            List<String> partnershipList = JSON.parseArray(partnership, String.class);
            for (String ps : partnershipList) {
                if (CompanyPartnershipEnum.FACTORY.getType().equals(ps)) {
                    continue;
                }
                if (Objects.nonNull(CompanyPartnershipEnum.getByType(ps))) {
                    partnershipEnumList.add(CompanyPartnershipEnum.getByType(ps));
                }
            }
            return String.join(",", partnershipEnumList.stream().map(n -> n.getName()).collect(Collectors.toList()));
        }
        return null;
    }

    /**
     * 修改企业档案
     *
     * @param operationModel
     * @param companySaveReq
     * @return: {@link CompanyUpdateModel}
     */
    @Override
    public CompanyUpdateModel updateCompany(OperationModel operationModel, CompanySaveReq companySaveReq) {
        String enterpriseNo = operationModel.getEnterpriseNo();
        ValidatorUtils.checkEmptyThrowEx(enterpriseNo, "租户编号不能为空");
        String companyNo = companySaveReq.getCompanyNo();
        ValidatorUtils.checkEmptyThrowEx(companyNo, "企业编号不能为空");
        Company company = companyDao.findByEnterpriseNoAndCompanyNo(enterpriseNo, companyNo);
        ValidatorUtils.checkEmptyThrowEx(company, "企业档案不存在");
        // 校验必填参数
        CompanyCheckService.validateRequiredField(companySaveReq);
        // 校验企业名称唯一
        CompanyNameValidReq companyNameValidReq = new CompanyNameValidReq();
        companyNameValidReq.setEnterpriseNo(enterpriseNo);
        companyNameValidReq.setCompanyName(companySaveReq.getCompanyName());
        companyNameValidReq.setCompanyNo(companyNo);
        if (!companyCheckService.validateCompanyNameUnique(companyNameValidReq)) {
            throw new RuntimeException("企业名称已存在");
        }
        // 校验社会信用代码唯一
        CompanyUnionSocialCodeValidReq unionSocialCodeValidReq = new CompanyUnionSocialCodeValidReq();
        unionSocialCodeValidReq.setEnterpriseNo(enterpriseNo);
        unionSocialCodeValidReq.setUnifiedSocialCode(companySaveReq.getUnifiedSocialCode());
        unionSocialCodeValidReq.setCompanyNo(companyNo);
        if (!companyCheckService.validateCompanyUnionSocialCodeUnique(unionSocialCodeValidReq)) {
            throw new RuntimeException("该统一社会信用代码已存在对应企业");
        }

        // 修改企业信息
        Company newCompany = new Company();
        BeanUtils.copyProperties(companySaveReq, newCompany);
        newCompany.setEnterpriseNo(enterpriseNo);
        CommonUtil.fillOperateInfo(operationModel, newCompany);
        LambdaUpdateWrapper<Company> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.eq(Company::getEnterpriseNo, enterpriseNo).eq(Company::getCompanyNo, companyNo);
        companyDao.update(newCompany, updateWrapper);

        // 保存资料文件
        saveOrUpdateCert(companySaveReq.getCatalogInfoBigClientReq(), enterpriseNo, companyNo, operationModel);

        // 记录日志
        cdcLogService.saveCompanyLog(operationModel, companyNo, CompanyLogTypeEnum.COMPANY_ADD.getType());

        CompanyUpdateModel companyUpdateModel = new CompanyUpdateModel();
        companyUpdateModel.setOldCompany(company);
        companyUpdateModel.setNewCompany(newCompany);
        companyUpdateModel.setOperationModel(operationModel);
        companyUpdateModel.setEnterpriseNo(enterpriseNo);
        companyUpdateModel.setUpdateSuccess(Boolean.TRUE);

        return companyUpdateModel;
    }


    /**
     * 获取本企业证照
     *
     * @param enterpriseNo
     * @return
     */
    @Override
    public CompanyDetailVO getEnterpriseDetail(String enterpriseNo) {
        final Company company = companyDao.findByEnterpriseNoAndCompanyNo(enterpriseNo, enterpriseNo);
        //和老接口一致不报错
        if (company == null) {
            return new CompanyDetailVO();
        }
        return getDetailByEnterpriseAndCompanyNo(enterpriseNo, enterpriseNo);
    }

    /**
     * 迪安外部推送产品档案的使用场景
     * 批量创建企业档案
     *
     * @param enterpriseNo
     * @param companyNameList
     * @return
     */
    @Override
    public List<CompanyInfoResponse> saveAndGetCompany(String enterpriseNo, List<String> companyNameList, String employerNo, String userName) {
        List<CompanyInfoResponse> result = new ArrayList<>();
        // 把companyNameList中的字符串去掉前后空格
        companyNameList = companyNameList.stream().map(String::trim).collect(Collectors.toList());
        LambdaQueryWrapper<Company> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(Company::getEnterpriseNo, enterpriseNo).in(Company::getCompanyName, companyNameList)
                .eq(Company::getStatus, CommonStatusEnum.EFFECTIVE.getValue());
        List<Company> customerList = companyDao.selectList(lambdaQueryWrapper);
        Map<String, Company> customerMap = customerList.stream().collect(Collectors.toMap(Company::getCompanyName, Function.identity(), (o, w) -> o));
        List<String> nonExistentCompanyList = companyNameList.stream().filter(t -> !customerMap.containsKey(t)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(nonExistentCompanyList)) {
            RLock lock = redisUtils.getRedissonClient().getLock(String.format(ServiceConstant.SERVICE_NAME + "_CompanyServiceImpl_saveAndGetCompany_%s", enterpriseNo));
            try {
                if (lock.tryLock(5, TimeUnit.SECONDS)) {
                    //获取锁之后再次查询品牌防止已被别的线程创建
                    customerList = companyDao.selectList(lambdaQueryWrapper);
                    Map<String, Company> reCustomerMap = customerList.stream().collect(Collectors.toMap(Company::getCompanyName, Function.identity(), (o, w) -> o));
                    nonExistentCompanyList = companyNameList.stream().filter(t -> !reCustomerMap.containsKey(t)).collect(Collectors.toList());
                    if (!CollectionUtil.isEmpty(nonExistentCompanyList)) {
                        List<Company> addList = new ArrayList<>();
                        List<String> companyNoList = numberCenterService.batchGenerateNoList(NumberCenterConstant.COMPANY_NO_GENERATE_KEY, nonExistentCompanyList.size());
                        AtomicInteger index = new AtomicInteger(-1);
                        nonExistentCompanyList.forEach(t -> {
                            Company company = new Company();
                            company.setEnterpriseNo(enterpriseNo);
                            company.setCompanyNo(companyNoList.get(index.incrementAndGet()));
                            company.setCompanyCode(company.getCompanyNo());
                            company.setCompanyName(t);
                            company.setUnifiedSocialCode("/");
                            company.setStatus(1);
                            company.setCreateName(userName);
                            company.setCreateNo(employerNo);
                            company.setCreateTime(DateUtil.getCurrentDate());
                            company.setOperateNo(employerNo);
                            company.setOperateName(userName);
                            addList.add(company);
                        });
                        companyDao.addBatch(addList);
                    }
                }
            } catch (InterruptedException e) {
                log.error("创建企业档案异常", e);
            } finally {
                lock.unlock();
            }
        }
        //reSelect
        customerList = companyDao.selectList(lambdaQueryWrapper);
        if (CollectionUtil.isNotEmpty(customerList)) {
            customerList.forEach(t -> {
                CompanyInfoResponse companyInfoResponse = new CompanyInfoResponse();
                BeanUtils.copyProperties(t, companyInfoResponse);
                result.add(companyInfoResponse);
            });
        }
        return result;
    }


    /**
     * 新增或更新企业资料
     *
     * @param catalogInfoBigClientReq
     * @param enterpriseNo
     * @param companyNo
     * @param operationModel
     * @return:
     */
    public void saveOrUpdateCert(CatalogInfoBigClientReq catalogInfoBigClientReq, String enterpriseNo, String companyNo, OperationModel operationModel) {
        if (Objects.nonNull(catalogInfoBigClientReq) && (CollectionUtils.isNotEmpty(catalogInfoBigClientReq.getCatalogInfoClientList())
                || CollectionUtils.isNotEmpty(catalogInfoBigClientReq.getDeleteIdList()))) {
            List<CatalogInfoClientReq> catalogInfoClientReqList = catalogInfoBigClientReq.getCatalogInfoClientList();
            if (CollectionUtils.isNotEmpty(catalogInfoClientReqList)) {
                for (CatalogInfoClientReq catalogInfoClientReq : catalogInfoClientReqList) {
                    catalogInfoClientReq.setEnterpriseNo(enterpriseNo);
                    catalogInfoClientReq.setUserNo(operationModel.getEmployerNo());
                    catalogInfoClientReq.setUserName(operationModel.getUserName());

                    CertCompanyClientReq certCompanyClientReq = new CertCompanyClientReq();
                    certCompanyClientReq.setCompanyNo(companyNo);
                    certCompanyClientReq.setObjCode("company");
                    catalogInfoClientReq.setCertCompanyClientReq(certCompanyClientReq);
                }
            }
            catalogInfoBigClientReq.setEnterpriseNo(enterpriseNo);
            catalogInfoBigClientReq.setUserNo(operationModel.getUserNo());
            catalogInfoBigClientReq.setUserName(operationModel.getUserName());
            catalogInfoBigClientReq.setEmployeeNo(operationModel.getEmployerNo());

            // 新增或更新资料
            catalogInfoService.saveOrUpdateCertBatch(catalogInfoBigClientReq);
        }
    }


    @Override
    public void updateCompany(OperationModel operationModel, Company company) {
        CommonUtil.fillOperateInfo(operationModel, company);
        companyDao.updateById(company);
    }

    /**
     * 根据租户编号+企业编码集合查询企业信息
     *
     * @param enterpriseNo
     * @param companyNoList
     * @return: {@link List< Company>}
     */
    @Override
    public List<Company> findByEnterpriseNoAndCompanyNoList(String enterpriseNo, List<String> companyNoList) {
        ValidatorUtils.checkEmptyThrowEx(enterpriseNo, "租户编号不能为空");
        ValidatorUtils.checkEmptyThrowEx(companyNoList, "企业编码集合不能为空");
        return companyDao.findByEnterpriseNoAndCompanyNoList(enterpriseNo, companyNoList);
    }

    @Override
    public PageVo<CompanyDetailVO> findCompanyListPage(CompanyQueryListPageReq queryReq, PageDto pageDTO) {
        ValidatorUtils.checkEmptyThrowEx(queryReq, "查询条件不能为空");
        String enterpriseNo = queryReq.getEnterpriseNo();
        ValidatorUtils.checkEmptyThrowEx(enterpriseNo, "租户编号不能为空");
        if (StringUtils.isBlank(pageDTO.getOrderBy())) {
            pageDTO.setOrderBy("operate_time desc");
        }
        Page<Company> page = PageHelper.startPage(pageDTO.getPageIndex(), pageDTO.getPageSize(), pageDTO.getOrderBy());
        companyDao.findCompanyListPage(queryReq);
        return PageUtils.convertPageVo(page, data -> {
            List<CompanyDetailVO> detailVOList = new ArrayList<>();
            if (Objects.nonNull(data)) {
                data.forEach(company -> {
                    CompanyDetailVO companyDetailVO = new CompanyDetailVO();
                    BeanUtils.copyProperties(company, companyDetailVO);
                    // 填充信息
                    fillCompanyDetailVO(enterpriseNo, companyDetailVO);
                    detailVOList.add(companyDetailVO);
                });
            }
            return detailVOList;
        });
    }

    public PageVo<CompanyDetailVO> findCompanyListPageForDing(CompanyQueryListPageReq queryReq, PageDto pageDTO) {
        PageVo<CompanyDetailVO> companyListPage = findCompanyListPage(queryReq, pageDTO);

        // 填充企业资料列表
        for (CompanyDetailVO row : companyListPage.getRows()) {
            CatalogInfoRelationClientReq catalogInfoRelationClientReq = new CatalogInfoRelationClientReq();
            catalogInfoRelationClientReq.setEnterpriseNo(queryReq.getEnterpriseNo());
            catalogInfoRelationClientReq.setRelation(row.getCompanyNo());
            catalogInfoRelationClientReq.setObjCode("company");
            catalogInfoRelationClientReq.setRetCertTypeInfos(YesNoEnum.YES.getValue());
            List<CatalogInfoClientRes> catalogInfoClientRes = MessageUtil.ensureCallResultSuccess(catalogInfoClient.findByRelation(catalogInfoRelationClientReq));
            row.setCatalogInfoList(catalogInfoClientRes);
        }
        return companyListPage;
    }

    /**
     * 根据统一社会信用代码查询企业信息
     *
     * @param enterpriseNo
     * @param unifiedSocialCodeList
     * @return: {@link List< Company>}
     */
    @Override
    public List<Company> findByUnifiedSocialCodeList(String enterpriseNo, List<String> unifiedSocialCodeList) {
        ValidatorUtils.checkEmptyThrowEx(enterpriseNo, "租户编号不能为空");
        ValidatorUtils.checkEmptyThrowEx(unifiedSocialCodeList, "统一社会信用代码集合不能为空");
        return companyDao.findByEnterpriseNoAndUnifiedSocialCodeList(enterpriseNo, unifiedSocialCodeList);
    }

    /**
     * 根据企业名称查询企业信息
     *
     * @param enterpriseNo
     * @param companyNameList
     * @return: {@link List< Company>}
     */
    @Override
    public List<Company> findByCompanyNameList(String enterpriseNo, List<String> companyNameList) {
        ValidatorUtils.checkEmptyThrowEx(enterpriseNo, "租户编号不能为空");
        ValidatorUtils.checkEmptyThrowEx(companyNameList, "企业名称集合不能为空");
        return companyDao.findByEnterpriseNoAndCompanyName(enterpriseNo, companyNameList);
    }

    /**
     * 批量保存导入企业档案
     *
     * @param sessionUser
     * @param importData
     * @return: {@link List<  CompanyImportDTO >}
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<CompanyImportDTO> saveImportCompanyBatch(SessionUser sessionUser, List<CompanyImportDTO> importData) {
        String enterpriseNo = sessionUser.getEnterpriseNo();
        //二次校验成功的数据 走保存逻辑
        List<CompanyImportDTO> verificationSuccessList = importData.stream().filter(t -> ExcelRowCheckResultEnum.SUCCESS.getCheckCode().equals(t.getExcelRowCheckResultEnumCode())).collect(Collectors.toList());
        try {
            if (CollectionUtils.isNotEmpty(verificationSuccessList)) {
                // 批量获取编码
                List<String> companyNoList = numberCenterService.batchGenerateNoList(NumberCenterConstant.COMPANY_NO_GENERATE_KEY, verificationSuccessList.size());
                List<Company> companyList = new ArrayList<>();
                AtomicInteger atomicCompanyNoIndex = new AtomicInteger(0);
                for (CompanyImportDTO companyImportDTO : importData) {
                    // 是否医疗机构
                    setDefaultInstitutionFields(companyImportDTO);
                    Company company = new Company();
                    BeanUtils.copyProperties(companyImportDTO, company);
                    company.setEnterpriseNo(enterpriseNo);
                    String companyNo = companyNoList.get(atomicCompanyNoIndex.getAndIncrement());
                    company.setCompanyNo(companyNo);
                    company.setCompanyCode(companyNo);
                    CommonUtil.fillCreatInfo(sessionUser, company);
                    CommonUtil.fillOperateInfo(sessionUser, company);
                    companyList.add(company);
                }
                if (CollectionUtils.isNotEmpty(companyList)) {
                    companyDao.addBatch(companyList);
                }
            }
        } catch (Exception e) {
            //保存全部失败 给前端返回异常数据
            verificationSuccessList.forEach(t -> {
                t.setExcelRowCheckResultEnumCode(UapExcelRowCheckResultEnum.FAIL.getCheckCode());
                t.setExcelRowCheckResultEnumMessage("数据保存异常!");
            });
            log.warn("保存导入企业档案数据异常", e);
        }
        return verificationSuccessList;
    }

    @Override
    public List<CertControlVo> checkQualification(OperationModel operationModel, CompanyCertDto companyCertDto) {
        ValidatorUtils.checkEmptyThrowEx(companyCertDto, "入参不能为空");
        String enterpriseNo = operationModel.getEnterpriseNo();//当前登录租户
        if (companyCertDto.getId() == null) {
            return new ArrayList<>();
        }
        List<CertControlVo> result = certControlService.checkCustomerSupplierCanSave(enterpriseNo, companyCertDto.getCompanyNo(), companyCertDto.getId(), companyCertDto.getCertTypeList());
        return result;
    }


    private void setDefaultInstitutionFields(CompanyImportDTO importData) {
        // 非医疗机构直接设置默认值
        if (CommonIfEnum.NO.getValue().equals(importData.getIsMedicalInstitution())) {
            importData.setInstitutionalType(null);
            importData.setHospitalType(null);
            importData.setHospitalClass(null);
            importData.setInstitutionalTypeName(null);
            importData.setHospitalTypeName(null);
            importData.setHospitalClassName(null);
            return;
        }

        // 是医疗机构但机构类型为空时设置默认值
        if (StringUtils.isBlank(importData.getInstitutionalType())) {
            importData.setInstitutionalType("yy");
            importData.setInstitutionalTypeName("医院");
        }

        // 设置医院类型和等级的默认值
        importData.setHospitalTypeName(StringUtils.isBlank(importData.getHospitalType()) ? "公立" : importData.getHospitalTypeName());
        importData.setHospitalClassName(importData.getHospitalClass() != null ? importData.getHospitalClassName() : "无等级");
        importData.setHospitalType(StringUtils.isBlank(importData.getHospitalType()) ? "yy" : importData.getHospitalType());
        importData.setHospitalClass(importData.getHospitalClass() != null ? importData.getHospitalClass() : 0);

        // 如果机构类型不是 "yy"，则重置医院类型和等级
        if (!"yy".equals(importData.getInstitutionalType())) {
            importData.setHospitalType(null);
            importData.setHospitalClass(null);
            importData.setHospitalTypeName(null);
            importData.setHospitalClassName(null);
        }
    }
}
