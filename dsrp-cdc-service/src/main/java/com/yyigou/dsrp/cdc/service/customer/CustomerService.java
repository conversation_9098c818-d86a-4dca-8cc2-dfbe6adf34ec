package com.yyigou.dsrp.cdc.service.customer;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yyigou.ddc.common.page.PageDto;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.dsrp.cdc.api.customer.dto.QueryPageCustomerByOrgDTO;
import com.yyigou.dsrp.cdc.api.customer.dto.QueryPageCustomerBySpecifyOrgDTO;
import com.yyigou.dsrp.cdc.api.customer.dto.QueryPageCustomerDTO;
import com.yyigou.dsrp.cdc.api.customer.dto.SaveCustomerDTO;
import com.yyigou.dsrp.cdc.api.customer.vo.CustomerPageVO;
import com.yyigou.dsrp.cdc.api.customer.vo.CustomerVO;
import com.yyigou.dsrp.cdc.client.common.response.QueryUseInfoResponse;
import com.yyigou.dsrp.cdc.client.customer.request.CustomerComponentRequest;
import com.yyigou.dsrp.cdc.client.customer.request.CustomerFindRequest;
import com.yyigou.dsrp.cdc.client.customer.request.CustomerNameRequest;
import com.yyigou.dsrp.cdc.client.customer.request.CustomerNoRequest;
import com.yyigou.dsrp.cdc.client.customer.request.CustomerPriceCateRequest;
import com.yyigou.dsrp.cdc.client.customer.response.CustomerComponentResponse;
import com.yyigou.dsrp.cdc.client.customer.response.CustomerInfoResponse;
import com.yyigou.dsrp.cdc.client.customer.response.CustomerResponse;
import com.yyigou.dsrp.cdc.client.customer.response.response.CompanyLinkmanResponse;
import com.yyigou.dsrp.cdc.common.util.OperationModel;
import com.yyigou.dsrp.cdc.dao.customer.entity.Customer;

import java.util.List;

/**
 * <p>
 * 客户表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-16
 */
public interface CustomerService extends IService<Customer> {

    /**
     * 根据客户名称查询客户信息
     *
     * @param request
     * @return: {@link CustomerInfoResponse}
     */
    List<CustomerInfoResponse> findCustomerByName(CustomerNameRequest request);

    /**
     * 根据客户编号集合查询客户信息
     *
     * @param request
     * @return: {@link List< CustomerInfoResponse>}
     */
    List<CustomerInfoResponse> findCustomerByNo(CustomerNoRequest request);

    /**
     * 根据价格体系查询客户信息
     *
     * @param request
     * @return: {@link List<CustomerInfoResponse>}
     */
    List<CustomerInfoResponse> findCustomerByPriceCate(CustomerPriceCateRequest request);

    /**
     * 按条件查询客户信息
     *
     * @param enterpriseNo
     * @param request
     * @return
     */
    List<CustomerResponse> findCustomer(String enterpriseNo, CustomerFindRequest request);

    List<String> findCustomerUserList(String enterpriseNo, String customerCode);


    String saveCustomer(OperationModel operationModel, SaveCustomerDTO params, String logRecord);

    Boolean updateCustomer(OperationModel operationModel, SaveCustomerDTO params);

    Boolean deleteCustomer(OperationModel operationModel, List<String> customerNoList);


    CustomerVO getCustomer(String enterpriseNo, String customerNo);

    /**
     * 根据企业编号查询客户档案
     *
     * @param enterpriseNo
     * @param companyNo
     * @return: {@link List< Customer>}
     */
    List<Customer> findCustomerByCompanyNo(String enterpriseNo, String companyNo);

    int updateCustomer(Customer customer);

    /**
     * 根据客户编码查询客户档案分配组织租户编号
     *
     * @param groupEnterpriseNo
     * @param customerCode
     * @return: {@link List< String>}
     */
    List<String> findCustomerAssignOrgEnterpriseNo(String groupEnterpriseNo, String customerCode);

    /**
     * 根据客户编码查询客户基本信息
     *
     * @param enterpriseNo
     * @param customerCode
     * @return: {@link Customer}
     */
    Customer getCustomerByCustomerCode(String enterpriseNo, String customerCode);


    Boolean checkOnlyCode(OperationModel operationModel, String no, String code);

    Boolean checkOnlyName(OperationModel operationModel, String no, String name);


    PageVo<CustomerPageVO> queryPageCustomer(OperationModel operationModel, QueryPageCustomerDTO params, PageDto pageDto);

    Long findExamCount(OperationModel operationModel);

    PageVo<CustomerPageVO> queryPageCustomerByGroup(OperationModel operationModel, QueryPageCustomerDTO params, PageDto pageDto);


    PageVo<CustomerPageVO> queryPageCustomerByOrg(OperationModel operationModel, QueryPageCustomerByOrgDTO params, PageDto pageDto);


    PageVo<CustomerPageVO> queryPageCustomerBySpecifyOrg(OperationModel operationModel, QueryPageCustomerBySpecifyOrgDTO params, PageDto pageDto);

    PageVo<CustomerPageVO> queryPageCustomerByCompatibleOrg(OperationModel operationModel, QueryPageCustomerBySpecifyOrgDTO params, PageDto pageDto);


    List<CompanyLinkmanResponse> getLinkmanListByCustomerNo(String enterpriseNo, String customerNo);

    List<CompanyLinkmanResponse> getLinkmanListByCustomerNoList(String enterpriseNo, List<String> customerNoList);


    PageVo<CustomerComponentResponse> selectCustomerPageForCommonComponent(CustomerComponentRequest params, PageDto pageDto);

    List<CustomerComponentResponse> selectCustomerListForCommonComponent(CustomerComponentRequest params);

    CustomerInfoResponse selectCustomerInfo(String enterpriseNo, String customerNo);


    List<QueryUseInfoResponse> queryUseInfo(String enterpriseNo, String customerCode);
}
