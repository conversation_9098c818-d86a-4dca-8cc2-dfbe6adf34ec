package com.yyigou.dsrp.cdc.service.company;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yyigou.dsrp.cdc.client.company.request.CompanyForOpenFindRequest;
import com.yyigou.dsrp.cdc.client.company.response.CompanyInfoResponse;
import com.yyigou.dsrp.cdc.dao.company.entity.Company;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public interface CompanyForOpenService extends IService<Company> {
    List<CompanyInfoResponse> findList(CompanyForOpenFindRequest params);
}
