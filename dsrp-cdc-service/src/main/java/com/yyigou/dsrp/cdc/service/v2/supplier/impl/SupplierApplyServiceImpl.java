package com.yyigou.dsrp.cdc.service.v2.supplier.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.yyigou.ddc.common.error.ErrorCode;
import com.yyigou.ddc.common.exception.BusinessException;
import com.yyigou.ddc.common.page.PageDto;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.ddc.common.util.DateUtil;
import com.yyigou.ddc.common.util.ValidatorUtils;
import com.yyigou.ddc.services.ddc.uap.domain.vo.GradeAutoReviewConfigVO;
import com.yyigou.ddc.services.ddc.uim.vo.EmployeeVo;
import com.yyigou.ddc.services.ddc.uim.vo.OrganizationVo;
import com.yyigou.ddc.services.dlog.dto.DLogLevel;
import com.yyigou.dsrp.cdc.api.v2.supplier.vo.SupplierApplyDetailVO;
import com.yyigou.dsrp.cdc.api.v2.supplier.vo.SupplierApplyPageVO;
import com.yyigou.dsrp.cdc.api.v2.supplier.vo.SupplierVO;
import com.yyigou.dsrp.cdc.common.enums.ApplyResultEnum;
import com.yyigou.dsrp.cdc.common.enums.CertSourceTypeEnum;
import com.yyigou.dsrp.cdc.common.enums.CommonIfEnum;
import com.yyigou.dsrp.cdc.common.enums.DeletedEnum;
import com.yyigou.dsrp.cdc.common.enums.supplier.SupplierApplyAuditStatusEnum;
import com.yyigou.dsrp.cdc.common.enums.supplier.SupplierApplyTypeEnum;
import com.yyigou.dsrp.cdc.common.util.BeanUtil;
import com.yyigou.dsrp.cdc.common.util.CommonUtil;
import com.yyigou.dsrp.cdc.common.util.OperationModel;
import com.yyigou.dsrp.cdc.dao.v2.company.entity.CompanyV2;
import com.yyigou.dsrp.cdc.dao.v2.supplier.SupplierApplyDAO;
import com.yyigou.dsrp.cdc.dao.v2.supplier.SupplierApplyItemDAO;
import com.yyigou.dsrp.cdc.dao.v2.supplier.entity.SupplierApply;
import com.yyigou.dsrp.cdc.dao.v2.supplier.entity.SupplierApplyItem;
import com.yyigou.dsrp.cdc.dao.v2.supplier.entity.SupplierBase;
import com.yyigou.dsrp.cdc.dao.v2.supplier.entity.SupplierV2;
import com.yyigou.dsrp.cdc.manager.integration.cert.CertService;
import com.yyigou.dsrp.cdc.manager.integration.cert.req.CompanyCertBasicRequest;
import com.yyigou.dsrp.cdc.manager.integration.cert.req.CompanyCertUpsertRequest;
import com.yyigou.dsrp.cdc.manager.integration.dict.DictEnterpriseService;
import com.yyigou.dsrp.cdc.manager.integration.dlog.BusinessLogService;
import com.yyigou.dsrp.cdc.manager.integration.uap.GradeControlService;
import com.yyigou.dsrp.cdc.manager.integration.uim.CustomDocService;
import com.yyigou.dsrp.cdc.manager.integration.uim.EmployeeService;
import com.yyigou.dsrp.cdc.manager.integration.uim.OrganizationService;
import com.yyigou.dsrp.cdc.manager.integration.uim.TransactionTypeService;
import com.yyigou.dsrp.cdc.manager.integration.uim.response.CustomDocResponse;
import com.yyigou.dsrp.cdc.model.constant.BillNameConstant;
import com.yyigou.dsrp.cdc.model.constant.SystemConstant;
import com.yyigou.dsrp.cdc.model.constant.ViewNameConstant;
import com.yyigou.dsrp.cdc.model.v2.company.req.CompanyBankReq;
import com.yyigou.dsrp.cdc.model.v2.company.req.CompanyCertReq;
import com.yyigou.dsrp.cdc.model.v2.company.req.CompanyLinkmanReq;
import com.yyigou.dsrp.cdc.model.v2.company.req.CompanyShippingAddressReq;
import com.yyigou.dsrp.cdc.model.v2.supplier.req.*;
import com.yyigou.dsrp.cdc.service.utils.PageUtils;
import com.yyigou.dsrp.cdc.service.v2.company.CompanyV2Service;
import com.yyigou.dsrp.cdc.service.v2.supplier.SupplierApplyService;
import com.yyigou.dsrp.cdc.service.v2.supplier.SupplierCategoryV2Service;
import com.yyigou.dsrp.cdc.service.v2.supplier.SupplierV2Service;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionCallbackWithoutResult;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.yyigou.dsrp.cdc.manager.integration.uap.GradeControlService.AUTO_REVIEW;

@Service("supplierApplyService")
@RequiredArgsConstructor
@Slf4j
public class SupplierApplyServiceImpl extends ServiceImpl<SupplierApplyDAO, SupplierApply> implements SupplierApplyService {
    @Resource
    private SupplierV2Service supplierV2Service;

    @Resource
    private SupplierApplyDAO supplierApplyDAO;

    @Resource
    private SupplierApplyItemDAO supplierApplyItemDAO;

    @Resource
    private SupplierCategoryV2Service supplierCategoryV2Service;

    @Resource
    private CompanyV2Service companyV2Service;

    @Resource
    private CustomDocService customDocService;

    @Resource
    private DictEnterpriseService dictEnterpriseService;

    @Resource
    private TransactionTypeService transactionTypeService;

    @Resource
    private OrganizationService organizationService;

    @Resource
    private EmployeeService employeeService;

    @Resource
    private BusinessLogService businessLogService;

    @Resource
    private GradeControlService gradeControlService;

    @Resource
    private TransactionTemplate transactionTemplate;

    @Resource
    private CertService certService;

    @Override
    public PageVo<SupplierApplyPageVO> applyFindListPage(OperationModel operationModel, SupplierApplyQueryListPageReq queryReq, PageDto pageDTO) {
        ValidatorUtils.checkEmptyThrowEx(queryReq, "查询条件不能为空");

        Page<SupplierApply> page = PageHelper.startPage(pageDTO.getPageIndex(), pageDTO.getPageSize(), StringUtils.isBlank(pageDTO.getOrderBy()) ? "create_time desc" : pageDTO.getOrderBy());
        supplierApplyDAO.selectList(Wrappers.<SupplierApply>lambdaQuery()
                .eq(SupplierApply::getEnterpriseNo, operationModel.getEnterpriseNo())
                .in(CollectionUtils.isNotEmpty(queryReq.getIdList()), SupplierApply::getApplyInstanceNo, queryReq.getIdList())
                .eq(SupplierApply::getDeleted, DeletedEnum.UN_DELETE.getValue()));

        return PageUtils.convertPageVo(page, data -> completeSupplementPageVO(operationModel.getEnterpriseNo(), data));
    }

    @Override
    public PageVo<SupplierApplyPageVO> approveFindListPage(OperationModel operationModel, SupplierApplyApproveQueryListPageReq queryReq, PageDto pageDTO) {
        ValidatorUtils.checkEmptyThrowEx(queryReq, "查询条件不能为空");

        Page<SupplierApply> page = PageHelper.startPage(pageDTO.getPageIndex(), pageDTO.getPageSize(), StringUtils.isBlank(pageDTO.getOrderBy()) ? "create_time desc" : pageDTO.getOrderBy());
        supplierApplyDAO.selectList(Wrappers.<SupplierApply>lambdaQuery()
                .eq(SupplierApply::getEnterpriseNo, operationModel.getEnterpriseNo())
                .in(CollectionUtils.isNotEmpty(queryReq.getIdList()), SupplierApply::getApplyInstanceNo, queryReq.getIdList())
                .eq(SupplierApply::getDeleted, DeletedEnum.UN_DELETE.getValue()));

        return PageUtils.convertPageVo(page, data -> completeSupplementPageVO(operationModel.getEnterpriseNo(), data));
    }

    @Override
    public Long getPendingCount(OperationModel operationModel) {
        return supplierApplyDAO.selectCount(Wrappers.<SupplierApply>lambdaQuery()
                .eq(SupplierApply::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(SupplierApply::getAuditStatus, SupplierApplyAuditStatusEnum.TO_BE_AUDITED.getValue())
                .eq(SupplierApply::getDeleted, DeletedEnum.UN_DELETE.getValue()));
    }

    private void saveOrUpdateValidate(OperationModel operationModel, SupplierApplySaveOrUpdateReq req) {
        // 必填校验
        ValidatorUtils.checkEmptyThrowEx(req, "申请信息不能为空");
        ValidatorUtils.checkEmptyThrowEx(req.getApplyInstanceNo(), "申请编号为空");
        ValidatorUtils.checkEmptyThrowEx(req.getAuditStatus(), "审核状态为空");

        // 范围校验
        ValidatorUtils.checkTrueThrowEx(req.getApplyInstanceNo().length() > 32, "申请编号长度不能超过32");
        if (StringUtils.isNotEmpty(req.getApplyDesc())) {
            ValidatorUtils.checkTrueThrowEx(req.getApplyDesc().length() > 500, "申请描述长度不能超过500");
        }

        // 合法性校验
        ValidatorUtils.checkTrueThrowEx(null == SupplierApplyTypeEnum.getNameByValue(req.getApplyType()), "申请类型不正确");
    }

    @Override
    @Transactional
    public String applySaveOrUpdate(OperationModel operationModel, SupplierApplySaveOrUpdateReq req) {
        // 参数校验
        saveOrUpdateValidate(operationModel, req);

        // 业务校验
        List<SupplierApply> supplierAppliesInDB = supplierApplyDAO.selectList(Wrappers.<SupplierApply>lambdaQuery()
                .eq(SupplierApply::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(SupplierApply::getSupplierName, req.getSupplierName())
                .ne(SupplierApply::getApplyInstanceNo, req.getApplyInstanceNo())
                .eq(SupplierApply::getAuditStatus, SupplierApplyAuditStatusEnum.TO_BE_AUDITED.getValue())
                .eq(SupplierApply::getDeleted, DeletedEnum.UN_DELETE.getValue()));
        if (SupplierApplyTypeEnum.ADD_APPLY.getValue().equals(req.getApplyType())) { // 新增申请
            /*
            申请组织视角下存在同名供应商的“待审核”申请单：供应商【供应商名称】已存在待审核的申请单【申请单号】，不能重复创建。
            供应商档案中已存在同名供应商，未分派，但是管理组织不同：名称【供应商名称】的供应商档案已存在，管理组织是【组织名称】，与申请单中的管理组织不一致，请更换管理组织！
            供应商档案中已存在同名供应商，已分派：供应商档案中已存在名称【供应商名称】的供应商，可以直接使用。
             */
            if (CollectionUtils.isNotEmpty(supplierAppliesInDB)) {
                String errorMessage = String.format("供应商【%s】已存在待审核的申请单【%s】，不能重复创建", req.getSupplierName(), supplierAppliesInDB.stream().map(SupplierApply::getApplyInstanceNo).collect(Collectors.joining(",")));
                throw new BusinessException(ErrorCode.param_invalid_msg, errorMessage);
            }

            SupplierV2 supplierV2 = null;
            if (StringUtils.isNotEmpty(req.getSupplierCode())) {
                supplierV2 = supplierV2Service.getSupplierByCode(operationModel, req.getSupplierName());
            }
            if (null == supplierV2) {
                supplierV2 = supplierV2Service.getSupplierByName(operationModel, req.getSupplierName());
            }
            if (null != supplierV2) {
                if (!supplierV2.getManageOrgNo().equals(req.getManageOrgNo())) {
                    List<OrganizationVo> organizationVoList = organizationService.findListNoAuth(operationModel.getEnterpriseNo(), Collections.singletonList(supplierV2.getManageOrgNo()));
                    final Map<String, OrganizationVo> orgNo2OrganizationVo = organizationVoList.stream().collect(Collectors.toMap(OrganizationVo::getOrgNo, Function.identity()));

                    String errorMessage = String.format("名称【%s】的供应商档案已存在，管理组织是【%s】，与申请单中的管理组织不一致，请更换管理组织！", req.getSupplierName(), orgNo2OrganizationVo.containsKey(supplierV2.getManageOrgNo()) ? orgNo2OrganizationVo.get(supplierV2.getManageOrgNo()).getOrgName() : "");
                    throw new BusinessException(ErrorCode.param_invalid_msg, errorMessage);
                }


                SupplierBase supplierBaseQuery = new SupplierBase();
                supplierBaseQuery.setSupplierCode(supplierV2.getSupplierCode());
                supplierBaseQuery.setManageOrgNo(supplierV2.getManageOrgNo());
                supplierBaseQuery.setUseOrgNo(req.getUseOrgNo());
                boolean hasAssigned = supplierV2Service.hasAssignSupplier(operationModel, supplierBaseQuery);
                ValidatorUtils.checkTrueThrowEx(hasAssigned, String.format("供应商档案中已存在名称【%s】的供应商，可以直接使用", req.getSupplierName()));
            }
        } else if (SupplierApplyTypeEnum.MODIFY_APPLY.getValue().equals(req.getApplyType())) { // 变更申请
            /*
            申请组织视角下存在同名供应商的“待审核”申请单：供应商【供应商名称】已存在待审核的申请单【申请单号】，不能重复创建。
             */
            if (CollectionUtils.isNotEmpty(supplierAppliesInDB)) {
                String errorMessage = String.format("供应商【%s】已存在待审核的申请单【%s】，不能重复创建", req.getSupplierName(), supplierAppliesInDB.stream().map(SupplierApply::getApplyInstanceNo).collect(Collectors.joining(",")));
                throw new BusinessException(ErrorCode.param_invalid_msg, errorMessage);
            }
        }


        SupplierApply supplierApply = supplierApplyDAO.selectOne(Wrappers.<SupplierApply>lambdaQuery()
                .eq(SupplierApply::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(SupplierApply::getApplyInstanceNo, req.getApplyInstanceNo())
                .eq(SupplierApply::getDeleted, DeletedEnum.UN_DELETE.getValue()));
        if (null == supplierApply) { // 新增
            // 当前状态：初始态
            // 可执行动作
            ValidatorUtils.checkTrueThrowEx(!(SupplierApplyAuditStatusEnum.DRAFT.getValue().equals(req.getAuditStatus()) || SupplierApplyAuditStatusEnum.TO_BE_AUDITED.getValue().equals(req.getAuditStatus())), "新增申请单需要保存为草稿态或待审核态");

            // 校验json内容
            supplierV2Service.validateSaveBasicAndInfoReqByApply(operationModel, req.getApplyContent());

            SupplierApply newSupplierApply = BeanUtil.copyFields(req, SupplierApply.class);
            CommonUtil.fillCreatInfo(operationModel, newSupplierApply);
            supplierApplyDAO.insert(newSupplierApply);

            SupplierApplyItem supplierApplyItem = new SupplierApplyItem();
            supplierApplyItem.setEnterpriseNo(operationModel.getEnterpriseNo());
            supplierApplyItem.setApplyInstanceId(newSupplierApply.getId());
            supplierApplyItem.setApplyInstanceNo(newSupplierApply.getApplyInstanceNo());
            supplierApplyItem.setApplyContent(req.getApplyContent());
            supplierApplyItemDAO.insert(supplierApplyItem);

            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                @Override
                public void afterCommit() {
                    try {
                        businessLogService.saveLog(DLogLevel.INFO.name(), ViewNameConstant.BDC_SUPPLIER_APPLY_VIEW, req.getApplyInstanceNo(), "新增供应商申请", "新增供应商申请", "", "");
                    } catch (Exception e) {
                        log.error("新增供应商申请日志保存失败", e);
                    }
                }
            });
        } else {
            // 当前状态
            ValidatorUtils.checkTrueThrowEx(!(SupplierApplyAuditStatusEnum.DRAFT.getValue().equals(supplierApply.getAuditStatus()) || SupplierApplyAuditStatusEnum.REJECTED.getValue().equals(supplierApply.getAuditStatus())), "非草稿态和审批失败态的申请单不允许修改");
            // 可执行动作
//            if (SupplierApplyAuditStatusEnum.REJECTED.getValue().equals(supplierApply.getAuditStatus())) {
//                ValidatorUtils.checkTrueThrowEx(!(SupplierApplyAuditStatusEnum.DRAFT.getValue().equals(req.getAuditStatus())), "审批失败的申请单需要保存为草稿态");
//            } else {
            ValidatorUtils.checkTrueThrowEx(!(req.getAuditStatus().equals(SupplierApplyAuditStatusEnum.DRAFT.getValue()) || req.getAuditStatus().equals(SupplierApplyAuditStatusEnum.TO_BE_AUDITED.getValue())), "申请单状态不正确");
//            }

            //校验json内容
            supplierV2Service.validateEditBasicReqByApply(operationModel, req.getApplyContent());

            LambdaUpdateWrapper<SupplierApply> supplierApplyLambdaUpdateWrapper = Wrappers.lambdaUpdate(SupplierApply.class);
            // 表头
            supplierApplyLambdaUpdateWrapper.set(SupplierApply::getApplyNo, req.getApplyNo());
            supplierApplyLambdaUpdateWrapper.set(SupplierApply::getApplyTime, req.getApplyTime());
            supplierApplyLambdaUpdateWrapper.set(SupplierApply::getApplyReason, req.getApplyReason());
            supplierApplyLambdaUpdateWrapper.set(SupplierApply::getApplyDesc, req.getApplyDesc());
            supplierApplyLambdaUpdateWrapper.set(SupplierApply::getAuditStatus, req.getAuditStatus());
            supplierApplyLambdaUpdateWrapper.set(SupplierApply::getApplyNo, req.getApplyNo());

            // 所属关系
            supplierApplyLambdaUpdateWrapper.set(SupplierApply::getUseOrgNo, req.getUseOrgNo());
            supplierApplyLambdaUpdateWrapper.set(SupplierApply::getManageOrgNo, req.getManageOrgNo());

            // 供应商基本信息
            supplierApplyLambdaUpdateWrapper.set(SupplierApply::getSupplierCode, req.getSupplierCode());
            supplierApplyLambdaUpdateWrapper.set(SupplierApply::getSupplierName, req.getSupplierName());

            // 企业信息
            supplierApplyLambdaUpdateWrapper.set(SupplierApply::getCompanyNo, req.getCompanyNo());
            supplierApplyLambdaUpdateWrapper.set(SupplierApply::getCompanyName, req.getCompanyName());

            // 变更人信息
            supplierApplyLambdaUpdateWrapper.set(SupplierApply::getModifyName, operationModel.getUserName());
            supplierApplyLambdaUpdateWrapper.set(SupplierApply::getModifyNo, operationModel.getEmployerNo());
            supplierApplyLambdaUpdateWrapper.set(SupplierApply::getModifyTime, DateUtil.getCurrentDate());

            // where条件
            supplierApplyLambdaUpdateWrapper.eq(SupplierApply::getEnterpriseNo, operationModel.getEnterpriseNo());
            supplierApplyLambdaUpdateWrapper.eq(SupplierApply::getId, supplierApply.getId());

            supplierApplyDAO.update(null, supplierApplyLambdaUpdateWrapper);


            LambdaUpdateWrapper<SupplierApplyItem> supplierApplyItemLambdaUpdateWrapper = Wrappers.lambdaUpdate(SupplierApplyItem.class);
            // 申请单明细
            supplierApplyItemLambdaUpdateWrapper.set(SupplierApplyItem::getApplyContent, req.getApplyContent());

            // where条件
            supplierApplyItemLambdaUpdateWrapper.eq(SupplierApplyItem::getEnterpriseNo, operationModel.getEnterpriseNo());
            supplierApplyItemLambdaUpdateWrapper.eq(SupplierApplyItem::getApplyInstanceId, supplierApply.getId());

            supplierApplyItemDAO.update(null, supplierApplyItemLambdaUpdateWrapper);


            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                @Override
                public void afterCommit() {
                    try {
                        businessLogService.saveLog(DLogLevel.INFO.name(), ViewNameConstant.BDC_SUPPLIER_APPLY_VIEW, req.getApplyInstanceNo(), "编辑供应商申请", "编辑供应商申请", "", "");
                    } catch (Exception e) {
                        log.error("新增供应商申请日志保存失败", e);
                    }
                }
            });
        }

        if (SupplierApplyAuditStatusEnum.TO_BE_AUDITED.getValue().equals(req.getAuditStatus())) {
            //获取自动审批
            boolean autoAudit = false;
            List<GradeAutoReviewConfigVO> gradeAutoReviewConfigVOS = gradeControlService.listApplyAutoReviewConfig(operationModel.getEnterpriseNo(), ViewNameConstant.BDC_SUPPLIER_VIEW, req.getManageOrgNo(), Collections.singletonList(req.getUseOrgNo()), SupplierApplyTypeEnum.ADD_APPLY.getValue().equals(req.getApplyType()));
            if (CollectionUtils.isNotEmpty(gradeAutoReviewConfigVOS)) {
                for (GradeAutoReviewConfigVO gradeAutoReviewConfigVO : gradeAutoReviewConfigVOS) {
                    if (req.getUseOrgNo().equals(gradeAutoReviewConfigVO.getUseOrgNo())) {
                        if (SupplierApplyTypeEnum.ADD_APPLY.getValue().equals(req.getApplyType())) {
                            autoAudit = AUTO_REVIEW.equals(gradeAutoReviewConfigVO.getAddAutoReview());
                        } else {
                            autoAudit = AUTO_REVIEW.equals(gradeAutoReviewConfigVO.getUpdateAutoReview());
                        }
                    }
                }
            }
            if (autoAudit) {
                SupplierApplyApproveReq supplierApplyApproveReq = new SupplierApplyApproveReq();
                supplierApplyApproveReq.setEnterpriseNo(operationModel.getEnterpriseNo());
                supplierApplyApproveReq.setApplyInstanceNo(req.getApplyInstanceNo());
                supplierApplyApproveReq.setAuditStatus(SupplierApplyAuditStatusEnum.APPROVED.getValue());
                supplierApplyApproveReq.setAuditRemark("自动审核通过");
                supplierApplyApproveReq.setAutoAudit(true);
                manageApprove(operationModel, supplierApplyApproveReq);
            }
        }



        return req.getApplyNo();
    }

    @Override
    public SupplierApplyDetailVO getDetail(OperationModel operationModel, SupplierApplyGetReq req) {
        SupplierApply supplierApply = supplierApplyDAO.selectOne(Wrappers.<SupplierApply>lambdaQuery()
                .eq(SupplierApply::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(SupplierApply::getApplyInstanceNo, req.getApplyInstanceNo())
                .eq(SupplierApply::getDeleted, DeletedEnum.UN_DELETE.getValue()));
        ValidatorUtils.checkEmptyThrowEx(supplierApply, "申请单不存在");

        SupplierApplyItem supplierApplyItem = supplierApplyItemDAO.selectOne(Wrappers.<SupplierApplyItem>lambdaQuery()
                .eq(SupplierApplyItem::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(SupplierApplyItem::getApplyInstanceId, supplierApply.getId()));
        ValidatorUtils.checkEmptyThrowEx(supplierApplyItem, "申请单不存在");

        return fillDetailsToApplyDetail(operationModel, supplierApply, supplierApplyItem);
    }

    @Override
    @Transactional
    public Boolean deleteApply(OperationModel operationModel, SupplierApplyDeleteReq req) {
        SupplierApply supplierApply = supplierApplyDAO.selectOne(Wrappers.<SupplierApply>lambdaQuery()
                .eq(SupplierApply::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(SupplierApply::getApplyInstanceNo, req.getApplyInstanceNo())
                .eq(SupplierApply::getDeleted, DeletedEnum.UN_DELETE.getValue()));
        ValidatorUtils.checkEmptyThrowEx(supplierApply, "申请单不存在");

        // 当前状态
        ValidatorUtils.checkTrueThrowEx(!(SupplierApplyAuditStatusEnum.DRAFT.getValue().equals(supplierApply.getAuditStatus()) || SupplierApplyAuditStatusEnum.REJECTED.getValue().equals(supplierApply.getAuditStatus())), "非草稿态和审批失败态的申请单不允许删除");
        // 可执行动作：删除

        SupplierApply newSupplierApply = new SupplierApply();
        newSupplierApply.setDeleted(DeletedEnum.DELETED.getValue());
        CommonUtil.fillModifyInfo(operationModel, newSupplierApply);

        int update = supplierApplyDAO.update(newSupplierApply, Wrappers.<SupplierApply>lambdaUpdate()
                .eq(SupplierApply::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(SupplierApply::getId, supplierApply.getId()));


        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                try {
                    businessLogService.saveLog(DLogLevel.INFO.name(), ViewNameConstant.BDC_SUPPLIER_APPLY_VIEW, req.getApplyInstanceNo(), "删除供应商申请", "删除供应商申请", "", "");
                } catch (Exception e) {
                    log.error("删除供应商申请日志保存失败", e);
                }
            }
        });

        return update > 0;
    }

    @Override
    @Transactional
    public Boolean withDrawApply(OperationModel operationModel, SupplierApplyWithdrawReq req) {
        SupplierApply supplierApply = supplierApplyDAO.selectOne(Wrappers.<SupplierApply>lambdaQuery()
                .eq(SupplierApply::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(SupplierApply::getApplyInstanceNo, req.getApplyInstanceNo())
                .eq(SupplierApply::getDeleted, DeletedEnum.UN_DELETE.getValue()));
        ValidatorUtils.checkEmptyThrowEx(supplierApply, "申请单不存在");

        // 当前状态
        ValidatorUtils.checkTrueThrowEx(!SupplierApplyAuditStatusEnum.TO_BE_AUDITED.getValue().equals(supplierApply.getAuditStatus()), "非待审核的申请单不允许撤回");
        // 可执行动作：撤回

        SupplierApply newSupplierApply = new SupplierApply();
        newSupplierApply.setAuditStatus(SupplierApplyAuditStatusEnum.DRAFT.getValue());
        CommonUtil.fillModifyInfo(operationModel, newSupplierApply);

        int update = supplierApplyDAO.update(newSupplierApply, Wrappers.<SupplierApply>lambdaUpdate()
                .eq(SupplierApply::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(SupplierApply::getId, supplierApply.getId()));


        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                try {
                    businessLogService.saveLog(DLogLevel.INFO.name(), ViewNameConstant.BDC_SUPPLIER_APPLY_VIEW, req.getApplyInstanceNo(), "撤回供应商申请", "撤回供应商申请", "", "");
                } catch (Exception e) {
                    log.error("撤回供应商申请日志保存失败", e);
                }
            }
        });

        return update > 0;
    }

    private Map<String, Object> manageApproveValidate(OperationModel operationModel, SupplierApplyApproveReq req) {
        // 必填校验
        ValidatorUtils.checkEmptyThrowEx(req, "申请信息不能为空");
        ValidatorUtils.checkEmptyThrowEx(req.getApplyInstanceNo(), "申请编号为空");
        ValidatorUtils.checkEmptyThrowEx(req.getAuditStatus(), "审核状态为空");
        if (SupplierApplyAuditStatusEnum.REJECTED.getValue().equals(req.getAuditStatus())) {
            ValidatorUtils.checkEmptyThrowEx(req.getAuditRemark(), "审核拒绝时，意见不能为空");
        }

        // 范围校验
        if (StringUtils.isNotEmpty(req.getAuditRemark())) {
            ValidatorUtils.checkTrueThrowEx(req.getAuditRemark().length() > 500, "审核原因长度不能超过500");
        }

        // 合法校验
        // 业务校验
        SupplierApply supplierApply = supplierApplyDAO.selectOne(Wrappers.<SupplierApply>lambdaQuery()
                .eq(SupplierApply::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(SupplierApply::getApplyInstanceNo, req.getApplyInstanceNo())
                .eq(SupplierApply::getDeleted, DeletedEnum.UN_DELETE.getValue()));
        ValidatorUtils.checkEmptyThrowEx(supplierApply, "申请单不存在");

        SupplierApplyItem supplierApplyItem = supplierApplyItemDAO.selectOne(Wrappers.<SupplierApplyItem>lambdaQuery()
                .eq(SupplierApplyItem::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(SupplierApplyItem::getApplyInstanceId, supplierApply.getId()));
        ValidatorUtils.checkEmptyThrowEx(supplierApplyItem, "申请单不存在");



        // 当前状态
        ValidatorUtils.checkTrueThrowEx(!SupplierApplyAuditStatusEnum.TO_BE_AUDITED.getValue().equals(supplierApply.getAuditStatus()), "非待审核的申请单不允许审核");
        // 可执行动作
        ValidatorUtils.checkTrueThrowEx(!(req.getAuditStatus().equals(SupplierApplyAuditStatusEnum.APPROVED.getValue()) || req.getAuditStatus().equals(SupplierApplyAuditStatusEnum.REJECTED.getValue())), "申请单状态不正确");

        Map<String, Object> result = new HashMap<>();
        result.put("supplierApply", supplierApply);
        result.put("supplierApplyItem", supplierApplyItem);

        return result;
    }

    @Override
    @Transactional
    public Boolean manageApprove(OperationModel operationModel, SupplierApplyApproveReq req) {
        // 参数校验
        Map<String, Object> validateResult = manageApproveValidate(operationModel, req);

        SupplierApply supplierApply = (SupplierApply) validateResult.get("supplierApply");
        SupplierApplyItem supplierApplyItem = (SupplierApplyItem) validateResult.get("supplierApplyItem");

        if (SupplierApplyAuditStatusEnum.REJECTED.getValue().equals(req.getAuditStatus())) {
            // 更新成“审核失败”
            SupplierApply newSupplierApply = new SupplierApply();
            newSupplierApply.setAuditStatus(SupplierApplyAuditStatusEnum.REJECTED.getValue());
            newSupplierApply.setAuditRemark(req.getAuditRemark());
            newSupplierApply.setAuditNo(operationModel.getEmployerNo());
            newSupplierApply.setAuditName(operationModel.getUserName());
            newSupplierApply.setAuditTime(DateUtil.getCurrentDate());
            CommonUtil.fillModifyInfo(operationModel, newSupplierApply);
            supplierApplyDAO.update(newSupplierApply, Wrappers.<SupplierApply>lambdaUpdate()
                    .eq(SupplierApply::getEnterpriseNo, operationModel.getEnterpriseNo())
                    .eq(SupplierApply::getId, supplierApply.getId()));
        } else {
            SupplierApplyFormReq supplierApplyFormReq = JSON.parseObject(supplierApplyItem.getApplyContent(), SupplierApplyFormReq.class);

            // 预校验供应商档案
            validateApprove(operationModel, supplierApply, supplierApplyItem, supplierApplyFormReq, req);

            // 更新成“审核通过”
            SupplierApply newSupplierApply = new SupplierApply();
            newSupplierApply.setAuditStatus(SupplierApplyAuditStatusEnum.APPROVED.getValue());
//            newSupplierApply.setApplyResult(ApplyResultEnum.YES.getValue());
            newSupplierApply.setAuditRemark(req.getAuditRemark());
            newSupplierApply.setAuditNo(operationModel.getEmployerNo());
            newSupplierApply.setAuditName(operationModel.getUserName());
            newSupplierApply.setAuditTime(DateUtil.getCurrentDate());
            CommonUtil.fillModifyInfo(operationModel, newSupplierApply);
            supplierApplyDAO.update(newSupplierApply, Wrappers.<SupplierApply>lambdaUpdate()
                    .eq(SupplierApply::getEnterpriseNo, operationModel.getEnterpriseNo())
                    .eq(SupplierApply::getId, supplierApply.getId()));

            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                @Override
                public void afterCommit() {
                    int errorType = 0;
                    Exception errorException = null;

                    Map<String, Object> approveResult = null;
                    try {
                        approveResult = transactionTemplate.execute(status ->
                                supplierV2Service.manageApprove(operationModel, supplierApply, supplierApplyItem, supplierApplyFormReq, req)
                        );
                    } catch (Exception e) {
                        errorType = 1;
                        log.error("供应商审核通过后处理供应商档案异常", e);

                        errorException = e;
                    }

                    SupplierVO supplierVO = null;

                    if (0 == errorType) {
                        SupplierGetReq supplierGetReq = new SupplierGetReq();
                        supplierGetReq.setEnterpriseNo(operationModel.getEnterpriseNo());
                        supplierGetReq.setSupplierCode((String) approveResult.get("supplierCode"));
                        supplierGetReq.setUseOrgNo(supplierApply.getUseOrgNo());
                        supplierVO = supplierV2Service.getSupplier(operationModel, supplierGetReq);

                        try {
                            CompanyCertUpsertRequest companyCertUpsertRequest = new CompanyCertUpsertRequest();
                            companyCertUpsertRequest.setEnterpriseNo(operationModel.getEnterpriseNo());
                            companyCertUpsertRequest.setOrgNo(supplierApply.getUseOrgNo());
                            companyCertUpsertRequest.setCompanyNo(supplierVO.getCompanyNo());
                            companyCertUpsertRequest.setSourceDocType(CertSourceTypeEnum.SUPPLIER.getValue());
                            companyCertUpsertRequest.setSourceDocCode(supplierVO.getSupplierCode());
                            List<CompanyCertBasicRequest> companyCertList = new ArrayList<>();
                            companyCertList.addAll(BeanUtil.copyFieldsListForJSON(supplierApplyFormReq.getCompanyCertList(), CompanyCertBasicRequest.class));
                            companyCertList.addAll(BeanUtil.copyFieldsListForJSON(supplierApplyFormReq.getSupplierCertList(), CompanyCertBasicRequest.class));
                            companyCertUpsertRequest.setCompanyCertList(companyCertList);
                            Boolean upsert = certService.upsert(companyCertUpsertRequest);
                            if (!upsert) {
                                throw new BusinessException(ErrorCode.param_invalid_msg, "证照保存失败");
                            }

                            //TODO shenbin 发送事件（企业证照修改）

                        } catch (Exception e) {
                            errorType = 2;
                            log.error("供应商审核通过后处理证照异常", e);

                            errorException = e;
                        }
                    }


                    final SupplierVO finalSupplierVO = supplierVO;
                    final Map<String, Object> finalApproveResult = approveResult;
                    final int finalErrorType = errorType;
                    final Exception finalErrorException = errorException;

                    transactionTemplate.execute(new TransactionCallbackWithoutResult() {
                        protected void doInTransactionWithoutResult(TransactionStatus status) {
                            CommonUtil.fillModifyInfo(operationModel, newSupplierApply);
                            newSupplierApply.setApplyResult(ApplyResultEnum.SUCC.getValue());

                            if (1 != finalErrorType) {
                                SupplierApplyItem newSupplierApplyItem = new SupplierApplyItem();

                                // 回写新生成的编码
                                if (StringUtils.isEmpty(supplierApplyFormReq.getSupplierCode())) {
                                    newSupplierApply.setSupplierCode((String) finalApproveResult.get("supplierCode"));

                                    supplierApplyFormReq.setSupplierCode((String) finalApproveResult.get("supplierCode"));
                                    newSupplierApplyItem.setApplyContent(JSON.toJSONString(supplierApplyFormReq));
                                }

                                // 审核通过时的镜像
                                if (finalApproveResult.containsKey("preApproveSupplier") && null != finalApproveResult.get("preApproveSupplier")) {
                                    newSupplierApplyItem.setPreApproveContent(JSON.toJSONString(packPreSupplier2ApproveForm(operationModel, supplierApply, (SupplierVO) finalApproveResult.get("preApproveSupplier"))));

                                }
                                newSupplierApplyItem.setApproveContent(JSON.toJSONString(packSupplier2ApproveForm(operationModel, supplierApply, finalSupplierVO)));
                                supplierApplyItemDAO.update(newSupplierApplyItem, Wrappers.<SupplierApplyItem>lambdaUpdate()
                                        .eq(SupplierApplyItem::getEnterpriseNo, operationModel.getEnterpriseNo())
                                        .eq(SupplierApplyItem::getApplyInstanceId, supplierApply.getId()));
                            }


                            // 更新审批后执行结果
                            if (0 != finalErrorType) {
                                newSupplierApply.setApplyResult(ApplyResultEnum.FAIL.getValue());

                                String failReason = null;
                                if (1 == finalErrorType) {
//                                    failReason = "供应商审核通过后处理供应商档案异常：" + (finalErrorException instanceof BusinessException ? finalErrorException.getMessage() : "系统异常");
                                    failReason="供应商档案创建失败";
                                } else {
//                                    failReason = "供应商审核通过后处理证照异常：" + (finalErrorException instanceof BusinessException ? finalErrorException.getMessage() : "系统异常");
                                    failReason="供应商资质创建失败";
                                }
                                newSupplierApply.setFailReason(failReason);
                            }
                            supplierApplyDAO.update(newSupplierApply, Wrappers.<SupplierApply>lambdaUpdate()
                                    .eq(SupplierApply::getEnterpriseNo, operationModel.getEnterpriseNo())
                                    .eq(SupplierApply::getId, supplierApply.getId()));
                        }
                    });
                }
            });
        }

        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                String op = req.isAutoAudit() ? "自动" : "手动";
                try {
                    businessLogService.saveLog(DLogLevel.INFO.name(), ViewNameConstant.BDC_SUPPLIER_APPLY_VIEW, req.getApplyInstanceNo(), op + "审核供应商申请" + SupplierApplyAuditStatusEnum.getNameByValue(req.getAuditStatus()), op + "审核供应商申请" + SupplierApplyAuditStatusEnum.getNameByValue(req.getAuditStatus()), "", req.getAuditRemark());
                } catch (Exception e) {
                    log.error(op + "审核供应商申请日志保存失败", e);
                }
            }
        });

        return true;
    }

    private void validateApprove(OperationModel operationModel, SupplierApply supplierApply, SupplierApplyItem supplierApplyItem, SupplierApplyFormReq supplierApplyFormReq, SupplierApplyApproveReq req) {
        // 校验管理权
        if (!req.isAutoAudit()) {
            List<String> manageNoList = gradeControlService.listMgrOrgNos(operationModel.getEnterpriseNo(), BillNameConstant.BDC_SUPPLIER_BILL);
            if (CollectionUtils.isEmpty(manageNoList) || !manageNoList.contains(supplierApply.getManageOrgNo())) {
                List<OrganizationVo> manageOrgList = organizationService.findListNoAuth(operationModel.getEnterpriseNo(), Collections.singletonList(supplierApply.getManageOrgNo()));
                if (CollectionUtils.isNotEmpty(manageOrgList)) {
                    for (OrganizationVo manageOrg : manageOrgList) {
                        if (supplierApply.getManageOrgNo().equals(manageOrg.getOrgNo())) {
                            throw new BusinessException(ErrorCode.param_invalid_msg, String.format("管理组织【%s】没有档案管理权，无法审核通过", manageOrg.getOrgName()));
                        }
                    }
                }
                throw new BusinessException(ErrorCode.param_invalid_msg, "管理组织没有档案管理权，无法审核通过");
            }
        }

        /*
            供应商档案中已存在同名供应商，未分派，但是管理组织不同：名称【供应商名称】的供应商档案已存在，管理组织是【组织名称】，与申请单中的管理组织不一致，无法审核通过。
            供应商档案中已存在同名供应商，已分派：【使用组织名称】的供应商档案中已存在名称【供应商名称】的供应商，无法审核通过。
             */
        if (SupplierApplyTypeEnum.ADD_APPLY.getValue().equals(supplierApply.getApplyType())) {
            SupplierV2 supplierV2 = null;
            if (StringUtils.isNotEmpty(supplierApply.getSupplierCode())) {
                supplierV2 = supplierV2Service.getSupplierByCode(operationModel, supplierApply.getSupplierCode());
            }
            if (null == supplierV2) {
                supplierV2 = supplierV2Service.getSupplierByName(operationModel, supplierApply.getSupplierName());
            }
            if (null != supplierV2) {
                if (!supplierV2.getManageOrgNo().equals(supplierApply.getManageOrgNo())) {
                    List<OrganizationVo> organizationVoList = organizationService.findListNoAuth(operationModel.getEnterpriseNo(), Collections.singletonList(supplierV2.getManageOrgNo()));
                    final Map<String, OrganizationVo> orgNo2OrganizationVo = organizationVoList.stream().collect(Collectors.toMap(OrganizationVo::getOrgNo, Function.identity()));

                    String errorMessage = String.format("名称【%s】的供应商档案已存在，管理组织是【%s】，与申请单中的管理组织不一致，无法审核通过。", supplierApply.getSupplierName(), orgNo2OrganizationVo.containsKey(supplierV2.getManageOrgNo()) ? orgNo2OrganizationVo.get(supplierV2.getManageOrgNo()).getOrgName() : "");
                    throw new BusinessException(ErrorCode.param_invalid_msg, errorMessage);
                }

                SupplierBase supplierBaseQuery = new SupplierBase();
                supplierBaseQuery.setSupplierCode(supplierV2.getSupplierCode());
                supplierBaseQuery.setManageOrgNo(supplierV2.getManageOrgNo());
                supplierBaseQuery.setUseOrgNo(supplierApply.getUseOrgNo());
                boolean hasAssigned = supplierV2Service.hasAssignSupplier(operationModel, supplierBaseQuery);
                ValidatorUtils.checkTrueThrowEx(hasAssigned, String.format("【%s】的供应商档案中已存在名称【%s】的供应商，无法审核通过。", supplierApply.getUseOrgName(), supplierApply.getSupplierName()));

                CompanyV2 companyV2 = companyV2Service.findByEnterpriseNoAndCompanyNo(operationModel.getEnterpriseNo(), supplierV2.getCompanyNo());
                if (!(supplierApplyFormReq.getCompanyName().equals(companyV2.getCompanyName()) && supplierApplyFormReq.getUnifiedSocialCode().equals(companyV2.getUnifiedSocialCode()))) {
                    String errorMessage = String.format("名称【%s】的供应商档案的企业名称或统一社会信用代码与申请单中的不一致，无法审核通过。", supplierApply.getSupplierName());
                    throw new BusinessException(ErrorCode.param_invalid_msg, errorMessage);
                }
            }
        }

        try {
            Map<String, String> preValidateResult = supplierV2Service.preValidateManageApprove(operationModel, supplierApply, supplierApplyItem, supplierApplyFormReq, req);
            String companyNo = preValidateResult.get("companyNo");
            if (StringUtils.isEmpty(companyNo)) {
                throw new Exception("companyNo is empty");
            }

            String supplierCode = preValidateResult.get("supplierCode");
            if (StringUtils.isEmpty(supplierCode)) {
                throw new Exception("supplierCode is empty");
            }

            CompanyCertUpsertRequest companyCertUpsertRequest = new CompanyCertUpsertRequest();
            companyCertUpsertRequest.setEnterpriseNo(operationModel.getEnterpriseNo());
            companyCertUpsertRequest.setOrgNo(supplierApply.getUseOrgNo());
            companyCertUpsertRequest.setCompanyNo(companyNo);
            companyCertUpsertRequest.setSourceDocType(CertSourceTypeEnum.SUPPLIER.getValue());
            companyCertUpsertRequest.setSourceDocCode(supplierCode);
            List<CompanyCertBasicRequest> companyCertList = new ArrayList<>();
            companyCertList.addAll(BeanUtil.copyFieldsListForJSON(supplierApplyFormReq.getCompanyCertList(), CompanyCertBasicRequest.class));
            companyCertList.addAll(BeanUtil.copyFieldsListForJSON(supplierApplyFormReq.getSupplierCertList(), CompanyCertBasicRequest.class));
            companyCertUpsertRequest.setCompanyCertList(companyCertList);
            certService.preValidateUpsert(companyCertUpsertRequest);
        } catch (Exception e) {
            if (e instanceof BusinessException) {
                throw (BusinessException) e;
            }

            log.error(req.getApplyInstanceNo() + "审核预校验异常", e);

            throw new BusinessException(ErrorCode.param_invalid_msg, "审核预校验异常");
        }
    }

    private SupplierApplyFormReq packPreSupplier2ApproveForm(OperationModel operationModel, SupplierApply supplierApply, SupplierVO supplierVO) {
        SupplierApplyFormReq supplierApplyFormReq = new SupplierApplyFormReq();

        // --------新增和变更共有的信息--------
        // 供应商信息
        supplierApplyFormReq.setSupplierNo(supplierVO.getSupplierNo());
        supplierApplyFormReq.setSupplierCode(supplierVO.getSupplierCode());
        supplierApplyFormReq.setSupplierName(supplierVO.getSupplierName());
        supplierApplyFormReq.setSupplierNameEn(supplierVO.getSupplierNameEn());
        supplierApplyFormReq.setMnemonicCode(supplierVO.getMnemonicCode());
        supplierApplyFormReq.setTransactionType(supplierVO.getTransactionType());
        supplierApplyFormReq.setTransactionTypeName(supplierVO.getTransactionTypeName());
        supplierApplyFormReq.setSupplierCategoryNo(supplierVO.getSupplierCategoryNo());
        supplierApplyFormReq.setSupplierCategoryName(supplierVO.getSupplierCategoryName());
        supplierApplyFormReq.setRetailInvestors(supplierVO.getRetailInvestors());
        supplierApplyFormReq.setRetailInvestorsName(supplierVO.getRetailInvestorsName());
        supplierApplyFormReq.setIsAssociatedEnterprise(supplierVO.getIsAssociatedEnterprise());
        supplierApplyFormReq.setIsAssociatedEnterpriseName(supplierVO.getIsAssociatedEnterpriseName());
        supplierApplyFormReq.setAssociatedOrgNo(supplierVO.getAssociatedOrgNo());
        supplierApplyFormReq.setAssociatedOrgCode(supplierVO.getAssociatedOrgCode());
        supplierApplyFormReq.setAssociatedOrgName(supplierVO.getAssociatedOrgName());
        supplierApplyFormReq.setRemark(supplierVO.getRemark());

        // 企业信息
        supplierApplyFormReq.setCompanyNo(supplierVO.getCompanyNo());
        supplierApplyFormReq.setCompanyName(supplierVO.getCompanyName());
        supplierApplyFormReq.setUnifiedSocialCode(supplierVO.getUnifiedSocialCode());
        supplierApplyFormReq.setFactoryType(supplierVO.getFactoryType());
        supplierApplyFormReq.setFactoryTypeName(supplierVO.getFactoryTypeName());
        supplierApplyFormReq.setCountryRegionId(supplierVO.getCountryRegionId());
        supplierApplyFormReq.setCountryRegionName(supplierVO.getCountryRegionName());
        supplierApplyFormReq.setTaxCategory(supplierVO.getTaxCategory());
        supplierApplyFormReq.setTaxCategoryName(supplierVO.getTaxCategoryName());
        supplierApplyFormReq.setEconomicType(supplierVO.getEconomicType());
        supplierApplyFormReq.setEconomicTypeName(supplierVO.getEconomicTypeName());
//        supplierApplyFormReq.setIsMedicalInstitution(supplierVO.getIsMedicalInstitution());
//        supplierApplyFormReq.setIsMedicalInstitutionName(supplierVO.getIsMedicalInstitutionName());
//        supplierApplyFormReq.setInstitutionalType(supplierVO.getInstitutionalType());
//        supplierApplyFormReq.setInstitutionalTypeName(supplierVO.getInstitutionalTypeName());
//        supplierApplyFormReq.setHospitalType(supplierVO.getHospitalType());
//        supplierApplyFormReq.setHospitalTypeName(supplierVO.getHospitalTypeName());
//        supplierApplyFormReq.setHospitalClass(supplierVO.getHospitalClass());
//        supplierApplyFormReq.setHospitalClassName(supplierVO.getHospitalClassName());

        // 企业银行信息
        supplierApplyFormReq.setBankList(BeanUtil.copyFieldsList(supplierVO.getBankList(), CompanyBankReq.class));

        //企业资料信息
        supplierApplyFormReq.setCompanyCertList(BeanUtil.copyFieldsList(supplierVO.getCompanyCertList(), CompanyCertReq.class));

        return supplierApplyFormReq;
    }

    private SupplierApplyFormReq packSupplier2ApproveForm(OperationModel operationModel, SupplierApply supplierApply, SupplierVO supplierVO) {
        SupplierApplyFormReq supplierApplyFormReq = new SupplierApplyFormReq();

        // --------新增和变更共有的信息--------
        // 供应商信息
        supplierApplyFormReq.setSupplierNo(supplierVO.getSupplierNo());
        supplierApplyFormReq.setSupplierCode(supplierVO.getSupplierCode());
        supplierApplyFormReq.setSupplierName(supplierVO.getSupplierName());
        supplierApplyFormReq.setSupplierNameEn(supplierVO.getSupplierNameEn());
        supplierApplyFormReq.setMnemonicCode(supplierVO.getMnemonicCode());
        supplierApplyFormReq.setTransactionType(supplierVO.getTransactionType());
        supplierApplyFormReq.setTransactionTypeName(supplierVO.getTransactionTypeName());
        supplierApplyFormReq.setSupplierCategoryNo(supplierVO.getSupplierCategoryNo());
        supplierApplyFormReq.setSupplierCategoryName(supplierVO.getSupplierCategoryName());
        supplierApplyFormReq.setRetailInvestors(supplierVO.getRetailInvestors());
        supplierApplyFormReq.setRetailInvestorsName(supplierVO.getRetailInvestorsName());
        supplierApplyFormReq.setIsAssociatedEnterprise(supplierVO.getIsAssociatedEnterprise());
        supplierApplyFormReq.setIsAssociatedEnterpriseName(supplierVO.getIsAssociatedEnterpriseName());
        supplierApplyFormReq.setAssociatedOrgNo(supplierVO.getAssociatedOrgNo());
        supplierApplyFormReq.setAssociatedOrgCode(supplierVO.getAssociatedOrgCode());
        supplierApplyFormReq.setAssociatedOrgName(supplierVO.getAssociatedOrgName());
        supplierApplyFormReq.setRemark(supplierVO.getRemark());

        // 企业信息
        supplierApplyFormReq.setCompanyNo(supplierVO.getCompanyNo());
        supplierApplyFormReq.setCompanyName(supplierVO.getCompanyName());
        supplierApplyFormReq.setUnifiedSocialCode(supplierVO.getUnifiedSocialCode());
        supplierApplyFormReq.setFactoryType(supplierVO.getFactoryType());
        supplierApplyFormReq.setFactoryTypeName(supplierVO.getFactoryTypeName());
        supplierApplyFormReq.setCountryRegionId(supplierVO.getCountryRegionId());
        supplierApplyFormReq.setCountryRegionName(supplierVO.getCountryRegionName());
        supplierApplyFormReq.setTaxCategory(supplierVO.getTaxCategory());
        supplierApplyFormReq.setTaxCategoryName(supplierVO.getTaxCategoryName());
        supplierApplyFormReq.setEconomicType(supplierVO.getEconomicType());
        supplierApplyFormReq.setEconomicTypeName(supplierVO.getEconomicTypeName());
//        supplierApplyFormReq.setIsMedicalInstitution(supplierVO.getIsMedicalInstitution());
//        supplierApplyFormReq.setIsMedicalInstitutionName(supplierVO.getIsMedicalInstitutionName());
//        supplierApplyFormReq.setInstitutionalType(supplierVO.getInstitutionalType());
//        supplierApplyFormReq.setInstitutionalTypeName(supplierVO.getInstitutionalTypeName());
//        supplierApplyFormReq.setHospitalType(supplierVO.getHospitalType());
//        supplierApplyFormReq.setHospitalTypeName(supplierVO.getHospitalTypeName());
//        supplierApplyFormReq.setHospitalClass(supplierVO.getHospitalClass());
//        supplierApplyFormReq.setHospitalClassName(supplierVO.getHospitalClassName());

        // 企业银行信息
        supplierApplyFormReq.setBankList(BeanUtil.copyFieldsList(supplierVO.getBankList(), CompanyBankReq.class));

        //企业资料信息
        supplierApplyFormReq.setCompanyCertList(BeanUtil.copyFieldsList(supplierVO.getCompanyCertList(), CompanyCertReq.class));

        // --------新增申请独有的业务信息--------
        if (SupplierApplyTypeEnum.ADD_APPLY.getValue().equals(supplierApply.getApplyType())) {
            supplierApplyFormReq.setCooperationMode(supplierVO.getCooperationMode());
            supplierApplyFormReq.setCooperationModeName(supplierVO.getCooperationModeName());
            supplierApplyFormReq.setOwnerCompany(supplierVO.getOwnerCompany());
            supplierApplyFormReq.setCreditAmount(supplierVO.getCreditAmount());
            supplierApplyFormReq.setSettlementModes(supplierVO.getSettlementModes());
            supplierApplyFormReq.setSettlementModesName(supplierVO.getSettlementModesName());
            supplierApplyFormReq.setPaymentTerm(supplierVO.getPaymentTerm());
            supplierApplyFormReq.setPaymentTermName(supplierVO.getPaymentTermName());
            supplierApplyFormReq.setCoopStartTime(supplierVO.getCoopStartTime());
            supplierApplyFormReq.setCoopEndTime(supplierVO.getCoopEndTime());
            supplierApplyFormReq.setPaymentAgreementId(supplierVO.getPaymentAgreementId());
            supplierApplyFormReq.setPaymentAgreementYsId(supplierVO.getPaymentAgreementYsId());
            supplierApplyFormReq.setPaymentAgreementCode(supplierVO.getPaymentAgreementCode());
            supplierApplyFormReq.setPaymentAgreementName(supplierVO.getPaymentAgreementName());
            supplierApplyFormReq.setPeriodDays(supplierVO.getPeriodDays());
            supplierApplyFormReq.setCurrencyId(supplierVO.getCurrencyId());
            supplierApplyFormReq.setCurrencyName(supplierVO.getCurrencyName());

            //使用组织资料信息
            supplierApplyFormReq.setSupplierCertList(BeanUtil.copyFieldsList(supplierVO.getSupplierCertList(), CompanyCertReq.class));
            supplierApplyFormReq.setLinkmanList(BeanUtil.copyFieldsList(supplierVO.getLinkmanList(), CompanyLinkmanReq.class));
            supplierApplyFormReq.setLinkAddressList(BeanUtil.copyFieldsList(supplierVO.getLinkAddressList(), CompanyShippingAddressReq.class));
            supplierApplyFormReq.setSupplierManList(BeanUtil.copyFieldsList(supplierVO.getSupplierManList(), SupplierOrderManReq.class));
        }

        return supplierApplyFormReq;
    }

    private SupplierApplyFormReq packLatestDiff(SupplierVO supplierVO) {
        SupplierApplyFormReq supplierApplyFormReq = new SupplierApplyFormReq();

        // 供应商信息
        supplierApplyFormReq.setSupplierNo(supplierVO.getSupplierNo());
        supplierApplyFormReq.setSupplierCode(supplierVO.getSupplierCode());
        supplierApplyFormReq.setSupplierName(supplierVO.getSupplierName());
        supplierApplyFormReq.setSupplierNameEn(supplierVO.getSupplierNameEn());
        supplierApplyFormReq.setMnemonicCode(supplierVO.getMnemonicCode());
        supplierApplyFormReq.setTransactionType(supplierVO.getTransactionType());
        supplierApplyFormReq.setTransactionTypeName(supplierVO.getTransactionTypeName());
        supplierApplyFormReq.setSupplierCategoryNo(supplierVO.getSupplierCategoryNo());
        supplierApplyFormReq.setSupplierCategoryName(supplierVO.getSupplierCategoryName());
        supplierApplyFormReq.setRetailInvestors(supplierVO.getRetailInvestors());
        supplierApplyFormReq.setRetailInvestorsName(supplierVO.getRetailInvestorsName());
        supplierApplyFormReq.setIsAssociatedEnterprise(supplierVO.getIsAssociatedEnterprise());
        supplierApplyFormReq.setIsAssociatedEnterpriseName(supplierVO.getIsAssociatedEnterpriseName());
        supplierApplyFormReq.setAssociatedOrgNo(supplierVO.getAssociatedOrgNo());
        supplierApplyFormReq.setAssociatedOrgCode(supplierVO.getAssociatedOrgCode());
        supplierApplyFormReq.setAssociatedOrgName(supplierVO.getAssociatedOrgName());
        supplierApplyFormReq.setRemark(supplierVO.getRemark());

        // 企业信息
        supplierApplyFormReq.setCompanyNo(supplierVO.getCompanyNo());
        supplierApplyFormReq.setCompanyName(supplierVO.getCompanyName());
        supplierApplyFormReq.setUnifiedSocialCode(supplierVO.getUnifiedSocialCode());
        supplierApplyFormReq.setFactoryType(supplierVO.getFactoryType());
        supplierApplyFormReq.setFactoryTypeName(supplierVO.getFactoryTypeName());
        supplierApplyFormReq.setCountryRegionId(supplierVO.getCountryRegionId());
        supplierApplyFormReq.setCountryRegionName(supplierVO.getCountryRegionName());
        supplierApplyFormReq.setTaxCategory(supplierVO.getTaxCategory());
        supplierApplyFormReq.setTaxCategoryName(supplierVO.getTaxCategoryName());
        supplierApplyFormReq.setEconomicType(supplierVO.getEconomicType());
        supplierApplyFormReq.setEconomicTypeName(supplierVO.getEconomicTypeName());
//        supplierApplyFormReq.setIsMedicalInstitution(supplierVO.getIsMedicalInstitution());
//        supplierApplyFormReq.setIsMedicalInstitutionName(supplierVO.getIsMedicalInstitutionName());
//        supplierApplyFormReq.setInstitutionalType(supplierVO.getInstitutionalType());
//        supplierApplyFormReq.setInstitutionalTypeName(supplierVO.getInstitutionalTypeName());
//        supplierApplyFormReq.setHospitalType(supplierVO.getHospitalType());
//        supplierApplyFormReq.setHospitalTypeName(supplierVO.getHospitalTypeName());
//        supplierApplyFormReq.setHospitalClass(supplierVO.getHospitalClass());
//        supplierApplyFormReq.setHospitalClassName(supplierVO.getHospitalClassName());

        // 企业银行信息
        supplierApplyFormReq.setBankList(BeanUtil.copyFieldsList(supplierVO.getBankList(), CompanyBankReq.class));

        //企业资料信息
        supplierApplyFormReq.setCompanyCertList(BeanUtil.copyFieldsList(supplierVO.getCompanyCertList(), CompanyCertReq.class));

        return supplierApplyFormReq;
    }

    private SupplierApplyDetailVO fillDetailsToApplyDetail(OperationModel operationModel, SupplierApply supplierApply, SupplierApplyItem supplierApplyItem) {
        List<OrganizationVo> organizationVoList = organizationService.findListNoAuth(operationModel.getEnterpriseNo(),
                Lists.newArrayList(supplierApply.getManageOrgNo(), supplierApply.getUseOrgNo(), supplierApply.getApplyOrgNo()));
        final Map<String, OrganizationVo> orgNo2OrganizationVo = organizationVoList.stream().collect(Collectors.toMap(OrganizationVo::getOrgNo, Function.identity()));


        List<EmployeeVo> employeeList = employeeService.getEmployeeList(operationModel.getEnterpriseNo(),
                Lists.newArrayList(supplierApply.getApplyNo(), supplierApply.getAuditNo()));
        final Map<String, EmployeeVo> employeeNo2EmployeeVo = employeeList.stream().collect(Collectors.toMap(EmployeeVo::getEmployeeNo, Function.identity()));

        final Map<String, CustomDocResponse> applyRasonMap = customDocService.getItemByCustomDocCode(operationModel.getEnterpriseNo(), SystemConstant.DSRP_APPLY_REASON);


        SupplierApplyDetailVO result = BeanUtil.copyFields(supplierApply, SupplierApplyDetailVO.class);

        // 补充申请时填写的详情JSON
        result.setApplyContent(supplierApplyItem.getApplyContent());
        result.setPreApproveContent(supplierApplyItem.getPreApproveContent());
        result.setApproveContent(supplierApplyItem.getApproveContent());

        // 补充审核状态名称
        result.setAuditStatusName(SupplierApplyAuditStatusEnum.getNameByValue(supplierApply.getAuditStatus()));

        // 补充申请原因名称
        if (StringUtils.isNotEmpty(supplierApply.getApplyReason())) {
            result.setApplyReasonName(applyRasonMap.getOrDefault(supplierApply.getApplyReason(), new CustomDocResponse()).getDocItemName());
        }

        // 补充申请类型名称
        result.setApplyTypeName(SupplierApplyTypeEnum.getNameByValue(supplierApply.getApplyType()));

        // 补充申请组织名称
        if (orgNo2OrganizationVo.containsKey(supplierApply.getApplyOrgNo())) {
            result.setApplyOrgName(orgNo2OrganizationVo.get(supplierApply.getApplyOrgNo()).getOrgName());
        }

        // 补充使用组织名称
        if (orgNo2OrganizationVo.containsKey(supplierApply.getUseOrgNo())) {
            result.setUseOrgName(orgNo2OrganizationVo.get(supplierApply.getUseOrgNo()).getOrgName());
        }

        // 补充管理组织名称
        if (orgNo2OrganizationVo.containsKey(supplierApply.getManageOrgNo())) {
            result.setManageOrgName(orgNo2OrganizationVo.get(supplierApply.getManageOrgNo()).getOrgName());
        }

        // 补充申请人姓名
        if (employeeNo2EmployeeVo.containsKey(supplierApply.getApplyNo())) {
            result.setApplyName(employeeNo2EmployeeVo.get(supplierApply.getApplyNo()).getUserName());
        }

        // 补充审核人姓名
        if (employeeNo2EmployeeVo.containsKey(supplierApply.getAuditNo())) {
            result.setAuditName(employeeNo2EmployeeVo.get(supplierApply.getAuditNo()).getUserName());
        }

        // 补充执行结果
        result.setApplyResultName(null != supplierApply.getApplyResult() ? CommonIfEnum.getNameByValue(supplierApply.getApplyResult()) : "");


        // 获取供应商和企业信息，用于前端进行diff
        if (SupplierApplyAuditStatusEnum.APPROVED.getValue().equals(supplierApply.getAuditStatus())) {
            result.setLatestContent(supplierApplyItem.getPreApproveContent());
        } else {
            SupplierVO supplierVO = null;
            if (StringUtils.isNotEmpty(supplierApply.getSupplierCode())) {
                supplierVO = supplierV2Service.getDetailSupplierByCode(operationModel, supplierApply.getUseOrgNo(), supplierApply.getSupplierCode());
            }
            if (null == supplierVO) {
                supplierVO = supplierV2Service.getDetailSupplierByName(operationModel, supplierApply.getUseOrgNo(), supplierApply.getSupplierName());
            }

            if (null != supplierVO) {
                SupplierApplyFormReq supplierApplyDiffVO = packLatestDiff(supplierVO);

                result.setLatestContent(JSON.toJSONString(supplierApplyDiffVO));
            }
        }

        return result;
    }

    private List<SupplierApplyPageVO> completeSupplementPageVO(String enterpriseNo, List<SupplierApply> data) {
        List<com.yyigou.dsrp.cdc.api.v2.supplier.vo.SupplierApplyPageVO> result = new ArrayList<>();

        List<OrganizationVo> organizationVoList = organizationService.findListNoAuth(enterpriseNo, new ArrayList<>(data.stream().flatMap(supplierApply -> Stream.of(supplierApply.getManageOrgNo(), supplierApply.getUseOrgNo(), supplierApply.getApplyOrgNo())).collect(Collectors.toSet())));
        final Map<String, OrganizationVo> orgNo2OrganizationVo = organizationVoList.stream().collect(Collectors.toMap(OrganizationVo::getOrgNo, Function.identity()));

        List<EmployeeVo> employeeList = employeeService.getEmployeeList(enterpriseNo, new ArrayList<>(data.stream().flatMap(supplierApply -> Stream.of(supplierApply.getApplyNo(), supplierApply.getAuditNo())).collect(Collectors.toSet())));
        final Map<String, EmployeeVo> employeeNo2EmployeeVo = employeeList.stream().collect(Collectors.toMap(EmployeeVo::getEmployeeNo, Function.identity()));

        final Map<String, CustomDocResponse> applyRasonMap = customDocService.getItemByCustomDocCode(enterpriseNo, SystemConstant.DSRP_APPLY_REASON);

        data.forEach(supplierapply -> {
            com.yyigou.dsrp.cdc.api.v2.supplier.vo.SupplierApplyPageVO supplierApplyPageVO = new com.yyigou.dsrp.cdc.api.v2.supplier.vo.SupplierApplyPageVO();
            org.springframework.beans.BeanUtils.copyProperties(supplierapply, supplierApplyPageVO);

            // 补充审核状态名称
            supplierApplyPageVO.setAuditStatusName(SupplierApplyAuditStatusEnum.getNameByValue(supplierapply.getAuditStatus()));

            // 补充申请类型名称
            supplierApplyPageVO.setApplyTypeName(SupplierApplyTypeEnum.getNameByValue(supplierapply.getApplyType()));

            // 补充申请原因名称
            if (StringUtils.isNotEmpty(supplierapply.getApplyReason())) {
                supplierApplyPageVO.setApplyReasonName(applyRasonMap.getOrDefault(supplierapply.getApplyReason(), new CustomDocResponse()).getDocItemName());
            }

            // 补充申请组织名称
            if (orgNo2OrganizationVo.containsKey(supplierapply.getApplyOrgNo())) {
                supplierApplyPageVO.setApplyOrgName(orgNo2OrganizationVo.get(supplierapply.getApplyOrgNo()).getOrgName());
            }

            // 补充使用组织名称
            if (orgNo2OrganizationVo.containsKey(supplierapply.getUseOrgNo())) {
                supplierApplyPageVO.setUseOrgName(orgNo2OrganizationVo.get(supplierapply.getUseOrgNo()).getOrgName());
            }

            // 补充管理组织名称
            if (orgNo2OrganizationVo.containsKey(supplierapply.getManageOrgNo())) {
                supplierApplyPageVO.setManageOrgName(orgNo2OrganizationVo.get(supplierapply.getManageOrgNo()).getOrgName());
            }

            // 补充申请人姓名
            if (employeeNo2EmployeeVo.containsKey(supplierapply.getApplyNo())) {
                supplierApplyPageVO.setApplyName(employeeNo2EmployeeVo.get(supplierapply.getApplyNo()).getUserName());
            }

            // 补充审核人姓名
            if (employeeNo2EmployeeVo.containsKey(supplierapply.getAuditNo())) {
                supplierApplyPageVO.setAuditName(employeeNo2EmployeeVo.get(supplierapply.getAuditNo()).getUserName());
            }

            // 补充执行结果
            supplierApplyPageVO.setApplyResultName(null != supplierapply.getApplyResult() ? ApplyResultEnum.getNameByValue(supplierapply.getApplyResult()) : "");

            result.add(supplierApplyPageVO);
        });
        return result;
    }
}
