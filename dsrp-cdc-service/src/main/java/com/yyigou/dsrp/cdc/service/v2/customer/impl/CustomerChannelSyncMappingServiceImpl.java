package com.yyigou.dsrp.cdc.service.v2.customer.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yyigou.ddc.common.util.ValidatorUtils;
import com.yyigou.dsrp.cdc.common.enums.CommonSyncStatusEnum;
import com.yyigou.dsrp.cdc.dao.v2.customer.CustomerChannelSyncMappingDAO;
import com.yyigou.dsrp.cdc.dao.v2.customer.entity.CustomerChannelSyncMapping;
import com.yyigou.dsrp.cdc.model.v2.customer.req.CustomerChannelSyncMappingScanRequest;
import com.yyigou.dsrp.cdc.service.v2.customer.CustomerChannelSyncMappingService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service("customerChannelSyncMappingService")
@RequiredArgsConstructor
@Slf4j
public class CustomerChannelSyncMappingServiceImpl extends ServiceImpl<CustomerChannelSyncMappingDAO, CustomerChannelSyncMapping> implements CustomerChannelSyncMappingService {
    @Resource
    private CustomerChannelSyncMappingDAO customerChannelSyncMappingDAO;

    @Override
    public List<CustomerChannelSyncMapping> scanNeedSyncList(CustomerChannelSyncMappingScanRequest request) {
        ValidatorUtils.checkEmptyThrowEx(request, "参数不能为空");
        ValidatorUtils.checkEmptyThrowEx(request.getLimit(), "limit不能为空");
        ValidatorUtils.checkEmptyThrowEx(request.getFailTimes(), "failTimes不能为空");

        return customerChannelSyncMappingDAO.selectList(Wrappers.lambdaQuery(CustomerChannelSyncMapping.class)
                .and(
                        e -> e.eq(CustomerChannelSyncMapping::getSyncStatus, CommonSyncStatusEnum.Pending.getValue())
                                .or(
                                        e1 -> e1.eq(CustomerChannelSyncMapping::getSyncStatus, CommonSyncStatusEnum.FAIL.getValue())
                                                .le(CustomerChannelSyncMapping::getFailTimes, request.getFailTimes())
                                )
                )
                .orderByAsc(CustomerChannelSyncMapping::getId)
                .last("limit " + request.getLimit())
        );
    }

    @Override
    public int updateSyncStatus(CustomerChannelSyncMapping customerChannelSyncMapping) {
        return customerChannelSyncMappingDAO.updateById(customerChannelSyncMapping);
    }
}
