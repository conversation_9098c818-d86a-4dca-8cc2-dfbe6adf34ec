package com.yyigou.dsrp.cdc.service.v2.company;

import com.yyigou.dsrp.cdc.api.customer.dto.CustomerExternalSaveDTO;
import com.yyigou.dsrp.cdc.api.supply.dto.SupplierExternalSaveDTO;
import com.yyigou.dsrp.cdc.common.util.OperationModel;
import com.yyigou.dsrp.cdc.dao.v2.company.entity.CompanyV2;

import java.util.List;
import java.util.Map;

public interface CompanyV2ExternalService {
    Map<String, List<CompanyV2>> saveOrUpdateCompanyBySupplierForDA(OperationModel operationModel, List<SupplierExternalSaveDTO> supplierExternalSaveDTOList);

    Map<String, List<CompanyV2>> saveOrUpdateCompanyByCustomerForDA(OperationModel operationModel, List<CustomerExternalSaveDTO> customerExternalSaveDTOList);
}
