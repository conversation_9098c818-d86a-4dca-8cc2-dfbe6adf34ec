//package com.yyigou.dsrp.cdc.service.config;
//
//import com.yyigou.ddc.common.dubbo.openapi.OpenapiConfiguration;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//
//@Configuration
//public class OpenApiGatewayConfig {
//
//    @Bean
//    public OpenapiConfiguration openApiConfig() {
//        OpenapiConfiguration config = new OpenapiConfiguration();
//        config.setDomainCode("JCY");
//        config.setSubDomainCode("company");
//        return config;
//    }
//}
