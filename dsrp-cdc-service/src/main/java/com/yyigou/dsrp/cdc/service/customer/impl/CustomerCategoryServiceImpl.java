package com.yyigou.dsrp.cdc.service.customer.impl;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yyigou.ddc.common.treegrid.TreeGridUtils;
import com.yyigou.ddc.common.util.ValidatorUtils;
import com.yyigou.ddc.services.ddc.uim.vo.OrganizationVo;
import com.yyigou.dsrp.cdc.api.customer.dto.CustomerExternalSaveDTO;
import com.yyigou.dsrp.cdc.api.customer.vo.CustomerCategoryTree;
import com.yyigou.dsrp.cdc.common.enums.CommonStatusEnum;
import com.yyigou.dsrp.cdc.common.enums.DeletedEnum;
import com.yyigou.dsrp.cdc.common.util.CommonUtil;
import com.yyigou.dsrp.cdc.common.util.OperationModel;
import com.yyigou.dsrp.cdc.dao.customer.CustomerCategoryDAO;
import com.yyigou.dsrp.cdc.dao.customer.entity.CustomerCategory;
import com.yyigou.dsrp.cdc.manager.integration.numberCenter.NumberCenterService;
import com.yyigou.dsrp.cdc.manager.integration.org.UimTenantService;
import com.yyigou.dsrp.cdc.manager.integration.uim.TenantService;
import com.yyigou.dsrp.cdc.model.constant.NumberCenterConstant;
import com.yyigou.dsrp.cdc.model.constant.ServiceConstant;
import com.yyigou.dsrp.cdc.model.constant.SystemConstant;
import com.yyigou.dsrp.cdc.service.customer.CustomerCategoryService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 客户分类Service实现类
 *
 * @author: Moore
 * @date: 2024/7/17 15:28
 * @version: 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CustomerCategoryServiceImpl extends ServiceImpl<CustomerCategoryDAO, CustomerCategory> implements CustomerCategoryService {
    @Autowired
    private CustomerCategoryDAO customerCategoryDAO;
    @Autowired
    private NumberCenterService numberCenterService;
    private final TenantService tenantService;
    private final UimTenantService uimTenantService;

    /**
     * 根据客户分类编号查询客户分类信息
     *
     * @param enterpriseNo
     * @param no
     * @author: Moore
     * @date: 2024/7/17 15:30
     * @version: 1.0.0
     */
    @Override
    public CustomerCategory getByNo(String enterpriseNo, String no) {
        ValidatorUtils.checkEmptyThrowEx(enterpriseNo, "租户编号不能为空");
        ValidatorUtils.checkEmptyThrowEx(no, "客户分类编号不能为空");
        return customerCategoryDAO.getByNo(enterpriseNo, no);
    }


    @Override
    public CustomerCategory getByGroupNo(String enterpriseNo, String groupNo) {
        ValidatorUtils.checkEmptyThrowEx(enterpriseNo, "租户编号不能为空");
        ValidatorUtils.checkEmptyThrowEx(groupNo, "集團客户分类编号不能为空");
        List<CustomerCategory> customerCategoryList = customerCategoryDAO.getListByGroupNo(enterpriseNo, groupNo);
        if (CollectionUtils.isEmpty(customerCategoryList)){
            return null;
        }else if (customerCategoryList.size() > 1){
            log.error("客户分类编号重复 {} {}", enterpriseNo, groupNo);
        }
        return customerCategoryList.get(0);
    }

    @Override
    public List<CustomerCategory> getCategoryNoList(String enterpriseNo, List<String> noList) {
        ValidatorUtils.checkEmptyThrowEx(enterpriseNo, "租户编号不能为空");
        ValidatorUtils.checkEmptyThrowEx(noList, "客户分类编号不能为空");
        return customerCategoryDAO.getByNoList(enterpriseNo, noList);
    }

    /**
     * 获取父级分类
     *
     * @param enterpriseNo
     * @param no
     * @return: {@link LinkedList < CustomerCategory>}
     */
    @Override
    public LinkedList<CustomerCategory> getParentNoLinkedList(String enterpriseNo, String no) {
        List<CustomerCategory> customerCategoryList = getParentCategoryNoList(enterpriseNo, no);
        LinkedList<CustomerCategory> linkedList = new LinkedList<>();
        String parentNo = SystemConstant.COMMON_TOP_CATEGORY_NO;
        while (true) {
            String finalParentNo = parentNo;
            Optional<CustomerCategory> optional = customerCategoryList.stream().filter(t -> finalParentNo.equals(t.getParentNo())).findAny();
            if (!optional.isPresent()) {
                break;
            }
            CustomerCategory customerCategory = optional.get();
            linkedList.add(customerCategory);
            parentNo = customerCategory.getNo();
        }
        return linkedList;
    }

    public List<CustomerCategory> getParentCategoryNoList(String enterpriseNo, String categoryNo) {
        List<CustomerCategory> parentList = new ArrayList<>();
        CustomerCategory customerCategory = getByNo(enterpriseNo, categoryNo);
        parentList.add(customerCategory);
        String categoryParentNo = customerCategory.getParentNo();
        while (true) {
            if (StringUtils.isEmpty(categoryParentNo) || SystemConstant.COMMON_TOP_CATEGORY_NO.equals(categoryParentNo)) {
                break;
            }
            CustomerCategory parentCategory = getByNo(enterpriseNo, categoryParentNo);
            parentList.add(parentCategory);
            categoryParentNo = parentCategory.getParentNo();
        }
        return parentList;
    }

    /**
     * 根据租户编号+集团租户分类编号+父级分类编号查询分类信息
     *
     * @param enterpriseNo
     * @param groupNo
     * @param parentNo
     * @return: {@link CustomerCategory}
     */
    @Override
    public CustomerCategory findByGroupNoAndParentNo(String enterpriseNo, String groupNo, String parentNo) {
        ValidatorUtils.checkEmptyThrowEx(enterpriseNo, "租户编号不能为空");
        return customerCategoryDAO.findByGroupNoAndParentNo(enterpriseNo, groupNo, parentNo);
    }

    @Override
    public void createAndUpdateCategory(OperationModel operationModel, List<CustomerExternalSaveDTO> params) {
        // 1.查询企业下存货分类
        LambdaQueryWrapper<CustomerCategory> queryWrapper = Wrappers.lambdaQuery(CustomerCategory.class)
                .eq(CustomerCategory::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CustomerCategory::getDeleted, DeletedEnum.UN_DELETE.getValue());
        List<CustomerCategory> CustomerCategoryList = customerCategoryDAO.selectList(queryWrapper);

        Map<String, List<CustomerCategory>> goodsCategoryGroupMap = CustomerCategoryList.stream().collect(Collectors.groupingBy(CustomerCategory::getParentNo));
        List<CustomerCategory> topNodeList = goodsCategoryGroupMap.getOrDefault(ServiceConstant.TOP_PARENT_NO, new ArrayList<>());

        params.forEach(t -> {
            if (org.apache.commons.lang3.StringUtils.isBlank(t.getCustomerCategoryCode_1())) {
                return;
            }
            Optional<CustomerCategory> categoryOptional = topNodeList.stream().filter(CustomerCategory -> t.getCustomerCategoryCode_1().equals(CustomerCategory.getCategoryCode())).findAny();
            CustomerCategory currentCategory = null;
            if (categoryOptional.isPresent()) {
                CustomerCategory goodsCategory = categoryOptional.get();
                goodsCategory.setCategoryName(t.getCustomerCategoryName_1());
                customerCategoryDAO.updateById(goodsCategory);
                currentCategory = goodsCategory;
                t.setCustomerCategoryNo(currentCategory.getNo());
            } else {
                //新增逻辑
                CustomerCategory category = new CustomerCategory();
                category.setParentNo(ServiceConstant.TOP_PARENT_NO);
                category.setNo(numberCenterService.createTreeNumber(NumberCenterConstant.CUSTOMER_NO_CATEGORY_KEY, ServiceConstant.TOP_PARENT_NO));
                category.setEnterpriseNo(operationModel.getEnterpriseNo());
                category.setCategoryName(t.getCustomerCategoryName_1());
                category.setCategoryCode(t.getCustomerCategoryCode_1());
                category.setMnemonicCode(t.getCustomerCategoryCode_1());
                category.setStatus(CommonStatusEnum.EFFECTIVE.getValue().toString());
                category.setDeleted(DeletedEnum.UN_DELETE.getValue());
                CommonUtil.fillCreatInfo(operationModel, category);
                customerCategoryDAO.insert(category);
                currentCategory = category;
                CustomerCategoryList.add(currentCategory);
                t.setCustomerCategoryNo(currentCategory.getNo());
            }
            if (org.apache.commons.lang3.StringUtils.isNotBlank(t.getCustomerCategoryCode_2())) {
                CustomerCategory finalCurrentCategory = currentCategory;
                Optional<CustomerCategory> categoryItemOptional = CustomerCategoryList.stream().filter(s -> s.getParentNo().equals(finalCurrentCategory.getNo()) && t.getCustomerCategoryCode_2().equals(s.getCategoryCode())).findAny();
                if (categoryItemOptional.isPresent()) {
                    CustomerCategory CustomerCategory = categoryItemOptional.get();
                    CustomerCategory.setCategoryName(t.getCustomerCategoryName_2());
                    customerCategoryDAO.updateById(CustomerCategory);
                    currentCategory = CustomerCategory;
                } else {
                    //新增逻辑
                    CustomerCategory CustomerCategory = new CustomerCategory();
                    CustomerCategory.setNo(numberCenterService.createTreeNumber(NumberCenterConstant.CUSTOMER_NO_CATEGORY_KEY, currentCategory.getNo()));
                    CustomerCategory.setParentNo(currentCategory.getNo());
                    CustomerCategory.setEnterpriseNo(operationModel.getEnterpriseNo());
                    CustomerCategory.setCategoryName(t.getCustomerCategoryName_2());
                    CustomerCategory.setCategoryCode(t.getCustomerCategoryCode_2());
                    CustomerCategory.setMnemonicCode(t.getCustomerCategoryCode_2());
                    CustomerCategory.setStatus(CommonStatusEnum.EFFECTIVE.getValue().toString());
                    CustomerCategory.setDeleted(DeletedEnum.UN_DELETE.getValue());
                    CommonUtil.fillCreatInfo(operationModel, CustomerCategory);
                    customerCategoryDAO.insert(CustomerCategory);
                    currentCategory = CustomerCategory;
                    CustomerCategoryList.add(currentCategory);
                }
                t.setCustomerCategoryNo(currentCategory.getNo());
            }

            if (org.apache.commons.lang3.StringUtils.isNotBlank(t.getCustomerCategoryCode_3())) {
                CustomerCategory finalCurrentCategory = currentCategory;
                Optional<CustomerCategory> categoryItemOptional = CustomerCategoryList.stream().filter(s -> s.getParentNo().equals(finalCurrentCategory.getNo()) && t.getCustomerCategoryCode_3().equals(s.getCategoryCode())).findAny();
                if (categoryItemOptional.isPresent()) {
                    CustomerCategory CustomerCategory = categoryItemOptional.get();
                    CustomerCategory.setCategoryName(t.getCustomerCategoryName_3());
                    customerCategoryDAO.updateById(CustomerCategory);
                    currentCategory = CustomerCategory;
                } else {
                    //新增逻辑
                    CustomerCategory CustomerCategory = new CustomerCategory();
                    CustomerCategory.setNo(numberCenterService.createTreeNumber(NumberCenterConstant.CUSTOMER_NO_CATEGORY_KEY, currentCategory.getNo()));
                    CustomerCategory.setParentNo(currentCategory.getNo());
                    CustomerCategory.setEnterpriseNo(operationModel.getEnterpriseNo());
                    CustomerCategory.setCategoryName(t.getCustomerCategoryName_3());
                    CustomerCategory.setCategoryCode(t.getCustomerCategoryCode_3());
                    CustomerCategory.setMnemonicCode(t.getCustomerCategoryCode_3());
                    CustomerCategory.setStatus(CommonStatusEnum.EFFECTIVE.getValue().toString());
                    CustomerCategory.setDeleted(DeletedEnum.UN_DELETE.getValue());
                    CommonUtil.fillCreatInfo(operationModel, CustomerCategory);
                    customerCategoryDAO.insert(CustomerCategory);
                    currentCategory = CustomerCategory;
                    CustomerCategoryList.add(currentCategory);
                }
                t.setCustomerCategoryNo(currentCategory.getNo());
            }

            if (org.apache.commons.lang3.StringUtils.isNotBlank(t.getCustomerCategoryCode_4())) {
                CustomerCategory finalCurrentCategory = currentCategory;
                Optional<CustomerCategory> categoryItemOptional = CustomerCategoryList.stream().filter(s -> s.getParentNo().equals(finalCurrentCategory.getNo()) && t.getCustomerCategoryCode_4().equals(s.getCategoryCode())).findAny();
                if (categoryItemOptional.isPresent()) {
                    CustomerCategory CustomerCategory = categoryItemOptional.get();
                    CustomerCategory.setCategoryName(t.getCustomerCategoryName_4());
                    customerCategoryDAO.updateById(CustomerCategory);
                    currentCategory = CustomerCategory;
                } else {
                    //新增逻辑
                    CustomerCategory CustomerCategory = new CustomerCategory();
                    CustomerCategory.setNo(numberCenterService.createTreeNumber(NumberCenterConstant.CUSTOMER_NO_CATEGORY_KEY, currentCategory.getNo()));
                    CustomerCategory.setParentNo(currentCategory.getNo());
                    CustomerCategory.setEnterpriseNo(operationModel.getEnterpriseNo());
                    CustomerCategory.setCategoryName(t.getCustomerCategoryName_4());
                    CustomerCategory.setCategoryCode(t.getCustomerCategoryCode_4());
                    CustomerCategory.setMnemonicCode(t.getCustomerCategoryCode_4());
                    CustomerCategory.setStatus(CommonStatusEnum.EFFECTIVE.getValue().toString());
                    CustomerCategory.setDeleted(DeletedEnum.UN_DELETE.getValue());
                    CommonUtil.fillCreatInfo(operationModel, CustomerCategory);
                    customerCategoryDAO.insert(CustomerCategory);
                    currentCategory = CustomerCategory;
                    CustomerCategoryList.add(currentCategory);
                }
                t.setCustomerCategoryNo(currentCategory.getNo());
            }
            if (org.apache.commons.lang3.StringUtils.isNotBlank(t.getCustomerCategoryCode_5())) {
                CustomerCategory finalCurrentCategory = currentCategory;
                Optional<CustomerCategory> categoryItemOptional = CustomerCategoryList.stream().filter(s -> s.getParentNo().equals(finalCurrentCategory.getNo()) && t.getCustomerCategoryCode_5().equals(s.getCategoryCode())).findAny();
                if (categoryItemOptional.isPresent()) {
                    CustomerCategory CustomerCategory = categoryItemOptional.get();
                    CustomerCategory.setCategoryName(t.getCustomerCategoryName_5());
                    customerCategoryDAO.updateById(CustomerCategory);
                    currentCategory = CustomerCategory;
                } else {
                    //新增逻辑
                    CustomerCategory CustomerCategory = new CustomerCategory();
                    CustomerCategory.setNo(numberCenterService.createTreeNumber(NumberCenterConstant.CUSTOMER_NO_CATEGORY_KEY, currentCategory.getNo()));
                    CustomerCategory.setParentNo(currentCategory.getNo());
                    CustomerCategory.setEnterpriseNo(operationModel.getEnterpriseNo());
                    CustomerCategory.setCategoryName(t.getCustomerCategoryName_5());
                    CustomerCategory.setCategoryCode(t.getCustomerCategoryCode_5());
                    CustomerCategory.setMnemonicCode(t.getCustomerCategoryCode_5());
                    CustomerCategory.setStatus(CommonStatusEnum.EFFECTIVE.getValue().toString());
                    CustomerCategory.setDeleted(DeletedEnum.UN_DELETE.getValue());
                    CommonUtil.fillCreatInfo(operationModel, CustomerCategory);
                    customerCategoryDAO.insert(CustomerCategory);
                    currentCategory = CustomerCategory;
                    CustomerCategoryList.add(currentCategory);
                }
                t.setCustomerCategoryNo(currentCategory.getNo());
            }
        });
    }

    public List<CustomerCategoryTree> queryTree(OperationModel operationModel) {
        final List<CustomerCategory> categoryList = customerCategoryDAO.selectList(Wrappers.<CustomerCategory>lambdaQuery()
                .eq(CustomerCategory::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CustomerCategory::getDeleted, DeletedEnum.UN_DELETE.getValue()));
        if (CollectionUtils.isEmpty(categoryList)) {
            return new ArrayList<>();
        }
        List<CustomerCategoryTree> treeList = new ArrayList<>();
        categoryList.forEach(t -> {
            CustomerCategoryTree tree = new CustomerCategoryTree();
            BeanUtils.copyProperties(t, tree);
            treeList.add(tree);
        });
        return TreeGridUtils.getChildCategoryTrees(treeList, ServiceConstant.TOP_PARENT_NO);
    }

    @Override
    public List<CustomerCategoryTree> queryGroupTree(OperationModel operationModel) {
        final String groupEnterpriseNo = tenantService.getGroupEnterpriseNo(operationModel.getEnterpriseNo());
        operationModel.setEnterpriseNo(groupEnterpriseNo);
        return queryTree(operationModel);
    }


    @Override
    public List<CustomerCategoryTree> queryOrgTree(OperationModel operationModel, String orgNo) {
        List<OrganizationVo> organizationList = uimTenantService.getAuthEnterpriseList(operationModel.getEnterpriseNo(), operationModel.getUserNo(), operationModel.getEmployerNo());
        final Optional<OrganizationVo> optional = organizationList.stream().filter(t -> orgNo.equals(t.getOrgNo())).findAny();
        if (!optional.isPresent()) {
            //无权限查询
            return new ArrayList();
        }

        final List<CustomerCategory> categoryList = customerCategoryDAO.selectList(Wrappers.<CustomerCategory>lambdaQuery()
                .eq(CustomerCategory::getEnterpriseNo, optional.get().getBindingEnterpriseNo())
                .eq(CustomerCategory::getDeleted, DeletedEnum.UN_DELETE.getValue()));

        if (CollectionUtils.isEmpty(categoryList)) {
            return new ArrayList<>();
        }
        List<CustomerCategoryTree> treeList = new ArrayList<>();
        categoryList.forEach(t -> {
            CustomerCategoryTree tree = new CustomerCategoryTree();
            BeanUtils.copyProperties(t, tree);
            tree.setLabel(t.getCategoryName());
            tree.setNo(t.getNo());
            tree.setParentNo(t.getParentNo());
            treeList.add(tree);
        });
        return TreeGridUtils.getChildCategoryTrees(treeList, ServiceConstant.TOP_PARENT_NO);
    }
}
