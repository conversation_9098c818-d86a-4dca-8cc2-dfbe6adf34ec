package com.yyigou.dsrp.cdc.service.v2.customer.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yyigou.ddc.common.exception.BusinessException;
import com.yyigou.ddc.common.util.ValidatorUtils;
import com.yyigou.ddc.services.ddc.task.export.base.AbstractUapExcelCheckAndImport;
import com.yyigou.ddc.services.ddc.task.state.ExcelRowCheckResultEnum;
import com.yyigou.ddc.services.ddc.uim.vo.EmployeeVo;
import com.yyigou.ddc.services.ddc.uim.vo.OrganizationVo;
import com.yyigou.ddc.services.dsrp.bdc.enums.FactoryTypeEnum;
import com.yyigou.dsrp.cdc.api.v2.customer.dto.*;
import com.yyigou.dsrp.cdc.common.enums.CommonIfEnum;
import com.yyigou.dsrp.cdc.common.enums.CommonStatusEnum;
import com.yyigou.dsrp.cdc.common.enums.DeletedEnum;
import com.yyigou.dsrp.cdc.common.enums.InvoiceTypeEnum;
import com.yyigou.dsrp.cdc.common.enums.company.HospitalTypeEnum;
import com.yyigou.dsrp.cdc.common.util.OperationModel;
import com.yyigou.dsrp.cdc.dao.v2.customer.CustomerCategoryV2DAO;
import com.yyigou.dsrp.cdc.dao.v2.customer.CustomerV2DAO;
import com.yyigou.dsrp.cdc.dao.v2.customer.entity.CustomerCategoryV2;
import com.yyigou.dsrp.cdc.dao.v2.customer.entity.CustomerV2;
import com.yyigou.dsrp.cdc.manager.integration.uim.EmployeeService;
import com.yyigou.dsrp.cdc.manager.integration.uim.OrganizationService;
import com.yyigou.dsrp.cdc.model.v2.company.req.CompanyBankReq;
import com.yyigou.dsrp.cdc.model.v2.company.req.CompanyLinkmanReq;
import com.yyigou.dsrp.cdc.model.v2.company.req.CompanyShippingAddressReq;
import com.yyigou.dsrp.cdc.model.v2.customer.req.*;
import com.yyigou.dsrp.cdc.service.utils.UserHandleUtils;
import com.yyigou.dsrp.cdc.service.v2.customer.CustomerV2Service;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service("customerV2ImportService")
@RequiredArgsConstructor
@Slf4j
public class CustomerV2ImportServiceImpl extends AbstractUapExcelCheckAndImport<CustomerImportDTO> {

    @Resource
    private CustomerV2DAO customerV2DAO;

    @Resource
    private CustomerCategoryV2DAO customerCategoryV2DAO;

    @Resource
    private CustomerV2Service customerV2Service;

    @Resource
    private TransactionTemplate transactionTemplate;


    @Resource
    private OrganizationService organizationService;


    @Resource
    private EmployeeService employeeService;

    @Override
    public List<CustomerImportDTO> checkBatchImportData(List<CustomerImportDTO> pojoList) {
        log.warn("checkBatchImportDataReq={}", pojoList);

        OperationModel operationModel = UserHandleUtils.getOperationModel();

        validateImportCustomer(operationModel, pojoList);

        log.warn("checkBatchImportDataResp={}", pojoList);

        return pojoList;
    }

    @Override
    public List<CustomerImportDTO> handleImportSliceData(List<CustomerImportDTO> pojoList) {
        log.warn("handleImportSliceDataReq={}", pojoList);

        OperationModel operationModel = UserHandleUtils.getOperationModel();

        adjustValue(operationModel, pojoList);

        for (CustomerImportDTO customerImportDTO : pojoList) {
            transactionTemplate.execute(status -> {
                // 新增客户
                CustomerSaveBasicAndBizReq customerSaveBasicAndBizReq = packCustomerSaveBasicAndBizReq(operationModel, customerImportDTO);
                customerV2Service.manageSaveCustomerBasicAndBiz(operationModel, customerSaveBasicAndBizReq);

                // 新增联系人信息
                CustomerLinkmanListEditReq customerLinkmanListEditReq = packCustomerSaveLinkmanListReq(operationModel, customerImportDTO);
                customerV2Service.editCustomerLinkmanList(operationModel, customerLinkmanListEditReq);

                // 新增地址信息
                CustomerAddressListEditReq customerAddressListEditReq = packCustomerSaveAddressListReq(operationModel, customerImportDTO);
                customerV2Service.editCustomerAddressList(operationModel, customerAddressListEditReq);

                // 新增银行信息
                CustomerBankListEditReq customerBankListEditReq = packCustomerSaveBankListReq(operationModel, customerImportDTO);
                customerV2Service.editCustomerBankList(operationModel, customerBankListEditReq);

                // 新增开票信息
                CustomerInvoiceListEditReq customerInvoiceListEditReq = packCustomerSaveInvoiceListReq(operationModel, customerImportDTO);
                customerV2Service.editCustomerInvoiceList(operationModel, customerInvoiceListEditReq);

                // 新增负责人信息
                CustomerSalesmanListEditReq customerSaveOrdermanListReq = packCustomerSaveSalesmanListReq(operationModel, customerImportDTO);
                customerV2Service.editCustomerSalesmanList(operationModel, customerSaveOrdermanListReq);


                return null;
            });
        }

        log.warn("handleImportSliceDataResp={}", pojoList);

        return pojoList;
    }

    private CustomerAddressListEditReq packCustomerSaveAddressListReq(OperationModel operationModel, CustomerImportDTO customerImportDTO) {
        CustomerAddressListEditReq req = new CustomerAddressListEditReq();

        // 设置基本信息
        req.setEnterpriseNo(operationModel.getEnterpriseNo());
        req.setUseOrgNo(customerImportDTO.getManageOrgNo()); // 使用组织编号使用管理组织编号
        req.setCustomerCode(customerImportDTO.getCustomerCode());

        // 处理地址列表
        if (CollectionUtils.isNotEmpty(customerImportDTO.getBdcCompanyShippingAddressDetailList())) {
            List<CompanyShippingAddressReq> addressList = new ArrayList<>(customerImportDTO.getBdcCompanyShippingAddressDetailList().size());

            for (CustomerAddressImportDTO addressImportDTO : customerImportDTO.getBdcCompanyShippingAddressDetailList()) {
                CompanyShippingAddressReq addressReq = new CompanyShippingAddressReq();

                // 设置地址信息
                addressReq.setReceiveUser(addressImportDTO.getReceiveUser());
                addressReq.setReceivePhone(addressImportDTO.getReceivePhone());
                addressReq.setAddressType(addressImportDTO.getAddressType());
                addressReq.setRegionCode(addressImportDTO.getRegionCode());
                addressReq.setRegionName(addressImportDTO.getRegionCodeName());
                addressReq.setReceiveAddr(addressImportDTO.getReceiveAddr());
                addressReq.setAddressDesc(addressImportDTO.getAddressDesc());
                addressReq.setIsDefault(addressImportDTO.getIsDefault());

                // 添加到列表
                addressList.add(addressReq);
            }

            req.setLinkAddressList(addressList);
        }

        return req;
    }

    private CustomerBankListEditReq packCustomerSaveBankListReq(OperationModel operationModel, CustomerImportDTO customerImportDTO) {
        CustomerBankListEditReq req = new CustomerBankListEditReq();

        // 设置基本信息
        req.setEnterpriseNo(operationModel.getEnterpriseNo());
        req.setManageOrgNo(customerImportDTO.getManageOrgNo()); // 银行信息使用管理组织编号
        req.setCustomerCode(customerImportDTO.getCustomerCode());

        // 处理银行列表
        if (CollectionUtils.isNotEmpty(customerImportDTO.getBdcSupplierBankDetailList())) {
            List<CompanyBankReq> bankList = new ArrayList<>(customerImportDTO.getBdcSupplierBankDetailList().size());

            for (CustomerBankImportDTO bankImportDTO : customerImportDTO.getBdcSupplierBankDetailList()) {
                CompanyBankReq bankReq = new CompanyBankReq();

                // 设置银行信息
                bankReq.setOpenBank(bankImportDTO.getOpenBank());
                bankReq.setAccountNo(bankImportDTO.getAccountNo());
                bankReq.setAccountName(bankImportDTO.getAccountName());
                bankReq.setBankType(bankImportDTO.getBankType());
                bankReq.setCurrencyId(bankImportDTO.getCurrencyId());
                bankReq.setAccountType(bankImportDTO.getAccountType());
                bankReq.setLinkPerson(bankImportDTO.getLinkPerson());
                bankReq.setLinkPhone(bankImportDTO.getLinkPhone());
                bankReq.setIsDefault(bankImportDTO.getIsDefault());
                bankReq.setStatus(bankImportDTO.getStatus());

                // 添加到列表
                bankList.add(bankReq);
            }

            req.setBankList(bankList);
        }

        return req;
    }

    private CustomerInvoiceListEditReq packCustomerSaveInvoiceListReq(OperationModel operationModel, CustomerImportDTO customerImportDTO) {
        CustomerInvoiceListEditReq req = new CustomerInvoiceListEditReq();

        // 设置基本信息
        req.setEnterpriseNo(operationModel.getEnterpriseNo());
        req.setUseOrgNo(customerImportDTO.getManageOrgNo()); // 使用组织编号使用管理组织编号
        req.setCustomerCode(customerImportDTO.getCustomerCode());

        // 处理发票列表
        if (CollectionUtils.isNotEmpty(customerImportDTO.getBdcCustomerInvoiceDetailList())) {
            List<CustomerInvoiceReq> invoiceList = new ArrayList<>(customerImportDTO.getBdcCustomerInvoiceDetailList().size());

            for (CustomerInvoiceImportDTO invoiceImportDTO : customerImportDTO.getBdcCustomerInvoiceDetailList()) {
                CustomerInvoiceReq invoiceReq = new CustomerInvoiceReq();

                // 设置发票信息
                invoiceReq.setType(invoiceImportDTO.getType());
                invoiceReq.setInvoiceTitle(invoiceImportDTO.getInvoiceTitle());
                invoiceReq.setTaxNo(invoiceImportDTO.getTaxNo());
                invoiceReq.setAddress(invoiceImportDTO.getAddress());
                invoiceReq.setPhone(invoiceImportDTO.getPhone());
                invoiceReq.setBankDeposit(invoiceImportDTO.getBankDeposit());
                invoiceReq.setBankAccount(invoiceImportDTO.getBankAccount());
                invoiceReq.setInvoicePhone(invoiceImportDTO.getInvoicePhone());
                invoiceReq.setEmail(invoiceImportDTO.getEmail());
                invoiceReq.setRequirement(invoiceImportDTO.getRequirement());
                invoiceReq.setIsDefault(invoiceImportDTO.getIsDefault());

                // 添加到列表
                invoiceList.add(invoiceReq);
            }

            req.setInvoiceList(invoiceList);
        }

        return req;
    }

    private CustomerSalesmanListEditReq packCustomerSaveSalesmanListReq(OperationModel operationModel, CustomerImportDTO customerImportDTO) {
        CustomerSalesmanListEditReq req = new CustomerSalesmanListEditReq();

        // 设置基本信息
        req.setEnterpriseNo(operationModel.getEnterpriseNo());
        req.setUseOrgNo(customerImportDTO.getManageOrgNo()); // 使用组织编号使用管理组织编号
        req.setCustomerCode(customerImportDTO.getCustomerCode());

        // 处理负责人列表
        if (CollectionUtils.isNotEmpty(customerImportDTO.getBdcCustomerSalesManDetailList())) {
            List<CustomerSalesManReq> orderManList = new ArrayList<>(customerImportDTO.getBdcCustomerSalesManDetailList().size());

            for (CustomerSalesManImportDTO orderManImportDTO : customerImportDTO.getBdcCustomerSalesManDetailList()) {
                CustomerSalesManReq orderManReq = new CustomerSalesManReq();

                // 设置负责人信息
                orderManReq.setDeptNo(orderManImportDTO.getDeptNo());
                orderManReq.setSalesManName(orderManImportDTO.getSalesManName());
                orderManReq.setPost(orderManImportDTO.getPost());
                orderManReq.setOrderSpecialist(orderManImportDTO.getOrderSpecialist());
                orderManReq.setIsDefault(orderManImportDTO.getIsDefault());

                // 添加到列表
                orderManList.add(orderManReq);
            }

            req.setSalesManList(orderManList);
        }

        return req;
    }

    private CustomerLinkmanListEditReq packCustomerSaveLinkmanListReq(OperationModel operationModel, CustomerImportDTO customerImportDTO) {
        CustomerLinkmanListEditReq req = new CustomerLinkmanListEditReq();

        // 设置基本信息
        req.setEnterpriseNo(operationModel.getEnterpriseNo());
        req.setUseOrgNo(customerImportDTO.getManageOrgNo()); // 使用组织编号使用管理组织编号
        req.setCustomerCode(customerImportDTO.getCustomerCode());

        // 处理联系人列表
        if (CollectionUtils.isNotEmpty(customerImportDTO.getBdcCompanyLinkmanDetailList())) {
            List<CompanyLinkmanReq> linkmanList = new ArrayList<>(customerImportDTO.getBdcCompanyLinkmanDetailList().size());

            for (CustomerLinkmanImportDTO linkmanImportDTO : customerImportDTO.getBdcCompanyLinkmanDetailList()) {
                CompanyLinkmanReq linkmanReq = new CompanyLinkmanReq();

                // 设置联系人信息
                linkmanReq.setLinkman(linkmanImportDTO.getLinkman());
                linkmanReq.setSex(linkmanImportDTO.getSex());
                linkmanReq.setPosition(linkmanImportDTO.getPosition());
                linkmanReq.setFixedPhone(linkmanImportDTO.getFixedPhone());
                linkmanReq.setMobilePhone(linkmanImportDTO.getMobilePhone());
                linkmanReq.setQq(linkmanImportDTO.getQq());
                linkmanReq.setWx(linkmanImportDTO.getWx());
                linkmanReq.setEmail(linkmanImportDTO.getEmail());
                linkmanReq.setIsDefault(linkmanImportDTO.getIsDefault());
                linkmanReq.setStatus(linkmanImportDTO.getStatus());

                // 添加到列表
                linkmanList.add(linkmanReq);
            }

            req.setLinkmanList(linkmanList);
        }

        return req;
    }

    private CustomerSaveBasicAndBizReq packCustomerSaveBasicAndBizReq(OperationModel operationModel, CustomerImportDTO customerImportDTO) {
        CustomerSaveBasicAndBizReq req = new CustomerSaveBasicAndBizReq();

        req.setEnterpriseNo(operationModel.getEnterpriseNo());

        // 设置基本信息
        req.setManageOrgNo(customerImportDTO.getManageOrgNo());
        req.setCustomerCode(customerImportDTO.getCustomerCode());
        req.setCustomerName(customerImportDTO.getCustomerName());
        req.setCustomerNameEn(customerImportDTO.getCustomerNameEn());
        req.setMnemonicCode(customerImportDTO.getMnemonicCode());
        req.setTransactionType(customerImportDTO.getTransactionType());
        req.setCustomerCategoryNo(customerImportDTO.getCustomerCategoryNo());
        req.setCompanyName(customerImportDTO.getCompanyName());
        req.setUnifiedSocialCode(customerImportDTO.getUnifiedSocialCode());
        req.setFactoryType(customerImportDTO.getFactoryType());
        req.setCountryRegionId(customerImportDTO.getCountryRegionId());
        req.setTaxCategory(customerImportDTO.getTaxCategory());
        req.setEconomicType(customerImportDTO.getEconomicType());
        req.setRetailInvestors(customerImportDTO.getRetailInvestors());
        req.setIsAssociatedEnterprise(customerImportDTO.getIsAssociatedEnterprise());
        req.setAssociatedOrgNo(customerImportDTO.getAssociatedOrgNo());
        req.setAssociatedOrgCode(customerImportDTO.getAssociatedOrgCode());
        req.setAssociatedOrgName(customerImportDTO.getAssociatedOrgNoName());
        req.setIsMedicalInstitution(customerImportDTO.getIsMedicalInstitution());
        req.setInstitutionalType(customerImportDTO.getInstitutionalType());
        req.setHospitalType(customerImportDTO.getHospitalType());
        req.setHospitalClass(customerImportDTO.getHospitalClass());
        req.setRemark(customerImportDTO.getRemark());

        // 设置业务信息
        req.setCooperationMode(customerImportDTO.getCooperationMode());
        req.setCooperationModeName(customerImportDTO.getCooperationModeName());
        req.setBusinessType(customerImportDTO.getBusinessType());
        req.setBusinessTypeName(customerImportDTO.getBusinessTypeName());
        req.setPriceCategoryCode(customerImportDTO.getPriceCategoryCode());
        req.setCurrencyId(customerImportDTO.getCurrencyId());
        req.setSettlementModes(customerImportDTO.getSettlementModes());
        req.setSettlementModesName(customerImportDTO.getSettlementModesName());
        req.setReceiveAgreement(customerImportDTO.getReceiveAgreement());
        req.setReceiveCondition(customerImportDTO.getReceiveCondition());
        req.setCreditAmount(customerImportDTO.getCreditAmount());
        req.setCreditDates(customerImportDTO.getCreditDates());
        req.setCoopStartTime(customerImportDTO.getCoopStartTime());
        req.setCoopEndTime(customerImportDTO.getCoopEndTime());
        req.setOwnerCompany(customerImportDTO.getOwnerCompany());


        return req;
    }

    private void adjustValue(OperationModel operationModel, List<CustomerImportDTO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        Map<String, List<CustomerCategoryV2>> categoryNameMap = new HashMap<>();
        List<String> customerCategoryNameList = list.stream().map(CustomerImportDTO::getCustomerCategoryName).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(customerCategoryNameList)) {
            List<CustomerCategoryV2> customerCategoryV2s = customerCategoryV2DAO.selectList(Wrappers.<CustomerCategoryV2>lambdaQuery()
                    .eq(CustomerCategoryV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                    .in(CustomerCategoryV2::getCategoryName, customerCategoryNameList)
                    .eq(CustomerCategoryV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
            categoryNameMap = customerCategoryV2s.stream().collect(Collectors.groupingBy(CustomerCategoryV2::getCategoryName));
        }

        Map<String, OrganizationVo> organizationVoMap = new HashMap<>();
        List<String> associatedOrgNoList = list.stream().map(CustomerImportDTO::getAssociatedOrgNo).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(associatedOrgNoList)) {
            List<OrganizationVo> organizationVos = organizationService.findListNoAuth(operationModel.getEnterpriseNo(), associatedOrgNoList);
            organizationVoMap = organizationVos.stream().collect(Collectors.toMap(OrganizationVo::getOrgNo, Function.identity()));
        }

        Map<String, List<EmployeeVo>> employeeNameMap = new HashMap<>();
        List<String> orderManNameList = list.stream().filter(supp -> null != supp.getBdcCustomerSalesManDetailList()).flatMap(supp -> supp.getBdcCustomerSalesManDetailList().stream()).map(CustomerSalesManImportDTO::getSalesManName).collect(Collectors.toList());
        List<EmployeeVo> employeeByNames = employeeService.getEmployeeByNames(operationModel.getEnterpriseNo(), orderManNameList);
        if (CollectionUtils.isNotEmpty(employeeByNames)) {
            employeeNameMap = employeeByNames.stream().collect(Collectors.groupingBy(EmployeeVo::getUserName));
        }

        for (CustomerImportDTO customerImportDTO : list) {
            // 分类
            if (!categoryNameMap.containsKey(customerImportDTO.getCustomerCategoryName())) {
                throw new BusinessException(customerImportDTO.getCustomerCategoryName() + "不存在");
            }
            List<CustomerCategoryV2> maybeMultiCategoryList = categoryNameMap.get(customerImportDTO.getCustomerCategoryName());
            if (CollectionUtils.isNotEmpty(maybeMultiCategoryList) && maybeMultiCategoryList.size() > 1) {
                throw new BusinessException(customerImportDTO.getCustomerCategoryName() + "对应多个分类");
            }
            customerImportDTO.setCustomerCategoryNo(maybeMultiCategoryList.get(0).getNo());

            // 企业名称
            if (StringUtils.isEmpty(customerImportDTO.getCompanyName())) {
                customerImportDTO.setCompanyName(customerImportDTO.getCustomerName());
            }

            // 企业注册地域
            if (null == customerImportDTO.getFactoryType()) {
                customerImportDTO.setFactoryType(FactoryTypeEnum.domestic.getValue());
            }

            // 散户
            if (null == customerImportDTO.getRetailInvestors()) {
                customerImportDTO.setRetailInvestors(CommonIfEnum.NO.getValue());
            }

            // 内部组织
            if (null == customerImportDTO.getIsAssociatedEnterprise()) {
                customerImportDTO.setIsAssociatedEnterprise(CommonIfEnum.NO.getValue());
            }

            if (CommonIfEnum.NO.getValue().equals(customerImportDTO.getIsAssociatedEnterprise())) {
                customerImportDTO.setAssociatedOrgNo(null);
            }

            // setAssociatedOrgCode
            if (CommonIfEnum.YES.getValue().equals(customerImportDTO.getIsAssociatedEnterprise())
                    && StringUtils.isNotEmpty(customerImportDTO.getAssociatedOrgNo())) {
                OrganizationVo organizationVo = organizationVoMap.get(customerImportDTO.getAssociatedOrgNo());
                if (null != organizationVo) {
                    customerImportDTO.setAssociatedOrgCode(organizationVo.getOrgCode());
                }
            }

            // 医疗机构
            if (null == customerImportDTO.getIsMedicalInstitution()) {
                customerImportDTO.setIsMedicalInstitution(CommonIfEnum.NO.getValue());
            }

            if (CommonIfEnum.NO.getValue().equals(customerImportDTO.getIsMedicalInstitution())) {
                customerImportDTO.setInstitutionalType(null);
                customerImportDTO.setHospitalType(null);
                customerImportDTO.setHospitalClass(null);
            }

            // 处理联系人
            if (CollectionUtils.isNotEmpty(customerImportDTO.getBdcCompanyLinkmanDetailList())) {
                for (CustomerLinkmanImportDTO customerLinkmanImportDTO : customerImportDTO.getBdcCompanyLinkmanDetailList()) {
                    // 设置默认值
                    if (null == customerLinkmanImportDTO.getIsDefault()) {
                        customerLinkmanImportDTO.setIsDefault(CommonIfEnum.NO.getValue());
                    }

                    // 设置状态
                    if (null == customerLinkmanImportDTO.getStatus()) {
                        customerLinkmanImportDTO.setStatus(CommonStatusEnum.EFFECTIVE.getValue());
                    }
                }
            }

            // 处理地址
            if (CollectionUtils.isNotEmpty(customerImportDTO.getBdcCompanyShippingAddressDetailList())) {
                for (CustomerAddressImportDTO customerAddressImportDTO : customerImportDTO.getBdcCompanyShippingAddressDetailList()) {
                    // 设置默认值
                    if (null == customerAddressImportDTO.getIsDefault()) {
                        customerAddressImportDTO.setIsDefault(CommonIfEnum.NO.getValue());
                    }
                }
            }

            // 处理银行
            if (CollectionUtils.isNotEmpty(customerImportDTO.getBdcSupplierBankDetailList())) {
                for (CustomerBankImportDTO customerBankImportDTO : customerImportDTO.getBdcSupplierBankDetailList()) {
                    // 设置默认值
                    if (null == customerBankImportDTO.getIsDefault()) {
                        customerBankImportDTO.setIsDefault(CommonIfEnum.NO.getValue());
                    }

                    // 设置状态
                    if (null == customerBankImportDTO.getStatus()) {
                        customerBankImportDTO.setStatus(CommonStatusEnum.EFFECTIVE.getValue());
                    }
                }
            }

            // 处理开票
            if (CollectionUtils.isNotEmpty(customerImportDTO.getBdcCustomerInvoiceDetailList())) {
                for (CustomerInvoiceImportDTO customerInvoiceImportDTO : customerImportDTO.getBdcCustomerInvoiceDetailList()) {
                    // 设置默认值
                    if (null == customerInvoiceImportDTO.getIsDefault()) {
                        customerInvoiceImportDTO.setIsDefault(CommonIfEnum.NO.getValue());
                    }
                }
            }

            // 处理负责人
            if (CollectionUtils.isNotEmpty(customerImportDTO.getBdcCustomerSalesManDetailList())) {
                for (CustomerSalesManImportDTO customerSalesManImportDTO : customerImportDTO.getBdcCustomerSalesManDetailList()) {
                    // 设置员工编号
                    if (!employeeNameMap.containsKey(customerSalesManImportDTO.getSalesManName())) {
                        throw new BusinessException(customerSalesManImportDTO.getSalesManName() + "不存在");
                    }
                    List<EmployeeVo> maybeMultiEmployeeList = employeeNameMap.get(customerSalesManImportDTO.getSalesManName());

                    if (CollectionUtils.isNotEmpty(maybeMultiEmployeeList) && maybeMultiEmployeeList.size() > 1) {
                        throw new BusinessException(customerSalesManImportDTO.getSalesManName() + "对应多个员工");
                    }
                    customerSalesManImportDTO.setSalesManNo(maybeMultiEmployeeList.get(0).getEmployeeNo());
                }
            }


            //TODO shenbin 移除不该填的值
        }
    }

    public static final String UNIFIED_SOCIAL_CODE_SLASH = "/";

    private void validateImportCustomer(OperationModel operationModel, List<CustomerImportDTO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        List<String> customerCodeList = list.stream().map(CustomerImportDTO::getCustomerCode).collect(Collectors.toList());
        List<CustomerV2> customerV2s = customerV2DAO.selectList(Wrappers.<CustomerV2>lambdaQuery()
                .eq(CustomerV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .in(CustomerV2::getCustomerCode, customerCodeList)
                .eq(CustomerV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
        Map<String, CustomerV2> dbCustomerMap = customerV2s.stream().collect(Collectors.toMap(CustomerV2::getCustomerCode, Function.identity()));


        Map<String, List<CustomerCategoryV2>> categoryNameMap = new HashMap<>();
        List<String> customerCategoryNameList = list.stream().map(CustomerImportDTO::getCustomerCategoryName).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(customerCategoryNameList)) {
            List<CustomerCategoryV2> customerCategoryV2s = customerCategoryV2DAO.selectList(Wrappers.<CustomerCategoryV2>lambdaQuery()
                    .eq(CustomerCategoryV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                    .in(CustomerCategoryV2::getCategoryName, customerCategoryNameList)
                    .eq(CustomerCategoryV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
            categoryNameMap = customerCategoryV2s.stream().collect(Collectors.groupingBy(CustomerCategoryV2::getCategoryName));
        }


        Map<String, List<EmployeeVo>> employeeNameMap = new HashMap<>();
        List<String> orderManNameList = list.stream().filter(supp -> null != supp.getBdcCustomerSalesManDetailList()).flatMap(supp -> supp.getBdcCustomerSalesManDetailList().stream()).map(CustomerSalesManImportDTO::getSalesManName).collect(Collectors.toList());
        List<EmployeeVo> employeeByNames = employeeService.getEmployeeByNames(operationModel.getEnterpriseNo(), orderManNameList);
        if (CollectionUtils.isNotEmpty(employeeByNames)) {
            employeeNameMap = employeeByNames.stream().collect(Collectors.groupingBy(EmployeeVo::getUserName));
        }


        for (CustomerImportDTO customerImportDTO : list) {
            if (dbCustomerMap.containsKey(customerImportDTO.getCustomerCode())) {
                customerImportDTO.setExcelRowCheckResultEnumCode(ExcelRowCheckResultEnum.FAIL.getCheckCode());
                customerImportDTO.setExcelRowCheckResultEnumMessage(customerImportDTO.getCustomerCode() + "已存在");

                continue;
            }

            try {
                //必填项校验
                ValidatorUtils.checkEmptyThrowEx(customerImportDTO.getManageOrgNo(), "管理组织不能为空");
                ValidatorUtils.checkEmptyThrowEx(customerImportDTO.getCustomerCode(), "客户编码不能为空");
                ValidatorUtils.checkEmptyThrowEx(customerImportDTO.getCustomerName(), "客户名称不能为空");
                ValidatorUtils.checkEmptyThrowEx(customerImportDTO.getTransactionType(), "客户类型不能为空");
                ValidatorUtils.checkEmptyThrowEx(customerImportDTO.getCustomerCategoryName(), "客户分类不能为空");
                ValidatorUtils.checkEmptyThrowEx(customerImportDTO.getUnifiedSocialCode(), "统一社会信用代码不能为空");
                ValidatorUtils.checkEmptyThrowEx(customerImportDTO.getTaxCategory(), "纳税类别不能为空");

                //合法值校验
                //TODO shenbin 下拉合法值校验，因为可以随便填值
                if (!FactoryTypeEnum.abroad.getValue().equals(customerImportDTO.getFactoryType())) {
                    ValidatorUtils.checkTrueThrowEx(UNIFIED_SOCIAL_CODE_SLASH.equals(customerImportDTO.getUnifiedSocialCode()), "境外企业统一社会信用代码不能为\"/\"");
                }
                if (null != customerImportDTO.getIsAssociatedEnterprise() && CommonIfEnum.YES.getValue().equals(customerImportDTO.getIsAssociatedEnterprise())) {
                    ValidatorUtils.checkEmptyThrowEx(customerImportDTO.getAssociatedOrgNo(), "对应内部组织不能为空"); //TODO shenbin 兼容单组织
                }
                if (null != customerImportDTO.getIsMedicalInstitution() && CommonIfEnum.YES.getValue().equals(customerImportDTO.getIsMedicalInstitution())) {
                    ValidatorUtils.checkEmptyThrowEx(customerImportDTO.getInstitutionalType(), "医疗机构类型不能为空");

                    if (HospitalTypeEnum.yy.getType().equals(customerImportDTO.getInstitutionalType())) {
                        ValidatorUtils.checkEmptyThrowEx(customerImportDTO.getHospitalType(), "医院类型不能为空");
                        ValidatorUtils.checkEmptyThrowEx(customerImportDTO.getHospitalClass(), "医院等级不能为空");
                    }
                }


                //范围校验
                if (StringUtils.isNotEmpty(customerImportDTO.getCustomerCode())) {
                    ValidatorUtils.checkTrueThrowEx(customerImportDTO.getCustomerCode().length() > 50, "客户编码不能大于50");
                }
                if (StringUtils.isNotEmpty(customerImportDTO.getCustomerName())) {
                    ValidatorUtils.checkTrueThrowEx(customerImportDTO.getCustomerName().length() > 128, "客户名称不能大于128");
                }
                if (StringUtils.isNotEmpty(customerImportDTO.getCustomerNameEn())) {
                    ValidatorUtils.checkTrueThrowEx(customerImportDTO.getCustomerNameEn().length() > 300, "客户英文名称不能大于300");
                }
                if (StringUtils.isNotEmpty(customerImportDTO.getMnemonicCode())) {
                    ValidatorUtils.checkTrueThrowEx(customerImportDTO.getMnemonicCode().length() > 100, "助记码不能大于100");
                }
                if (StringUtils.isNotEmpty(customerImportDTO.getCompanyName())) {
                    ValidatorUtils.checkTrueThrowEx(customerImportDTO.getCompanyName().length() > 128, "企业名称不能大于128");
                }
                if (StringUtils.isNotEmpty(customerImportDTO.getUnifiedSocialCode())) {
                    ValidatorUtils.checkTrueThrowEx(customerImportDTO.getUnifiedSocialCode().length() > 100, "统一社会信用代码不能大于100");
                }
                if (StringUtils.isNotEmpty(customerImportDTO.getRemark())) {
                    ValidatorUtils.checkTrueThrowEx(customerImportDTO.getRemark().length() > 300, "备注不能大于300");
                }


                if (StringUtils.isNotEmpty(customerImportDTO.getReceiveAgreement())) {
                    ValidatorUtils.checkTrueThrowEx(customerImportDTO.getReceiveAgreement().length() > 500, "收款协议不能大于500");
                }
                if (StringUtils.isNotEmpty(customerImportDTO.getReceiveCondition())) {
                    ValidatorUtils.checkTrueThrowEx(customerImportDTO.getReceiveCondition().length() > 500, "收款条件不能大于500");
                }
                if (StringUtils.isNotEmpty(customerImportDTO.getCreditAmount())) {
                    ValidatorUtils.checkTrueThrowEx(customerImportDTO.getCreditAmount().length() > 20, "信用额度不能大于20");
                }
                if (StringUtils.isNotEmpty(customerImportDTO.getCreditDates())) {
                    ValidatorUtils.checkTrueThrowEx(customerImportDTO.getCreditDates().length() > 20, "信用期限不能大于20");
                }
                if (StringUtils.isNotEmpty(customerImportDTO.getOwnerCompany())) {
                    ValidatorUtils.checkTrueThrowEx(customerImportDTO.getOwnerCompany().length() > 100, "业务归属不能大于100");
                }
            } catch (Exception e) {
                customerImportDTO.setExcelRowCheckResultEnumCode(ExcelRowCheckResultEnum.FAIL.getCheckCode());
                customerImportDTO.setExcelRowCheckResultEnumMessage(e.getMessage());
            }

            //校验关联信息
            validateCustomerLinkmanList(operationModel, customerImportDTO.getBdcCompanyLinkmanDetailList());
            validateCustomerAddressList(operationModel, customerImportDTO.getBdcCompanyShippingAddressDetailList());
            validateCustomerBankList(operationModel, customerImportDTO.getBdcSupplierBankDetailList());
            validateCustomerInvoiceList(operationModel, customerImportDTO.getBdcCustomerInvoiceDetailList());
            validateCustomerSalesManList(operationModel, customerImportDTO.getBdcCustomerSalesManDetailList(), employeeNameMap);
        }
    }

    private void validateCustomerLinkmanList(OperationModel operationModel, List<CustomerLinkmanImportDTO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        for (CustomerLinkmanImportDTO customerLinkmanImportDTO : list) {
            try {
                //必填项校验
                ValidatorUtils.checkEmptyThrowEx(customerLinkmanImportDTO.getLinkman(), "联系人不能为空");

                //范围校验
                ValidatorUtils.checkTrueThrowEx(customerLinkmanImportDTO.getLinkman().length() > 50, "联系人不能大于50");

                if (StringUtils.isNotEmpty(customerLinkmanImportDTO.getPosition())) {
                    ValidatorUtils.checkTrueThrowEx(customerLinkmanImportDTO.getPosition().length() > 100, "职位不能大于100");
                }

                if (StringUtils.isNotEmpty(customerLinkmanImportDTO.getFixedPhone())) {
                    ValidatorUtils.checkTrueThrowEx(customerLinkmanImportDTO.getFixedPhone().length() > 100, "电话不能大于100");
                }

                if (StringUtils.isNotEmpty(customerLinkmanImportDTO.getMobilePhone())) {
                    ValidatorUtils.checkTrueThrowEx(customerLinkmanImportDTO.getMobilePhone().length() > 100, "手机不能大于100");
                }

                if (StringUtils.isNotEmpty(customerLinkmanImportDTO.getQq())) {
                    ValidatorUtils.checkTrueThrowEx(customerLinkmanImportDTO.getQq().length() > 100, "QQ不能大于100");
                }

                if (StringUtils.isNotEmpty(customerLinkmanImportDTO.getWx())) {
                    ValidatorUtils.checkTrueThrowEx(customerLinkmanImportDTO.getWx().length() > 100, "微信不能大于100");
                }

                if (StringUtils.isNotEmpty(customerLinkmanImportDTO.getEmail())) {
                    ValidatorUtils.checkTrueThrowEx(customerLinkmanImportDTO.getEmail().length() > 100, "邮箱不能大于100");
                }
            } catch (Exception e) {
                customerLinkmanImportDTO.setExcelRowCheckResultEnumCode(ExcelRowCheckResultEnum.FAIL.getCheckCode());
                customerLinkmanImportDTO.setExcelRowCheckResultEnumMessage(e.getMessage());
            }
        }
    }

    private void validateCustomerAddressList(OperationModel operationModel, List<CustomerAddressImportDTO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        for (CustomerAddressImportDTO customerAddressImportDTO : list) {
            try {
                //必填项校验
                ValidatorUtils.checkEmptyThrowEx(customerAddressImportDTO.getReceiveUser(), "联系人不能为空");
                ValidatorUtils.checkEmptyThrowEx(customerAddressImportDTO.getReceivePhone(), "联系电话不能为空");
                ValidatorUtils.checkEmptyThrowEx(customerAddressImportDTO.getAddressType(), "地址类型不能为空");
                ValidatorUtils.checkEmptyThrowEx(customerAddressImportDTO.getRegionCode(), "行政区域不能为空");
                ValidatorUtils.checkEmptyThrowEx(customerAddressImportDTO.getReceiveAddr(), "详细地址不能为空");

                //范围校验
                ValidatorUtils.checkTrueThrowEx(customerAddressImportDTO.getReceiveUser().length() > 50, "联系人不能大于50");
                ValidatorUtils.checkTrueThrowEx(customerAddressImportDTO.getReceivePhone().length() > 100, "联系电话不能大于100");
                ValidatorUtils.checkTrueThrowEx(customerAddressImportDTO.getReceiveAddr().length() > 300, "详细地址不能大于300");
                if (StringUtils.isNotEmpty(customerAddressImportDTO.getAddressDesc())) {
                    ValidatorUtils.checkTrueThrowEx(customerAddressImportDTO.getAddressDesc().length() > 100, "地址描述不能大于100");
                }
            } catch (Exception e) {
                customerAddressImportDTO.setExcelRowCheckResultEnumCode(ExcelRowCheckResultEnum.FAIL.getCheckCode());
                customerAddressImportDTO.setExcelRowCheckResultEnumMessage(e.getMessage());
            }
        }
    }

    private void validateCustomerBankList(OperationModel operationModel, List<CustomerBankImportDTO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        for (CustomerBankImportDTO customerBankImportDTO : list) {
            try {
                //必填项校验
                ValidatorUtils.checkEmptyThrowEx(customerBankImportDTO.getOpenBank(), "开户银行不能为空");
                ValidatorUtils.checkEmptyThrowEx(customerBankImportDTO.getAccountNo(), "银行账号不能为空");
                ValidatorUtils.checkEmptyThrowEx(customerBankImportDTO.getAccountName(), "账户名称不能为空");
                ValidatorUtils.checkEmptyThrowEx(customerBankImportDTO.getBankType(), "银行类别不能为空");
                ValidatorUtils.checkEmptyThrowEx(customerBankImportDTO.getCurrencyId(), "币种不能为空");
                ValidatorUtils.checkEmptyThrowEx(customerBankImportDTO.getAccountType(), "账户性质不能为空");

                //范围校验
                ValidatorUtils.checkTrueThrowEx(customerBankImportDTO.getOpenBank().length() > 100, "开户银行不能大于100");
                ValidatorUtils.checkTrueThrowEx(customerBankImportDTO.getAccountName().length() > 100, "账户名称不能大于100");
                ValidatorUtils.checkTrueThrowEx(customerBankImportDTO.getAccountNo().length() > 100, "银行账号不能大于100");
                if (StringUtils.isNotEmpty(customerBankImportDTO.getLinkPerson())) {
                    ValidatorUtils.checkTrueThrowEx(customerBankImportDTO.getLinkPerson().length() > 100, "联系人不能大于100");
                }
                if (StringUtils.isNotEmpty(customerBankImportDTO.getLinkPhone())) {
                    ValidatorUtils.checkTrueThrowEx(customerBankImportDTO.getLinkPhone().length() > 100, "联系电话不能大于100");
                }
            } catch (Exception e) {
                customerBankImportDTO.setExcelRowCheckResultEnumCode(ExcelRowCheckResultEnum.FAIL.getCheckCode());
                customerBankImportDTO.setExcelRowCheckResultEnumMessage(e.getMessage());
            }
        }
    }

    private static final Set<Integer> DedicatedInvoiceTypeList = new HashSet<Integer>() {
        {
            add(InvoiceTypeEnum.zzszyfq.getType());
            add(InvoiceTypeEnum.zzsdzzyfp.getType());
            add(InvoiceTypeEnum.qdzyfp.getType());
            add(InvoiceTypeEnum.All_ELECTRIC_PROFESSION.getType());
            add(InvoiceTypeEnum.All_ELECTRIC_PAPER_PROFESSION.getType());
        }
    };
    private void validateCustomerInvoiceList(OperationModel operationModel, List<CustomerInvoiceImportDTO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        for (CustomerInvoiceImportDTO customerInvoiceImportDTO : list) {
            try {
                //必填项校验
                ValidatorUtils.checkEmptyThrowEx(customerInvoiceImportDTO.getType(), "发票类型不能为空");
                ValidatorUtils.checkEmptyThrowEx(customerInvoiceImportDTO.getInvoiceTitle(), "开票名称不能为空");

                if (DedicatedInvoiceTypeList.contains(customerInvoiceImportDTO.getType())) {
                    ValidatorUtils.checkEmptyThrowEx(customerInvoiceImportDTO.getTaxNo(), "纳税人识别号不能为空");
                    ValidatorUtils.checkEmptyThrowEx(customerInvoiceImportDTO.getAddress(), "地址不能为空");
                    ValidatorUtils.checkEmptyThrowEx(customerInvoiceImportDTO.getPhone(), "电话不能为空");
                    ValidatorUtils.checkEmptyThrowEx(customerInvoiceImportDTO.getBankDeposit(), "开户行不能为空");
                    ValidatorUtils.checkEmptyThrowEx(customerInvoiceImportDTO.getBankAccount(), "开户账号不能为空");
                }

                //范围校验
                if (StringUtils.isNotEmpty(customerInvoiceImportDTO.getInvoiceTitle())) {
                    ValidatorUtils.checkTrueThrowEx(customerInvoiceImportDTO.getInvoiceTitle().length() > 128, "开票名称不能大于128");
                }
                if (StringUtils.isNotEmpty(customerInvoiceImportDTO.getTaxNo())) {
                    ValidatorUtils.checkTrueThrowEx(customerInvoiceImportDTO.getTaxNo().length() > 100, "纳税人识别号不能大于100");
                }
                if (StringUtils.isNotEmpty(customerInvoiceImportDTO.getAddress())) {
                    ValidatorUtils.checkTrueThrowEx(customerInvoiceImportDTO.getAddress().length() > 300, "地址不能大于300");
                }
                if (StringUtils.isNotEmpty(customerInvoiceImportDTO.getPhone())) {
                    ValidatorUtils.checkTrueThrowEx(customerInvoiceImportDTO.getPhone().length() > 100, "电话不能大于100");
                }
                if (StringUtils.isNotEmpty(customerInvoiceImportDTO.getBankDeposit())) {
                    ValidatorUtils.checkTrueThrowEx(customerInvoiceImportDTO.getBankDeposit().length() > 255, "开户行不能大于255");
                }
                if (StringUtils.isNotEmpty(customerInvoiceImportDTO.getBankAccount())) {
                    ValidatorUtils.checkTrueThrowEx(customerInvoiceImportDTO.getBankAccount().length() > 100, "开户账号不能大于100");
                }
                if (StringUtils.isNotEmpty(customerInvoiceImportDTO.getInvoicePhone())) {
                    ValidatorUtils.checkTrueThrowEx(customerInvoiceImportDTO.getInvoicePhone().length() > 100, "收票手机号不能大于100");
                }
                if (StringUtils.isNotEmpty(customerInvoiceImportDTO.getEmail())) {
                    ValidatorUtils.checkTrueThrowEx(customerInvoiceImportDTO.getEmail().length() > 100, "收票邮箱不能大于100");
                }
                if (StringUtils.isNotEmpty(customerInvoiceImportDTO.getRequirement())) {
                    ValidatorUtils.checkTrueThrowEx(customerInvoiceImportDTO.getRequirement().length() > 500, "客户开票要求不能大于500");
                }
            } catch (Exception e) {
                customerInvoiceImportDTO.setExcelRowCheckResultEnumCode(ExcelRowCheckResultEnum.FAIL.getCheckCode());
                customerInvoiceImportDTO.setExcelRowCheckResultEnumMessage(e.getMessage());
            }
        }
    }

    private void validateCustomerSalesManList(OperationModel operationModel, List<CustomerSalesManImportDTO> list, Map<String, List<EmployeeVo>> employeeNameMap) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        for (CustomerSalesManImportDTO customerSalesManImportDTO : list) {
            try {
                //必填项校验
                ValidatorUtils.checkEmptyThrowEx(customerSalesManImportDTO.getDeptNo(), "部门不能为空");
                ValidatorUtils.checkEmptyThrowEx(customerSalesManImportDTO.getSalesManName(), "员工不能为空");
                ValidatorUtils.checkEmptyThrowEx(customerSalesManImportDTO.getOrderSpecialist(), "订单专员不能为空");
                ValidatorUtils.checkEmptyThrowEx(customerSalesManImportDTO.getIsDefault(), "采购员不能为空");

                //范围校验
                if (StringUtils.isNotEmpty(customerSalesManImportDTO.getSalesManName())) {
                    ValidatorUtils.checkTrueThrowEx(customerSalesManImportDTO.getSalesManName().length() > 255, "员工姓名不能大于255");
                }
                if (StringUtils.isNotEmpty(customerSalesManImportDTO.getPost())) {
                    ValidatorUtils.checkTrueThrowEx(customerSalesManImportDTO.getPost().length() > 100, "职务不能大于100");
                }

                if (!employeeNameMap.containsKey(customerSalesManImportDTO.getSalesManName())) {
                    throw new BusinessException(customerSalesManImportDTO.getSalesManName() + "不存在");
                } else {
                    List<EmployeeVo> maybeMultiEmployeeList = employeeNameMap.get(customerSalesManImportDTO.getSalesManName());
                    if (CollectionUtils.isNotEmpty(maybeMultiEmployeeList) && maybeMultiEmployeeList.size() > 1) {
                        throw new BusinessException(customerSalesManImportDTO.getSalesManName() + "对应多个员工");
                    }
                }
            } catch (Exception e) {
                customerSalesManImportDTO.setExcelRowCheckResultEnumCode(ExcelRowCheckResultEnum.FAIL.getCheckCode());
                customerSalesManImportDTO.setExcelRowCheckResultEnumMessage(e.getMessage());
            }
        }
    }
}
