package com.yyigou.dsrp.cdc.service.v2.customer.impl;


import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yyigou.ddc.common.util.ValidatorUtils;
import com.yyigou.ddc.services.ddc.uim.vo.OrganizationTreeVo;
import com.yyigou.ddc.services.dlog.dto.IntegrationLogDTO;
import com.yyigou.ddc.services.dlog.enums.integration.IntegrationError;
import com.yyigou.ddc.services.dlog.enums.integration.IntegrationRequestType;
import com.yyigou.ddc.services.dlog.enums.integration.MdmBillType;
import com.yyigou.ddc.services.dlog.vo.IntegrationLogSaveVO;
import com.yyigou.ddc.services.dsrp.bdc.vo.PaymentAgreementVo;
import com.yyigou.dsrp.cdc.api.company.dto.BankDTO;
import com.yyigou.dsrp.cdc.api.company.dto.CompanyFileDTO;
import com.yyigou.dsrp.cdc.api.company.dto.CompanyLinkmanDTO;
import com.yyigou.dsrp.cdc.api.company.dto.CompanyShippingAddressDTO;
import com.yyigou.dsrp.cdc.api.customer.dto.CustomerExternalAssignItemDTO;
import com.yyigou.dsrp.cdc.api.customer.dto.CustomerExternalSaveDTO;
import com.yyigou.dsrp.cdc.api.customer.dto.CustomerInvoiceDTO;
import com.yyigou.dsrp.cdc.api.customer.vo.CustomerExternalSaveVO;
import com.yyigou.dsrp.cdc.api.customer.vo.CustomerInternalSaveVO;
import com.yyigou.dsrp.cdc.api.supply.dto.SupplierSalesManDTO;
import com.yyigou.dsrp.cdc.client.v2.customer.request.CustomerAssignDocExeRequest;
import com.yyigou.dsrp.cdc.client.v2.customer.request.CustomerAssignExeRequest;
import com.yyigou.dsrp.cdc.common.enums.*;
import com.yyigou.dsrp.cdc.common.enums.company.*;
import com.yyigou.dsrp.cdc.common.enums.customer.CustomerBusinessFlagEnum;
import com.yyigou.dsrp.cdc.common.enums.customer.CustomerControlStatusEnum;
import com.yyigou.dsrp.cdc.common.util.BeanUtil;
import com.yyigou.dsrp.cdc.common.util.CommonUtil;
import com.yyigou.dsrp.cdc.common.util.OperationModel;
import com.yyigou.dsrp.cdc.dao.common.entity.BankType;
import com.yyigou.dsrp.cdc.dao.v2.company.CompanyLinkmanV2DAO;
import com.yyigou.dsrp.cdc.dao.v2.company.CompanyShippingAddressV2DAO;
import com.yyigou.dsrp.cdc.dao.v2.company.entity.CompanyLinkmanV2;
import com.yyigou.dsrp.cdc.dao.v2.company.entity.CompanyShippingAddressV2;
import com.yyigou.dsrp.cdc.dao.v2.company.entity.CompanyV2;
import com.yyigou.dsrp.cdc.dao.v2.customer.*;
import com.yyigou.dsrp.cdc.dao.v2.customer.entity.*;
import com.yyigou.dsrp.cdc.manager.integration.cert.CertService;
import com.yyigou.dsrp.cdc.manager.integration.cert.req.CompanyCertBasicRequest;
import com.yyigou.dsrp.cdc.manager.integration.cert.req.CompanyCertFileRequest;
import com.yyigou.dsrp.cdc.manager.integration.cert.req.CompanyCertUpsertRequest;
import com.yyigou.dsrp.cdc.manager.integration.dict.DictEnterpriseService;
import com.yyigou.dsrp.cdc.manager.integration.numberCenter.NumberCenterService;
import com.yyigou.dsrp.cdc.manager.integration.org.UimTenantService;
import com.yyigou.dsrp.cdc.manager.integration.paymentAgreement.PaymentAgreementService;
import com.yyigou.dsrp.cdc.manager.integration.uim.CustomDocService;
import com.yyigou.dsrp.cdc.manager.integration.uim.response.CustomDocResponse;
import com.yyigou.dsrp.cdc.manager.integration.ulog.ULogService;
import com.yyigou.dsrp.cdc.model.constant.NumberCenterConstant;
import com.yyigou.dsrp.cdc.model.constant.SystemConstant;
import com.yyigou.dsrp.cdc.model.v2.company.req.CompanyBankReq;
import com.yyigou.dsrp.cdc.model.v2.company.req.CompanyLinkmanReq;
import com.yyigou.dsrp.cdc.model.v2.company.req.CompanySaveOrUpdateReq;
import com.yyigou.dsrp.cdc.model.v2.company.req.CompanyShippingAddressReq;
import com.yyigou.dsrp.cdc.model.v2.customer.req.*;
import com.yyigou.dsrp.cdc.service.v2.MdmGray;
import com.yyigou.dsrp.cdc.service.v2.company.CompanyV2ExternalService;
import com.yyigou.dsrp.cdc.service.v2.company.CompanyV2Service;
import com.yyigou.dsrp.cdc.service.v2.customer.CustomerCategoryV2Service;
import com.yyigou.dsrp.cdc.service.v2.customer.CustomerV2ExternalService;
import com.yyigou.dsrp.cdc.service.v2.customer.CustomerV2Service;
import com.yyigou.dsrp.cert.common.enums.CompanyCertSourceTypeEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service("customerV2ExternalService")
@RequiredArgsConstructor
@Slf4j
public class CustomerV2ExternalServiceImpl implements CustomerV2ExternalService {
    private final NumberCenterService numberCenterService;

    @Resource
    private CustomerV2DAO customerV2DAO;

    @Resource
    private CustomerBizDAO customerBizDAO;

    @Resource
    private CustomerBaseDAO customerBaseDAO;

    @Resource
    private CustomerCategoryBaseDAO customerCategoryBaseDAO;

    @Resource
    private CustomerCategoryV2Service customerCategoryV2Service;

    @Resource
    private CompanyV2ExternalService companyV2ExternalService;

    @Resource
    private CustomDocService customDocService;

    @Resource
    private DictEnterpriseService dictEnterpriseService;

    @Resource
    private CompanyLinkmanV2DAO companyLinkmanV2DAO;

    @Resource
    private CompanyShippingAddressV2DAO companyShippingAddressV2DAO;

    @Resource
    private CustomerSalesManV2DAO customerSalesManV2DAO;

    @Resource
    private CustomerInvoiceV2DAO customerInvoiceV2DAO;

    @Resource
    private ULogService uLogService;

    @Resource
    private UimTenantService uimTenantService;


    @Resource
    private MdmGray mdmGray;

    @Resource
    private CompanyV2Service companyV2Service;

    @Resource
    private CustomerV2Service customerV2Service;

    @Resource
    private CertService certService;

    @Resource
    private PaymentAgreementService paymentAgreementService;


    private void batchSaveValidateAndPreProcessForDA(OperationModel operationModelIn, List<CustomerExternalSaveDTO> paramsIn, Map<String, CustomerV2> localCustomerMapIn, List<CustomerInternalSaveVO> invalidatedListOut, Map<String, String> cooperationModeMapOut) {
        paramsIn.removeIf(customerExternalSaveDTO -> {
            //set default
            if (customerExternalSaveDTO.getIsMedicalInstitution() == null) {
                customerExternalSaveDTO.setIsMedicalInstitution(CommonIfEnum.NO.getValue());
            }
            //默认非关联企业
            if (customerExternalSaveDTO.getIsAssociatedEnterprise() == null) {
                customerExternalSaveDTO.setIsAssociatedEnterprise(CommonIfEnum.NO.getValue());
            }

            if (StringUtils.isEmpty(customerExternalSaveDTO.getCustomerCode())) {
                invalidatedListOut.add(new CustomerInternalSaveVO(customerExternalSaveDTO.getCustomerCode(), customerExternalSaveDTO.getCustomerName(), false,
                        "客户编码不能为空", operationModelIn, IntegrationError.PARAMS_EMPTY_ERR));
                return true;
            }

            // 没有启停状态
//            if (StringUtils.isEmpty(customerExternalSaveDTO.getControlStatus())) {
//                invalidatedListOut.add(new CustomerInternalSaveVO(customerExternalSaveDTO.getCustomerCode(), customerExternalSaveDTO.getCustomerName(), false,
//                        "客户生效状态不能为空", operationModelIn, IntegrationError.PARAMS_EMPTY_ERR));
//                return true;
//            }
//
//            EnableEnum controlStatusEnum = null;
//            try {
//                int controlStatus = Integer.parseInt(customerExternalSaveDTO.getControlStatus());
//                controlStatusEnum = EnableEnum.getByValue(controlStatus);
//                if (null == controlStatusEnum) {
//                    invalidatedListOut.add(new CustomerInternalSaveVO(customerExternalSaveDTO.getCustomerCode(), customerExternalSaveDTO.getCustomerName(), false,
//                            "客户状态不正确", operationModelIn, IntegrationError.PARAMS_EMPTY_ERR));
//                    return true;
//                }
//            } catch (NumberFormatException e) {
//                invalidatedListOut.add(new CustomerInternalSaveVO(customerExternalSaveDTO.getCustomerCode(), customerExternalSaveDTO.getCustomerName(), false,
//                        "客户状态不正确", operationModelIn, IntegrationError.PARAMS_EMPTY_ERR));
//                return true;
//            }
//
//
//            // 如果禁用的档案不存在则报错
//            if (controlStatusEnum == EnableEnum.DISABLE) {
//                if (null == localCustomerMapIn.get(customerExternalSaveDTO.getCustomerCode())) {
//                    invalidatedListOut.add(new CustomerInternalSaveVO(customerExternalSaveDTO.getCustomerCode(), customerExternalSaveDTO.getCustomerName(), false,
//                            "禁用的客户编码不存在", operationModelIn, IntegrationError.PARAMS_EMPTY_ERR));
//                    return true;
//                }
//            }

            if (StringUtils.isEmpty(customerExternalSaveDTO.getCustomerName())) {
                invalidatedListOut.add(new CustomerInternalSaveVO(customerExternalSaveDTO.getCustomerCode(), customerExternalSaveDTO.getCustomerName(), false, "客户名称不能为空", operationModelIn, IntegrationError.PARAMS_EMPTY_ERR));
                return true;
            }

            if (StringUtils.isEmpty(customerExternalSaveDTO.getUnifiedSocialCode())) {
                invalidatedListOut.add(new CustomerInternalSaveVO(customerExternalSaveDTO.getCustomerCode(), customerExternalSaveDTO.getCustomerName(), false, "统一社会信用代码不能为空", operationModelIn, IntegrationError.PARAMS_EMPTY_ERR));
                return true;
            }
            if (StringUtils.isEmpty(customerExternalSaveDTO.getCompanyName())) {
                invalidatedListOut.add(new CustomerInternalSaveVO(customerExternalSaveDTO.getCustomerCode(), customerExternalSaveDTO.getCustomerName(), false, "企业名称不能为空", operationModelIn, IntegrationError.PARAMS_EMPTY_ERR));
                return true;
            }
            if (StringUtils.isEmpty(customerExternalSaveDTO.getCustomerCategoryCode_1())) {
                invalidatedListOut.add(new CustomerInternalSaveVO(customerExternalSaveDTO.getCustomerCode(), customerExternalSaveDTO.getCustomerName(), false, "客户分类不能为空", operationModelIn, IntegrationError.PARAMS_EMPTY_ERR));
                return true;
            }

            if (null == customerExternalSaveDTO.getIsMedicalInstitution() ||
                    null == CommonIfEnum.getByValue(customerExternalSaveDTO.getIsMedicalInstitution())) {
                invalidatedListOut.add(new CustomerInternalSaveVO(customerExternalSaveDTO.getCustomerCode(), customerExternalSaveDTO.getCustomerName(), false, "是否医疗机构不合法", operationModelIn, IntegrationError.PARAMS_EMPTY_ERR));
                return true;
            }

            if (CommonIfEnum.YES.getValue().equals(customerExternalSaveDTO.getIsMedicalInstitution())) {
                if (StringUtils.isEmpty(customerExternalSaveDTO.getInstitutionalType())) {
                    invalidatedListOut.add(new CustomerInternalSaveVO(customerExternalSaveDTO.getCustomerCode(), customerExternalSaveDTO.getCustomerName(), false,
                            "医疗机构类型不能为空", operationModelIn, IntegrationError.PARAMS_EMPTY_ERR));
                    return true;
                } else if (null == InstitutionalTypeEnum.getByType(customerExternalSaveDTO.getInstitutionalType())) {
                    invalidatedListOut.add(new CustomerInternalSaveVO(customerExternalSaveDTO.getCustomerCode(), customerExternalSaveDTO.getCustomerName(), false,
                            "医疗机构类型不合法", operationModelIn, IntegrationError.PARAMS_INVALID_ERR));
                    return true;
                }

                if (InstitutionalTypeEnum.yy.getType().equals(customerExternalSaveDTO.getInstitutionalType())) {
                    if (StringUtils.isEmpty(customerExternalSaveDTO.getHospitalType())) {
                        invalidatedListOut.add(new CustomerInternalSaveVO(customerExternalSaveDTO.getCustomerCode(), customerExternalSaveDTO.getCustomerName(), false,
                                "医疗机构性质不能为空", operationModelIn, IntegrationError.PARAMS_EMPTY_ERR));
                        return true;
                    } else if (null == HospitalTypeEnum.getByType(customerExternalSaveDTO.getHospitalType())) {
                        invalidatedListOut.add(new CustomerInternalSaveVO(customerExternalSaveDTO.getCustomerCode(), customerExternalSaveDTO.getCustomerName(), false,
                                "医疗机构性质不合法", operationModelIn, IntegrationError.PARAMS_INVALID_ERR));
                        return true;
                    }
                    if (null == customerExternalSaveDTO.getHospitalClass()) {
                        invalidatedListOut.add(new CustomerInternalSaveVO(customerExternalSaveDTO.getCustomerCode(), customerExternalSaveDTO.getCustomerName(), false,
                                "医疗机构等级不能为空", operationModelIn, IntegrationError.PARAMS_EMPTY_ERR));
                        return true;
                    } else if (null == HospitalClassEnum.getByType(customerExternalSaveDTO.getHospitalClass())) {
                        invalidatedListOut.add(new CustomerInternalSaveVO(customerExternalSaveDTO.getCustomerCode(), customerExternalSaveDTO.getCustomerName(), false,
                                "医疗机构等级不合法", operationModelIn, IntegrationError.PARAMS_INVALID_ERR));
                        return true;
                    }
                }
            }

            if (CommonIfEnum.YES.getValue().equals(customerExternalSaveDTO.getIsAssociatedEnterprise())) {
                if (StringUtils.isEmpty(customerExternalSaveDTO.getAssociatedOrgCode())) {
                    invalidatedListOut.add(new CustomerInternalSaveVO(customerExternalSaveDTO.getCustomerCode(), customerExternalSaveDTO.getCustomerName(), false,
                            "关联企业编码不能为空", operationModelIn, IntegrationError.PARAMS_EMPTY_ERR));
                    return true;
                }
//                if (!organizationMap.containsKey(t.getAssociatedOrgCode())) {
//                    resultList.add(new CustomerExternalSaveVO(t.getCustomerCode(), false, "关联组织" + t.getAssociatedOrgCode() + "在DSRP中不存在"));
//                    return Boolean.TRUE;
//                }
            }

            // 没有合作性质
//            if (StringUtils.isNotBlank(customerExternalSaveDTO.getCooperationMode()) && StringUtils.isNotBlank(customerExternalSaveDTO.getCooperationModeName())) {
//                if (cooperationModeMapOut.containsKey(customerExternalSaveDTO.getCooperationMode())) {
//                    if (!cooperationModeMapOut.get(customerExternalSaveDTO.getCooperationMode()).equals(customerExternalSaveDTO.getCooperationModeName())) {
//                        invalidatedListOut.add(new CustomerInternalSaveVO(customerExternalSaveDTO.getCustomerCode(), customerExternalSaveDTO.getCustomerName(), false,
//                                "客户性质编码和名称不统一", operationModelIn, IntegrationError.PARAMS_INVALID_ERR));
//                        return true;
//                    }
//                } else {
//                    cooperationModeMapOut.put(customerExternalSaveDTO.getCooperationMode(), customerExternalSaveDTO.getCooperationModeName());
//                }
//            }

            if (!org.springframework.util.CollectionUtils.isEmpty(customerExternalSaveDTO.getInvoiceList())) {
                if (customerExternalSaveDTO.getInvoiceList().stream().anyMatch(invoice -> StringUtils.isEmpty(invoice.getInvoiceCode()))) {
                    invalidatedListOut.add(new CustomerInternalSaveVO(customerExternalSaveDTO.getCustomerCode(), customerExternalSaveDTO.getCustomerName(),
                            false, "发票编码不能为空", operationModelIn, IntegrationError.PARAMS_EMPTY_ERR));
                    return true;
                } else {
                    final Map<String, List<CustomerInvoiceDTO>> invoiceGroup = customerExternalSaveDTO.getInvoiceList().stream().collect(Collectors.groupingBy(CustomerInvoiceDTO::getInvoiceCode));
                    for (String invoiceCode : invoiceGroup.keySet()) {
                        if (invoiceGroup.get(invoiceCode).size() > 1) {
                            invalidatedListOut.add(new CustomerInternalSaveVO(customerExternalSaveDTO.getCustomerCode(), customerExternalSaveDTO.getCustomerName(),
                                    false, "发票编码" + invoiceCode + "不能重复", operationModelIn, IntegrationError.PARAMS_INVALID_ERR));
                            return true;
                        }
                    }
                }
            }
            if (!org.springframework.util.CollectionUtils.isEmpty(customerExternalSaveDTO.getLinkmanList())) {
                if (customerExternalSaveDTO.getLinkmanList().stream().anyMatch(linkCode -> StringUtils.isEmpty(linkCode.getLinkCode()))) {
                    invalidatedListOut.add(new CustomerInternalSaveVO(customerExternalSaveDTO.getCustomerCode(), customerExternalSaveDTO.getCustomerName(),
                            false, "联系人编码不能为空", operationModelIn, IntegrationError.PARAMS_EMPTY_ERR));
                    return true;
                } else {
                    final Map<String, List<CompanyLinkmanDTO>> invoiceGroup = customerExternalSaveDTO.getLinkmanList().stream().collect(Collectors.groupingBy(CompanyLinkmanDTO::getLinkCode));
                    for (String linkCode : invoiceGroup.keySet()) {
                        if (invoiceGroup.get(linkCode).size() > 1) {
                            invalidatedListOut.add(new CustomerInternalSaveVO(customerExternalSaveDTO.getCustomerCode(), customerExternalSaveDTO.getCustomerName(),
                                    false, "发票编码" + linkCode + "不能重复", operationModelIn, IntegrationError.PARAMS_INVALID_ERR));
                            return true;
                        }
                    }
                }
            }

            return false;
        });
    }

    private void batchSaveBusinessLogForDA(List<CustomerInternalSaveVO> resultList) {
        if (!org.springframework.util.CollectionUtils.isEmpty(resultList)) {
            List<CustomerInternalSaveVO> successList = new ArrayList<>();
            List<CustomerInternalSaveVO> failList = new ArrayList<>();
            for (CustomerInternalSaveVO customerInternalSaveVO : resultList) {
                if (customerInternalSaveVO.getSuccess() != null && !customerInternalSaveVO.getSuccess()
                        && !StringUtils.isEmpty(customerInternalSaveVO.getCustomerCode())) {
                    failList.add(customerInternalSaveVO);
                } else if (customerInternalSaveVO.getSuccess() != null && customerInternalSaveVO.getSuccess()) {
                    successList.add(customerInternalSaveVO);
                }
            }

            if (!org.springframework.util.CollectionUtils.isEmpty(successList)) {
                List<IntegrationLogDTO> successLogDTOList = new ArrayList<>();
                for (CustomerInternalSaveVO customerInternalSaveVO : successList) {
                    IntegrationLogDTO integrationLogDTO = new IntegrationLogDTO();
                    // 上游请求id，open推进来的不用管
//                integrationLogDTO.setRequestId(UUID.randomUUID().toString());
                    integrationLogDTO.setRequestType(IntegrationRequestType.OPEN.getRequestType());
                    integrationLogDTO.setSuccess(true); // 是否成功
                    integrationLogDTO.setBusinessBillType(MdmBillType.ZSGYS.name());

                    integrationLogDTO.setBusinessBillNo(customerInternalSaveVO.getCustomerCode());
                    integrationLogDTO.setBusinessBillName(customerInternalSaveVO.getCustomerName());
                    OperationModel operationModel = customerInternalSaveVO.getOperationModel();
                    integrationLogDTO.setEnterpriseNo(operationModel.getEnterpriseNo());
                    integrationLogDTO.setSubjectSystem(MdmBillType.systemCode); // 对接系统

                    integrationLogDTO.setBusinessOrgNo(operationModel.getOrgNo());
                    integrationLogDTO.setBusinessOrgName(operationModel.getOrgName());
                    // 交易对象
//                integrationLogDTO.setTradeObject(t.getCustomerName());
                    successLogDTOList.add(integrationLogDTO);
                }
                log.warn("batchSaveLogReq={}", successLogDTOList);
                List<IntegrationLogSaveVO> integrationLogSaveVOS = uLogService.batchSaveLog(successLogDTOList);
                log.warn("batchSaveLogResp={}", integrationLogSaveVOS);
            }
            if (!org.springframework.util.CollectionUtils.isEmpty(failList)) {
                List<IntegrationLogDTO> errorList = new ArrayList<>();
                for (CustomerInternalSaveVO customerInternalSaveVO : failList) {
                    IntegrationLogDTO integrationLogDTO = new IntegrationLogDTO();
                    // 上游请求id，open推进来的不用管
//                integrationLogDTO.setRequestId(UUID.randomUUID().toString());
                    integrationLogDTO.setRequestType(IntegrationRequestType.OPEN.getRequestType());
                    integrationLogDTO.setSuccess(false); // 是否成功
                    integrationLogDTO.setErrorCode(customerInternalSaveVO.getIntegrationError().getErrorCode()); // 错误编码
                    integrationLogDTO.setErrorMsg(customerInternalSaveVO.getMessage()); // 错误信息
                    integrationLogDTO.setBusinessBillType(MdmBillType.ZSGYS.name());

                    integrationLogDTO.setBusinessBillNo(customerInternalSaveVO.getCustomerCode());
                    integrationLogDTO.setBusinessBillName(customerInternalSaveVO.getCustomerName());

                    OperationModel operationModel = customerInternalSaveVO.getOperationModel();
                    integrationLogDTO.setEnterpriseNo(operationModel.getEnterpriseNo());
                    integrationLogDTO.setSubjectSystem(MdmBillType.systemCode); // 对接系统

                    integrationLogDTO.setBusinessOrgNo(operationModel.getOrgNo());
                    integrationLogDTO.setBusinessOrgName(operationModel.getOrgName());
                    // 交易对象
//                integrationLogDTO.setTradeObject(t.getCustomerName());
                    errorList.add(integrationLogDTO);
                }
                log.warn("batchSaveLogReq={}", errorList);
                List<IntegrationLogSaveVO> integrationLogSaveVOS = uLogService.batchSaveLog(errorList);
                log.warn("batchSaveLogResp={}", integrationLogSaveVOS);
            }
        }
    }

    private List<CustomerExternalSaveVO> convert2ExternalVoForDA(List<CustomerInternalSaveVO> invalidatedList, List<CustomerInternalSaveVO> handledList) {
        if (CollectionUtils.isEmpty(invalidatedList) && CollectionUtils.isEmpty(handledList)) {
            return Collections.emptyList();
        }

        List<CustomerExternalSaveVO> retResultList = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(invalidatedList)) {
            for (CustomerInternalSaveVO customerInternalSaveVO : invalidatedList) {
                CustomerExternalSaveVO customerExternalSaveVO = new CustomerExternalSaveVO();
                BeanUtils.copyProperties(customerInternalSaveVO, customerExternalSaveVO);
                retResultList.add(customerExternalSaveVO);
            }
        }

        if (CollectionUtils.isNotEmpty(handledList)) {
            for (CustomerInternalSaveVO customerInternalSaveVO : handledList) {
                CustomerExternalSaveVO customerExternalSaveVO = new CustomerExternalSaveVO();
                BeanUtils.copyProperties(customerInternalSaveVO, customerExternalSaveVO);
                retResultList.add(customerExternalSaveVO);
            }
        }

        return retResultList;
    }

    private Map<String, List<?>> cudLinkmanPerCustomerForDA(OperationModel operationModel, List<CompanyLinkmanV2> oldList, List<CompanyLinkmanDTO> newList, String useOrgNo, CustomerV2 futureCustomer) {
        Map<String, List<?>> diffResultMap = BeanUtil.diffCUD(oldList, newList);

        List<CompanyLinkmanDTO> addList = (List<CompanyLinkmanDTO>) diffResultMap.get("addList");
        List<CompanyLinkmanDTO> updateList = (List<CompanyLinkmanDTO>) diffResultMap.get("updateList");
        List<CompanyLinkmanV2> deleteList = (List<CompanyLinkmanV2>) diffResultMap.get("deleteList");

        if (CollectionUtils.isNotEmpty(addList)) {
            List<CompanyLinkmanV2> addLinkmanList = new ArrayList<>();

            for (CompanyLinkmanDTO companyLinkmanReq : addList) {
                CompanyLinkmanV2 companyLinkman = new CompanyLinkmanV2();
                companyLinkman.setEnterpriseNo(operationModel.getEnterpriseNo());
                companyLinkman.setUseOrgNo(useOrgNo);
                companyLinkman.setCompanyNo(futureCustomer.getCompanyNo());
                companyLinkman.setLinkCode(companyLinkmanReq.getLinkCode());
                companyLinkman.setLinkman(companyLinkmanReq.getLinkman());
                companyLinkman.setPosition(companyLinkmanReq.getPosition());
                companyLinkman.setMobilePhone(companyLinkmanReq.getMobilePhone());
                companyLinkman.setSex(companyLinkmanReq.getSex());
                companyLinkman.setFixedPhone(companyLinkmanReq.getFixedPhone());
                companyLinkman.setQq(companyLinkmanReq.getQq());
                companyLinkman.setWx(companyLinkmanReq.getWx());
                companyLinkman.setLinkmanType(LinkmanTypeEnum.SALE.getValue());
                companyLinkman.setStatus(CommonStatusEnum.EFFECTIVE.getValue());
                companyLinkman.setSourceNo(futureCustomer.getCustomerCode());
                companyLinkman.setEmail(companyLinkmanReq.getEmail());
                companyLinkman.setIsDefault(companyLinkmanReq.getIsDefault());
                companyLinkman.setStatus(companyLinkmanReq.getStatus());
                CommonUtil.fillCreatInfo(operationModel, companyLinkman);

                addLinkmanList.add(companyLinkman);
            }
            companyLinkmanV2DAO.addBatch(addLinkmanList);
        }

        if (CollectionUtils.isNotEmpty(updateList)) {
            List<CompanyLinkmanV2> updateBankList = new ArrayList<>();
            for (CompanyLinkmanDTO companyLinkmanReq : updateList) {
                CompanyLinkmanV2 companyLinkman = new CompanyLinkmanV2();
                companyLinkman.setId(companyLinkmanReq.getId());
                companyLinkman.setEnterpriseNo(operationModel.getEnterpriseNo());
                companyLinkman.setUseOrgNo(useOrgNo);
                companyLinkman.setCompanyNo(futureCustomer.getCompanyNo());
                companyLinkman.setLinkCode(companyLinkmanReq.getLinkCode());
                companyLinkman.setLinkman(companyLinkmanReq.getLinkman());
                companyLinkman.setPosition(companyLinkmanReq.getPosition());
                companyLinkman.setMobilePhone(companyLinkmanReq.getMobilePhone());
                companyLinkman.setSex(companyLinkmanReq.getSex());
                companyLinkman.setFixedPhone(companyLinkmanReq.getFixedPhone());
                companyLinkman.setQq(companyLinkmanReq.getQq());
                companyLinkman.setWx(companyLinkmanReq.getWx());
                companyLinkman.setLinkmanType(LinkmanTypeEnum.SALE.getValue());
                companyLinkman.setStatus(CommonStatusEnum.EFFECTIVE.getValue());
                companyLinkman.setSourceNo(futureCustomer.getCustomerCode());
                companyLinkman.setEmail(companyLinkmanReq.getEmail());
                companyLinkman.setIsDefault(companyLinkmanReq.getIsDefault());
                companyLinkman.setStatus(companyLinkmanReq.getStatus());
                CommonUtil.fillOperateInfo(operationModel, companyLinkman);

                updateBankList.add(companyLinkman);
            }
            companyLinkmanV2DAO.updateByIdBatch(updateBankList);
        }

        if (CollectionUtils.isNotEmpty(deleteList)) {
            CompanyLinkmanV2 companyLinkmanV2 = new CompanyLinkmanV2();
            companyLinkmanV2.setDeleted(DeletedEnum.DELETED.getValue());
            CommonUtil.fillOperateInfo(operationModel, companyLinkmanV2);

            companyLinkmanV2DAO.update(companyLinkmanV2, Wrappers.<CompanyLinkmanV2>lambdaQuery()
                    .eq(CompanyLinkmanV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                    .in(CompanyLinkmanV2::getId, deleteList.stream().map(CompanyLinkmanV2::getId).collect(Collectors.toList())));
        }

        return diffResultMap;
    }

    private Map<String, List<?>> cudAddressPerCustomerForDA(OperationModel operationModel, List<CompanyShippingAddressV2> oldList, List<CompanyShippingAddressDTO> newList, String useOrgNo, CustomerV2 futureCustomer) {
        Map<String, List<?>> diffResultMap = BeanUtil.diffCUD(oldList, newList);

        List<CompanyShippingAddressDTO> addList = (List<CompanyShippingAddressDTO>) diffResultMap.get("addList");
        List<CompanyShippingAddressDTO> updateList = (List<CompanyShippingAddressDTO>) diffResultMap.get("updateList");
        List<CompanyShippingAddressV2> deleteList = (List<CompanyShippingAddressV2>) diffResultMap.get("deleteList");

        if (CollectionUtils.isNotEmpty(addList)) {
            List<CompanyShippingAddressV2> addCompanyShippingAddressList = new ArrayList<>();
            for (CompanyShippingAddressDTO companyLinkmanReq : addList) {
                CompanyShippingAddressV2 shippingAddress = new CompanyShippingAddressV2();
                shippingAddress.setEnterpriseNo(operationModel.getEnterpriseNo());
                shippingAddress.setUseOrgNo(useOrgNo);
                shippingAddress.setCompanyNo(futureCustomer.getCompanyNo());
                shippingAddress.setLinkAddressCode(companyLinkmanReq.getLinkAddressCode());
                shippingAddress.setSourceNo(futureCustomer.getCustomerCode());
                shippingAddress.setStatus(CommonStatusEnum.EFFECTIVE.getValue());
                shippingAddress.setLinkaddType(LinkmanTypeEnum.SALE.getValue());
                shippingAddress.setReceiveUser(companyLinkmanReq.getReceiveUser());
                shippingAddress.setReceivePhone(companyLinkmanReq.getReceivePhone());
                shippingAddress.setRegionCode(companyLinkmanReq.getRegionCode());
                shippingAddress.setRegionName(companyLinkmanReq.getRegionName());
                shippingAddress.setReceiveAddr(companyLinkmanReq.getReceiveAddr());
                shippingAddress.setIsDefault(companyLinkmanReq.getIsDefault());
                shippingAddress.setAddressDesc(companyLinkmanReq.getAddressDesc());
                shippingAddress.setAddressType(companyLinkmanReq.getAddressType());
                CommonUtil.fillCreatInfo(operationModel, shippingAddress);

                addCompanyShippingAddressList.add(shippingAddress);
            }
            companyShippingAddressV2DAO.addBatch(addCompanyShippingAddressList);
        }

        if (CollectionUtils.isNotEmpty(updateList)) {
            List<CompanyShippingAddressV2> addCompanyShippingAddressList = new ArrayList<>();
            for (CompanyShippingAddressDTO companyLinkmanReq : updateList) {
                CompanyShippingAddressV2 shippingAddress = new CompanyShippingAddressV2();
                shippingAddress.setId(companyLinkmanReq.getId());
                shippingAddress.setEnterpriseNo(operationModel.getEnterpriseNo());
                shippingAddress.setUseOrgNo(useOrgNo);
                shippingAddress.setCompanyNo(futureCustomer.getCompanyNo());
                shippingAddress.setLinkAddressCode(companyLinkmanReq.getLinkAddressCode());
                shippingAddress.setSourceNo(futureCustomer.getCustomerCode());
                shippingAddress.setStatus(CommonStatusEnum.EFFECTIVE.getValue());
                shippingAddress.setLinkaddType(LinkmanTypeEnum.SALE.getValue());
                shippingAddress.setReceiveUser(companyLinkmanReq.getReceiveUser());
                shippingAddress.setReceivePhone(companyLinkmanReq.getReceivePhone());
                shippingAddress.setRegionCode(companyLinkmanReq.getRegionCode());
                shippingAddress.setRegionName(companyLinkmanReq.getRegionName());
                shippingAddress.setReceiveAddr(companyLinkmanReq.getReceiveAddr());
                shippingAddress.setIsDefault(companyLinkmanReq.getIsDefault());
                shippingAddress.setAddressDesc(companyLinkmanReq.getAddressDesc());
                shippingAddress.setAddressType(companyLinkmanReq.getAddressType());
                CommonUtil.fillOperateInfo(operationModel, shippingAddress);

                addCompanyShippingAddressList.add(shippingAddress);
            }
            companyShippingAddressV2DAO.updateByIdBatch(addCompanyShippingAddressList);
        }

        if (CollectionUtils.isNotEmpty(deleteList)) {
            CompanyShippingAddressV2 companyShippingAddressV2 = new CompanyShippingAddressV2();
            companyShippingAddressV2.setDeleted(DeletedEnum.DELETED.getValue());
            CommonUtil.fillOperateInfo(operationModel, companyShippingAddressV2);

            companyShippingAddressV2DAO.update(companyShippingAddressV2, Wrappers.<CompanyShippingAddressV2>lambdaQuery()
                    .eq(CompanyShippingAddressV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                    .in(CompanyShippingAddressV2::getId, deleteList.stream().map(CompanyShippingAddressV2::getId).collect(Collectors.toList())));
        }

        return diffResultMap;
    }

    private CustomerV2 packCustomerV2(CustomerV2 oldCustomer, CustomerV2 newCustomer) {
        CustomerV2 customer = new CustomerV2();
        BeanUtils.copyProperties(oldCustomer, customer);

        customer.setCustomerName(newCustomer.getCustomerName());
        customer.setMnemonicCode(newCustomer.getMnemonicCode());
        customer.setCustomerNameEn(newCustomer.getCustomerNameEn());
        customer.setRemark(newCustomer.getRemark());
        customer.setCustomerCategoryNo(newCustomer.getCustomerCategoryNo());

        return customer;
    }

    private CustomerBiz packCustomerBiz(CustomerBiz oldCustomerBiz, CustomerBiz newCustomerBiz) {
        CustomerBiz customerBiz = new CustomerBiz();
        BeanUtils.copyProperties(oldCustomerBiz, customerBiz);

        //业务归属
        customerBiz.setOwnerCompany(newCustomerBiz.getOwnerCompany());
        //信用天数额度
        customerBiz.setCreditAmount(newCustomerBiz.getCreditAmount());
//                customerBiz.setCreditDates(null != t.getPeriodDays() ? t.getPeriodDays().toString() : null);
        //合作起止日期
        customerBiz.setCoopStartTime(newCustomerBiz.getCoopStartTime());
        customerBiz.setCoopEndTime(newCustomerBiz.getCoopEndTime());
        // 客户性质（合作性质）
        customerBiz.setCooperationMode(newCustomerBiz.getCooperationMode());
        // 启停状态
//                customerBiz.setControlStatus(t.getControlStatus());

        return customerBiz;
    }

    private List<CustomerInternalSaveVO> saveOrUpdateCustomerV2ForDA(OperationModel operationModel, Map<String, CompanyV2> companyNameMap, Map<String, CustomerV2> localCustomerMap, List<CustomerExternalSaveDTO> newList) {
        String manageOrgNo = operationModel.getOrgNo();

        List<CustomerInternalSaveVO> resultList = new ArrayList<>(newList.size());

        Map<String, CustomerBiz> customerCode2BizMap = null;
        Map<String, List<CustomerSalesManV2>> salesManMap = null;
        Map<String, List<CompanyLinkmanV2>> linkManMap = null;
        Map<String, List<CompanyShippingAddressV2>> addressMap = null;
        Map<String, List<CustomerInvoiceV2>> invoiceMap = null;
        if (MapUtils.isEmpty(localCustomerMap)) {
            customerCode2BizMap = new HashMap<>();
            salesManMap = new HashMap<>();
            linkManMap = new HashMap<>();
            addressMap = new HashMap<>();
            invoiceMap = new HashMap<>();
        } else {
            Set<String> customerCodeSet = localCustomerMap.keySet();

            // 查询出所有档案的业务信息
            List<CustomerBiz> customerBizList = customerBizDAO.selectList(Wrappers.<CustomerBiz>lambdaQuery()
                    .eq(CustomerBiz::getEnterpriseNo, operationModel.getEnterpriseNo())
                    .eq(CustomerBiz::getUseOrgNo, manageOrgNo)
                    .in(CustomerBiz::getCustomerCode, customerCodeSet)
                    .eq(CustomerBiz::getDeleted, DeletedEnum.UN_DELETE.getValue()));
            customerCode2BizMap = customerBizList.stream().collect(Collectors.toMap(CustomerBiz::getCustomerCode, Function.identity()));


            // 批量查询出所有的负责人、联系人、联系地址
            List<CustomerSalesManV2> customerSalesManV2s = customerSalesManV2DAO.selectList(Wrappers.<CustomerSalesManV2>lambdaQuery()
                    .eq(CustomerSalesManV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                    .eq(CustomerSalesManV2::getUseOrgNo, manageOrgNo)
                    .in(CustomerSalesManV2::getCustomerCode, customerCodeSet)
                    .eq(CustomerSalesManV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
            salesManMap = customerSalesManV2s.stream().collect(Collectors.groupingBy(CustomerSalesManV2::getCustomerCode));

            List<CompanyLinkmanV2> customerLinkManV2s = companyLinkmanV2DAO.selectList(Wrappers.<CompanyLinkmanV2>lambdaQuery()
                    .eq(CompanyLinkmanV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                    .eq(CompanyLinkmanV2::getUseOrgNo, manageOrgNo)
                    .in(CompanyLinkmanV2::getSourceNo, customerCodeSet)
                    .eq(CompanyLinkmanV2::getLinkmanType, LinkmanTypeEnum.SALE.getValue())
                    .eq(CompanyLinkmanV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
            linkManMap = customerLinkManV2s.stream().collect(Collectors.groupingBy(CompanyLinkmanV2::getSourceNo));

            List<CompanyShippingAddressV2> customerAddressV2s = companyShippingAddressV2DAO.selectList(Wrappers.<CompanyShippingAddressV2>lambdaQuery()
                    .eq(CompanyShippingAddressV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                    .eq(CompanyShippingAddressV2::getUseOrgNo, manageOrgNo)
                    .in(CompanyShippingAddressV2::getSourceNo, customerCodeSet)
                    .eq(CompanyShippingAddressV2::getLinkaddType, LinkmanTypeEnum.SALE.getValue())
                    .eq(CompanyShippingAddressV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
            addressMap = customerAddressV2s.stream().collect(Collectors.groupingBy(CompanyShippingAddressV2::getSourceNo));

            List<CustomerInvoiceV2> customerInvoiceV2s = customerInvoiceV2DAO.selectList(Wrappers.<CustomerInvoiceV2>lambdaQuery()
                    .eq(CustomerInvoiceV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                    .eq(CustomerInvoiceV2::getUseOrgNo, manageOrgNo)
                    .in(CustomerInvoiceV2::getCustomerCode, customerCodeSet)
                    .eq(CustomerInvoiceV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
            invoiceMap = customerInvoiceV2s.stream().collect(Collectors.groupingBy(CustomerInvoiceV2::getCustomerCode));
        }

        List<CustomerV2> saveManageCustomerV2List = new ArrayList<>();
        List<CustomerV2> updateManageCustomerV2List = new ArrayList<>();
        List<CustomerBiz> saveManageCustomerBizList = new ArrayList<>();
        List<CustomerBiz> updateManageCustomerBizList = new ArrayList<>();
        List<CustomerBase> saveManageCustomerBaseList = new ArrayList<>();

        List<Map<String, Object>> futureCustomerBaseAndBizList = new ArrayList<>();

        final List<OrganizationTreeVo> organizationTreeList = uimTenantService.getOrganizationTreeList(operationModel.getEnterpriseNo());
        final Map<String, OrganizationTreeVo> organizationMap = organizationTreeList.stream().collect(Collectors.toMap(OrganizationTreeVo::getOrgCode, Function.identity(), (k, v) -> k));

        for (CustomerExternalSaveDTO t : newList) {
            CustomerV2 futureCustomer = null;
            CustomerBiz futureCustomerBiz = null;
            //修改
            if (localCustomerMap.containsKey(t.getCustomerCode())) {
                CustomerV2 customer = localCustomerMap.get(t.getCustomerCode());

                CustomerV2 newCustomer = new CustomerV2();
                newCustomer.setCustomerName(t.getCustomerName());
                newCustomer.setMnemonicCode(t.getMnemonicCode());
                newCustomer.setCustomerNameEn(t.getCustomerNameEn());
                newCustomer.setRemark(t.getRemark());
                newCustomer.setCustomerCategoryNo(t.getCustomerCategoryNo());

                newCustomer.setIsAssociatedEnterprise(t.getIsAssociatedEnterprise());
                newCustomer.setAssociatedOrgCode(t.getAssociatedOrgCode());
                if (CommonIfEnum.YES.getValue().equals(t.getIsAssociatedEnterprise())) {
                    final OrganizationTreeVo organizationTreeVo = organizationMap.get(t.getAssociatedOrgCode());
                    if (organizationTreeVo != null) {
                        newCustomer.setAssociatedOrgNo(organizationTreeVo.getOrgNo());
                        newCustomer.setAssociatedOrgName(organizationTreeVo.getOrgName());
                    }
                }

                CommonUtil.fillOperateInfo(operationModel, newCustomer);

                // where条件
                newCustomer.setEnterpriseNo(operationModel.getEnterpriseNo());
                newCustomer.setManageOrgNo(customer.getManageOrgNo());
                newCustomer.setCustomerNo(customer.getCustomerNo());

//                customerV2DAO.update(newCustomer, Wrappers.<CustomerV2>lambdaUpdate()
//                        .eq(CustomerV2::getEnterpriseNo, operationModel.getEnterpriseNo())
//                        .eq(CustomerV2::getManageOrgNo, customer.getManageOrgNo())
//                        .eq(CustomerV2::getCustomerCode, customer.getCustomerCode())
//                        .eq(CustomerV2::getDeleted, DeletedEnum.UN_DELETE.getValue())
//                );
                updateManageCustomerV2List.add(newCustomer);

                futureCustomer = packCustomerV2(customer, newCustomer);

                CustomerBiz customerBiz = customerCode2BizMap.get(customer.getCustomerCode());

                CustomerBiz newCustomerBiz = new CustomerBiz();
                //业务归属
                newCustomerBiz.setOwnerCompany(t.getOwnerCompany());
                //信用天数额度
                newCustomerBiz.setCreditAmount(t.getCreditAmount());
//                newCustomerBiz.setCreditDates(null != t.getPeriodDays() ? t.getPeriodDays().toString() : null);
                //合作起止日期
                newCustomerBiz.setCoopStartTime(t.getCoopStartTime());
                newCustomerBiz.setCoopEndTime(t.getCoopEndTime());
                // 客户性质（合作性质）
                newCustomerBiz.setCooperationMode(t.getCooperationMode());
                // 启停状态
//                newCustomerBiz.setControlStatus(t.getControlStatus());
                CommonUtil.fillOperateInfo(operationModel, newCustomerBiz);

                // where条件
                newCustomerBiz.setId(customerBiz.getId());
                newCustomerBiz.setEnterpriseNo(operationModel.getEnterpriseNo());
                newCustomerBiz.setCustomerCode(customer.getCustomerCode());
                newCustomerBiz.setUseOrgNo(customer.getManageOrgNo());

                updateManageCustomerBizList.add(newCustomerBiz);

                futureCustomerBiz = packCustomerBiz(customerBiz, newCustomerBiz);

//                customerBizDAO.update(newCustomerBiz, Wrappers.<CustomerBiz>lambdaUpdate()
//                        .eq(CustomerBiz::getId, customerBiz.getId())
//                );

                resultList.add(new CustomerInternalSaveVO(t.getCustomerCode(), t.getCustomerName(), true, "客户档案更新成功", operationModel));
            } else {
                //新增 。新增的客户档案只能是生效状态，组织的档案状态后续单独处理
                // 新增管理组织的基本信息
                CustomerV2 customer = new CustomerV2();
                customer.setEnterpriseNo(operationModel.getEnterpriseNo());
                customer.setManageOrgNo(manageOrgNo);
                customer.setCustomerNo(numberCenterService.createNumber(NumberCenterConstant.CUSTOMER_NO_KEY));
                customer.setCompanyNo(companyNameMap.get(t.getCompanyName()).getCompanyNo());
                customer.setUnifiedSocialCode(t.getUnifiedSocialCode());
                customer.setCustomerCode(t.getCustomerCode());
                customer.setCustomerName(t.getCustomerName());
                customer.setMnemonicCode(t.getMnemonicCode());
                customer.setCustomerNameEn(t.getCustomerNameEn());
                customer.setRemark(t.getRemark());
                customer.setBusinessFlag(CustomerBusinessFlagEnum.FORMAL.getValue());
                customer.setCustomerCategoryNo(t.getCustomerCategoryNo());
                customer.setIsAssociatedEnterprise(t.getIsAssociatedEnterprise());
                customer.setAssociatedOrgCode(t.getAssociatedOrgCode());
                if (CommonIfEnum.YES.getValue().equals(t.getIsAssociatedEnterprise())) {
                    final OrganizationTreeVo organizationTreeVo = organizationMap.get(t.getAssociatedOrgCode());
                    if (organizationTreeVo != null) {
                        customer.setAssociatedOrgNo(organizationTreeVo.getOrgNo());
                        customer.setAssociatedOrgName(organizationTreeVo.getOrgName());
                    }
                }
                CommonUtil.fillCreatInfo(operationModel, customer);
                saveManageCustomerV2List.add(customer);

                futureCustomer = customer;

                // 新增管理组织的业务信息
                CustomerBiz customerBiz = new CustomerBiz();
                customerBiz.setEnterpriseNo(operationModel.getEnterpriseNo());
                customerBiz.setUseOrgNo(customer.getManageOrgNo());
                customerBiz.setCustomerNo(customer.getCustomerNo());
                customerBiz.setCustomerCode(customer.getCustomerCode());
                customerBiz.setOwnerCompany(t.getOwnerCompany());
                customerBiz.setCreditAmount(t.getCreditAmount());
//                customerBiz.setCreditDates(null != t.getPeriodDays() ? t.getPeriodDays().toString() : null);
                customerBiz.setCoopStartTime(t.getCoopStartTime());
                customerBiz.setCoopEndTime(t.getCoopEndTime());
//                customerBiz.setCooperationMode(t.getCooperationMode());
                CommonUtil.fillCreatInfo(operationModel, customerBiz);
                saveManageCustomerBizList.add(customerBiz);

                futureCustomerBiz = customerBiz;

                resultList.add(new CustomerInternalSaveVO(t.getCustomerCode(), t.getCustomerName(), true, "客户档案新增成功", operationModel));

                // 新增管理组织的分派记录
                CustomerBase customerBase = new CustomerBase();
                customerBase.setEnterpriseNo(operationModel.getEnterpriseNo());
                customerBase.setCustomerNo(customer.getCustomerNo());
                customerBase.setCustomerCode(customer.getCustomerCode());
                customerBase.setManageOrgNo(manageOrgNo);
                customerBase.setUseOrgNo(manageOrgNo);
                customerBase.setDeleted(DeletedEnum.UN_DELETE.getValue());
                CommonUtil.fillCreatInfo(operationModel, customerBase);
                saveManageCustomerBaseList.add(customerBase);
            }

            cudLinkmanPerCustomerForDA(operationModel, linkManMap.get(t.getCustomerCode()), t.getLinkmanList(), manageOrgNo, futureCustomer);

            cudAddressPerCustomerForDA(operationModel, addressMap.get(t.getCustomerCode()), t.getLinkAddressList(), manageOrgNo, futureCustomer);

            cudSalesmanPerCustomerForDA(operationModel, salesManMap.get(t.getCustomerCode()), t.getResponsibleManList(), manageOrgNo, futureCustomer);

            cudInvoicePerCustomerForDA(operationModel, invoiceMap.get(t.getCustomerCode()), t.getInvoiceList(), manageOrgNo, futureCustomer);


            Map<String, Object> futureCustomerBaseAndBizMap = new HashMap<>();
            futureCustomerBaseAndBizMap.put("futureCustomer", futureCustomer);
            futureCustomerBaseAndBizMap.put("futureCustomerBiz", futureCustomerBiz);
            futureCustomerBaseAndBizList.add(futureCustomerBaseAndBizMap);
        }

        if (CollectionUtils.isNotEmpty(saveManageCustomerV2List)) {
            customerV2DAO.addBatch(saveManageCustomerV2List);
        }

        if (CollectionUtils.isNotEmpty(updateManageCustomerV2List)) {
            customerV2DAO.batchUpdateForDA(updateManageCustomerV2List);
        }

        if (CollectionUtils.isNotEmpty(saveManageCustomerBizList)) {
            customerBizDAO.addBatch(saveManageCustomerBizList);
        }

        if (CollectionUtils.isNotEmpty(updateManageCustomerBizList)) {
            customerBizDAO.batchUpdateForDA(updateManageCustomerBizList);
        }

        if (CollectionUtils.isNotEmpty(saveManageCustomerBaseList)) {
            customerBaseDAO.addBatch(saveManageCustomerBaseList);
        }

        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                // 取消分派
                {
                    CustomerAssignExeRequest customerAssignExeRequest = new CustomerAssignExeRequest();
                    customerAssignExeRequest.setEnterpriseNo(operationModel.getEnterpriseNo());
                    customerAssignExeRequest.setManageOrgNo(manageOrgNo);
                    List<CustomerAssignDocExeRequest> docList = new ArrayList<>();
                    for (CustomerExternalSaveDTO customerExternalSaveDTO : newList) {
                        CustomerAssignDocExeRequest customerAssignDocExeRequest = new CustomerAssignDocExeRequest();
                        customerAssignDocExeRequest.setCustomerCode(customerExternalSaveDTO.getCustomerCode());
                        List<String> assignOrgNoList = new ArrayList<>();
                        for (CustomerExternalAssignItemDTO customerExternalAssignItemDTO : customerExternalSaveDTO.getAssignOrgList()) {
                            if ("disableAssign".equals(customerExternalAssignItemDTO.getStatus())) {
                                assignOrgNoList.add(customerExternalAssignItemDTO.getOrgCode());
                            }
                        }
                        customerAssignDocExeRequest.setUseOrgNoList(assignOrgNoList);
                        docList.add(customerAssignDocExeRequest);
                    }
                    customerAssignExeRequest.setDocList(docList);

                    customerV2Service.deAssignCustomer(customerAssignExeRequest);
                }

                // 分派
                {
                    CustomerAssignExeRequest customerAssignExeRequest = new CustomerAssignExeRequest();
                    customerAssignExeRequest.setEnterpriseNo(operationModel.getEnterpriseNo());
                    customerAssignExeRequest.setManageOrgNo(manageOrgNo);
                    List<CustomerAssignDocExeRequest> docList = new ArrayList<>();
                    for (CustomerExternalSaveDTO customerExternalSaveDTO : newList) {
                        CustomerAssignDocExeRequest customerAssignDocExeRequest = new CustomerAssignDocExeRequest();
                        customerAssignDocExeRequest.setCustomerCode(customerExternalSaveDTO.getCustomerCode());
                        List<String> assignOrgNoList = new ArrayList<>();
                        for (CustomerExternalAssignItemDTO customerExternalAssignItemDTO : customerExternalSaveDTO.getAssignOrgList()) {
                            if ("assign".equals(customerExternalAssignItemDTO.getStatus())) {
                                assignOrgNoList.add(customerExternalAssignItemDTO.getOrgCode());
                            }
                        }
                        customerAssignDocExeRequest.setUseOrgNoList(assignOrgNoList);
                        docList.add(customerAssignDocExeRequest);
                    }
                    customerAssignExeRequest.setDocList(docList);

                    customerV2Service.assignCustomer(customerAssignExeRequest);
                }
            }
        });

        return resultList;
    }

    private Map<String, List<?>> cudSalesmanPerCustomerForDA(OperationModel operationModel, List<CustomerSalesManV2> oldList, List<SupplierSalesManDTO> newList, String useOrgNo, CustomerV2 futureCustomer) {
        Map<String, List<?>> diffResultMap = BeanUtil.diffCUD(oldList, newList);

        List<SupplierSalesManDTO> addList = (List<SupplierSalesManDTO>) diffResultMap.get("addList");
        List<SupplierSalesManDTO> updateList = (List<SupplierSalesManDTO>) diffResultMap.get("updateList");
        List<CustomerSalesManV2> deleteList = (List<CustomerSalesManV2>) diffResultMap.get("deleteList");

        if (CollectionUtils.isNotEmpty(addList)) {
            List<CustomerSalesManV2> addSalesManList = new ArrayList<>();

            for (SupplierSalesManDTO customerSalesManReq : addList) {
                CustomerSalesManV2 customerSalesMan = new CustomerSalesManV2();
                customerSalesMan.setEnterpriseNo(operationModel.getEnterpriseNo());
                customerSalesMan.setUseOrgNo(useOrgNo);
                customerSalesMan.setCustomerCode(futureCustomer.getCustomerCode());
                customerSalesMan.setManCode(customerSalesManReq.getManCode());
                customerSalesMan.setSalesManNo(customerSalesManReq.getOrderManNo());
                customerSalesMan.setSalesManName(customerSalesManReq.getOrderManName());
                customerSalesMan.setDeptNo(customerSalesManReq.getDeptNo());
                customerSalesMan.setDeptName(customerSalesManReq.getDeptName());
                customerSalesMan.setPost(customerSalesManReq.getPost());
                customerSalesMan.setIsDefault(customerSalesManReq.getIsDefault());
                customerSalesMan.setOrderSpecialist(customerSalesManReq.getOrderSpecialist());
                customerSalesMan.setPost(customerSalesManReq.getPost());
                CommonUtil.fillCreatInfo(operationModel, customerSalesMan);

                addSalesManList.add(customerSalesMan);
            }
            customerSalesManV2DAO.addBatch(addSalesManList);
        }

        if (CollectionUtils.isNotEmpty(updateList)) {
            List<CustomerSalesManV2> updateSalesManList = new ArrayList<>();
            for (SupplierSalesManDTO customerSalesManReq : updateList) {
                CustomerSalesManV2 customerSalesMan = new CustomerSalesManV2();
                customerSalesMan.setId(customerSalesManReq.getId());
                customerSalesMan.setEnterpriseNo(operationModel.getEnterpriseNo());
                customerSalesMan.setUseOrgNo(useOrgNo);
                customerSalesMan.setCustomerCode(futureCustomer.getCustomerCode());
                customerSalesMan.setManCode(customerSalesManReq.getManCode());
                customerSalesMan.setSalesManNo(customerSalesManReq.getOrderManNo());
                customerSalesMan.setSalesManName(customerSalesManReq.getOrderManName());
                customerSalesMan.setDeptNo(customerSalesManReq.getDeptNo());
                customerSalesMan.setDeptName(customerSalesManReq.getDeptName());
                customerSalesMan.setPost(customerSalesManReq.getPost());
                customerSalesMan.setIsDefault(customerSalesManReq.getIsDefault());
                customerSalesMan.setOrderSpecialist(customerSalesManReq.getOrderSpecialist());
                customerSalesMan.setPost(customerSalesManReq.getPost());
                CommonUtil.fillOperateInfo(operationModel, customerSalesMan);

                updateSalesManList.add(customerSalesMan);
            }
            customerSalesManV2DAO.updateByIdBatch(updateSalesManList);
        }

        if (CollectionUtils.isNotEmpty(deleteList)) {
            CustomerSalesManV2 customerSalesMan = new CustomerSalesManV2();
            customerSalesMan.setDeleted(DeletedEnum.DELETED.getValue());
            CommonUtil.fillOperateInfo(operationModel, customerSalesMan);

            customerSalesManV2DAO.update(customerSalesMan, Wrappers.<CustomerSalesManV2>lambdaQuery()
                    .eq(CustomerSalesManV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                    .in(CustomerSalesManV2::getId, deleteList.stream().map(CustomerSalesManV2::getId).collect(Collectors.toList())));
        }

        return diffResultMap;
    }

    private Map<String, List<?>> cudInvoicePerCustomerForDA(OperationModel operationModel, List<CustomerInvoiceV2> oldList, List<CustomerInvoiceDTO> newList, String useOrgNo, CustomerV2 futureCustomer) {
        Map<String, List<?>> diffResultMap = BeanUtil.diffCUD(oldList, newList);

        List<CustomerInvoiceDTO> addList = (List<CustomerInvoiceDTO>) diffResultMap.get("addList");
        List<CustomerInvoiceDTO> updateList = (List<CustomerInvoiceDTO>) diffResultMap.get("updateList");
        List<CustomerInvoiceV2> deleteList = (List<CustomerInvoiceV2>) diffResultMap.get("deleteList");

        if (CollectionUtils.isNotEmpty(addList)) {
            List<CustomerInvoiceV2> addInvoiceList = new ArrayList<>();

            for (CustomerInvoiceDTO customerInvoiceReq : addList) {
                CustomerInvoiceV2 customerInvoice = new CustomerInvoiceV2();

                customerInvoice.setEnterpriseNo(operationModel.getEnterpriseNo());
                customerInvoice.setUseOrgNo(useOrgNo);
//                customerInvoice.setCustomerNo();
                customerInvoice.setCustomerCode(futureCustomer.getCustomerCode());
                customerInvoice.setInvoiceCode(customerInvoiceReq.getInvoiceCode());
                customerInvoice.setType(customerInvoiceReq.getType());
                customerInvoice.setTaxNo(customerInvoiceReq.getTaxNo());
                customerInvoice.setInvoiceTitle(customerInvoiceReq.getInvoiceTitle());
                customerInvoice.setPhone(customerInvoiceReq.getPhone());
                customerInvoice.setInvoicePhone(customerInvoiceReq.getInvoicePhone());
                customerInvoice.setEmail(customerInvoiceReq.getEmail());
//                customerInvoice.setRegionCode();
//                customerInvoice.setRegionName();
                customerInvoice.setAddress(customerInvoiceReq.getAddress());
                customerInvoice.setBankDeposit(customerInvoiceReq.getBankDeposit());
                customerInvoice.setBankAccount(customerInvoiceReq.getBankAccount());
                customerInvoice.setIsDefault(customerInvoiceReq.getIsDefault());
//                customerInvoice.setOpType();
//                customerInvoice.setAddregionCode();
                customerInvoice.setRequirement(customerInvoiceReq.getRequirement());
//                customerInvoice.setOutSystemId();
                CommonUtil.fillCreatInfo(operationModel, customerInvoice);

                addInvoiceList.add(customerInvoice);
            }
            customerInvoiceV2DAO.addBatch(addInvoiceList);
        }

        if (CollectionUtils.isNotEmpty(updateList)) {
            List<CustomerInvoiceV2> updateInvoiceList = new ArrayList<>();
            for (CustomerInvoiceDTO customerInvoiceReq : updateList) {
                CustomerInvoiceV2 customerInvoice = new CustomerInvoiceV2();
                customerInvoice.setId(customerInvoiceReq.getId());
                customerInvoice.setEnterpriseNo(operationModel.getEnterpriseNo());
                customerInvoice.setUseOrgNo(useOrgNo);
//                customerInvoice.setCustomerNo();
                customerInvoice.setCustomerCode(futureCustomer.getCustomerCode());
                customerInvoice.setInvoiceCode(customerInvoiceReq.getInvoiceCode());
                customerInvoice.setType(customerInvoiceReq.getType());
                customerInvoice.setTaxNo(customerInvoiceReq.getTaxNo());
                customerInvoice.setInvoiceTitle(customerInvoiceReq.getInvoiceTitle());
                customerInvoice.setPhone(customerInvoiceReq.getPhone());
                customerInvoice.setInvoicePhone(customerInvoiceReq.getInvoicePhone());
                customerInvoice.setEmail(customerInvoiceReq.getEmail());
//                customerInvoice.setRegionCode();
//                customerInvoice.setRegionName();
                customerInvoice.setAddress(customerInvoiceReq.getAddress());
                customerInvoice.setBankDeposit(customerInvoiceReq.getBankDeposit());
                customerInvoice.setBankAccount(customerInvoiceReq.getBankAccount());
                customerInvoice.setIsDefault(customerInvoiceReq.getIsDefault());
//                customerInvoice.setOpType();
//                customerInvoice.setAddregionCode();
                customerInvoice.setRequirement(customerInvoiceReq.getRequirement());
//                customerInvoice.setOutSystemId();
                CommonUtil.fillOperateInfo(operationModel, customerInvoice);

                updateInvoiceList.add(customerInvoice);
            }
            customerInvoiceV2DAO.updateByIdBatch(updateInvoiceList);
        }

        if (CollectionUtils.isNotEmpty(deleteList)) {
            CustomerInvoiceV2 customerInvoice = new CustomerInvoiceV2();
            customerInvoice.setDeleted(DeletedEnum.DELETED.getValue());
            CommonUtil.fillOperateInfo(operationModel, customerInvoice);

            customerInvoiceV2DAO.update(customerInvoice, Wrappers.<CustomerInvoiceV2>lambdaQuery()
                    .eq(CustomerInvoiceV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                    .in(CustomerInvoiceV2::getId, deleteList.stream().map(CustomerInvoiceV2::getId).collect(Collectors.toList())));
        }

        return diffResultMap;
    }

    @Override
    public List<CustomerExternalSaveVO> batchSaveForDA(OperationModel operationModel, List<CustomerExternalSaveDTO> params) {
        // 提前查询客户档案是否存在，因为后续需要校验禁用的档案在系统中是否存在
        Map<String, CustomerV2> localCustomerMap = new HashMap<>();
        List<String> customerCodeList = params.stream().map(CustomerExternalSaveDTO::getCustomerCode).filter(customerCode -> !StringUtils.isEmpty(customerCode)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(customerCodeList)) {
            List<CustomerV2> localCustomerList = customerV2DAO.selectList(Wrappers.<CustomerV2>lambdaQuery()
                    .eq(CustomerV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                    .in(CustomerV2::getCustomerCode, customerCodeList)
                    .eq(CustomerV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
            localCustomerMap.putAll(localCustomerList.stream().collect(Collectors.toMap(CustomerV2::getCustomerCode, Function.identity())));
        }

        // 合作性质
        Map<String, String> cooperationModeMap = new HashMap<>();

        // 非法入参的记录
        List<CustomerInternalSaveVO> invalidatedList = new ArrayList<>();

        // 参数合法性校验和参数预处理，过滤不合法的记录
        batchSaveValidateAndPreProcessForDA(operationModel, params, localCustomerMap, invalidatedList, cooperationModeMap);

        if (CollectionUtils.isEmpty(params)) {
            batchSaveBusinessLogForDA(invalidatedList);

            return convert2ExternalVoForDA(invalidatedList, null);
        }

        // 开始处理合法的记录
        Set<String> validCustomerCodeSet = params.stream().map(CustomerExternalSaveDTO::getCustomerCode).collect(Collectors.toSet());

        // 只保留合法的记录
        localCustomerMap.entrySet().removeIf(
                entry -> !validCustomerCodeSet.contains(entry.getKey()));

        // 处理企业档案和银行信息
        Map<String, List<CompanyV2>> companySaveOrUpdateListMap = companyV2ExternalService.saveOrUpdateCompanyByCustomerForDA(operationModel, params);
        List<CompanyV2> addCompanyList = companySaveOrUpdateListMap.get("addList");
        List<CompanyV2> updateCompanyList = companySaveOrUpdateListMap.get("updateList");
        Map<String, CompanyV2> companyNameMap = new HashMap<>(addCompanyList.size() + updateCompanyList.size());
        companyNameMap.putAll(addCompanyList.stream().collect(Collectors.toMap(CompanyV2::getCompanyName, Function.identity())));
        companyNameMap.putAll(updateCompanyList.stream().collect(Collectors.toMap(CompanyV2::getCompanyName, Function.identity())));

        // 处理客户分类
        customerCategoryV2Service.createAndUpdateCategoryForDA(operationModel, params);

        // 处理合作性质
        if (MapUtils.isNotEmpty(cooperationModeMap)) {
            customDocService.saveAndUpdate(operationModel.getEnterpriseNo(), SystemConstant.CUSTOMER_COOPERATION_NUMBER, cooperationModeMap, operationModel.getEmployerNo(), operationModel.getUserName());
        }

        // 处理客户档案、业务信息、资质证照、负责人、联系人、联系地址，分派记录
        final List<CustomerInternalSaveVO> handledList = saveOrUpdateCustomerV2ForDA(operationModel, companyNameMap, localCustomerMap, params);

        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                try {
                    batchSaveBusinessLogForDA(handledList);
                } catch (Exception e) {
                    log.error("客户档案批量更新日志保存失败", e);
                }
            }
        });

        return convert2ExternalVoForDA(invalidatedList, handledList);
    }

    public void handCustomerCertListMdmCompatible(OperationModel operationModel, CustomerExternalSaveDTO customerExternalSaveDTO, CustomerV2 customerV2) {
        String manageOrgNo = operationModel.getOrgNo();
        String companyNo = customerV2.getCompanyNo();
        String customerCode = customerV2.getCustomerCode();

        CompanyCertUpsertRequest companyCertUpsertRequest = new CompanyCertUpsertRequest();
        companyCertUpsertRequest.setEnterpriseNo(operationModel.getEnterpriseNo());
        companyCertUpsertRequest.setOrgNo(manageOrgNo);
        companyCertUpsertRequest.setCompanyNo(companyNo);
        companyCertUpsertRequest.setSourceDocType(CompanyCertSourceTypeEnum.CUSTOMER.getCode());
        companyCertUpsertRequest.setSourceDocCode(customerCode);

        List<CompanyCertBasicRequest> companyCertList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(customerExternalSaveDTO.getCertList())) {
            for (CompanyFileDTO companyFileDTO : customerExternalSaveDTO.getCertList()) {
                CompanyCertBasicRequest companyCertBasicRequest = new CompanyCertBasicRequest();
                companyCertBasicRequest.setCertTypeCode(companyFileDTO.getCertTypeCode());
                companyCertBasicRequest.setCertCode(companyFileDTO.getCertCode());
                companyCertBasicRequest.setCertName(companyFileDTO.getCertName());
                companyCertBasicRequest.setStartTime(companyFileDTO.getStartTime());
                companyCertBasicRequest.setEndTime(companyFileDTO.getEndTime());
                companyCertBasicRequest.setLongTerm(companyFileDTO.getLongTerm());
//                    companyCertBasicRequest.setIssuingAuthority();
                companyCertBasicRequest.setRemark(companyFileDTO.getRemark());
                List<CompanyCertFileRequest> companyCertFileRequestList = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(companyFileDTO.getBaseFileList())) {
                    for (String file : companyFileDTO.getBaseFileList()) {
                        CompanyCertFileRequest companyCertFileRequest = new CompanyCertFileRequest();

                        companyCertFileRequest.setFilePath(file);
                        companyCertFileRequest.setFileName(file);

                        companyCertFileRequestList.add(companyCertFileRequest);
                    }
                }
                companyCertBasicRequest.setFileList(companyCertFileRequestList);
//                    companyCertBasicRequest.setBusinessScopeList();
            }
        }
        companyCertUpsertRequest.setCompanyCertList(companyCertList);
        certService.upsert(companyCertUpsertRequest);
    }

    public void handCustomerInvoiceListMdmCompatible(OperationModel operationModel, CustomerExternalSaveDTO customerExternalSaveDTO, CustomerV2 customerV2) {
        String manageOrgNo = operationModel.getOrgNo();
        String customerCode = customerV2.getCustomerCode();

        CustomerInvoiceListEditReq customerInvoiceListEditReq = new CustomerInvoiceListEditReq();
        customerInvoiceListEditReq.setEnterpriseNo(operationModel.getEnterpriseNo());
        customerInvoiceListEditReq.setUseOrgNo(manageOrgNo);
        customerInvoiceListEditReq.setCustomerCode(customerCode);
        List<CustomerInvoiceReq> invoiceList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(customerExternalSaveDTO.getInvoiceList())) {
            for (CustomerInvoiceDTO customerInvoiceDTO : customerExternalSaveDTO.getInvoiceList()) {
                CustomerInvoiceReq invoiceReq = new CustomerInvoiceReq();
//                invoiceReq.setInvoiceCode();

                invoiceReq.setType(customerInvoiceDTO.getType());
                invoiceReq.setTaxNo(customerInvoiceDTO.getTaxNo());
                invoiceReq.setInvoiceTitle(customerInvoiceDTO.getInvoiceTitle());
                invoiceReq.setPhone(customerInvoiceDTO.getPhone());
                invoiceReq.setInvoicePhone(customerInvoiceDTO.getInvoicePhone());
                invoiceReq.setEmail(customerInvoiceDTO.getEmail());
                invoiceReq.setAddress(customerInvoiceDTO.getAddress());
                invoiceReq.setBankDeposit(customerInvoiceDTO.getBankDeposit());
                invoiceReq.setBankAccount(customerInvoiceDTO.getBankAccount());
                invoiceReq.setRequirement(customerInvoiceDTO.getRequirement());
                invoiceReq.setIsDefault(customerInvoiceDTO.getIsDefault());
                invoiceReq.setTypeName(null != customerInvoiceDTO.getType() ? (InvoiceTypeEnum.getByType(customerInvoiceDTO.getType()) == null ? null : InvoiceTypeEnum.getByType(customerInvoiceDTO.getType()).getName()) : null);
                invoiceReq.setIsDefaultName(null != customerInvoiceDTO.getIsDefault() ? CommonIfEnum.getNameByValue(customerInvoiceDTO.getIsDefault()) : null);

                invoiceList.add(invoiceReq);
            }
        }
        customerInvoiceListEditReq.setInvoiceList(invoiceList);

        customerV2Service.overwriteCustomerInvoiceList(operationModel, customerInvoiceListEditReq);
    }

    public void handCustomerOrderManListMdmCompatible(OperationModel operationModel, CustomerExternalSaveDTO customerExternalSaveDTO, CustomerV2 customerV2) {
        String manageOrgNo = operationModel.getOrgNo();
        String customerCode = customerV2.getCustomerCode();

        CustomerSalesmanListEditReq customerOrdermanListEditReq = new CustomerSalesmanListEditReq();
        customerOrdermanListEditReq.setEnterpriseNo(operationModel.getEnterpriseNo());
        customerOrdermanListEditReq.setUseOrgNo(manageOrgNo);
        customerOrdermanListEditReq.setCustomerCode(customerCode);
        List<CustomerSalesManReq> salesManList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(customerExternalSaveDTO.getResponsibleManList())) {
            for (SupplierSalesManDTO customerSalesManDTO : customerExternalSaveDTO.getResponsibleManList()) {
                CustomerSalesManReq orderManReq = new CustomerSalesManReq();
                orderManReq.setDeptNo(customerSalesManDTO.getDeptNo());
                orderManReq.setDeptName(customerSalesManDTO.getDeptName());
                orderManReq.setSalesManNo(customerSalesManDTO.getOrderManNo());
                orderManReq.setSalesManName(customerSalesManDTO.getOrderManName());
                orderManReq.setPost(customerSalesManDTO.getPost());
                orderManReq.setOrderSpecialist(customerSalesManDTO.getOrderSpecialist());
                orderManReq.setIsDefault(customerSalesManDTO.getIsDefault());
                orderManReq.setOrderSpecialistName(null != customerSalesManDTO.getOrderSpecialist() ? CommonIfEnum.getNameByValue(customerSalesManDTO.getOrderSpecialist()) : null);
                orderManReq.setIsDefaultName(null != customerSalesManDTO.getIsDefault() ? CommonIfEnum.getNameByValue(customerSalesManDTO.getIsDefault()) : null);

                salesManList.add(orderManReq);
            }
        }

        customerOrdermanListEditReq.setSalesManList(salesManList);
        customerV2Service.overwriteCustomerSalesmanList(operationModel, customerOrdermanListEditReq);
    }

    public void handCustomerAddressListMdmCompatible(OperationModel operationModel, CustomerExternalSaveDTO customerExternalSaveDTO, CustomerV2 customerV2, Map<String, String> countryRegionMap) {
        String manageOrgNo = operationModel.getOrgNo();
        String customerCode = customerV2.getCustomerCode();

        CustomerAddressListEditReq customerAddressListEditReq = new CustomerAddressListEditReq();
        customerAddressListEditReq.setEnterpriseNo(operationModel.getEnterpriseNo());
        customerAddressListEditReq.setUseOrgNo(manageOrgNo);
        customerAddressListEditReq.setCustomerCode(customerCode);
        if (CollectionUtils.isNotEmpty(customerExternalSaveDTO.getLinkAddressList())) {
            List<CompanyShippingAddressReq> addressList = new ArrayList<>();

            for (CompanyShippingAddressDTO companyShippingAddressDTO : customerExternalSaveDTO.getLinkAddressList()) {
                CompanyShippingAddressReq addressReq = new CompanyShippingAddressReq();
                addressReq.setLinkAddressCode(companyShippingAddressDTO.getLinkAddressCode());
                addressReq.setReceiveUser(companyShippingAddressDTO.getReceiveUser());
                addressReq.setReceivePhone(companyShippingAddressDTO.getReceivePhone());
                addressReq.setRegionCode(companyShippingAddressDTO.getRegionCode());
                addressReq.setRegionName(companyShippingAddressDTO.getRegionName());
                addressReq.setReceiveAddr(companyShippingAddressDTO.getReceiveAddr());
                addressReq.setIsDefault(companyShippingAddressDTO.getIsDefault());
                addressReq.setAddressType(companyShippingAddressDTO.getAddressType());
                addressReq.setAddressDesc(companyShippingAddressDTO.getAddressDesc());
                addressReq.setAddressTypeName(StringUtils.isNotEmpty(companyShippingAddressDTO.getAddressType()) ? AddressEnum.getByType(companyShippingAddressDTO.getAddressType()).getName() : null);
                addressReq.setRegionFullName(StringUtils.isNotEmpty(companyShippingAddressDTO.getRegionCode()) ? countryRegionMap.get(companyShippingAddressDTO.getRegionCode()) : null);
                addressReq.setIsDefaultName(null != companyShippingAddressDTO.getIsDefault() ? CommonIfEnum.getNameByValue(companyShippingAddressDTO.getIsDefault()) : null);

                addressList.add(addressReq);
            }

            customerAddressListEditReq.setLinkAddressList(addressList);
        }
        customerV2Service.overwriteCustomerAddressList(operationModel, customerAddressListEditReq);
    }

    public void handCustomerLinkmanListMdmCompatible(OperationModel operationModel, CustomerExternalSaveDTO customerExternalSaveDTO, CustomerV2 customerV2) {
        String manageOrgNo = operationModel.getOrgNo();
        String customerCode = customerV2.getCustomerCode();

        CustomerLinkmanListEditReq customerLinkmanListEditReq = new CustomerLinkmanListEditReq();
        customerLinkmanListEditReq.setEnterpriseNo(operationModel.getEnterpriseNo());
        customerLinkmanListEditReq.setUseOrgNo(manageOrgNo);
        customerLinkmanListEditReq.setCustomerCode(customerCode);
        if (CollectionUtils.isNotEmpty(customerExternalSaveDTO.getLinkmanList())) {
            List<CompanyLinkmanReq> linkmanList = new ArrayList<>();

            for (CompanyLinkmanDTO companyLinkmanDTO : customerExternalSaveDTO.getLinkmanList()) {
                CompanyLinkmanReq linkmanReq = new CompanyLinkmanReq();
                linkmanReq.setLinkman(companyLinkmanDTO.getLinkman());
                linkmanReq.setPosition(companyLinkmanDTO.getPosition());
                linkmanReq.setMobilePhone(companyLinkmanDTO.getMobilePhone());
                linkmanReq.setFixedPhone(companyLinkmanDTO.getFixedPhone());
                linkmanReq.setSex(companyLinkmanDTO.getSex());
                linkmanReq.setEmail(companyLinkmanDTO.getEmail());
                linkmanReq.setQq(companyLinkmanDTO.getQq());
                linkmanReq.setWx(companyLinkmanDTO.getWx());
                linkmanReq.setStatus(companyLinkmanDTO.getStatus());
                linkmanReq.setIsDefault(companyLinkmanDTO.getIsDefault());
                linkmanReq.setLinkCode(companyLinkmanDTO.getLinkCode());
                linkmanReq.setSexName(StringUtils.isNotEmpty(companyLinkmanDTO.getSex()) ? (null != SexEnum.getByType(companyLinkmanDTO.getSex()) ? SexEnum.getByType(companyLinkmanDTO.getSex()).getName() : null) : null);
                linkmanReq.setStatusName(null != companyLinkmanDTO.getStatus() ? CommonStatusEnum.getNameByValue(companyLinkmanDTO.getStatus()) : null);
                linkmanReq.setIsDefaultName(null != companyLinkmanDTO.getIsDefault() ? CommonIfEnum.getNameByValue(companyLinkmanDTO.getIsDefault()) : null);

                linkmanList.add(linkmanReq);

            }

            customerLinkmanListEditReq.setLinkmanList(linkmanList);
        }
        customerV2Service.overwriteCustomerLinkmanList(operationModel, customerLinkmanListEditReq);
    }

    public void handCustomerCategoryMdmCompatible(OperationModel operationModel, CustomerExternalSaveDTO customerExternalSaveDTO, CustomerV2 customerV2) {
        String manageOrgNo = operationModel.getOrgNo();

        if (StringUtils.isNotEmpty(customerV2.getCustomerCategoryNo())) {
            CustomerCategoryV2 customerCategoryV2 = customerCategoryV2Service.getByNo(operationModel.getEnterpriseNo(), customerV2.getCustomerCategoryNo());
            ValidatorUtils.checkTrueThrowEx(null == customerCategoryV2, "客户分类不存在");

            boolean categoryBaseExists = customerCategoryBaseDAO.exists(Wrappers.<CustomerCategoryBase>lambdaQuery()
                    .eq(CustomerCategoryBase::getEnterpriseNo, operationModel.getEnterpriseNo())
                    .eq(CustomerCategoryBase::getUseOrgNo, manageOrgNo)
                    .eq(CustomerCategoryBase::getCategoryNo, customerV2.getCustomerCategoryNo())
                    .eq(CustomerCategoryBase::getDeleted, DeletedEnum.UN_DELETE.getValue()));
//            ValidatorUtils.checkTrueThrowEx(categoryBaseExists, "客户分类分派信息已经存在");
            if (categoryBaseExists) {
                log.warn("客户分类分派信息已经存在,customerCategoryNo={},manageOrgNo={}", customerV2.getCustomerCategoryNo(), manageOrgNo);
                return;
            }

            CustomerCategoryBase customerCategoryBase = new CustomerCategoryBase();
            customerCategoryBase.setEnterpriseNo(operationModel.getEnterpriseNo());
            customerCategoryBase.setCategoryNo(customerV2.getCustomerCategoryNo());
            customerCategoryBase.setCategoryCode(customerCategoryV2.getCategoryCode());
            customerCategoryBase.setManageOrgNo(customerCategoryV2.getManageOrgNo());
            customerCategoryBase.setUseOrgNo(manageOrgNo);
            CommonUtil.fillCreatInfo(operationModel, customerCategoryBase);
            CommonUtil.fillOperateInfo(operationModel, customerCategoryBase);

            customerCategoryBaseDAO.insert(customerCategoryBase);
        }
    }

    public void handCustomerBizMdmCompatible(OperationModel operationModel, CustomerExternalSaveDTO customerExternalSaveDTO, CustomerV2 customerV2, Map<String, CustomDocResponse> paymentTermMap, Map<String, PaymentAgreementVo> paymentAgreementMap) {
        String manageOrgNo = operationModel.getOrgNo();
        String customerCode = customerExternalSaveDTO.getCustomerCode();

        boolean bizExists = customerBizDAO.exists(Wrappers.<CustomerBiz>lambdaQuery()
                .eq(CustomerBiz::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CustomerBiz::getUseOrgNo, manageOrgNo)
                .eq(CustomerBiz::getCustomerCode, customerCode)
                .eq(CustomerBiz::getDeleted, DeletedEnum.UN_DELETE.getValue()));
//        ValidatorUtils.checkTrueThrowEx(bizExists, "客户业务信息已经存在");
        if (bizExists) {
            log.warn("客户业务信息已经存在,customerCode={},manageOrgNo={}", customerCode, manageOrgNo);
            return;
        }

        CustomerBiz customerBiz = new CustomerBiz();
        customerBiz.setCustomerNo(customerV2.getCustomerNo());
        customerBiz.setEnterpriseNo(customerV2.getEnterpriseNo());
        customerBiz.setUseOrgNo(manageOrgNo);
        customerBiz.setUseOrgModifyFlag(CommonIfEnum.NO.getValue());
        customerBiz.setCustomerCode(customerV2.getCustomerCode());
        customerBiz.setCooperationMode(customerExternalSaveDTO.getCooperationMode());
//        customerBiz.setPriceCategoryCode();
        customerBiz.setOwnerCompany(customerExternalSaveDTO.getOwnerCompany());
        customerBiz.setCreditAmount(customerExternalSaveDTO.getCreditAmount());
        customerBiz.setCreditDates(null != customerExternalSaveDTO.getPeriodDays() ? customerExternalSaveDTO.getPeriodDays().toString() : null);
        customerBiz.setSettlementModes(customerExternalSaveDTO.getSettlementModesCode());
        customerBiz.setSettlementModesName(customerExternalSaveDTO.getSettlementModesName());
        customerBiz.setReceiveAgreement(customerExternalSaveDTO.getPaymentAgreementName());
        customerBiz.setReceiveCondition(customerExternalSaveDTO.getPaymentTermCode());
//        customerBiz.setBusinessType();
//        customerBiz.setBusinessTypeName();
        customerBiz.setCoopStartTime(customerExternalSaveDTO.getCoopStartTime());
        customerBiz.setCoopEndTime(customerExternalSaveDTO.getCoopEndTime());
//        customerBiz.setCurrencyId();
//        customerBiz.setOmsCustomerNo();
        CommonUtil.fillCreatInfo(operationModel, customerBiz);
        CommonUtil.fillOperateInfo(operationModel, customerBiz);

        customerBizDAO.insert(customerBiz);
    }

    public void handCustomerBaseMdmCompatible(OperationModel operationModel, CustomerExternalSaveDTO customerExternalSaveDTO, CustomerV2 customerV2) {
        String manageOrgNo = operationModel.getOrgNo();
        String customerCode = customerExternalSaveDTO.getCustomerCode();

        boolean baseExists = customerBaseDAO.exists(Wrappers.<CustomerBase>lambdaQuery()
                .eq(CustomerBase::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CustomerBase::getManageOrgNo, manageOrgNo)
                .eq(CustomerBase::getUseOrgNo, manageOrgNo)
                .eq(CustomerBase::getCustomerCode, customerCode)
                .eq(CustomerBase::getDeleted, DeletedEnum.UN_DELETE.getValue()));
//        ValidatorUtils.checkTrueThrowEx(baseExists, "客户分派信息已经存在");
        if (baseExists) {
            log.warn("客户分派信息已经存在,customerCode={},manageOrgNo={}", customerCode, manageOrgNo);
            return;
        }

        CustomerBase customerBase = new CustomerBase();
        customerBase.setEnterpriseNo(customerV2.getEnterpriseNo());
        customerBase.setCustomerNo(customerV2.getCustomerNo());
        customerBase.setCustomerCode(customerV2.getCustomerCode());
        customerBase.setManageOrgNo(manageOrgNo);
        customerBase.setUseOrgNo(manageOrgNo);
        customerBase.setControlStatus(CustomerControlStatusEnum.ENABLED.getType()); //默认启用
        CommonUtil.fillCreatInfo(operationModel, customerBase);
        CommonUtil.fillOperateInfo(operationModel, customerBase);

        customerBaseDAO.insert(customerBase);
    }

    public void handCompanyBankMdmCompatible(OperationModel operationModel, CustomerExternalSaveDTO customerExternalSaveDTO, CustomerV2 customerV2, Map<String, BankType> bankTypeMap) {
        String manageOrgNo = operationModel.getOrgNo();
        String companyNo = customerV2.getCompanyNo();

        List<CompanyBankReq> companyBankReqList = null;
        if (CollectionUtils.isNotEmpty(customerExternalSaveDTO.getBankList())) {
            companyBankReqList = new ArrayList<>();
            for (BankDTO bankDTO : customerExternalSaveDTO.getBankList()) {
                CompanyBankReq companyBankReq = new CompanyBankReq();
                companyBankReq.setBankCode(bankDTO.getBankCode());
                companyBankReq.setBankType(bankDTO.getBankType());
                companyBankReq.setOpenBank(bankDTO.getOpenBank());
                companyBankReq.setAccountName(bankDTO.getAccountName());
                companyBankReq.setAccountNo(bankDTO.getAccountNo());
                companyBankReq.setAccountType(bankDTO.getAccountType());
                companyBankReq.setLinkPerson(bankDTO.getLinkPerson());
                companyBankReq.setLinkPhone(bankDTO.getLinkPhone());
                companyBankReq.setStatus(bankDTO.getStatus());
                companyBankReq.setIsDefault(bankDTO.getIsDefault());
//                    companyBankReq.setCurrencyId();
//                    companyBankReq.setCurrencyName();
                companyBankReq.setBankTypeName(StringUtils.isNotEmpty(bankDTO.getBankType()) ? bankTypeMap.getOrDefault(bankDTO.getBankType(), new BankType()).getBankName() : null);
                companyBankReq.setAccountTypeName(null != bankDTO.getAccountType() ? BankAccountTypeEnum.getNameByValue(bankDTO.getAccountType()) : null);
                companyBankReq.setStatusName(null != bankDTO.getStatus() ? CommonStatusEnum.getNameByValue(bankDTO.getStatus()) : null);
                companyBankReq.setIsDefaultName(null != bankDTO.getIsDefault() ? CommonIfEnum.getNameByValue(bankDTO.getIsDefault()) : null);

                companyBankReqList.add(companyBankReq);
            }
        }
        companyV2Service.overwriteBank(operationModel, companyBankReqList, companyNo, manageOrgNo);
    }


    @Override
    @Transactional
//    @Async("mdmCompatibleExecutor")
    public void batchSaveMdmCompatible(OperationModel operationModel, List<CustomerExternalSaveDTO> params, List<CustomerInternalSaveVO> resultList) {
        log.warn("batchSaveMdmCompatible start, enterpriseNo={}, manageOrgNo={}, params={}, resultList={}", operationModel.getEnterpriseNo(), operationModel.getOrgNo(), JSON.toJSONString(params), JSON.toJSONString(resultList));

        // 不在灰度范围内的租户不处理
        if (!mdmGray.isEnterpriseGray(operationModel.getEnterpriseNo())) {
            log.warn("batchSaveMdmCompatible end, not in gray");

            return;
        }

        // 管理组织
        String manageOrgNo = operationModel.getOrgNo();

        if (StringUtils.isEmpty(manageOrgNo)) {
            log.warn("batchSaveMdmCompatible end, manageOrgNo is empty");

            return;
        }

        if (CollectionUtils.isEmpty(params) || CollectionUtils.isEmpty(resultList)) {
            log.warn("batchSaveMdmCompatible end, params or resultList null");

            return;
        }

        resultList = resultList.stream().filter(r -> null != r.getSuccess() && r.getSuccess()).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(resultList)) {
            log.warn("batchSaveMdmCompatible end, successful resultList empty");

            return;
        }

        List<CustomerV2> customerV2s = customerV2DAO.selectList(Wrappers.<CustomerV2>lambdaQuery()
                .eq(CustomerV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .in(CustomerV2::getCustomerCode, resultList.stream().map(CustomerInternalSaveVO::getCustomerCode).collect(Collectors.toList()))
                .eq(CustomerV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
        Map<String, CustomerV2> customerCode2CustomerV2Map = customerV2s.stream().collect(Collectors.toMap(CustomerV2::getCustomerCode, Function.identity()));

        Map<String, CustomerExternalSaveDTO> customerCode2SaveDTO = params.stream().collect(Collectors.toMap(CustomerExternalSaveDTO::getCustomerCode, Function.identity()));

//        Map<String, String> currencyMap = dictEnterpriseService.findCurrencyList(operationModel.getEnterpriseNo());
//        currencyMap = currencyMap.entrySet().stream().collect(Collectors.toMap(entity-> entity.getValue(),entity-> entity.getKey()));

        List<OrganizationTreeVo> organizationTreeList = uimTenantService.getOrganizationTreeList(operationModel.getEnterpriseNo());
        Map<String, OrganizationTreeVo> organizationMap = organizationTreeList.stream().collect(Collectors.toMap(OrganizationTreeVo::getOrgCode, Function.identity(), (k, v) -> k));

        List<PaymentAgreementVo> paymentAgreementVoList = paymentAgreementService.findList(operationModel.getEnterpriseNo());
        Map<String, PaymentAgreementVo> paymentAgreementMap = paymentAgreementVoList.stream().collect(Collectors.toMap(PaymentAgreementVo::getCode, Function.identity()));

        Map<String, CustomDocResponse> paymentTermMap = customDocService.getItemByCustomDocCode(operationModel.getEnterpriseNo(), SystemConstant.DSRP_CUSTOMER_PAYMENT_TERM);

        List<BankType> bankTypeList = companyV2Service.getBankTypeList();
        Map<String, BankType> bankTypeMap = bankTypeList.stream().collect(Collectors.toMap(BankType::getBankCode, Function.identity()));

        Map<String, String> countryRegionMap = dictEnterpriseService.findPlatformCountryRegionList();


        for (CustomerInternalSaveVO customerInternalSaveVO : resultList) {
            String customerCode = customerInternalSaveVO.getCustomerCode();
            CustomerExternalSaveDTO customerExternalSaveDTO = customerCode2SaveDTO.get(customerCode);
            CustomerV2 customerV2 = customerCode2CustomerV2Map.get(customerCode);
            ValidatorUtils.checkTrueThrowEx(null == customerExternalSaveDTO || null == customerV2, "通过供应商编码无法找到对应供应商");

            // 关联的内部组织关系
            CompanySaveOrUpdateReq companySaveOrUpdateReq = new CompanySaveOrUpdateReq();
            companySaveOrUpdateReq.setCompanyNo(customerV2.getCompanyNo());
            companySaveOrUpdateReq.setIsAssociatedEnterprise(customerExternalSaveDTO.getIsAssociatedEnterprise());
            if (CommonIfEnum.YES.getValue().equals(customerExternalSaveDTO.getIsAssociatedEnterprise())) {
                OrganizationTreeVo organizationTreeVo = organizationMap.get(customerExternalSaveDTO.getAssociatedOrgCode());
                if (null != organizationTreeVo) {
                    companySaveOrUpdateReq.setAssociatedOrgCode(customerExternalSaveDTO.getAssociatedOrgCode());
                    companySaveOrUpdateReq.setAssociatedOrgNo(organizationTreeVo.getOrgNo());
                    companySaveOrUpdateReq.setAssociatedOrgName(organizationTreeVo.getOrgName());
                }
            } else {
                companySaveOrUpdateReq.setAssociatedOrgCode(null);
                companySaveOrUpdateReq.setAssociatedOrgNo(null);
                companySaveOrUpdateReq.setAssociatedOrgName(null);
            }
            log.warn("saveSupCusAssociatedOrg, companyNo={}, customerCode={}, companySaveOrUpdateReq={}",customerV2.getCompanyNo(), customerCode, JSON.toJSONString(companySaveOrUpdateReq));
            companyV2Service.saveSupCusAssociatedOrg(customerV2.getCompanyNo(), operationModel.getEnterpriseNo(), customerCode, companySaveOrUpdateReq, CompanyPartnershipEnum.CUSTOMER);

            // 处理银行信息
            log.warn("handCompanyBankMdmCompatible, customerExternalSaveDTO={}, customerV2={}, bankTypeMap={}", customerExternalSaveDTO, customerV2, bankTypeMap);
            handCompanyBankMdmCompatible(operationModel, customerExternalSaveDTO, customerV2, bankTypeMap);

            // 更新内部组织信息
            log.warn("update customerV2, customerCode={}, customerExternalSaveDTO={}", customerCode, customerExternalSaveDTO);
            customerV2DAO.update(null, Wrappers.<CustomerV2>lambdaUpdate()
                    // 更新字段
                    .set(CustomerV2::getIsAssociatedEnterprise, customerExternalSaveDTO.getIsAssociatedEnterprise())
                    .set(StringUtils.isNotEmpty(customerExternalSaveDTO.getAssociatedOrgCode()), CustomerV2::getAssociatedOrgCode, customerExternalSaveDTO.getAssociatedOrgCode())
                    .set(StringUtils.isNotEmpty(customerExternalSaveDTO.getAssociatedOrgCode()), CustomerV2::getAssociatedOrgNo, organizationMap.get(customerExternalSaveDTO.getAssociatedOrgCode()) != null ? organizationMap.get(customerExternalSaveDTO.getAssociatedOrgCode()).getOrgNo() : null)
                    .set(StringUtils.isNotEmpty(customerExternalSaveDTO.getAssociatedOrgCode()), CustomerV2::getAssociatedOrgName, organizationMap.get(customerExternalSaveDTO.getAssociatedOrgCode()) != null ? organizationMap.get(customerExternalSaveDTO.getAssociatedOrgCode()).getOrgName() : null)
                    // where条件
                    .eq(CustomerV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                    .eq(CustomerV2::getCustomerCode, customerCode)
            );


            // 处理分派数据
            log.warn("handCustomerBaseMdmCompatible, customerExternalSaveDTO={}, customerV2={}", customerExternalSaveDTO, customerV2);
            handCustomerBaseMdmCompatible(operationModel, customerExternalSaveDTO, customerV2);

            // 处理业务数据
            log.warn("handCustomerBizMdmCompatible, customerExternalSaveDTO={}, customerV2={}, paymentTermMap={}, paymentAgreementMap={}", customerExternalSaveDTO, customerV2, paymentTermMap, paymentAgreementMap);
            handCustomerBizMdmCompatible(operationModel, customerExternalSaveDTO, customerV2, paymentTermMap, paymentAgreementMap);

            // 处理分类分派
            log.warn("handCustomerCategoryMdmCompatible, customerExternalSaveDTO={}, customerV2={}", customerExternalSaveDTO, customerV2);
            handCustomerCategoryMdmCompatible(operationModel, customerExternalSaveDTO, customerV2);

            // 处理联系人
            log.warn("handCustomerLinkmanListMdmCompatible, customerExternalSaveDTO={}, customerV2={}", customerExternalSaveDTO, customerV2);
            handCustomerLinkmanListMdmCompatible(operationModel, customerExternalSaveDTO, customerV2);

            // 处理地址
            log.warn("handCustomerAddressListMdmCompatible, customerExternalSaveDTO={}, customerV2={}, countryRegionMap={}", customerExternalSaveDTO, customerV2, countryRegionMap);
            handCustomerAddressListMdmCompatible(operationModel, customerExternalSaveDTO, customerV2, countryRegionMap);

            // 处理负责人
            log.warn("handCustomerOrderManListMdmCompatible, customerExternalSaveDTO={}, customerV2={}", customerExternalSaveDTO, customerV2);
            handCustomerOrderManListMdmCompatible(operationModel, customerExternalSaveDTO, customerV2);

            //处理开票信息
            log.warn("handCustomerInvoiceListMdmCompatible, customerExternalSaveDTO={}, customerV2={}", customerExternalSaveDTO, customerV2);
            handCustomerInvoiceListMdmCompatible(operationModel, customerExternalSaveDTO, customerV2);

            // 处理资质信息
            log.warn("handCustomerCertListMdmCompatible, customerExternalSaveDTO={}, customerV2={}", customerExternalSaveDTO, customerV2);
            handCustomerCertListMdmCompatible(operationModel, customerExternalSaveDTO, customerV2);

            // 分派通过CustomerAssignListener处理
        }

        log.warn("batchSaveMdmCompatible end");
    }
}
