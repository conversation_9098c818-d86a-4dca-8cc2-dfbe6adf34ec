package com.yyigou.dsrp.cdc.service.customer.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.yyigou.ddc.common.page.PageDto;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.ddc.common.util.ValidatorUtils;
import com.yyigou.ddc.services.ddc.uim.vo.OrganizationVo;
import com.yyigou.ddc.services.dsrp.bdc.vo.CustomerSalesManVo;
import com.yyigou.dsrp.cdc.api.customer.dto.CustomerSalesManQueryBySpecifyDTO;
import com.yyigou.dsrp.cdc.api.customer.dto.CustomerSalesManQueryDTO;
import com.yyigou.dsrp.cdc.api.customer.vo.CustomerSalesManVO;
import com.yyigou.dsrp.cdc.common.enums.DeletedEnum;
import com.yyigou.dsrp.cdc.common.util.OperationModel;
import com.yyigou.dsrp.cdc.dao.customer.CustomerDAO;
import com.yyigou.dsrp.cdc.dao.customer.CustomerSalesManDAO;
import com.yyigou.dsrp.cdc.dao.customer.entity.Customer;
import com.yyigou.dsrp.cdc.dao.customer.entity.CustomerSalesMan;
import com.yyigou.dsrp.cdc.manager.integration.org.UimTenantService;
import com.yyigou.dsrp.cdc.service.customer.CustomerSaleManService;
import com.yyigou.dsrp.cdc.service.utils.PageUtils;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class CustomerSaleManServiceImpl implements CustomerSaleManService {
    private final UimTenantService uimTenantService;
    private final CustomerDAO customerDAO;
    private final CustomerSalesManDAO customerSalesManDAO;

    /**
     * 跨组织获取客户销售人列表
     *
     * @param operationModel
     * @param customerCode
     * @param orgNo
     * @return
     */
    @Override
    public List<CustomerSalesManVO> getCustomerSaleManListByOrg(OperationModel operationModel, String customerCode, String orgNo) {
        ValidatorUtils.checkTrueThrowEx(StringUtils.isEmpty(customerCode), "客户编码不存在");
        ValidatorUtils.checkTrueThrowEx(StringUtils.isEmpty(orgNo), "组织编码不存在");
        //获取有权限的组织列表
        List<OrganizationVo> organizationList = uimTenantService.getAuthEnterpriseList(operationModel.getEnterpriseNo(), operationModel.getUserNo(), operationModel.getEmployerNo());
        final Optional<OrganizationVo> optional = organizationList.stream().filter(t -> orgNo.equals(t.getOrgNo())).findAny();
        if (!optional.isPresent()) {
            //无权限查询
            return new ArrayList<>();
        }
        final OrganizationVo organizationVo = optional.get();
        Customer customer = customerDAO.getCustomerByCustomerCode(organizationVo.getBindingEnterpriseNo(), customerCode);
        ValidatorUtils.checkTrueThrowEx(customer == null, "客户不存在");
        List<CustomerSalesManVO> result = new ArrayList<>();
        final List<CustomerSalesMan> customerSalesManByCustomerNoList = customerSalesManDAO.getCustomerSalesManByCustomerNoList(customer.getEnterpriseNo(), Collections.singletonList(customer.getCustomerNo()));
        for (CustomerSalesMan customerSalesMan : customerSalesManByCustomerNoList) {
            CustomerSalesManVO customerSalesManVO = new CustomerSalesManVO();
            BeanUtils.copyProperties(customerSalesMan, customerSalesManVO);
            result.add(customerSalesManVO);
        }
        return result;
    }

    /**
     * 获取客户销售人列表
     *
     * @param operationModel
     * @param params
     * @return
     */
    @Override
    public List<CustomerSalesManVo> findList(OperationModel operationModel, CustomerSalesManQueryDTO params) {
        LambdaQueryWrapper<CustomerSalesMan> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(CustomerSalesMan::getDeleted, DeletedEnum.UN_DELETE.getValue());
        handleQueryParams(lambdaQueryWrapper, params);
        lambdaQueryWrapper.inSql(CustomerSalesMan::getCustomerNo, "select customer_no  from  bdc_customer where deleted=0 and enterprise_no in ('" + operationModel.getEnterpriseNo() + "')");
        final List<CustomerSalesMan> list = customerSalesManDAO.selectList(lambdaQueryWrapper);
        return list.stream().map(customerSalesMan -> {
            CustomerSalesManVo customerSalesManVo = new CustomerSalesManVo();
            BeanUtils.copyProperties(customerSalesMan, customerSalesManVo);
            return customerSalesManVo;

        }).collect(Collectors.toList());
    }

    /**
     * 获取客户销售人列表
     *
     * @param operationModel
     * @param params
     * @param pageDto
     * @return
     */
    @Override
    public PageVo<CustomerSalesManVo> findPage(OperationModel operationModel, CustomerSalesManQueryDTO params, PageDto pageDto) {
        Page<CustomerSalesMan> page = PageHelper.startPage(pageDto.getPageIndex(), pageDto.getPageSize(), StringUtils.isBlank(pageDto.getOrderBy()) ? "create_time desc" : pageDto.getOrderBy());
        LambdaQueryWrapper<CustomerSalesMan> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(CustomerSalesMan::getDeleted, DeletedEnum.UN_DELETE.getValue());
        handleQueryParams(lambdaQueryWrapper, params);
        lambdaQueryWrapper.inSql(CustomerSalesMan::getCustomerNo, "select customer_no  from  bdc_customer where deleted=0 and enterprise_no in ('" + operationModel.getEnterpriseNo() + "')");
        customerSalesManDAO.selectList(lambdaQueryWrapper);
        return PageUtils.convertPageVo(page, data -> {
            List<CustomerSalesManVo> result = new ArrayList<>();
            data.forEach(t -> {
                CustomerSalesManVo vo = new CustomerSalesManVo();
                BeanUtils.copyProperties(t, vo);
                result.add(vo);
            });
            return result;
        });
    }

    /**
     * 获取客户销售人列表
     *
     * @param operationModel
     * @param params
     * @return
     */
    @Override
    public List<CustomerSalesManVo> findListBySpecifyOrg(OperationModel operationModel, CustomerSalesManQueryBySpecifyDTO params) {
        List<OrganizationVo> organizationList = uimTenantService.getAuthEnterpriseList(operationModel.getEnterpriseNo(), operationModel.getUserNo(), operationModel.getEmployerNo());
        final Optional<OrganizationVo> optional = organizationList.stream().filter(t -> params.getOrgNo().equals(t.getOrgNo())).findAny();
        if (!optional.isPresent()) {
            //无权限查询
            return new ArrayList<>();
        }
        LambdaQueryWrapper<CustomerSalesMan> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(CustomerSalesMan::getDeleted, DeletedEnum.UN_DELETE.getValue());
        handleQueryParams(lambdaQueryWrapper, params);
        lambdaQueryWrapper.inSql(CustomerSalesMan::getCustomerNo, "select customer_no  from  bdc_customer where deleted=0 and enterprise_no in ('" + optional.get().getBindingEnterpriseNo() + "')");
        final List<CustomerSalesMan> list = customerSalesManDAO.selectList(lambdaQueryWrapper);
        return list.stream().map(customerSalesMan -> {
            CustomerSalesManVo customerSalesManVo = new CustomerSalesManVo();
            BeanUtils.copyProperties(customerSalesMan, customerSalesManVo);
            return customerSalesManVo;

        }).collect(Collectors.toList());
    }

    /**
     * 获取客户销售人列表
     *
     * @param operationModel
     * @param params
     * @param pageDto
     * @return
     */
    @Override
    public PageVo<CustomerSalesManVo> findPageBySpecifyOrg(OperationModel operationModel, CustomerSalesManQueryBySpecifyDTO params, PageDto pageDto) {
        List<OrganizationVo> organizationList = uimTenantService.getAuthEnterpriseList(operationModel.getEnterpriseNo(), operationModel.getUserNo(), operationModel.getEmployerNo());
        final Optional<OrganizationVo> optional = organizationList.stream().filter(t -> params.getOrgNo().equals(t.getOrgNo())).findAny();
        if (!optional.isPresent()) {
            //无权限查询
            return new PageVo<>();
        }
        Page<CustomerSalesMan> page = PageHelper.startPage(pageDto.getPageIndex(), pageDto.getPageSize(), StringUtils.isBlank(pageDto.getOrderBy()) ? "create_time desc" : pageDto.getOrderBy());
        LambdaQueryWrapper<CustomerSalesMan> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(CustomerSalesMan::getDeleted, DeletedEnum.UN_DELETE.getValue());
        handleQueryParams(lambdaQueryWrapper, params);
        lambdaQueryWrapper.inSql(CustomerSalesMan::getCustomerNo, "select customer_no  from  bdc_customer where deleted=0 and enterprise_no in ('" + optional.get().getBindingEnterpriseNo() + "')");
        customerSalesManDAO.selectList(lambdaQueryWrapper);
        return PageUtils.convertPageVo(page, data -> {
            List<CustomerSalesManVo> result = new ArrayList<>();
            data.forEach(t -> {
                CustomerSalesManVo vo = new CustomerSalesManVo();
                BeanUtils.copyProperties(t, vo);
                result.add(vo);
            });
            return result;
        });
    }


    private void handleQueryParams(LambdaQueryWrapper<CustomerSalesMan> wrapper, CustomerSalesManQueryDTO params) {
        wrapper.in(CollectionUtils.isNotEmpty(params.getCustomerNoList()), CustomerSalesMan::getCustomerNo, params.getCustomerNoList())
                .in(CollectionUtils.isNotEmpty(params.getDeptNoList()), CustomerSalesMan::getOrgNo, params.getDeptNoList())
                .in(CollectionUtils.isNotEmpty(params.getIsDefaultList()), CustomerSalesMan::getIsDefault, params.getIsDefaultList())
                .in(CollectionUtils.isNotEmpty(params.getOrderSpecialistList()), CustomerSalesMan::getOrderSpecialist, params.getOrderSpecialistList())
        ;
    }
}
