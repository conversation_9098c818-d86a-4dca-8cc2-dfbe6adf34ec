package com.yyigou.dsrp.cdc.service.customer;

import com.yyigou.ddc.common.page.PageDto;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.dsrp.cdc.api.company.vo.CompanyShippingAddressVO;
import com.yyigou.dsrp.cdc.api.customer.dto.CustomerLinkAddressByOrgQueryDTO;
import com.yyigou.dsrp.cdc.api.customer.dto.CustomerLinkAddressQueryDTO;
import com.yyigou.dsrp.cdc.api.customer.dto.CustomerLinkAddressSaveByOrgDTO;
import com.yyigou.dsrp.cdc.api.customer.dto.CustomerLinkAddressSaveDTO;
import com.yyigou.dsrp.cdc.common.util.OperationModel;

import java.util.List;

public interface CustomerLinkAddressService {


    /**
     * 跨组织获取客户联系地址列表
     *
     * @param orgNo
     * @return
     */
    List<CompanyShippingAddressVO> getCustomerLinkAddressList(OperationModel operationModel, String customerCode);

    /**
     * 跨组织获取客户联系地址列表(分页)
     *
     * @return
     */
    PageVo<CompanyShippingAddressVO> findCustomerLinkAddressPage(OperationModel operationModel, CustomerLinkAddressQueryDTO params, PageDto pageDto);

    CompanyShippingAddressVO saveCustomerLinkAddress(OperationModel operationModel, CustomerLinkAddressSaveDTO params);

    CompanyShippingAddressVO editCustomerLinkAddress(OperationModel operationModel, CustomerLinkAddressSaveDTO params);


    /**
     * 跨组织获取客户联系地址列表
     *
     * @param customerCode
     * @param orgNo
     * @return
     */
    List<CompanyShippingAddressVO> getCustomerLinkAddressListByOrg(OperationModel operationModel, String customerCode, String orgNo);

    /**
     * 跨组织获取客户联系地址列表(分页)
     *
     * @return
     */
    PageVo<CompanyShippingAddressVO> findCustomerLinkAddressListByOrgPage(OperationModel operationModel, CustomerLinkAddressByOrgQueryDTO params, PageDto pageDto);

    /**
     * 跨组织保存客户联系地址列表
     *
     * @param params
     * @return
     */
    CompanyShippingAddressVO saveCustomerLinkAddressByOrg(OperationModel operationModel, CustomerLinkAddressSaveByOrgDTO params);

    /**
     * 跨组织编辑联系地址
     *
     * @param params
     * @param
     * @return
     */
    CompanyShippingAddressVO editCustomerLinkAddressByOrg(OperationModel operationModel, CustomerLinkAddressSaveByOrgDTO params);

    /**
     * 跨组织删除联系地址
     *
     * @param linkAddressId
     * @param
     * @return
     */
    Boolean deleteCustomerLinkAddressByOrg(OperationModel operationModel, Long linkAddressId);

}
