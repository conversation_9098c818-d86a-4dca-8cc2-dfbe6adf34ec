package com.yyigou.dsrp.cdc.service.customer.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yyigou.ddc.common.util.ValidatorUtils;
import com.yyigou.ddc.services.ddc.uim.vo.OrganizationVo;
import com.yyigou.dsrp.cdc.api.company.vo.CompanyLinkmanVO;
import com.yyigou.dsrp.cdc.api.customer.dto.CustomerLinkmanByOrgDTO;
import com.yyigou.dsrp.cdc.common.enums.CommonStatusEnum;
import com.yyigou.dsrp.cdc.common.enums.DeletedEnum;
import com.yyigou.dsrp.cdc.common.enums.LinkmanTypeEnum;
import com.yyigou.dsrp.cdc.common.util.OperationModel;
import com.yyigou.dsrp.cdc.dao.company.CompanyLinkmanDAO;
import com.yyigou.dsrp.cdc.dao.company.entity.CompanyLinkman;
import com.yyigou.dsrp.cdc.dao.customer.CustomerDAO;
import com.yyigou.dsrp.cdc.dao.customer.entity.Customer;
import com.yyigou.dsrp.cdc.manager.integration.org.UimTenantService;
import com.yyigou.dsrp.cdc.service.customer.CustomerLinkManService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Service
@RequiredArgsConstructor
public class CustomerLinkManServiceImpl implements CustomerLinkManService {
    private final UimTenantService uimTenantService;
    private final CustomerDAO customerDAO;
    private final CompanyLinkmanDAO companyLinkmanDAO;

    /**
     * 跨组织获取客户联系人列表
     *
     * @param operationModel 操作人信息
     * @param supplierCode
     * @param orgNo
     * @return
     */
    @Override
    public List<CompanyLinkmanVO> getCustomerLinkmanListByOrg(OperationModel operationModel, String supplierCode, String orgNo) {
        ValidatorUtils.checkTrueThrowEx(StringUtils.isEmpty(supplierCode), "客户编码不存在");
        ValidatorUtils.checkTrueThrowEx(StringUtils.isEmpty(orgNo), "组织编码不存在");
        //获取有权限的组织列表
        List<OrganizationVo> organizationList = uimTenantService.getAuthEnterpriseList(operationModel.getEnterpriseNo(), operationModel.getUserNo(), operationModel.getEmployerNo());
        final Optional<OrganizationVo> optional = organizationList.stream().filter(t -> orgNo.equals(t.getOrgNo())).findAny();
        if (!optional.isPresent()) {
            //无权限查询
            return new ArrayList<>();
        }
        final OrganizationVo organizationVo = optional.get();
        Customer customer = customerDAO.getCustomerByCustomerCode(organizationVo.getBindingEnterpriseNo(), supplierCode);
        ValidatorUtils.checkTrueThrowEx(customer == null, "客户不存在");
        List<CompanyLinkmanVO> customerLinkmanVoList = new ArrayList<>();
        //客户联系人
        List<CompanyLinkman> customerLinkmanList = companyLinkmanDAO.getCompanyLinkmanListByCompanyNo(customer.getEnterpriseNo(), customer.getCompanyNo(), LinkmanTypeEnum.SALE.getValue(), customer.getCustomerNo());
        if (CollectionUtils.isNotEmpty(customerLinkmanList)) {
            customerLinkmanList.forEach(t -> {
                CompanyLinkmanVO vo = new CompanyLinkmanVO();
                BeanUtils.copyProperties(t, vo);
                customerLinkmanVoList.add(vo);
            });
        }
        return customerLinkmanVoList;
    }

    /**
     * 跨组织保存客户联系人列表
     *
     * @param operationModel
     * @param params
     * @return
     */
    @Override
    public CompanyLinkmanVO saveCustomerLinkmanByOrg(OperationModel operationModel, CustomerLinkmanByOrgDTO params) {
        ValidatorUtils.checkTrueThrowEx(StringUtils.isEmpty(params.getCustomerCode()), "客户编码不存在");
        ValidatorUtils.checkTrueThrowEx(StringUtils.isEmpty(params.getOrgNo()), "组织编码不存在");
        //获取有权限的组织列表
        List<OrganizationVo> organizationList = uimTenantService.getAuthEnterpriseList(operationModel.getEnterpriseNo(), operationModel.getUserNo(), operationModel.getEmployerNo());
        final Optional<OrganizationVo> optional = organizationList.stream().filter(t -> params.getOrgNo().equals(t.getOrgNo())).findAny();
        ValidatorUtils.checkTrueThrowEx(!optional.isPresent(), "无权限操作");

        final OrganizationVo organizationVo = optional.get();
        Customer customer = customerDAO.getCustomerByCustomerCode(organizationVo.getBindingEnterpriseNo(), params.getCustomerCode());
        ValidatorUtils.checkTrueThrowEx(customer == null, "客户不存在");
        //说明这个人有权限操作这个客户
        CompanyLinkman companyLinkman = new CompanyLinkman();
        companyLinkman.setEnterpriseNo(customer.getEnterpriseNo());
        companyLinkman.setCompanyNo(customer.getCompanyNo());
        companyLinkman.setLinkCode(UUID.randomUUID().toString());
        companyLinkman.setLinkman(params.getLinkman());
        companyLinkman.setPosition(params.getPosition());
        companyLinkman.setMobilePhone(params.getMobilePhone());
        companyLinkman.setSex(params.getSex());
        companyLinkman.setFixedPhone(params.getFixedPhone());
        companyLinkman.setQq(params.getQq());
        companyLinkman.setWx(params.getWx());
        companyLinkman.setLinkmanType(LinkmanTypeEnum.SALE.getValue());
        companyLinkman.setStatus(CommonStatusEnum.EFFECTIVE.getValue());
        companyLinkman.setSourceNo(customer.getCustomerNo());
        companyLinkman.setEmail(params.getEmail());
        companyLinkman.setIsDefault(params.getIsDefault());
        companyLinkmanDAO.insert(companyLinkman);
        //如果新保存的这个条记录是默认的，则将之前的默认联系人修改为非默认
        if (new Integer(1).equals(params.getIsDefault())) {
            CompanyLinkman updateDefault = new CompanyLinkman();
            updateDefault.setIsDefault(0);
            companyLinkmanDAO.update(updateDefault, new QueryWrapper<CompanyLinkman>().lambda().eq(CompanyLinkman::getEnterpriseNo, customer.getEnterpriseNo()).eq(CompanyLinkman::getCompanyNo, customer.getCompanyNo())
                    .eq(CompanyLinkman::getSourceNo, customer.getCustomerNo()).eq(CompanyLinkman::getLinkmanType, LinkmanTypeEnum.SALE.getValue()).eq(CompanyLinkman::getIsDefault, 1)
                    .eq(CompanyLinkman::getDeleted, DeletedEnum.UN_DELETE.getValue()).ne(CompanyLinkman::getId, companyLinkman.getId()));
        }
        CompanyLinkmanVO companyLinkmanVO = new CompanyLinkmanVO();
        BeanUtils.copyProperties(companyLinkman, companyLinkmanVO);
        return companyLinkmanVO;
    }

    /**
     * 跨组织删除联系人
     *
     * @param operationModel
     * @param params
     * @return
     */
    @Override
    public CompanyLinkmanVO editCustomerLinkmanByOrg(OperationModel operationModel, CustomerLinkmanByOrgDTO params) {
        ValidatorUtils.checkTrueThrowEx(StringUtils.isEmpty(params.getCustomerCode()), "客户编码不存在");
        ValidatorUtils.checkTrueThrowEx(StringUtils.isEmpty(params.getOrgNo()), "组织编码不存在");
        //获取有权限的组织列表
        List<OrganizationVo> organizationList = uimTenantService.getAuthEnterpriseList(operationModel.getEnterpriseNo(), operationModel.getUserNo(), operationModel.getEmployerNo());
        final Optional<OrganizationVo> optional = organizationList.stream().filter(t -> params.getOrgNo().equals(t.getOrgNo())).findAny();
        ValidatorUtils.checkTrueThrowEx(!optional.isPresent(), "无权限操作");

        final OrganizationVo organizationVo = optional.get();
        Customer customer = customerDAO.getCustomerByCustomerCode(organizationVo.getBindingEnterpriseNo(), params.getCustomerCode());
        ValidatorUtils.checkTrueThrowEx(customer == null, "客户不存在");

        LambdaQueryWrapper<CompanyLinkman> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(CompanyLinkman::getEnterpriseNo, customer.getEnterpriseNo())
                .eq(CompanyLinkman::getCompanyNo, customer.getCompanyNo())
                .eq(CompanyLinkman::getLinkmanType, LinkmanTypeEnum.SALE.getValue())
                .eq(CompanyLinkman::getSourceNo, customer.getCustomerNo())
                .eq(CompanyLinkman::getDeleted, DeletedEnum.UN_DELETE.getValue())
                .eq(CompanyLinkman::getId, params.getId());
        final CompanyLinkman companyLinkman = companyLinkmanDAO.selectOne(wrapper);
        ValidatorUtils.checkTrueThrowEx(companyLinkman == null, "客户联系人不存在");

        companyLinkman.setLinkman(params.getLinkman());
        companyLinkman.setPosition(params.getPosition());
        companyLinkman.setMobilePhone(params.getMobilePhone());
        companyLinkman.setSex(params.getSex());
        companyLinkman.setFixedPhone(params.getFixedPhone());
        companyLinkman.setQq(params.getQq());
        companyLinkman.setWx(params.getWx());
        companyLinkman.setLinkmanType(LinkmanTypeEnum.SALE.getValue());
        companyLinkman.setStatus(params.getStatus());
        companyLinkman.setSourceNo(customer.getCustomerNo());
        companyLinkman.setEmail(params.getEmail());
        companyLinkman.setIsDefault(params.getIsDefault());
        companyLinkmanDAO.updateById(companyLinkman);
        //如果新保存的这个条记录是默认的，则将之前的默认联系人修改为非默认
        if (new Integer(1).equals(params.getIsDefault())) {
            CompanyLinkman updateDefault = new CompanyLinkman();
            updateDefault.setIsDefault(0);
            companyLinkmanDAO.update(updateDefault, new QueryWrapper<CompanyLinkman>().lambda().eq(CompanyLinkman::getEnterpriseNo, customer.getEnterpriseNo()).eq(CompanyLinkman::getCompanyNo, customer.getCompanyNo())
                    .eq(CompanyLinkman::getSourceNo, customer.getCustomerNo()).eq(CompanyLinkman::getLinkmanType, LinkmanTypeEnum.SALE.getValue()).eq(CompanyLinkman::getIsDefault, 1)
                    .eq(CompanyLinkman::getDeleted, DeletedEnum.UN_DELETE.getValue()).ne(CompanyLinkman::getId, companyLinkman.getId()));
        }
        CompanyLinkmanVO companyLinkmanVO = new CompanyLinkmanVO();
        BeanUtils.copyProperties(companyLinkman, companyLinkmanVO);
        return companyLinkmanVO;
    }

    /**
     * 跨组织删除联系人
     *
     * @param operationModel
     * @param linkmanId
     * @return
     */
    @Override
    public Boolean deleteCustomerLinkmanByOrg(OperationModel operationModel, Long linkmanId) {
        //获取有权限的组织列表
        List<OrganizationVo> organizationList = uimTenantService.getAuthEnterpriseList(operationModel.getEnterpriseNo(), operationModel.getUserNo(), operationModel.getEmployerNo());
        ValidatorUtils.checkTrueThrowEx(CollectionUtils.isEmpty(organizationList), "无权限删除联系人");
        final CompanyLinkman companyLinkman = companyLinkmanDAO.selectById(linkmanId);
        ValidatorUtils.checkTrueThrowEx(companyLinkman == null, "联系人不存在");
        Customer customer = customerDAO.getCustomerByNoEnterpriseNo(companyLinkman.getSourceNo());
        ValidatorUtils.checkTrueThrowEx(customer == null, "客户不存在");
        final Optional<OrganizationVo> optional = organizationList.stream().filter(t -> customer.getEnterpriseNo().equals(t.getBindingEnterpriseNo())).findAny();
        ValidatorUtils.checkTrueThrowEx(!optional.isPresent(), "无权限删除联系人");
        companyLinkman.setDeleted(DeletedEnum.DELETED.getValue());
        companyLinkmanDAO.updateById(companyLinkman);
        return Boolean.TRUE;
    }
}
