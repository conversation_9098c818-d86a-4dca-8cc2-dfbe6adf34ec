package com.yyigou.dsrp.cdc.service.listener.v2;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yyigou.ddc.common.util.ValidatorUtils;
import com.yyigou.ddc.services.ddc.uim.vo.OrganizationVo;
import com.yyigou.ddc.services.mq.dto.JmsHeadersDto;
import com.yyigou.ddc.services.mq.manager.MessageQueueManager;
import com.yyigou.dsrp.cdc.client.v2.customer.request.CustomerAssignDocExeRequest;
import com.yyigou.dsrp.cdc.client.v2.customer.request.CustomerAssignExeRequest;
import com.yyigou.dsrp.cdc.client.v2.customer.response.CustomerAssignExeResponse;
import com.yyigou.dsrp.cdc.manager.integration.dlog.BusinessLogService;
import com.yyigou.dsrp.cdc.manager.integration.uim.OrganizationService;
import com.yyigou.dsrp.cdc.manager.integration.uim.response.UimOrganizationResponse;
import com.yyigou.dsrp.cdc.service.v2.customer.CustomerV2Service;
import lombok.extern.slf4j.Slf4j;
import org.apache.activemq.command.ActiveMQTopic;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jms.listener.SessionAwareMessageListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import javax.jms.JMSException;
import javax.jms.Message;
import javax.jms.Session;
import javax.jms.TextMessage;
import java.util.Collections;
import java.util.List;

import static com.yyigou.dsrp.cdc.model.constant.CdcMqConstant.CDC_CUSTOMER_ASSIGN_RESULT_TOPIC_V2;

/**
 * 客户分派监听
 */
@Component("customerV2AssignListener")
@Slf4j
public class CustomerV2AssignListener implements SessionAwareMessageListener {
    @Resource
    private CustomerV2Service customerV2Service;

    @Resource
    private OrganizationService organizationService;

    @Resource
    private TransactionTemplate transactionTemplate;

    @Resource
    private BusinessLogService businessLogService;

    @Resource
    private MessageQueueManager messageQueueManager;


    /**
     * com.yyigou.dsrp.cdc.model.constant.CDC_CUSTOMER_ASSIGN_TOPIC_V2
     * 消息格式:
     * enterpriseNo：集团租户编号
     * useOrgBindingEnterpriseNo：（对应scs租户）
     * useOrgNo：使用组织编号（对应scs租户对应的业务单元）
     * customerCode：客户编码
     * <p>
     *
     * <p>
     * com.yyigou.dsrp.cdc.model.constant.CDC_CUSTOMER_ASSIGN_RESULT_TOPIC_V2
     * 消息格式：
     * enterpriseNo：集团租户编号
     * useOrgNo：使用组织编号（对应scs租户对应的业务单元）
     * useOrgBindingEnterpriseNo：（对应scs租户）
     * customerCode：客户编码
     * quoteSuccess:true
     * quoteMessage:"xxxx"
     *
     * @param message
     * @param session
     * @throws JMSException
     */
    @Override
    public void onMessage(Message message, Session session) throws JMSException {
        //  获取消息
        TextMessage textMessage = (TextMessage) message;

        JSONObject result = new JSONObject();

        try {
            //客户引用模型
            JSONObject params = JSONObject.parseObject(textMessage.getText());
            String enterpriseNo = params.getString("enterpriseNo");
            String useOrgBindingEnterpriseNo = params.getString("useOrgBindingEnterpriseNo");
            String useOrgNo = params.getString("useOrgNo");
            String customerCode = params.getString("customerCode");

            ValidatorUtils.checkEmptyThrowEx(enterpriseNo, "租户编号不能为空");
            ValidatorUtils.checkEmptyThrowEx(useOrgBindingEnterpriseNo, "使用组织绑定租户编号不能为空");
            ValidatorUtils.checkEmptyThrowEx(useOrgNo, "使用组织编号不能为空");
            ValidatorUtils.checkEmptyThrowEx(customerCode, "客户编码不能为空");

            result.put("enterpriseNo", enterpriseNo);
            result.put("useOrgBindingEnterpriseNo", useOrgBindingEnterpriseNo);
            result.put("useOrgNo", useOrgNo);
            result.put("customerCode", customerCode);

            result.put("quoteSuccess", Boolean.FALSE);
            result.put("quoteMessage", "分派失败");

            OrganizationVo organizationVo = organizationService.getOrgByEnterpriseNo(enterpriseNo, enterpriseNo);
            if (null == organizationVo) {
                log.error("集团组织编号不存在");
                throw new RuntimeException("集团组织编号不存在");
            }

            String groupOrgNo = organizationVo.getOrgNo();

            // 分派
            List<CustomerAssignExeResponse> customerAssignExeResponseList = transactionTemplate.execute(status -> {
                CustomerAssignExeRequest customerAssignExeRequest = new CustomerAssignExeRequest();
                customerAssignExeRequest.setEnterpriseNo(enterpriseNo);
                customerAssignExeRequest.setManageOrgNo(groupOrgNo);
                CustomerAssignDocExeRequest customerAssignDocExeRequest = new CustomerAssignDocExeRequest();
                customerAssignDocExeRequest.setCustomerCode(customerCode);
                customerAssignDocExeRequest.setUseOrgNoList(Collections.singletonList(useOrgNo));
                return customerV2Service.assignCustomer(customerAssignExeRequest);
            });

            if (CollectionUtils.isNotEmpty(customerAssignExeResponseList)) {
                for (CustomerAssignExeResponse customerAssignExeResponse : customerAssignExeResponseList) {
                    if (customerAssignExeResponse.isSuccess()) {
                        result.put("quoteSuccess", Boolean.TRUE);
                        result.put("quoteMessage", "分派成功");

                        break;
                    }
                }
            }
        } catch (Exception e) {
            log.error("客户档案分派：消费失败,消息内容：{}, 失败原因:{}", textMessage.getText(), e.getMessage(), e);
        } finally {
            // 主动发送消息回执, 告知broker消息已经被消费了
            message.acknowledge();

            try {
                result.put("quoteSuccess", Boolean.TRUE);
                result.put("quoteMessage", "分派成功");
                JmsHeadersDto jmsHeadersDto = new JmsHeadersDto();
                jmsHeadersDto.setSessionTransacted(true);
                messageQueueManager.produceMessage(new ActiveMQTopic(CDC_CUSTOMER_ASSIGN_RESULT_TOPIC_V2), JSON.toJSONString(result), null, jmsHeadersDto);
            } catch (Exception e) {
                log.error("发送消息失败", e);
            }
        }
    }
}
