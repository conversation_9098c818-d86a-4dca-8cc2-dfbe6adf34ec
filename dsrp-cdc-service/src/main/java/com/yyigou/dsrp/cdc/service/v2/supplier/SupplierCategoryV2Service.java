package com.yyigou.dsrp.cdc.service.v2.supplier;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yyigou.ddc.common.page.PageDto;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.dsrp.cdc.api.supply.dto.SupplierExternalSaveDTO;
import com.yyigou.dsrp.cdc.api.v2.supplier.vo.SupplierCategoryTreeVO;
import com.yyigou.dsrp.cdc.api.v2.supplier.vo.SupplierCategoryVO;
import com.yyigou.dsrp.cdc.common.util.OperationModel;
import com.yyigou.dsrp.cdc.dao.v2.supplier.entity.SupplierCategoryV2;
import com.yyigou.dsrp.cdc.model.v2.supplier.req.*;

import java.util.List;

public interface SupplierCategoryV2Service extends IService<SupplierCategoryV2> {

    List<SupplierCategoryTreeVO> queryTree(OperationModel operationModel, SupplierCategoryQueryTreeReq queryTreeReq);

    List<SupplierCategoryTreeVO> queryManageTree(OperationModel operationModel, SupplierCategoryQueryCategoryTreeReq queryTreeReq);

    List<SupplierCategoryTreeVO> queryUseTree(OperationModel operationModel, SupplierCategoryQueryUseCategoryTreeReq queryTreeReq);

    List<SupplierCategoryTreeVO> queryReferTree(OperationModel operationModel, SupplierCategoryQuerySolutionTreeReq queryTreeReq);

    PageVo<SupplierCategoryVO> queryListPage(OperationModel operationModel, SupplierCategoryQueryListPageReq queryListReq, PageDto pageDto);

    Boolean checkUniqueName(OperationModel operationModel, String no, String name, String parentNo);

    Boolean checkUniqueCode(OperationModel operationModel, String no, String code);

    Boolean checkUseOrgRemoval(OperationModel operationModel, SupplierCategoryCheckUseOrgRemovalReq params);

    SupplierCategoryVO saveSupplierCategory(OperationModel operationModel, SupplierCategorySaveReq supplierCategorySaveReq);

    SupplierCategoryVO getSupplierCategory(OperationModel operationModel, SupplierCategoryGetReq supplierCategoryGetReq);

    SupplierCategoryVO updateSupplierCategory(OperationModel operationModel, SupplierCategoryUpdateReq supplierCategoryUpdateReq);

    Boolean deleteSupplierCategory(OperationModel operationModel, SupplierCategoryDeleteReq supplierCategoryDeleteReq);

    Boolean changeSupplierCategoryStatus(OperationModel operationModel, SupplierCategoryChangeStatusReq supplierCategoryChangeStatusReq);

    SupplierCategoryV2 getByNo(String enterpriseNo, String no);

    List<SupplierCategoryV2> getByNoList(String enterpriseNo, List<String> noList);

    void createAndUpdateCategoryForDA(OperationModel operationModel, List<SupplierExternalSaveDTO> params);

    PageVo<SupplierCategoryVO> queryFLatListPage(OperationModel operationModel, SupplierCategoryQueryFlagListPageReq queryListReq, PageDto pageDTO);
}
