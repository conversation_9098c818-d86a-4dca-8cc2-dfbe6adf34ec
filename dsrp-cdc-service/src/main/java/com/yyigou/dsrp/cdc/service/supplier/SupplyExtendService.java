package com.yyigou.dsrp.cdc.service.supplier;

import com.yyigou.dsrp.cdc.client.supplier.request.SupplierManQueryRequest;
import com.yyigou.dsrp.cdc.client.supplier.response.SupplierSalesManResponse;
import com.yyigou.dsrp.gcs.client.executeRecord.response.ExecuteRecordResponse;

import java.util.List;

public interface SupplyExtendService {

    void startSupplierAssignTask(ExecuteRecordResponse executeRecord);
    List<SupplierSalesManResponse> querySupplierMan(String enterpriseNo, String supplierNo, String orgNo, String orderManNo, List<Integer> orderSpecialist);


    List<SupplierSalesManResponse> querySupplierManList(SupplierManQueryRequest params);

}
