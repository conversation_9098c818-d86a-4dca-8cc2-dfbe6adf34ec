package com.yyigou.dsrp.cdc.service.company;

import com.yyigou.ddc.common.util.ValidatorUtils;
import com.yyigou.ddc.services.ddc.dict.vo.AreaCodeVo;
import com.yyigou.dsrp.cdc.client.company.response.CompanyLinkmanResponse;
import com.yyigou.dsrp.cdc.client.company.response.CompanyShippingAddressResponse;
import com.yyigou.dsrp.cdc.common.enums.LinkmanTypeEnum;
import com.yyigou.dsrp.cdc.dao.company.CompanyLinkmanDAO;
import com.yyigou.dsrp.cdc.dao.company.CompanyShippingAddressDAO;
import com.yyigou.dsrp.cdc.dao.company.entity.CompanyLinkman;
import com.yyigou.dsrp.cdc.dao.company.entity.CompanyShippingAddress;
import com.yyigou.dsrp.cdc.manager.integration.dict.DictEnterpriseService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
public class CompanyExtendService {
    private final CompanyLinkmanDAO companyLinkmanDAO;
    private final CompanyShippingAddressDAO companyShippingAddressDAO;
    private final DictEnterpriseService dictEnterpriseService;


    /**
     * 获取联系人列表
     *
     * @param enterpriseNo
     * @param typeEnum
     * @param sourceList
     * @return
     */
    public List<CompanyLinkmanResponse> getLinkmanList(String enterpriseNo, LinkmanTypeEnum typeEnum, List<String> sourceList) {
        ValidatorUtils.checkEmptyThrowEx(enterpriseNo, "租户编号不能为空");
        ValidatorUtils.checkEmptyThrowEx(enterpriseNo, "查询类型不能为空");
        ValidatorUtils.checkEmptyThrowEx(sourceList, "查询源不能为空");
        //客户联系人
        List<CompanyLinkmanResponse> customerLinkmanVoList = new ArrayList<>();
        List<CompanyLinkman> customerLinkmanList = companyLinkmanDAO.getCompanyLinkmanListBySourceNoList(enterpriseNo, typeEnum.getValue(), sourceList);
        if (CollectionUtils.isNotEmpty(customerLinkmanList)) {
            customerLinkmanList.forEach(t -> {
                CompanyLinkmanResponse vo = new CompanyLinkmanResponse();
                BeanUtils.copyProperties(t, vo);
                customerLinkmanVoList.add(vo);
            });
        }
        return customerLinkmanVoList;
    }


    /**
     * 获取地址列表
     *
     * @param enterpriseNo
     * @param typeEnum
     * @param sourceList
     * @return
     */
    public List<CompanyShippingAddressResponse> getLinkAddressList(String enterpriseNo, LinkmanTypeEnum typeEnum, List<String> sourceList) {
        ValidatorUtils.checkEmptyThrowEx(enterpriseNo, "租户编号不能为空");
        ValidatorUtils.checkEmptyThrowEx(enterpriseNo, "查询类型不能为空");
        ValidatorUtils.checkEmptyThrowEx(sourceList, "查询源不能为空");
        List<CompanyShippingAddressResponse> customerLinkmanVoList = new ArrayList<>();
        //客户联系人
        List<CompanyShippingAddress> customerAddressList = companyShippingAddressDAO.getCompanyShippingAddressBySourceNoList(enterpriseNo, typeEnum.getValue(), sourceList);
        if (CollectionUtils.isNotEmpty(customerAddressList)) {
            final List<String> regionCodeList = customerAddressList.stream().map(CompanyShippingAddress::getRegionCode).filter(t -> !StringUtils.isEmpty(t)).collect(Collectors.toList());
            Map<String, AreaCodeVo> areaMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(regionCodeList)) {
                List<AreaCodeVo> areaCodeList = dictEnterpriseService.findAreaByCodes(regionCodeList);
                areaMap = areaCodeList.stream().collect(Collectors.toMap(AreaCodeVo::getAreaCode, Function.identity()));
            }
            Map<String, AreaCodeVo> finalAreaMap = areaMap;
            customerAddressList.forEach(t -> {
                CompanyShippingAddressResponse vo = new CompanyShippingAddressResponse();
                BeanUtils.copyProperties(t, vo);
                if (finalAreaMap.containsKey(t.getRegionCode())) {
//                    vo.setRegionName(finalAreaMap.get(t.getRegionCode()).getAreaName());
                    vo.setRegionFullName(finalAreaMap.get(t.getRegionCode()).getAreaName());
                }
                customerLinkmanVoList.add(vo);
            });
        }
        return customerLinkmanVoList;
    }

}
