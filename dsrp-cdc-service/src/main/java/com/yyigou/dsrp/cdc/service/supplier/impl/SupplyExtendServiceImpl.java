package com.yyigou.dsrp.cdc.service.supplier.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yyigou.ddc.common.util.ValidatorUtils;
import com.yyigou.ddc.services.ddc.uim.vo.EmployeeVo;
import com.yyigou.ddc.services.mq.dto.JmsHeadersDto;
import com.yyigou.ddc.services.mq.manager.MessageQueueManager;
import com.yyigou.dsrp.cdc.client.supplier.request.SupplierManQueryRequest;
import com.yyigou.dsrp.cdc.client.supplier.response.SupplierSalesManResponse;
import com.yyigou.dsrp.cdc.common.enums.DeletedEnum;
import com.yyigou.dsrp.cdc.common.util.OperationModel;
import com.yyigou.dsrp.cdc.dao.supplier.SupplierDAO;
import com.yyigou.dsrp.cdc.dao.supplier.SupplierOrderManDAO;
import com.yyigou.dsrp.cdc.dao.supplier.entity.Supplier;
import com.yyigou.dsrp.cdc.dao.supplier.entity.SupplierOrderMan;
import com.yyigou.dsrp.cdc.manager.integration.uim.EmployeeService;
import com.yyigou.dsrp.cdc.service.listener.model.SupplierAssignModel;
import com.yyigou.dsrp.cdc.service.supplier.SupplyExtendService;
import com.yyigou.dsrp.gcs.client.executeRecord.response.ExecuteRecordResponse;
import com.yyigou.dsrp.gcs.common.enums.executeRecord.ExecuteRecordExcuteTypeEnum;
import com.yyigou.dsrp.gcs.model.api.constant.MqConstant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.activemq.ScheduledMessage;
import org.apache.activemq.command.ActiveMQTopic;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class SupplyExtendServiceImpl implements SupplyExtendService {

    private final SupplierOrderManDAO supplierOrderManDAO;
    private final EmployeeService employeeService;
    private final SupplierDAO supplierDAO;
    private final MessageQueueManager messageQueueManager;

    @Override
    public void startSupplierAssignTask(ExecuteRecordResponse executeRecord){
        OperationModel operationModel = new OperationModel();
        operationModel.setEnterpriseNo(executeRecord.getApplyEnterpriseNo());
        operationModel.setGroupTenantNo(executeRecord.getGroupEnterpriseNo());
        operationModel.setEmployerNo(executeRecord.getCreateNo());
        operationModel.setUserName(executeRecord.getCreateName());
        SupplierAssignModel model = new SupplierAssignModel();
        model.setSupplierCode(executeRecord.getObjCode());
        model.setOperationModel(operationModel);
        model.setGroupEnterpriseNo(executeRecord.getGroupEnterpriseNo());
        model.setSubEnterpriseNo(executeRecord.getApplyEnterpriseNo());
        model.setExecuteRecordExecuteTypeEnum(ExecuteRecordExcuteTypeEnum.getByValue(executeRecord.getExecuteType()));
        model.setRecordId(executeRecord.getId());
        log.warn("供应商{}开始分派", JSON.toJSONString(model));

        try {
            JmsHeadersDto jmsHeadersDto = new JmsHeadersDto();
            jmsHeadersDto.setSessionTransacted(true);
            Map<String, Object> customPropMap = new HashMap<>();
            customPropMap.put(ScheduledMessage.AMQ_SCHEDULED_DELAY, 5000L);
            messageQueueManager.produceMessage(new ActiveMQTopic(MqConstant.CDC_SUPPLIER_ASSIGN_TOPIC), JSON.toJSONString(model), customPropMap, jmsHeadersDto);
        } catch (Exception e) {
            log.error("分派供应商失败", e);
        }
    }

    @Override
    public List<SupplierSalesManResponse> querySupplierMan(String enterpriseNo, String supplierNo, String orgNo, String orderManNo, List<Integer> orderSpecialist) {
        ValidatorUtils.checkTrueThrowEx(StringUtils.isEmpty(enterpriseNo), "租户编号不存在");
        final Supplier suppler = supplierDAO.getSupplierByNo(enterpriseNo, supplierNo);
        ValidatorUtils.checkTrueThrowEx(suppler == null, "供应商不存在");
        List<SupplierSalesManResponse> result = new ArrayList<>();
        LambdaQueryWrapper<SupplierOrderMan> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(SupplierOrderMan::getSupplierNo, supplierNo)
                .eq(StringUtils.isNotEmpty(orderManNo), SupplierOrderMan::getOrderManNo, orderManNo)
                .eq(StringUtils.isNotEmpty(orgNo), SupplierOrderMan::getOrgNo, orgNo)
                .in(CollectionUtils.isNotEmpty(orderSpecialist), SupplierOrderMan::getOrderSpecialist, orderSpecialist)
                .eq(SupplierOrderMan::getDeleted, DeletedEnum.UN_DELETE.getValue());
        final List<SupplierOrderMan> salesManList = supplierOrderManDAO.selectList(wrapper);
        if (CollectionUtils.isNotEmpty(salesManList)) {
            final List<String> salesManNoList = salesManList.stream().map(SupplierOrderMan::getOrderManNo).collect(Collectors.toList());
            Map<String, EmployeeVo> employeeMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(salesManNoList)) {
                final List<EmployeeVo> employeeList = employeeService.getEmployeeList(enterpriseNo, salesManNoList);
                employeeMap = employeeList.stream().collect(Collectors.toMap(EmployeeVo::getEmployeeNo, Function.identity()));
            }
            Map<String, EmployeeVo> finalEmployeeMap = employeeMap;
            salesManList.forEach(t -> {
                SupplierSalesManResponse supplierSalesManVO = new SupplierSalesManResponse();
                BeanUtils.copyProperties(t, supplierSalesManVO);
                supplierSalesManVO.setOrgNo(t.getOrgNo());
                supplierSalesManVO.setOrgName(t.getOrgName());
                if (finalEmployeeMap.containsKey(t.getOrderManNo())) {
                    supplierSalesManVO.setMobile(finalEmployeeMap.get(t.getOrderManNo()).getMobile());
                }
                result.add(supplierSalesManVO);
            });
        }
        return result;
    }


    @Override
    public List<SupplierSalesManResponse> querySupplierManList(SupplierManQueryRequest params) {
        ValidatorUtils.checkTrueThrowEx(StringUtils.isEmpty(params.getEnterpriseNo()), "租户编号不存在");
        List<SupplierSalesManResponse> result = new ArrayList<>();
        LambdaQueryWrapper<SupplierOrderMan> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(StringUtils.isNotEmpty(params.getSupplierNo()), SupplierOrderMan::getSupplierNo, params.getSupplierNo())
                .in(CollectionUtils.isNotEmpty(params.getSupplierNoList()), SupplierOrderMan::getSupplierNo, params.getSupplierNoList())
                .eq(StringUtils.isNotEmpty(params.getOrderManNo()), SupplierOrderMan::getOrderManNo, params.getOrderManNo())
                .eq(StringUtils.isNotEmpty(params.getOrgNo()), SupplierOrderMan::getOrgNo, params.getOrgNo())
                .in(CollectionUtils.isNotEmpty(params.getOrderSpecialist()), SupplierOrderMan::getOrderSpecialist, params.getOrderSpecialist())
                .eq(SupplierOrderMan::getDeleted, DeletedEnum.UN_DELETE.getValue());
        final List<SupplierOrderMan> salesManList = supplierOrderManDAO.selectList(wrapper);
        if (CollectionUtils.isNotEmpty(salesManList)) {
            final List<String> salesManNoList = salesManList.stream().map(SupplierOrderMan::getOrderManNo).collect(Collectors.toList());
            Map<String, EmployeeVo> employeeMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(salesManNoList)) {
                final List<EmployeeVo> employeeList = employeeService.getEmployeeList(params.getEnterpriseNo(), salesManNoList);
                employeeMap = employeeList.stream().collect(Collectors.toMap(EmployeeVo::getEmployeeNo, Function.identity()));
            }
            Map<String, EmployeeVo> finalEmployeeMap = employeeMap;
            salesManList.forEach(t -> {
                SupplierSalesManResponse supplierSalesManVO = new SupplierSalesManResponse();
                BeanUtils.copyProperties(t, supplierSalesManVO);
                supplierSalesManVO.setOrgNo(t.getOrgNo());
                supplierSalesManVO.setOrgName(t.getOrgName());
                if (finalEmployeeMap.containsKey(t.getOrderManNo())) {
                    supplierSalesManVO.setMobile(finalEmployeeMap.get(t.getOrderManNo()).getMobile());
                }
                result.add(supplierSalesManVO);
            });
        }
        return result;
    }
}
