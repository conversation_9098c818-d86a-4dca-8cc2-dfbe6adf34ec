package com.yyigou.dsrp.cdc.service.customer.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.yyigou.ddc.common.page.PageDto;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.ddc.common.util.ValidatorUtils;
import com.yyigou.ddc.services.ddc.dict.vo.AreaCodeVo;
import com.yyigou.ddc.services.ddc.uim.vo.OrganizationVo;
import com.yyigou.dsrp.cdc.api.company.vo.CompanyShippingAddressVO;
import com.yyigou.dsrp.cdc.api.customer.dto.CustomerLinkAddressByOrgQueryDTO;
import com.yyigou.dsrp.cdc.api.customer.dto.CustomerLinkAddressQueryDTO;
import com.yyigou.dsrp.cdc.api.customer.dto.CustomerLinkAddressSaveByOrgDTO;
import com.yyigou.dsrp.cdc.api.customer.dto.CustomerLinkAddressSaveDTO;
import com.yyigou.dsrp.cdc.common.enums.CommonStatusEnum;
import com.yyigou.dsrp.cdc.common.enums.DeletedEnum;
import com.yyigou.dsrp.cdc.common.enums.LinkmanTypeEnum;
import com.yyigou.dsrp.cdc.common.util.OperationModel;
import com.yyigou.dsrp.cdc.dao.company.CompanyShippingAddressDAO;
import com.yyigou.dsrp.cdc.dao.company.entity.CompanyShippingAddress;
import com.yyigou.dsrp.cdc.dao.customer.CustomerDAO;
import com.yyigou.dsrp.cdc.dao.customer.entity.Customer;
import com.yyigou.dsrp.cdc.manager.integration.dict.DictEnterpriseService;
import com.yyigou.dsrp.cdc.manager.integration.org.UimTenantService;
import com.yyigou.dsrp.cdc.service.customer.CustomerLinkAddressService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class CustomerLinkAddressServiceImpl implements CustomerLinkAddressService {
    private final UimTenantService uimTenantService;
    private final CustomerDAO customerDAO;
    private final CompanyShippingAddressDAO companyShippingAddressDAO;
    private final DictEnterpriseService dictEnterpriseService;


    /**
     * 获取客户联系地址列表
     *
     * @param operationModel
     * @param customerCode
     * @return
     */
    @Override
    public List<CompanyShippingAddressVO> getCustomerLinkAddressList(OperationModel operationModel, String customerCode) {
        ValidatorUtils.checkTrueThrowEx(StringUtils.isEmpty(customerCode), "客户编码不存在");
        Customer customer = customerDAO.getCustomerByCustomerCode(operationModel.getEnterpriseNo(), customerCode);
        ValidatorUtils.checkTrueThrowEx(customer == null, "客户不存在");
        //客户联系人
        List<CompanyShippingAddress> customerAddressList = companyShippingAddressDAO.getCompanyShippingAddressByCompanyNo(customer.getEnterpriseNo(), customer.getCompanyNo(), LinkmanTypeEnum.SALE.getValue(), customer.getCustomerNo());
        return resultToVo(customerAddressList, operationModel.getEnterpriseNo());
    }

    /**
     * 获取客户联系地址列表(分页)
     *
     * @param operationModel
     * @param params
     * @param pageDto
     * @return
     */
    @Override
    public PageVo<CompanyShippingAddressVO> findCustomerLinkAddressPage(OperationModel operationModel, CustomerLinkAddressQueryDTO params, PageDto pageDto) {
        ValidatorUtils.checkTrueThrowEx(StringUtils.isEmpty(params.getCustomerCode()), "客户编码不存在");
        Customer customer = customerDAO.getCustomerByCustomerCode(operationModel.getEnterpriseNo(), params.getCustomerCode());
        ValidatorUtils.checkTrueThrowEx(customer == null, "客户不存在");
        //客户联系人
        Page<CompanyShippingAddress> pageInfo = PageHelper.startPage(pageDto.getPageIndex(), pageDto.getPageSize());
        companyShippingAddressDAO.getCompanyShippingAddressByCompanyNoAddressType(customer.getEnterpriseNo(),
                customer.getCompanyNo(), LinkmanTypeEnum.SALE.getValue(), customer.getCustomerNo(), params.getAddressTypeList(), params.getAddressType());
        List<CompanyShippingAddressVO> customerLinkmanVoList = resultToVo(pageInfo.getResult(), operationModel.getEnterpriseNo());
        return new PageVo<>(pageInfo.getPageNum(), pageInfo.getPageSize(), pageInfo.getTotal(), customerLinkmanVoList);
    }

    /**
     * 跨组织获取客户联系地址列表
     *
     * @param operationModel
     * @param customerCode
     * @param orgNo
     * @return
     */
    @Override
    public List<CompanyShippingAddressVO> getCustomerLinkAddressListByOrg(OperationModel operationModel, String customerCode, String orgNo) {
        ValidatorUtils.checkTrueThrowEx(StringUtils.isEmpty(customerCode), "客户编码不存在");
        ValidatorUtils.checkTrueThrowEx(StringUtils.isEmpty(orgNo), "组织编码不存在");
        //获取有权限的组织列表
        List<OrganizationVo> organizationList = uimTenantService.getAuthEnterpriseList(operationModel.getEnterpriseNo(), operationModel.getUserNo(), operationModel.getEmployerNo());
        final Optional<OrganizationVo> optional = organizationList.stream().filter(t -> orgNo.equals(t.getOrgNo())).findAny();
        if (!optional.isPresent()) {
            //无权限查询
            return new ArrayList<>();
        }
        final OrganizationVo organizationVo = optional.get();
        Customer customer = customerDAO.getCustomerByCustomerCode(organizationVo.getBindingEnterpriseNo(), customerCode);
        ValidatorUtils.checkTrueThrowEx(customer == null, "客户不存在");
        //客户联系人
        List<CompanyShippingAddress> customerAddressList = companyShippingAddressDAO.getCompanyShippingAddressByCompanyNo(customer.getEnterpriseNo(), customer.getCompanyNo(), LinkmanTypeEnum.SALE.getValue(), customer.getCustomerNo());
        return resultToVo(customerAddressList, organizationVo.getBindingEnterpriseNo());
    }


    @Override
    public CompanyShippingAddressVO saveCustomerLinkAddress(OperationModel operationModel, CustomerLinkAddressSaveDTO params) {
        Customer customer = customerDAO.getCustomerByCustomerCode(operationModel.getEnterpriseNo(), params.getCustomerCode());
        ValidatorUtils.checkTrueThrowEx(customer == null, "客户不存在");
        //说明这个人有权限操作这个客户
        CompanyShippingAddress shippingAddress = new CompanyShippingAddress();
        shippingAddress.setEnterpriseNo(customer.getEnterpriseNo());
        shippingAddress.setCompanyNo(customer.getCompanyNo());
        shippingAddress.setLinkAddressCode(UUID.randomUUID().toString());
        shippingAddress.setSourceNo(customer.getCustomerNo());
        shippingAddress.setStatus(CommonStatusEnum.EFFECTIVE.getValue());
        shippingAddress.setLinkaddType(LinkmanTypeEnum.SALE.getValue());
        shippingAddress.setReceiveUser(params.getReceiveUser());
        shippingAddress.setReceivePhone(params.getReceivePhone());
        shippingAddress.setRegionCode(params.getRegionCode());
        shippingAddress.setRegionName(params.getRegionName());
        shippingAddress.setReceiveAddr(params.getReceiveAddr());
        shippingAddress.setIsDefault(params.getIsDefault());
        shippingAddress.setAddressDesc(params.getAddressDesc());
        shippingAddress.setAddressType(params.getAddressType());
        companyShippingAddressDAO.insert(shippingAddress);

        //如果新保存的这个条记录是默认的，则将之前的默认联系人修改为非默认
        if (new Integer(1).equals(params.getIsDefault())) {
            CompanyShippingAddress updateDefault = new CompanyShippingAddress();
            updateDefault.setIsDefault(0);
            companyShippingAddressDAO.update(updateDefault, new QueryWrapper<CompanyShippingAddress>().lambda().eq(CompanyShippingAddress::getEnterpriseNo, customer.getEnterpriseNo()).eq(CompanyShippingAddress::getCompanyNo, customer.getCompanyNo())
                    .eq(CompanyShippingAddress::getSourceNo, customer.getCustomerNo()).eq(CompanyShippingAddress::getLinkaddType, LinkmanTypeEnum.SALE.getValue()).eq(CompanyShippingAddress::getIsDefault, 1)
                    .eq(CompanyShippingAddress::getDeleted, DeletedEnum.UN_DELETE.getValue()).ne(CompanyShippingAddress::getId, shippingAddress.getId()));
        }
        CompanyShippingAddressVO shippingAddressVO = new CompanyShippingAddressVO();
        BeanUtils.copyProperties(shippingAddress, shippingAddressVO);
        return shippingAddressVO;
    }

    @Override
    public CompanyShippingAddressVO editCustomerLinkAddress(OperationModel operationModel, CustomerLinkAddressSaveDTO params) {
        Customer customer = customerDAO.getCustomerByCustomerCode(operationModel.getEnterpriseNo(), params.getCustomerCode());
        ValidatorUtils.checkTrueThrowEx(customer == null, "客户不存在");

        LambdaQueryWrapper<CompanyShippingAddress> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(CompanyShippingAddress::getEnterpriseNo, customer.getEnterpriseNo())
                .eq(CompanyShippingAddress::getCompanyNo, customer.getCompanyNo())
                .eq(CompanyShippingAddress::getLinkaddType, LinkmanTypeEnum.SALE.getValue())
                .eq(CompanyShippingAddress::getSourceNo, customer.getCustomerNo())
                .eq(CompanyShippingAddress::getDeleted, DeletedEnum.UN_DELETE.getValue())
                .eq(CompanyShippingAddress::getId, params.getId());
        final CompanyShippingAddress companyShippingAddress = companyShippingAddressDAO.selectOne(wrapper);
        ValidatorUtils.checkTrueThrowEx(companyShippingAddress == null, "客户联系人不存在");

        companyShippingAddress.setReceiveUser(params.getReceiveUser());
        companyShippingAddress.setRegionName(params.getRegionName());
        companyShippingAddress.setReceivePhone(params.getReceivePhone());
        companyShippingAddress.setRegionCode(params.getRegionCode());
        companyShippingAddress.setReceiveAddr(params.getReceiveAddr());
        companyShippingAddress.setIsDefault(params.getIsDefault());
        companyShippingAddress.setAddressDesc(params.getAddressDesc());
        companyShippingAddress.setAddressType(params.getAddressType());
        companyShippingAddressDAO.updateById(companyShippingAddress);
        //如果新保存的这个条记录是默认的，则将之前的默认联系人修改为非默认
        if (new Integer(1).equals(params.getIsDefault())) {
            CompanyShippingAddress updateDefault = new CompanyShippingAddress();
            updateDefault.setIsDefault(0);
            companyShippingAddressDAO.update(updateDefault, new QueryWrapper<CompanyShippingAddress>().lambda().eq(CompanyShippingAddress::getEnterpriseNo, customer.getEnterpriseNo()).eq(CompanyShippingAddress::getCompanyNo, customer.getCompanyNo())
                    .eq(CompanyShippingAddress::getSourceNo, customer.getCustomerNo()).eq(CompanyShippingAddress::getLinkaddType, LinkmanTypeEnum.SALE.getValue()).eq(CompanyShippingAddress::getIsDefault, 1)
                    .eq(CompanyShippingAddress::getDeleted, DeletedEnum.UN_DELETE.getValue()).ne(CompanyShippingAddress::getId, companyShippingAddress.getId()));
        }
        CompanyShippingAddressVO shippingAddressVO = new CompanyShippingAddressVO();
        BeanUtils.copyProperties(companyShippingAddress, shippingAddressVO);
        return shippingAddressVO;
    }

    /**
     * 跨组织获取客户联系地址列表
     *
     * @param operationModel
     * @param params
     * @return
     */
    @Override
    public PageVo<CompanyShippingAddressVO> findCustomerLinkAddressListByOrgPage(OperationModel operationModel, CustomerLinkAddressByOrgQueryDTO params, PageDto pageDto) {
        ValidatorUtils.checkTrueThrowEx(StringUtils.isEmpty(params.getCustomerCode()), "客户编码不存在");
        ValidatorUtils.checkTrueThrowEx(StringUtils.isEmpty(params.getOrgNo()), "组织编码不存在");
        //获取有权限的组织列表
        List<OrganizationVo> organizationList = uimTenantService.getAuthEnterpriseList(operationModel.getEnterpriseNo(), operationModel.getUserNo(), operationModel.getEmployerNo());
        final Optional<OrganizationVo> optional = organizationList.stream().filter(t -> params.getOrgNo().equals(t.getOrgNo())).findAny();
        if (!optional.isPresent()) {
            //无权限查询
            return new PageVo<>();
        }
        final OrganizationVo organizationVo = optional.get();
        Customer customer = customerDAO.getCustomerByCustomerCode(organizationVo.getBindingEnterpriseNo(), params.getCustomerCode());
        ValidatorUtils.checkTrueThrowEx(customer == null, "客户不存在");
        //客户联系人
        Page<CompanyShippingAddress> pageInfo = PageHelper.startPage(pageDto.getPageIndex(), pageDto.getPageSize());
        companyShippingAddressDAO.getCompanyShippingAddressByCompanyNoAddressType(customer.getEnterpriseNo(),
                customer.getCompanyNo(), LinkmanTypeEnum.SALE.getValue(), customer.getCustomerNo(), params.getAddressTypeList(), params.getAddressType());
        List<CompanyShippingAddressVO> customerLinkmanVoList = resultToVo(pageInfo.getResult(), organizationVo.getBindingEnterpriseNo());
        return new PageVo<>(pageInfo.getPageNum(), pageInfo.getPageSize(), pageInfo.getTotal(), customerLinkmanVoList);
    }

    private List<CompanyShippingAddressVO> resultToVo(List<CompanyShippingAddress> result, String enterpriseNo) {
        if (CollectionUtils.isEmpty(result)) {
            return new ArrayList<>();
        }
        List<CompanyShippingAddressVO> voResult = new ArrayList<>();
        final List<String> regionCodeList = result.stream().map(CompanyShippingAddress::getRegionCode).filter(t -> !StringUtils.isEmpty(t)).collect(Collectors.toList());
        Map<String, AreaCodeVo> areaMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(regionCodeList)) {
            List<AreaCodeVo> areaCodeList = dictEnterpriseService.findAreaByCodes(regionCodeList);
            areaMap = areaCodeList.stream().collect(Collectors.toMap(AreaCodeVo::getAreaCode, t -> t));
        }
        Map<String, AreaCodeVo> finalAreaMap = areaMap;
        result.forEach(t -> {
            CompanyShippingAddressVO vo = new CompanyShippingAddressVO();
            BeanUtils.copyProperties(t, vo);
            if (finalAreaMap.containsKey(t.getRegionCode())) {
//                vo.setRegionName(finalAreaMap.get(t.getRegionCode()).getAreaName());
                vo.setRegionFullName(finalAreaMap.get(t.getRegionCode()).getAreaName());
            }
            voResult.add(vo);
        });
        return voResult;
    }

    /**
     * 跨组织保存客户联系地址列表
     *
     * @param operationModel
     * @param params
     * @return
     */
    @Override
    public CompanyShippingAddressVO saveCustomerLinkAddressByOrg(OperationModel operationModel, CustomerLinkAddressSaveByOrgDTO params) {
        ValidatorUtils.checkTrueThrowEx(StringUtils.isEmpty(params.getCustomerCode()), "客户编码不存在");
        ValidatorUtils.checkTrueThrowEx(StringUtils.isEmpty(params.getOrgNo()), "组织编码不存在");
        //获取有权限的组织列表
        List<OrganizationVo> organizationList = uimTenantService.getAuthEnterpriseList(operationModel.getEnterpriseNo(), operationModel.getUserNo(), operationModel.getEmployerNo());
        final Optional<OrganizationVo> optional = organizationList.stream().filter(t -> params.getOrgNo().equals(t.getOrgNo())).findAny();
        ValidatorUtils.checkTrueThrowEx(!optional.isPresent(), "无权限操作");

        final OrganizationVo organizationVo = optional.get();
        Customer customer = customerDAO.getCustomerByCustomerCode(organizationVo.getBindingEnterpriseNo(), params.getCustomerCode());
        ValidatorUtils.checkTrueThrowEx(customer == null, "客户不存在");
        //说明这个人有权限操作这个客户
        CompanyShippingAddress shippingAddress = new CompanyShippingAddress();
        shippingAddress.setEnterpriseNo(customer.getEnterpriseNo());
        shippingAddress.setCompanyNo(customer.getCompanyNo());
        shippingAddress.setLinkAddressCode(UUID.randomUUID().toString());
        shippingAddress.setSourceNo(customer.getCustomerNo());
        shippingAddress.setStatus(CommonStatusEnum.EFFECTIVE.getValue());
        shippingAddress.setLinkaddType(LinkmanTypeEnum.SALE.getValue());
        shippingAddress.setReceiveUser(params.getReceiveUser());
        shippingAddress.setReceivePhone(params.getReceivePhone());
        shippingAddress.setRegionCode(params.getRegionCode());
        shippingAddress.setReceiveAddr(params.getReceiveAddr());
        shippingAddress.setIsDefault(params.getIsDefault());
        shippingAddress.setAddressDesc(params.getAddressDesc());
        shippingAddress.setRegionName(params.getRegionName());
        shippingAddress.setAddressType(params.getAddressType());
        companyShippingAddressDAO.insert(shippingAddress);

        //如果新保存的这个条记录是默认的，则将之前的默认联系人修改为非默认
        if (new Integer(1).equals(params.getIsDefault())) {
            CompanyShippingAddress updateDefault = new CompanyShippingAddress();
            updateDefault.setIsDefault(0);
            companyShippingAddressDAO.update(updateDefault, new QueryWrapper<CompanyShippingAddress>().lambda().eq(CompanyShippingAddress::getEnterpriseNo, customer.getEnterpriseNo()).eq(CompanyShippingAddress::getCompanyNo, customer.getCompanyNo())
                    .eq(CompanyShippingAddress::getSourceNo, customer.getCustomerNo()).eq(CompanyShippingAddress::getLinkaddType, LinkmanTypeEnum.SALE.getValue()).eq(CompanyShippingAddress::getIsDefault, 1)
                    .eq(CompanyShippingAddress::getDeleted, DeletedEnum.UN_DELETE.getValue()).ne(CompanyShippingAddress::getId, shippingAddress.getId()));
        }
        CompanyShippingAddressVO shippingAddressVO = new CompanyShippingAddressVO();
        BeanUtils.copyProperties(shippingAddress, shippingAddressVO);
        return shippingAddressVO;
    }

    /**
     * 跨组织编辑联系地址
     *
     * @param operationModel
     * @param params
     * @return
     */
    @Override
    public CompanyShippingAddressVO editCustomerLinkAddressByOrg(OperationModel operationModel, CustomerLinkAddressSaveByOrgDTO params) {
        ValidatorUtils.checkTrueThrowEx(StringUtils.isEmpty(params.getCustomerCode()), "客户编码不存在");
        ValidatorUtils.checkTrueThrowEx(StringUtils.isEmpty(params.getOrgNo()), "组织编码不存在");
        //获取有权限的组织列表
        List<OrganizationVo> organizationList = uimTenantService.getAuthEnterpriseList(operationModel.getEnterpriseNo(), operationModel.getUserNo(), operationModel.getEmployerNo());
        final Optional<OrganizationVo> optional = organizationList.stream().filter(t -> params.getOrgNo().equals(t.getOrgNo())).findAny();
        ValidatorUtils.checkTrueThrowEx(!optional.isPresent(), "无权限操作");

        final OrganizationVo organizationVo = optional.get();
        Customer customer = customerDAO.getCustomerByCustomerCode(organizationVo.getBindingEnterpriseNo(), params.getCustomerCode());
        ValidatorUtils.checkTrueThrowEx(customer == null, "客户不存在");

        LambdaQueryWrapper<CompanyShippingAddress> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(CompanyShippingAddress::getEnterpriseNo, customer.getEnterpriseNo())
                .eq(CompanyShippingAddress::getCompanyNo, customer.getCompanyNo())
                .eq(CompanyShippingAddress::getLinkaddType, LinkmanTypeEnum.SALE.getValue())
                .eq(CompanyShippingAddress::getSourceNo, customer.getCustomerNo())
                .eq(CompanyShippingAddress::getDeleted, DeletedEnum.UN_DELETE.getValue())
                .eq(CompanyShippingAddress::getId, params.getId());
        final CompanyShippingAddress companyShippingAddress = companyShippingAddressDAO.selectOne(wrapper);
        ValidatorUtils.checkTrueThrowEx(companyShippingAddress == null, "客户联系人不存在");

        companyShippingAddress.setReceiveUser(params.getReceiveUser());
        companyShippingAddress.setRegionName(params.getRegionName());
        companyShippingAddress.setReceivePhone(params.getReceivePhone());
        companyShippingAddress.setRegionCode(params.getRegionCode());
        companyShippingAddress.setReceiveAddr(params.getReceiveAddr());
        companyShippingAddress.setIsDefault(params.getIsDefault());
        companyShippingAddress.setAddressDesc(params.getAddressDesc());
        companyShippingAddress.setAddressType(params.getAddressType());
        companyShippingAddressDAO.updateById(companyShippingAddress);
        //如果新保存的这个条记录是默认的，则将之前的默认联系人修改为非默认
        if (new Integer(1).equals(params.getIsDefault())) {
            CompanyShippingAddress updateDefault = new CompanyShippingAddress();
            updateDefault.setIsDefault(0);
            companyShippingAddressDAO.update(updateDefault, new QueryWrapper<CompanyShippingAddress>().lambda().eq(CompanyShippingAddress::getEnterpriseNo, customer.getEnterpriseNo()).eq(CompanyShippingAddress::getCompanyNo, customer.getCompanyNo())
                    .eq(CompanyShippingAddress::getSourceNo, customer.getCustomerNo()).eq(CompanyShippingAddress::getLinkaddType, LinkmanTypeEnum.SALE.getValue()).eq(CompanyShippingAddress::getIsDefault, 1)
                    .eq(CompanyShippingAddress::getDeleted, DeletedEnum.UN_DELETE.getValue()).ne(CompanyShippingAddress::getId, companyShippingAddress.getId()));
        }
        CompanyShippingAddressVO shippingAddressVO = new CompanyShippingAddressVO();
        BeanUtils.copyProperties(companyShippingAddress, shippingAddressVO);
        return shippingAddressVO;
    }

    /**
     * 跨组织删除联系地址
     *
     * @param operationModel
     * @param linkAddressId
     * @return
     */
    @Override
    public Boolean deleteCustomerLinkAddressByOrg(OperationModel operationModel, Long linkAddressId) {
        //获取有权限的组织列表
        List<OrganizationVo> organizationList = uimTenantService.getAuthEnterpriseList(operationModel.getEnterpriseNo(), operationModel.getUserNo(), operationModel.getEmployerNo());
        ValidatorUtils.checkTrueThrowEx(CollectionUtils.isEmpty(organizationList), "无权限删除联系人");
        final CompanyShippingAddress shippingAddress = companyShippingAddressDAO.selectById(linkAddressId);
        ValidatorUtils.checkTrueThrowEx(shippingAddress == null, "联系人不存在");
        Customer customer = customerDAO.getCustomerByNoEnterpriseNo(shippingAddress.getSourceNo());
        ValidatorUtils.checkTrueThrowEx(customer == null, "客户不存在");
        final Optional<OrganizationVo> optional = organizationList.stream().filter(t -> customer.getEnterpriseNo().equals(t.getBindingEnterpriseNo())).findAny();
        ValidatorUtils.checkTrueThrowEx(!optional.isPresent(), "无权限删除联系人");
        shippingAddress.setDeleted(DeletedEnum.DELETED.getValue());
        companyShippingAddressDAO.updateById(shippingAddress);
        return Boolean.TRUE;
    }
}
