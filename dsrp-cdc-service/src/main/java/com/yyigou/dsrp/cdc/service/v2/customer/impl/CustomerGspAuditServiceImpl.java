package com.yyigou.dsrp.cdc.service.v2.customer.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yyigou.dsrp.cdc.dao.v2.customer.CustomerGspAuditDAO;
import com.yyigou.dsrp.cdc.dao.v2.customer.entity.CustomerGspAudit;
import com.yyigou.dsrp.cdc.service.v2.customer.CustomerGspAuditService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service("customerGspAuditService")
@RequiredArgsConstructor
@Slf4j
public class CustomerGspAuditServiceImpl extends ServiceImpl<CustomerGspAuditDAO, CustomerGspAudit> implements CustomerGspAuditService {


}
