package com.yyigou.dsrp.cdc.service.customer;

import com.yyigou.dsrp.cdc.api.customer.dto.CustomerInvoiceQueryDTO;
import com.yyigou.dsrp.cdc.api.customer.vo.CustomerInvoiceVO;
import com.yyigou.dsrp.cdc.client.customer.response.response.CustomerInvoiceResponse;
import com.yyigou.dsrp.cdc.common.util.OperationModel;

import java.util.List;

public interface CustomerInvoiceService {

    List<CustomerInvoiceVO> getInvoiceListByCustomerCode(OperationModel operationModel, CustomerInvoiceQueryDTO params);

    List<CustomerInvoiceResponse> getInvoiceListByCustomerCodeNoSession(String enterpriseNo, String customerCode);
}
