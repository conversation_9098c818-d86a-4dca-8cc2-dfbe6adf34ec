package com.yyigou.dsrp.cdc.service.v2.company.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.yyigou.ddc.common.exception.BusinessException;
import com.yyigou.ddc.common.page.PageDto;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.ddc.common.util.DateUtil;
import com.yyigou.ddc.common.util.ValidatorUtils;
import com.yyigou.ddc.services.ddc.dict.vo.AreaCodeVo;
import com.yyigou.ddc.services.ddc.uim.vo.OrganizationVo;
import com.yyigou.ddc.services.dlog.dto.DLogLevel;
import com.yyigou.dsrp.cdc.api.v2.company.vo.CompanyBankVO;
import com.yyigou.dsrp.cdc.api.v2.company.vo.CompanyBasicVO;
import com.yyigou.dsrp.cdc.api.v2.company.vo.CompanyDetailVO;
import com.yyigou.dsrp.cdc.client.company.response.CompanyLinkmanResponse;
import com.yyigou.dsrp.cdc.client.company.response.CompanyShippingAddressResponse;
import com.yyigou.dsrp.cdc.client.v2.company.response.Company2InfoResponse;
import com.yyigou.dsrp.cdc.client.v2.company.response.CompanyAssociatedOrgResponse;
import com.yyigou.dsrp.cdc.client.v2.company.response.CompanyBankResponse;
import com.yyigou.dsrp.cdc.client.v2.company.response.CompanyBasicResponse;
import com.yyigou.dsrp.cdc.common.enums.*;
import com.yyigou.dsrp.cdc.common.enums.company.*;
import com.yyigou.dsrp.cdc.common.util.BeanUtil;
import com.yyigou.dsrp.cdc.common.util.CommonUtil;
import com.yyigou.dsrp.cdc.common.util.OperationModel;
import com.yyigou.dsrp.cdc.dao.common.BankTypeDAO;
import com.yyigou.dsrp.cdc.dao.common.entity.BankType;
import com.yyigou.dsrp.cdc.dao.company.CompanyDAO;
import com.yyigou.dsrp.cdc.dao.company.entity.Company;
import com.yyigou.dsrp.cdc.dao.v2.company.*;
import com.yyigou.dsrp.cdc.dao.v2.company.entity.*;
import com.yyigou.dsrp.cdc.manager.integration.dict.DictEnterpriseService;
import com.yyigou.dsrp.cdc.manager.integration.dlog.BusinessLogService;
import com.yyigou.dsrp.cdc.manager.integration.numberCenter.NumberCenterService;
import com.yyigou.dsrp.cdc.manager.integration.org.UimTenantService;
import com.yyigou.dsrp.cdc.manager.integration.uim.CustomDocService;
import com.yyigou.dsrp.cdc.manager.integration.uim.OrganizationService;
import com.yyigou.dsrp.cdc.manager.integration.uim.response.CustomDocResponse;
import com.yyigou.dsrp.cdc.model.constant.NumberCenterConstant;
import com.yyigou.dsrp.cdc.model.constant.SystemConstant;
import com.yyigou.dsrp.cdc.model.constant.ViewNameConstant;
import com.yyigou.dsrp.cdc.model.enums.FactoryTypeEnum;
import com.yyigou.dsrp.cdc.model.v2.company.req.*;
import com.yyigou.dsrp.cdc.service.listener.model.v2.CompanyUpdateModel;
import com.yyigou.dsrp.cdc.service.listener.model.v2.TenantCompanySaveModel;
import com.yyigou.dsrp.cdc.service.utils.PageUtils;
import com.yyigou.dsrp.cdc.service.v2.company.CompanyV2Service;
import com.yyigou.dsrp.cdc.service.v2.customer.CustomerV2Service;
import com.yyigou.dsrp.cdc.service.v2.supplier.SupplierV2Service;
import com.yyigou.dsrp.gcs.common.enums.StatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service("companyV2Service")
@Slf4j
public class CompanyV2ServiceImpl extends ServiceImpl<CompanyV2DAO, CompanyV2> implements CompanyV2Service {
    @Resource
    private CompanyV2DAO companyV2Dao;

    @Resource
    private CompanyBankV2DAO companyBankV2DAO;

    @Resource
    private CompanyLinkmanV2DAO companyLinkmanV2DAO;

    @Resource
    private CompanyShippingAddressV2DAO companyShippingAddressV2DAO;

    @Resource
    private BankTypeDAO bankTypeDAO;

    @Resource
    private NumberCenterService numberCenterService;

    @Resource
    private CustomDocService customDocService;

    @Resource
    private DictEnterpriseService dictEnterpriseService;

    @Autowired
    private BusinessLogService businessLogService;

    @Resource
    private UimTenantService uimTenantService;

    @Resource
    private SupplierV2Service supplierV2Service;

    @Resource
    private CustomerV2Service customerV2Service;

    @Resource
    private CompanyAssociatedOrgDAO companyAssociatedOrgDAO;
    @Autowired
    private OrganizationService organizationService;
    @Autowired
    private CompanyDAO companyDAO;

    @Override
    public PageVo<CompanyDetailVO> findCompanyListPage(CompanyQueryListPageReq queryReq, PageDto pageDTO) {
        ValidatorUtils.checkEmptyThrowEx(queryReq, "查询条件不能为空");
        String enterpriseNo = queryReq.getEnterpriseNo();
        ValidatorUtils.checkEmptyThrowEx(enterpriseNo, "租户编号不能为空");
        if (StringUtils.isBlank(pageDTO.getOrderBy())) {
            pageDTO.setOrderBy("operate_time desc");
        }
        Page<CompanyV2> page = PageHelper.startPage(pageDTO.getPageIndex(), pageDTO.getPageSize(), pageDTO.getOrderBy());
        companyV2Dao.findCompanyListPage(queryReq);
        return PageUtils.convertPageVo(page, data -> {
            List<CompanyDetailVO> detailVOList = new ArrayList<>();
            if (Objects.nonNull(data)) {
                final Map<String, CustomDocResponse> taxCategoryMap = customDocService.getItemByCustomDocCode(enterpriseNo, SystemConstant.TAX_CATEGORY);
                final Map<String, CustomDocResponse> economicTypeMap = customDocService.getItemByCustomDocCode(enterpriseNo, SystemConstant.ECONOMIC_TYPE);
                final Map<String, String> areaMap = dictEnterpriseService.findAreaMapByCodesWithFullName(data.stream().map(CompanyV2::getRegionCode).collect(Collectors.toList()));
                final Map<String, String> regionMap = dictEnterpriseService.findPlatformCountryRegionList();
                final Map<String, Set<OrganizationVo>> companyAssociatedOrgMap = getCompanyAssociatedOrgMap(enterpriseNo, data);

                data.forEach(company -> {
                    CompanyDetailVO companyDetailVO = new CompanyDetailVO();
                    BeanUtils.copyProperties(company, companyDetailVO);
                    // 填充信息
                    fillCompanyDetailVO(companyDetailVO, taxCategoryMap, economicTypeMap, areaMap, regionMap, companyAssociatedOrgMap);
                    detailVOList.add(companyDetailVO);
                });
            }
            return detailVOList;
        });
    }

    @Override
    public PageVo<CompanyDetailVO> findCompanyListPageForGoods(CompanyQueryListPageForGoodsReq queryReq, PageDto pageDTO) {
        ValidatorUtils.checkEmptyThrowEx(queryReq, "查询条件不能为空");
        String enterpriseNo = queryReq.getEnterpriseNo();
        ValidatorUtils.checkEmptyThrowEx(enterpriseNo, "租户编号不能为空");
        if (StringUtils.isBlank(pageDTO.getOrderBy())) {
            pageDTO.setOrderBy("operate_time desc");
        }
        Page<CompanyV2> page = PageHelper.startPage(pageDTO.getPageIndex(), pageDTO.getPageSize(), pageDTO.getOrderBy());
        companyV2Dao.findCompanyListPageForGoods(queryReq);
        return PageUtils.convertPageVo(page, data -> {
            List<CompanyDetailVO> detailVOList = new ArrayList<>();
            if (Objects.nonNull(data)) {
                final Map<String, CustomDocResponse> taxCategoryMap = customDocService.getItemByCustomDocCode(enterpriseNo, SystemConstant.TAX_CATEGORY);
                final Map<String, CustomDocResponse> economicTypeMap = customDocService.getItemByCustomDocCode(enterpriseNo, SystemConstant.ECONOMIC_TYPE);
                final Map<String, String> areaMap = dictEnterpriseService.findAreaMapByCodesWithFullName(data.stream().map(CompanyV2::getRegionCode).collect(Collectors.toList()));
                final Map<String, String> regionMap = dictEnterpriseService.findPlatformCountryRegionList();
                final Map<String, Set<OrganizationVo>> companyAssociatedOrgMap = getCompanyAssociatedOrgMap(enterpriseNo, data);


                data.forEach(company -> {
                    CompanyDetailVO companyDetailVO = new CompanyDetailVO();
                    BeanUtils.copyProperties(company, companyDetailVO);
                    // 填充信息
                    fillCompanyDetailVO(companyDetailVO, taxCategoryMap, economicTypeMap, areaMap, regionMap, companyAssociatedOrgMap);
                    detailVOList.add(companyDetailVO);
                });
            }
            return detailVOList;
        });
    }

    @Override
    public Boolean checkName(OperationModel operationModel, String no, String name) {
        return companyV2Dao.selectCount(new LambdaQueryWrapper<CompanyV2>()
                .eq(CompanyV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CompanyV2::getCompanyName, name)
                .eq(CompanyV2::getDeleted, DeletedEnum.UN_DELETE.getValue())
                .ne(StringUtils.isNotEmpty(no), CompanyV2::getCompanyNo, no)) <= 0;
    }

    @Override
    public Boolean checkUnifiedSocialCode(OperationModel operationModel, String no, String unifiedSocialCode, Integer factoryType) {
        //境内
        if (FactoryTypeEnum.DOMESTIC.getValue().equals(factoryType)) {
            return companyV2Dao.selectCount(new LambdaQueryWrapper<CompanyV2>()
                    .eq(CompanyV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                    .eq(CompanyV2::getUnifiedSocialCode, unifiedSocialCode)
                    .eq(CompanyV2::getDeleted, DeletedEnum.UN_DELETE.getValue())
                    .ne(CompanyV2::getFactoryType, FactoryTypeEnum.ABROAD.getValue())
                    .ne(StringUtils.isNotEmpty(no), CompanyV2::getCompanyNo, no)) <= 0;
        }

        return true;
    }

    @Override
    public Boolean checkUniqueCompany(OperationModel operationModel, String no, String name, String unifiedSocialCode, Integer factoryType) {
        Boolean unique = checkName(operationModel, no, name);
        if (!unique) {
            return false;
        }

        return checkUnifiedSocialCode(operationModel, no, unifiedSocialCode, factoryType);
    }

    private void validateSaveCompany(OperationModel operationModel, CompanySaveReq companySaveReq) {
        // 校验必填参数
        ValidatorUtils.checkEmptyThrowEx(operationModel.getEnterpriseNo(), "租户编号不能为空");
        ValidatorUtils.checkEmptyThrowEx(companySaveReq.getCompanyName(), "企业名称不能为空");
        ValidatorUtils.checkEmptyThrowEx(companySaveReq.getUnifiedSocialCode(), "统一社会信用代码不能为空");
        ValidatorUtils.checkEmptyThrowEx(companySaveReq.getFactoryType(), "企业注册地域不能为空");
        ValidatorUtils.checkEmptyThrowEx(companySaveReq.getTaxCategory(), "纳税类别不能为空");

        // 校验范围
        // 法定代表人：100
        if (StringUtils.isNotEmpty(companySaveReq.getLegalPerson())) {
            ValidatorUtils.checkTrueThrowEx(companySaveReq.getLegalPerson().length() > 100, "法定代表人不能大于100");
        }

        // 经营状态：128
        if (StringUtils.isNotEmpty(companySaveReq.getBusinessStatus())) {
            ValidatorUtils.checkTrueThrowEx(companySaveReq.getBusinessStatus().length() > 128, "经营状态不能大于128");
        }

        // 成立日期：19
        if (StringUtils.isNotEmpty(companySaveReq.getEstablishmentDate())) {
            ValidatorUtils.checkTrueThrowEx(companySaveReq.getEstablishmentDate().length() > 19, "成立日期不能大于19");
        }

        // 注册地址：300
        if (StringUtils.isNotEmpty(companySaveReq.getAddress())) {
            ValidatorUtils.checkTrueThrowEx(companySaveReq.getAddress().length() > 300, "注册地址不能大于300");
        }

        // 注册资本：100
        if (StringUtils.isNotEmpty(companySaveReq.getRegistedCapital())) {
            ValidatorUtils.checkTrueThrowEx(companySaveReq.getRegistedCapital().length() > 100, "注册资本不能大于100");
        }

        // 实缴资本：128
        if (StringUtils.isNotEmpty(companySaveReq.getPaidCapital())) {
            ValidatorUtils.checkTrueThrowEx(companySaveReq.getPaidCapital().length() > 128, "实缴资本不能大于128");
        }

        // 所属行业：255
        if (StringUtils.isNotEmpty(companySaveReq.getIndustry())) {
            ValidatorUtils.checkTrueThrowEx(companySaveReq.getIndustry().length() > 255, "所属行业不能大于255");
        }

        // 曾用名：128
        if (StringUtils.isNotEmpty(companySaveReq.getLastName())) {
            ValidatorUtils.checkTrueThrowEx(companySaveReq.getLastName().length() > 128, "曾用名不能大于128");
        }

        // 工商注册号：128
        if (StringUtils.isNotEmpty(companySaveReq.getBusinessRegistNo())) {
            ValidatorUtils.checkTrueThrowEx(companySaveReq.getBusinessRegistNo().length() > 128, "工商注册号不能大于128");
        }

        // 组织机构代码：32
        if (StringUtils.isNotEmpty(companySaveReq.getOrganizationNo())) {
            ValidatorUtils.checkTrueThrowEx(companySaveReq.getOrganizationNo().length() > 32, "组织机构代码不能大于32");
        }

        // 纳税人识别号：128
        if (StringUtils.isNotEmpty(companySaveReq.getTaxpayerNo())) {
            ValidatorUtils.checkTrueThrowEx(companySaveReq.getTaxpayerNo().length() > 128, "纳税人识别号不能大于128");
        }

        // 营业期限开始时间：19
        if (StringUtils.isNotEmpty(companySaveReq.getBusinessStartTime())) {
            ValidatorUtils.checkTrueThrowEx(companySaveReq.getBusinessStartTime().length() > 19, "营业期限开始时间不能大于19");
        }

        // 营业期限结束时间：19
        if (StringUtils.isNotEmpty(companySaveReq.getBusinessEndTime())) {
            ValidatorUtils.checkTrueThrowEx(companySaveReq.getBusinessEndTime().length() > 19, "营业期限结束时间不能大于19");
        }

        // 核准日期：19
        if (StringUtils.isNotEmpty(companySaveReq.getApprovalDate())) {
            ValidatorUtils.checkTrueThrowEx(companySaveReq.getApprovalDate().length() > 19, "核准日期不能大于19");
        }

        // 登记机关：255
        if (StringUtils.isNotEmpty(companySaveReq.getRegistrationAuthority())) {
            ValidatorUtils.checkTrueThrowEx(companySaveReq.getRegistrationAuthority().length() > 255, "登记机关不能大于255");
        }

        // 参保人数：255
        if (StringUtils.isNotEmpty(companySaveReq.getInsuredNumber())) {
            ValidatorUtils.checkTrueThrowEx(companySaveReq.getInsuredNumber().length() > 255, "参保人数不能大于255");
        }

        // WEB网站：200
        if (StringUtils.isNotEmpty(companySaveReq.getWebSite())) {
            ValidatorUtils.checkTrueThrowEx(companySaveReq.getWebSite().length() > 200, "WEB网站不能大于200");
        }

        // 企业邮箱：200
        if (StringUtils.isNotEmpty(companySaveReq.getEmail())) {
            ValidatorUtils.checkTrueThrowEx(companySaveReq.getEmail().length() > 200, "企业邮箱不能大于200");
        }

        // 传真：100
        if (StringUtils.isNotEmpty(companySaveReq.getFax())) {
            ValidatorUtils.checkTrueThrowEx(companySaveReq.getFax().length() > 100, "传真不能大于100");
        }

        // 经营范围：500
        if (StringUtils.isNotEmpty(companySaveReq.getManageScope())) {
            ValidatorUtils.checkTrueThrowEx(companySaveReq.getManageScope().length() > 500, "经营范围不能大于500");
        }

        // 校验企业唯一性
        boolean isUnique = this.checkUniqueCompany(operationModel, companySaveReq.getCompanyNo(), companySaveReq.getCompanyName(), companySaveReq.getUnifiedSocialCode(), companySaveReq.getFactoryType());
        if (!isUnique) {
            throw new RuntimeException("企业已存在");
        }
    }

    @Override
    @Transactional
    public CompanyBasicVO saveCompany(OperationModel operationModel, CompanySaveReq companySaveReq) {
        // 校验入参必填、范围
        validateSaveCompany(operationModel, companySaveReq);

        // 保存企业信息
        CompanyV2 companyV2 = new CompanyV2();
        BeanUtils.copyProperties(companySaveReq, companyV2);
        companyV2.setEnterpriseNo(operationModel.getEnterpriseNo());
        String companyNo = companySaveReq.getSpecificNewCompanyNo() == null ? numberCenterService.createNumber(NumberCenterConstant.COMPANY_NO_GENERATE_KEY) : companySaveReq.getSpecificNewCompanyNo();
        companyV2.setCompanyNo(companyNo);
        companyV2.setCompanyCode(companyNo);

        CommonUtil.fillCreatInfo(operationModel, companyV2);
        CommonUtil.fillOperateInfo(operationModel, companyV2);
        companyV2Dao.insert(companyV2);

        CompanyBasicVO companyBasicVO = new CompanyBasicVO();
        BeanUtils.copyProperties(companyV2, companyBasicVO);

        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                businessLogService.saveLog(DLogLevel.INFO.name(), ViewNameConstant.BDC_COMPANY_VIEW, companyNo, "新增企业档案", "新增企业档案", JSON.toJSONString(companyV2), "");
            }
        });

        return companyBasicVO;
    }

    /**
     * 有些内部联动逻辑需要创建企业，但不像页面一样能提供这么多企业信息
     * @param enterpriseNo
     * @param companySaveReq
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public CompanyBasicVO createSimpleCompany(String enterpriseNo, CompanySaveReq companySaveReq) {
        // 只校验企业名称，其他字段不校验必填性
        ValidatorUtils.checkEmptyThrowEx(enterpriseNo, "租户编号不能为空");
        ValidatorUtils.checkEmptyThrowEx(companySaveReq.getCompanyName(), "企业名称不能为空");

        // 保存企业信息
        CompanyV2 companyV2 = new CompanyV2();
        BeanUtils.copyProperties(companySaveReq, companyV2);
        companyV2.setEnterpriseNo(enterpriseNo);
        String companyNo = companySaveReq.getSpecificNewCompanyNo() == null ? numberCenterService.createNumber(NumberCenterConstant.COMPANY_NO_GENERATE_KEY) : companySaveReq.getSpecificNewCompanyNo();
        companyV2.setCompanyNo(companyNo);
        companyV2.setCompanyCode(companyNo);

        companyV2.setCreateTime(DateUtil.getCurrentDate());
        companyV2.setOperateTime(DateUtil.getCurrentDate());
        companyV2Dao.insert(companyV2);

        CompanyBasicVO companyBasicVO = new CompanyBasicVO();
        BeanUtils.copyProperties(companyV2, companyBasicVO);

        return companyBasicVO;
    }

    @Override
    public CompanyDetailVO getDetailByEnterpriseAndCompanyNo(String enterpriseNo, String companyNo) {
        ValidatorUtils.checkEmptyThrowEx(enterpriseNo, "租户编号不能为空");
        ValidatorUtils.checkEmptyThrowEx(companyNo, "企业编号不能为空");
        CompanyV2 companyV2 = companyV2Dao.findByEnterpriseNoAndCompanyNo(enterpriseNo, companyNo);
        ValidatorUtils.checkEmptyThrowEx(companyV2, "企业档案不存在");
        CompanyDetailVO companyDetailVO = new CompanyDetailVO();
        BeanUtils.copyProperties(companyV2, companyDetailVO);

        final Map<String, CustomDocResponse> taxCategoryMap = customDocService.getItemByCustomDocCode(enterpriseNo, SystemConstant.TAX_CATEGORY);
        final Map<String, CustomDocResponse> economicTypeMap = customDocService.getItemByCustomDocCode(enterpriseNo, SystemConstant.ECONOMIC_TYPE);
        final Map<String, String> areaMap = dictEnterpriseService.findAreaMapByCodesWithFullName(Collections.singletonList(companyV2.getRegionCode()));
        final Map<String, String> regionMap = dictEnterpriseService.findPlatformCountryRegionList();
        final Map<String, Set<OrganizationVo>> companyAssociatedOrgMap = getCompanyAssociatedOrgMap(enterpriseNo, Lists.newArrayList(companyV2));

        // 填充信息
        fillCompanyDetailVO(companyDetailVO, taxCategoryMap, economicTypeMap, areaMap, regionMap, companyAssociatedOrgMap);

        return companyDetailVO;
    }

    /**
     *  单组织租户的企业证照取的是 companyNo = 租户编号的企业信息
     * @param enterpriseNo
     * @return
     */
    @Override
    public CompanyDetailVO getTenantCompany(String enterpriseNo) {
        try {
            return this.getDetailByEnterpriseAndCompanyNo(enterpriseNo, enterpriseNo);
        } catch (Exception e) {
            if ("企业档案不存在".equals(e.getMessage())) {
                return null;
            }
            throw e;
        }
    }

    @Override
    public CompanyDetailVO getDetailByEnterpriseAndCompanyName(String enterpriseNo, String companyName) {
        ValidatorUtils.checkEmptyThrowEx(enterpriseNo, "租户编号不能为空");
        ValidatorUtils.checkEmptyThrowEx(companyName, "企业名称不能为空");
        List<CompanyV2> companyV2List = companyV2Dao.findByEnterpriseNoAndCompanyList(enterpriseNo, companyName);
        if (CollectionUtil.isEmpty(companyV2List)) {
            return null;
        }
        return getDetailByEnterpriseAndCompanyNo(enterpriseNo, companyV2List.get(0).getCompanyNo());
    }

    @Override
    public CompanyV2 getByEnterpriseAndCompanyName(String enterpriseNo, String companyName) {
        return companyV2Dao.selectOne(Wrappers.<CompanyV2>lambdaQuery()
                .eq(CompanyV2::getEnterpriseNo, enterpriseNo)
                .eq(CompanyV2::getCompanyName, companyName)
                .eq(CompanyV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
    }

    @Override
    public CompanyV2 getByEnterpriseAndCompanyNo(String enterpriseNo, String companyNo) {
        return companyV2Dao.selectOne(Wrappers.<CompanyV2>lambdaQuery()
                .eq(CompanyV2::getEnterpriseNo, enterpriseNo)
                .eq(CompanyV2::getCompanyNo, companyNo)
                .eq(CompanyV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
    }

    @Override
    public CompanyV2 getByEnterpriseAndUnifiedSocialCodeDomestic(String enterpriseNo, String unifiedSocialCode) {
        return companyV2Dao.selectOne(Wrappers.<CompanyV2>lambdaQuery()
                .eq(CompanyV2::getEnterpriseNo, enterpriseNo)
                .eq(CompanyV2::getUnifiedSocialCode, unifiedSocialCode)
                .eq(CompanyV2::getFactoryType, FactoryTypeEnum.DOMESTIC.getValue())
                .eq(CompanyV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
    }

    private void fillCompanyDetailVO(CompanyDetailVO companyDetailVO, Map<String, CustomDocResponse> taxCategoryMap, Map<String, CustomDocResponse> economicTypeMap, Map<String, String> areaMap, Map<String, String> regionMap, Map<String, Set<OrganizationVo>> companyAssociatedOrgMap) {
        // 填充关联组织相关字段
        if (MapUtils.isNotEmpty(companyAssociatedOrgMap)) {
            Set<OrganizationVo> organizationVos = companyAssociatedOrgMap.get(companyDetailVO.getCompanyNo());
            if (CollectionUtils.isNotEmpty(organizationVos)) {
                companyDetailVO.setIsAssociatedEnterprise(CommonIfEnum.YES.getValue());
                companyDetailVO.setAssociatedOrgNo(organizationVos.stream().map(OrganizationVo::getOrgNo).collect(Collectors.joining(",")));
                companyDetailVO.setAssociatedOrgCode(organizationVos.stream().map(OrganizationVo::getOrgCode).collect(Collectors.joining(",")));
                companyDetailVO.setAssociatedOrgName(organizationVos.stream().map(OrganizationVo::getOrgName).collect(Collectors.joining(",")));

            } else {
                companyDetailVO.setIsAssociatedEnterprise(CommonIfEnum.NO.getValue());
            }
        }

        // 合作关系
        companyDetailVO.setPartnershipText(convertPartnership(companyDetailVO.getPartnership()));
        // 纳税类别
        if (StringUtils.isNotBlank(companyDetailVO.getTaxCategory())) {
            companyDetailVO.setTaxCategoryName(taxCategoryMap.getOrDefault(companyDetailVO.getTaxCategory(), new CustomDocResponse()).getDocItemName());
        }
        // 经济类型
        if (StringUtils.isNotBlank(companyDetailVO.getEconomicType())) {
            companyDetailVO.setEconomicTypeName(economicTypeMap.getOrDefault(companyDetailVO.getEconomicType(), new CustomDocResponse()).getDocItemName());
        }
        // 企业注册地域
        if (Objects.nonNull(companyDetailVO.getFactoryType())) {
            companyDetailVO.setFactoryTypeName(FactoryTypeEnum.getByType(companyDetailVO.getFactoryType()).getName());
        }
        // 营业期限
        if (StringUtils.isNotBlank(companyDetailVO.getBusinessEndTime())) {
            companyDetailVO.setBusinessEndTimeName(companyDetailVO.getBusinessEndTime());
        } else {
            if (CommonIfEnum.YES.getValue().equals(companyDetailVO.getBusinessLongTerm())) {
                companyDetailVO.setBusinessEndTimeName("长期");
            }
        }
        //是否关联企业
        if (Objects.nonNull(companyDetailVO.getIsAssociatedEnterprise())) {
            companyDetailVO.setIsAssociatedEnterpriseName(CommonIfEnum.getNameByValue(companyDetailVO.getIsAssociatedEnterprise()));
        }

        // 是否医疗机构
        if (Objects.nonNull(companyDetailVO.getIsMedicalInstitution())) {
            companyDetailVO.setIsMedicalInstitutionName(CommonIfEnum.YES.getValue().equals(companyDetailVO.getIsMedicalInstitution()) ? CommonIfEnum.YES.getName() : CommonIfEnum.NO.getName());
        }

        // 医疗机构类型名称
        if (Objects.nonNull(companyDetailVO.getInstitutionalType())) {
            companyDetailVO.setInstitutionalTypeName(InstitutionalTypeEnum.getByType(companyDetailVO.getInstitutionalType()) != null ? InstitutionalTypeEnum.getByType(companyDetailVO.getInstitutionalType()).getName() : "");
        }

        // 医院类型名称
        if (Objects.nonNull(companyDetailVO.getHospitalType())) {
            companyDetailVO.setHospitalTypeName(HospitalTypeEnum.getByType(companyDetailVO.getHospitalType()) != null ? HospitalTypeEnum.getByType(companyDetailVO.getHospitalType()).getName() : "");
        }

        // 医院等级名称
        if (Objects.nonNull(companyDetailVO.getHospitalClass())) {
            companyDetailVO.setHospitalClassName(HospitalClassEnum.getByType(companyDetailVO.getHospitalClass()) != null ? HospitalClassEnum.getByType(companyDetailVO.getHospitalClass()).getName() : "");
        }

        // 所在区域
        if (StringUtils.isNotBlank(companyDetailVO.getRegionCode())) {
            if (MapUtils.isNotEmpty(areaMap)) {
                companyDetailVO.setRegionName(areaMap.get(companyDetailVO.getRegionCode()));
            }
        }
        if (StringUtils.isNotBlank(companyDetailVO.getCountryRegionId())) {
            companyDetailVO.setCountryRegionName(regionMap.get(companyDetailVO.getCountryRegionId()));
        }
    }

    private String convertPartnership(String partnership) {
        if (StringUtils.isNotBlank(partnership)) {
            List<CompanyPartnershipEnum> partnershipEnumList = new ArrayList<>();
            List<String> partnershipList = JSON.parseArray(partnership, String.class);
            for (String ps : partnershipList) {
                if (CompanyPartnershipEnum.FACTORY.getType().equals(ps)) {
                    continue;
                }
                if (Objects.nonNull(CompanyPartnershipEnum.getByType(ps))) {
                    partnershipEnumList.add(CompanyPartnershipEnum.getByType(ps));
                }
            }
            return String.join(",", partnershipEnumList.stream().map(n -> n.getName()).collect(Collectors.toList()));
        }
        return null;
    }

    private void validateUpdateCompany(OperationModel operationModel, CompanyUpdateReq companyUpdateReq) {
        // 校验必填参数
        ValidatorUtils.checkEmptyThrowEx(operationModel.getEnterpriseNo(), "租户编号不能为空");
        ValidatorUtils.checkEmptyThrowEx(companyUpdateReq.getCompanyNo(), "企业编号不能为空");
        ValidatorUtils.checkEmptyThrowEx(companyUpdateReq.getCompanyName(), "企业名称不能为空");
        ValidatorUtils.checkEmptyThrowEx(companyUpdateReq.getUnifiedSocialCode(), "统一社会信用代码不能为空");
        ValidatorUtils.checkEmptyThrowEx(companyUpdateReq.getFactoryType(), "企业注册地域不能为空");
        ValidatorUtils.checkEmptyThrowEx(companyUpdateReq.getTaxCategory(), "纳税类别不能为空");

        // 校验范围
        // 法定代表人：100
        if (StringUtils.isNotEmpty(companyUpdateReq.getLegalPerson())) {
            ValidatorUtils.checkTrueThrowEx(companyUpdateReq.getLegalPerson().length() > 100, "法定代表人不能大于100");
        }

        // 经营状态：128
        if (StringUtils.isNotEmpty(companyUpdateReq.getBusinessStatus())) {
            ValidatorUtils.checkTrueThrowEx(companyUpdateReq.getBusinessStatus().length() > 128, "经营状态不能大于128");
        }

        // 成立日期：19
        if (StringUtils.isNotEmpty(companyUpdateReq.getEstablishmentDate())) {
            ValidatorUtils.checkTrueThrowEx(companyUpdateReq.getEstablishmentDate().length() > 19, "成立日期不能大于19");
        }

        // 注册地址：300
        if (StringUtils.isNotEmpty(companyUpdateReq.getAddress())) {
            ValidatorUtils.checkTrueThrowEx(companyUpdateReq.getAddress().length() > 300, "注册地址不能大于300");
        }

        // 注册资本：100
        if (StringUtils.isNotEmpty(companyUpdateReq.getRegistedCapital())) {
            ValidatorUtils.checkTrueThrowEx(companyUpdateReq.getRegistedCapital().length() > 100, "注册资本不能大于100");
        }

        // 实缴资本：128
        if (StringUtils.isNotEmpty(companyUpdateReq.getPaidCapital())) {
            ValidatorUtils.checkTrueThrowEx(companyUpdateReq.getPaidCapital().length() > 128, "实缴资本不能大于128");
        }

        // 所属行业：255
        if (StringUtils.isNotEmpty(companyUpdateReq.getIndustry())) {
            ValidatorUtils.checkTrueThrowEx(companyUpdateReq.getIndustry().length() > 255, "所属行业不能大于255");
        }

        // 曾用名：128
        if (StringUtils.isNotEmpty(companyUpdateReq.getLastName())) {
            ValidatorUtils.checkTrueThrowEx(companyUpdateReq.getLastName().length() > 128, "曾用名不能大于128");
        }

        // 工商注册号：128
        if (StringUtils.isNotEmpty(companyUpdateReq.getBusinessRegistNo())) {
            ValidatorUtils.checkTrueThrowEx(companyUpdateReq.getBusinessRegistNo().length() > 128, "工商注册号不能大于128");
        }

        // 组织机构代码：32
        if (StringUtils.isNotEmpty(companyUpdateReq.getOrganizationNo())) {
            ValidatorUtils.checkTrueThrowEx(companyUpdateReq.getOrganizationNo().length() > 32, "组织机构代码不能大于32");
        }

        // 纳税人识别号：128
        if (StringUtils.isNotEmpty(companyUpdateReq.getTaxpayerNo())) {
            ValidatorUtils.checkTrueThrowEx(companyUpdateReq.getTaxpayerNo().length() > 128, "纳税人识别号不能大于128");
        }

        // 营业期限开始时间：19
        if (StringUtils.isNotEmpty(companyUpdateReq.getBusinessStartTime())) {
            ValidatorUtils.checkTrueThrowEx(companyUpdateReq.getBusinessStartTime().length() > 19, "营业期限开始时间不能大于19");
        }

        // 营业期限结束时间：19
        if (StringUtils.isNotEmpty(companyUpdateReq.getBusinessEndTime())) {
            ValidatorUtils.checkTrueThrowEx(companyUpdateReq.getBusinessEndTime().length() > 19, "营业期限结束时间不能大于19");
        }

        // 核准日期：19
        if (StringUtils.isNotEmpty(companyUpdateReq.getApprovalDate())) {
            ValidatorUtils.checkTrueThrowEx(companyUpdateReq.getApprovalDate().length() > 19, "核准日期不能大于19");
        }

        // 登记机关：255
        if (StringUtils.isNotEmpty(companyUpdateReq.getRegistrationAuthority())) {
            ValidatorUtils.checkTrueThrowEx(companyUpdateReq.getRegistrationAuthority().length() > 255, "登记机关不能大于255");
        }

        // 参保人数：255
        if (StringUtils.isNotEmpty(companyUpdateReq.getInsuredNumber())) {
            ValidatorUtils.checkTrueThrowEx(companyUpdateReq.getInsuredNumber().length() > 255, "参保人数不能大于255");
        }

        // WEB网站：200
        if (StringUtils.isNotEmpty(companyUpdateReq.getWebSite())) {
            ValidatorUtils.checkTrueThrowEx(companyUpdateReq.getWebSite().length() > 200, "WEB网站不能大于200");
        }

        // 企业邮箱：200
        if (StringUtils.isNotEmpty(companyUpdateReq.getEmail())) {
            ValidatorUtils.checkTrueThrowEx(companyUpdateReq.getEmail().length() > 200, "企业邮箱不能大于200");
        }

        // 传真：100
        if (StringUtils.isNotEmpty(companyUpdateReq.getFax())) {
            ValidatorUtils.checkTrueThrowEx(companyUpdateReq.getFax().length() > 100, "传真不能大于100");
        }

        // 经营范围：500
        if (StringUtils.isNotEmpty(companyUpdateReq.getManageScope())) {
            ValidatorUtils.checkTrueThrowEx(companyUpdateReq.getManageScope().length() > 500, "经营范围不能大于500");
        }




        // 校验企业唯一性
        boolean isUnique = this.checkUniqueCompany(operationModel, companyUpdateReq.getCompanyNo(), companyUpdateReq.getCompanyName(), companyUpdateReq.getUnifiedSocialCode(), companyUpdateReq.getFactoryType());
        if (!isUnique) {
            throw new RuntimeException("企业已存在");
        }
    }
    @Override
    @Transactional
    public CompanyUpdateModel updateCompany(OperationModel operationModel, CompanyUpdateReq companyUpdateReq) {
        ValidatorUtils.checkEmptyThrowEx(operationModel.getEnterpriseNo(), "租户编号不能为空");
        ValidatorUtils.checkEmptyThrowEx(companyUpdateReq.getCompanyNo(), "企业编号不能为空");
        CompanyV2 companyV2 = companyV2Dao.findByEnterpriseNoAndCompanyNo(operationModel.getEnterpriseNo(), companyUpdateReq.getCompanyNo());
        ValidatorUtils.checkEmptyThrowEx(companyV2, "企业档案不存在");

        // 校验入参必填、范围
        validateUpdateCompany(operationModel, companyUpdateReq);

        String enterpriseNo = operationModel.getEnterpriseNo();
        String companyNo = companyUpdateReq.getCompanyNo();

        LambdaUpdateWrapper<CompanyV2> updateWrapper = Wrappers.lambdaUpdate(CompanyV2.class);
        updateWrapper.set(CompanyV2::getCompanyName, companyUpdateReq.getCompanyName());
        updateWrapper.set(CompanyV2::getUnifiedSocialCode, companyUpdateReq.getUnifiedSocialCode());
        updateWrapper.set(CompanyV2::getFactoryType, companyUpdateReq.getFactoryType());
        updateWrapper.set(CompanyV2::getCountryRegionId, companyUpdateReq.getCountryRegionId());
        updateWrapper.set(CompanyV2::getTaxCategory, companyUpdateReq.getTaxCategory());
        updateWrapper.set(CompanyV2::getTaxCategoryName, companyUpdateReq.getTaxCategoryName());
        updateWrapper.set(CompanyV2::getEconomicType, companyUpdateReq.getEconomicType());
        updateWrapper.set(CompanyV2::getEconomicTypeName, companyUpdateReq.getEconomicTypeName());
        updateWrapper.set(CompanyV2::getIsMedicalInstitution, companyUpdateReq.getIsMedicalInstitution());

        if (CommonIfEnum.YES.getValue().equals(companyUpdateReq.getIsMedicalInstitution())) {
            updateWrapper.set(CompanyV2::getInstitutionalType, companyUpdateReq.getInstitutionalType());
            updateWrapper.set(CompanyV2::getHospitalType, companyUpdateReq.getHospitalType());
            updateWrapper.set(CompanyV2::getHospitalClass, companyUpdateReq.getHospitalClass());
        } else {
            updateWrapper.set(CompanyV2::getInstitutionalType, null);
            updateWrapper.set(CompanyV2::getHospitalType, null);
            updateWrapper.set(CompanyV2::getHospitalClass, null);
        }

        updateWrapper.set(CompanyV2::getLegalPerson, companyUpdateReq.getLegalPerson());
        updateWrapper.set(CompanyV2::getBusinessStatus, companyUpdateReq.getBusinessStatus());
        updateWrapper.set(CompanyV2::getEstablishmentDate, companyUpdateReq.getEstablishmentDate());
        updateWrapper.set(CompanyV2::getRegionCode, companyUpdateReq.getRegionCode());
        updateWrapper.set(CompanyV2::getRegionName, companyUpdateReq.getRegionName());
        updateWrapper.set(CompanyV2::getAddress, companyUpdateReq.getAddress());
        updateWrapper.set(CompanyV2::getRegistedCapital, companyUpdateReq.getRegistedCapital());
        updateWrapper.set(CompanyV2::getPaidCapital, companyUpdateReq.getPaidCapital());
        updateWrapper.set(CompanyV2::getIndustry, companyUpdateReq.getIndustry());
        updateWrapper.set(CompanyV2::getLastName, companyUpdateReq.getLastName());
        updateWrapper.set(CompanyV2::getBusinessRegistNo, companyUpdateReq.getBusinessRegistNo());
        updateWrapper.set(CompanyV2::getOrganizationNo, companyUpdateReq.getOrganizationNo());
        updateWrapper.set(CompanyV2::getTaxpayerNo, companyUpdateReq.getTaxpayerNo());
        updateWrapper.set(CompanyV2::getBusinessStartTime, companyUpdateReq.getBusinessStartTime());
        updateWrapper.set(CompanyV2::getBusinessEndTime, companyUpdateReq.getBusinessEndTime());
        updateWrapper.set(CompanyV2::getBusinessLongTerm, companyUpdateReq.getBusinessLongTerm());
        updateWrapper.set(CompanyV2::getApprovalDate, companyUpdateReq.getApprovalDate());
        updateWrapper.set(CompanyV2::getRegistrationAuthority, companyUpdateReq.getRegistrationAuthority());
        updateWrapper.set(CompanyV2::getInsuredNumber, companyUpdateReq.getInsuredNumber());
        updateWrapper.set(CompanyV2::getIsListed, companyUpdateReq.getIsListed());
        updateWrapper.set(CompanyV2::getWebSite, companyUpdateReq.getWebSite());
        updateWrapper.set(CompanyV2::getEmail, companyUpdateReq.getEmail());
        updateWrapper.set(CompanyV2::getFax, companyUpdateReq.getFax());
        updateWrapper.set(CompanyV2::getManageScope, companyUpdateReq.getManageScope());
        updateWrapper.set(CompanyV2::getOperateName, operationModel.getUserName());
        updateWrapper.set(CompanyV2::getOperateNo, operationModel.getEmployerNo());
        String currentDate = DateUtil.getCurrentDate();
        updateWrapper.set(CompanyV2::getOperateTime, currentDate);

        //where条件
        updateWrapper.eq(CompanyV2::getEnterpriseNo, enterpriseNo);
        updateWrapper.eq(CompanyV2::getCompanyNo, companyNo);
        companyV2Dao.update(null, updateWrapper);


        CompanyV2 afterUpdateModelCompany = new CompanyV2();
        afterUpdateModelCompany.setEnterpriseNo(enterpriseNo);
        afterUpdateModelCompany.setCompanyNo(companyNo);
        afterUpdateModelCompany.setPartnership(companyV2.getPartnership());
        afterUpdateModelCompany.setCompanyName(companyUpdateReq.getCompanyName());
        afterUpdateModelCompany.setOperateName(operationModel.getUserName());
        afterUpdateModelCompany.setOperateNo(operationModel.getEmployerNo());
        afterUpdateModelCompany.setOperateTime(currentDate);

        CompanyUpdateModel companyUpdateModel = new CompanyUpdateModel();
        companyUpdateModel.setOldCompanyV2(companyV2);
        companyUpdateModel.setNewCompanyV2(afterUpdateModelCompany);
        companyUpdateModel.setOperationModel(operationModel);
        companyUpdateModel.setEnterpriseNo(enterpriseNo);
        companyUpdateModel.setUpdateSuccess(Boolean.TRUE);

        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                businessLogService.saveLog(DLogLevel.INFO.name(), ViewNameConstant.BDC_COMPANY_VIEW, companyNo, "编辑企业档案", "编辑企业档案", JSON.toJSONString(afterUpdateModelCompany), "");
            }
        });

        return companyUpdateModel;
    }

    @Override
    @Transactional
    public CompanyV2 saveOrUpdateCompany(OperationModel operationModel, String supCusCode, CompanySaveOrUpdateReq companySaveOrUpdateReq, CompanyPartnershipEnum companyPartnershipEnum) {
        CompanyV2 company = null;
        if (StringUtils.isNotBlank(companySaveOrUpdateReq.getCompanyNo())) {
            // 通过企业编号查询企业档案
            company = companyV2Dao.findByEnterpriseNoAndCompanyNo(operationModel.getEnterpriseNo(), companySaveOrUpdateReq.getCompanyNo());
            ValidatorUtils.checkEmptyThrowEx(company, "企业档案不存在");
        } else {
            // 通过企业名称、统一信用代码查询企业档案
            company = companyV2Dao.selectOne(new LambdaQueryWrapper<CompanyV2>()
                    .eq(CompanyV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                    .eq(CompanyV2::getCompanyName, companySaveOrUpdateReq.getCompanyName())
                    .eq(CompanyV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
        }
        if (null != company) {
            List<String> partnershipList = null;
            if (StringUtils.isNotBlank(company.getPartnership())) {
                partnershipList = JSON.parseArray(company.getPartnership(), String.class);
                if (!partnershipList.contains(companyPartnershipEnum.getType())) {
                    partnershipList.add(companyPartnershipEnum.getType());
                }
            } else {
                partnershipList = new ArrayList<>();
                partnershipList.add(companyPartnershipEnum.getType());
            }

            LambdaUpdateWrapper<CompanyV2> updateWrapper = Wrappers.lambdaUpdate(CompanyV2.class);
            updateWrapper.set(CompanyV2::getAddress, companySaveOrUpdateReq.getAddress());
            updateWrapper.set(CompanyV2::getPartnership, JSON.toJSONString(partnershipList));
            updateWrapper.set(CompanyV2::getFactoryType, companySaveOrUpdateReq.getFactoryType());
            updateWrapper.set(CompanyV2::getCountryRegionId, companySaveOrUpdateReq.getCountryRegionId());
            updateWrapper.set(CompanyV2::getTaxCategory, companySaveOrUpdateReq.getTaxCategory());
            updateWrapper.set(CompanyV2::getTaxCategoryName, companySaveOrUpdateReq.getTaxCategoryName());
            updateWrapper.set(CompanyV2::getEconomicType, companySaveOrUpdateReq.getEconomicType());
            updateWrapper.set(CompanyV2::getEconomicTypeName, companySaveOrUpdateReq.getEconomicTypeName());


//            Set<String> orgNos = new HashSet<>();
//            Set<String> orgCodes = new HashSet<>();
//            Set<String> orgNames = new HashSet<>();
//            List<SupplierV2> supplierV2List = supplierV2Service.queryByCompanyNo(operationModel.getEnterpriseNo(), companySaveOrUpdateReq.getCompanyNo());
//            List<CustomerV2> customerV2List = customerV2Service.queryByCompanyNo(operationModel.getEnterpriseNo(), companySaveOrUpdateReq.getCompanyNo());
//            if (CollectionUtils.isNotEmpty(supplierV2List)) {
//                for (SupplierV2 supplierV2 : supplierV2List) {
//                    if (CommonIfEnum.NO.getValue().equals(supplierV2.getIsAssociatedEnterprise())) {
//                        continue;
//                    }
//
//                    if (CompanyPartnershipEnum.SUPPLIER == companyPartnershipEnum && null != supCusCode && supCusCode.equals(supplierV2.getSupplierCode())) {
//                        continue;
//                    }
//
//                    if (null != supplierV2.getAssociatedOrgNo()) {
//                        orgNos.add(supplierV2.getAssociatedOrgNo());
//                    }
//
//                    if (null != supplierV2.getAssociatedOrgCode()) {
//                        orgCodes.add(supplierV2.getAssociatedOrgCode());
//                    }
//
//                    if (null != supplierV2.getAssociatedOrgName()) {
//                        orgNames.add(supplierV2.getAssociatedOrgName());
//                    }
//                }
//            }
//            if (CollectionUtils.isNotEmpty(customerV2List)) {
//                for (CustomerV2 customerV2 : customerV2List) {
//                    if (CommonIfEnum.NO.getValue().equals(customerV2.getIsAssociatedEnterprise())) {
//                        continue;
//                    }
//
//                    if (CompanyPartnershipEnum.CUSTOMER == companyPartnershipEnum && null != supCusCode && supCusCode.equals(customerV2.getCustomerCode())) {
//                        continue;
//                    }
//
//                    if (null != customerV2.getAssociatedOrgNo()) {
//                        orgNos.add(customerV2.getAssociatedOrgNo());
//                    }
//
//                    if (null != customerV2.getAssociatedOrgCode()) {
//                        orgCodes.add(customerV2.getAssociatedOrgCode());
//                    }
//
//                    if (null != customerV2.getAssociatedOrgName()) {
//                        orgNames.add(customerV2.getAssociatedOrgName());
//                    }
//                }
//            }
//            if (CommonIfEnum.YES.getValue().equals(companySaveOrUpdateReq.getIsAssociatedEnterprise())) {
//                orgNos.add(companySaveOrUpdateReq.getAssociatedOrgNo());
//                orgCodes.add(companySaveOrUpdateReq.getAssociatedOrgCode());
//                orgNames.add(companySaveOrUpdateReq.getAssociatedOrgName());
//            }
//
//            updateWrapper.set(CompanyV2::getAssociatedOrgNo, String.join(",", orgNos));
//            updateWrapper.set(CompanyV2::getAssociatedOrgCode, String.join(",", orgCodes));
//            updateWrapper.set(CompanyV2::getAssociatedOrgName, String.join(",", orgNames));
//            if (CollectionUtils.isNotEmpty(orgNos)) {
//                updateWrapper.set(CompanyV2::getIsAssociatedEnterprise, CommonIfEnum.YES.getValue());
//            } else {
//                updateWrapper.set(CompanyV2::getIsAssociatedEnterprise, CommonIfEnum.NO.getValue());
//            }

            saveSupCusAssociatedOrg(companySaveOrUpdateReq.getCompanyNo(), operationModel.getEnterpriseNo(), supCusCode, companySaveOrUpdateReq, companyPartnershipEnum);

            if (CompanyPartnershipEnum.CUSTOMER.getType().equals(companyPartnershipEnum.getType())) {
                updateWrapper.set(CompanyV2::getIsMedicalInstitution, companySaveOrUpdateReq.getIsMedicalInstitution());
                if (CommonIfEnum.YES.getValue().equals(companySaveOrUpdateReq.getIsMedicalInstitution())) {
                    updateWrapper.set(CompanyV2::getInstitutionalType, companySaveOrUpdateReq.getInstitutionalType());
                    updateWrapper.set(CompanyV2::getHospitalType, companySaveOrUpdateReq.getHospitalType());
                    updateWrapper.set(CompanyV2::getHospitalClass, companySaveOrUpdateReq.getHospitalClass());
                } else {
                    updateWrapper.set(CompanyV2::getInstitutionalType, null);
                    updateWrapper.set(CompanyV2::getHospitalType, null);
                    updateWrapper.set(CompanyV2::getHospitalClass, null);
                }
            }
            updateWrapper.set(CompanyV2::getOperateName, operationModel.getUserName());
            updateWrapper.set(CompanyV2::getOperateNo, operationModel.getEmployerNo());
            updateWrapper.set(CompanyV2::getOperateTime, DateUtil.getCurrentDate());

            // where条件
            updateWrapper.eq(CompanyV2::getEnterpriseNo, operationModel.getEnterpriseNo());
            updateWrapper.eq(CompanyV2::getCompanyNo, company.getCompanyNo());

            companyV2Dao.update(null, updateWrapper);

            return company;
        }


        //校验企业唯一性
        boolean companyUnique = this.checkUnifiedSocialCode(operationModel, null, companySaveOrUpdateReq.getUnifiedSocialCode(), companySaveOrUpdateReq.getFactoryType());
        ValidatorUtils.checkTrueThrowEx(!companyUnique, String.format("企业名称和统一社会信用代码重复,企业名称:%s,统一社会信用代码:%s,企业注册地域:%s", companySaveOrUpdateReq.getCompanyName(), companySaveOrUpdateReq.getUnifiedSocialCode(), FactoryTypeEnum.getByType(companySaveOrUpdateReq.getFactoryType()).getName()));

        //创建企业档案
        company = packCompanyV2(operationModel, companySaveOrUpdateReq, companyPartnershipEnum);
        companyV2Dao.insert(company);
        saveSupCusAssociatedOrg(company.getCompanyNo(), operationModel.getEnterpriseNo(), supCusCode, companySaveOrUpdateReq, companyPartnershipEnum);

        return company;
    }

    /**
     * 保存供应商/客户关联的内部组织关系
     * @param companyNo
     * @param enterpriseNo
     * @param supCusCode
     * @param companySaveOrUpdateReq
     * @param companyPartnershipEnum
     */
    @Override
    @Transactional
    public void saveSupCusAssociatedOrg(String companyNo ,String enterpriseNo, String supCusCode, CompanySaveOrUpdateReq companySaveOrUpdateReq, CompanyPartnershipEnum companyPartnershipEnum) {
        Integer sourceType = null;
        boolean formCustomerOrSupplier = false;
        if (CompanyPartnershipEnum.CUSTOMER == companyPartnershipEnum) {
            sourceType = CompanyAssociatedOrgTypeEnum.CUSTOMER.getValue();
            formCustomerOrSupplier = true;
        } else if (CompanyPartnershipEnum.SUPPLIER == companyPartnershipEnum) {
            sourceType = CompanyAssociatedOrgTypeEnum.SUPPLIER.getValue();
            formCustomerOrSupplier = true;
        }
        if (formCustomerOrSupplier) {
            CompanyAssociatedOrg companyAssociatedOrg = companyAssociatedOrgDAO.selectBySourceTypeAndCode(enterpriseNo, sourceType, supCusCode);
            if (CommonIfEnum.YES.getValue().equals(companySaveOrUpdateReq.getIsAssociatedEnterprise())) {
                // 校验组织是否已经关联了其他企业
                final Integer finalSourceType = sourceType;
                List<CompanyAssociatedOrg> companyAssociatedOrgs = companyAssociatedOrgDAO.selectList(Wrappers.lambdaQuery(CompanyAssociatedOrg.class)
                                .eq(CompanyAssociatedOrg::getEnterpriseNo, enterpriseNo)
                                .eq(CompanyAssociatedOrg::getDeleted, DeletedEnum.UN_DELETE.getValue())
                                .eq(CompanyAssociatedOrg::getAssociatedOrgNo, companySaveOrUpdateReq.getAssociatedOrgNo())
                                .and(item -> item.ne(CompanyAssociatedOrg::getSourceType, finalSourceType).ne(CompanyAssociatedOrg::getSourceCode, supCusCode))
                );
                List<String> repeatCompanyNoList = companyAssociatedOrgs.stream().map(CompanyAssociatedOrg::getCompanyNo).filter(Objects::nonNull).filter(e -> !e.equals(companySaveOrUpdateReq.getCompanyNo())).distinct().collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(repeatCompanyNoList)) {
                    List<Company> byEnterpriseNoAndCompanyNoList = companyDAO.findByEnterpriseNoAndCompanyNoList(enterpriseNo, repeatCompanyNoList);
                    String repeatCompanyName = byEnterpriseNoAndCompanyNoList.stream().map(Company::getCompanyName).collect(Collectors.joining(","));
                    throw new BusinessException("对应内部组织已关联企业档案【 " +repeatCompanyName+ " 】,与当前企业不一致");
                }

                if (companyAssociatedOrg == null) {
                    companyAssociatedOrg = new CompanyAssociatedOrg();
                    companyAssociatedOrg.setEnterpriseNo(enterpriseNo);
                    companyAssociatedOrg.setDeleted(DeletedEnum.UN_DELETE.getValue());
                    companyAssociatedOrg.setSourceType(sourceType);
                    companyAssociatedOrg.setSourceCode(supCusCode);
                    companyAssociatedOrg.setStatus(CommonStatusEnum.EFFECTIVE.getValue());
                    companyAssociatedOrg.setModifyTime(DateUtil.getCurrentDate());
                    companyAssociatedOrg.setCreateTime(DateUtil.getCurrentDate());
                    companyAssociatedOrg.setAssociatedOrgCode(companySaveOrUpdateReq.getAssociatedOrgCode());
                    companyAssociatedOrg.setAssociatedOrgNo(companySaveOrUpdateReq.getAssociatedOrgNo());
                    companyAssociatedOrg.setAssociatedOrgName(companySaveOrUpdateReq.getAssociatedOrgName());
                    companyAssociatedOrg.setCompanyNo(companyNo);
                    companyAssociatedOrgDAO.insert(companyAssociatedOrg);
                } else {
                    companyAssociatedOrg.setCompanyNo(companyNo);
                    companyAssociatedOrg.setAssociatedOrgCode(companySaveOrUpdateReq.getAssociatedOrgCode());
                    companyAssociatedOrg.setAssociatedOrgNo(companySaveOrUpdateReq.getAssociatedOrgNo());
                    companyAssociatedOrg.setAssociatedOrgName(companySaveOrUpdateReq.getAssociatedOrgName());
                    companyAssociatedOrg.setModifyTime(DateUtil.getCurrentDate());
                    companyAssociatedOrgDAO.updateAllById(enterpriseNo, companyAssociatedOrg);
                }
            } else {
                if (companyAssociatedOrg != null) {
                    companyAssociatedOrg.setAssociatedOrgCode(null);
                    companyAssociatedOrg.setAssociatedOrgNo(null);
                    companyAssociatedOrg.setAssociatedOrgName(null);
                    companyAssociatedOrg.setModifyTime(DateUtil.getCurrentDate());
                    companyAssociatedOrgDAO.updateAllById(enterpriseNo, companyAssociatedOrg);
                }
            }
        }
    }

    @Override
    public String preValidateSaveOrUpdateCompany(OperationModel operationModel, String supCusCode, CompanySaveOrUpdateReq companySaveOrUpdateReq, CompanyPartnershipEnum companyPartnershipEnum) {
        CompanyV2 company = null;
        if (StringUtils.isNotBlank(companySaveOrUpdateReq.getCompanyNo())) {
            // 通过企业编号查询企业档案
            company = companyV2Dao.findByEnterpriseNoAndCompanyNo(operationModel.getEnterpriseNo(), companySaveOrUpdateReq.getCompanyNo());
            ValidatorUtils.checkEmptyThrowEx(company, "企业档案不存在");
        } else {
            // 通过企业名称、统一信用代码查询企业档案
            company = companyV2Dao.selectOne(new LambdaQueryWrapper<CompanyV2>()
                    .eq(CompanyV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                    .eq(CompanyV2::getCompanyName, companySaveOrUpdateReq.getCompanyName())
                    .eq(CompanyV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
        }
        if (null != company) {
            return company.getCompanyNo();
        }


        //校验企业唯一性
        boolean companyUnique = this.checkUnifiedSocialCode(operationModel, null, companySaveOrUpdateReq.getUnifiedSocialCode(), companySaveOrUpdateReq.getFactoryType());
        ValidatorUtils.checkTrueThrowEx(!companyUnique, String.format("企业名称和统一社会信用代码重复,企业名称:%s,统一社会信用代码:%s,企业注册地域:%s", companySaveOrUpdateReq.getCompanyName(), companySaveOrUpdateReq.getUnifiedSocialCode(), FactoryTypeEnum.getByType(companySaveOrUpdateReq.getFactoryType()).getName()));

        return numberCenterService.createNumber(NumberCenterConstant.COMPANY_NO_GENERATE_KEY);
    }

    private CompanyV2 packCompanyV2(OperationModel operationModel, CompanySaveOrUpdateReq req, CompanyPartnershipEnum companyPartnershipEnum) {
        String companyNo = numberCenterService.createNumber(NumberCenterConstant.COMPANY_NO_GENERATE_KEY);

        CompanyV2 companyV2 = new CompanyV2();
        companyV2.setEnterpriseNo(operationModel.getEnterpriseNo());
        companyV2.setCompanyNo(companyNo);
        companyV2.setCompanyCode(companyNo);
        companyV2.setCompanyName(req.getCompanyName());
        companyV2.setUnifiedSocialCode(req.getUnifiedSocialCode());
        companyV2.setFactoryType(req.getFactoryType());
        companyV2.setCountryRegionId(req.getCountryRegionId());
        companyV2.setTaxCategory(req.getTaxCategory());
        companyV2.setTaxCategoryName(req.getTaxCategoryName());
        companyV2.setEconomicType(req.getEconomicType());
        companyV2.setEconomicTypeName(req.getEconomicTypeName());
        companyV2.setIsAssociatedEnterprise(req.getIsAssociatedEnterprise());
        companyV2.setAssociatedOrgNo(req.getAssociatedOrgNo());
        companyV2.setAssociatedOrgCode(req.getAssociatedOrgCode());
        companyV2.setAssociatedOrgName(req.getAssociatedOrgName());
        companyV2.setIsMedicalInstitution(req.getIsMedicalInstitution());
        companyV2.setInstitutionalType(req.getInstitutionalType());
        companyV2.setHospitalType(req.getHospitalType());
        companyV2.setHospitalClass(req.getHospitalClass());
        companyV2.setPartnership(JSON.toJSONString(Collections.singletonList(companyPartnershipEnum.getType())));
        companyV2.setLegalPerson(req.getLegalPerson());
        companyV2.setBusinessStatus(req.getBusinessStatus());
        companyV2.setEstablishmentDate(req.getEstablishmentDate());
        companyV2.setBusinessStartTime(req.getBusinessStartTime());
        companyV2.setBusinessEndTime(req.getBusinessEndTime());
        companyV2.setBusinessLongTerm(req.getBusinessLongTerm());
        companyV2.setRegistedCapital(req.getRegistedCapital());
        companyV2.setPaidCapital(req.getPaidCapital());
        companyV2.setCompanyBusinessType(req.getCompanyBusinessType());
        companyV2.setIndustry(req.getIndustry());
        companyV2.setBusinessRegistNo(req.getBusinessRegistNo());
        companyV2.setOrganizationNo(req.getOrganizationNo());
        companyV2.setTaxpayerNo(req.getTaxpayerNo());
        companyV2.setTaxpayerQualification(req.getTaxpayerQualification());
        companyV2.setIsListed(req.getIsListed());
        companyV2.setApprovalDate(req.getApprovalDate());
        companyV2.setRegistrationAuthority(req.getRegistrationAuthority());
        companyV2.setRegionCode(req.getRegionCode());
        companyV2.setRegionName(req.getRegionName());
        companyV2.setAddress(req.getAddress());
        companyV2.setLastName(req.getLastName());
        companyV2.setInsuredNumber(req.getInsuredNumber());
        companyV2.setWebSite(req.getWebSite());
        companyV2.setEmail(req.getEmail());
        companyV2.setFax(req.getFax());
        companyV2.setManageScope(req.getManageScope());
        companyV2.setStatus(StatusEnum.EFFECTIVE.getValue());
        companyV2.setDeleted(DeletedEnum.UN_DELETE.getValue());
        companyV2.setAddress(req.getAddress());
        CommonUtil.fillCreatInfo(companyV2);
        return companyV2;

    }

    /**
     * 查询租户下指定企业编号的基本信息
     *
     * @param enterpriseNo
     * @param companyNoList
     * @return
     */
    @Override
    public List<Company2InfoResponse> findListByCompanyNoList(String enterpriseNo, List<String> companyNoList) {
        if (CollectionUtil.isEmpty(companyNoList)) {
            return new ArrayList<>();
        }
        List<CompanyV2> companyV2s = companyV2Dao.selectList(new LambdaQueryWrapper<CompanyV2>()
                .eq(CompanyV2::getEnterpriseNo, enterpriseNo)
                .in(CompanyV2::getCompanyNo, companyNoList)
                .eq(CompanyV2::getDeleted, DeletedEnum.UN_DELETE.getValue())
        );
        return BeanUtil.copyFieldsList(companyV2s, Company2InfoResponse.class);
    }

    public List<BankType> getBankTypeList() {
        return bankTypeDAO.getList();
    }

    public List<CompanyBankVO> findBankList(String enterpriseNo, String companyNo) {
        List<CompanyBankV2> bankList = companyBankV2DAO.selectList(Wrappers.<CompanyBankV2>lambdaQuery()
                .eq(CompanyBankV2::getEnterpriseNo, enterpriseNo)
                .eq(CompanyBankV2::getCompanyNo, companyNo)
                .eq(CompanyBankV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));

        if (CollectionUtils.isNotEmpty(bankList)) {
            final List<BankType> bankTypeList = bankTypeDAO.getList();
            final Map<String, BankType> bankTypeMap = bankTypeList.stream().collect(Collectors.toMap(BankType::getBankCode, Function.identity(), (v1, v2) -> v1));

            final Map<String, String> currencyMap = dictEnterpriseService.findCurrencyList(enterpriseNo);

            List<CompanyBankVO> companyBankVoList = new ArrayList<>();
            bankList.forEach(t -> {
                CompanyBankVO companyBankVO = new CompanyBankVO();
                BeanUtils.copyProperties(t, companyBankVO);

                companyBankVO.setBankTypeName(bankTypeMap.getOrDefault(t.getBankType(), new BankType()).getBankName());
                companyBankVO.setCurrencyName(currencyMap.getOrDefault(t.getCurrencyId(), ""));
                companyBankVO.setAccountTypeName(BankAccountTypeEnum.getNameByValue(t.getAccountType()));
                companyBankVO.setStatusName(BankStatusEnum.getNameByValue(t.getStatus()));
                companyBankVO.setIsDefaultName(CommonIfEnum.getNameByValue(t.getIsDefault()));

                companyBankVoList.add(companyBankVO);
            });

            return companyBankVoList;
        }

        return Collections.emptyList();
    }

    @Override
    public List<CompanyBankV2> findBankList(String enterpriseNo, List<String> companyNoList) {
        ValidatorUtils.checkEmptyThrowEx(enterpriseNo, "租户编号不能为空");
        ValidatorUtils.checkEmptyThrowEx(companyNoList, "企业编号集合不能为空");
        return companyBankV2DAO.selectList(Wrappers.<CompanyBankV2>lambdaQuery()
                .eq(CompanyBankV2::getEnterpriseNo, enterpriseNo)
                .in(CompanyBankV2::getCompanyNo, companyNoList)
                .eq(CompanyBankV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
    }

    @Transactional
    public Map<String, List<?>> saveOrUpdateBank(OperationModel operationModel, List<CompanyBankReq> companyBankReqList, String companyNo, String manageOrgNo) {
        List<CompanyBankV2> companyBankV2s = companyBankV2DAO.selectList(Wrappers.<CompanyBankV2>lambdaQuery()
                .eq(CompanyBankV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CompanyBankV2::getCompanyNo, companyNo)
                .eq(CompanyBankV2::getDeleted, DeletedEnum.UN_DELETE.getValue())
        );
        Map<String, List<?>> diffResultMap = BeanUtil.diffCUD(companyBankV2s, companyBankReqList);

        List<CompanyBankReq> addList = (List<CompanyBankReq>) diffResultMap.get("addList");
        List<CompanyBankReq> updateList = (List<CompanyBankReq>) diffResultMap.get("updateList");
        List<CompanyBankV2> deleteList = (List<CompanyBankV2>) diffResultMap.get("deleteList");

        if (CollectionUtils.isNotEmpty(addList)) {
            List<CompanyBankV2> addBankList = new ArrayList<>();
            for (CompanyBankReq paramBank : addList) {
                CompanyBankV2 companyBank = new CompanyBankV2();
                companyBank.setEnterpriseNo(operationModel.getEnterpriseNo());
                companyBank.setCompanyNo(companyNo);
                companyBank.setBankCode(paramBank.getBankCode());
                companyBank.setBankType(paramBank.getBankType());
                companyBank.setOpenBank(paramBank.getOpenBank());
                companyBank.setAccountName(paramBank.getAccountName());
                companyBank.setAccountNo(paramBank.getAccountNo());
                companyBank.setAccountType(paramBank.getAccountType());
                companyBank.setLinkPerson(paramBank.getLinkPerson());
                companyBank.setLinkPhone(paramBank.getLinkPhone());
                companyBank.setStatus(paramBank.getStatus());
                companyBank.setIsDefault(paramBank.getIsDefault());
                companyBank.setManageOrgNo(manageOrgNo);
                companyBank.setCurrencyId(paramBank.getCurrencyId());
                addBankList.add(companyBank);
            }
            companyBankV2DAO.addBatch(addBankList);

            diffResultMap.put("addedList", addBankList);
        }

        if (CollectionUtils.isNotEmpty(updateList)) {
            List<CompanyBankV2> updateBankList = new ArrayList<>();
            for (CompanyBankReq paramBank : updateList) {
                CompanyBankV2 companyBank = new CompanyBankV2();
                companyBank.setId(paramBank.getId());
                companyBank.setEnterpriseNo(operationModel.getEnterpriseNo());
                companyBank.setCompanyNo(companyNo);
                companyBank.setBankCode(paramBank.getBankCode());
                companyBank.setBankType(paramBank.getBankType());
                companyBank.setOpenBank(paramBank.getOpenBank());
                companyBank.setAccountName(paramBank.getAccountName());
                companyBank.setAccountNo(paramBank.getAccountNo());
                companyBank.setAccountType(paramBank.getAccountType());
                companyBank.setLinkPerson(paramBank.getLinkPerson());
                companyBank.setLinkPhone(paramBank.getLinkPhone());
                companyBank.setStatus(paramBank.getStatus());
                companyBank.setIsDefault(paramBank.getIsDefault());
                companyBank.setManageOrgNo(manageOrgNo);
                companyBank.setCurrencyId(paramBank.getCurrencyId());
                updateBankList.add(companyBank);
            }
            companyBankV2DAO.updateByIdBatch(updateBankList);
        }

        if (CollectionUtils.isNotEmpty(deleteList)) {
            CompanyBankV2 companyBank = new CompanyBankV2();
            companyBank.setDeleted(DeletedEnum.DELETED.getValue());
            companyBankV2DAO.update(companyBank, Wrappers.<CompanyBankV2>lambdaQuery()
                    .eq(CompanyBankV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                    .in(CompanyBankV2::getId, deleteList.stream().map(CompanyBankV2::getId).collect(Collectors.toList())));
        }

        return diffResultMap;
    }

    @Override
    @Transactional
    public List<CompanyBankV2> overwriteBank(OperationModel operationModel, List<CompanyBankReq> companyBankReqList, String companyNo, String manageOrgNo) {
        CompanyBankV2 toDeleteCompanyBank = new CompanyBankV2();
        toDeleteCompanyBank.setDeleted(DeletedEnum.DELETED.getValue());
        companyBankV2DAO.update(toDeleteCompanyBank, Wrappers.<CompanyBankV2>lambdaQuery()
                .eq(CompanyBankV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CompanyBankV2::getManageOrgNo, manageOrgNo)
                .eq(CompanyBankV2::getCompanyNo, companyNo)
                .eq(CompanyBankV2::getDeleted, DeletedEnum.UN_DELETE.getValue())
        );

        if (CollectionUtils.isNotEmpty(companyBankReqList)) {
            List<CompanyBankV2> addBankList = new ArrayList<>();
            for (CompanyBankReq paramBank : companyBankReqList) {
                CompanyBankV2 companyBank = new CompanyBankV2();
                companyBank.setEnterpriseNo(operationModel.getEnterpriseNo());
                companyBank.setCompanyNo(companyNo);
                companyBank.setBankCode(paramBank.getBankCode());
                companyBank.setBankType(paramBank.getBankType());
                companyBank.setOpenBank(paramBank.getOpenBank());
                companyBank.setAccountName(paramBank.getAccountName());
                companyBank.setAccountNo(paramBank.getAccountNo());
                companyBank.setAccountType(paramBank.getAccountType());
                companyBank.setLinkPerson(paramBank.getLinkPerson());
                companyBank.setLinkPhone(paramBank.getLinkPhone());
                companyBank.setStatus(paramBank.getStatus());
                companyBank.setIsDefault(paramBank.getIsDefault());
                companyBank.setManageOrgNo(manageOrgNo);
                companyBank.setCurrencyId(paramBank.getCurrencyId());

                addBankList.add(companyBank);
            }
            companyBankV2DAO.addBatch(addBankList);

            return addBankList;
        }

        return Collections.emptyList();
    }

    @Override
    public CompanyV2 findByEnterpriseNoAndCompanyNo(String enterpriseNo, String companyNo) {
        return companyV2Dao.findByEnterpriseNoAndCompanyNo(enterpriseNo, companyNo);
    }

    @Override
    public List<CompanyV2> findByEnterpriseNoAndCompanyNoList(String enterpriseNo, List<String> companyNoList) {
        return companyV2Dao.findByEnterpriseNoAndCompanyNoList(enterpriseNo, companyNoList);
    }

    @Override
    public List<CompanyShippingAddressResponse> getLinkAddressListBySourceNoList(String enterpriseNo, String useOrgNo, LinkmanTypeEnum typeEnum, List<String> sourceList) {
        ValidatorUtils.checkEmptyThrowEx(enterpriseNo, "租户编号不能为空");
        ValidatorUtils.checkEmptyThrowEx(useOrgNo, "使用组织不能为空");
        ValidatorUtils.checkEmptyThrowEx(typeEnum, "客商类型不能为空");
        ValidatorUtils.checkEmptyThrowEx(sourceList, "查询源不能为空");

        List<CompanyShippingAddressV2> companyShippingAddressBySourceNoList = companyShippingAddressV2DAO.getCompanyShippingAddressBySourceNoList(enterpriseNo, useOrgNo, typeEnum.getValue(), sourceList);

        return convert2CompanyShippingAddressResponse(companyShippingAddressBySourceNoList);
    }

    @Override
    public List<CompanyLinkmanResponse> getLinkmanListBySourceNoList(String enterpriseNo, String useOrgNo, LinkmanTypeEnum typeEnum, List<String> sourceList) {
        ValidatorUtils.checkEmptyThrowEx(enterpriseNo, "租户编号不能为空");
        ValidatorUtils.checkEmptyThrowEx(useOrgNo, "使用组织不能为空");
        ValidatorUtils.checkEmptyThrowEx(typeEnum, "客商类型不能为空");
        ValidatorUtils.checkEmptyThrowEx(sourceList, "查询源不能为空");

        List<CompanyLinkmanV2> companyLinkmanBySourceNoList = companyLinkmanV2DAO.getCompanyLinkmanListBySourceNoList(enterpriseNo, useOrgNo, typeEnum.getValue(), sourceList);

        return convert2CompanyLinkmanResponse(companyLinkmanBySourceNoList);
    }

    private List<CompanyShippingAddressResponse> convert2CompanyShippingAddressResponse(List<CompanyShippingAddressV2> companyShippingAddressV2List) {
        if (CollectionUtils.isEmpty(companyShippingAddressV2List)) {
            return Collections.emptyList();
        }

        List<AreaCodeVo> areaCodeList = dictEnterpriseService.findAreaByCodes(companyShippingAddressV2List.stream().map(CompanyShippingAddressV2::getRegionCode).collect(Collectors.toList()));
        Map<String, AreaCodeVo> areaMap = areaCodeList.stream().collect(Collectors.toMap(AreaCodeVo::getAreaCode, t -> t));

        List<CompanyShippingAddressResponse> companyShippingAddressResponseList = BeanUtil.copyFieldsList(companyShippingAddressV2List, CompanyShippingAddressResponse.class);

        for (CompanyShippingAddressResponse companyShippingAddressRespons : companyShippingAddressResponseList) {
            if (areaMap.containsKey(companyShippingAddressRespons.getRegionCode())) {
                AreaCodeVo areaCodeVo = areaMap.get(companyShippingAddressRespons.getRegionCode());
                companyShippingAddressRespons.setRegionName(areaCodeVo.getAreaName());
                companyShippingAddressRespons.setRegionFullName(areaCodeVo.getAreaFullName());
            }
        }

        return companyShippingAddressResponseList;
    }

    private List<CompanyLinkmanResponse> convert2CompanyLinkmanResponse(List<CompanyLinkmanV2> companyLinkmanV2List) {
        if (CollectionUtils.isEmpty(companyLinkmanV2List)) {
            return Collections.emptyList();
        }

        return BeanUtil.copyFieldsList(companyLinkmanV2List, CompanyLinkmanResponse.class);
    }

    public List<CompanyBankResponse> convert2BankResponse(Map<String, String> currencyMap, List<CompanyBankV2> companyBankV2s) {
        if (CollectionUtils.isEmpty(companyBankV2s)) {
            return Collections.emptyList();
        }

        List<CompanyBankResponse> companyBankResponses = BeanUtil.copyFieldsList(companyBankV2s, CompanyBankResponse.class);
        for (CompanyBankResponse companyBankResponse : companyBankResponses) {
            companyBankResponse.setCurrencyName(currencyMap.getOrDefault(companyBankResponse.getCurrency(), ""));
        }
        return companyBankResponses;
    }


    @Override
    public List<Company2InfoResponse> findCompanyList(CompanyQueryListPageReq queryReq) {
        List<CompanyV2> companyListPage = companyV2Dao.findCompanyListPage(queryReq);
        return BeanUtil.copyFieldsList(companyListPage, Company2InfoResponse.class);
    }

    @Override
    public String generateCompanyNo() {
        return numberCenterService.createNumber(NumberCenterConstant.COMPANY_NO_GENERATE_KEY);
    }

    /**
     * 通过组织查询企业相关信息
     *
     * @param params
     * @return
     */
    @Override
    public List<CompanyBasicResponse> findCompanyBasicInfoByOrgList(CompanyAssociateOrgReq params) {
        ValidatorUtils.checkEmptyThrowEx(params, "查询参数不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getEnterpriseNo(), "租户编号不能为空");
        if (CollectionUtils.isEmpty(params.getOrgCodeList()) && CollectionUtils.isEmpty(params.getOrgNoList())) {
            throw new BusinessException("组织编号不能为空");
        }
        List<CompanyAssociatedOrg> companyAssociatedOrgList = companyAssociatedOrgDAO.selectList(Wrappers.lambdaQuery(CompanyAssociatedOrg.class)
                .eq(CompanyAssociatedOrg::getEnterpriseNo, params.getEnterpriseNo())
                .eq(CompanyAssociatedOrg::getDeleted, DeletedEnum.UN_DELETE.getValue())
                .in(CollectionUtils.isNotEmpty(params.getOrgNoList()), CompanyAssociatedOrg::getAssociatedOrgNo, params.getOrgNoList())
                .in(CollectionUtils.isNotEmpty(params.getOrgCodeList()), CompanyAssociatedOrg::getAssociatedOrgCode, params.getOrgCodeList())
                .eq(Objects.nonNull(params.getSourceType()), CompanyAssociatedOrg::getSourceType, params.getSourceType())
                .isNotNull(CompanyAssociatedOrg::getCompanyNo)
        );
        List<String> companyNoList = companyAssociatedOrgList.stream().map(CompanyAssociatedOrg::getCompanyNo).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(companyNoList)) {
            return Collections.emptyList();
        }
        List<CompanyV2> companyList = companyV2Dao.findByEnterpriseNoAndCompanyNoList(params.getEnterpriseNo(), companyNoList);
        if (CollectionUtils.isEmpty(companyList)) {
            return Collections.emptyList();
        }
        return BeanUtil.copyFieldsList(companyList, CompanyBasicResponse.class);
    }

    @Override
    public List<CompanyBasicResponse> findCompanyBasicInfo(CompanyCertQueryReq params) {
        ValidatorUtils.checkEmptyThrowEx(params, "查询参数不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getEnterpriseNo(), "租户编号不能为空");
        if (CollectionUtils.isEmpty(params.getCompanyNoList())) {
            throw new BusinessException("企业编号不能为空");
        }
        List<CompanyV2> companyList = companyV2Dao.findByEnterpriseNoAndCompanyNoList(params.getEnterpriseNo(), params.getCompanyNoList());
        if (CollectionUtils.isEmpty(companyList)) {
            return Collections.emptyList();
        }
        return BeanUtil.copyFieldsList(companyList, CompanyBasicResponse.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TenantCompanySaveModel saveTenantCompany(OperationModel operationModel, CompanyUpdateReq companyUpdateReq) {
        CompanyV2 companyV2 = companyV2Dao.findByEnterpriseNoAndCompanyNo(operationModel.getEnterpriseNo(), companyUpdateReq.getCompanyNo());
        TenantCompanySaveModel tenantCompanySaveModel = new TenantCompanySaveModel();

        if(companyV2 == null) {
            tenantCompanySaveModel.setUpdate(false);
            CompanySaveReq companySaveReq = new CompanySaveReq();
            BeanUtils.copyProperties(companyUpdateReq, companySaveReq);
            companySaveReq.setSpecificNewCompanyNo(companyUpdateReq.getCompanyNo());
            CompanyBasicVO companyBasicVO = this.saveCompany(operationModel, companySaveReq);
            tenantCompanySaveModel.setCompanyBasicVO(companyBasicVO);
        } else {
            tenantCompanySaveModel.setUpdate(true);
            CompanyUpdateModel companyUpdateModel = this.updateCompany(operationModel, companyUpdateReq);
            tenantCompanySaveModel.setCompanyUpdateModel(companyUpdateModel);
            CompanyBasicVO companyBasicVO = new CompanyBasicVO();
            BeanUtils.copyProperties(companyUpdateModel.getNewCompanyV2(), companyBasicVO);
            tenantCompanySaveModel.setCompanyBasicVO(companyBasicVO);
        }

        // 保存组织和企业关系
        CompanyAssociatedOrg companyAssociatedOrg = companyAssociatedOrgDAO.selectOne(Wrappers.lambdaQuery(CompanyAssociatedOrg.class)
                .eq(CompanyAssociatedOrg::getSourceType, CompanyAssociatedOrgTypeEnum.TAXPAYER_ORG.getValue())
                .eq(CompanyAssociatedOrg::getSourceCode, operationModel.getOrgNo())
                .eq(CompanyAssociatedOrg::getDeleted, DeletedEnum.UN_DELETE.getValue())
        );
        if (companyAssociatedOrg == null) {
            companyAssociatedOrg = new CompanyAssociatedOrg();
            companyAssociatedOrg.setEnterpriseNo(operationModel.getEnterpriseNo());
            companyAssociatedOrg.setDeleted(DeletedEnum.UN_DELETE.getValue());
            companyAssociatedOrg.setSourceType(CompanyAssociatedOrgTypeEnum.TAXPAYER_ORG.getValue());
            companyAssociatedOrg.setStatus(CommonStatusEnum.EFFECTIVE.getValue());
            companyAssociatedOrg.setSourceCode(operationModel.getOrgNo());
            companyAssociatedOrg.setModifyTime(DateUtil.getCurrentDate());
            companyAssociatedOrg.setCreateTime(DateUtil.getCurrentDate());
            companyAssociatedOrg.setCompanyNo(companyUpdateReq.getCompanyNo());
            companyAssociatedOrg.setAssociatedOrgName(operationModel.getOrgName());
            companyAssociatedOrg.setAssociatedOrgNo(operationModel.getOrgNo());
            companyAssociatedOrg.setAssociatedOrgCode(operationModel.getOrgCode());
            companyAssociatedOrgDAO.insert(companyAssociatedOrg);
        } else if (!Objects.equals(companyAssociatedOrg.getCompanyNo(), companyUpdateReq.getCompanyNo())) {
            companyAssociatedOrg.setCompanyNo(companyUpdateReq.getCompanyNo());
            companyAssociatedOrg.setModifyTime(DateUtil.getCurrentDate());
            companyAssociatedOrgDAO.updateById(operationModel.getEnterpriseNo(), companyAssociatedOrg);
        }

        return tenantCompanySaveModel;
    }

    @Override
    public List<CompanyAssociatedOrgResponse> findCompanyAssociatedOrgList(CompanyAssociateOrgReq req) {
        ValidatorUtils.checkEmptyThrowEx(req, "查询参数不能为空");
        ValidatorUtils.checkEmptyThrowEx(req.getEnterpriseNo(), "租户编号不能为空");
        if (CollectionUtils.isEmpty(req.getOrgNoList()) && CollectionUtils.isEmpty(req.getOrgCodeList())) {
            return Collections.emptyList();
        }
        List<CompanyAssociatedOrg> companyAssociatedOrgs = companyAssociatedOrgDAO.selectList(Wrappers.lambdaQuery(CompanyAssociatedOrg.class)
                .eq(CompanyAssociatedOrg::getEnterpriseNo, req.getEnterpriseNo())
                .eq(CompanyAssociatedOrg::getDeleted, DeletedEnum.UN_DELETE.getValue())
                .eq(Objects.nonNull(req.getSourceType()), CompanyAssociatedOrg::getSourceType, req.getSourceType())
                .in(CollectionUtils.isNotEmpty(req.getOrgNoList()), CompanyAssociatedOrg::getAssociatedOrgNo, req.getOrgNoList())
                .in(CollectionUtils.isNotEmpty(req.getOrgCodeList()), CompanyAssociatedOrg::getAssociatedOrgCode, req.getOrgCodeList())
        );
        return BeanUtil.copyFieldsList(companyAssociatedOrgs, CompanyAssociatedOrgResponse.class);
    }

    /**
     * 查询企业关联的内部组织信息Map
     * @param enterpriseNo
     * @param companyV2List
     * @return
     */
    public Map<String, Set<OrganizationVo>> getCompanyAssociatedOrgMap(String enterpriseNo, List<CompanyV2> companyV2List) {
        Map<String, Set<OrganizationVo>> companyAssociatedOrgMap = new HashMap<>();
        if (CollectionUtils.isEmpty(companyV2List)) {
            return companyAssociatedOrgMap;
        }
        List<CompanyAssociatedOrg> companyAssociatedOrgs = companyAssociatedOrgDAO.selectList(Wrappers.lambdaQuery(CompanyAssociatedOrg.class)
                .eq(CompanyAssociatedOrg::getEnterpriseNo, enterpriseNo)
                .eq(CompanyAssociatedOrg::getDeleted, DeletedEnum.UN_DELETE.getValue())
                .isNotNull(CompanyAssociatedOrg::getAssociatedOrgNo)
                .orderByAsc(CompanyAssociatedOrg::getId)
                .in(CompanyAssociatedOrg::getCompanyNo, companyV2List.stream().map(CompanyV2::getCompanyNo).collect(Collectors.toList()))
        );
        List<OrganizationVo> listNoAuth = organizationService.findListNoAuth(enterpriseNo, companyAssociatedOrgs.stream().map(CompanyAssociatedOrg::getAssociatedOrgNo).distinct().collect(Collectors.toList()));
        Map<String, OrganizationVo> organizationVoMap = listNoAuth.stream().collect(Collectors.toMap(OrganizationVo::getOrgNo, Function.identity(), (o, n) -> n));
        for (CompanyAssociatedOrg companyAssociatedOrg : companyAssociatedOrgs) {
            OrganizationVo organizationVo = organizationVoMap.get(companyAssociatedOrg.getAssociatedOrgNo());
            if (organizationVo != null) {
                Set<OrganizationVo> organizationVos = companyAssociatedOrgMap.computeIfAbsent(companyAssociatedOrg.getCompanyNo(), k -> new LinkedHashSet<>());
                organizationVos.add(organizationVo);
            }
        }
        return companyAssociatedOrgMap;
    }
}
