package com.yyigou.dsrp.cdc.service.v2.customer;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yyigou.ddc.common.page.PageDto;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.dsrp.cdc.api.customer.dto.CustomerExternalSaveDTO;
import com.yyigou.dsrp.cdc.api.v2.customer.vo.CustomerCategoryTreeVO;
import com.yyigou.dsrp.cdc.api.v2.customer.vo.CustomerCategoryVO;
import com.yyigou.dsrp.cdc.common.util.OperationModel;
import com.yyigou.dsrp.cdc.dao.v2.customer.entity.CustomerCategoryV2;
import com.yyigou.dsrp.cdc.model.v2.customer.req.*;

import java.util.List;

public interface CustomerCategoryV2Service extends IService<CustomerCategoryV2> {

    List<CustomerCategoryTreeVO> queryTree(OperationModel operationModel, CustomerCategoryQueryTreeReq queryTreeReq);

    List<CustomerCategoryTreeVO> queryManageTree(OperationModel operationModel, CustomerCategoryQueryCategoryTreeReq queryTreeReq);

    List<CustomerCategoryTreeVO> queryUseTree(OperationModel operationModel, CustomerCategoryQueryUseCategoryTreeReq queryTreeReq);

    List<CustomerCategoryTreeVO> queryReferTree(OperationModel operationModel, CustomerCategoryQuerySolutionTreeReq queryTreeReq);

    PageVo<CustomerCategoryVO> queryListPage(OperationModel operationModel, CustomerCategoryQueryListPageReq queryListReq, PageDto pageDto);

    List<CustomerCategoryVO> queryList(CustomerCategoryQueryListPageReq queryListReq);

    Boolean checkUniqueName(OperationModel operationModel, String no, String name, String parentNo);

    Boolean checkUniqueCode(OperationModel operationModel, String no, String code);

    Boolean checkUseOrgRemoval(OperationModel operationModel, CustomerCategoryCheckUseOrgRemovalReq params);

    CustomerCategoryVO saveCustomerCategory(OperationModel operationModel, CustomerCategorySaveReq customerCategorySaveReq);

    CustomerCategoryVO getCustomerCategory(OperationModel operationModel, CustomerCategoryGetReq customerCategoryGetReq);

    CustomerCategoryVO updateCustomerCategory(OperationModel operationModel, CustomerCategoryUpdateReq customerCategoryUpdateReq);

    Boolean deleteCustomerCategory(OperationModel operationModel, CustomerCategoryDeleteReq customerCategoryDeleteReq);

    Boolean changeCustomerCategoryStatus(OperationModel operationModel, CustomerCategoryChangeStatusReq customerCategoryChangeStatusReq);

    CustomerCategoryV2 getByNo(String enterpriseNo, String no);

    List<CustomerCategoryV2> getByNoList(String enterpriseNo, List<String> noList);

    void createAndUpdateCategoryForDA(OperationModel operationModel, List<CustomerExternalSaveDTO> params);
}
