package com.yyigou.dsrp.cdc.service.supplier;

import com.yyigou.ddc.common.page.PageDto;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.dsrp.cdc.api.supply.dto.SupplierBankQueryDTO;
import com.yyigou.dsrp.cdc.api.supply.vo.SupplierBankPageVO;
import com.yyigou.dsrp.cdc.common.util.OperationModel;

public interface SupplierBankService {

    PageVo<SupplierBankPageVO> findBankPage(OperationModel operationModel, SupplierBankQueryDTO params, PageDto pageDto);
}
