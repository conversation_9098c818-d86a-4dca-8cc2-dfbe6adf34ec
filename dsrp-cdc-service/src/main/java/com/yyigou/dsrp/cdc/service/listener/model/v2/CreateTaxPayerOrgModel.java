package com.yyigou.dsrp.cdc.service.listener.model.v2;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/05/23
 */
@Data
public class CreateTaxPayerOrgModel {

    @EntityField(name = "关联组织编号")
    private String associatedOrgNo;

    @EntityField(name = "关联组织编码")
    private String associatedOrgCode;

    @EntityField(name = "关联组织名称")
    private String associatedOrgName;

    @EntityField(name = "租户编号")
    private String enterpriseNo;

    @EntityField(name = "纳税人识别号")
    private String taxpayerIdentityNumber;

    @EntityField(name = "纳税人名称")
    private String taxpayerIdentityName;

}
