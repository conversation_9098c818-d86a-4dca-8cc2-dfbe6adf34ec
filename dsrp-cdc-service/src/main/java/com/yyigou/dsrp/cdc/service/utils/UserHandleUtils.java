package com.yyigou.dsrp.cdc.service.utils;

import com.yyigou.ddc.common.service.ServiceBaseAbstract;
import com.yyigou.ddc.common.service.SessionUser;
import com.yyigou.ddc.services.ddc.uim.enums.TenantTypeEnum;
import com.yyigou.dsrp.cdc.common.util.OperationModel;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;

public class UserHandleUtils {

    public static <T extends OperationModel> void supplementOperationInfo(T params) {
        SessionUser session = ServiceBaseAbstract.currentRequest().getSession();
        params.setEnterpriseNo(session.getEnterpriseNo());
        params.setEnterpriseName(session.getEnterpriseName());
        params.setCompanyNo(session.getCompanyNo());
        params.setCompanyName(session.getCompanyName());
        params.setEmployerNo(session.getEmployerNo());
        params.setUserName(session.getUserName());
        Map<String, Object> extInfo = session.getExtInfo();
        // 获取集团租户编号，如果有
        String groupTenantNo = MapUtils.getString(extInfo, "groupTenantNo");
        if (StringUtils.isNotBlank(groupTenantNo) && !groupTenantNo.equals(session.getEnterpriseNo())) {
            params.setSubTenant(true);
        }
        params.setGroupTenantNo(groupTenantNo);
        params.setExtInfo(extInfo);
    }

    /**
     * 获取操作人信息
     *
     * @return画
     */
    public static OperationModel getOperationModel() {
        SessionUser session = ServiceBaseAbstract.currentRequest().getSession();
        OperationModel operationModel = new OperationModel();
        operationModel.setEnterpriseNo(session.getEnterpriseNo());
        operationModel.setEnterpriseName(session.getEnterpriseName());
        operationModel.setCompanyNo(session.getCompanyNo());
        operationModel.setCompanyName(session.getCompanyName());
        operationModel.setEmployerNo(session.getEmployerNo());
        operationModel.setUserName(session.getUserName());
        operationModel.setUserNo(session.getUserNo());
        // 获取集团租户编号，如果有
        Map<String, Object> extInfo = session.getExtInfo();
        operationModel.setExtInfo(extInfo);
        String groupTenantNo = MapUtils.getString(extInfo, "groupTenantNo");
        if (StringUtils.isNotBlank(groupTenantNo) && !groupTenantNo.equals(session.getEnterpriseNo())) {
            operationModel.setSubTenant(true);
        }
        operationModel.setGroupTenantNo(groupTenantNo);
        operationModel.setExtInfo(extInfo);
        operationModel.setTenantType(session.getTenantType());
        operationModel.setOrgNo(MapUtils.getString(extInfo, "orgNo"));
        operationModel.setOrgCode(MapUtils.getString(extInfo, "orgCode"));
        operationModel.setOrgName(MapUtils.getString(extInfo, "orgName"));

        if(TenantTypeEnum.GROUP.getValue().equals(session.getTenantType()) || TenantTypeEnum.SUB_ENTERPRISE.getValue().equals(session.getTenantType())) {
            operationModel.setTenantNo(operationModel.getGroupTenantNo());
            operationModel.setSingleOrgFlag(false);
        } else {
            operationModel.setTenantNo(session.getEnterpriseNo());
            operationModel.setSingleOrgFlag(true);
        }
        return operationModel;
    }
}
