package com.yyigou.dsrp.cdc.service.v2.supplier;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yyigou.ddc.common.page.PageDto;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.ddc.services.ddc.uim.vo.OrganizationVo;
import com.yyigou.dsrp.cdc.api.v2.supplier.vo.*;
import com.yyigou.dsrp.cdc.client.v2.supplier.request.SupplierAssignExeRequest;
import com.yyigou.dsrp.cdc.client.v2.supplier.request.SupplierAssignMdmExeRequest;
import com.yyigou.dsrp.cdc.client.v2.supplier.response.*;
import com.yyigou.dsrp.cdc.common.util.OperationModel;
import com.yyigou.dsrp.cdc.dao.v2.company.entity.CompanyLinkmanV2;
import com.yyigou.dsrp.cdc.dao.v2.company.entity.CompanyShippingAddressV2;
import com.yyigou.dsrp.cdc.dao.v2.supplier.entity.*;
import com.yyigou.dsrp.cdc.model.v2.supplier.req.*;

import java.util.List;
import java.util.Map;

public interface SupplierV2Service extends IService<SupplierV2> {
    PageVo<SupplierPageVO> manageFindListPage(OperationModel operationModel, SupplierManageQueryListPageReq queryReq, PageDto pageDTO);

    PageVo<SupplierPageVO> useFindListPage(OperationModel operationModel, SupplierUseQueryListPageReq queryReq, PageDto pageDTO);

    PageVo<SupplierPageVO> findListPageForTenant(SupplierFindQueryListPageForTenantReq queryReq, PageDto pageDTO);

    PageVo<SupplierPageVO> useFindListPageNoAuthZ(OperationModel operationModel, SupplierUseQueryListPageNoAuthZReq queryReq, PageDto pageDTO);

    PageVo<SupplierReversePageVO> reverseUseFindListPageNoAuthZ(OperationModel operationModel, SupplierReverseUseQueryListPageNoAuthZReq queryReq, PageDto pageDTO);

    Boolean manageDeleteSupplier(OperationModel operationModel, SupplierDeleteReq params);

    SupplierVO getSupplier(OperationModel operationModel, SupplierGetReq params);

    SupplierV2 getSupplierByName(OperationModel operationModel, String supplierName);

    SupplierV2 getSupplierByCode(OperationModel operationModel, String supplierCode);

    SupplierVO getSupplierByCode(String enterpriseNo, String supplierCode);

    SupplierVO getDetailSupplierByName(OperationModel operationModel, String useOrgNo, String supplierName);

    SupplierVO getDetailSupplierByCode(OperationModel operationModel, String useOrgNo, String supplierCode);

    Boolean hasAssignSupplier(OperationModel operationModel, SupplierBase supplierBase);

    Boolean changeSupplierStatus(OperationModel operationModel, SupplierChangeStatusReq params);

    List<SupplierAssignVO> getAssignSupplier(OperationModel operationModel, SupplierGetAssignReq params);

    Long getPendingCount(OperationModel operationModel);

    Boolean checkOnlyCode(OperationModel operationModel, String no, String code);

    Boolean checkOnlyName(OperationModel operationModel, String no, String name);

    Boolean checkQuoteCategory(OperationModel operationModel, String supplierCategoryNo);

    List<SupplierV2> queryByManageOrgNoList(OperationModel operationModel, String supplierCategoryNo, List<String> manageOrgNoList);

    List<SupplierV2> queryByCompanyNo(String enterpriseNo, String companyNo);

    Boolean syncSupplierNameByCompanyName(String enterpriseNo, String supplierCode, SupplierV2 supplierV2);

    List<SupplierBasicResponse> queryStandardInfo(SupplierStandardQueryReq supplierStandardQueryReq);

    List<SupplierBizResponse> queryWithBizInfo(SupplierStandardQueryReq supplierStandardQueryReq);

    List<SupplierBizResponse> selectSupplierBizByCodeOrgPair(SupplierCodeOrgPairListReq supplierCodeOrgPairListReq);

    PageVo<SupplierBizResponse> queryPageWithBizInfo(SupplierStandardQueryReq supplierStandardQueryReq, PageDto pageDTO);

    SupplierCoordinationCreateResponse createCoordinationSupplier(String enterpriseNo, String orgNo, String supplierName, String unifiedSocialCode, String omsSupplierNo);


    List<SupplierAssignExeResponse> assignSupplier(SupplierAssignExeRequest req);

    List<SupplierAssignExeResponse> assignMdmSupplier(SupplierAssignMdmExeRequest req);

    List<SupplierAssignExeResponse> deAssignSupplier(SupplierAssignExeRequest req);

    PageVo<SupplierFormalPageVO> findListPageByFormal(OperationModel operationModel, SupplierPageByFormalQueryReq queryReq, PageDto pageDTO);

    PageVo<SupplierPageVO> querySpecifyOrgPageSupplier(OperationModel operationModel, SupplierPageQueryBySpecifyOrgReq params, PageDto pageDto);


    String manageSaveSupplierBasicAndBiz(OperationModel operationModel, SupplierSaveBasicAndBizReq params);

    String preValidateManageSaveSupplierBasicAndBiz(OperationModel operationModel, SupplierSaveBasicAndBizReq params);

    Boolean editSupplierBasicAndBiz(OperationModel operationModel, SupplierEditBasicAndBizReq params);

    Boolean assumeManageEditSupplierBasicAndBiz(OperationModel operationModel, SupplierAssumeManageEditBasicAndBizReq params);

    String preValidateAssumeManageEditSupplierBasicAndBiz(OperationModel operationModel, SupplierAssumeManageEditBasicAndBizReq params);

    Boolean editSupplierBasic(OperationModel operationModel, SupplierEditBasicReq params);

    String preValidateEditSupplierBasic(OperationModel operationModel, SupplierEditBasicReq params);

    List<Long> editSupplierAddressList(OperationModel operationModel, SupplierAddressListEditReq params);

    List<CompanyShippingAddressV2> overwriteSupplierAddressList(OperationModel operationModel, SupplierAddressListEditReq params);

    void preValidateEditSupplierAddressList(OperationModel operationModel, SupplierAddressListEditReq params);

    List<Long> editSupplierLinkmanList(OperationModel operationModel, SupplierLinkmanListEditReq params);

    List<CompanyLinkmanV2> overwriteSupplierLinkmanList(OperationModel operationModel, SupplierLinkmanListEditReq params);

    void preValidateEditSupplierLinkmanList(OperationModel operationModel, SupplierLinkmanListEditReq params);

    List<Long> editSupplierOrdermanList(OperationModel operationModel, SupplierOrdermanListEditReq params);

    List<SupplierOrderManV2> overwriteSupplierOrdermanList(OperationModel operationModel, SupplierOrdermanListEditReq params);

    void preValidateEditSupplierOrdermanList(OperationModel operationModel, SupplierOrdermanListEditReq params);

    List<Long> editSupplierBankList(OperationModel operationModel, SupplierBankListEditReq params);

    void preValidateEditSupplierBankList(OperationModel operationModel, SupplierBankListEditReq params);

    List<SupplierV2> findByInternalOrgNo(OperationModel operationModel, String supplierCode, String internalOrgNo);


    PageVo<OrganizationVo> findOrgListPageBySetting(OperationModel operationModel, SupplierEligibleOrgListQueryReq params, PageDto pageDto) ;

    Map<String,Object> manageApprove(OperationModel operationModel, SupplierApply supplierApply, SupplierApplyItem supplierApplyItem, SupplierApplyFormReq supplierApplyFormReq, SupplierApplyApproveReq req);

    Map<String, String> preValidateManageApprove(OperationModel operationModel, SupplierApply supplierApply, SupplierApplyItem supplierApplyItem, SupplierApplyFormReq supplierApplyFormReq, SupplierApplyApproveReq req);


    void validateSaveBasicAndInfoReqByApply(OperationModel operationModel, String applyContent);

    void validateEditBasicReqByApply(OperationModel operationModel, String applyContent);


    Boolean changeSupplierGspStatus(SupplierGspStatusReq params);

    List<SupplierBasicResponse> querySupplierByCodeList(String enterpriseNo, List<String> supplierCodeList);


    List<SupplierBaseResponse> queryAssignInfo(String enterpriseNo, String supplierCode);


    Boolean checkConvertToCustomer(OperationModel operationModel, String supplierCode);
}
