package com.yyigou.dsrp.cdc.service.customer.impl;

import com.alibaba.fastjson.JSON;
import com.yyigou.ddc.common.util.DateUtil;
import com.yyigou.ddc.common.util.ValidatorUtils;
import com.yyigou.ddc.services.ddc.uim.vo.OrganizationTreeVo;
import com.yyigou.ddc.services.dlog.api.ULogAPI;
import com.yyigou.ddc.services.dlog.dto.IntegrationLogDTO;
import com.yyigou.ddc.services.dlog.enums.integration.IntegrationError;
import com.yyigou.ddc.services.dlog.enums.integration.IntegrationRequestType;
import com.yyigou.ddc.services.dlog.enums.integration.MdmBillType;
import com.yyigou.dsrp.cdc.api.company.dto.BankDTO;
import com.yyigou.dsrp.cdc.api.company.dto.CompanyFileDTO;
import com.yyigou.dsrp.cdc.api.company.dto.CompanyLinkmanDTO;
import com.yyigou.dsrp.cdc.api.company.dto.CompanyShippingAddressDTO;
import com.yyigou.dsrp.cdc.api.customer.dto.CustomerExternalAssignDTO;
import com.yyigou.dsrp.cdc.api.customer.dto.CustomerExternalSaveDTO;
import com.yyigou.dsrp.cdc.api.customer.dto.CustomerInvoiceDTO;
import com.yyigou.dsrp.cdc.api.customer.vo.CustomerExternalSaveVO;
import com.yyigou.dsrp.cdc.api.customer.vo.CustomerInternalSaveVO;
import com.yyigou.dsrp.cdc.api.supply.dto.SupplierSalesManDTO;
import com.yyigou.dsrp.cdc.common.enums.CommonStatusEnum;
import com.yyigou.dsrp.cdc.common.enums.DeletedEnum;
import com.yyigou.dsrp.cdc.common.enums.LinkmanTypeEnum;
import com.yyigou.dsrp.cdc.common.enums.company.HospitalClassEnum;
import com.yyigou.dsrp.cdc.common.enums.company.HospitalTypeEnum;
import com.yyigou.dsrp.cdc.common.enums.company.InstitutionalTypeEnum;
import com.yyigou.dsrp.cdc.common.util.CommonUtil;
import com.yyigou.dsrp.cdc.common.util.OperationModel;
import com.yyigou.dsrp.cdc.dao.common.BankTypeDAO;
import com.yyigou.dsrp.cdc.dao.common.entity.BankType;
import com.yyigou.dsrp.cdc.dao.company.CompanyDAO;
import com.yyigou.dsrp.cdc.dao.company.CompanyLinkmanDAO;
import com.yyigou.dsrp.cdc.dao.company.CompanyShippingAddressDAO;
import com.yyigou.dsrp.cdc.dao.company.entity.Company;
import com.yyigou.dsrp.cdc.dao.company.entity.CompanyLinkman;
import com.yyigou.dsrp.cdc.dao.company.entity.CompanyShippingAddress;
import com.yyigou.dsrp.cdc.dao.customer.CustomerDAO;
import com.yyigou.dsrp.cdc.dao.customer.CustomerInvoiceDAO;
import com.yyigou.dsrp.cdc.dao.customer.CustomerSalesManDAO;
import com.yyigou.dsrp.cdc.dao.customer.entity.Customer;
import com.yyigou.dsrp.cdc.dao.customer.entity.CustomerInvoice;
import com.yyigou.dsrp.cdc.dao.customer.entity.CustomerSalesMan;
import com.yyigou.dsrp.cdc.dao.supplier.CompanyBankDAO;
import com.yyigou.dsrp.cdc.dao.supplier.entity.CompanyBank;
import com.yyigou.dsrp.cdc.manager.integration.dccert.CatalogInfoService;
import com.yyigou.dsrp.cdc.manager.integration.exeresult.MasterDataExecuteService;
import com.yyigou.dsrp.cdc.manager.integration.numberCenter.NumberCenterService;
import com.yyigou.dsrp.cdc.manager.integration.org.UimTenantService;
import com.yyigou.dsrp.cdc.model.constant.NumberCenterConstant;
import com.yyigou.dsrp.cdc.model.constant.SystemConstant;
import com.yyigou.dsrp.cdc.service.company.CompanyService;
import com.yyigou.dsrp.cdc.service.customer.CustomerCategoryService;
import com.yyigou.dsrp.cdc.service.customer.CustomerExtendService;
import com.yyigou.dsrp.cdc.service.customer.CustomerExternService;
import com.yyigou.dsrp.cdc.service.v2.customer.CustomerV2ExternalService;
import com.yyigou.dsrp.cert.client.certbase.req.BaseFileClientReq;
import com.yyigou.dsrp.cert.client.certbase.req.CatalogInfoBigClientReq;
import com.yyigou.dsrp.cert.client.certbase.req.CatalogInfoClientReq;
import com.yyigou.dsrp.cert.client.certbase.req.CertCompanyClientReq;
import com.yyigou.dsrp.cert.client.certbase.res.CatalogInfoClientRes;
import com.yyigou.dsrp.gcs.client.executeRecord.request.SaveExecuteRecordRequest;
import com.yyigou.dsrp.gcs.client.executeRecord.response.ExecuteRecordResponse;
import com.yyigou.dsrp.gcs.common.enums.executeRecord.ExecuteRecordDataTypeEnum;
import com.yyigou.dsrp.gcs.common.enums.executeRecord.ExecuteRecordExcuteTypeEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class CustomerExternServiceImpl implements CustomerExternService {
    private final CustomerDAO customerDAO;
    private final CompanyLinkmanDAO companyLinkmanDAO;
    private final CompanyShippingAddressDAO companyShippingAddressDAO;
    private final CompanyBankDAO companyBankDAO;
    private final CustomerInvoiceDAO customerInvoiceDAO;
    private final CustomerSalesManDAO customerSalesManDAO;
    private final CompanyDAO companyDAO;
    private final NumberCenterService numberCenterService;
    private final BankTypeDAO bankTypeDAO;
    private final CustomerCategoryService customerCategoryService;
    private final CompanyService companyService;
    private final CatalogInfoService catalogInfoService;
    private final UimTenantService uimTenantService;
    private final MasterDataExecuteService masterDataExecuteService;
    private final CustomerExtendService customerExtendService;

    private final ULogAPI uLogAPI;

    @Resource
    private CustomerV2ExternalService customerV2ExternalService;

    @Override
    @Transactional
    public List<CustomerExternalSaveVO> batchSave(OperationModel operationModel, List<CustomerExternalSaveDTO> params) {
        ValidatorUtils.checkTrueThrowEx(CollectionUtils.isEmpty(params), "数据不能为空");
        ValidatorUtils.checkTrueThrowEx(params.size() > 20, "推送数据不能超过20条");
        String enterpriseNo = operationModel.getEnterpriseNo();
        final List<OrganizationTreeVo> organizationTreeList = uimTenantService.getOrganizationTreeList(enterpriseNo);
        final Map<String, OrganizationTreeVo> organizationMap = organizationTreeList.stream().collect(Collectors.toMap(OrganizationTreeVo::getOrgCode, Function.identity(), (k, v) -> k));
        List<CustomerInternalSaveVO> resultList = new ArrayList<>();
        params.removeIf(t -> {
            //set default
            if (t.getIsMedicalInstitution() == null) {
                t.setIsMedicalInstitution(0);
            }
            //默认非关联企业
            if (t.getIsAssociatedEnterprise() == null) {
                t.setIsAssociatedEnterprise(0);
            }
            if (StringUtils.isEmpty(t.getCustomerCode())) {
                resultList.add(new CustomerInternalSaveVO(t.getCustomerCode(), t.getCustomerName(),
                        false, "客户编码不能为空", operationModel, IntegrationError.PARAMS_EMPTY_ERR));
                return Boolean.TRUE;
            }
            if (StringUtils.isEmpty(t.getCustomerName())) {
                resultList.add(new CustomerInternalSaveVO(t.getCustomerCode(), t.getCustomerName(),
                        false, "客户名称不能为空", operationModel, IntegrationError.PARAMS_EMPTY_ERR));
                return Boolean.TRUE;
            }
            if (StringUtils.isEmpty(t.getUnifiedSocialCode())) {
                resultList.add(new CustomerInternalSaveVO(t.getCustomerCode(), t.getCustomerName(),
                        false, "统一社会信用代码不能为空", operationModel, IntegrationError.PARAMS_EMPTY_ERR));
                return Boolean.TRUE;
            }
            if (StringUtils.isEmpty(t.getCompanyName())) {
                resultList.add(new CustomerInternalSaveVO(t.getCustomerCode(), t.getCustomerName(),
                        false, "企业名称不能为空", operationModel, IntegrationError.PARAMS_EMPTY_ERR));
                return Boolean.TRUE;
            }
            if (StringUtils.isEmpty(t.getCustomerCategoryCode_1())) {
                resultList.add(new CustomerInternalSaveVO(t.getCustomerCode(), t.getCustomerName(),
                        false, "客户分类不能为空", operationModel, IntegrationError.PARAMS_EMPTY_ERR));
                return Boolean.TRUE;
            }
            if (t.getIsMedicalInstitution() == null || (!new Integer(1).equals(t.getIsMedicalInstitution()) && !new Integer(0).equals(t.getIsMedicalInstitution()))) {
                resultList.add(new CustomerInternalSaveVO(t.getCustomerCode(), t.getCustomerName(),
                        false, "是否医疗机构不合法", operationModel, IntegrationError.PARAMS_INVALID_ERR));
                return Boolean.TRUE;
            }
            if (new Integer(1).equals(t.getIsMedicalInstitution())) {
                if (StringUtils.isEmpty(t.getInstitutionalType())) {
                    resultList.add(new CustomerInternalSaveVO(t.getCustomerCode(), t.getCustomerName(),
                            false, "医疗机构类型不能为空", operationModel, IntegrationError.PARAMS_EMPTY_ERR));
                    return Boolean.TRUE;
                } else if (InstitutionalTypeEnum.getByType(t.getInstitutionalType()) == null) {
                    resultList.add(new CustomerInternalSaveVO(t.getCustomerCode(), t.getCustomerName(),
                            false, "医疗机构类型不合法", operationModel, IntegrationError.PARAMS_INVALID_ERR));
                    return Boolean.TRUE;
                }
                if (StringUtils.isEmpty(t.getHospitalType())) {
                    resultList.add(new CustomerInternalSaveVO(t.getCustomerCode(), t.getCustomerName(),
                            false, "医疗机构性质不能为空", operationModel, IntegrationError.PARAMS_EMPTY_ERR));
                    return Boolean.TRUE;
                } else if (HospitalTypeEnum.getByType(t.getHospitalType()) == null) {
                    resultList.add(new CustomerInternalSaveVO(t.getCustomerCode(), t.getCustomerName(),
                            false, "医疗机构性质不合法", operationModel, IntegrationError.PARAMS_INVALID_ERR));
                    return Boolean.TRUE;
                }
                if (t.getHospitalClass() == null) {
                    resultList.add(new CustomerInternalSaveVO(t.getCustomerCode(), t.getCustomerName(),
                            false, "医疗机构等级不能为空", operationModel, IntegrationError.PARAMS_EMPTY_ERR));
                    return Boolean.TRUE;
                } else if (HospitalClassEnum.getByType(t.getHospitalClass()) == null) {
                    resultList.add(new CustomerInternalSaveVO(t.getCustomerCode(), t.getCustomerName(),
                            false, "医疗机构等级不合法", operationModel, IntegrationError.PARAMS_INVALID_ERR));
                    return Boolean.TRUE;
                }
            }
            if (!CollectionUtils.isEmpty(t.getInvoiceList())) {
                if (t.getInvoiceList().stream().anyMatch(invoice -> StringUtils.isEmpty(invoice.getInvoiceCode()))) {
                    resultList.add(new CustomerInternalSaveVO(t.getCustomerCode(), t.getCustomerName(),
                            false, "发票编码不能为空", operationModel, IntegrationError.PARAMS_EMPTY_ERR));
                    return Boolean.TRUE;
                } else {
                    final Map<String, List<CustomerInvoiceDTO>> invoiceGroup = t.getInvoiceList().stream().collect(Collectors.groupingBy(CustomerInvoiceDTO::getInvoiceCode));
                    for (String invoiceCode : invoiceGroup.keySet()) {
                        if (invoiceGroup.get(invoiceCode).size() > 1) {
                            resultList.add(new CustomerInternalSaveVO(t.getCustomerCode(), t.getCustomerName(),
                                    false, "发票编码" + invoiceCode + "不能重复", operationModel, IntegrationError.PARAMS_INVALID_ERR));
                            return Boolean.TRUE;
                        }
                    }
                }
            }
            if (!CollectionUtils.isEmpty(t.getLinkmanList())) {
                if (t.getLinkmanList().stream().anyMatch(linkCode -> StringUtils.isEmpty(linkCode.getLinkCode()))) {
                    resultList.add(new CustomerInternalSaveVO(t.getCustomerCode(), t.getCustomerName(),
                            false, "联系人编码不能为空", operationModel, IntegrationError.PARAMS_EMPTY_ERR));
                    return Boolean.TRUE;
                } else {
                    final Map<String, List<CompanyLinkmanDTO>> invoiceGroup = t.getLinkmanList().stream().collect(Collectors.groupingBy(CompanyLinkmanDTO::getLinkCode));
                    for (String linkCode : invoiceGroup.keySet()) {
                        if (invoiceGroup.get(linkCode).size() > 1) {
                            resultList.add(new CustomerInternalSaveVO(t.getCustomerCode(), t.getCustomerName(),
                                    false, "发票编码" + linkCode + "不能重复", operationModel, IntegrationError.PARAMS_INVALID_ERR));
                            return Boolean.TRUE;
                        }
                    }
                }
            }
            if (new Integer(1).equals(t.getIsAssociatedEnterprise())) {
                if (StringUtils.isEmpty(t.getAssociatedOrgCode())) {
                    resultList.add(new CustomerInternalSaveVO(t.getCustomerCode(), t.getCustomerName(),
                            false, "关联企业编码不能为空", operationModel, IntegrationError.PARAMS_EMPTY_ERR));
                    return Boolean.TRUE;
                }
//                if (!organizationMap.containsKey(t.getAssociatedOrgCode())) {
//                    resultList.add(new CustomerExternalSaveVO(t.getCustomerCode(), false, "关联组织" + t.getAssociatedOrgCode() + "在DSRP中不存在"));
//                    return Boolean.TRUE;
//                }

            }

            return Boolean.FALSE;
        });
        if (CollectionUtils.isEmpty(params)) {
            return saveBusinessLogAndRetResult(resultList);
        }
        final List<String> customerCodeList = params.stream().map(CustomerExternalSaveDTO::getCustomerCode).filter(customerCode -> !StringUtils.isEmpty(customerCode)).collect(Collectors.toList());
        List<Customer> localCustomerList = customerDAO.getCustomerByCodeList(enterpriseNo, customerCodeList);
        final Map<String, Customer> localCustomerMap = localCustomerList.stream().collect(Collectors.toMap(Customer::getCustomerCode, Function.identity()));
        //查询已经存在的客户信息
        Map<String, List<CompanyBank>> supplierBankGroup;
        Map<String, List<CompanyLinkman>> companyLinkmanGroup;
        Map<String, List<CompanyShippingAddress>> companyShippingAddressMap;
        Map<String, List<CustomerSalesMan>> supplierOrderManMap;
        Map<String, List<CustomerInvoice>> customerInvoiceMap;
        if (!CollectionUtils.isEmpty(localCustomerList)) {
            final List<String> localCustomerNoList = localCustomerList.stream().map(Customer::getCustomerNo).collect(Collectors.toList());
            //查询银行信息
            final List<CompanyBank> companyBankList = companyBankDAO.getCompanyBankListBySourceNoList(enterpriseNo, LinkmanTypeEnum.SALE.getValue(), localCustomerNoList);
            supplierBankGroup = companyBankList.stream().collect(Collectors.groupingBy(CompanyBank::getSourceNo));
            //联系人
            final List<CompanyLinkman> companyLinkmanList = companyLinkmanDAO.getCompanyLinkmanListBySourceNoList(enterpriseNo, LinkmanTypeEnum.SALE.getValue(), localCustomerNoList);
            companyLinkmanGroup = companyLinkmanList.stream().collect(Collectors.groupingBy(CompanyLinkman::getSourceNo));
            //查询联系地址
            final List<CompanyShippingAddress> companyShippingAddressList = companyShippingAddressDAO.getCompanyShippingAddressBySourceNoList(enterpriseNo, LinkmanTypeEnum.SALE.getValue(), localCustomerNoList);
            companyShippingAddressMap = companyShippingAddressList.stream().collect(Collectors.groupingBy(CompanyShippingAddress::getSourceNo));
            //负责人
            final List<CustomerSalesMan> supplierOrderManList = customerSalesManDAO.getCustomerSalesManByCustomerNoList(enterpriseNo, localCustomerNoList);
            supplierOrderManMap = supplierOrderManList.stream().collect(Collectors.groupingBy(CustomerSalesMan::getCustomerNo));

            final List<CustomerInvoice> customerInvoiceList = customerInvoiceDAO.getCustomerInvoiceByCustomerNoList(enterpriseNo, localCustomerNoList);
            customerInvoiceMap = customerInvoiceList.stream().collect(Collectors.groupingBy(CustomerInvoice::getCustomerNo));
        } else {
            companyLinkmanGroup = new HashMap<>();
            companyShippingAddressMap = new HashMap<>();
            supplierOrderManMap = new HashMap<>();
            supplierBankGroup = new HashMap<>();
            customerInvoiceMap = new HashMap<>();
        }
        final List<CustomerExternalSaveDTO> updateSupplyList = params.stream().filter(t -> localCustomerMap.containsKey(t.getCustomerCode())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(updateSupplyList)) {
            final List<CustomerExternalSaveDTO> deleteBankList = updateSupplyList.stream().filter(t -> CollectionUtils.isEmpty(t.getBankList())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(deleteBankList)) {
                companyBankDAO.deleteCompanyBankListBySourceNoList(enterpriseNo, LinkmanTypeEnum.SALE.getValue(), deleteBankList.stream().map(t -> localCustomerMap.get(t.getCustomerCode()).getCustomerNo()).collect(Collectors.toList()));
            }
            final List<CustomerExternalSaveDTO> deleteResponsibleManList = updateSupplyList.stream().filter(t -> CollectionUtils.isEmpty(t.getResponsibleManList())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(deleteResponsibleManList)) {
                customerSalesManDAO.deleteCustomerSalesManByCustomerNoList(enterpriseNo, deleteResponsibleManList.stream().map(t -> localCustomerMap.get(t.getCustomerCode()).getCustomerNo()).collect(Collectors.toList()));
            }
            final List<CustomerExternalSaveDTO> deleteLinkmanList = updateSupplyList.stream().filter(t -> CollectionUtils.isEmpty(t.getLinkmanList())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(deleteLinkmanList)) {
                companyLinkmanDAO.deleteCompanyLinkmanListByCompanyNo(enterpriseNo, LinkmanTypeEnum.SALE.getValue(), deleteLinkmanList.stream().map(t -> localCustomerMap.get(t.getCustomerCode()).getCustomerNo()).collect(Collectors.toList()));
            }
            final List<CustomerExternalSaveDTO> deleteLinkAddressList = updateSupplyList.stream().filter(t -> CollectionUtils.isEmpty(t.getLinkAddressList())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(deleteLinkAddressList)) {
                companyShippingAddressDAO.deleteShippingAddressListBySourceNoList(enterpriseNo, LinkmanTypeEnum.SALE.getValue(), deleteLinkAddressList.stream().map(t -> localCustomerMap.get(t.getCustomerCode()).getCustomerNo()).collect(Collectors.toList()));
            }
            final List<CustomerExternalSaveDTO> deleteInvoiceList = updateSupplyList.stream().filter(t -> CollectionUtils.isEmpty(t.getLinkAddressList())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(deleteInvoiceList)) {
                customerInvoiceDAO.deleteCustomerInvoiceByCustomerNoList(enterpriseNo, deleteLinkAddressList.stream().map(t -> localCustomerMap.get(t.getCustomerCode()).getCustomerNo()).collect(Collectors.toList()));
            }
        }
        //create and update supplierCategory 创建客户分类
        final List<CustomerExternalSaveDTO> supplierCategoryList = params.stream().filter(t -> StringUtils.isNotBlank(t.getCustomerCategoryCode_1())).collect(Collectors.toList());
        customerCategoryService.createAndUpdateCategory(operationModel, supplierCategoryList);
        //create and update company
        final List<String> unifiedSocialCodeList = params.stream().map(CustomerExternalSaveDTO::getUnifiedSocialCode).filter(t -> !SystemConstant.DEFAULT_UNIFIED_SOCIAL_CODE.equalsIgnoreCase(t)).collect(Collectors.toList());
        final List<String> companyNameList = params.stream().map(CustomerExternalSaveDTO::getCompanyName).collect(Collectors.toList());

        Map<String, Company> localUnifiedSocialCompanyMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(unifiedSocialCodeList)) {
            List<Company> localUnifiedSocialCompanyList = companyDAO.findByEnterpriseNoAndUnifiedSocialCodeList(enterpriseNo, unifiedSocialCodeList);
            localUnifiedSocialCompanyMap = localUnifiedSocialCompanyList.stream().collect(Collectors.toMap(Company::getUnifiedSocialCode, Function.identity(), (v1, v2) -> v1));
        }
        List<Company> locaCompanyNameList = companyDAO.findByEnterpriseNoAndCompanyName(enterpriseNo, companyNameList);
        Map<String, Company> locaCompanyNameMap = locaCompanyNameList.stream().collect(Collectors.toMap(Company::getCompanyName, Function.identity(), (v1, v2) -> v1));

        final List<BankType> bankList = bankTypeDAO.getList();
        final Map<String, BankType> bankMap = bankList.stream().collect(Collectors.toMap(BankType::getBankName, Function.identity(), (v1, v2) -> v1));
        //企业不存在创建企业
        List<Company> companyList = new ArrayList<>();
        for (CustomerExternalSaveDTO t : params) {
            if (locaCompanyNameMap.containsKey(t.getCompanyName())) {
                //说明企业名称能匹配上
                Company company = locaCompanyNameMap.get(t.getCompanyName());
                company.setUnifiedSocialCode(t.getUnifiedSocialCode());
                company.setInstitutionalType(t.getInstitutionalType());
                company.setHospitalType(t.getHospitalType());
                company.setHospitalClass(t.getHospitalClass());
                company.setLegalPerson(t.getLegalPerson());
                company.setRegionCode(t.getRegionCode());
                company.setIsAssociatedEnterprise(t.getIsAssociatedEnterprise());
                company.setAssociatedOrgCode(t.getAssociatedOrgCode());
                if (new Integer(1).equals(t.getIsAssociatedEnterprise())) {
                    final OrganizationTreeVo organizationTreeVo = organizationMap.get(t.getAssociatedOrgCode());
                    if (organizationTreeVo != null) {
                        company.setAssociatedOrgNo(organizationTreeVo.getOrgNo());
                        company.setAssociatedOrgName(organizationTreeVo.getOrgName());
                    }
                }
                companyService.updateCompany(operationModel, company);
            } else if (localUnifiedSocialCompanyMap.containsKey(t.getUnifiedSocialCode())) {
                //说明有企业信用代码能匹配上
                Company company = localUnifiedSocialCompanyMap.get(t.getUnifiedSocialCode());
                company.setCompanyName(t.getCompanyName());
                company.setInstitutionalType(t.getInstitutionalType());
                company.setHospitalType(t.getHospitalType());
                company.setHospitalClass(t.getHospitalClass());
                company.setLegalPerson(t.getLegalPerson());
                company.setRegionCode(t.getRegionCode());
                company.setIsAssociatedEnterprise(t.getIsAssociatedEnterprise());
                company.setAssociatedOrgCode(t.getAssociatedOrgCode());
                if (new Integer(1).equals(t.getIsAssociatedEnterprise())) {
                    final OrganizationTreeVo organizationTreeVo = organizationMap.get(t.getAssociatedOrgCode());
                    if (organizationTreeVo != null) {
                        company.setAssociatedOrgNo(organizationTreeVo.getOrgNo());
                        company.setAssociatedOrgName(organizationTreeVo.getOrgName());
                    }
                }
                companyService.updateCompany(operationModel, company);
            } else if (!localUnifiedSocialCompanyMap.containsKey(t.getUnifiedSocialCode()) && !locaCompanyNameMap.containsKey(t.getCompanyName())) {
                //没有社会信用代码也没有一样企业名称的企业 应该新增企业
                Company company = new Company();
                company.setEnterpriseNo(enterpriseNo);
                company.setCompanyCode(SystemConstant.DEFAULT_UNIFIED_SOCIAL_CODE.equalsIgnoreCase(t.getUnifiedSocialCode()) ? t.getCustomerCode() : t.getUnifiedSocialCode());
                company.setCompanyName(t.getCompanyName());
                company.setUnifiedSocialCode(t.getUnifiedSocialCode());
                company.setFactoryType(t.getFactoryType());
                company.setInstitutionalType(t.getInstitutionalType());
                company.setHospitalType(t.getHospitalType());
                company.setHospitalClass(t.getHospitalClass());
                company.setLegalPerson(t.getLegalPerson());
                company.setRegionCode(t.getRegionCode());
                company.setIsAssociatedEnterprise(t.getIsAssociatedEnterprise());
                company.setAssociatedOrgCode(t.getAssociatedOrgCode());
                if (new Integer(1).equals(t.getIsAssociatedEnterprise())) {
                    final OrganizationTreeVo organizationTreeVo = organizationMap.get(t.getAssociatedOrgCode());
                    if (organizationTreeVo != null) {
                        company.setAssociatedOrgNo(organizationTreeVo.getOrgNo());
                        company.setAssociatedOrgName(organizationTreeVo.getOrgName());
                    }
                }
                CommonUtil.fillCreatInfo(operationModel, company);
                CommonUtil.fillOperateInfo(operationModel, company);
                company.setStatus(CommonStatusEnum.EFFECTIVE.getValue());
                company.setDeleted(DeletedEnum.UN_DELETE.getValue());
                companyList.add(company);
            }
        }
        //创建企业
        if (!CollectionUtils.isEmpty(companyList)) {
            final List<String> companyNoList = numberCenterService.batchGenerateNoList(NumberCenterConstant.COMPANY_NO_GENERATE_KEY, companyList.size());
            AtomicInteger companyNoindex = new AtomicInteger(-1);
            companyList.forEach(t -> t.setCompanyNo(companyNoList.get(companyNoindex.incrementAndGet())));
            companyDAO.addBatch(companyList);
            locaCompanyNameList = companyDAO.findByEnterpriseNoAndCompanyName(enterpriseNo, companyNameList);
            locaCompanyNameMap = locaCompanyNameList.stream().collect(Collectors.toMap(Company::getCompanyName, Function.identity(), (v1, v2) -> v1));
        }
        //本次新增的客户
        final long count = params.stream().filter(t -> !localCustomerMap.containsKey(t.getCustomerCode())).count();
        List<String> supplyNoList;
        AtomicInteger supplyNoIndex = new AtomicInteger(-1);
        if (count > 0) {
            supplyNoList = numberCenterService.batchGenerateNoList(NumberCenterConstant.CUSTOMER_NO_KEY, (int) count);
        } else {
            supplyNoList = new ArrayList<>();
        }
        List<Customer> saveCustomerList = new ArrayList<>();
        List<CompanyBank> saveCompanyBankList = new ArrayList<>();
        List<CustomerSalesMan> saveResponsibleManListList = new ArrayList<>();
        List<CompanyShippingAddress> saveShippingAddressListList = new ArrayList<>();
        List<CompanyLinkman> saveLinkmanListList = new ArrayList<>();
        List<CustomerInvoice> customerInvoiceList = new ArrayList<>();
        Map<String, Company> finalLocaCompanyMap = locaCompanyNameMap;
        List<SaveExecuteRecordRequest> executeRecordList = new ArrayList<>();
        params.forEach(t -> {
            Customer customer = new Customer();
            //修改
            if (localCustomerMap.containsKey(t.getCustomerCode())) {
                customer = localCustomerMap.get(t.getCustomerCode());
                customer.setCustomerName(t.getCustomerName());
                customer.setMnemonicCode(t.getMnemonicCode());
                customer.setCustomerNameEn(t.getCustomerNameEn());
                customer.setOwnerCompany(t.getOwnerCompany());
                customer.setHospitalClass(t.getHospitalClass());
                customer.setRemark(t.getRemark());
                //信用天数额度
                customer.setCreditAmount(t.getCreditAmount());
//                customer.setPeriodDays(t.getPeriodDays());
                //合作起止日期
                customer.setCoopStartTime(t.getCoopStartTime());
                customer.setCoopEndTime(t.getCoopEndTime());
                customer.setCustomerCategoryNo(t.getCustomerCategoryNo());
                customerDAO.updateById(customer);

                resultList.add(new CustomerInternalSaveVO(t.getCustomerCode(), t.getCustomerName(),
                        true, "客户档案更新成功", operationModel));
            } else {
                //新增
                customer.setEnterpriseNo(enterpriseNo);
                customer.setCustomerNo(supplyNoList.get(supplyNoIndex.incrementAndGet()));
                customer.setCompanyNo(finalLocaCompanyMap.get(t.getCompanyName()).getCompanyNo());
                customer.setUnifiedSocialCode(t.getUnifiedSocialCode());
                customer.setCustomerCode(t.getCustomerCode());
                customer.setCustomerName(t.getCustomerName());
                customer.setMnemonicCode(t.getMnemonicCode());
                customer.setCustomerNameEn(t.getCustomerNameEn());
                customer.setOwnerCompany(t.getOwnerCompany());
                customer.setHospitalClass(t.getHospitalClass());
                customer.setRemark(t.getRemark());
                //信用天数额度
                customer.setCreditAmount(t.getCreditAmount());
//                customer.setPeriodDays(t.getPeriodDays());
                //合作起止日期
                customer.setCoopStartTime(t.getCoopStartTime());
                customer.setCoopEndTime(t.getCoopEndTime());
                customer.setBusinessFlag(1);
                customer.setCustomerCategoryNo(t.getCustomerCategoryNo());
                saveCustomerList.add(customer);

                resultList.add(new CustomerInternalSaveVO(t.getCustomerCode(), t.getCustomerName(),
                        true, "客户档案新增成功", operationModel));
            }
            //银行
            if (!CollectionUtils.isEmpty(t.getBankList())) {
                List<CompanyBank> localBankList = supplierBankGroup.get(customer.getCustomerNo());
                Map<String, CompanyBank> localBankMap;
                if (!CollectionUtils.isEmpty(localBankList)) {
                    localBankMap = localBankList.stream().collect(Collectors.toMap(CompanyBank::getBankCode, Function.identity()));
                } else {
                    localBankMap = new HashMap<>();
                }
                for (BankDTO b : t.getBankList()) {
                    final BankType bankType = bankMap.getOrDefault(b.getBankType(), new BankType());
                    if (localBankMap.containsKey(b.getBankCode())) {
                        CompanyBank companyBank = localBankMap.get(b.getBankCode());
                        companyBank.setBankType(bankType.getBankCode());
                        companyBank.setBankTypeName(bankType.getBankName());
                        companyBank.setOpenBank(b.getOpenBank());
                        companyBank.setAccountName(b.getAccountName());
                        companyBank.setAccountNo(b.getAccountNo());
                        companyBank.setAccountType(b.getAccountType());
                        companyBank.setLinkPerson(b.getLinkPerson());
                        companyBank.setLinkPhone(b.getLinkPhone());
                        companyBankDAO.updateById(companyBank);
                    } else {
                        CompanyBank companyBank = new CompanyBank();
                        companyBank.setEnterpriseNo(enterpriseNo);
                        companyBank.setLinkaddType(LinkmanTypeEnum.SALE.getValue());
                        companyBank.setCompanyNo(customer.getCompanyNo());
                        companyBank.setSourceNo(customer.getCustomerNo());
                        companyBank.setBankCode(b.getBankCode());
                        companyBank.setBankType(bankType.getBankCode());
                        companyBank.setBankTypeName(bankType.getBankName());
                        companyBank.setOpenBank(b.getOpenBank());
                        companyBank.setAccountName(b.getAccountName());
                        companyBank.setAccountNo(b.getAccountNo());
                        companyBank.setAccountType(b.getAccountType());
                        companyBank.setLinkPerson(b.getLinkPerson());
                        companyBank.setLinkPhone(b.getLinkPhone());
                        companyBank.setStatus(b.getStatus());
                        companyBank.setIsDefault(b.getIsDefault());
                        saveCompanyBankList.add(companyBank);
                    }
                }
            }
            //负责人
            if (!CollectionUtils.isEmpty(t.getResponsibleManList())) {
                final List<CustomerSalesMan> orderManList = supplierOrderManMap.get(customer.getCustomerNo());
                Map<String, CustomerSalesMan> localOrderManMap;
                if (!CollectionUtils.isEmpty(orderManList)) {
                    localOrderManMap = orderManList.stream().collect(Collectors.toMap(CustomerSalesMan::getManCode, Function.identity()));
                } else {
                    localOrderManMap = new HashMap<>();
                }
                for (SupplierSalesManDTO r : t.getResponsibleManList()) {
                    if (localOrderManMap.containsKey(r.getManCode())) {
                        CustomerSalesMan customerSalesMan = localOrderManMap.get(r.getManCode());
                        customerSalesMan.setCustomerNo(customer.getCustomerNo());
                        customerSalesMan.setManCode(r.getManCode());
                        customerSalesMan.setOrgNo(r.getDeptNo());
                        customerSalesMan.setPost(r.getPost());
                        customerSalesMan.setIsDefault(r.getIsDefault());
                        customerSalesMan.setOrderSpecialist(r.getOrderSpecialist());
                        customerSalesMan.setPost(r.getPost());
                        customerSalesManDAO.updateById(customerSalesMan);
                    } else {
                        CustomerSalesMan customerSalesMan = new CustomerSalesMan();
                        customerSalesMan.setCustomerNo(customer.getCustomerNo());
                        customerSalesMan.setManCode(r.getManCode());
                        customerSalesMan.setOrgNo(r.getDeptNo());
                        customerSalesMan.setPost(r.getPost());
                        customerSalesMan.setIsDefault(r.getIsDefault());
                        customerSalesMan.setOrderSpecialist(r.getOrderSpecialist());
                        customerSalesMan.setPost(r.getPost());
                        saveResponsibleManListList.add(customerSalesMan);
                    }
                }
            }
            //联系人
            if (!CollectionUtils.isEmpty(t.getLinkmanList())) {
                final List<CompanyLinkman> companyLinkmanList = companyLinkmanGroup.get(customer.getCustomerNo());
                Map<String, CompanyLinkman> localCompanyLinkmanMap;
                if (!CollectionUtils.isEmpty(companyLinkmanList)) {
                    localCompanyLinkmanMap = companyLinkmanList.stream().collect(Collectors.toMap(CompanyLinkman::getLinkCode, Function.identity()));
                } else {
                    localCompanyLinkmanMap = new HashMap<>();
                }
                for (CompanyLinkmanDTO r : t.getLinkmanList()) {
                    if (localCompanyLinkmanMap.containsKey(r.getLinkCode())) {
                        CompanyLinkman companyLinkman = localCompanyLinkmanMap.get(r.getLinkCode());
                        companyLinkman.setLinkman(r.getLinkman());
                        companyLinkman.setPosition(r.getPosition());
                        companyLinkman.setMobilePhone(r.getMobilePhone());
                        companyLinkman.setSex(r.getSex());
                        companyLinkman.setFixedPhone(r.getFixedPhone());
                        companyLinkman.setQq(r.getQq());
                        companyLinkman.setWx(r.getWx());
                        companyLinkman.setLinkmanType(LinkmanTypeEnum.SALE.getValue());
                        companyLinkman.setStatus(CommonStatusEnum.EFFECTIVE.getValue());
                        companyLinkman.setSourceNo(customer.getCustomerNo());
                        companyLinkman.setEmail(r.getEmail());
                        companyLinkman.setIsDefault(r.getIsDefault());
                        companyLinkmanDAO.updateById(companyLinkman);
                    } else {
                        CompanyLinkman companyLinkman = new CompanyLinkman();
                        companyLinkman.setEnterpriseNo(enterpriseNo);
                        companyLinkman.setCompanyNo(customer.getCompanyNo());
                        companyLinkman.setLinkCode(r.getLinkCode());
                        companyLinkman.setLinkman(r.getLinkman());
                        companyLinkman.setPosition(r.getPosition());
                        companyLinkman.setMobilePhone(r.getMobilePhone());
                        companyLinkman.setSex(r.getSex());
                        companyLinkman.setFixedPhone(r.getFixedPhone());
                        companyLinkman.setQq(r.getQq());
                        companyLinkman.setWx(r.getWx());
                        companyLinkman.setLinkmanType(LinkmanTypeEnum.SALE.getValue());
                        companyLinkman.setStatus(CommonStatusEnum.EFFECTIVE.getValue());
                        companyLinkman.setSourceNo(customer.getCustomerNo());
                        companyLinkman.setEmail(r.getEmail());
                        companyLinkman.setIsDefault(r.getIsDefault());
                        saveLinkmanListList.add(companyLinkman);
                    }
                }
            }
            //联系地址
            if (!CollectionUtils.isEmpty(t.getLinkAddressList())) {
                final List<CompanyShippingAddress> shippingAddressList = companyShippingAddressMap.get(customer.getCustomerNo());
                Map<String, CompanyShippingAddress> localShippingAddressMap;
                if (!CollectionUtils.isEmpty(shippingAddressList)) {
                    localShippingAddressMap = shippingAddressList.stream().collect(Collectors.toMap(CompanyShippingAddress::getLinkAddressCode, Function.identity()));
                } else {
                    localShippingAddressMap = new HashMap<>();
                }
                for (CompanyShippingAddressDTO r : t.getLinkAddressList()) {
                    if (localShippingAddressMap.containsKey(r.getLinkAddressCode())) {
                        CompanyShippingAddress shippingAddress = localShippingAddressMap.get(r.getLinkAddressCode());
                        shippingAddress.setReceiveUser(r.getReceiveUser());
                        shippingAddress.setReceivePhone(r.getReceivePhone());
                        shippingAddress.setRegionCode(r.getRegionCode());
                        shippingAddress.setReceiveAddr(r.getReceiveAddr());
                        shippingAddress.setIsDefault(r.getIsDefault());
                        shippingAddress.setAddressType(r.getAddressType());
                        companyShippingAddressDAO.updateById(shippingAddress);
                    } else {
                        CompanyShippingAddress shippingAddress = new CompanyShippingAddress();
                        shippingAddress.setEnterpriseNo(enterpriseNo);
                        shippingAddress.setCompanyNo(customer.getCompanyNo());
                        shippingAddress.setLinkAddressCode(r.getLinkAddressCode());
                        shippingAddress.setSourceNo(customer.getCustomerNo());
                        shippingAddress.setStatus(CommonStatusEnum.EFFECTIVE.getValue());
                        shippingAddress.setLinkaddType(LinkmanTypeEnum.SALE.getValue());

                        shippingAddress.setReceiveUser(r.getReceiveUser());
                        shippingAddress.setReceivePhone(r.getReceivePhone());
                        shippingAddress.setRegionCode(r.getRegionCode());
                        shippingAddress.setReceiveAddr(r.getReceiveAddr());
                        shippingAddress.setIsDefault(r.getIsDefault());
                        shippingAddress.setAddressType(r.getAddressType());
                        saveShippingAddressListList.add(shippingAddress);
                    }
                }
            }
            //发票信息
            if (!CollectionUtils.isEmpty(t.getInvoiceList())) {
                final List<CustomerInvoice> itemCustomerInvoiceList = customerInvoiceMap.get(customer.getCustomerNo());
                Map<String, CustomerInvoice> localInvoiceMap;
                if (!CollectionUtils.isEmpty(itemCustomerInvoiceList)) {
                    localInvoiceMap = itemCustomerInvoiceList.stream().collect(Collectors.toMap(CustomerInvoice::getInvoiceCode, Function.identity()));
                } else {
                    localInvoiceMap = new HashMap<>();
                }
                for (CustomerInvoiceDTO r : t.getInvoiceList()) {
                    if (localInvoiceMap.containsKey(r.getInvoiceCode())) {
                        CustomerInvoice customerInvoice = localInvoiceMap.get(r.getInvoiceCode());
                        customerInvoice.setType(r.getType());
                        customerInvoice.setTaxNo(r.getTaxNo());
                        customerInvoice.setInvoiceTitle(r.getInvoiceTitle());
                        customerInvoice.setPhone(r.getPhone());
                        customerInvoice.setInvoicePhone(r.getInvoicePhone());
                        customerInvoice.setEmail(r.getEmail());
                        customerInvoice.setAddress(r.getAddress());
                        customerInvoice.setBankDeposit(r.getBankDeposit());
                        customerInvoice.setBankAccount(r.getBankAccount());
                        customerInvoice.setStatus(CommonStatusEnum.EFFECTIVE.getValue().toString());
                        customerInvoice.setIsDefault(r.getIsDefault());
                        customerInvoice.setRequirement(r.getRequirement());
                        customerInvoice.setOutSystemId(r.getInvoiceCode());
                        customerInvoiceDAO.updateById(customerInvoice);
                    } else {
                        CustomerInvoice customerInvoice = new CustomerInvoice();
                        customerInvoice.setInvoiceCode(r.getInvoiceCode());
                        customerInvoice.setEnterpriseNo(enterpriseNo);
                        customerInvoice.setCustomerNo(customer.getCustomerNo());
                        customerInvoice.setType(r.getType());
                        customerInvoice.setTaxNo(r.getTaxNo());
                        customerInvoice.setInvoiceTitle(r.getInvoiceTitle());
                        customerInvoice.setPhone(r.getPhone());
                        customerInvoice.setInvoicePhone(r.getInvoicePhone());
                        customerInvoice.setEmail(r.getEmail());
                        customerInvoice.setAddress(r.getAddress());
                        customerInvoice.setBankDeposit(r.getBankDeposit());
                        customerInvoice.setBankAccount(r.getBankAccount());
                        customerInvoice.setStatus(CommonStatusEnum.EFFECTIVE.getValue().toString());
                        customerInvoice.setIsDefault(r.getIsDefault());
                        customerInvoice.setRequirement(r.getRequirement());
                        customerInvoice.setOutSystemId(r.getInvoiceCode());
                        customerInvoiceList.add(customerInvoice);
                    }
                }
            }
            CatalogInfoBigClientReq catalogInfoBigClientReq = new CatalogInfoBigClientReq();
            catalogInfoBigClientReq.setEnterpriseNo(enterpriseNo);
            catalogInfoBigClientReq.setUserNo(operationModel.getUserNo());
            catalogInfoBigClientReq.setEmployeeNo(operationModel.getEmployerNo());
            catalogInfoBigClientReq.setUserName(operationModel.getUserName());
            List<CatalogInfoClientRes> catalogInfoList = catalogInfoService.findCompanyCatalogInfoList(catalogInfoBigClientReq, Collections.singletonList(customer.getCompanyNo()));
            final Map<String, CatalogInfoClientRes> catalogInfoMap = catalogInfoList.stream().collect(Collectors.toMap(CatalogInfoClientRes::getCertType, Function.identity()));
            if (CollectionUtils.isEmpty(t.getCertList()) && !CollectionUtils.isEmpty(catalogInfoList)) {
                //本次没有文件但是存在历史文件删除逻辑
                catalogInfoBigClientReq.setDeleteIdList(catalogInfoList.stream().map(CatalogInfoClientRes::getId).collect(Collectors.toList()));
                catalogInfoService.saveOrUpdateCertBatch(catalogInfoBigClientReq);
            } else if (!CollectionUtils.isEmpty(t.getCertList())) {
                final List<String> itemCertTypeList = t.getCertList().stream().map(CompanyFileDTO::getCertTypeCode).collect(Collectors.toList());
                //如果文件存在 并且本次没有走删除逻辑
                catalogInfoBigClientReq.setDeleteIdList(catalogInfoList.stream().filter(i -> !itemCertTypeList.contains(i.getCertType())).map(CatalogInfoClientRes::getId).collect(Collectors.toList()));
                List<CatalogInfoClientReq> catalogInfoClientList = new ArrayList<>();
                catalogInfoBigClientReq.setCatalogInfoClientList(catalogInfoClientList);
                for (CompanyFileDTO companyFileDTO : t.getCertList()) {
                    CatalogInfoClientReq catalogInfoClientReq = new CatalogInfoClientReq();
                    catalogInfoClientReq.setUserNo(operationModel.getUserNo());
                    catalogInfoClientReq.setEmployeeNo(operationModel.getEmployerNo());
                    catalogInfoClientReq.setUserName(operationModel.getUserName());
                    catalogInfoClientReq.setCertDataTypeCode("company");
                    catalogInfoClientReq.setObjCode("company");
                    catalogInfoClientReq.setEnterpriseNo(enterpriseNo);
                    catalogInfoClientReq.setId(catalogInfoMap.containsKey(companyFileDTO.getCertTypeCode()) ? catalogInfoMap.get(companyFileDTO.getCertTypeCode()).getId() : null);
                    catalogInfoClientReq.setCertType(companyFileDTO.getCertTypeCode());
                    catalogInfoClientReq.setCertCode(companyFileDTO.getCertCode());
                    catalogInfoClientReq.setCertName(companyFileDTO.getCertName());

                    if (StringUtils.isEmpty(catalogInfoClientReq.getCertName())){
                        log.warn("企业证照文件名称为空，不存入电子资料库 {} {}", enterpriseNo, JSON.toJSONString(companyFileDTO));
                        continue;
                    }

                    catalogInfoClientReq.setLongTerm(companyFileDTO.getLongTerm());
                    catalogInfoClientReq.setStartTime(companyFileDTO.getStartTime());
                    catalogInfoClientReq.setEndTime(companyFileDTO.getEndTime());
                    catalogInfoClientReq.setApprovalDate(companyFileDTO.getApprovalDate());
                    catalogInfoClientReq.setRemark(companyFileDTO.getRemark());
                    CertCompanyClientReq certCompanyClientReq = new CertCompanyClientReq();
                    certCompanyClientReq.setCompanyNo(customer.getCompanyNo());
                    certCompanyClientReq.setObjCode("company");
                    catalogInfoClientReq.setCertCompanyClientReq(certCompanyClientReq);
                    catalogInfoClientList.add(catalogInfoClientReq);
                    if (!CollectionUtils.isEmpty(companyFileDTO.getBaseFileList())) {
                        List<BaseFileClientReq> baseFileList = new ArrayList<>();
                        companyFileDTO.getBaseFileList().forEach(file -> {
                            BaseFileClientReq clientReq = new BaseFileClientReq();
                            clientReq.setFileUrl(file);
                            clientReq.setFileName(companyFileDTO.getCertName());
                            clientReq.setOuterFile(1);
                            baseFileList.add(clientReq);
                        });
                        catalogInfoClientReq.setBaseFileList(baseFileList);
                    }
                }
                if (!CollectionUtils.isEmpty(catalogInfoBigClientReq.getCatalogInfoClientList())) {
                    catalogInfoService.saveOrUpdateCertBatch(catalogInfoBigClientReq);
                }
            }

            if (!CollectionUtils.isEmpty(t.getAssignOrgList())) {
                Customer finalCustomer = customer;
                t.getAssignOrgList().forEach(assignItem -> {
                    if (organizationMap.containsKey(assignItem.getOrgCode())) {
                        final OrganizationTreeVo organizationTreeVo = organizationMap.get(assignItem.getOrgCode());
                        SaveExecuteRecordRequest recordRequest = new SaveExecuteRecordRequest();
                        recordRequest.setDataType(ExecuteRecordDataTypeEnum.CUSTOMER);
                        recordRequest.setExecuteType(ExecuteRecordExcuteTypeEnum.AUTOMATIC_DISPATCH);
                        recordRequest.setApplyOrgNo(organizationTreeVo.getOrgNo());
                        recordRequest.setApplyOrgName(organizationTreeVo.getOrgName());
                        recordRequest.setApplySource("MDM->DSRP");
                        recordRequest.setApplyEnterpriseNo(organizationTreeVo.getBindingEnterpriseNo());
                        recordRequest.setGroupEnterpriseNo(enterpriseNo);
                        recordRequest.setManageEnterpriseNo(enterpriseNo);
                        recordRequest.setObjNo(finalCustomer.getCustomerNo());
                        recordRequest.setObjCode(finalCustomer.getCustomerCode());
                        recordRequest.setObjName(finalCustomer.getCustomerName());
                        recordRequest.setExecuteTime(DateUtil.getCurrentDate());
                        recordRequest.setCreateNo(operationModel.getEmployerNo());
                        recordRequest.setCreateName(operationModel.getUserName());
                        executeRecordList.add(recordRequest);
                    }
                });

            }
        });
        if (!CollectionUtils.isEmpty(saveCustomerList)) {
            saveCustomerList.forEach(t -> CommonUtil.fillCreatInfo(operationModel, t));
            customerDAO.addBatch(saveCustomerList);
        }
        if (!CollectionUtils.isEmpty(saveCompanyBankList)) {
            saveCompanyBankList.forEach(t -> CommonUtil.fillCreatInfo(operationModel, t));
            companyBankDAO.addBatch(saveCompanyBankList);
        }
        if (!CollectionUtils.isEmpty(saveResponsibleManListList)) {
            saveResponsibleManListList.forEach(t -> CommonUtil.fillOperateInfo(operationModel, t));
            customerSalesManDAO.addBatch(saveResponsibleManListList);
        }
        if (!CollectionUtils.isEmpty(saveLinkmanListList)) {
            saveLinkmanListList.forEach(t -> CommonUtil.fillCreatInfo(operationModel, t));
            companyLinkmanDAO.addBatch(saveLinkmanListList);
        }
        if (!CollectionUtils.isEmpty(saveShippingAddressListList)) {
            saveShippingAddressListList.forEach(t -> CommonUtil.fillCreatInfo(operationModel, t));
            companyShippingAddressDAO.addBatch(saveShippingAddressListList);
        }
        if (!CollectionUtils.isEmpty(customerInvoiceList)) {
            customerInvoiceList.forEach(t -> CommonUtil.fillCreatInfo(operationModel, t));
            customerInvoiceDAO.addBatch(customerInvoiceList);
        }

        // 迪安客户批量保存接口(兼容MDM)，会在多组织的表里也插入一份数据
        customerV2ExternalService.batchSaveMdmCompatible(operationModel, params, resultList);

        //分派逻辑
        if (!CollectionUtils.isEmpty(executeRecordList)) {
            saveExecuteRecordListAndStartExecute(executeRecordList);
        }


//        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
//            @Override
//            public void afterCommit() {
//                try {
                    // 迪安客户批量保存接口(兼容MDM)，会在多组织的表里也插入一份数据
//                    customerV2ExternalService.batchSaveMdmCompatible(operationModel, params, resultList);
//                } catch (Exception e) {
//                    log.error("mdm接口兼容多组织失败", e);
//                }
//            }
//        });


        return saveBusinessLogAndRetResult(resultList);
    }

    /**
     * 保存执行记录并启动执行
     * @param executeRecordList
     */
    private void saveExecuteRecordListAndStartExecute(List<SaveExecuteRecordRequest> executeRecordList) {
        List<ExecuteRecordResponse> executeRecordResponseList = masterDataExecuteService.saveExecuteRecordListNoExecute(executeRecordList);
        executeRecordResponseList.forEach(executeRecord -> {
            customerExtendService.startCustomerAssignTask(executeRecord);
        });
    }

    /**
     * 保存业务日志，并返回转换后的对象
     * @param resultList
     * @return
     */
    private List<CustomerExternalSaveVO> saveBusinessLogAndRetResult(List<CustomerInternalSaveVO> resultList) {
        List<CustomerExternalSaveVO> retResultList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(resultList)){
            List<CustomerInternalSaveVO> successList = new ArrayList<>();
            List<CustomerInternalSaveVO> failList = new ArrayList<>();
            for (CustomerInternalSaveVO customerInternalSaveVO : resultList){
                CustomerExternalSaveVO customerExternalSaveVO = new CustomerExternalSaveVO();
                BeanUtils.copyProperties(customerInternalSaveVO, customerExternalSaveVO);
                retResultList.add(customerExternalSaveVO);
                if (customerInternalSaveVO.getSuccess() != null && !customerInternalSaveVO.getSuccess()
                        && !StringUtils.isEmpty(customerInternalSaveVO.getCustomerCode())){
                    failList.add(customerInternalSaveVO);
                }else if (customerInternalSaveVO.getSuccess() != null && customerInternalSaveVO.getSuccess()){
                    successList.add(customerInternalSaveVO);
                }
            }
            if (!CollectionUtils.isEmpty(successList)){
                mdmCustomerBusinessSuccessLog(successList);
            }
            if (!CollectionUtils.isEmpty(failList)){
                mdmCustomerBusinessFailLog(failList);
            }
        }
        return retResultList;
    }

    /**
     * mdm客户保存失败日志
     * @param failList
     */
    private void mdmCustomerBusinessFailLog(List<CustomerInternalSaveVO> failList) {
        if (CollectionUtils.isEmpty(failList)){
            return;
        }
        List<IntegrationLogDTO> errorList = new ArrayList<>();
        for (CustomerInternalSaveVO customerInternalSaveVO : failList) {
            IntegrationLogDTO integrationLogDTO = new IntegrationLogDTO();
            // 上游请求id，open推进来的不用管
//                integrationLogDTO.setRequestId(UUID.randomUUID().toString());
            integrationLogDTO.setRequestType(IntegrationRequestType.OPEN.getRequestType());
            integrationLogDTO.setSuccess(false); // 是否成功
            integrationLogDTO.setErrorCode(customerInternalSaveVO.getIntegrationError().getErrorCode()); // 错误编码
            integrationLogDTO.setErrorMsg(customerInternalSaveVO.getMessage()); // 错误信息
            integrationLogDTO.setBusinessBillType(MdmBillType.ZSKH.name());

            integrationLogDTO.setBusinessBillNo(customerInternalSaveVO.getCustomerCode());
            integrationLogDTO.setBusinessBillName(customerInternalSaveVO.getCustomerName());

            OperationModel operationModel = customerInternalSaveVO.getOperationModel();
            integrationLogDTO.setEnterpriseNo(operationModel.getEnterpriseNo());
            integrationLogDTO.setSubjectSystem(MdmBillType.systemCode); // 对接系统

            integrationLogDTO.setBusinessOrgNo(operationModel.getOrgNo());
            integrationLogDTO.setBusinessOrgName(operationModel.getOrgName());
            // 交易对象
//                integrationLogDTO.setTradeObject(t.getCustomerName());
            errorList.add(integrationLogDTO);
        }
        uLogAPI.saveBatchForRpc(errorList);
    }

    /**
     * 推送保存成功日志
     * @param successList
     */
    private void mdmCustomerBusinessSuccessLog(List<CustomerInternalSaveVO> successList) {
        if (CollectionUtils.isEmpty(successList)) {
            return;
        }
        List<IntegrationLogDTO> successLogDTOList = new ArrayList<>();
        for (CustomerInternalSaveVO customerInternalSaveVO : successList) {
            IntegrationLogDTO integrationLogDTO = new IntegrationLogDTO();
            // 上游请求id，open推进来的不用管
//                integrationLogDTO.setRequestId(UUID.randomUUID().toString());
            integrationLogDTO.setRequestType(IntegrationRequestType.OPEN.getRequestType());
            integrationLogDTO.setSuccess(true); // 是否成功
            integrationLogDTO.setBusinessBillType(MdmBillType.ZSKH.name());

            integrationLogDTO.setBusinessBillNo(customerInternalSaveVO.getCustomerCode());
            integrationLogDTO.setBusinessBillName(customerInternalSaveVO.getCustomerName());
            OperationModel operationModel = customerInternalSaveVO.getOperationModel();
            integrationLogDTO.setEnterpriseNo(operationModel.getEnterpriseNo());
            integrationLogDTO.setSubjectSystem(MdmBillType.systemCode); // 对接系统

            integrationLogDTO.setBusinessOrgNo(operationModel.getOrgNo());
            integrationLogDTO.setBusinessOrgName(operationModel.getOrgName());
            // 交易对象
//                integrationLogDTO.setTradeObject(t.getCustomerName());
            successLogDTOList.add(integrationLogDTO);
        }
        uLogAPI.saveBatchForRpc(successLogDTOList);
    }


    @Override
    public Boolean customerAssign(OperationModel operationModel, CustomerExternalAssignDTO params) {
        ValidatorUtils.checkTrueThrowEx(CollectionUtils.isEmpty(params.getAssignOrgList()), "分派组织不为空");
        ValidatorUtils.checkTrueThrowEx(params.getAssignOrgList().size() > 20, "分派组织不能超过20个");
        //组织编码
        final List<OrganizationTreeVo> treeList = uimTenantService.getOrganizationTreeList(operationModel.getEnterpriseNo());
        final Map<String, OrganizationTreeVo> treeMap = treeList.stream().collect(Collectors.toMap(OrganizationTreeVo::getOrgCode, Function.identity()));
        final Customer customer = customerDAO.getCustomerByCustomerCode(operationModel.getEnterpriseNo(), params.getCustomerCode());
        ValidatorUtils.checkTrueThrowEx(customer == null, "客户不存在");
        List<SaveExecuteRecordRequest> recordList = new ArrayList<>();
        params.getAssignOrgList().forEach(t -> {
            if (treeMap.containsKey(t.getOrgCode())) {
                final OrganizationTreeVo organizationTreeVo = treeMap.get(t.getOrgCode());
                SaveExecuteRecordRequest recordRequest = new SaveExecuteRecordRequest();
                recordRequest.setDataType(ExecuteRecordDataTypeEnum.CUSTOMER);
                recordRequest.setExecuteType(ExecuteRecordExcuteTypeEnum.AUTOMATIC_DISPATCH);
                recordRequest.setApplyOrgNo(organizationTreeVo.getOrgNo());
                recordRequest.setApplyOrgName(organizationTreeVo.getOrgName());
                recordRequest.setApplySource("MDM->DSRP");
                recordRequest.setApplyEnterpriseNo(organizationTreeVo.getBindingEnterpriseNo());
                recordRequest.setGroupEnterpriseNo(operationModel.getEnterpriseNo());
                recordRequest.setManageEnterpriseNo(operationModel.getEnterpriseNo());
                recordRequest.setObjNo(customer.getCustomerNo());
                recordRequest.setObjCode(customer.getCustomerCode());
                recordRequest.setObjName(customer.getCustomerName());
                recordRequest.setExecuteTime(DateUtil.getCurrentDate());
                recordRequest.setCreateNo(operationModel.getEmployerNo());
                recordRequest.setCreateName(operationModel.getUserName());
                recordList.add(recordRequest);
            }
        });
        if (!CollectionUtils.isEmpty(recordList)) {
            saveExecuteRecordListAndStartExecute(recordList);
        }
        return Boolean.TRUE;
    }
}
