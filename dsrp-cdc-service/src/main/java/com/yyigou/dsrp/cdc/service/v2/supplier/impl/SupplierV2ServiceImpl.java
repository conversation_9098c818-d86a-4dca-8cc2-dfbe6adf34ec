package com.yyigou.dsrp.cdc.service.v2.supplier.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Sets;
import com.yyigou.ddc.common.error.ErrorCode;
import com.yyigou.ddc.common.exception.BusinessException;
import com.yyigou.ddc.common.page.PageDto;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.ddc.common.util.DateUtil;
import com.yyigou.ddc.common.util.ValidatorUtils;
import com.yyigou.ddc.services.ddc.dict.vo.AreaCodeVo;
import com.yyigou.ddc.services.ddc.uap.util.UapThreadLocalUtil;
import com.yyigou.ddc.services.ddc.uim.vo.EmployeeVo;
import com.yyigou.ddc.services.ddc.uim.vo.OrganizationVo;
import com.yyigou.ddc.services.dlog.dto.DLogLevel;
import com.yyigou.dsrp.cdc.api.v2.company.vo.*;
import com.yyigou.dsrp.cdc.api.v2.supplier.vo.*;
import com.yyigou.dsrp.cdc.client.v2.company.response.CompanyLinkmanResponse;
import com.yyigou.dsrp.cdc.client.v2.company.response.CompanyShippingAddressResponse;
import com.yyigou.dsrp.cdc.client.v2.supplier.request.SupplierAssignDocExeRequest;
import com.yyigou.dsrp.cdc.client.v2.supplier.request.SupplierAssignExeRequest;
import com.yyigou.dsrp.cdc.client.v2.supplier.request.SupplierAssignMdmExeRequest;
import com.yyigou.dsrp.cdc.client.v2.supplier.response.*;
import com.yyigou.dsrp.cdc.common.enums.*;
import com.yyigou.dsrp.cdc.common.enums.company.*;
import com.yyigou.dsrp.cdc.common.enums.supplier.SupplierApplyTypeEnum;
import com.yyigou.dsrp.cdc.common.enums.supplier.SupplierBusinessFlagEnum;
import com.yyigou.dsrp.cdc.common.enums.supplier.SupplierControlStatusEnum;
import com.yyigou.dsrp.cdc.common.util.BeanUtil;
import com.yyigou.dsrp.cdc.common.util.CommonUtil;
import com.yyigou.dsrp.cdc.common.util.FormatNameUtils;
import com.yyigou.dsrp.cdc.common.util.OperationModel;
import com.yyigou.dsrp.cdc.dao.common.entity.BankType;
import com.yyigou.dsrp.cdc.dao.v2.company.CompanyLinkmanV2DAO;
import com.yyigou.dsrp.cdc.dao.v2.company.CompanyShippingAddressV2DAO;
import com.yyigou.dsrp.cdc.dao.v2.company.entity.CompanyBankV2;
import com.yyigou.dsrp.cdc.dao.v2.company.entity.CompanyLinkmanV2;
import com.yyigou.dsrp.cdc.dao.v2.company.entity.CompanyShippingAddressV2;
import com.yyigou.dsrp.cdc.dao.v2.company.entity.CompanyV2;
import com.yyigou.dsrp.cdc.dao.v2.supplier.*;
import com.yyigou.dsrp.cdc.dao.v2.supplier.entity.*;
import com.yyigou.dsrp.cdc.manager.integration.cert.CertService;
import com.yyigou.dsrp.cdc.manager.integration.cert.req.CompanyCertQueryRequest;
import com.yyigou.dsrp.cdc.manager.integration.cert.res.CompanyCertResponse;
import com.yyigou.dsrp.cdc.manager.integration.dict.DictEnterpriseService;
import com.yyigou.dsrp.cdc.manager.integration.dict.enums.DictNumberEnum;
import com.yyigou.dsrp.cdc.manager.integration.dlog.BusinessLogService;
import com.yyigou.dsrp.cdc.manager.integration.numberCenter.NumberCenterService;
import com.yyigou.dsrp.cdc.manager.integration.org.UimTenantService;
import com.yyigou.dsrp.cdc.manager.integration.uap.GradeControlService;
import com.yyigou.dsrp.cdc.manager.integration.uap.res.GradeCheckMgrOrgRes;
import com.yyigou.dsrp.cdc.manager.integration.uim.CustomDocService;
import com.yyigou.dsrp.cdc.manager.integration.uim.EmployeeService;
import com.yyigou.dsrp.cdc.manager.integration.uim.OrganizationService;
import com.yyigou.dsrp.cdc.manager.integration.uim.TransactionTypeService;
import com.yyigou.dsrp.cdc.manager.integration.uim.response.CustomDocResponse;
import com.yyigou.dsrp.cdc.manager.integration.uim.response.TransactionTypeResponse;
import com.yyigou.dsrp.cdc.manager.integration.ulog.ULogService;
import com.yyigou.dsrp.cdc.model.constant.*;
import com.yyigou.dsrp.cdc.model.enums.FactoryTypeEnum;
import com.yyigou.dsrp.cdc.model.v2.company.req.CompanyBankReq;
import com.yyigou.dsrp.cdc.model.v2.company.req.CompanyLinkmanReq;
import com.yyigou.dsrp.cdc.model.v2.company.req.CompanySaveOrUpdateReq;
import com.yyigou.dsrp.cdc.model.v2.company.req.CompanyShippingAddressReq;
import com.yyigou.dsrp.cdc.model.v2.supplier.req.*;
import com.yyigou.dsrp.cdc.service.utils.PageUtils;
import com.yyigou.dsrp.cdc.service.utils.RedisClientUtil;
import com.yyigou.dsrp.cdc.service.v2.company.CompanyV2Service;
import com.yyigou.dsrp.cdc.service.v2.customer.CustomerV2Service;
import com.yyigou.dsrp.cdc.service.v2.supplier.SupplierCategoryV2Service;
import com.yyigou.dsrp.cdc.service.v2.supplier.SupplierGspAuditService;
import com.yyigou.dsrp.cdc.service.v2.supplier.SupplierV2Service;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service("supplierV2Service")
@RequiredArgsConstructor
@Slf4j
public class SupplierV2ServiceImpl extends ServiceImpl<SupplierV2DAO, SupplierV2> implements SupplierV2Service {
    private final NumberCenterService numberCenterService;

    @Resource
    private SupplierV2DAO supplierV2DAO;

    @Resource
    private SupplierBizDAO supplierBizDAO;

    @Resource
    private SupplierBaseDAO supplierBaseDAO;

    @Resource
    private SupplierGspAuditDAO supplierGspAuditDAO;

    @Resource
    private SupplierCategoryV2Service supplierCategoryV2Service;

    @Resource
    private CustomerV2Service customerV2Service;

    @Resource
    private CompanyV2Service companyV2Service;

    @Resource
    private CustomDocService customDocService;

    @Resource
    private DictEnterpriseService dictEnterpriseService;

    @Resource
    private TransactionTypeService transactionTypeService;

    @Resource
    private CompanyLinkmanV2DAO companyLinkmanV2DAO;

    @Resource
    private CompanyShippingAddressV2DAO companyShippingAddressV2DAO;

    @Resource
    private SupplierOrderManV2DAO supplierOrderManV2DAO;

    @Resource
    private EmployeeService employeeService;

    @Resource
    private BusinessLogService businessLogService;

    @Resource
    private OrganizationService organizationService;

    @Resource
    private GradeControlService gradeControlService;

    @Resource
    private ULogService uLogService;

    @Resource
    private RedisClientUtil redisClientUtil;

    @Resource
    private UimTenantService uimTenantService;

    @Resource
    private SupplierGspAuditService supplierGspAuditService;

    @Resource
    private CertService certService;

    @Override
    public PageVo<SupplierPageVO> manageFindListPage(OperationModel operationModel, SupplierManageQueryListPageReq queryReq, PageDto pageDTO) {
        ValidatorUtils.checkEmptyThrowEx(queryReq, "查询条件不能为空");

        Page<SupplierV2WithBiz> page = PageHelper.startPage(pageDTO.getPageIndex(), pageDTO.getPageSize(), StringUtils.isBlank(pageDTO.getOrderBy()) ? "b.create_time desc" : pageDTO.getOrderBy());
        supplierV2DAO.selectManageView(queryReq);

        return PageUtils.convertPageVo(page, data -> completeSupplementPageVO(operationModel.getEnterpriseNo(), data));
    }

    @Override
    public PageVo<SupplierPageVO> useFindListPage(OperationModel operationModel, SupplierUseQueryListPageReq queryReq, PageDto pageDTO) {
        ValidatorUtils.checkEmptyThrowEx(queryReq, "查询条件不能为空");

        Page<SupplierV2WithBiz> page = PageHelper.startPage(pageDTO.getPageIndex(), pageDTO.getPageSize(), StringUtils.isBlank(pageDTO.getOrderBy()) ? "b.create_time desc" : pageDTO.getOrderBy());
        supplierV2DAO.selectUseView(queryReq);
        return PageUtils.convertPageVo(page, data -> completeSupplementPageVO(operationModel.getEnterpriseNo(), data));
    }

    @Override
    public PageVo<SupplierPageVO> findListPageForTenant(SupplierFindQueryListPageForTenantReq queryReq, PageDto pageDTO) {
        com.yyigou.dsrp.gcs.common.util.ValidatorUtils.checkEmptyThrowEx(queryReq.getEnterpriseNo(), "租户编号为空");
        try {
            UapThreadLocalUtil.setQueryPlanNextSqlJumpFlag(true);
            Set<String> supplierCodeSet = supplierV2DAO.selectDistinctSupplierCodeByUseOrgNoList(queryReq);
            if (CollectionUtils.isEmpty(supplierCodeSet)) {
                return new PageVo<>(pageDTO.getPageIndex(), pageDTO.getPageSize(), 0L, new ArrayList<>());
            }
            queryReq.setSupplierCodeSet(supplierCodeSet);
        } finally {
            UapThreadLocalUtil.setQueryPlanNextSqlJumpFlag(null);
        }
        Page<SupplierV2WithBase> page = PageHelper.startPage(pageDTO.getPageIndex(), pageDTO.getPageSize(), StringUtils.isBlank(pageDTO.getOrderBy()) ? "create_time desc" : pageDTO.getOrderBy());
        List<SupplierV2WithBase> supplierWithBaseForTenantList = supplierV2DAO.findListPageForTenant(queryReq);
        if (CollectionUtils.isEmpty(supplierWithBaseForTenantList)) {
            return new PageVo<>(pageDTO.getPageIndex(), pageDTO.getPageSize(), 0L, new ArrayList<>());
        }

        return PageUtils.convertPageVo(page, data -> completeSupplementForTenantPageVO(queryReq.getEnterpriseNo(), queryReq.getUseOrgNoList(), data));
    }

    @Override
    public PageVo<SupplierPageVO> useFindListPageNoAuthZ(OperationModel operationModel, SupplierUseQueryListPageNoAuthZReq queryReq, PageDto pageDTO) {
        ValidatorUtils.checkEmptyThrowEx(queryReq, "查询条件不能为空");
        ValidatorUtils.checkEmptyThrowEx(queryReq.getUseOrgNo(), "使用组织不能为空");

        Page<SupplierV2WithBiz> page = PageHelper.startPage(pageDTO.getPageIndex(), pageDTO.getPageSize(), StringUtils.isBlank(pageDTO.getOrderBy()) ? "b.create_time desc" : pageDTO.getOrderBy());
        supplierV2DAO.selectUseViewNoAuthZ(queryReq);
        return PageUtils.convertPageVo(page, data -> completeSupplementPageVO(operationModel.getEnterpriseNo(), data));

    }

    @Override
    public PageVo<SupplierReversePageVO> reverseUseFindListPageNoAuthZ(OperationModel operationModel, SupplierReverseUseQueryListPageNoAuthZReq queryReq, PageDto pageDTO) {
        ValidatorUtils.checkEmptyThrowEx(queryReq, "查询条件不能为空");
        ValidatorUtils.checkEmptyThrowEx(queryReq.getUseOrgNo(), "使用组织不能为空");

        Page<SupplierV2WithBase> page = PageHelper.startPage(pageDTO.getPageIndex(), pageDTO.getPageSize(), StringUtils.isBlank(pageDTO.getOrderBy()) ? "a.create_time desc" : pageDTO.getOrderBy());
        supplierV2DAO.selectReverseUseViewNoAuthZ(queryReq);
        return PageUtils.convertPageVo(page, data -> completeSupplementReversePageVO(operationModel.getEnterpriseNo(), queryReq.getUseOrgNo(), data));

    }


    @Override
    @Transactional
    public Boolean manageDeleteSupplier(OperationModel operationModel, SupplierDeleteReq params) {
        ValidatorUtils.checkEmptyThrowEx(params.getSupplierCode(), "供应商编码不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getManageOrgNo(), "管理组织编号不能为空");

        LambdaQueryWrapper<SupplierV2> supplierQueryWrapper = Wrappers.<SupplierV2>lambdaQuery()
                .eq(SupplierV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(SupplierV2::getManageOrgNo, params.getManageOrgNo())
                .eq(SupplierV2::getSupplierCode, params.getSupplierCode())
                .eq(SupplierV2::getDeleted, DeletedEnum.UN_DELETE.getValue());

        SupplierV2 supplierV2 = supplierV2DAO.selectOne(supplierQueryWrapper);

        ValidatorUtils.checkEmptyThrowEx(supplierV2, "供应商不存在");

        ValidatorUtils.checkEmptyThrowEx(!SupplierBusinessFlagEnum.DRAFT.getValue().equals(supplierV2.getBusinessFlag()), "供应商不是草稿态");

        // 基础信息
        SupplierV2 newSupplierV2 = new SupplierV2();
        newSupplierV2.setDeleted(DeletedEnum.DELETED.getValue());
        CommonUtil.fillOperateInfo(operationModel, newSupplierV2);
        supplierV2DAO.update(newSupplierV2, supplierQueryWrapper);

        // 业务信息
        LambdaQueryWrapper<SupplierBiz> bizQueryWrapper = Wrappers.<SupplierBiz>lambdaQuery()
                .eq(SupplierBiz::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(SupplierBiz::getUseOrgNo, supplierV2.getManageOrgNo())
                .eq(SupplierBiz::getSupplierCode, supplierV2.getSupplierCode())
                .eq(SupplierBiz::getDeleted, DeletedEnum.UN_DELETE.getValue());

        SupplierBiz supplierBiz = supplierBizDAO.selectOne(bizQueryWrapper);
        ValidatorUtils.checkEmptyThrowEx(supplierBiz, "供应商业务信息不存在");

        SupplierBiz newSupplierBiz = new SupplierBiz();
        newSupplierBiz.setDeleted(DeletedEnum.DELETED.getValue());
        CommonUtil.fillOperateInfo(operationModel, newSupplierBiz);
        supplierBizDAO.update(newSupplierBiz, bizQueryWrapper);

        // 分派信息
        SupplierBase newSupplierBase = new SupplierBase();
        newSupplierBase.setDeleted(DeletedEnum.DELETED.getValue());
        CommonUtil.fillOperateInfo(operationModel, newSupplierBase);
        supplierBaseDAO.update(newSupplierBase, Wrappers.<SupplierBase>lambdaQuery()
                .eq(SupplierBase::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(SupplierBase::getUseOrgNo, supplierV2.getManageOrgNo())
                .eq(SupplierBase::getSupplierCode, supplierV2.getSupplierCode())
                .eq(SupplierBase::getDeleted, DeletedEnum.UN_DELETE.getValue()));

        // 联系人
        CompanyLinkmanV2 newCompanyLinkmanV2 = new CompanyLinkmanV2();
        newCompanyLinkmanV2.setDeleted(DeletedEnum.DELETED.getValue());
        CommonUtil.fillOperateInfo(operationModel, newCompanyLinkmanV2);
        companyLinkmanV2DAO.update(newCompanyLinkmanV2, Wrappers.<CompanyLinkmanV2>lambdaQuery()
                .eq(CompanyLinkmanV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CompanyLinkmanV2::getUseOrgNo, supplierV2.getManageOrgNo())
                .eq(CompanyLinkmanV2::getSourceNo, supplierV2.getSupplierCode())
                .eq(CompanyLinkmanV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));

        // 联系地址
        CompanyShippingAddressV2 newCompanyShippingAddressV2 = new CompanyShippingAddressV2();
        newCompanyShippingAddressV2.setDeleted(DeletedEnum.DELETED.getValue());
        CommonUtil.fillOperateInfo(operationModel, newCompanyShippingAddressV2);
        companyShippingAddressV2DAO.update(newCompanyShippingAddressV2, Wrappers.<CompanyShippingAddressV2>lambdaQuery()
                .eq(CompanyShippingAddressV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CompanyShippingAddressV2::getUseOrgNo, supplierV2.getManageOrgNo())
                .eq(CompanyShippingAddressV2::getSourceNo, supplierV2.getSupplierCode())
                .eq(CompanyShippingAddressV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));

        // 负责人
        SupplierOrderManV2 newSupplierOrderManV2 = new SupplierOrderManV2();
        newSupplierOrderManV2.setDeleted(DeletedEnum.DELETED.getValue());
        CommonUtil.fillOperateInfo(operationModel, newSupplierOrderManV2);
        supplierOrderManV2DAO.update(newSupplierOrderManV2, Wrappers.<SupplierOrderManV2>lambdaQuery()
                .eq(SupplierOrderManV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(SupplierOrderManV2::getUseOrgNo, supplierV2.getManageOrgNo())
                .eq(SupplierOrderManV2::getSupplierCode, supplierV2.getSupplierCode())
                .eq(SupplierOrderManV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));


        Map<String, Object> businessValue = new HashMap<>();
        newSupplierV2.setSupplierCode(supplierV2.getSupplierCode());
        businessValue.put("supplier", newSupplierV2);
        newSupplierBiz.setSupplierCode(supplierBiz.getSupplierCode());
        businessValue.put("supplierBiz", newSupplierBiz);
        newSupplierBase.setSupplierCode(supplierV2.getSupplierCode());
        businessValue.put("supplierBase", newSupplierBase);

        newCompanyLinkmanV2.setSourceNo(supplierV2.getSupplierCode());
        businessValue.put("companyLinkman", newCompanyLinkmanV2);

        newCompanyShippingAddressV2.setSourceNo(supplierV2.getSupplierCode());
        businessValue.put("companyShippingAddress", newCompanyShippingAddressV2);

        newSupplierOrderManV2.setSupplierCode(supplierV2.getSupplierCode());
        businessValue.put("supplierOrderMan", newSupplierOrderManV2);

        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                try {
                    businessLogService.saveLog(DLogLevel.INFO.name(), ViewNameConstant.BDC_SUPPLIER_VIEW, params.getSupplierCode() + params.getManageOrgNo(), "删除供应商档案", "删除供应商档案", JSON.toJSONString(businessValue), "");
                } catch (Exception e) {
                    log.error("删除供应商日志保存失败", e);
                }
            }
        });

        return true;
    }


    private void validateEditBasicAndBizReq(OperationModel operationModel, SupplierEditBasicAndBizReq params) {
        //必填项校验
        ValidatorUtils.checkEmptyThrowEx(params.getSupplierCode(), "供应商编码不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getSupplierName(), "供应商名称不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getTransactionType(), "供应商类型不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getSupplierCategoryNo(), "供应商分类不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getCompanyName(), "企业名称不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getUnifiedSocialCode(), "统一社会信用代码不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getFactoryType(), "企业注册地域不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getTaxCategory(), "纳税类别不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getRetailInvestors(), "是否散户不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getIsAssociatedEnterprise(), "是否内部组织不能为空");
        if (null != params.getIsAssociatedEnterprise() && CommonIfEnum.YES.getValue().equals(params.getIsAssociatedEnterprise())) {
            ValidatorUtils.checkEmptyThrowEx(params.getAssociatedOrgNo(), "对应内部组织不能为空");
        }

        //范围校验
        if (StringUtils.isNotEmpty(params.getSupplierCode())) {
            ValidatorUtils.checkTrueThrowEx(params.getSupplierCode().length() > 50, "供应商编码不能大于50");
        }
        if (StringUtils.isNotEmpty(params.getSupplierName())) {
            ValidatorUtils.checkTrueThrowEx(params.getSupplierName().length() > 128, "供应商名称不能大于128");
        }
        if (StringUtils.isNotEmpty(params.getSupplierNameEn())) {
            ValidatorUtils.checkTrueThrowEx(params.getSupplierNameEn().length() > 300, "供应商英文名称不能大于300");
        }
        if (StringUtils.isNotEmpty(params.getMnemonicCode())) {
            ValidatorUtils.checkTrueThrowEx(params.getMnemonicCode().length() > 100, "助记码不能大于100");
        }
        if (StringUtils.isNotEmpty(params.getCompanyName())) {
            ValidatorUtils.checkTrueThrowEx(params.getCompanyName().length() > 128, "企业名称不能大于128");
        }
        if (StringUtils.isNotEmpty(params.getUnifiedSocialCode())) {
            ValidatorUtils.checkTrueThrowEx(params.getUnifiedSocialCode().length() > 100, "统一社会信用代码不能大于100");
        }
        if (StringUtils.isNotEmpty(params.getCountry())) {
            ValidatorUtils.checkTrueThrowEx(params.getCountry().length() > 50, "国家/地区不能大于50");
        }
        if (StringUtils.isNotEmpty(params.getRemark())) {
            ValidatorUtils.checkTrueThrowEx(params.getRemark().length() > 300, "备注不能大于300");
        }


        if (StringUtils.isNotEmpty(params.getCreditAmount())) {
            ValidatorUtils.checkTrueThrowEx(params.getCreditAmount().length() > 20, "信用额度不能大于20");
        }
        if (StringUtils.isNotEmpty(params.getOwnerCompany())) {
            ValidatorUtils.checkTrueThrowEx(params.getOwnerCompany().length() > 100, "业务归属不能大于100");
        }
    }

    private void validateEditBasicReq(OperationModel operationModel, SupplierEditBasicReq params) {
        //必填项校验
        ValidatorUtils.checkEmptyThrowEx(params.getSupplierCode(), "供应商编码不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getSupplierName(), "供应商名称不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getTransactionType(), "供应商类型不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getSupplierCategoryNo(), "供应商分类不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getCompanyName(), "企业名称不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getUnifiedSocialCode(), "统一社会信用代码不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getFactoryType(), "企业注册地域不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getTaxCategory(), "纳税类别不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getRetailInvestors(), "是否散户不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getIsAssociatedEnterprise(), "是否内部组织不能为空");
        if (null != params.getIsAssociatedEnterprise() && CommonIfEnum.YES.getValue().equals(params.getIsAssociatedEnterprise())) {
            ValidatorUtils.checkEmptyThrowEx(params.getAssociatedOrgNo(), "对应内部组织不能为空");
        }

        //范围校验
        if (StringUtils.isNotEmpty(params.getSupplierCode())) {
            ValidatorUtils.checkTrueThrowEx(params.getSupplierCode().length() > 50, "供应商编码不能大于50");
        }
        if (StringUtils.isNotEmpty(params.getSupplierName())) {
            ValidatorUtils.checkTrueThrowEx(params.getSupplierName().length() > 128, "供应商名称不能大于128");
        }
        if (StringUtils.isNotEmpty(params.getSupplierNameEn())) {
            ValidatorUtils.checkTrueThrowEx(params.getSupplierNameEn().length() > 300, "供应商英文名称不能大于300");
        }
        if (StringUtils.isNotEmpty(params.getMnemonicCode())) {
            ValidatorUtils.checkTrueThrowEx(params.getMnemonicCode().length() > 100, "助记码不能大于100");
        }
        if (StringUtils.isNotEmpty(params.getCompanyName())) {
            ValidatorUtils.checkTrueThrowEx(params.getCompanyName().length() > 128, "企业名称不能大于128");
        }
        if (StringUtils.isNotEmpty(params.getUnifiedSocialCode())) {
            ValidatorUtils.checkTrueThrowEx(params.getUnifiedSocialCode().length() > 100, "统一社会信用代码不能大于100");
        }
        if (StringUtils.isNotEmpty(params.getCountry())) {
            ValidatorUtils.checkTrueThrowEx(params.getCountry().length() > 50, "国家/地区不能大于50");
        }
        if (StringUtils.isNotEmpty(params.getRemark())) {
            ValidatorUtils.checkTrueThrowEx(params.getRemark().length() > 300, "备注不能大于300");
        }
    }


    @Override
    public SupplierVO getSupplier(OperationModel operationModel, SupplierGetReq params) {
        SupplierV2 supplier = supplierV2DAO.selectOne(Wrappers.<SupplierV2>lambdaQuery()
                .eq(SupplierV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(SupplierV2::getSupplierCode, params.getSupplierCode())
                .eq(SupplierV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
        ValidatorUtils.checkEmptyThrowEx(supplier, "供应商不存在");

        return packDetail(operationModel, params, supplier);
    }

    private SupplierVO packDetail(OperationModel operationModel, SupplierGetReq params, SupplierV2 supplier) {

        //基本信息
        SupplierVO supplierVO = new SupplierVO();
        BeanUtils.copyProperties(supplier, supplierVO);

        //业务信息
        SupplierBiz supplierBiz = supplierBizDAO.selectOne(Wrappers.<SupplierBiz>lambdaQuery()
                .eq(SupplierBiz::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(SupplierBiz::getUseOrgNo, params.getUseOrgNo())
                .eq(SupplierBiz::getSupplierCode, params.getSupplierCode())
                .eq(SupplierBiz::getDeleted, DeletedEnum.UN_DELETE.getValue()));
        if (null != supplierBiz) {
            BeanUtils.copyProperties(supplierBiz, supplierVO);
        }

        //分派信息
        SupplierBase supplierAssign = supplierBaseDAO.selectOne(Wrappers.<SupplierBase>lambdaQuery()
                .eq(SupplierBase::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(SupplierBase::getUseOrgNo, params.getUseOrgNo())
                .eq(SupplierBase::getSupplierCode, params.getSupplierCode())
                .eq(SupplierBase::getDeleted, DeletedEnum.UN_DELETE.getValue()));
        if (null != supplierAssign) {
            supplierVO.setControlStatus(supplierAssign.getControlStatus());
            supplierVO.setControlStatusName(SupplierControlStatusEnum.getByType(supplierAssign.getControlStatus()) == null ? null : SupplierControlStatusEnum.getByType(supplierAssign.getControlStatus()).getName());
        }

        SupplierGspAudit supplierGspAudit = supplierGspAuditDAO.selectOne(Wrappers.<SupplierGspAudit>lambdaQuery()
                .eq(SupplierGspAudit::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(SupplierGspAudit::getUseOrgNo, params.getUseOrgNo())
                .eq(SupplierGspAudit::getSupplierCode, params.getSupplierCode())
                .eq(SupplierGspAudit::getDeleted, DeletedEnum.UN_DELETE.getValue()));

        Map<String, Object> infoMap = collectBasicInfo(operationModel.getEnterpriseNo(), Collections.singletonList(supplier), Collections.singletonList(params.getUseOrgNo()), Sets.newHashSet(
                "companyMap",
                "taxCategoryMap",
                "economicTypeMap",
                "orgMap",
                "categoryMap",
                "currencyMap",
                "cooperationMap",
                "paymentTermMap",
                "settlementModesMap",
                "transactionTypeMap",
                "linkManMap",
                "addressMap",
                "orderManMap",
                "bankMap",
                "countryRegionMap"
        ));

        //填充企业信息
        final CompanyV2 company = ((Map<String, CompanyV2>) infoMap.get("companyMap")).get(supplier.getCompanyNo());
        final Map<String, String> countryRegionMap = (Map<String, String>) infoMap.get("countryRegionMap");
        if (company != null) {
            supplierVO.setCompanyName(company.getCompanyName());
            supplierVO.setUnifiedSocialCode(company.getUnifiedSocialCode());
//            supplierVO.setCountry(company.getCountry());
            supplierVO.setCountryRegionId(company.getCountryRegionId());
            if (StringUtils.isNotEmpty(company.getCountryRegionId())) {
                supplierVO.setCountryRegionName(countryRegionMap.get(company.getCountryRegionId()));
            }
            supplierVO.setFactoryType(company.getFactoryType());
            supplierVO.setFactoryTypeName(FactoryTypeEnum.getByType(company.getFactoryType()) == null ? null : FactoryTypeEnum.getByType(company.getFactoryType()).getName());
            supplierVO.setEconomicType(company.getEconomicType());
//            supplierVO.setIsAssociatedEnterprise(company.getIsAssociatedEnterprise());
//            supplierVO.setAssociatedOrgNo(company.getAssociatedOrgNo());
//            supplierVO.setAssociatedOrgCode(company.getAssociatedOrgCode());
//            supplierVO.setAssociatedOrgName(company.getAssociatedOrgName());

            if (null != company.getRegionCode()) {
                List<AreaCodeVo> areaCodeList = dictEnterpriseService.findAreaByCodes(Collections.singletonList(company.getRegionCode()));
                final Map<String, AreaCodeVo> areaMap = areaCodeList.stream().collect(Collectors.toMap(AreaCodeVo::getAreaCode, Function.identity()));
                supplierVO.setRegionFullName(areaMap.getOrDefault(company.getRegionCode(), new AreaCodeVo()).getAreaFullName());
            }
            supplierVO.setAddress(company.getAddress());
            supplierVO.setIsMedicalInstitution(company.getIsMedicalInstitution());
            supplierVO.setIsMedicalInstitutionName(CommonIfEnum.getNameByValue(company.getIsMedicalInstitution()));
            supplierVO.setInstitutionalType(company.getInstitutionalType());
            supplierVO.setInstitutionalTypeName(InstitutionalTypeEnum.getByType(supplierVO.getInstitutionalType()) != null ? InstitutionalTypeEnum.getByType(supplierVO.getInstitutionalType()).getName() : "");
            supplierVO.setHospitalType(company.getHospitalType());
            supplierVO.setHospitalTypeName(HospitalTypeEnum.getByType(supplierVO.getHospitalType()) != null ? HospitalTypeEnum.getByType(supplierVO.getHospitalType()).getName() : "");
            supplierVO.setHospitalClass(company.getHospitalClass());
            supplierVO.setHospitalClassName(HospitalClassEnum.getByType(supplierVO.getHospitalClass()) != null ? HospitalClassEnum.getByType(supplierVO.getHospitalClass()).getName() : "");

            if (StringUtils.isNotBlank(company.getTaxCategory())) {
                final Map<String, CustomDocResponse> taxCategoryMap = (Map<String, CustomDocResponse>) infoMap.get("taxCategoryMap");
                supplierVO.setTaxCategory(company.getTaxCategory());
                supplierVO.setTaxCategoryName(taxCategoryMap.getOrDefault(company.getTaxCategory(), new CustomDocResponse()).getDocItemName());
            }
            if (StringUtils.isNotBlank(company.getEconomicType())) {
                final Map<String, CustomDocResponse> economicTypeMap = (Map<String, CustomDocResponse>) infoMap.get("economicTypeMap");
                supplierVO.setEconomicTypeName(economicTypeMap.getOrDefault(company.getEconomicType(), new CustomDocResponse()).getDocItemName());
            }

            // 企业资料
            CompanyCertQueryRequest companyCertQueryRequest = new CompanyCertQueryRequest();
            companyCertQueryRequest.setEnterpriseNo(operationModel.getEnterpriseNo());
            companyCertQueryRequest.setOrgNo(params.getUseOrgNo());
            companyCertQueryRequest.setCompanyNo(company.getCompanyNo());
            companyCertQueryRequest.setSourceDocType(CertSourceTypeEnum.SUPPLIER.getValue());
            companyCertQueryRequest.setSourceDocCode(supplier.getSupplierCode());
            List<CompanyCertResponse> companyCertResponseList = certService.findList(companyCertQueryRequest);
            if (CollectionUtils.isNotEmpty(companyCertResponseList)) {
                Map<Integer, List<CompanyCertResponse>> docType2CertList = companyCertResponseList.stream().collect(Collectors.groupingBy(CompanyCertResponse::getSourceDocType));

                // 企业资料
                if (docType2CertList.containsKey(CertSourceTypeEnum.COMPANY.getValue())) {
                    List<CompanyCertResponse> rows = docType2CertList.get(CertSourceTypeEnum.COMPANY.getValue());
                    if (CollectionUtils.isNotEmpty(rows)) {
                        supplierVO.setCompanyCertList(BeanUtil.copyFieldsListForJSON(rows, CompanyCertVO.class));
                    }
                }

                // 使用组织资料
                if (docType2CertList.containsKey(CertSourceTypeEnum.SUPPLIER.getValue())) {
                    List<CompanyCertResponse> rows = docType2CertList.get(CertSourceTypeEnum.SUPPLIER.getValue());
                    if (CollectionUtils.isNotEmpty(rows)) {
                        supplierVO.setSupplierCertList(BeanUtil.copyFieldsListForJSON(rows, CompanyCertVO.class));
                    }
                }
            }
        }

        //填充编码字段对应的名称
        supplierVO.setRetailInvestorsName(CommonIfEnum.getNameByValue(supplier.getRetailInvestors()));

        supplierVO.setIsAssociatedEnterpriseName(CommonIfEnum.getNameByValue(supplier.getIsAssociatedEnterprise()));

        final Map<String, OrganizationVo> orgNo2OrganizationVo = (Map<String, OrganizationVo>) infoMap.get("orgMap");


        if (orgNo2OrganizationVo.containsKey(supplier.getManageOrgNo())) {
            supplierVO.setManageOrgName(orgNo2OrganizationVo.get(supplier.getManageOrgNo()).getOrgName());
        }

        supplierVO.setUseOrgNo(params.getUseOrgNo());

        if (orgNo2OrganizationVo.containsKey(params.getUseOrgNo())) {
            supplierVO.setUseOrgName(orgNo2OrganizationVo.get(params.getUseOrgNo()).getOrgName());
        }

        //供应商分类
        if (StringUtils.isNotBlank(supplier.getSupplierCategoryNo())) {
            final SupplierCategoryV2 supplierCategory = ((Map<String, SupplierCategoryV2>) infoMap.get("categoryMap")).get(supplier.getSupplierCategoryNo());
            if (supplierCategory != null) {
                supplierVO.setSupplierCategoryName(supplierCategory.getCategoryName());
            }
        }

        if (null != supplierBiz) {
            //交易币种
            if (StringUtils.isNotBlank(supplierBiz.getCurrencyId())) {
                final Map<String, String> currencyMap = (Map<String, String>) infoMap.get("currencyMap");
                supplierVO.setCurrencyName(currencyMap.getOrDefault(supplierBiz.getCurrencyId(), ""));
            }
            //合作性质
            if (StringUtils.isNotBlank(supplierBiz.getCooperationMode())) {
                final Map<String, CustomDocResponse> cooperationMap = (Map<String, CustomDocResponse>) infoMap.get("cooperationMap");
                supplierVO.setCooperationModeName(cooperationMap.getOrDefault(supplierBiz.getCooperationMode(), new CustomDocResponse()).getDocItemName());
            }

            //付款协议
            if (StringUtils.isNotBlank(supplierBiz.getPaymentTerm())) {
                final Map<String, CustomDocResponse> paymentTermMap = (Map<String, CustomDocResponse>) infoMap.get("paymentTermMap");
                supplierVO.setPaymentTermName(paymentTermMap.getOrDefault(supplierBiz.getPaymentTerm(), new CustomDocResponse()).getDocItemName());
            }

            //结算方式
            if (StringUtils.isNotBlank(supplierBiz.getSettlementModes())) {
                final Map<String, String> settlementModesMap = (Map<String, String>) infoMap.get("settlementModesMap");
                supplierVO.setSettlementModesName(settlementModesMap.getOrDefault(supplierBiz.getSettlementModes(), ""));
            }
        }

        if (null != supplierGspAudit) {
            supplierVO.setGspStatus(supplierGspAudit.getGspAuditStatus());
            supplierVO.setGspStatusName(null != GspAuditStatusEnum.getByType(supplierGspAudit.getGspAuditStatus()) ? GspAuditStatusEnum.getByType(supplierGspAudit.getGspAuditStatus()).getName() : "");
        }

        //供应商类型
        if (StringUtils.isNotBlank(supplier.getTransactionType())) {
            final Map<String, TransactionTypeResponse> transactionTypeMap = (Map<String, TransactionTypeResponse>) infoMap.get("transactionTypeMap");
            supplierVO.setTransactionTypeName(transactionTypeMap.getOrDefault(supplier.getTransactionType(), new TransactionTypeResponse()).getTransactionTypeName());
        }


        // 获取联系人
        final Map<String, Map<String, List<CompanyLinkmanV2>>> code2org2LinkManList = (Map<String, Map<String, List<CompanyLinkmanV2>>>) infoMap.get("linkManMap");

        // 获取地址
        final Map<String, Map<String, List<CompanyShippingAddressV2>>> code2org2AddressList = (Map<String, Map<String, List<CompanyShippingAddressV2>>>) infoMap.get("addressMap");

        // 获取负责人
        final Map<String, Map<String, List<SupplierOrderManV2>>> code2org2OrderManList = (Map<String, Map<String, List<SupplierOrderManV2>>>) infoMap.get("orderManMap");


        //供应商联系人
        final List<CompanyLinkmanV2> supplierLinkmanList = code2org2LinkManList.getOrDefault(supplier.getSupplierCode(), Collections.emptyMap()).get(params.getUseOrgNo());
        if (CollectionUtils.isNotEmpty(supplierLinkmanList)) {
            List<CompanyLinkmanVO> supplierLinkmanVoList = new ArrayList<>();
            supplierLinkmanList.forEach(t -> {
                CompanyLinkmanVO vo = new CompanyLinkmanVO();
                BeanUtils.copyProperties(t, vo);

                vo.setSexName(null != SexEnum.getByType(vo.getSex()) ? SexEnum.getByType(vo.getSex()).getName() : "");
                vo.setStatusName(CommonStatusEnum.getNameByValue(vo.getStatus()));
                vo.setIsDefaultName(CommonIfEnum.getNameByValue(vo.getIsDefault()));

                supplierLinkmanVoList.add(vo);
            });
            supplierVO.setLinkmanList(supplierLinkmanVoList);
        }

        //负责人信息
        final List<SupplierOrderManV2> salesManList = code2org2OrderManList.getOrDefault(supplier.getSupplierCode(), Collections.emptyMap()).get(params.getUseOrgNo());
        if (CollectionUtils.isNotEmpty(salesManList)) {
            final List<String> salesManNoList = salesManList.stream().map(SupplierOrderManV2::getOrderManNo).collect(Collectors.toList());
            Map<String, EmployeeVo> employeeMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(salesManNoList)) {
                final List<EmployeeVo> employeeList = employeeService.getEmployeeList(operationModel.getEnterpriseNo(), salesManNoList);
                employeeMap = employeeList.stream().collect(Collectors.toMap(EmployeeVo::getEmployeeNo, Function.identity()));
            }
            List<SupplierOrderManVO> salesManVoList = new ArrayList<>();
            Map<String, EmployeeVo> finalEmployeeMap = employeeMap;
            salesManList.forEach(t -> {
                SupplierOrderManVO supplierOrderManVO = new SupplierOrderManVO();
                BeanUtils.copyProperties(t, supplierOrderManVO);
                supplierOrderManVO.setDeptNo(t.getDeptNo());
                supplierOrderManVO.setDeptName(t.getDeptName());

                if (finalEmployeeMap.containsKey(t.getOrderManNo())) {
                    supplierOrderManVO.setMobile(finalEmployeeMap.get(t.getOrderManNo()).getMobile());
                }
                supplierOrderManVO.setOrderSpecialistName(CommonIfEnum.getNameByValue(t.getOrderSpecialist()));
                supplierOrderManVO.setIsDefaultName(CommonIfEnum.getNameByValue(t.getIsDefault()));

                salesManVoList.add(supplierOrderManVO);
            });
            supplierVO.setSupplierManList(salesManVoList);
        }

        //银行信息
        final Map<String, List<CompanyBankV2>> bankMap = (Map<String, List<CompanyBankV2>>) infoMap.get("bankMap");
        if (bankMap.containsKey(supplier.getCompanyNo())) {
            List<CompanyBankV2> bankList = bankMap.get(supplier.getCompanyNo());
            if (CollectionUtils.isNotEmpty(bankList)) {
                final List<BankType> bankTypeList = companyV2Service.getBankTypeList();
                final Map<String, BankType> bankTypeMap = bankTypeList.stream().collect(Collectors.toMap(BankType::getBankCode, Function.identity(), (v1, v2) -> v1));
                final Map<String, String> currencyMap = (Map<String, String>) infoMap.get("currencyMap");

                List<CompanyBankVO> companyBankVoList = new ArrayList<>();
                bankList.forEach(t -> {
                    CompanyBankVO companyBankVO = new CompanyBankVO();
                    BeanUtils.copyProperties(t, companyBankVO);

                    companyBankVO.setBankTypeName(bankTypeMap.getOrDefault(t.getBankType(), new BankType()).getBankName());
                    companyBankVO.setCurrencyName(currencyMap.getOrDefault(t.getCurrencyId(), ""));
                    companyBankVO.setAccountTypeName(BankAccountTypeEnum.getNameByValue(t.getAccountType()));
                    companyBankVO.setStatusName(CommonStatusEnum.getNameByValue(t.getStatus()));
                    companyBankVO.setIsDefaultName(CommonIfEnum.getNameByValue(t.getIsDefault()));

                    companyBankVoList.add(companyBankVO);
                });

                supplierVO.setBankList(companyBankVoList);
            }

        }

        //联系地址
        final List<CompanyShippingAddressV2> companyShippingAddressList = code2org2AddressList.getOrDefault(supplier.getSupplierCode(), Collections.emptyMap()).get(params.getUseOrgNo());
        if (CollectionUtils.isNotEmpty(companyShippingAddressList)) {
            List<CompanyShippingAddressVO> salesManVoList = new ArrayList<>();
            companyShippingAddressList.forEach(t -> {
                final List<String> regionCodeList = companyShippingAddressList.stream().map(CompanyShippingAddressV2::getRegionCode).filter(s -> !StringUtils.isEmpty(s)).collect(Collectors.toList());
                Map<String, AreaCodeVo> areaMap = new HashMap<>();
                if (CollectionUtils.isNotEmpty(regionCodeList)) {
                    List<AreaCodeVo> areaCodeList = dictEnterpriseService.findAreaByCodes(regionCodeList);
                    areaMap = areaCodeList.stream().collect(Collectors.toMap(AreaCodeVo::getAreaCode, Function.identity()));
                }
                Map<String, AreaCodeVo> finalAreaMap = areaMap;
                CompanyShippingAddressVO companyShippingAddressVO = new CompanyShippingAddressVO();
                BeanUtils.copyProperties(t, companyShippingAddressVO);

                if (finalAreaMap.containsKey(t.getRegionCode())) {
//                    companyShippingAddressVO.setRegionName(finalAreaMap.get(t.getRegionCode()).getAreaName());
                    companyShippingAddressVO.setRegionFullName(finalAreaMap.get(t.getRegionCode()).getAreaFullName());
                }
                companyShippingAddressVO.setAddressTypeName(AddressEnum.getByType(t.getAddressType()) != null ? AddressEnum.getByType(t.getAddressType()).getName() : "");
                companyShippingAddressVO.setIsDefaultName(CommonIfEnum.getNameByValue(t.getIsDefault()));

                salesManVoList.add(companyShippingAddressVO);
            });
            supplierVO.setLinkAddressList(salesManVoList);
        }

        //对应客户
//        final List<CustomerV2> customerList = customerV2Service.queryByCompanyNo(operationModel, supplier.getCompanyNo());
//        if (CollectionUtils.isNotEmpty(customerList)) {
//            supplierVO.setCustomerCode(customerList.stream().map(CustomerV2::getCustomerCode).collect(Collectors.joining(",")));
//            supplierVO.setCustomerName(customerList.stream().map(CustomerV2::getCustomerName).collect(Collectors.joining(",")));
//        }


        // 不是管理组织不展示
        if (params.getUseOrgNo().equals(supplier.getManageOrgNo())) {
            List<SupplierBase> supplierBases = supplierBaseDAO.selectList(Wrappers.<SupplierBase>lambdaQuery()
                    .eq(SupplierBase::getEnterpriseNo, operationModel.getEnterpriseNo())
                    .eq(SupplierBase::getSupplierCode, params.getSupplierCode())
                    .eq(SupplierBase::getDeleted, DeletedEnum.UN_DELETE.getValue()));

            List<OrganizationVo> assignrganizationVoList = organizationService.findListNoAuth(operationModel.getEnterpriseNo(), new ArrayList<>(supplierBases.stream().map(SupplierBase::getUseOrgNo).collect(Collectors.toSet())));
            Map<String, OrganizationVo> assignOrgNo2OrganizationVo = assignrganizationVoList.stream().collect(Collectors.toMap(OrganizationVo::getOrgNo, Function.identity()));

            if (CollectionUtils.isNotEmpty(supplierBases)) {
                List<SupplierAssignVO> supplierAssignVOList = new ArrayList<>();

                for (SupplierBase supplierBase : supplierBases) {
                    SupplierAssignVO supplierAssignVO = BeanUtil.copyFields(supplierBase, SupplierAssignVO.class);

                    if (assignOrgNo2OrganizationVo.containsKey(supplierBase.getManageOrgNo())) {
                        supplierAssignVO.setManageOrgName(assignOrgNo2OrganizationVo.get(supplierBase.getManageOrgNo()).getOrgName());
                    }

                    if (assignOrgNo2OrganizationVo.containsKey(supplierBase.getUseOrgNo())) {
                        supplierAssignVO.setUseOrgName(assignOrgNo2OrganizationVo.get(supplierBase.getUseOrgNo()).getOrgName());
                    }

                    supplierAssignVOList.add(supplierAssignVO);
                }

                supplierVO.setSupplierBaseList(supplierAssignVOList);
            }
        }

        return supplierVO;
    }

    @Override
    public SupplierV2 getSupplierByName(OperationModel operationModel, String supplierName) {
        return supplierV2DAO.selectOne(
                Wrappers.<SupplierV2>lambdaQuery()
                        .eq(SupplierV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                        .eq(SupplierV2::getSupplierName, supplierName)
                        .eq(SupplierV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
    }

    @Override
    public SupplierV2 getSupplierByCode(OperationModel operationModel, String supplierCode) {
        return supplierV2DAO.selectOne(
                Wrappers.<SupplierV2>lambdaQuery()
                        .eq(SupplierV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                        .eq(SupplierV2::getSupplierCode, supplierCode)
                        .eq(SupplierV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
    }

    private SupplierVO packBasicInfo(String enterprise, SupplierV2 supplier) {
        if (null == supplier) {
            return null;
        }

        //基本信息
        SupplierVO supplierVO = new SupplierVO();
        BeanUtils.copyProperties(supplier, supplierVO);


        Map<String, Object> infoMap = collectBasicInfo(enterprise, Collections.singletonList(supplier), null, Sets.newHashSet(
                "companyMap",
                "taxCategoryMap",
                "economicTypeMap",
                "orgMap",
                "categoryMap",
                "currencyMap",
                "transactionTypeMap",
                "countryRegionMap"
        ));

        //填充企业信息
        final CompanyV2 company = ((Map<String, CompanyV2>) infoMap.get("companyMap")).get(supplier.getCompanyNo());
        final Map<String, String> countryRegionMap = (Map<String, String>) infoMap.get("countryRegionMap");
        if (company != null) {
            supplierVO.setCompanyName(company.getCompanyName());
            supplierVO.setUnifiedSocialCode(company.getUnifiedSocialCode());
//            supplierVO.setCountry(company.getCountry());
            supplierVO.setCountryRegionId(company.getCountryRegionId());
            if (StringUtils.isNotEmpty(company.getCountryRegionId())) {
                supplierVO.setCountryRegionName(countryRegionMap.get(company.getCountryRegionId()));
            }
            supplierVO.setFactoryType(company.getFactoryType());
            supplierVO.setFactoryTypeName(FactoryTypeEnum.getByType(company.getFactoryType()) == null ? null : FactoryTypeEnum.getByType(company.getFactoryType()).getName());
            supplierVO.setEconomicType(company.getEconomicType());
//            supplierVO.setIsAssociatedEnterprise(company.getIsAssociatedEnterprise());
//            supplierVO.setAssociatedOrgNo(company.getAssociatedOrgNo());
//            supplierVO.setAssociatedOrgCode(company.getAssociatedOrgCode());
//            supplierVO.setAssociatedOrgName(company.getAssociatedOrgName());

            if (null != company.getRegionCode()) {
                List<AreaCodeVo> areaCodeList = dictEnterpriseService.findAreaByCodes(Collections.singletonList(company.getRegionCode()));
                final Map<String, AreaCodeVo> areaMap = areaCodeList.stream().collect(Collectors.toMap(AreaCodeVo::getAreaCode, Function.identity()));
                supplierVO.setRegionFullName(areaMap.getOrDefault(company.getRegionCode(), new AreaCodeVo()).getAreaFullName());
            }
            supplierVO.setAddress(company.getAddress());
            supplierVO.setIsMedicalInstitution(company.getIsMedicalInstitution());
            supplierVO.setIsMedicalInstitutionName(CommonIfEnum.getNameByValue(company.getIsMedicalInstitution()));
            supplierVO.setInstitutionalType(company.getInstitutionalType());
            supplierVO.setInstitutionalTypeName(InstitutionalTypeEnum.getByType(supplierVO.getInstitutionalType()) != null ? InstitutionalTypeEnum.getByType(supplierVO.getInstitutionalType()).getName() : "");
            supplierVO.setHospitalType(company.getHospitalType());
            supplierVO.setHospitalTypeName(HospitalTypeEnum.getByType(supplierVO.getHospitalType()) != null ? HospitalTypeEnum.getByType(supplierVO.getHospitalType()).getName() : "");
            supplierVO.setHospitalClass(company.getHospitalClass());
            supplierVO.setHospitalClassName(HospitalClassEnum.getByType(supplierVO.getHospitalClass()) != null ? HospitalClassEnum.getByType(supplierVO.getHospitalClass()).getName() : "");

            if (StringUtils.isNotBlank(company.getTaxCategory())) {
                final Map<String, CustomDocResponse> taxCategoryMap = (Map<String, CustomDocResponse>) infoMap.get("taxCategoryMap");
                supplierVO.setTaxCategory(company.getTaxCategory());
                supplierVO.setTaxCategoryName(taxCategoryMap.getOrDefault(company.getTaxCategory(), new CustomDocResponse()).getDocItemName());
            }
            if (StringUtils.isNotBlank(company.getEconomicType())) {
                final Map<String, CustomDocResponse> economicTypeMap = (Map<String, CustomDocResponse>) infoMap.get("economicTypeMap");
                supplierVO.setEconomicTypeName(economicTypeMap.getOrDefault(company.getEconomicType(), new CustomDocResponse()).getDocItemName());
            }
        }

        //填充编码字段对应的名称
        supplierVO.setRetailInvestorsName(CommonIfEnum.getNameByValue(supplier.getRetailInvestors()));

        supplierVO.setIsAssociatedEnterpriseName(CommonIfEnum.getNameByValue(supplier.getIsAssociatedEnterprise()));

        final Map<String, OrganizationVo> orgNo2OrganizationVo = (Map<String, OrganizationVo>) infoMap.get("orgMap");


        if (orgNo2OrganizationVo.containsKey(supplier.getManageOrgNo())) {
            supplierVO.setManageOrgName(orgNo2OrganizationVo.get(supplier.getManageOrgNo()).getOrgName());
        }

        //供应商分类
        if (StringUtils.isNotBlank(supplier.getSupplierCategoryNo())) {
            final SupplierCategoryV2 supplierCategory = ((Map<String, SupplierCategoryV2>) infoMap.get("categoryMap")).get(supplier.getSupplierCategoryNo());
            if (supplierCategory != null) {
                supplierVO.setSupplierCategoryName(supplierCategory.getCategoryName());
            }
        }

        //供应商类型
        if (StringUtils.isNotBlank(supplier.getTransactionType())) {
            final Map<String, TransactionTypeResponse> transactionTypeMap = (Map<String, TransactionTypeResponse>) infoMap.get("transactionTypeMap");
            supplierVO.setTransactionTypeName(transactionTypeMap.getOrDefault(supplier.getTransactionType(), new TransactionTypeResponse()).getTransactionTypeName());
        }

        return supplierVO;
    }

    @Override
    public SupplierVO getSupplierByCode(String enterpriseNo, String supplierCode) {
        SupplierV2 supplierV2 = supplierV2DAO.selectOne(
                Wrappers.<SupplierV2>lambdaQuery()
                        .eq(SupplierV2::getEnterpriseNo, enterpriseNo)
                        .eq(SupplierV2::getSupplierCode, supplierCode)
                        .eq(SupplierV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));

        return packBasicInfo(enterpriseNo, supplierV2);
    }

    @Override
    public SupplierVO getDetailSupplierByName(OperationModel operationModel, String useOrgNo, String supplierName) {
        SupplierV2 supplierV2 = supplierV2DAO.selectOne(
                Wrappers.<SupplierV2>lambdaQuery()
                        .eq(SupplierV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                        .eq(SupplierV2::getSupplierName, supplierName)
                        .eq(SupplierV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
        if (null == supplierV2) {
            return null;
        }

        SupplierGetReq params = new SupplierGetReq();
        params.setEnterpriseNo(operationModel.getEnterpriseNo());
        params.setUseOrgNo(useOrgNo);
        params.setSupplierCode(supplierV2.getSupplierCode());
        return packDetail(operationModel, params, supplierV2);
    }

    @Override
    public SupplierVO getDetailSupplierByCode(OperationModel operationModel, String useOrgNo, String supplierCode) {
        SupplierV2 supplierV2 = supplierV2DAO.selectOne(
                Wrappers.<SupplierV2>lambdaQuery()
                        .eq(SupplierV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                        .eq(SupplierV2::getSupplierCode, supplierCode)
                        .eq(SupplierV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
        if (null == supplierV2) {
            return null;
        }

        SupplierGetReq params = new SupplierGetReq();
        params.setEnterpriseNo(operationModel.getEnterpriseNo());
        params.setUseOrgNo(useOrgNo);
        params.setSupplierCode(supplierCode);
        return packDetail(operationModel, params, supplierV2);
    }

    @Override
    public Boolean hasAssignSupplier(OperationModel operationModel, SupplierBase supplierBase) {
        return supplierBaseDAO.exists(Wrappers.<SupplierBase>lambdaQuery()
                .eq(SupplierBase::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(SupplierBase::getSupplierCode, supplierBase.getSupplierCode())
                .eq(SupplierBase::getManageOrgNo, supplierBase.getManageOrgNo())
                .eq(SupplierBase::getUseOrgNo, supplierBase.getUseOrgNo())
                .eq(SupplierBase::getDeleted, DeletedEnum.UN_DELETE.getValue()));
    }

    @Override
    @Transactional
    public Boolean changeSupplierStatus(OperationModel operationModel, SupplierChangeStatusReq supplierChangeStatusReq) {
        // 校验必填参数
        ValidatorUtils.checkEmptyThrowEx(operationModel.getEnterpriseNo(), "租户编号不能为空");
        ValidatorUtils.checkEmptyThrowEx(supplierChangeStatusReq.getUseOrgNo(), "使用组织不能为空");
        ValidatorUtils.checkEmptyThrowEx(supplierChangeStatusReq.getSupplierCode(), "供应商编码不能为空");
        ValidatorUtils.checkEmptyThrowEx(supplierChangeStatusReq.getControlStatus(), "管控状态不能为空");

        SupplierV2 dbSupplierV2 = supplierV2DAO.selectOne(new LambdaQueryWrapper<SupplierV2>()
                .eq(SupplierV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(SupplierV2::getSupplierCode, supplierChangeStatusReq.getSupplierCode())
                .eq(SupplierV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
        ValidatorUtils.checkEmptyThrowEx(dbSupplierV2, "供应商不存在");

        SupplierBase supplierBase = supplierBaseDAO.selectOne(new LambdaQueryWrapper<SupplierBase>()
                .eq(SupplierBase::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(SupplierBase::getUseOrgNo, supplierChangeStatusReq.getUseOrgNo())
                .eq(SupplierBase::getSupplierCode, dbSupplierV2.getSupplierCode())
                .eq(SupplierBase::getDeleted, DeletedEnum.UN_DELETE.getValue()));
        ValidatorUtils.checkEmptyThrowEx(supplierBase, "供应商未分派");


        SupplierBase newSupplierBase = new SupplierBase();

        String op = null;

        // 启用
        if (ControlStatusEnum.ENABLE.getValue().toString().equals(supplierChangeStatusReq.getControlStatus())) {
            ValidatorUtils.checkTrueThrowEx(!ControlStatusEnum.DISABLE.getValue().toString().equals(supplierBase.getControlStatus()), "供应商需要为停用状态");

            // 管理组织 != 使用组织，判断管理组织的启停状态
            if (!supplierChangeStatusReq.getUseOrgNo().equals(dbSupplierV2.getManageOrgNo())) {
                SupplierBase manageSupplierBase = supplierBaseDAO.selectOne(new LambdaQueryWrapper<SupplierBase>()
                        .eq(SupplierBase::getEnterpriseNo, operationModel.getEnterpriseNo())
                        .eq(SupplierBase::getUseOrgNo, dbSupplierV2.getManageOrgNo())
                        .eq(SupplierBase::getSupplierCode, dbSupplierV2.getSupplierCode())
                        .eq(SupplierBase::getDeleted, DeletedEnum.UN_DELETE.getValue()));
                ValidatorUtils.checkEmptyThrowEx(manageSupplierBase, "管理组织的启停信息不存在");

                ValidatorUtils.checkTrueThrowEx(ControlStatusEnum.DISABLE.getValue().toString().equals(manageSupplierBase.getControlStatus()), "供应商已经被管理组织停用");
            }

            newSupplierBase.setControlStatus(ControlStatusEnum.ENABLE.getValue().toString());

            op = "启用供应商档案";
        } else { // 停用
            ValidatorUtils.checkTrueThrowEx(!ControlStatusEnum.ENABLE.getValue().toString().equals(supplierBase.getControlStatus()), "供应商需要为启用状态");

            newSupplierBase.setControlStatus(ControlStatusEnum.DISABLE.getValue().toString());

            op = "停用供应商档案";
        }

        supplierBaseDAO.update(newSupplierBase, Wrappers.<SupplierBase>lambdaUpdate()
                .eq(SupplierBase::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(SupplierBase::getUseOrgNo, supplierChangeStatusReq.getUseOrgNo())
                .eq(SupplierBase::getSupplierCode, supplierChangeStatusReq.getSupplierCode())
                .eq(SupplierBase::getDeleted, DeletedEnum.UN_DELETE.getValue()));


        final String logOp = op;

        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                try {
                    businessLogService.saveLog(DLogLevel.INFO.name(), ViewNameConstant.BDC_SUPPLIER_VIEW, supplierChangeStatusReq.getSupplierCode() + supplierChangeStatusReq.getUseOrgNo(), logOp, logOp, JSON.toJSONString(newSupplierBase), "");
                } catch (Exception e) {
                    log.error(logOp + "日志保存失败", e);
                }
            }
        });

        return true;
    }

    @Override
    public List<SupplierAssignVO> getAssignSupplier(OperationModel operationModel, SupplierGetAssignReq params) {
        SupplierV2 supplierV2 = supplierV2DAO.selectOne(Wrappers.<SupplierV2>lambdaQuery()
                .eq(SupplierV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(SupplierV2::getManageOrgNo, params.getUseOrgNo())
                .eq(SupplierV2::getSupplierCode, params.getSupplierCode())
                .eq(SupplierV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
        // 不是管理组织不展示
        if (supplierV2 == null) {
            return Collections.emptyList();
        }

        List<SupplierBase> supplierBases = supplierBaseDAO.selectList(Wrappers.<SupplierBase>lambdaQuery()
                .eq(SupplierBase::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(SupplierBase::getSupplierCode, params.getSupplierCode())
                .eq(SupplierBase::getDeleted, DeletedEnum.UN_DELETE.getValue()));

        if (CollectionUtils.isEmpty(supplierBases)) {
            return Collections.emptyList();
        }

        List<SupplierAssignVO> supplierAssignVOList = new ArrayList<>();

        List<OrganizationVo> organizationVoList = organizationService.findListNoAuth(operationModel.getEnterpriseNo(), new ArrayList<>(supplierBases.stream().flatMap(supplierBase -> Stream.of(supplierBase.getManageOrgNo(), supplierBase.getUseOrgNo())).collect(Collectors.toSet())));
        final Map<String, OrganizationVo> orgNo2OrganizationVo = organizationVoList.stream().collect(Collectors.toMap(OrganizationVo::getOrgNo, Function.identity()));


        for (SupplierBase supplierBase : supplierBases) {
            SupplierAssignVO supplierAssignVO = BeanUtil.copyFields(supplierBase, SupplierAssignVO.class);

            if (orgNo2OrganizationVo.containsKey(supplierBase.getManageOrgNo())) {
                supplierAssignVO.setManageOrgName(orgNo2OrganizationVo.get(supplierBase.getManageOrgNo()).getOrgName());
            }

            if (orgNo2OrganizationVo.containsKey(supplierBase.getUseOrgNo())) {
                supplierAssignVO.setUseOrgName(orgNo2OrganizationVo.get(supplierBase.getUseOrgNo()).getOrgName());
            }

            supplierAssignVOList.add(supplierAssignVO);
        }

        return supplierAssignVOList;
    }


    private List<com.yyigou.dsrp.cdc.api.v2.supplier.vo.SupplierPageVO> completeSupplementPageVO(String enterpriseNo, List<SupplierV2WithBiz> data) {
        List<com.yyigou.dsrp.cdc.api.v2.supplier.vo.SupplierPageVO> result = new ArrayList<>();

        Map<String, Object> infoMap = collectBizInfo(enterpriseNo, data,
                Sets.newHashSet(
                        "categoryMap",
                        "companyMap",
                        "taxCategoryMap",
                        "economicTypeMap",
                        "transactionTypeMap",
                        "orgMap",
                        "supplierBizMap",
                        "supplierGspMap",
                        "supplierBaseMap",
                        "countryRegionMap"
                ));
        final Map<String, SupplierCategoryV2> categoryMap = (Map<String, SupplierCategoryV2>) infoMap.get("categoryMap");
        final Map<String, CompanyV2> companyMap = (Map<String, CompanyV2>) infoMap.get("companyMap");
        final Map<String, CustomDocResponse> taxCategoryMap = (Map<String, CustomDocResponse>) infoMap.get("taxCategoryMap");
        final Map<String, CustomDocResponse> economicTypeMap = (Map<String, CustomDocResponse>) infoMap.get("economicTypeMap");
        final Map<String, TransactionTypeResponse> transactionTypeMap = (Map<String, TransactionTypeResponse>) infoMap.get("transactionTypeMap");
        final Map<String, OrganizationVo> orgNo2OrganizationVo = (Map<String, OrganizationVo>) infoMap.get("orgMap");
        final Map<Long, SupplierBiz> supplierBizMap = (Map<Long, SupplierBiz>) infoMap.get("supplierBizMap");
        final Map<String, Map<String, SupplierGspAudit>> supplierGspMap = (Map<String, Map<String, SupplierGspAudit>>) infoMap.get("supplierGspMap");
        final Map<String, Map<String, SupplierBase>> supplierBaseMap = (Map<String, Map<String, SupplierBase>>) infoMap.get("supplierBaseMap");
        final Map<String, String> countryRegionMap = (Map<String, String>) infoMap.get("countryRegionMap");

        data.forEach(supplierV2WithBiz -> {
            com.yyigou.dsrp.cdc.api.v2.supplier.vo.SupplierPageVO supplierPageVO = new com.yyigou.dsrp.cdc.api.v2.supplier.vo.SupplierPageVO();
            BeanUtils.copyProperties(supplierV2WithBiz, supplierPageVO);

            supplierPageVO.setTransactionTypeName(transactionTypeMap.getOrDefault(supplierV2WithBiz.getTransactionType(), new TransactionTypeResponse()).getTransactionTypeName());

            // 补充使用组织名称
            if (orgNo2OrganizationVo.containsKey(supplierV2WithBiz.getUseOrgNo())) {
                supplierPageVO.setUseOrgName(orgNo2OrganizationVo.get(supplierV2WithBiz.getUseOrgNo()).getOrgName());
            }

            // 补充管理组织名称
            if (orgNo2OrganizationVo.containsKey(supplierV2WithBiz.getManageOrgNo())) {
                supplierPageVO.setManageOrgName(orgNo2OrganizationVo.get(supplierV2WithBiz.getManageOrgNo()).getOrgName());
            }

            // 补充供应商业务信息
            if (supplierBizMap.containsKey(supplierV2WithBiz.getBizId())) {
                SupplierBiz supplierBiz = supplierBizMap.get(supplierV2WithBiz.getBizId());
                BeanUtils.copyProperties(supplierBiz, supplierPageVO);

                supplierPageVO.setOwnerCompany(supplierBiz.getOwnerCompany());

//                supplierVO.setControlStatus(supplierBiz.getControlStatus());
//                supplierVO.setControlStatusName(ControlStatusEnum.getByValue(Integer.valueOf(supplierBiz.getControlStatus())).getName());
            }

            // 补充业务首营状态
            if (supplierGspMap.containsKey(supplierV2WithBiz.getSupplierCode())) {
                Map<String, SupplierGspAudit> org2GspMap = supplierGspMap.get(supplierV2WithBiz.getSupplierCode());
                if (org2GspMap.containsKey(supplierV2WithBiz.getUseOrgNo())) {
                    SupplierGspAudit supplierGspAudit = org2GspMap.get(supplierV2WithBiz.getUseOrgNo());
                    if (null != supplierGspAudit.getGspAuditStatus()) {
                        supplierPageVO.setGspAuditStatus(supplierGspAudit.getGspAuditStatus());
                        supplierPageVO.setGspAuditStatusName(GspAuditStatusEnum.getByType(supplierGspAudit.getGspAuditStatus()) != null ? GspAuditStatusEnum.getByType(supplierGspAudit.getGspAuditStatus()).getName() : "");
                    }
                    supplierPageVO.setGspAuditResult(supplierGspAudit.getGspAuditResult());
                }
            }

            if (supplierBaseMap.containsKey(supplierV2WithBiz.getSupplierCode())) {
                Map<String, SupplierBase> org2BaseMap = supplierBaseMap.get(supplierV2WithBiz.getSupplierCode());
                if (org2BaseMap.containsKey(supplierV2WithBiz.getUseOrgNo())) {
                    SupplierBase supplierBase = org2BaseMap.get(supplierV2WithBiz.getUseOrgNo());
                    supplierPageVO.setControlStatus(supplierBase.getControlStatus());
                    supplierPageVO.setControlStatusName(ControlStatusEnum.getByValue(Integer.valueOf(supplierBase.getControlStatus())).getName());
                }
            }

            if (companyMap.containsKey(supplierV2WithBiz.getCompanyNo())) {
                final CompanyV2 company = companyMap.get(supplierV2WithBiz.getCompanyNo());
                supplierPageVO.setTaxCategoryName(taxCategoryMap.getOrDefault(company.getTaxCategory(), new CustomDocResponse()).getDocItemName());
                supplierPageVO.setEconomicTypeName(economicTypeMap.getOrDefault(company.getEconomicType(), new CustomDocResponse()).getDocItemName());
                supplierPageVO.setCompanyName(company.getCompanyName());
                supplierPageVO.setUnifiedSocialCode(company.getUnifiedSocialCode());
//                supplierVO.setCountry(company.getCountry());
                supplierPageVO.setCountryRegionId(company.getCountryRegionId());
                if (StringUtils.isNotEmpty(company.getCountryRegionId())) {
                    supplierPageVO.setCountryRegionName(countryRegionMap.get(company.getCountryRegionId()));
                }
                supplierPageVO.setFactoryType(company.getFactoryType());
                supplierPageVO.setFactoryTypeName(FactoryTypeEnum.getByType(company.getFactoryType()) == null ? null : FactoryTypeEnum.getByType(company.getFactoryType()).getName());
                supplierPageVO.setEconomicType(company.getEconomicType());
//                supplierVO.setIsAssociatedEnterprise(company.getIsAssociatedEnterprise());
//                supplierVO.setAssociatedOrgNo(company.getAssociatedOrgNo());
//                supplierVO.setAssociatedOrgCode(company.getAssociatedOrgCode());
//                supplierVO.setAssociatedOrgName(company.getAssociatedOrgName());
            }

            supplierPageVO.setSupplierCategoryName(categoryMap.getOrDefault(supplierV2WithBiz.getSupplierCategoryNo(), new SupplierCategoryV2()).getCategoryName());

            result.add(supplierPageVO);
        });
        return result;
    }

    private List<com.yyigou.dsrp.cdc.api.v2.supplier.vo.SupplierReversePageVO> completeSupplementReversePageVO(String enterpriseNo, String useOrgNo, List<SupplierV2WithBase> data) {
        List<com.yyigou.dsrp.cdc.api.v2.supplier.vo.SupplierReversePageVO> result = new ArrayList<>();

        Map<String, Object> infoMap = collectBasicInfo(enterpriseNo, data, Collections.singletonList(useOrgNo),
                Sets.newHashSet(
                        "categoryMap",
                        "companyMap",
                        "taxCategoryMap",
                        "economicTypeMap",
                        "transactionTypeMap",
                        "orgMap",
                        "countryRegionMap"
                ));
        final Map<String, SupplierCategoryV2> categoryMap = (Map<String, SupplierCategoryV2>) infoMap.get("categoryMap");
        final Map<String, CompanyV2> companyMap = (Map<String, CompanyV2>) infoMap.get("companyMap");
        final Map<String, CustomDocResponse> taxCategoryMap = (Map<String, CustomDocResponse>) infoMap.get("taxCategoryMap");
        final Map<String, CustomDocResponse> economicTypeMap = (Map<String, CustomDocResponse>) infoMap.get("economicTypeMap");
        final Map<String, TransactionTypeResponse> transactionTypeMap = (Map<String, TransactionTypeResponse>) infoMap.get("transactionTypeMap");
        final Map<String, OrganizationVo> orgNo2OrganizationVo = (Map<String, OrganizationVo>) infoMap.get("orgMap");
        final Map<String, String> countryRegionMap = (Map<String, String>) infoMap.get("countryRegionMap");

        data.forEach(supplier -> {
            com.yyigou.dsrp.cdc.api.v2.supplier.vo.SupplierReversePageVO supplierReversePageVO = new com.yyigou.dsrp.cdc.api.v2.supplier.vo.SupplierReversePageVO();
            BeanUtils.copyProperties(supplier, supplierReversePageVO);

            supplierReversePageVO.setAssigned(null != supplier.getBaseId() ? CommonIfEnum.YES.getValue() : CommonIfEnum.NO.getValue());

            supplierReversePageVO.setTransactionTypeName(transactionTypeMap.getOrDefault(supplier.getTransactionType(), new TransactionTypeResponse()).getTransactionTypeName());

            // 补充使用组织名称
            if (orgNo2OrganizationVo.containsKey(supplier.getUseOrgNo())) {
                supplierReversePageVO.setUseOrgName(orgNo2OrganizationVo.get(supplier.getUseOrgNo()).getOrgName());
            }

            // 补充管理组织名称
            if (orgNo2OrganizationVo.containsKey(supplier.getManageOrgNo())) {
                supplierReversePageVO.setManageOrgName(orgNo2OrganizationVo.get(supplier.getManageOrgNo()).getOrgName());
            }

            if (companyMap.containsKey(supplier.getCompanyNo())) {
                final CompanyV2 company = companyMap.get(supplier.getCompanyNo());
                supplierReversePageVO.setTaxCategory(company.getTaxCategory());
                supplierReversePageVO.setTaxCategoryName(taxCategoryMap.getOrDefault(company.getTaxCategory(), new CustomDocResponse()).getDocItemName());
                supplierReversePageVO.setEconomicType(company.getEconomicType());
                supplierReversePageVO.setEconomicTypeName(economicTypeMap.getOrDefault(company.getEconomicType(), new CustomDocResponse()).getDocItemName());
                supplierReversePageVO.setCompanyName(company.getCompanyName());
                supplierReversePageVO.setUnifiedSocialCode(company.getUnifiedSocialCode());
//                supplierVO.setCountry(company.getCountry());
                supplierReversePageVO.setCountryRegionId(company.getCountryRegionId());
                if (StringUtils.isNotEmpty(company.getCountryRegionId())) {
                    supplierReversePageVO.setCountryRegionName(countryRegionMap.get(company.getCountryRegionId()));
                }
                supplierReversePageVO.setFactoryType(company.getFactoryType());
                supplierReversePageVO.setFactoryTypeName(FactoryTypeEnum.getByType(company.getFactoryType()) == null ? null : FactoryTypeEnum.getByType(company.getFactoryType()).getName());
//                supplierVO.setIsAssociatedEnterprise(company.getIsAssociatedEnterprise());
//                supplierVO.setAssociatedOrgNo(company.getAssociatedOrgNo());
//                supplierVO.setAssociatedOrgCode(company.getAssociatedOrgCode());
//                supplierVO.setAssociatedOrgName(company.getAssociatedOrgName());
            }

            supplierReversePageVO.setSupplierCategoryName(categoryMap.getOrDefault(supplier.getSupplierCategoryNo(), new SupplierCategoryV2()).getCategoryName());

            result.add(supplierReversePageVO);
        });
        return result;
    }

    private List<com.yyigou.dsrp.cdc.api.v2.supplier.vo.SupplierPageVO> completeSupplementForTenantPageVO(String enterpriseNo, List<String> useOrgNoList, List<SupplierV2WithBase> data) {
        List<com.yyigou.dsrp.cdc.api.v2.supplier.vo.SupplierPageVO> result = new ArrayList<>();

        Map<String, Object> infoMap = collectBasicInfo(enterpriseNo, data, useOrgNoList,
                Sets.newHashSet(
                        "categoryMap",
                        "companyMap",
                        "taxCategoryMap",
                        "economicTypeMap",
                        "transactionTypeMap",
                        "orgMap",
                        "countryRegionMap"
                ));
        final Map<String, SupplierCategoryV2> categoryMap = (Map<String, SupplierCategoryV2>) infoMap.get("categoryMap");
        final Map<String, CompanyV2> companyMap = (Map<String, CompanyV2>) infoMap.get("companyMap");
        final Map<String, CustomDocResponse> taxCategoryMap = (Map<String, CustomDocResponse>) infoMap.get("taxCategoryMap");
        final Map<String, CustomDocResponse> economicTypeMap = (Map<String, CustomDocResponse>) infoMap.get("economicTypeMap");
        final Map<String, TransactionTypeResponse> transactionTypeMap = (Map<String, TransactionTypeResponse>) infoMap.get("transactionTypeMap");
        final Map<String, OrganizationVo> orgNo2OrganizationVo = (Map<String, OrganizationVo>) infoMap.get("orgMap");
        final Map<String, String> countryRegionMap = (Map<String, String>) infoMap.get("countryRegionMap");

        data.forEach(supplier -> {
            com.yyigou.dsrp.cdc.api.v2.supplier.vo.SupplierPageVO supplierPageVO = new com.yyigou.dsrp.cdc.api.v2.supplier.vo.SupplierPageVO();
            BeanUtils.copyProperties(supplier, supplierPageVO);

            supplierPageVO.setTransactionTypeName(transactionTypeMap.getOrDefault(supplier.getTransactionType(), new TransactionTypeResponse()).getTransactionTypeName());

            // 补充使用组织名称
            if (orgNo2OrganizationVo.containsKey(supplier.getUseOrgNo())) {
                supplierPageVO.setUseOrgName(orgNo2OrganizationVo.get(supplier.getUseOrgNo()).getOrgName());
            }

            // 补充管理组织名称
            if (orgNo2OrganizationVo.containsKey(supplier.getManageOrgNo())) {
                supplierPageVO.setManageOrgName(orgNo2OrganizationVo.get(supplier.getManageOrgNo()).getOrgName());
            }

            if (companyMap.containsKey(supplier.getCompanyNo())) {
                final CompanyV2 company = companyMap.get(supplier.getCompanyNo());
                supplierPageVO.setTaxCategory(company.getTaxCategory());
                supplierPageVO.setTaxCategoryName(taxCategoryMap.getOrDefault(company.getTaxCategory(), new CustomDocResponse()).getDocItemName());
                supplierPageVO.setEconomicType(company.getEconomicType());
                supplierPageVO.setEconomicTypeName(economicTypeMap.getOrDefault(company.getEconomicType(), new CustomDocResponse()).getDocItemName());
                supplierPageVO.setCompanyName(company.getCompanyName());
                supplierPageVO.setUnifiedSocialCode(company.getUnifiedSocialCode());
//                supplierVO.setCountry(company.getCountry());
                supplierPageVO.setCountryRegionId(company.getCountryRegionId());
                if (StringUtils.isNotEmpty(company.getCountryRegionId())) {
                    supplierPageVO.setCountryRegionName(countryRegionMap.get(company.getCountryRegionId()));
                }
                supplierPageVO.setFactoryType(company.getFactoryType());
                supplierPageVO.setFactoryTypeName(FactoryTypeEnum.getByType(company.getFactoryType()) == null ? null : FactoryTypeEnum.getByType(company.getFactoryType()).getName());
//                supplierVO.setIsAssociatedEnterprise(company.getIsAssociatedEnterprise());
//                supplierVO.setAssociatedOrgNo(company.getAssociatedOrgNo());
//                supplierVO.setAssociatedOrgCode(company.getAssociatedOrgCode());
//                supplierVO.setAssociatedOrgName(company.getAssociatedOrgName());
            }

            supplierPageVO.setSupplierCategoryName(categoryMap.getOrDefault(supplier.getSupplierCategoryNo(), new SupplierCategoryV2()).getCategoryName());

            result.add(supplierPageVO);
        });
        return result;
    }

    /**
     * 支持以下infos值：
     * - categoryMap：供应商分类信息
     * - companyMap：企业信息
     * - taxCategoryMap：税收分类信息
     * - economicTypeMap：经济类型信息
     * - cooperationMap：合作性质信息
     * - transactionTypeMap：供应商类型信息
     * - orgMap：组织信息
     * - supplierBizMap：供应商业务信息
     * - supplierGspMap：供应商首营信息
     * - supplierBaseMap：供应商分派信息
     * - addressMap：地址信息
     * - linkManMap：联系人信息
     * - orderManMap：负责人信息
     * - bankMap：银行信息
     * - currencyMap：币种信息
     * - settlementModesMap：结算方式信息
     * - paymentTermMap：付款条件信息
     * - countryRegionMap：国家地区信息
     *
     * @param enterpriseNo 企业编号
     * @param data         供应商数据列表
     * @param infos        需要获取的信息类型集合
     * @return 包含请求信息的Map
     */
    private Map<String, Object> collectBizInfo(String enterpriseNo, List<SupplierV2WithBiz> data, Set<String> infos) {
        Map<String, Object> result = new HashMap<>();

        // 供应商分类
        if (infos.contains("categoryMap")) {
            List<String> categoryNoList = data.stream().map(SupplierV2WithBiz::getSupplierCategoryNo).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
            final Map<String, SupplierCategoryV2> categoryMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(categoryNoList)) {
                List<SupplierCategoryV2> categoryList = supplierCategoryV2Service.getByNoList(enterpriseNo, categoryNoList);
                if (CollectionUtils.isNotEmpty(categoryList)) {
                    categoryMap.putAll(categoryList.stream().collect(Collectors.toMap(SupplierCategoryV2::getNo, Function.identity())));
                }
            }
            result.put("categoryMap", categoryMap);
        }

        // 企业信息
        if (infos.contains("companyMap")) {
            List<String> companyNoList = data.stream().map(SupplierV2WithBiz::getCompanyNo).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
            final Map<String, CompanyV2> companyMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(companyNoList)) {
                List<CompanyV2> companyList = companyV2Service.findByEnterpriseNoAndCompanyNoList(enterpriseNo, companyNoList);
                if (CollectionUtils.isNotEmpty(companyList)) {
                    companyMap.putAll(companyList.stream().collect(Collectors.toMap(CompanyV2::getCompanyNo, Function.identity())));
                }
            }
            result.put("companyMap", companyMap);
        }

        //税收分类
        if (infos.contains("taxCategoryMap")) {
            final Map<String, CustomDocResponse> taxCategoryMap = customDocService.getItemByCustomDocCode(enterpriseNo, SystemConstant.TAX_CATEGORY);
            result.put("taxCategoryMap", taxCategoryMap);
        }

        //经济类型
        if (infos.contains("economicTypeMap")) {
            final Map<String, CustomDocResponse> economicTypeMap = customDocService.getItemByCustomDocCode(enterpriseNo, SystemConstant.ECONOMIC_TYPE);
            result.put("economicTypeMap", economicTypeMap);
        }

        //合作性质
        if (infos.contains("cooperationMap")) {
            final Map<String, CustomDocResponse> cooperationMap = customDocService.getItemByCustomDocCode(enterpriseNo, SystemConstant.SUPPLIER_COOPERATION_NUMBER);
            result.put("cooperationMap", cooperationMap);
        }

        // 供应商类型
        if (infos.contains("transactionTypeMap")) {
            List<TransactionTypeResponse> transactionTypeList = transactionTypeService.getTransactionTypeList(enterpriseNo, ViewNameConstant.BDC_SUPPLIER_VIEW);
            final Map<String, TransactionTypeResponse> transactionTypeMap = transactionTypeList.stream().collect(Collectors.toMap(TransactionTypeResponse::getTransactionTypeCode, Function.identity()));
            result.put("transactionTypeMap", transactionTypeMap);
        }

        // 组织信息
        if (infos.contains("orgMap")) {
            List<OrganizationVo> organizationVoList = organizationService.findListNoAuth(enterpriseNo, new ArrayList<>(data.stream().flatMap(supplierV2WithBiz -> Stream.of(supplierV2WithBiz.getManageOrgNo(), supplierV2WithBiz.getUseOrgNo())).collect(Collectors.toSet())));
            final Map<String, OrganizationVo> orgNo2OrganizationVo = organizationVoList.stream().collect(Collectors.toMap(OrganizationVo::getOrgNo, Function.identity()));
            result.put("orgMap", orgNo2OrganizationVo);
        }

        // 供应商业务信息
        if (infos.contains("supplierBizMap")) {
            List<SupplierBiz> supplierBizList = supplierBizDAO.selectList(Wrappers.<SupplierBiz>lambdaQuery()
                    .eq(SupplierBiz::getEnterpriseNo, enterpriseNo)
                    .in(SupplierBiz::getId, data.stream().map(SupplierV2WithBiz::getBizId).collect(Collectors.toList())));
            final Map<Long, SupplierBiz> supplierBizMap = supplierBizList.stream().collect(Collectors.toMap(SupplierBiz::getId, Function.identity()));
            result.put("supplierBizMap", supplierBizMap);
        }

        // 供应商首营信息
        if (infos.contains("supplierGspMap")) {
            List<SupplierGspAudit> supplierGspList = supplierGspAuditDAO.selectList(Wrappers.<SupplierGspAudit>lambdaQuery()
                    .eq(SupplierGspAudit::getEnterpriseNo, enterpriseNo)
                    .in(SupplierGspAudit::getSupplierCode, data.stream().map(SupplierV2WithBiz::getSupplierCode).collect(Collectors.toList()))
                    .eq(SupplierGspAudit::getDeleted, DeletedEnum.UN_DELETE.getValue()));
            final Map<String, Map<String, SupplierGspAudit>> supplierGspMap = supplierGspList.stream().collect(Collectors.groupingBy(SupplierGspAudit::getSupplierCode, Collectors.toMap(SupplierGspAudit::getUseOrgNo, Function.identity())));
            result.put("supplierGspMap", supplierGspMap);
        }

        // 供应商分派信息
        if (infos.contains("supplierBaseMap")) {
            List<SupplierBase> supplierBaseList = supplierBaseDAO.selectList(Wrappers.<SupplierBase>lambdaQuery()
                    .eq(SupplierBase::getEnterpriseNo, enterpriseNo)
                    .in(SupplierBase::getSupplierCode, data.stream().map(SupplierV2WithBiz::getSupplierCode).collect(Collectors.toList()))
                    .eq(SupplierBase::getDeleted, DeletedEnum.UN_DELETE.getValue()));
            Map<String, Map<String, SupplierBase>> supplierBaseMap = supplierBaseList.stream().collect(Collectors.groupingBy(SupplierBase::getSupplierCode, Collectors.toMap(SupplierBase::getUseOrgNo, Function.identity())));
            result.put("supplierBaseMap", supplierBaseMap);
        }

        // 地址
        if (infos.contains("addressMap")) {
            List<CompanyShippingAddressV2> supplierAddressList = companyShippingAddressV2DAO.selectList(Wrappers.<CompanyShippingAddressV2>lambdaQuery()
                    .eq(CompanyShippingAddressV2::getEnterpriseNo, enterpriseNo)
                    .in(CompanyShippingAddressV2::getSourceNo, data.stream().map(SupplierV2WithBiz::getSupplierCode).collect(Collectors.toSet()))
                    .in(CompanyShippingAddressV2::getUseOrgNo, data.stream().map(SupplierV2WithBiz::getUseOrgNo).collect(Collectors.toSet()))
                    .eq(CompanyShippingAddressV2::getLinkaddType, LinkmanTypeEnum.SUPPLY.getValue())
                    .eq(CompanyShippingAddressV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
            final Map<String, Map<String, List<CompanyShippingAddressV2>>> code2org2AddressList = supplierAddressList.stream().collect(Collectors.groupingBy(CompanyShippingAddressV2::getSourceNo, Collectors.groupingBy(CompanyShippingAddressV2::getUseOrgNo)));
            result.put("addressMap", code2org2AddressList);
        }

        //联系人
        if (infos.contains("linkManMap")) {
            List<CompanyLinkmanV2> supplierLinkmanList = companyLinkmanV2DAO.selectList(Wrappers.<CompanyLinkmanV2>lambdaQuery()
                    .eq(CompanyLinkmanV2::getEnterpriseNo, enterpriseNo)
                    .in(CompanyLinkmanV2::getSourceNo, data.stream().map(SupplierV2WithBiz::getSupplierCode).collect(Collectors.toSet()))
                    .in(CompanyLinkmanV2::getUseOrgNo, data.stream().map(SupplierV2WithBiz::getUseOrgNo).collect(Collectors.toSet()))
                    .eq(CompanyLinkmanV2::getLinkmanType, LinkmanTypeEnum.SUPPLY.getValue())
                    .eq(CompanyLinkmanV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
            final Map<String, Map<String, List<CompanyLinkmanV2>>> code2org2LinkManList = supplierLinkmanList.stream().collect(Collectors.groupingBy(CompanyLinkmanV2::getSourceNo, Collectors.groupingBy(CompanyLinkmanV2::getUseOrgNo)));
            result.put("linkManMap", code2org2LinkManList);
        }

        // 获取银行信息
        if (infos.contains("bankMap")) {
            List<CompanyBankV2> bankList = companyV2Service.findBankList(enterpriseNo, new ArrayList<>(data.stream().map(SupplierV2WithBiz::getCompanyNo).collect(Collectors.toSet())));
            final Map<String, List<CompanyBankV2>> companyNo2BankList = bankList.stream().collect(Collectors.groupingBy(CompanyBankV2::getCompanyNo));
            result.put("bankMap", companyNo2BankList);
        }

        // 获取国家地区
        if (infos.contains("countryRegionMap")) {
            final Map<String, String> countryRegionMap = dictEnterpriseService.findPlatformCountryRegionList();
            result.put("countryRegionMap", countryRegionMap);
        }

        // 获取币种
        if (infos.contains("currencyMap")) {
            final Map<String, String> currencyMap = dictEnterpriseService.findCurrencyList(enterpriseNo);
            result.put("currencyMap", currencyMap);
        }

        // 获取结算方式
        if (infos.contains("settlementModesMap")) {
            final Map<String, String> settlementModesMap = dictEnterpriseService.getValMapByNumber(enterpriseNo, DictNumberEnum.DSRP_CUSTOMER_SETTLEMENT_MODES);
            result.put("settlementModesMap", settlementModesMap);
        }

        //付款条件
        if (infos.contains("paymentTermMap")) {
            final Map<String, CustomDocResponse> paymentTermMap = customDocService.getItemByCustomDocCode(enterpriseNo, SystemConstant.DSRP_CUSTOMER_PAYMENT_TERM);
            result.put("paymentTermMap", paymentTermMap);
        }

        // 获取负责人
        if (infos.contains("orderManMap")) {
            List<SupplierOrderManV2> supplierOrderManV2List = supplierOrderManV2DAO.selectList(Wrappers.<SupplierOrderManV2>lambdaQuery()
                    .eq(SupplierOrderManV2::getEnterpriseNo, enterpriseNo)
                    .in(SupplierOrderManV2::getSupplierCode, data.stream().map(SupplierV2WithBiz::getSupplierCode).collect(Collectors.toSet()))
                    .in(SupplierOrderManV2::getUseOrgNo, data.stream().map(SupplierV2WithBiz::getUseOrgNo).collect(Collectors.toSet()))
                    .eq(SupplierOrderManV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
            final Map<String, Map<String, List<SupplierOrderManV2>>> code2org2OrderManList = supplierOrderManV2List.stream().collect(Collectors.groupingBy(SupplierOrderManV2::getSupplierCode, Collectors.groupingBy(SupplierOrderManV2::getUseOrgNo)));
            result.put("orderManMap", code2org2OrderManList);
        }

        return result;
    }

    /**
     * 支持以下infos值：
     * - categoryMap：供应商分类信息
     * - companyMap：企业信息
     * - countryRegionMap：国家地区信息
     * - currencyMap：币种信息
     * - paymentTermMap：付款条件信息
     * - bankMap：银行信息
     * - taxCategoryMap：税收分类信息
     * - economicTypeMap：经济类型信息
     * - cooperationMap：合作性质信息
     * - settlementModesMap：结算方式信息
     * - transactionTypeMap：供应商类型信息
     * - orgMap：组织信息
     * - addressMap：地址信息
     * - linkManMap：联系人信息
     * - orderManMap：负责人信息
     *
     * @param enterpriseNo          企业编号
     * @param data                  供应商数据列表
     * @param potentialUseOrgNoList 使用组织编号列表
     * @param infos                 需要获取的信息类型集合
     * @return 包含请求信息的Map
     */
    private Map<String, Object> collectBasicInfo(String enterpriseNo, List<? extends SupplierV2> data, List<String> potentialUseOrgNoList, Set<String> infos) {
        Map<String, Object> result = new HashMap<>();

        List<String> allOrgNoList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(potentialUseOrgNoList)) {
            allOrgNoList.addAll(potentialUseOrgNoList);
        }
        allOrgNoList.addAll(data.stream().map(SupplierV2::getManageOrgNo).collect(Collectors.toSet()));

        // 供应商分类
        if (infos.contains("categoryMap")) {
            List<String> categoryNoList = data.stream().map(SupplierV2::getSupplierCategoryNo).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
            final Map<String, SupplierCategoryV2> categoryMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(categoryNoList)) {
                List<SupplierCategoryV2> categoryList = supplierCategoryV2Service.getByNoList(enterpriseNo, categoryNoList);
                if (CollectionUtils.isNotEmpty(categoryList)) {
                    categoryMap.putAll(categoryList.stream().collect(Collectors.toMap(SupplierCategoryV2::getNo, Function.identity())));
                }
            }
            result.put("categoryMap", categoryMap);
        }

        // 企业信息
        if (infos.contains("companyMap")) {
            List<String> companyNoList = data.stream().map(SupplierV2::getCompanyNo).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
            final Map<String, CompanyV2> companyMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(companyNoList)) {
                List<CompanyV2> companyList = companyV2Service.findByEnterpriseNoAndCompanyNoList(enterpriseNo, companyNoList);
                if (CollectionUtils.isNotEmpty(companyList)) {
                    companyMap.putAll(companyList.stream().collect(Collectors.toMap(CompanyV2::getCompanyNo, Function.identity())));
                }
            }
            result.put("companyMap", companyMap);
        }

        // 获取国家地区
        if (infos.contains("countryRegionMap")) {
            final Map<String, String> countryRegionMap = dictEnterpriseService.findPlatformCountryRegionList();
            result.put("countryRegionMap", countryRegionMap);
        }

        // 获取币种
        if (infos.contains("currencyMap")) {
            final Map<String, String> currencyMap = dictEnterpriseService.findCurrencyList(enterpriseNo);
            result.put("currencyMap", currencyMap);
        }

        //付款条件
        if (infos.contains("paymentTermMap")) {
            final Map<String, CustomDocResponse> paymentTermMap = customDocService.getItemByCustomDocCode(enterpriseNo, SystemConstant.DSRP_CUSTOMER_PAYMENT_TERM);
            result.put("paymentTermMap", paymentTermMap);
        }

        // 获取银行信息
        if (infos.contains("bankMap")) {
            List<CompanyBankV2> bankList = companyV2Service.findBankList(enterpriseNo, new ArrayList<>(data.stream().map(SupplierV2::getCompanyNo).collect(Collectors.toSet())));
            final Map<String, List<CompanyBankV2>> companyNo2BankList = bankList.stream().collect(Collectors.groupingBy(CompanyBankV2::getCompanyNo));
            result.put("bankMap", companyNo2BankList);
        }

        //税收分类
        if (infos.contains("taxCategoryMap")) {
            final Map<String, CustomDocResponse> taxCategoryMap = customDocService.getItemByCustomDocCode(enterpriseNo, SystemConstant.TAX_CATEGORY);
            result.put("taxCategoryMap", taxCategoryMap);
        }

        //经济类型
        if (infos.contains("economicTypeMap")) {
            final Map<String, CustomDocResponse> economicTypeMap = customDocService.getItemByCustomDocCode(enterpriseNo, SystemConstant.ECONOMIC_TYPE);
            result.put("economicTypeMap", economicTypeMap);
        }

        //合作性质
        if (infos.contains("cooperationMap")) {
            final Map<String, CustomDocResponse> cooperationMap = customDocService.getItemByCustomDocCode(enterpriseNo, SystemConstant.SUPPLIER_COOPERATION_NUMBER);
            result.put("cooperationMap", cooperationMap);
        }

        if (infos.contains("settlementModesMap")) {
            final Map<String, String> settlementModesMap = dictEnterpriseService.getValMapByNumber(enterpriseNo, DictNumberEnum.DSRP_CUSTOMER_SETTLEMENT_MODES);
            result.put("settlementModesMap", settlementModesMap);
        }

        // 供应商类型
        if (infos.contains("transactionTypeMap")) {
            List<TransactionTypeResponse> transactionTypeList = transactionTypeService.getTransactionTypeList(enterpriseNo, ViewNameConstant.BDC_SUPPLIER_VIEW);
            final Map<String, TransactionTypeResponse> transactionTypeMap = transactionTypeList.stream().collect(Collectors.toMap(TransactionTypeResponse::getTransactionTypeCode, Function.identity()));
            result.put("transactionTypeMap", transactionTypeMap);
        }

        if (infos.contains("orgMap")) {
            List<OrganizationVo> organizationVoList = organizationService.findListNoAuth(enterpriseNo, allOrgNoList);
            final Map<String, OrganizationVo> orgNo2OrganizationVo = organizationVoList.stream().collect(Collectors.toMap(OrganizationVo::getOrgNo, Function.identity()));
            result.put("orgMap", orgNo2OrganizationVo);
        }

        // 地址
        if (infos.contains("addressMap")) {
            List<CompanyShippingAddressV2> supplierAddressList = companyShippingAddressV2DAO.selectList(Wrappers.<CompanyShippingAddressV2>lambdaQuery()
                    .eq(CompanyShippingAddressV2::getEnterpriseNo, enterpriseNo)
                    .in(CompanyShippingAddressV2::getSourceNo, data.stream().map(SupplierV2::getSupplierCode).collect(Collectors.toSet()))
                    .in(CompanyShippingAddressV2::getUseOrgNo, allOrgNoList)
                    .eq(CompanyShippingAddressV2::getLinkaddType, LinkmanTypeEnum.SUPPLY.getValue())
                    .eq(CompanyShippingAddressV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
            final Map<String, Map<String, List<CompanyShippingAddressV2>>> code2org2AddressList = supplierAddressList.stream().collect(Collectors.groupingBy(CompanyShippingAddressV2::getSourceNo, Collectors.groupingBy(CompanyShippingAddressV2::getUseOrgNo)));

            result.put("addressMap", code2org2AddressList);
        }

        //联系人
        if (infos.contains("linkManMap")) {
            List<CompanyLinkmanV2> supplierLinkmanList = companyLinkmanV2DAO.selectList(Wrappers.<CompanyLinkmanV2>lambdaQuery()
                    .eq(CompanyLinkmanV2::getEnterpriseNo, enterpriseNo)
                    .in(CompanyLinkmanV2::getSourceNo, data.stream().map(SupplierV2::getSupplierCode).collect(Collectors.toSet()))
                    .in(CompanyLinkmanV2::getUseOrgNo, allOrgNoList)
                    .eq(CompanyLinkmanV2::getLinkmanType, LinkmanTypeEnum.SUPPLY.getValue())
                    .eq(CompanyLinkmanV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
            final Map<String, Map<String, List<CompanyLinkmanV2>>> code2org2LinkManList = supplierLinkmanList.stream().collect(Collectors.groupingBy(CompanyLinkmanV2::getSourceNo, Collectors.groupingBy(CompanyLinkmanV2::getUseOrgNo)));

            result.put("linkManMap", code2org2LinkManList);
        }

        // 获取负责人
        if (infos.contains("orderManMap")) {
            List<SupplierOrderManV2> supplierOrderManV2List = supplierOrderManV2DAO.selectList(Wrappers.<SupplierOrderManV2>lambdaQuery()
                    .eq(SupplierOrderManV2::getEnterpriseNo, enterpriseNo)
                    .in(SupplierOrderManV2::getSupplierCode, data.stream().map(SupplierV2::getSupplierCode).collect(Collectors.toSet()))
                    .in(SupplierOrderManV2::getUseOrgNo, allOrgNoList)
                    .eq(SupplierOrderManV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
            final Map<String, Map<String, List<SupplierOrderManV2>>> code2org2OrderManList = supplierOrderManV2List.stream().collect(Collectors.groupingBy(SupplierOrderManV2::getSupplierCode, Collectors.groupingBy(SupplierOrderManV2::getUseOrgNo)));
            result.put("orderManMap", code2org2OrderManList);
        }

        return result;
    }

    private void fillAddressToSupplier(SupplierFormalPageVO supplierVo, List<CompanyShippingAddressV2> companyShippingAddressV2List) {
        List<CompanyShippingAddressFormalPageVO> companyShippingAddressFormalPageVOList = new ArrayList<>();
        for (CompanyShippingAddressV2 companyShippingAddressV2 : companyShippingAddressV2List) {
            CompanyShippingAddressFormalPageVO companyShippingAddressFormalPageVO = new CompanyShippingAddressFormalPageVO();

            companyShippingAddressFormalPageVO.setId(companyShippingAddressV2.getId());
            companyShippingAddressFormalPageVO.setEnterpriseNo(companyShippingAddressV2.getEnterpriseNo());
            companyShippingAddressFormalPageVO.setCompanyNo(companyShippingAddressV2.getCompanyNo());
//            companyShippingAddressFormalPageVO.setCustomerCode();
//            companyShippingAddressFormalPageVO.setCustomerName();
            companyShippingAddressFormalPageVO.setReceiveUser(companyShippingAddressV2.getReceiveUser());
            companyShippingAddressFormalPageVO.setReceivePhone(companyShippingAddressV2.getReceivePhone());
            companyShippingAddressFormalPageVO.setRegionCode(companyShippingAddressV2.getRegionCode());
            companyShippingAddressFormalPageVO.setRegionName(companyShippingAddressV2.getRegionName());
            companyShippingAddressFormalPageVO.setReceiveAddr(companyShippingAddressV2.getReceiveAddr());
            companyShippingAddressFormalPageVO.setEmail(companyShippingAddressV2.getEmail());
            if (null != companyShippingAddressV2.getIsDefault()) {
                companyShippingAddressFormalPageVO.setIsDefault(companyShippingAddressV2.getIsDefault().byteValue());
            }
            companyShippingAddressFormalPageVO.setStatus(companyShippingAddressV2.getStatus());
            companyShippingAddressFormalPageVO.setAddressType(companyShippingAddressV2.getAddressType());
            companyShippingAddressFormalPageVO.setCreateNo(companyShippingAddressV2.getCreateNo());
            companyShippingAddressFormalPageVO.setCreateTime(companyShippingAddressV2.getCreateTime());
            companyShippingAddressFormalPageVO.setOperateNo(companyShippingAddressV2.getOperateNo());
            companyShippingAddressFormalPageVO.setOperateName(companyShippingAddressV2.getOperateName());
            companyShippingAddressFormalPageVO.setOperateTime(companyShippingAddressV2.getOperateTime());
            companyShippingAddressFormalPageVO.setOpTimestamp(companyShippingAddressV2.getOpTimestamp());
            companyShippingAddressFormalPageVO.setOpRevsion(companyShippingAddressV2.getOpRevsion());
            companyShippingAddressFormalPageVO.setOpType(companyShippingAddressV2.getOpType());
            companyShippingAddressFormalPageVO.setLinkaddType(companyShippingAddressV2.getLinkaddType());
            companyShippingAddressFormalPageVO.setTcSyncFlag(companyShippingAddressV2.getTcSyncFlag());
            companyShippingAddressFormalPageVO.setTcSyncResult(companyShippingAddressV2.getTcSyncResult());
//            companyShippingAddressFormalPageVO.setProvince();
//            companyShippingAddressFormalPageVO.setCity();
//            companyShippingAddressFormalPageVO.setArea();

            companyShippingAddressFormalPageVOList.add(companyShippingAddressFormalPageVO);
        }

        supplierVo.setCompanyShippingAddressVoList(companyShippingAddressFormalPageVOList);
    }

    private void fillCompanyToSupplier(SupplierFormalPageVO supplierVo, CompanyV2 company, Map<String, CustomDocResponse> taxCategoryMap) {
        supplierVo.setCompanyName(company.getCompanyName());
        supplierVo.setUnifiedSocialCode(company.getUnifiedSocialCode());
        supplierVo.setRegistedCapital(company.getRegistedCapital());
        supplierVo.setEstablishmentDate(company.getEstablishmentDate());
        supplierVo.setRegionCode(company.getRegionCode());
        supplierVo.setRegionName(company.getRegionName());
        supplierVo.setAddress(company.getAddress());
        supplierVo.setLegalPerson(company.getLegalPerson());
        supplierVo.setEmail(company.getEmail());
        supplierVo.setWebSite(company.getWebSite());
        supplierVo.setFax(company.getFax());
//        supplierVo.setLinkMan(company.getLinkMan());
//        supplierVo.setLinkPhone(company.getLinkPhone());
//        supplierVo.setLinkManPosition(company.getLinkManPosition());
        supplierVo.setTaxCategoryName(taxCategoryMap.containsKey(company.getTaxCategory()) ? taxCategoryMap.get(company.getTaxCategory()).getDocItemName() : company.getTaxCategoryName());
//        supplierVo.setTaxRate(company.getTaxRate());
        supplierVo.setTaxCategory(company.getTaxCategory());
//        supplierVo.setOwnerShip(company.getOwnerShip());
//        supplierVo.setOwnerShipName(company.getOwnerShipName());
        supplierVo.setIsListed(company.getIsListed());
        supplierVo.setBusinessStartTime(company.getBusinessStartTime());
        supplierVo.setBusinessEndTime(company.getBusinessEndTime());
        supplierVo.setBusinessLongTerm(company.getBusinessLongTerm());
        supplierVo.setPaidCapital(company.getPaidCapital());
        supplierVo.setIndustry(company.getIndustry());
        supplierVo.setBusinessRegistNo(company.getBusinessRegistNo());
        supplierVo.setOrganizationNo(company.getOrganizationNo());
        supplierVo.setTaxpayerNo(company.getTaxpayerNo());
        supplierVo.setTaxpayerQualification(company.getTaxpayerQualification());
        supplierVo.setRegistrationAuthority(company.getRegistrationAuthority());
        supplierVo.setApprovalDate(company.getApprovalDate());
        supplierVo.setLastName(company.getLastName());
        supplierVo.setInsuredNumber(company.getInsuredNumber());
        supplierVo.setCompanyBusinessType(company.getCompanyBusinessType());
        supplierVo.setBusinessStatus(company.getBusinessStatus());
        // 覆盖经营范围
        supplierVo.setManageScope(company.getManageScope());
        supplierVo.setFactoryType(company.getFactoryType());
//        supplierVo.setIsAssociatedEnterprise(company.getIsAssociatedEnterprise());
//        supplierVo.setIsAssociatedEnterpriseName(null != company.getIsAssociatedEnterprise() && company.getIsAssociatedEnterprise() == 1 ? "是" : "否");
        if (!org.apache.axis.utils.StringUtils.isEmpty(company.getPartnership())) {
            try {
                List<CompanyPartnershipEnum> partnershipEnumList = new ArrayList<>();
                List<String> partnershipList = JSON.parseArray(company.getPartnership(), String.class);
                for (String partnership : partnershipList) {
                    partnershipEnumList.add(CompanyPartnershipEnum.getByType(partnership));
                }
                supplierVo.setPartnershipText(String.join(",", partnershipEnumList.stream().map(n -> n.getName()).collect(Collectors.toList())));
            } catch (Exception e) {
                log.error("合作关系解析类型失败,失败原因:{}", JSON.toJSONString(e));
            }
        }
    }

    private List<com.yyigou.dsrp.cdc.api.v2.supplier.vo.SupplierFormalPageVO> completeSupplementFormalPageVO(String enterpriseNo, List<SupplierV2WithBiz> data) {
        List<com.yyigou.dsrp.cdc.api.v2.supplier.vo.SupplierFormalPageVO> result = new ArrayList<>();

        Map<String, Object> infoMap = collectBizInfo(enterpriseNo, data,
                Sets.newHashSet(
                        "categoryMap",
                        "companyMap",
                        "taxCategoryMap",
//                        "economicTypeMap",
                        "cooperationMap",
//                        "transactionTypeMap",
//                        "orgMap",
                        "supplierBizMap",
                        "addressMap",
                        "linkManMap"
                ));
        final Map<String, SupplierCategoryV2> categoryMap = (Map<String, SupplierCategoryV2>) infoMap.get("categoryMap");
        final Map<String, CompanyV2> companyMap = (Map<String, CompanyV2>) infoMap.get("companyMap");
        final Map<String, CustomDocResponse> taxCategoryMap = (Map<String, CustomDocResponse>) infoMap.get("taxCategoryMap");
//        final Map<String, CustomDocResponse> economicTypeMap = (Map<String, CustomDocResponse>) infoMap.get("economicTypeMap");
        final Map<String, CustomDocResponse> cooperationMap = (Map<String, CustomDocResponse>) infoMap.get("cooperationMap");
//        final Map<String, TransactionTypeResponse> transactionTypeMap = (Map<String, TransactionTypeResponse>) infoMap.get("transactionTypeMap");
//        final Map<String, OrganizationVo> orgNo2OrganizationVo = (Map<String, OrganizationVo>) infoMap.get("orgMap");
        final Map<Long, SupplierBiz> supplierBizMap = (Map<Long, SupplierBiz>) infoMap.get("supplierBizMap");
        final Map<String, List<CompanyShippingAddressV2>> addressMap = (Map<String, List<CompanyShippingAddressV2>>) infoMap.get("addressMap");
        final Map<String, List<CompanyLinkmanV2>> linkManMap = (Map<String, List<CompanyLinkmanV2>>) infoMap.get("linkManMap");

        data.forEach(supplier -> {
            com.yyigou.dsrp.cdc.api.v2.supplier.vo.SupplierFormalPageVO supplierVO = new com.yyigou.dsrp.cdc.api.v2.supplier.vo.SupplierFormalPageVO();
            BeanUtils.copyProperties(supplier, supplierVO);

            // 补充供应商业务信息
            if (supplierBizMap.containsKey(supplier.getBizId())) {
                SupplierBiz supplierBiz = supplierBizMap.get(supplier.getBizId());
                BeanUtils.copyProperties(supplierBiz, supplierVO);

                supplierVO.setCooperationModeName(cooperationMap.getOrDefault(supplierBiz.getCooperationMode(), new CustomDocResponse()).getDocItemName());
            }

            if (companyMap.containsKey(supplier.getCompanyNo())) {
                fillCompanyToSupplier(supplierVO, companyMap.get(supplier.getCompanyNo()), taxCategoryMap);
            }

            supplierVO.setIsAssociatedEnterpriseName(null != supplier.getIsAssociatedEnterprise() && supplier.getIsAssociatedEnterprise().equals(CommonIfEnum.YES.getValue()) ? "是" : "否");

            supplierVO.setSupplierCategoryName(categoryMap.getOrDefault(supplier.getSupplierCategoryNo(), new SupplierCategoryV2()).getCategoryName());

            if (addressMap.containsKey(supplier.getSupplierCode())) {
                fillAddressToSupplier(supplierVO, addressMap.get(supplier.getSupplierCode()));
            }

            if (linkManMap.containsKey(supplier.getSupplierCode())) {
                linkManMap.get(supplier.getSupplierCode()).stream().filter(companyLinkmanV2 -> companyLinkmanV2.getIsDefault().equals(CommonIfEnum.YES.getValue())).findFirst().ifPresent(companyLinkmanV2 -> {
                    supplierVO.setLinkMan(companyLinkmanV2.getLinkman());
                    supplierVO.setLinkPhone(companyLinkmanV2.getMobilePhone());
                    supplierVO.setLinkManPosition(companyLinkmanV2.getPosition());
                });
            } else {
                supplierVO.setLinkMan(null);
                supplierVO.setLinkPhone(null);
            }


            //不兼容老接口
//            supplierVo.setIsSupplierVersion("true");
//            supplierVo.setGspAuditStatus("");
//            supplierVo.setApplyformBillType("");
//            supplierVo.setCustomerNo("");
//            supplierVo.setIsSQAuditing(0);
//            supplierVo.setHasHistory("0");
//            supplierVo.setIsAccountManagerName(supplierVo.getIsAccountManager() == 1 ? "是" : "否");

            result.add(supplierVO);
        });
        return result;
    }

    /**
     * @description: 查询待处理供应商数量
     * @author: baoww
     * @date: 2025/3/4 17:35
     * @Param operationModel:
     * @return: java.lang.Long
     */
    @Override
    public Long getPendingCount(OperationModel operationModel) {
        return supplierV2DAO.getPendingCount(operationModel.getEnterpriseNo(), SupplierBusinessFlagEnum.DRAFT.getValue(), DeletedEnum.UN_DELETE.getValue());
    }

    @Override
    public Boolean checkOnlyCode(OperationModel operationModel, String no, String code) {
        String realNo = null;
        if (StringUtils.isNotEmpty(no)) {
            SupplierBase useBase = supplierBaseDAO.selectOne(Wrappers.<SupplierBase>lambdaQuery()
                    .eq(SupplierBase::getEnterpriseNo, operationModel.getEnterpriseNo())
                    .eq(SupplierBase::getSupplierNo, no)
                    .eq(SupplierBase::getDeleted, DeletedEnum.UN_DELETE.getValue()));
            if (null != useBase) {
                if (useBase.getManageOrgNo().equals(useBase.getUseOrgNo())) {
                    realNo = useBase.getSupplierNo();
                } else {
                    SupplierBase manageBase = supplierBaseDAO.selectOne(Wrappers.<SupplierBase>lambdaQuery()
                            .eq(SupplierBase::getEnterpriseNo, operationModel.getEnterpriseNo())
                            .eq(SupplierBase::getSupplierCode, useBase.getSupplierCode())
                            .apply(" manage_org_no = use_org_no ")
                            .eq(SupplierBase::getDeleted, DeletedEnum.UN_DELETE.getValue()));
                    if (null != manageBase) {
                        realNo = manageBase.getSupplierNo();
                    }
                }
            }
        }

        return supplierV2DAO.selectCount(new LambdaQueryWrapper<SupplierV2>()
                .eq(SupplierV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(SupplierV2::getSupplierCode, code)
                .eq(SupplierV2::getDeleted, DeletedEnum.UN_DELETE.getValue())
                .ne(StringUtils.isNotEmpty(realNo), SupplierV2::getSupplierNo, realNo)) <= 0;
    }

    @Override
    public Boolean checkOnlyName(OperationModel operationModel, String no, String name) {
        String realNo = null;
        if (StringUtils.isNotEmpty(no)) {
            SupplierBase useBase = supplierBaseDAO.selectOne(Wrappers.<SupplierBase>lambdaQuery()
                    .eq(SupplierBase::getEnterpriseNo, operationModel.getEnterpriseNo())
                    .eq(SupplierBase::getSupplierNo, no)
                    .eq(SupplierBase::getDeleted, DeletedEnum.UN_DELETE.getValue()));
            if (null != useBase) {
                if (useBase.getManageOrgNo().equals(useBase.getUseOrgNo())) {
                    realNo = useBase.getSupplierNo();
                } else {
                    SupplierBase manageBase = supplierBaseDAO.selectOne(Wrappers.<SupplierBase>lambdaQuery()
                            .eq(SupplierBase::getEnterpriseNo, operationModel.getEnterpriseNo())
                            .eq(SupplierBase::getSupplierCode, useBase.getSupplierCode())
                            .apply(" manage_org_no = use_org_no ")
                            .eq(SupplierBase::getDeleted, DeletedEnum.UN_DELETE.getValue()));
                    if (null != manageBase) {
                        realNo = manageBase.getSupplierNo();
                    }
                }
            }
        }

        return supplierV2DAO.selectCount(new LambdaQueryWrapper<SupplierV2>()
                .eq(SupplierV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(SupplierV2::getSupplierName, name)
                .eq(SupplierV2::getDeleted, DeletedEnum.UN_DELETE.getValue())
                .ne(StringUtils.isNotEmpty(realNo), SupplierV2::getSupplierNo, realNo)) <= 0;
    }

    @Override
    public Boolean checkQuoteCategory(OperationModel operationModel, String supplierCategoryNo) {
        return supplierV2DAO.exists(Wrappers.<SupplierV2>lambdaQuery()
                .eq(SupplierV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(SupplierV2::getSupplierCategoryNo, supplierCategoryNo)
                .eq(SupplierV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
    }

    @Override
    public List<SupplierV2> queryByManageOrgNoList(OperationModel operationModel, String supplierCategoryNo, List<String> manageOrgNoList) {
        return supplierV2DAO.selectList(Wrappers.<SupplierV2>lambdaQuery()
                .eq(SupplierV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(SupplierV2::getSupplierCategoryNo, supplierCategoryNo)
                .in(SupplierV2::getManageOrgNo, manageOrgNoList)
                .eq(SupplierV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
    }

    @Override
    public List<SupplierV2> queryByCompanyNo(String enterpriseNo, String companyNo) {
        return supplierV2DAO.selectList(Wrappers.lambdaQuery(SupplierV2.class)
                .eq(SupplierV2::getEnterpriseNo, enterpriseNo)
                .eq(SupplierV2::getCompanyNo, companyNo)
                .eq(SupplierV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
    }

    @Override
    public List<SupplierBasicResponse> queryStandardInfo(SupplierStandardQueryReq supplierStandardQueryReq) {
        ValidatorUtils.checkEmptyThrowEx(supplierStandardQueryReq, "参数不能为空");
        ValidatorUtils.checkEmptyThrowEx(supplierStandardQueryReq.getEnterpriseNo(), "企业编号不能为空");

        return convert2SupplierBasicResponse(supplierV2DAO.queryStandardInfo(supplierStandardQueryReq));
    }

    @Override
    public List<SupplierBizResponse> queryWithBizInfo(SupplierStandardQueryReq supplierStandardQueryReq) {
        ValidatorUtils.checkEmptyThrowEx(supplierStandardQueryReq, "参数不能为空");
        ValidatorUtils.checkEmptyThrowEx(supplierStandardQueryReq.getEnterpriseNo(), "企业编号不能为空");

        return convert2SupplierWithBizInfoResponse(supplierV2DAO.queryWithBizInfo(supplierStandardQueryReq));
    }

    @Override
    public List<SupplierBizResponse> selectSupplierBizByCodeOrgPair(SupplierCodeOrgPairListReq supplierCodeOrgPairListReq) {
        ValidatorUtils.checkEmptyThrowEx(supplierCodeOrgPairListReq, "参数不能为空");
        ValidatorUtils.checkEmptyThrowEx(supplierCodeOrgPairListReq.getEnterpriseNo(), "企业编号不能为空");

        return convert2SupplierWithBizInfoResponse(supplierV2DAO.selectSupplierBizByCodeOrgPair(supplierCodeOrgPairListReq));
    }

    @Override
    public PageVo<SupplierBizResponse> queryPageWithBizInfo(SupplierStandardQueryReq supplierStandardQueryReq, PageDto pageDTO) {
        Page<SupplierV2WithBiz> page = PageHelper.startPage(pageDTO.getPageIndex(), pageDTO.getPageSize(), StringUtils.isBlank(pageDTO.getOrderBy()) ? "b.create_time desc" : pageDTO.getOrderBy());
        supplierV2DAO.queryWithBizInfo(supplierStandardQueryReq);

        return PageUtils.convertPageVo(page, this::convert2SupplierWithBizInfoResponse);
    }

    @Override
    @Transactional
    public SupplierCoordinationCreateResponse createCoordinationSupplier(String enterpriseNo, String orgNo, String supplierName, String unifiedSocialCode, String omsSupplierNo) {
        ValidatorUtils.checkEmptyThrowEx(enterpriseNo, "企业编号不能为空");
        ValidatorUtils.checkEmptyThrowEx(orgNo, "组织编号不能为空");
        ValidatorUtils.checkEmptyThrowEx(supplierName, "供应商名称不能为空");
        ValidatorUtils.checkEmptyThrowEx(unifiedSocialCode, "统一社会信用代码不能为空");
        ValidatorUtils.checkTrueThrowEx(SystemConstant.DEFAULT_UNIFIED_SOCIAL_CODE.equals(unifiedSocialCode), "无效的社会信用代码");

        String authedType = null;
        String createOrgNo = null;

        GradeCheckMgrOrgRes gradeCheckMgrOrgRes = gradeControlService.checkOrgNoAndListMgrOrgNos(enterpriseNo, BillNameConstant.BDC_SUPPLIER_BILL, ViewNameConstant.BDC_SUPPLIER_VIEW, orgNo);
        if (gradeCheckMgrOrgRes.getMgrPrevEnabled()) {
            authedType = "manage";
            createOrgNo = orgNo;
        } else if (CollectionUtils.isNotEmpty(gradeCheckMgrOrgRes.getMgrOrgNos())) {
            authedType = "use";
            createOrgNo = gradeCheckMgrOrgRes.getMgrOrgNos().get(0);
        }

        if (null == authedType) {
            throw new BusinessException(com.yyigou.ddc.common.error.ErrorCode.param_invalid_code, "无档案管理权");
        }

        supplierName = FormatNameUtils.formatName(supplierName, Boolean.TRUE);
        log.warn("创建SCS协同供应商名称转换后结果{}", supplierName);

        SupplierV2 supplier = supplierV2DAO.selectOne(new LambdaQueryWrapper<SupplierV2>()
                .eq(SupplierV2::getEnterpriseNo, enterpriseNo)
                .eq(SupplierV2::getSupplierName, supplierName)
                .eq(SupplierV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
        if (null != supplier) {
            log.warn("当前租户内已存在供应商{},不做创建处理", JSON.toJSONString(supplier));
            if (!StringUtils.isEmpty(omsSupplierNo)) {
                SupplierV2 newSupplier = new SupplierV2();
                newSupplier.setOmsSupplierNo(omsSupplierNo);
                supplierV2DAO.update(newSupplier, new LambdaQueryWrapper<SupplierV2>()
                        .eq(SupplierV2::getSupplierCode, supplier.getSupplierCode()));
            }

            if ("use".equals(authedType)) {
                // 需要同步分派，否则拿不到使用组织分派后的no
                SupplierAssignExeRequest req = new SupplierAssignExeRequest();
                req.setEnterpriseNo(enterpriseNo);
                req.setManageOrgNo(createOrgNo);
                SupplierAssignDocExeRequest supplierAssignDocExeRequest = new SupplierAssignDocExeRequest();
                supplierAssignDocExeRequest.setSupplierCode(supplier.getSupplierCode());
                supplierAssignDocExeRequest.setUseOrgNoList(Collections.singletonList(orgNo));
                req.setDocList(Collections.singletonList(supplierAssignDocExeRequest));
                assignSupplier(req);


                final String finalCreateOrgNo = createOrgNo;
                TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                    @Override
                    public void afterCommit() {
                        try {
                            log.warn("updateDocHandlerReq={},{},{},{},{}", enterpriseNo, BillNameConstant.BDC_SUPPLIER_BILL, supplier.getSupplierCode(), finalCreateOrgNo, 0);

                            Boolean updateCallResult = gradeControlService.updateDocHandler(enterpriseNo, BillNameConstant.BDC_SUPPLIER_BILL, supplier.getSupplierCode(), finalCreateOrgNo, 0);

                            log.warn("updateDocHandlerResp={}", updateCallResult);
                        } catch (Exception e) {
                            log.error("供应商档案分派异常", e);
                        }
                    }
                });
            }

            SupplierCoordinationCreateResponse resp = new SupplierCoordinationCreateResponse();
            if ("use".equals(authedType)) {
                SupplierBase supplierBase = supplierBaseDAO.selectOne(Wrappers.lambdaQuery(SupplierBase.class)
                        .eq(SupplierBase::getEnterpriseNo, enterpriseNo)
                        .eq(SupplierBase::getSupplierCode, supplier.getSupplierCode())
                        .eq(SupplierBase::getUseOrgNo, orgNo)
                        .eq(SupplierBase::getDeleted, DeletedEnum.UN_DELETE.getValue()));
                resp.setSupplierNo(supplierBase.getSupplierNo());
            } else {
                resp.setSupplierNo(supplier.getSupplierNo());
            }
            resp.setSupplierCode(supplier.getSupplierCode());
            resp.setSupplierName(supplier.getSupplierName());
            return resp;
        }

        //没有客户 需要新创建 创建客户的前提是定company所以需要判定company是否存在 1.查询企业名称 2.查询统一社会信用代码
        CompanyV2 company = companyV2Service.getByEnterpriseAndCompanyName(enterpriseNo, supplierName);
        if (null == company) {
            company = companyV2Service.getByEnterpriseAndUnifiedSocialCodeDomestic(enterpriseNo, unifiedSocialCode);
        }
        //走创建逻辑企业逻辑
        if (null == company) {
            log.warn("根据供应商名称:[{}],未在租户[{}]内查询到企业,新增处理", supplierName, enterpriseNo);
            company = new CompanyV2();
            company.setEnterpriseNo(enterpriseNo);
            company.setCompanyNo(companyV2Service.generateCompanyNo());
            company.setCompanyName(supplierName);
            company.setUnifiedSocialCode(unifiedSocialCode);
            //SCS创建的默认境内
            company.setFactoryType(FactoryTypeEnum.DOMESTIC.getValue());
            companyV2Service.save(company);
        }

        SupplierV2 newSupplier = new SupplierV2();
        newSupplier.setEnterpriseNo(enterpriseNo);
        newSupplier.setManageOrgNo(createOrgNo);
        newSupplier.setCompanyNo(company.getCompanyNo());
        newSupplier.setSupplierNo(numberCenterService.createNumber(NumberCenterConstant.SUPPLY_NO_KEY));
        newSupplier.setSupplierCode(numberCenterService.createNumberForBill(BillNameConstant.BDC_SUPPLIER_BILL, enterpriseNo));
        newSupplier.setSupplierName(supplierName);
        newSupplier.setUnifiedSocialCode(unifiedSocialCode);
        newSupplier.setBusinessFlag(SupplierBusinessFlagEnum.FORMAL.getValue());
        newSupplier.setSourceChannel("SCS");
        newSupplier.setOmsSupplierNo(omsSupplierNo);
        this.save(newSupplier);

        SupplierBiz supplierBiz = new SupplierBiz();
        supplierBiz.setEnterpriseNo(enterpriseNo);
        supplierBiz.setUseOrgNo(createOrgNo);
        supplierBiz.setSupplierNo(newSupplier.getSupplierNo());
        supplierBiz.setSupplierCode(newSupplier.getSupplierCode());
        supplierBiz.setDeleted(DeletedEnum.UN_DELETE.getValue());
        supplierBizDAO.insert(supplierBiz);

        SupplierBase supplierBase = new SupplierBase();
        supplierBase.setEnterpriseNo(enterpriseNo);
        supplierBase.setSupplierNo(newSupplier.getSupplierNo());
        supplierBase.setSupplierCode(newSupplier.getSupplierCode());
        supplierBase.setManageOrgNo(newSupplier.getManageOrgNo());
        supplierBase.setUseOrgNo(newSupplier.getManageOrgNo());
        supplierBase.setDeleted(DeletedEnum.UN_DELETE.getValue());
        supplierBaseDAO.insert(supplierBase);

        log.warn("创建供应商[{}]完成", newSupplier.getSupplierNo());

        if ("use".equals(authedType)) {
            // 需要同步分派，否则拿不到使用组织分派后的no
            SupplierAssignExeRequest req = new SupplierAssignExeRequest();
            req.setEnterpriseNo(enterpriseNo);
            req.setManageOrgNo(createOrgNo);
            SupplierAssignDocExeRequest supplierAssignDocExeRequest = new SupplierAssignDocExeRequest();
            supplierAssignDocExeRequest.setSupplierCode(newSupplier.getSupplierCode());
            supplierAssignDocExeRequest.setUseOrgNoList(Collections.singletonList(orgNo));
            req.setDocList(Collections.singletonList(supplierAssignDocExeRequest));
            assignSupplier(req);


            final String finalCreateOrgNo = createOrgNo;
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                @Override
                public void afterCommit() {
                    try {
                        log.warn("updateDocHandlerReq={},{},{},{},{}", enterpriseNo, BillNameConstant.BDC_SUPPLIER_BILL, newSupplier.getSupplierCode(), finalCreateOrgNo, 0);

                        Boolean updateCallResult = gradeControlService.updateDocHandler(enterpriseNo, BillNameConstant.BDC_SUPPLIER_BILL, newSupplier.getSupplierCode(), finalCreateOrgNo, 0);

                        log.warn("updateDocHandlerResp={}", updateCallResult);
                    } catch (Exception e) {
                        log.error("供应商档案分派异常", e);
                    }
                }
            });
        }

        SupplierCoordinationCreateResponse resp = new SupplierCoordinationCreateResponse();
        if ("use".equals(authedType)) {
            SupplierBase useSupplierBase = supplierBaseDAO.selectOne(Wrappers.lambdaQuery(SupplierBase.class)
                    .eq(SupplierBase::getEnterpriseNo, enterpriseNo)
                    .eq(SupplierBase::getSupplierCode, newSupplier.getSupplierCode())
                    .eq(SupplierBase::getUseOrgNo, orgNo)
                    .eq(SupplierBase::getDeleted, DeletedEnum.UN_DELETE.getValue()));
            resp.setSupplierNo(useSupplierBase.getSupplierNo());
        } else {
            resp.setSupplierNo(newSupplier.getSupplierNo());
        }
        resp.setSupplierCode(newSupplier.getSupplierCode());
        resp.setSupplierName(newSupplier.getSupplierName());

        return resp;
    }

    private SupplierAssignExeResponse packAssignResp(String useOrgNo, String msg, boolean result, String docNo) {
        SupplierAssignExeResponse response = new SupplierAssignExeResponse();
        response.setUseOrgNo(useOrgNo);
        response.setMessage(msg);
        response.setSuccess(result);
        response.setDocNo(docNo);

        return response;
    }

    @Override
    @Transactional
    public List<SupplierAssignExeResponse> assignSupplier(SupplierAssignExeRequest req) {
        if (null == req || CollectionUtils.isEmpty(req.getDocList())) {
            return Collections.emptyList();
        }

        String lockKey = null;
        try {
            lockKey = String.join(DistributedLockConstant.DELIMITER, DistributedLockConstant.SUPPLIER_ASSIGN_LOCK_KEY, req.getEnterpriseNo(), req.getManageOrgNo());
            if (!redisClientUtil.lock(lockKey, DistributedLockConstant.DEFAULT_WAIT_TIME, DistributedLockConstant.DEFAULT_LEASE_TIME)) {
                throw new BusinessException(ErrorCode.param_invalid_code, "当前组织正在分派，稍后重试");
            }

            List<String> supplierCodeList = req.getDocList().stream().map(SupplierAssignDocExeRequest::getSupplierCode).collect(Collectors.toList());
            List<SupplierV2> supplierList = supplierV2DAO.selectList(Wrappers.<SupplierV2>lambdaQuery()
                    .eq(SupplierV2::getEnterpriseNo, req.getEnterpriseNo())
                    .eq(SupplierV2::getManageOrgNo, req.getManageOrgNo())
                    .in(SupplierV2::getSupplierCode, supplierCodeList)
                    .eq(SupplierV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
            Map<String, SupplierV2> code2SupplierMap = supplierList.stream().collect(Collectors.toMap(SupplierV2::getSupplierCode, Function.identity()));

            List<SupplierBiz> supplierBizList = supplierBizDAO.selectList(Wrappers.<SupplierBiz>lambdaQuery()
                    .eq(SupplierBiz::getEnterpriseNo, req.getEnterpriseNo())
                    .in(SupplierBiz::getSupplierCode, supplierCodeList)
                    .eq(SupplierBiz::getDeleted, DeletedEnum.UN_DELETE.getValue()));
            Map<String, Map<String, SupplierBiz>> code2usrOrgNo2Biz = supplierBizList.stream().collect(Collectors.groupingBy(SupplierBiz::getSupplierCode, Collectors.toMap(SupplierBiz::getUseOrgNo, Function.identity())));

            List<SupplierBase> supplierBaseList = supplierBaseDAO.selectList(Wrappers.<SupplierBase>lambdaQuery()
                    .eq(SupplierBase::getEnterpriseNo, req.getEnterpriseNo())
                    .in(SupplierBase::getSupplierCode, supplierCodeList)
                    .eq(SupplierBase::getManageOrgNo, req.getManageOrgNo())
                    .eq(SupplierBase::getDeleted, DeletedEnum.UN_DELETE.getValue()));
            Map<String, Map<String, SupplierBase>> code2usrOrgNo2Base = supplierBaseList.stream().collect(Collectors.groupingBy(SupplierBase::getSupplierCode, Collectors.toMap(SupplierBase::getUseOrgNo, Function.identity())));


            List<SupplierGspAudit> supplierGspAuditList = supplierGspAuditDAO.selectList(Wrappers.<SupplierGspAudit>lambdaQuery()
                    .eq(SupplierGspAudit::getEnterpriseNo, req.getEnterpriseNo())
                    .in(SupplierGspAudit::getSupplierCode, supplierCodeList)
                    .eq(SupplierGspAudit::getDeleted, DeletedEnum.UN_DELETE.getValue()));
            Map<String, Map<String, SupplierGspAudit>> code2usrOrgNo2Gsp = supplierGspAuditList.stream().collect(Collectors.groupingBy(SupplierGspAudit::getSupplierCode, Collectors.toMap(SupplierGspAudit::getUseOrgNo, Function.identity())));


            // 没有分派记录的新增组织分派记录
            List<SupplierBase> addSupplierBaseList = new ArrayList<>();

            // 全量比较后删除不存在的组织分派记录
//            List<SupplierBase> deleteSupplierBaseList = new ArrayList<>();

            // 兼容逻辑，防止旧数据没有业务信息，如果管理组织有业务数据则拷贝管理组织的业务信息，如果管理组织没有业务信息，则插入空白的业务信息
            List<SupplierBiz> addSupplierBizList = new ArrayList<>();

            // 如果使用组织没有修改过业务信息，则拷贝管理组织的业务信息
//            List<SupplierBiz> updateSupplierBizList = new ArrayList<>();

            // 首营信息
            List<SupplierGspAudit> addSupplierGspAuditList = new ArrayList<>();


            Map<String, String> codeOrg2No = new HashMap<>();

            List<SupplierAssignExeResponse> resultList = new ArrayList<>(req.getDocList().size());
            for (SupplierAssignDocExeRequest supplierAssignDocExeReq : req.getDocList()) {
                if (!code2SupplierMap.containsKey(supplierAssignDocExeReq.getSupplierCode())) {
                    if (CollectionUtils.isNotEmpty(supplierAssignDocExeReq.getUseOrgNoList())) {
                        for (String useOrgNo : supplierAssignDocExeReq.getUseOrgNoList()) {
                            resultList.add(packAssignResp(useOrgNo, "供应商档案不存在", false, supplierAssignDocExeReq.getSupplierCode()));
                        }
                    }

                    continue;
                }

                if (CollectionUtils.isNotEmpty(supplierAssignDocExeReq.getUseOrgNoList())) {
//                    if (!MapUtils.isEmpty(code2usrOrgNo2Base.get(supplierAssignDocExeReq.getSupplierCode()))) {
//                        Set<String> existsUseOrgNoSet = new HashSet<>(code2usrOrgNo2Base.get(supplierAssignDocExeReq.getSupplierCode()).keySet());
//                        existsUseOrgNoSet.remove(req.getManageOrgNo());
//
//                        List<String> toDeleteUserOrgNo = existsUseOrgNoSet.stream().filter(useOrgNo -> !supplierAssignDocExeReq.getUseOrgNoList().contains(useOrgNo)).collect(Collectors.toList());
//                        if (CollectionUtils.isNotEmpty(toDeleteUserOrgNo)) {
//                            for (String useOrgNo : toDeleteUserOrgNo) {
//                                deleteSupplierBaseList.add(code2usrOrgNo2Base.get(supplierAssignDocExeReq.getSupplierCode()).get(useOrgNo));
//                            }
//                        }
//                    }

                    for (String useOrgNo : supplierAssignDocExeReq.getUseOrgNoList()) {
                        String key = supplierAssignDocExeReq.getSupplierCode() + "-" + useOrgNo;

                        if (code2usrOrgNo2Base.containsKey(supplierAssignDocExeReq.getSupplierCode()) && code2usrOrgNo2Base.get(supplierAssignDocExeReq.getSupplierCode()).containsKey(useOrgNo)) {
                            if (!codeOrg2No.containsKey(key)) {
                                SupplierBase supplierBase = code2usrOrgNo2Base.get(supplierAssignDocExeReq.getSupplierCode()).get(useOrgNo);
                                codeOrg2No.put(key, supplierBase.getSupplierNo());
                            }

                            resultList.add(packAssignResp(useOrgNo, "供应商档案已分派", true, supplierAssignDocExeReq.getSupplierCode()));
                        } else {
                            SupplierBase supplierBase = new SupplierBase();
                            supplierBase.setEnterpriseNo(req.getEnterpriseNo());

//                            if (code2SupplierMap.containsKey(supplierAssignDocExeReq.getSupplierCode())) {
//                                supplierBase.setSupplierNo(code2SupplierMap.get(supplierAssignDocExeReq.getSupplierCode()).getSupplierNo());
//                            }

                            if (!codeOrg2No.containsKey(key)) {
                                codeOrg2No.put(key, numberCenterService.createNumber(NumberCenterConstant.SUPPLY_NO_KEY));
                            }

                            supplierBase.setSupplierNo(codeOrg2No.get(key));
                            supplierBase.setSupplierCode(supplierAssignDocExeReq.getSupplierCode());
                            supplierBase.setManageOrgNo(req.getManageOrgNo());
                            supplierBase.setUseOrgNo(useOrgNo);
                            supplierBase.setDeleted(DeletedEnum.UN_DELETE.getValue());

                            addSupplierBaseList.add(supplierBase);

                            resultList.add(packAssignResp(useOrgNo, "", true, supplierAssignDocExeReq.getSupplierCode()));
                        }

                        if (code2usrOrgNo2Biz.containsKey(supplierAssignDocExeReq.getSupplierCode()) && code2usrOrgNo2Biz.get(supplierAssignDocExeReq.getSupplierCode()).containsKey(useOrgNo)) {
//                            SupplierBiz oldSupplierBiz = code2usrOrgNo2Biz.get(supplierAssignDocExeReq.getSupplierCode()).get(useOrgNo);
//                            if (CommonIfEnum.NO.getValue() == oldSupplierBiz.getUseOrgModifyFlag()) {
//                                SupplierBiz manageSupplierBiz = code2usrOrgNo2Biz.get(supplierAssignDocExeReq.getSupplierCode()).get(req.getManageOrgNo());
//
//                                SupplierBiz newSupplierBiz = BeanUtil.copyFields(manageSupplierBiz, SupplierBiz.class);
//                                newSupplierBiz.setId(oldSupplierBiz.getId());
//
//                                if (!codeOrg2No.containsKey(key)) {
//                                    codeOrg2No.put(key, numberCenterService.createNumber(NumberCenterConstant.SUPPLY_NO_KEY));
//                                }
//
//                                newSupplierBiz.setSupplierNo(codeOrg2No.get(key));
//                                newSupplierBiz.setUseOrgNo(oldSupplierBiz.getUseOrgNo());
//                                newSupplierBiz.setUseOrgModifyFlag(CommonIfEnum.NO.getValue());
//
//                                updateSupplierBizList.add(newSupplierBiz);
//                            }
                        } else {
                            SupplierBiz manageSupplierBiz = code2usrOrgNo2Biz.get(supplierAssignDocExeReq.getSupplierCode()).get(req.getManageOrgNo());
                            SupplierBiz supplierBiz = BeanUtil.copyFields(manageSupplierBiz, SupplierBiz.class);
                            supplierBiz.setId(null);

                            if (!codeOrg2No.containsKey(key)) {
                                codeOrg2No.put(key, numberCenterService.createNumber(NumberCenterConstant.SUPPLY_NO_KEY));
                            }

                            supplierBiz.setSupplierNo(codeOrg2No.get(key));
                            supplierBiz.setUseOrgNo(useOrgNo);
                            supplierBiz.setUseOrgModifyFlag(CommonIfEnum.NO.getValue());

                            addSupplierBizList.add(supplierBiz);
                        }

                        if (code2usrOrgNo2Gsp.containsKey(supplierAssignDocExeReq.getSupplierCode()) && !code2usrOrgNo2Gsp.get(supplierAssignDocExeReq.getSupplierCode()).containsKey(useOrgNo)) {
                            SupplierGspAudit supplierGspAudit = new SupplierGspAudit();
                            supplierGspAudit.setEnterpriseNo(req.getEnterpriseNo());
                            supplierGspAudit.setUseOrgNo(useOrgNo);
                            supplierGspAudit.setSupplierNo(codeOrg2No.get(key));
                            supplierGspAudit.setSupplierCode(supplierAssignDocExeReq.getSupplierCode());
                            supplierGspAudit.setGspAuditStatus(GspAuditStatusEnum.NON_FIRST_BUSINESS.getType());

                            addSupplierGspAuditList.add(supplierGspAudit);
                        }
                    }
                }
            }

            if (CollectionUtils.isNotEmpty(addSupplierBaseList)) {
                supplierBaseDAO.addBatch(addSupplierBaseList);
            }

//            if (CollectionUtils.isNotEmpty(deleteSupplierBaseList)) {
//                supplierBaseDAO.updateByIdBatch(deleteSupplierBaseList);
//            }

            if (CollectionUtils.isNotEmpty(addSupplierBizList)) {
                supplierBizDAO.addBatch(addSupplierBizList);
            }

//            if (CollectionUtils.isNotEmpty(updateSupplierBizList)) {
//                supplierBizDAO.updateByIdBatch(updateSupplierBizList);
//            }

            if (CollectionUtils.isNotEmpty(addSupplierGspAuditList)) {
                supplierGspAuditDAO.addBatch(addSupplierGspAuditList);
            }

            return resultList;
        } finally {
            if (lockKey != null) {
                redisClientUtil.unlock(lockKey);
            }
        }
    }

    @Override
    public List<SupplierAssignExeResponse> assignMdmSupplier(SupplierAssignMdmExeRequest req) {
        if (null == req || CollectionUtils.isEmpty(req.getDocList())) {
            return Collections.emptyList();
        }

        String lockKey = null;
        try {
            lockKey = String.join(DistributedLockConstant.DELIMITER, DistributedLockConstant.SUPPLIER_ASSIGN_LOCK_KEY, req.getEnterpriseNo(), req.getManageOrgNo());
            if (!redisClientUtil.lock(lockKey, DistributedLockConstant.DEFAULT_WAIT_TIME, DistributedLockConstant.DEFAULT_LEASE_TIME)) {
                throw new BusinessException(ErrorCode.param_invalid_code, "当前组织正在分派，稍后重试");
            }

            List<String> supplierCodeList = req.getDocList().stream().map(SupplierAssignDocExeRequest::getSupplierCode).collect(Collectors.toList());
            List<SupplierV2> supplierList = supplierV2DAO.selectList(Wrappers.<SupplierV2>lambdaQuery()
                    .eq(SupplierV2::getEnterpriseNo, req.getEnterpriseNo())
                    .eq(SupplierV2::getManageOrgNo, req.getManageOrgNo())
                    .in(SupplierV2::getSupplierCode, supplierCodeList)
                    .eq(SupplierV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
            Map<String, SupplierV2> code2SupplierMap = supplierList.stream().collect(Collectors.toMap(SupplierV2::getSupplierCode, Function.identity()));

            List<SupplierBiz> supplierBizList = supplierBizDAO.selectList(Wrappers.<SupplierBiz>lambdaQuery()
                    .eq(SupplierBiz::getEnterpriseNo, req.getEnterpriseNo())
                    .in(SupplierBiz::getSupplierCode, supplierCodeList)
                    .eq(SupplierBiz::getDeleted, DeletedEnum.UN_DELETE.getValue()));
            Map<String, Map<String, SupplierBiz>> code2usrOrgNo2Biz = supplierBizList.stream().collect(Collectors.groupingBy(SupplierBiz::getSupplierCode, Collectors.toMap(SupplierBiz::getUseOrgNo, Function.identity())));

            List<SupplierBase> supplierBaseList = supplierBaseDAO.selectList(Wrappers.<SupplierBase>lambdaQuery()
                    .eq(SupplierBase::getEnterpriseNo, req.getEnterpriseNo())
                    .in(SupplierBase::getSupplierCode, supplierCodeList)
                    .eq(SupplierBase::getManageOrgNo, req.getManageOrgNo())
                    .eq(SupplierBase::getDeleted, DeletedEnum.UN_DELETE.getValue()));
            Map<String, Map<String, SupplierBase>> code2usrOrgNo2Base = supplierBaseList.stream().collect(Collectors.groupingBy(SupplierBase::getSupplierCode, Collectors.toMap(SupplierBase::getUseOrgNo, Function.identity())));


            List<SupplierGspAudit> supplierGspAuditList = supplierGspAuditDAO.selectList(Wrappers.<SupplierGspAudit>lambdaQuery()
                    .eq(SupplierGspAudit::getEnterpriseNo, req.getEnterpriseNo())
                    .in(SupplierGspAudit::getSupplierCode, supplierCodeList)
                    .eq(SupplierGspAudit::getDeleted, DeletedEnum.UN_DELETE.getValue()));
            Map<String, Map<String, SupplierGspAudit>> code2usrOrgNo2Gsp = supplierGspAuditList.stream().collect(Collectors.groupingBy(SupplierGspAudit::getSupplierCode, Collectors.toMap(SupplierGspAudit::getUseOrgNo, Function.identity())));


            // 没有分派记录的新增组织分派记录
            List<SupplierBase> addSupplierBaseList = new ArrayList<>();

            // 全量比较后删除不存在的组织分派记录
//            List<SupplierBase> deleteSupplierBaseList = new ArrayList<>();

            // 兼容逻辑，防止旧数据没有业务信息，如果管理组织有业务数据则拷贝管理组织的业务信息，如果管理组织没有业务信息，则插入空白的业务信息
            List<SupplierBiz> addSupplierBizList = new ArrayList<>();

            // 如果使用组织没有修改过业务信息，则拷贝管理组织的业务信息
//            List<SupplierBiz> updateSupplierBizList = new ArrayList<>();

            // 首营信息
            List<SupplierGspAudit> addSupplierGspAuditList = new ArrayList<>();


            Map<String, String> codeOrg2No = new HashMap<>();

            List<SupplierAssignExeResponse> resultList = new ArrayList<>(req.getDocList().size());
            for (SupplierAssignDocExeRequest supplierAssignDocExeReq : req.getDocList()) {
                if (!code2SupplierMap.containsKey(supplierAssignDocExeReq.getSupplierCode())) {
                    if (CollectionUtils.isNotEmpty(supplierAssignDocExeReq.getUseOrgNoList())) {
                        for (String useOrgNo : supplierAssignDocExeReq.getUseOrgNoList()) {
                            resultList.add(packAssignResp(useOrgNo, "供应商档案不存在", false, supplierAssignDocExeReq.getSupplierCode()));
                        }
                    }

                    continue;
                }

                if (CollectionUtils.isNotEmpty(supplierAssignDocExeReq.getUseOrgNoList())) {
//                    if (!MapUtils.isEmpty(code2usrOrgNo2Base.get(supplierAssignDocExeReq.getSupplierCode()))) {
//                        Set<String> existsUseOrgNoSet = new HashSet<>(code2usrOrgNo2Base.get(supplierAssignDocExeReq.getSupplierCode()).keySet());
//                        existsUseOrgNoSet.remove(req.getManageOrgNo());
//
//                        List<String> toDeleteUserOrgNo = existsUseOrgNoSet.stream().filter(useOrgNo -> !supplierAssignDocExeReq.getUseOrgNoList().contains(useOrgNo)).collect(Collectors.toList());
//                        if (CollectionUtils.isNotEmpty(toDeleteUserOrgNo)) {
//                            for (String useOrgNo : toDeleteUserOrgNo) {
//                                deleteSupplierBaseList.add(code2usrOrgNo2Base.get(supplierAssignDocExeReq.getSupplierCode()).get(useOrgNo));
//                            }
//                        }
//                    }

                    for (String useOrgNo : supplierAssignDocExeReq.getUseOrgNoList()) {
                        String key = supplierAssignDocExeReq.getSupplierCode() + "-" + useOrgNo;

                        if (code2usrOrgNo2Base.containsKey(supplierAssignDocExeReq.getSupplierCode()) && code2usrOrgNo2Base.get(supplierAssignDocExeReq.getSupplierCode()).containsKey(useOrgNo)) {
                            if (!codeOrg2No.containsKey(key)) {
                                SupplierBase supplierBase = code2usrOrgNo2Base.get(supplierAssignDocExeReq.getSupplierCode()).get(useOrgNo);
                                codeOrg2No.put(key, supplierBase.getSupplierNo());
                            }

                            resultList.add(packAssignResp(useOrgNo, "供应商档案已分派", true, supplierAssignDocExeReq.getSupplierCode()));
                        } else {
                            SupplierBase supplierBase = new SupplierBase();
                            supplierBase.setEnterpriseNo(req.getEnterpriseNo());

//                            if (code2SupplierMap.containsKey(supplierAssignDocExeReq.getSupplierCode())) {
//                                supplierBase.setSupplierNo(code2SupplierMap.get(supplierAssignDocExeReq.getSupplierCode()).getSupplierNo());
//                            }

                            if (!codeOrg2No.containsKey(key)) {
                                codeOrg2No.put(key, req.getOrgNo2SupplierNoMap().get(useOrgNo));
                            }

                            supplierBase.setSupplierNo(codeOrg2No.get(key));
                            supplierBase.setSupplierCode(supplierAssignDocExeReq.getSupplierCode());
                            supplierBase.setManageOrgNo(req.getManageOrgNo());
                            supplierBase.setUseOrgNo(useOrgNo);
                            supplierBase.setDeleted(DeletedEnum.UN_DELETE.getValue());

                            addSupplierBaseList.add(supplierBase);

                            resultList.add(packAssignResp(useOrgNo, "", true, supplierAssignDocExeReq.getSupplierCode()));
                        }

                        if (code2usrOrgNo2Biz.containsKey(supplierAssignDocExeReq.getSupplierCode()) && code2usrOrgNo2Biz.get(supplierAssignDocExeReq.getSupplierCode()).containsKey(useOrgNo)) {
//                            SupplierBiz oldSupplierBiz = code2usrOrgNo2Biz.get(supplierAssignDocExeReq.getSupplierCode()).get(useOrgNo);
//                            if (CommonIfEnum.NO.getValue() == oldSupplierBiz.getUseOrgModifyFlag()) {
//                                SupplierBiz manageSupplierBiz = code2usrOrgNo2Biz.get(supplierAssignDocExeReq.getSupplierCode()).get(req.getManageOrgNo());
//
//                                SupplierBiz newSupplierBiz = BeanUtil.copyFields(manageSupplierBiz, SupplierBiz.class);
//                                newSupplierBiz.setId(oldSupplierBiz.getId());
//
//                                if (!codeOrg2No.containsKey(key)) {
//                                    codeOrg2No.put(key, numberCenterService.createNumber(NumberCenterConstant.SUPPLY_NO_KEY));
//                                }
//
//                                newSupplierBiz.setSupplierNo(codeOrg2No.get(key));
//                                newSupplierBiz.setUseOrgNo(oldSupplierBiz.getUseOrgNo());
//                                newSupplierBiz.setUseOrgModifyFlag(CommonIfEnum.NO.getValue());
//
//                                updateSupplierBizList.add(newSupplierBiz);
//                            }
                        } else {
                            SupplierBiz manageSupplierBiz = code2usrOrgNo2Biz.get(supplierAssignDocExeReq.getSupplierCode()).get(req.getManageOrgNo());
                            SupplierBiz supplierBiz = BeanUtil.copyFields(manageSupplierBiz, SupplierBiz.class);
                            supplierBiz.setId(null);

                            if (!codeOrg2No.containsKey(key)) {
                                codeOrg2No.put(key, numberCenterService.createNumber(NumberCenterConstant.SUPPLY_NO_KEY));
                            }

                            supplierBiz.setSupplierNo(codeOrg2No.get(key));
                            supplierBiz.setUseOrgNo(useOrgNo);
                            supplierBiz.setUseOrgModifyFlag(CommonIfEnum.NO.getValue());

                            addSupplierBizList.add(supplierBiz);
                        }

                        if (code2usrOrgNo2Gsp.containsKey(supplierAssignDocExeReq.getSupplierCode()) && !code2usrOrgNo2Gsp.get(supplierAssignDocExeReq.getSupplierCode()).containsKey(useOrgNo)) {
                            SupplierGspAudit supplierGspAudit = new SupplierGspAudit();
                            supplierGspAudit.setEnterpriseNo(req.getEnterpriseNo());
                            supplierGspAudit.setUseOrgNo(useOrgNo);
                            supplierGspAudit.setSupplierNo(codeOrg2No.get(key));
                            supplierGspAudit.setSupplierCode(supplierAssignDocExeReq.getSupplierCode());
                            supplierGspAudit.setGspAuditStatus(GspAuditStatusEnum.NON_FIRST_BUSINESS.getType());

                            addSupplierGspAuditList.add(supplierGspAudit);
                        }
                    }
                }
            }

            if (CollectionUtils.isNotEmpty(addSupplierBaseList)) {
                supplierBaseDAO.addBatch(addSupplierBaseList);
            }

//            if (CollectionUtils.isNotEmpty(deleteSupplierBaseList)) {
//                supplierBaseDAO.updateByIdBatch(deleteSupplierBaseList);
//            }

            if (CollectionUtils.isNotEmpty(addSupplierBizList)) {
                supplierBizDAO.addBatch(addSupplierBizList);
            }

//            if (CollectionUtils.isNotEmpty(updateSupplierBizList)) {
//                supplierBizDAO.updateByIdBatch(updateSupplierBizList);
//            }

            if (CollectionUtils.isNotEmpty(addSupplierGspAuditList)) {
                supplierGspAuditDAO.addBatch(addSupplierGspAuditList);
            }

            return resultList;
        } finally {
            if (lockKey != null) {
                redisClientUtil.unlock(lockKey);
            }
        }
    }

    @Override
    @Transactional
    public List<SupplierAssignExeResponse> deAssignSupplier(SupplierAssignExeRequest req) {
        if (null == req || CollectionUtils.isEmpty(req.getDocList())) {
            return Collections.emptyList();
        }

        String lockKey = null;
        try {
            lockKey = String.join(DistributedLockConstant.DELIMITER, DistributedLockConstant.SUPPLIER_ASSIGN_LOCK_KEY, req.getEnterpriseNo(), req.getManageOrgNo());
            if (!redisClientUtil.lock(lockKey, DistributedLockConstant.DEFAULT_WAIT_TIME, DistributedLockConstant.DEFAULT_LEASE_TIME)) {
                throw new BusinessException(ErrorCode.param_invalid_code, "当前组织正在分派，稍后重试");
            }

            List<String> supplierCodeList = req.getDocList().stream().map(SupplierAssignDocExeRequest::getSupplierCode).collect(Collectors.toList());
            List<SupplierV2> supplierList = supplierV2DAO.selectList(Wrappers.<SupplierV2>lambdaQuery()
                    .eq(SupplierV2::getEnterpriseNo, req.getEnterpriseNo())
                    .eq(SupplierV2::getManageOrgNo, req.getManageOrgNo())
                    .in(SupplierV2::getSupplierCode, supplierCodeList)
                    .eq(SupplierV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
            Map<String, SupplierV2> code2SupplierMap = supplierList.stream().collect(Collectors.toMap(SupplierV2::getSupplierCode, Function.identity()));

            List<SupplierBase> supplierBaseList = supplierBaseDAO.selectList(Wrappers.<SupplierBase>lambdaQuery()
                    .eq(SupplierBase::getEnterpriseNo, req.getEnterpriseNo())
                    .in(SupplierBase::getSupplierCode, supplierCodeList)
                    .eq(SupplierBase::getManageOrgNo, req.getManageOrgNo())
                    .eq(SupplierBase::getDeleted, DeletedEnum.UN_DELETE.getValue()));
            Map<String, Map<String, SupplierBase>> code2usrOrgNo2Base = supplierBaseList.stream().collect(Collectors.groupingBy(SupplierBase::getSupplierCode, Collectors.toMap(SupplierBase::getUseOrgNo, Function.identity())));

            List<SupplierBase> deleteSupplierBaseList = new ArrayList<>();

            List<SupplierAssignExeResponse> resultList = new ArrayList<>(req.getDocList().size());

            for (SupplierAssignDocExeRequest supplierAssignDocExeReq : req.getDocList()) {
                if (!code2SupplierMap.containsKey(supplierAssignDocExeReq.getSupplierCode())) {
                    if (CollectionUtils.isNotEmpty(supplierAssignDocExeReq.getUseOrgNoList())) {
                        for (String useOrgNo : supplierAssignDocExeReq.getUseOrgNoList()) {
                            resultList.add(packAssignResp(useOrgNo, "供应商档案不存在", false, supplierAssignDocExeReq.getSupplierCode()));
                        }
                    }

                    continue;
                }

                if (CollectionUtils.isNotEmpty(supplierAssignDocExeReq.getUseOrgNoList())) {
                    if (!MapUtils.isEmpty(code2usrOrgNo2Base.get(supplierAssignDocExeReq.getSupplierCode()))) {
                        Set<String> existsUseOrgNoSet = new HashSet<>(code2usrOrgNo2Base.get(supplierAssignDocExeReq.getSupplierCode()).keySet());
                        existsUseOrgNoSet.remove(req.getManageOrgNo());

                        for (String useOrgNo : supplierAssignDocExeReq.getUseOrgNoList()) {
                            if (!existsUseOrgNoSet.contains(useOrgNo)) {
                                resultList.add(packAssignResp(useOrgNo, "不存在分派关系", false, supplierAssignDocExeReq.getSupplierCode()));
                                continue;
                            }

                            SupplierBase supplierBase = code2usrOrgNo2Base.get(supplierAssignDocExeReq.getSupplierCode()).get(useOrgNo);
                            supplierBase.setDeleted(DeletedEnum.DELETED.getValue());

                            deleteSupplierBaseList.add(supplierBase);

                            resultList.add(packAssignResp(useOrgNo, "", true, supplierAssignDocExeReq.getSupplierCode()));
                        }
                    }
                }
            }

            if (CollectionUtils.isNotEmpty(deleteSupplierBaseList)) {
                supplierBaseDAO.updateByIdBatch(deleteSupplierBaseList);
            }

            return resultList;
        } finally {
            if (lockKey != null) {
                redisClientUtil.unlock(lockKey);
            }
        }
    }

    @Override
    public PageVo<SupplierFormalPageVO> findListPageByFormal(OperationModel operationModel, SupplierPageByFormalQueryReq queryReq, PageDto pageDTO) {
        ValidatorUtils.checkEmptyThrowEx(queryReq, "查询条件不能为空");

        Page<SupplierV2WithBiz> page = PageHelper.startPage(pageDTO.getPageIndex(), pageDTO.getPageSize(), StringUtils.isBlank(pageDTO.getOrderBy()) ? "b.create_time desc" : pageDTO.getOrderBy());
        supplierV2DAO.findListPageByFormal(queryReq);

        return PageUtils.convertPageVo(page, data -> completeSupplementFormalPageVO(operationModel.getEnterpriseNo(), data));
    }


    @Override
    public PageVo<SupplierPageVO> querySpecifyOrgPageSupplier(OperationModel operationModel, SupplierPageQueryBySpecifyOrgReq params, PageDto pageDto) {
        ValidatorUtils.checkEmptyThrowEx(params.getOrgNo(), "指定组织不能为空");

        //获取有权限的组织列表
        List<OrganizationVo> organizationList = uimTenantService.getAuthEnterpriseList(operationModel.getEnterpriseNo(), operationModel.getUserNo(), operationModel.getEmployerNo());
        final Optional<OrganizationVo> optional = organizationList.stream().filter(t -> params.getOrgNo().equals(t.getOrgNo())).findAny();
        if (!optional.isPresent()) {
            return new PageVo<>();
        }

        Page<SupplierV2WithBiz> page = PageHelper.startPage(pageDto.getPageIndex(), pageDto.getPageSize(), StringUtils.isBlank(pageDto.getOrderBy()) ? "b.create_time desc" : pageDto.getOrderBy());
        SupplierStandardQueryReq query = new SupplierStandardQueryReq();
        query.setEnterpriseNo(operationModel.getEnterpriseNo());
        query.setUseOrgNo(params.getOrgNo());
        if (null != params.getControlStatus()) {
            query.setControlStatusList(Collections.singletonList(params.getControlStatus()));
        }
        supplierV2DAO.queryWithBizInfo(query);
        return PageUtils.convertPageVo(page, data -> completeSupplementPageVO(operationModel.getEnterpriseNo(), data));
    }

    @Override
    public void validateSaveBasicAndInfoReqByApply(OperationModel operationModel, String applyContent) {
        SupplierApplyFormReq params = JSON.parseObject(applyContent, SupplierApplyFormReq.class);

        //必填项校验
//        ValidatorUtils.checkEmptyThrowEx(params.getSupplierCode(), "供应商编码不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getSupplierName(), "供应商名称不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getTransactionType(), "供应商类型不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getSupplierCategoryNo(), "供应商分类不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getCompanyName(), "企业名称不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getUnifiedSocialCode(), "统一社会信用代码不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getFactoryType(), "企业注册地域不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getTaxCategory(), "纳税类别不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getRetailInvestors(), "是否散户不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getIsAssociatedEnterprise(), "是否内部组织不能为空");
        if (null != params.getIsAssociatedEnterprise() && CommonIfEnum.YES.getValue().equals(params.getIsAssociatedEnterprise())) {
            ValidatorUtils.checkEmptyThrowEx(params.getAssociatedOrgNo(), "对应内部组织不能为空");
        }

        //范围校验
        if (StringUtils.isNotEmpty(params.getSupplierCode())) {
            ValidatorUtils.checkTrueThrowEx(params.getSupplierCode().length() > 50, "供应商编码不能大于50");
        }
        if (StringUtils.isNotEmpty(params.getSupplierName())) {
            ValidatorUtils.checkTrueThrowEx(params.getSupplierName().length() > 128, "供应商名称不能大于128");
        }
        if (StringUtils.isNotEmpty(params.getSupplierNameEn())) {
            ValidatorUtils.checkTrueThrowEx(params.getSupplierNameEn().length() > 300, "供应商英文名称不能大于300");
        }
        if (StringUtils.isNotEmpty(params.getMnemonicCode())) {
            ValidatorUtils.checkTrueThrowEx(params.getMnemonicCode().length() > 100, "助记码不能大于100");
        }
        if (StringUtils.isNotEmpty(params.getCompanyName())) {
            ValidatorUtils.checkTrueThrowEx(params.getCompanyName().length() > 128, "企业名称不能大于128");
        }
        if (StringUtils.isNotEmpty(params.getUnifiedSocialCode())) {
            ValidatorUtils.checkTrueThrowEx(params.getUnifiedSocialCode().length() > 100, "统一社会信用代码不能大于100");
        }
//        if (StringUtils.isNotEmpty(params.getCountry())) {
//            ValidatorUtils.checkTrueThrowEx(params.getCountry().length() > 50, "国家/地区不能大于50");
//        }
        if (StringUtils.isNotEmpty(params.getRemark())) {
            ValidatorUtils.checkTrueThrowEx(params.getRemark().length() > 300, "备注不能大于300");
        }


        if (StringUtils.isNotEmpty(params.getCreditAmount())) {
            ValidatorUtils.checkTrueThrowEx(params.getCreditAmount().length() > 20, "信用额度不能大于20");
        }
        if (StringUtils.isNotEmpty(params.getOwnerCompany())) {
            ValidatorUtils.checkTrueThrowEx(params.getOwnerCompany().length() > 100, "业务归属不能大于100");
        }


        SupplierAddressListEditReq supplierAddressListEditReq = new SupplierAddressListEditReq();
        supplierAddressListEditReq.setLinkAddressList(params.getLinkAddressList());
        editSupplierAddressListValidate(operationModel, supplierAddressListEditReq);

        SupplierLinkmanListEditReq supplierLinkmanListEditReq = new SupplierLinkmanListEditReq();
        supplierLinkmanListEditReq.setLinkmanList(params.getLinkmanList());
        editSupplierLinkmanListValidate(operationModel, supplierLinkmanListEditReq);

        SupplierOrdermanListEditReq supplierOrdermanListEditReq = new SupplierOrdermanListEditReq();
        supplierOrdermanListEditReq.setSupplierManList(params.getSupplierManList());
        editSupplierOrdermanListValidate(operationModel, supplierOrdermanListEditReq);

        SupplierBankListEditReq supplierBankListEditReq = new SupplierBankListEditReq();
        supplierBankListEditReq.setBankList(params.getBankList());
        editSupplierBankListValidate(operationModel, supplierBankListEditReq);
    }

    @Override
    public void validateEditBasicReqByApply(OperationModel operationModel, String applyContent) {
        SupplierEditBasicReq params = JSON.parseObject(applyContent, SupplierEditBasicReq.class);

        //必填项校验
//        ValidatorUtils.checkEmptyThrowEx(params.getSupplierCode(), "供应商编码不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getSupplierName(), "供应商名称不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getTransactionType(), "供应商类型不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getSupplierCategoryNo(), "供应商分类不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getCompanyName(), "企业名称不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getUnifiedSocialCode(), "统一社会信用代码不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getFactoryType(), "企业注册地域不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getTaxCategory(), "纳税类别不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getRetailInvestors(), "是否散户不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getIsAssociatedEnterprise(), "是否内部组织不能为空");
        if (null != params.getIsAssociatedEnterprise() && CommonIfEnum.YES.getValue().equals(params.getIsAssociatedEnterprise())) {
            ValidatorUtils.checkEmptyThrowEx(params.getAssociatedOrgNo(), "对应内部组织不能为空");
        }

        //范围校验
        if (StringUtils.isNotEmpty(params.getSupplierCode())) {
            ValidatorUtils.checkTrueThrowEx(params.getSupplierCode().length() > 50, "供应商编码不能大于50");
        }
        if (StringUtils.isNotEmpty(params.getSupplierName())) {
            ValidatorUtils.checkTrueThrowEx(params.getSupplierName().length() > 128, "供应商名称不能大于128");
        }
        if (StringUtils.isNotEmpty(params.getSupplierNameEn())) {
            ValidatorUtils.checkTrueThrowEx(params.getSupplierNameEn().length() > 300, "供应商英文名称不能大于300");
        }
        if (StringUtils.isNotEmpty(params.getMnemonicCode())) {
            ValidatorUtils.checkTrueThrowEx(params.getMnemonicCode().length() > 100, "助记码不能大于100");
        }
        if (StringUtils.isNotEmpty(params.getCompanyName())) {
            ValidatorUtils.checkTrueThrowEx(params.getCompanyName().length() > 128, "企业名称不能大于128");
        }
        if (StringUtils.isNotEmpty(params.getUnifiedSocialCode())) {
            ValidatorUtils.checkTrueThrowEx(params.getUnifiedSocialCode().length() > 100, "统一社会信用代码不能大于100");
        }
        if (StringUtils.isNotEmpty(params.getCountry())) {
            ValidatorUtils.checkTrueThrowEx(params.getCountry().length() > 50, "国家/地区不能大于50");
        }
        if (StringUtils.isNotEmpty(params.getRemark())) {
            ValidatorUtils.checkTrueThrowEx(params.getRemark().length() > 300, "备注不能大于300");
        }


        SupplierBankListEditReq supplierBankListEditReq = new SupplierBankListEditReq();
        supplierBankListEditReq.setBankList(params.getBankList());
        editSupplierBankListValidate(operationModel, supplierBankListEditReq);
    }

    @Override
    public Boolean changeSupplierGspStatus(SupplierGspStatusReq params) {
        SupplierGspAudit newSupplierGspAudit = new SupplierGspAudit();
        newSupplierGspAudit.setGspAuditStatus(GspAuditStatusEnum.HAS_FIRST_BUSINESS.getType());
        newSupplierGspAudit.setGspAuditResult(GspAuditStatusEnum.HAS_FIRST_BUSINESS.getName());

        int update = supplierGspAuditDAO.update(newSupplierGspAudit, Wrappers.<SupplierGspAudit>lambdaUpdate()
                .eq(SupplierGspAudit::getEnterpriseNo, params.getEnterpriseNo())
                .eq(SupplierGspAudit::getUseOrgNo, params.getUseOrgNo())
                .eq(SupplierGspAudit::getSupplierCode, params.getSupplierCode())
                .eq(SupplierGspAudit::getDeleted, DeletedEnum.UN_DELETE.getValue()));
        return update == 0;
    }

    @Override
    public List<SupplierBasicResponse> querySupplierByCodeList(String enterpriseNo, List<String> supplierCodeList) {
        ValidatorUtils.checkEmptyThrowEx(enterpriseNo, "企业编号不能为空");
        ValidatorUtils.checkEmptyThrowEx(supplierCodeList, "供应商编码不能为空");

        List<SupplierV2> supplierV2s = supplierV2DAO.selectList(Wrappers.<SupplierV2>lambdaQuery()
                .eq(SupplierV2::getEnterpriseNo, enterpriseNo)
                .in(SupplierV2::getSupplierCode, supplierCodeList)
                .eq(SupplierV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));

        return convert2SupplierBasicResponse(supplierV2s);
    }

    @Override
    public List<SupplierBaseResponse> queryAssignInfo(String enterpriseNo, String supplierCode) {
        List<SupplierBase> supplierBaseList = supplierBaseDAO.selectList(Wrappers.<SupplierBase>lambdaQuery()
                .eq(SupplierBase::getEnterpriseNo, enterpriseNo)
                .eq(SupplierBase::getSupplierCode, supplierCode)
                .eq(SupplierBase::getDeleted, DeletedEnum.UN_DELETE.getValue()));
        if (CollectionUtils.isEmpty(supplierBaseList)) {
            return Collections.emptyList();
        }

        List<OrganizationVo> organizationVoList = organizationService.findListNoAuth(enterpriseNo, new ArrayList<>(supplierBaseList.stream().flatMap(supplierBase -> Stream.of(supplierBase.getUseOrgNo(), supplierBase.getManageOrgNo())).collect(Collectors.toSet())));
        final Map<String, OrganizationVo> orgNo2OrganizationVo = organizationVoList.stream().collect(Collectors.toMap(OrganizationVo::getOrgNo, Function.identity()));

        return supplierBaseList.stream().map(supplierBase -> {
            SupplierBaseResponse response = BeanUtil.copyFields(supplierBase, SupplierBaseResponse.class);

            // 补充管理组织名称
            response.setManageOrgName(null != orgNo2OrganizationVo.get(supplierBase.getManageOrgNo()) ? orgNo2OrganizationVo.get(supplierBase.getManageOrgNo()).getOrgName() : "");

            // 补充使用组织名称
            response.setManageOrgName(null != orgNo2OrganizationVo.get(supplierBase.getUseOrgNo()) ? orgNo2OrganizationVo.get(supplierBase.getUseOrgNo()).getOrgName() : "");

            // 补充管控状态名称
            response.setControlStatusName(SupplierControlStatusEnum.getByType(supplierBase.getControlStatus()) == null ? null : SupplierControlStatusEnum.getByType(supplierBase.getControlStatus()).getName());

            return response;
        }).collect(Collectors.toList());
    }

    @Override
    public Boolean checkConvertToCustomer(OperationModel operationModel, String supplierCode) {
        ValidatorUtils.checkEmptyThrowEx(supplierCode, "供应商编码不能为空");

        SupplierV2 supplier = getSupplierByCode(operationModel, supplierCode);
        ValidatorUtils.checkEmptyThrowEx(supplier, "供应商不存在");

        // 管理组织列表
        List<String> manageNoList = gradeControlService.listMgrOrgNos(operationModel.getEnterpriseNo(), BillNameConstant.BDC_CUSTOMER_BILL);
        if (CollectionUtils.isEmpty(manageNoList) || !manageNoList.contains(supplier.getManageOrgNo())) {
            return false;
        }

        return true;
    }

    private void validateSaveBasicAndBizReq(OperationModel operationModel, SupplierSaveBasicAndBizReq params) {
        //必填项校验
        ValidatorUtils.checkEmptyThrowEx(params.getSupplierCode(), "供应商编码不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getSupplierName(), "供应商名称不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getTransactionType(), "供应商类型不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getSupplierCategoryNo(), "供应商分类不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getCompanyName(), "企业名称不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getUnifiedSocialCode(), "统一社会信用代码不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getFactoryType(), "企业注册地域不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getTaxCategory(), "纳税类别不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getRetailInvestors(), "是否散户不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getIsAssociatedEnterprise(), "是否内部组织不能为空");
        if (null != params.getIsAssociatedEnterprise() && CommonIfEnum.YES.getValue().equals(params.getIsAssociatedEnterprise())) {
            ValidatorUtils.checkEmptyThrowEx(params.getAssociatedOrgNo(), "对应内部组织不能为空");
        }

        //范围校验
        if (StringUtils.isNotEmpty(params.getSupplierCode())) {
            ValidatorUtils.checkTrueThrowEx(params.getSupplierCode().length() > 50, "供应商编码不能大于50");
        }
        if (StringUtils.isNotEmpty(params.getSupplierName())) {
            ValidatorUtils.checkTrueThrowEx(params.getSupplierName().length() > 128, "供应商名称不能大于128");
        }
        if (StringUtils.isNotEmpty(params.getSupplierNameEn())) {
            ValidatorUtils.checkTrueThrowEx(params.getSupplierNameEn().length() > 300, "供应商英文名称不能大于300");
        }
        if (StringUtils.isNotEmpty(params.getMnemonicCode())) {
            ValidatorUtils.checkTrueThrowEx(params.getMnemonicCode().length() > 100, "助记码不能大于100");
        }
        if (StringUtils.isNotEmpty(params.getCompanyName())) {
            ValidatorUtils.checkTrueThrowEx(params.getCompanyName().length() > 128, "企业名称不能大于128");
        }
        if (StringUtils.isNotEmpty(params.getUnifiedSocialCode())) {
            ValidatorUtils.checkTrueThrowEx(params.getUnifiedSocialCode().length() > 100, "统一社会信用代码不能大于100");
        }
        if (StringUtils.isNotEmpty(params.getCountry())) {
            ValidatorUtils.checkTrueThrowEx(params.getCountry().length() > 50, "国家/地区不能大于50");
        }
        if (StringUtils.isNotEmpty(params.getRemark())) {
            ValidatorUtils.checkTrueThrowEx(params.getRemark().length() > 300, "备注不能大于300");
        }


        if (StringUtils.isNotEmpty(params.getCreditAmount())) {
            ValidatorUtils.checkTrueThrowEx(params.getCreditAmount().length() > 20, "信用额度不能大于20");
        }
        if (StringUtils.isNotEmpty(params.getOwnerCompany())) {
            ValidatorUtils.checkTrueThrowEx(params.getOwnerCompany().length() > 100, "业务归属不能大于100");
        }
    }

    @Override
    @Transactional
    public String manageSaveSupplierBasicAndBiz(OperationModel operationModel, SupplierSaveBasicAndBizReq params) {
        ValidatorUtils.checkEmptyThrowEx(params, "供应商信息不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getManageOrgNo(), "管理组织不能为空");

        // 多组织时才需要校验档案管理权，目前集团和子租户都属于多组织架构，否则不需要校验；uap统一处理
        List<String> manageNoList = gradeControlService.listMgrOrgNos(operationModel.getEnterpriseNo(), BillNameConstant.BDC_SUPPLIER_BILL);
        ValidatorUtils.checkEmptyThrowEx(manageNoList, "无档案管理权");
        ValidatorUtils.checkTrueThrowEx(!manageNoList.contains(params.getManageOrgNo()), "无档案管理权");

        // 校验入参必填、范围
        validateSaveBasicAndBizReq(operationModel, params);


        ValidatorUtils.checkTrueThrowEx(!checkOnlyCode(operationModel, null, params.getSupplierCode()), "供应商编码重复");
        ValidatorUtils.checkTrueThrowEx(!checkOnlyName(operationModel, null, params.getSupplierName()), "供应商名称重复");

        if (CommonIfEnum.YES.getValue().equals(params.getIsAssociatedEnterprise())) {
            List<SupplierV2> byInternalOrgNo = findByInternalOrgNo(operationModel, null, params.getAssociatedOrgNo());
            if (CollectionUtils.isNotEmpty(byInternalOrgNo)) {
                String errorMessage = String.format("对应内部组织已被供应商【%s】关联", String.join(",", byInternalOrgNo.stream().map(SupplierV2::getSupplierName).collect(Collectors.toSet())));
                throw new BusinessException(ErrorCode.param_invalid_msg, errorMessage);
            }
        }

        // 如果企业不存在，则创建企业
        CompanySaveOrUpdateReq companySaveOrUpdateReq = new CompanySaveOrUpdateReq();
        BeanUtils.copyProperties(params, companySaveOrUpdateReq);
        CompanyV2 company = companyV2Service.saveOrUpdateCompany(operationModel, params.getSupplierCode(), companySaveOrUpdateReq, CompanyPartnershipEnum.SUPPLIER);

        // 供应商基本信息
        SupplierV2 supplier = new SupplierV2();
        String supplierNo = numberCenterService.createNumber(NumberCenterConstant.SUPPLY_NO_KEY);
        supplier.setEnterpriseNo(operationModel.getEnterpriseNo());
        supplier.setManageOrgNo(params.getManageOrgNo());
        supplier.setCompanyNo(company.getCompanyNo());
        supplier.setUnifiedSocialCode(company.getUnifiedSocialCode());
        supplier.setSupplierNo(supplierNo);
        supplier.setSupplierCode(params.getSupplierCode());
        supplier.setSupplierName(params.getSupplierName());
        supplier.setSupplierCategoryNo(params.getSupplierCategoryNo());
        supplier.setTransactionType(params.getTransactionType());

        supplier.setIsGspControl(CommonIfEnum.YES.getValue()); //供应商默认启用

        supplier.setIsAssociatedEnterprise(params.getIsAssociatedEnterprise());
        supplier.setAssociatedOrgNo(params.getAssociatedOrgNo());
        supplier.setAssociatedOrgCode(params.getAssociatedOrgCode());
        supplier.setAssociatedOrgName(params.getAssociatedOrgName());

        supplier.setMnemonicCode(params.getMnemonicCode());
        supplier.setSupplierNameEn(params.getSupplierNameEn());
        supplier.setBusinessFlag(SupplierBusinessFlagEnum.DRAFT.getValue().equals(params.getBusinessFlag()) ? SupplierBusinessFlagEnum.DRAFT.getValue() : SupplierBusinessFlagEnum.FORMAL.getValue());
        supplier.setDeleted(DeletedEnum.UN_DELETE.getValue());
        supplier.setRemark(params.getRemark());
        supplier.setRetailInvestors(params.getRetailInvestors());

        CommonUtil.fillCreatInfo(operationModel, supplier);

        supplierV2DAO.insert(supplier);

        SupplierBiz supplierBiz = null;
        //业务信息
        supplierBiz = new SupplierBiz();
        supplierBiz.setEnterpriseNo(operationModel.getEnterpriseNo());
        supplierBiz.setSupplierCode(params.getSupplierCode());
        supplierBiz.setUseOrgNo(params.getManageOrgNo());
        supplierBiz.setSupplierNo(supplierNo);
//        supplierBiz.setControlId(params.getControlId());
//        supplierBiz.setControlTypeName(params.getControlTypeName());
        supplierBiz.setCooperationMode(params.getCooperationMode());
        supplierBiz.setCurrencyId(params.getCurrencyId());
        supplierBiz.setSettlementModes(params.getSettlementModes());
        supplierBiz.setCreditAmount(params.getCreditAmount());
        supplierBiz.setPeriodDays(params.getPeriodDays());
        supplierBiz.setCoopStartTime(params.getCoopStartTime());
        supplierBiz.setCoopEndTime(params.getCoopEndTime());
        supplierBiz.setPaymentAgreementId(params.getPaymentAgreementId());
        supplierBiz.setPaymentAgreementCode(params.getPaymentAgreementCode());
        supplierBiz.setPaymentAgreementName(params.getPaymentAgreementName());
        supplierBiz.setPaymentAgreementYsId(params.getPaymentAgreementYsId());
        supplierBiz.setPaymentTerm(params.getPaymentTerm());
        supplierBiz.setOwnerCompany(params.getOwnerCompany());
        supplierBiz.setDeleted(DeletedEnum.UN_DELETE.getValue());

        CommonUtil.fillCreatInfo(operationModel, supplierBiz);
        supplierBizDAO.insert(supplierBiz);

        // 新增管理组织的分派记录
        SupplierBase supplierBase = new SupplierBase();
        supplierBase.setEnterpriseNo(operationModel.getEnterpriseNo());
        supplierBase.setSupplierNo(supplierNo);
        supplierBase.setSupplierCode(params.getSupplierCode());
        supplierBase.setManageOrgNo(params.getManageOrgNo());
        supplierBase.setUseOrgNo(params.getManageOrgNo());
        supplierBase.setDeleted(DeletedEnum.UN_DELETE.getValue());
        CommonUtil.fillCreatInfo(operationModel, supplierBase);
        CommonUtil.fillOperateInfo(operationModel, supplierBase);
        supplierBaseDAO.insert(supplierBase);

        SupplierGspAudit supplierGspAudit = new SupplierGspAudit();
        supplierGspAudit.setEnterpriseNo(operationModel.getEnterpriseNo());
        supplierGspAudit.setUseOrgNo(supplier.getManageOrgNo());
        supplierGspAudit.setSupplierNo(supplier.getSupplierNo());
        supplierGspAudit.setSupplierCode(supplier.getSupplierCode());
        supplierGspAudit.setGspAuditStatus(GspAuditStatusEnum.NON_FIRST_BUSINESS.getType());
        supplierGspAuditService.save(supplierGspAudit);

        Map<String, Object> businessValue = new HashMap<>();
        businessValue.put("supplier", supplier);
        businessValue.put("supplierBase", supplierBase);
        businessValue.put("supplierBiz", supplierBiz);
        businessValue.put("supplierGspAudit", supplierGspAudit);
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                try {
                    businessLogService.saveLog(DLogLevel.INFO.name(), ViewNameConstant.BDC_SUPPLIER_VIEW, params.getSupplierCode() + params.getManageOrgNo(), "新增供应商档案", "新增供应商档案", JSON.toJSONString(businessValue), "");
                } catch (Exception e) {
                    log.error("新增供应商日志保存失败", e);
                }
            }
        });

        return params.getSupplierCode();
    }

    @Override
    public String preValidateManageSaveSupplierBasicAndBiz(OperationModel operationModel, SupplierSaveBasicAndBizReq params) {
        ValidatorUtils.checkEmptyThrowEx(params, "供应商信息不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getManageOrgNo(), "管理组织不能为空");

        // 多组织时才需要校验档案管理权，目前集团和子租户都属于多组织架构，否则不需要校验；uap统一处理
        List<String> manageNoList = gradeControlService.listMgrOrgNos(operationModel.getEnterpriseNo(), BillNameConstant.BDC_SUPPLIER_BILL);
        ValidatorUtils.checkEmptyThrowEx(manageNoList, "无档案管理权");
        ValidatorUtils.checkTrueThrowEx(!manageNoList.contains(params.getManageOrgNo()), "无档案管理权");

        // 校验入参必填、范围
        validateSaveBasicAndBizReq(operationModel, params);


        ValidatorUtils.checkTrueThrowEx(!checkOnlyCode(operationModel, null, params.getSupplierCode()), "供应商编码重复");
        ValidatorUtils.checkTrueThrowEx(!checkOnlyName(operationModel, null, params.getSupplierName()), "供应商名称重复");

        if (CommonIfEnum.YES.getValue().equals(params.getIsAssociatedEnterprise())) {
            List<SupplierV2> byInternalOrgNo = findByInternalOrgNo(operationModel, null, params.getAssociatedOrgNo());
            if (CollectionUtils.isNotEmpty(byInternalOrgNo)) {
                String errorMessage = String.format("对应内部组织已被供应商【%s】关联", String.join(",", byInternalOrgNo.stream().map(SupplierV2::getSupplierName).collect(Collectors.toSet())));
                throw new BusinessException(ErrorCode.param_invalid_msg, errorMessage);
            }
        }

        // 如果企业不存在，则创建企业
        CompanySaveOrUpdateReq companySaveOrUpdateReq = new CompanySaveOrUpdateReq();
        BeanUtils.copyProperties(params, companySaveOrUpdateReq);
        return companyV2Service.preValidateSaveOrUpdateCompany(operationModel, null, companySaveOrUpdateReq, CompanyPartnershipEnum.SUPPLIER);
    }

    @Override
    @Transactional
    public Boolean editSupplierBasicAndBiz(OperationModel operationModel, SupplierEditBasicAndBizReq params) {
        //校验入参必填、范围
        validateEditBasicAndBizReq(operationModel, params);

        LambdaQueryWrapper<SupplierV2> queryWrapper = Wrappers.<SupplierV2>lambdaQuery()
                .eq(SupplierV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(SupplierV2::getSupplierCode, params.getSupplierCode())
                .eq(SupplierV2::getDeleted, DeletedEnum.UN_DELETE.getValue());
        SupplierV2 supplierV2 = supplierV2DAO.selectOne(queryWrapper);
        ValidatorUtils.checkEmptyThrowEx(supplierV2, "供应商不存在");

        // 当前为管理组织，档案不为正式态，需要校验管理权开启与否
        if (supplierV2.getManageOrgNo().equals(params.getUseOrgNo()) &&
                (!SupplierBusinessFlagEnum.FORMAL.getValue().equals(supplierV2.getBusinessFlag()))) {
            List<String> manageNoList = gradeControlService.listMgrOrgNos(operationModel.getEnterpriseNo(), BillNameConstant.BDC_SUPPLIER_BILL);
            ValidatorUtils.checkEmptyThrowEx(manageNoList, "无档案管理权");
            ValidatorUtils.checkTrueThrowEx(!manageNoList.contains(params.getUseOrgNo()), "无档案管理权");
        }

        ValidatorUtils.checkTrueThrowEx(supplierV2DAO.selectCount(new LambdaQueryWrapper<SupplierV2>()
                .eq(SupplierV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(SupplierV2::getSupplierName, params.getSupplierName())
                .ne(SupplierV2::getSupplierCode, params.getSupplierCode())
                .eq(SupplierV2::getDeleted, DeletedEnum.UN_DELETE.getValue())) > 0, "供应商名称重复");

        if (CommonIfEnum.YES.getValue().equals(params.getIsAssociatedEnterprise())) {
            List<SupplierV2> byInternalOrgNo = findByInternalOrgNo(operationModel, params.getSupplierCode(), params.getAssociatedOrgNo());
            if (CollectionUtils.isNotEmpty(byInternalOrgNo)) {
                String errorMessage = String.format("对应内部组织已被供应商【%s】关联", String.join(",", byInternalOrgNo.stream().map(SupplierV2::getSupplierName).collect(Collectors.toSet())));
                throw new BusinessException(ErrorCode.param_invalid_msg, errorMessage);
            }
        }

        // 管理组织才能修改基本信息
        CompanyV2 company = null;
//        SupplierV2 newSupplier = null;
        if (supplierV2.getManageOrgNo().equals(params.getUseOrgNo())) {
            // 如果企业不存在，则创建企业
            CompanySaveOrUpdateReq companySaveOrUpdateReq = new CompanySaveOrUpdateReq();
            BeanUtils.copyProperties(params, companySaveOrUpdateReq);
            company = companyV2Service.saveOrUpdateCompany(operationModel, params.getSupplierCode(), companySaveOrUpdateReq, CompanyPartnershipEnum.SUPPLIER);

            // 供应商基本信息
            LambdaUpdateWrapper<SupplierV2> updateWrapper = Wrappers.lambdaUpdate(SupplierV2.class);
            updateWrapper.set(SupplierV2::getSupplierName, params.getSupplierName());
            updateWrapper.set(SupplierV2::getSupplierNameEn, params.getSupplierNameEn());
            updateWrapper.set(SupplierV2::getMnemonicCode, params.getMnemonicCode());
            updateWrapper.set(SupplierV2::getTransactionType, params.getTransactionType());
            updateWrapper.set(SupplierV2::getSupplierCategoryNo, params.getSupplierCategoryNo());

            updateWrapper.set(SupplierV2::getCompanyNo, company.getCompanyNo());
            updateWrapper.set(SupplierV2::getUnifiedSocialCode, company.getUnifiedSocialCode());

            updateWrapper.set(SupplierV2::getRetailInvestors, params.getRetailInvestors());

            updateWrapper.set(SupplierV2::getIsAssociatedEnterprise, params.getIsAssociatedEnterprise());
            if (CommonIfEnum.YES.getValue().equals(params.getIsAssociatedEnterprise())) {
                updateWrapper.set(SupplierV2::getAssociatedOrgNo, params.getAssociatedOrgNo());
                updateWrapper.set(SupplierV2::getAssociatedOrgCode, params.getAssociatedOrgCode());
                updateWrapper.set(SupplierV2::getAssociatedOrgName, params.getAssociatedOrgName());
            } else {
                updateWrapper.set(SupplierV2::getAssociatedOrgNo, null);
                updateWrapper.set(SupplierV2::getAssociatedOrgCode, null);
                updateWrapper.set(SupplierV2::getAssociatedOrgName, null);
            }

            updateWrapper.set(SupplierV2::getRemark, params.getRemark());

            updateWrapper.set(SupplierV2::getIsGspControl, CommonIfEnum.YES.getValue());
            updateWrapper.set(SupplierV2::getBusinessFlag, SupplierBusinessFlagEnum.DRAFT.getValue().equals(params.getBusinessFlag()) ? SupplierBusinessFlagEnum.DRAFT.getValue() : SupplierBusinessFlagEnum.FORMAL.getValue());

            updateWrapper.set(SupplierV2::getOperateName, operationModel.getUserName());
            updateWrapper.set(SupplierV2::getOperateNo, operationModel.getEmployerNo());
            updateWrapper.set(SupplierV2::getOperateTime, DateUtil.getCurrentDate());

            // where条件
            updateWrapper.eq(SupplierV2::getEnterpriseNo, operationModel.getEnterpriseNo());
            updateWrapper.eq(SupplierV2::getManageOrgNo, supplierV2.getManageOrgNo());
            updateWrapper.eq(SupplierV2::getSupplierCode, params.getSupplierCode());
            updateWrapper.eq(SupplierV2::getDeleted, DeletedEnum.UN_DELETE.getValue());

            supplierV2DAO.update(null, updateWrapper);

//            newSupplier = updateWrapper.getEntity();
        } else {
            company = companyV2Service.getOne(Wrappers.<CompanyV2>lambdaQuery()
                    .eq(CompanyV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                    .eq(CompanyV2::getCompanyNo, supplierV2.getCompanyNo()));
        }

        LambdaQueryWrapper<SupplierBiz> bizQueryWrapper = Wrappers.<SupplierBiz>lambdaQuery()
                .eq(SupplierBiz::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(SupplierBiz::getUseOrgNo, params.getUseOrgNo())
                .eq(SupplierBiz::getSupplierCode, params.getSupplierCode())
                .eq(SupplierBiz::getDeleted, DeletedEnum.UN_DELETE.getValue());
        SupplierBiz dbSupplierBiz = supplierBizDAO.selectOne(bizQueryWrapper);
        ValidatorUtils.checkEmptyThrowEx(dbSupplierBiz, "供应商业务信息不存在");

        //业务信息
        LambdaUpdateWrapper<SupplierBiz> updateBizWrapper = Wrappers.lambdaUpdate(SupplierBiz.class);
        updateBizWrapper.set(SupplierBiz::getCooperationMode, params.getCooperationMode());
        updateBizWrapper.set(SupplierBiz::getCurrencyId, params.getCurrencyId());
        updateBizWrapper.set(SupplierBiz::getSettlementModes, params.getSettlementModes());
        updateBizWrapper.set(SupplierBiz::getCreditAmount, params.getCreditAmount());
        updateBizWrapper.set(SupplierBiz::getPeriodDays, params.getPeriodDays());
        updateBizWrapper.set(SupplierBiz::getCoopStartTime, params.getCoopStartTime());
        updateBizWrapper.set(SupplierBiz::getCoopEndTime, params.getCoopEndTime());
        updateBizWrapper.set(SupplierBiz::getPaymentAgreementId, params.getPaymentAgreementId());
        updateBizWrapper.set(SupplierBiz::getPaymentAgreementCode, params.getPaymentAgreementCode());
        updateBizWrapper.set(SupplierBiz::getPaymentAgreementName, params.getPaymentAgreementName());
        updateBizWrapper.set(SupplierBiz::getPaymentAgreementYsId, params.getPaymentAgreementYsId());
        updateBizWrapper.set(SupplierBiz::getPaymentTerm, params.getPaymentTerm());
        updateBizWrapper.set(SupplierBiz::getOwnerCompany, params.getOwnerCompany());
        if (!supplierV2.getManageOrgNo().equals(params.getUseOrgNo())) {
            updateBizWrapper.set(SupplierBiz::getUseOrgModifyFlag, CommonIfEnum.YES.getValue());
        }
        updateBizWrapper.set(SupplierBiz::getOperateName, operationModel.getUserName());
        updateBizWrapper.set(SupplierBiz::getOperateNo, operationModel.getEmployerNo());
        updateBizWrapper.set(SupplierBiz::getOperateTime, DateUtil.getCurrentDate());

        //where条件
        updateBizWrapper.eq(SupplierBiz::getEnterpriseNo, operationModel.getEnterpriseNo());
        updateBizWrapper.eq(SupplierBiz::getUseOrgNo, params.getUseOrgNo());
        updateBizWrapper.eq(SupplierBiz::getSupplierCode, params.getSupplierCode());
        updateBizWrapper.eq(SupplierBiz::getDeleted, DeletedEnum.UN_DELETE.getValue());

        supplierBizDAO.update(null, updateBizWrapper);


        Map<String, Object> businessValue = new HashMap<>();
//        if (null != newSupplier) {
//            businessValue.put("supplier", newSupplier);
//        }
//        businessValue.put("supplierBiz", updateBizWrapper.getEntity());

        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                try {
                    businessLogService.saveLog(DLogLevel.INFO.name(), ViewNameConstant.BDC_SUPPLIER_VIEW, params.getSupplierCode() + params.getUseOrgNo(), "编辑供应商档案", "编辑供应商档案", JSON.toJSONString(businessValue), "");
                } catch (Exception e) {
                    log.error("编辑供应商日志保存失败", e);
                }
            }
        });

        // 分派更新
        if (!SupplierBusinessFlagEnum.DRAFT.getValue().equals(params.getBusinessFlag()) && supplierV2.getManageOrgNo().equals(params.getUseOrgNo())) {
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                @Override
                public void afterCommit() {
                    try {
                        log.warn("updateDocHandlerReq={},{},{},{},{}", operationModel.getEnterpriseNo(), BillNameConstant.BDC_SUPPLIER_BILL, params.getSupplierCode(), supplierV2.getManageOrgNo(), 0);

                        Boolean updateCallResult = gradeControlService.updateDocHandler(operationModel.getEnterpriseNo(), BillNameConstant.BDC_SUPPLIER_BILL, params.getSupplierCode(), supplierV2.getManageOrgNo(), 0);

                        log.warn("updateDocHandlerResp={}", updateCallResult);
                    } catch (Exception e) {
                        log.error("供应商档案调用分派更新失败", e);
                    }
                }
            });
        }

        return true;
    }

    private void validateAssumeManageEditBasicAndBizReq(OperationModel operationModel, SupplierAssumeManageEditBasicAndBizReq params) {
        //必填项校验
        ValidatorUtils.checkEmptyThrowEx(params.getSupplierCode(), "供应商编码不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getSupplierName(), "供应商名称不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getTransactionType(), "供应商类型不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getSupplierCategoryNo(), "供应商分类不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getCompanyName(), "企业名称不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getUnifiedSocialCode(), "统一社会信用代码不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getFactoryType(), "企业注册地域不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getTaxCategory(), "纳税类别不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getRetailInvestors(), "是否散户不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getIsAssociatedEnterprise(), "是否内部组织不能为空");
        if (null != params.getIsAssociatedEnterprise() && CommonIfEnum.YES.getValue().equals(params.getIsAssociatedEnterprise())) {
            ValidatorUtils.checkEmptyThrowEx(params.getAssociatedOrgNo(), "对应内部组织不能为空");
        }

        //范围校验
        if (StringUtils.isNotEmpty(params.getSupplierCode())) {
            ValidatorUtils.checkTrueThrowEx(params.getSupplierCode().length() > 50, "供应商编码不能大于50");
        }
        if (StringUtils.isNotEmpty(params.getSupplierName())) {
            ValidatorUtils.checkTrueThrowEx(params.getSupplierName().length() > 128, "供应商名称不能大于128");
        }
        if (StringUtils.isNotEmpty(params.getSupplierNameEn())) {
            ValidatorUtils.checkTrueThrowEx(params.getSupplierNameEn().length() > 300, "供应商英文名称不能大于300");
        }
        if (StringUtils.isNotEmpty(params.getMnemonicCode())) {
            ValidatorUtils.checkTrueThrowEx(params.getMnemonicCode().length() > 100, "助记码不能大于100");
        }
        if (StringUtils.isNotEmpty(params.getCompanyName())) {
            ValidatorUtils.checkTrueThrowEx(params.getCompanyName().length() > 128, "企业名称不能大于128");
        }
        if (StringUtils.isNotEmpty(params.getUnifiedSocialCode())) {
            ValidatorUtils.checkTrueThrowEx(params.getUnifiedSocialCode().length() > 100, "统一社会信用代码不能大于100");
        }
        if (StringUtils.isNotEmpty(params.getCountry())) {
            ValidatorUtils.checkTrueThrowEx(params.getCountry().length() > 50, "国家/地区不能大于50");
        }
        if (StringUtils.isNotEmpty(params.getRemark())) {
            ValidatorUtils.checkTrueThrowEx(params.getRemark().length() > 300, "备注不能大于300");
        }


        if (StringUtils.isNotEmpty(params.getCreditAmount())) {
            ValidatorUtils.checkTrueThrowEx(params.getCreditAmount().length() > 20, "信用额度不能大于20");
        }
        if (StringUtils.isNotEmpty(params.getOwnerCompany())) {
            ValidatorUtils.checkTrueThrowEx(params.getOwnerCompany().length() > 100, "业务归属不能大于100");
        }
    }

    @Override
    @Transactional
    public Boolean assumeManageEditSupplierBasicAndBiz(OperationModel operationModel, SupplierAssumeManageEditBasicAndBizReq params) {

        //校验入参必填、范围
        validateAssumeManageEditBasicAndBizReq(operationModel, params);

        LambdaQueryWrapper<SupplierV2> queryWrapper = Wrappers.<SupplierV2>lambdaQuery()
                .eq(SupplierV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(SupplierV2::getSupplierCode, params.getSupplierCode())
                .eq(SupplierV2::getDeleted, DeletedEnum.UN_DELETE.getValue());
        SupplierV2 supplierV2 = supplierV2DAO.selectOne(queryWrapper);
        ValidatorUtils.checkEmptyThrowEx(supplierV2, "供应商不存在");

        ValidatorUtils.checkTrueThrowEx(supplierV2DAO.selectCount(new LambdaQueryWrapper<SupplierV2>()
                .eq(SupplierV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(SupplierV2::getSupplierName, params.getSupplierName())
                .ne(SupplierV2::getSupplierCode, params.getSupplierCode())
                .eq(SupplierV2::getDeleted, DeletedEnum.UN_DELETE.getValue())) > 0, "供应商名称重复");

        if (CommonIfEnum.YES.getValue().equals(params.getIsAssociatedEnterprise())) {
            List<SupplierV2> byInternalOrgNo = findByInternalOrgNo(operationModel, params.getSupplierCode(), params.getAssociatedOrgNo());
            if (CollectionUtils.isNotEmpty(byInternalOrgNo)) {
                String errorMessage = String.format("对应内部组织已被供应商【%s】关联", String.join(",", byInternalOrgNo.stream().map(SupplierV2::getSupplierName).collect(Collectors.toSet())));
                throw new BusinessException(ErrorCode.param_invalid_msg, errorMessage);
            }
        }


        // 更新企业信息
        CompanySaveOrUpdateReq companySaveOrUpdateReq = new CompanySaveOrUpdateReq();
        BeanUtils.copyProperties(params, companySaveOrUpdateReq);
        CompanyV2 company = companyV2Service.saveOrUpdateCompany(operationModel, params.getSupplierCode(), companySaveOrUpdateReq, CompanyPartnershipEnum.SUPPLIER);

        // 更新供应商信息
        LambdaUpdateWrapper<SupplierV2> updateWrapper = Wrappers.lambdaUpdate(SupplierV2.class);
        updateWrapper.set(SupplierV2::getSupplierName, params.getSupplierName());
        updateWrapper.set(SupplierV2::getSupplierNameEn, params.getSupplierNameEn());
        updateWrapper.set(SupplierV2::getMnemonicCode, params.getMnemonicCode());
        updateWrapper.set(SupplierV2::getTransactionType, params.getTransactionType());
        updateWrapper.set(SupplierV2::getSupplierCategoryNo, params.getSupplierCategoryNo());

        updateWrapper.set(SupplierV2::getCompanyNo, company.getCompanyNo());
        updateWrapper.set(SupplierV2::getUnifiedSocialCode, company.getUnifiedSocialCode());

        updateWrapper.set(SupplierV2::getRetailInvestors, params.getRetailInvestors());

        updateWrapper.set(SupplierV2::getIsAssociatedEnterprise, params.getIsAssociatedEnterprise());
        if (CommonIfEnum.YES.getValue().equals(params.getIsAssociatedEnterprise())) {
            updateWrapper.set(SupplierV2::getAssociatedOrgNo, params.getAssociatedOrgNo());
            updateWrapper.set(SupplierV2::getAssociatedOrgCode, params.getAssociatedOrgCode());
            updateWrapper.set(SupplierV2::getAssociatedOrgName, params.getAssociatedOrgName());
        } else {
            updateWrapper.set(SupplierV2::getAssociatedOrgNo, null);
            updateWrapper.set(SupplierV2::getAssociatedOrgCode, null);
            updateWrapper.set(SupplierV2::getAssociatedOrgName, null);
        }

        updateWrapper.set(SupplierV2::getRemark, params.getRemark());

        updateWrapper.set(SupplierV2::getIsGspControl, CommonIfEnum.YES.getValue());
        updateWrapper.set(SupplierV2::getBusinessFlag, SupplierBusinessFlagEnum.DRAFT.getValue().equals(params.getBusinessFlag()) ? SupplierBusinessFlagEnum.DRAFT.getValue() : SupplierBusinessFlagEnum.FORMAL.getValue());

        updateWrapper.set(SupplierV2::getOperateName, operationModel.getUserName());
        updateWrapper.set(SupplierV2::getOperateNo, operationModel.getEmployerNo());
        updateWrapper.set(SupplierV2::getOperateTime, DateUtil.getCurrentDate());

        // where条件
        updateWrapper.eq(SupplierV2::getEnterpriseNo, operationModel.getEnterpriseNo());
        updateWrapper.eq(SupplierV2::getManageOrgNo, supplierV2.getManageOrgNo());
        updateWrapper.eq(SupplierV2::getSupplierCode, params.getSupplierCode());
        updateWrapper.eq(SupplierV2::getDeleted, DeletedEnum.UN_DELETE.getValue());

        supplierV2DAO.update(null, updateWrapper);


        //更新使用组织的业务信息
        LambdaQueryWrapper<SupplierBiz> bizQueryWrapper = Wrappers.<SupplierBiz>lambdaQuery()
                .eq(SupplierBiz::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(SupplierBiz::getUseOrgNo, params.getUseOrgNo())
                .eq(SupplierBiz::getSupplierCode, params.getSupplierCode())
                .eq(SupplierBiz::getDeleted, DeletedEnum.UN_DELETE.getValue());
        SupplierBiz dbSupplierBiz = supplierBizDAO.selectOne(bizQueryWrapper);
        ValidatorUtils.checkEmptyThrowEx(dbSupplierBiz, "供应商业务信息不存在");

        LambdaUpdateWrapper<SupplierBiz> updateBizWrapper = Wrappers.lambdaUpdate(SupplierBiz.class);
        updateBizWrapper.set(SupplierBiz::getCooperationMode, params.getCooperationMode());
        updateBizWrapper.set(SupplierBiz::getCurrencyId, params.getCurrencyId());
        updateBizWrapper.set(SupplierBiz::getSettlementModes, params.getSettlementModes());
        updateBizWrapper.set(SupplierBiz::getCreditAmount, params.getCreditAmount());
        updateBizWrapper.set(SupplierBiz::getPeriodDays, params.getPeriodDays());
        updateBizWrapper.set(SupplierBiz::getCoopStartTime, params.getCoopStartTime());
        updateBizWrapper.set(SupplierBiz::getCoopEndTime, params.getCoopEndTime());
        updateBizWrapper.set(SupplierBiz::getPaymentAgreementId, params.getPaymentAgreementId());
        updateBizWrapper.set(SupplierBiz::getPaymentAgreementCode, params.getPaymentAgreementCode());
        updateBizWrapper.set(SupplierBiz::getPaymentAgreementName, params.getPaymentAgreementName());
        updateBizWrapper.set(SupplierBiz::getPaymentAgreementYsId, params.getPaymentAgreementYsId());
        updateBizWrapper.set(SupplierBiz::getPaymentTerm, params.getPaymentTerm());
        updateBizWrapper.set(SupplierBiz::getOwnerCompany, params.getOwnerCompany());
        if (!supplierV2.getManageOrgNo().equals(params.getUseOrgNo())) {
            updateBizWrapper.set(SupplierBiz::getUseOrgModifyFlag, CommonIfEnum.YES.getValue());
        }
        updateBizWrapper.set(SupplierBiz::getOperateName, operationModel.getUserName());
        updateBizWrapper.set(SupplierBiz::getOperateNo, operationModel.getEmployerNo());
        updateBizWrapper.set(SupplierBiz::getOperateTime, DateUtil.getCurrentDate());

        //where条件
        updateBizWrapper.eq(SupplierBiz::getEnterpriseNo, operationModel.getEnterpriseNo());
        updateBizWrapper.eq(SupplierBiz::getUseOrgNo, params.getUseOrgNo());
        updateBizWrapper.eq(SupplierBiz::getSupplierCode, params.getSupplierCode());
        updateBizWrapper.eq(SupplierBiz::getDeleted, DeletedEnum.UN_DELETE.getValue());

        supplierBizDAO.update(null, updateBizWrapper);


        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                try {
                    businessLogService.saveLog(DLogLevel.INFO.name(), ViewNameConstant.BDC_SUPPLIER_VIEW, params.getSupplierCode() + params.getUseOrgNo(), "编辑供应商档案", "编辑供应商档案", "", "");
                } catch (Exception e) {
                    log.error("编辑供应商日志保存失败", e);
                }
            }
        });

        return true;
    }

    @Override
    public String preValidateAssumeManageEditSupplierBasicAndBiz(OperationModel operationModel, SupplierAssumeManageEditBasicAndBizReq params) {

        //校验入参必填、范围
        validateAssumeManageEditBasicAndBizReq(operationModel, params);

        LambdaQueryWrapper<SupplierV2> queryWrapper = Wrappers.<SupplierV2>lambdaQuery()
                .eq(SupplierV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(SupplierV2::getSupplierCode, params.getSupplierCode())
                .eq(SupplierV2::getDeleted, DeletedEnum.UN_DELETE.getValue());
        SupplierV2 supplierV2 = supplierV2DAO.selectOne(queryWrapper);
        ValidatorUtils.checkEmptyThrowEx(supplierV2, "供应商不存在");

        ValidatorUtils.checkTrueThrowEx(supplierV2DAO.selectCount(new LambdaQueryWrapper<SupplierV2>()
                .eq(SupplierV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(SupplierV2::getSupplierName, params.getSupplierName())
                .ne(SupplierV2::getSupplierCode, params.getSupplierCode())
                .eq(SupplierV2::getDeleted, DeletedEnum.UN_DELETE.getValue())) > 0, "供应商名称重复");

        if (CommonIfEnum.YES.getValue().equals(params.getIsAssociatedEnterprise())) {
            List<SupplierV2> byInternalOrgNo = findByInternalOrgNo(operationModel, params.getSupplierCode(), params.getAssociatedOrgNo());
            if (CollectionUtils.isNotEmpty(byInternalOrgNo)) {
                String errorMessage = String.format("对应内部组织已被供应商【%s】关联", String.join(",", byInternalOrgNo.stream().map(SupplierV2::getSupplierName).collect(Collectors.toSet())));
                throw new BusinessException(ErrorCode.param_invalid_msg, errorMessage);
            }
        }


        // 更新企业信息
        CompanySaveOrUpdateReq companySaveOrUpdateReq = new CompanySaveOrUpdateReq();
        BeanUtils.copyProperties(params, companySaveOrUpdateReq);
        return companyV2Service.preValidateSaveOrUpdateCompany(operationModel, params.getSupplierCode(), companySaveOrUpdateReq, CompanyPartnershipEnum.SUPPLIER);

    }

    @Override
    public Boolean editSupplierBasic(OperationModel operationModel, SupplierEditBasicReq params) {
        //校验入参必填、范围
        validateEditBasicReq(operationModel, params);

        SupplierV2 supplierV2 = getSupplierByCode(operationModel, params.getSupplierCode());
        ValidatorUtils.checkEmptyThrowEx(supplierV2, "供应商不存在");

        ValidatorUtils.checkTrueThrowEx(supplierV2DAO.selectCount(new LambdaQueryWrapper<SupplierV2>()
                .eq(SupplierV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(SupplierV2::getSupplierName, params.getSupplierName())
                .ne(SupplierV2::getSupplierCode, params.getSupplierCode())
                .eq(SupplierV2::getDeleted, DeletedEnum.UN_DELETE.getValue())) > 0, "供应商名称重复");

        if (CommonIfEnum.YES.getValue().equals(params.getIsAssociatedEnterprise())) {
            List<SupplierV2> byInternalOrgNo = findByInternalOrgNo(operationModel, params.getSupplierCode(), params.getAssociatedOrgNo());
            if (CollectionUtils.isNotEmpty(byInternalOrgNo)) {
                String errorMessage = String.format("对应内部组织已被供应商【%s】关联", String.join(",", byInternalOrgNo.stream().map(SupplierV2::getSupplierName).collect(Collectors.toSet())));
                throw new BusinessException(ErrorCode.param_invalid_msg, errorMessage);
            }
        }

        // 处理企业
        CompanySaveOrUpdateReq companySaveOrUpdateReq = new CompanySaveOrUpdateReq();
        BeanUtils.copyProperties(params, companySaveOrUpdateReq);
        CompanyV2 company = companyV2Service.saveOrUpdateCompany(operationModel, params.getSupplierCode(), companySaveOrUpdateReq, CompanyPartnershipEnum.SUPPLIER);

        // 处理企业银行
        companyV2Service.saveOrUpdateBank(operationModel, params.getBankList(), company.getCompanyNo(), supplierV2.getManageOrgNo());

        // 处理供应商
        LambdaUpdateWrapper<SupplierV2> updateWrapper = Wrappers.lambdaUpdate(SupplierV2.class);
        updateWrapper.set(SupplierV2::getSupplierName, params.getSupplierName());
        updateWrapper.set(SupplierV2::getSupplierNameEn, params.getSupplierNameEn());
        updateWrapper.set(SupplierV2::getMnemonicCode, params.getMnemonicCode());
        updateWrapper.set(SupplierV2::getTransactionType, params.getTransactionType());
        updateWrapper.set(SupplierV2::getSupplierCategoryNo, params.getSupplierCategoryNo());

        updateWrapper.set(SupplierV2::getCompanyNo, company.getCompanyNo());
        updateWrapper.set(SupplierV2::getUnifiedSocialCode, company.getUnifiedSocialCode());

        updateWrapper.set(SupplierV2::getRetailInvestors, params.getRetailInvestors());

        updateWrapper.set(SupplierV2::getIsAssociatedEnterprise, params.getIsAssociatedEnterprise());
        if (CommonIfEnum.YES.getValue().equals(params.getIsAssociatedEnterprise())) {
            updateWrapper.set(SupplierV2::getAssociatedOrgNo, params.getAssociatedOrgNo());
            updateWrapper.set(SupplierV2::getAssociatedOrgCode, params.getAssociatedOrgCode());
            updateWrapper.set(SupplierV2::getAssociatedOrgName, params.getAssociatedOrgName());
        } else {
            updateWrapper.set(SupplierV2::getAssociatedOrgNo, null);
            updateWrapper.set(SupplierV2::getAssociatedOrgCode, null);
            updateWrapper.set(SupplierV2::getAssociatedOrgName, null);
        }

        updateWrapper.set(SupplierV2::getRemark, params.getRemark());

        updateWrapper.set(SupplierV2::getIsGspControl, CommonIfEnum.YES.getValue());
        updateWrapper.set(SupplierV2::getBusinessFlag, SupplierBusinessFlagEnum.FORMAL.getValue());

        updateWrapper.set(SupplierV2::getOperateName, operationModel.getUserName());
        updateWrapper.set(SupplierV2::getOperateNo, operationModel.getEmployerNo());
        updateWrapper.set(SupplierV2::getOperateTime, DateUtil.getCurrentDate());

        // where条件
        updateWrapper.eq(SupplierV2::getEnterpriseNo, operationModel.getEnterpriseNo());
        updateWrapper.eq(SupplierV2::getManageOrgNo, supplierV2.getManageOrgNo());
        updateWrapper.eq(SupplierV2::getSupplierCode, params.getSupplierCode());
        updateWrapper.eq(SupplierV2::getDeleted, DeletedEnum.UN_DELETE.getValue());

        int update = supplierV2DAO.update(null, updateWrapper);

        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                try {
                    businessLogService.saveLog(DLogLevel.INFO.name(), ViewNameConstant.BDC_SUPPLIER_VIEW, params.getSupplierCode() + params.getManageOrgNo(), "供应商档案变更申请编辑", "供应商档案变更申请编辑", "", "");
                } catch (Exception e) {
                    log.error("供应商档案变更申请编辑日志保存失败", e);
                }
            }
        });

        return update > 0;
    }

    @Override
    public String preValidateEditSupplierBasic(OperationModel operationModel, SupplierEditBasicReq params) {
        //校验入参必填、范围
        validateEditBasicReq(operationModel, params);

        SupplierV2 supplierV2 = getSupplierByCode(operationModel, params.getSupplierCode());
        ValidatorUtils.checkEmptyThrowEx(supplierV2, "供应商不存在");

        ValidatorUtils.checkTrueThrowEx(supplierV2DAO.selectCount(new LambdaQueryWrapper<SupplierV2>()
                .eq(SupplierV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(SupplierV2::getSupplierName, params.getSupplierName())
                .ne(SupplierV2::getSupplierCode, params.getSupplierCode())
                .eq(SupplierV2::getDeleted, DeletedEnum.UN_DELETE.getValue())) > 0, "供应商名称重复");

        if (CommonIfEnum.YES.getValue().equals(params.getIsAssociatedEnterprise())) {
            List<SupplierV2> byInternalOrgNo = findByInternalOrgNo(operationModel, params.getSupplierCode(), params.getAssociatedOrgNo());
            if (CollectionUtils.isNotEmpty(byInternalOrgNo)) {
                String errorMessage = String.format("对应内部组织已被供应商【%s】关联", String.join(",", byInternalOrgNo.stream().map(SupplierV2::getSupplierName).collect(Collectors.toSet())));
                throw new BusinessException(ErrorCode.param_invalid_msg, errorMessage);
            }
        }

        // 处理企业
        CompanySaveOrUpdateReq companySaveOrUpdateReq = new CompanySaveOrUpdateReq();
        BeanUtils.copyProperties(params, companySaveOrUpdateReq);
        return companyV2Service.preValidateSaveOrUpdateCompany(operationModel, params.getSupplierCode(), companySaveOrUpdateReq, CompanyPartnershipEnum.SUPPLIER);

    }

    private void editSupplierAddressListValidate(OperationModel operationModel, SupplierAddressListEditReq params) {
        if (CollectionUtils.isEmpty(params.getLinkAddressList())) {
            return;
        }

        for (CompanyShippingAddressReq companyShippingAddressReq : params.getLinkAddressList()) {
            //必填项校验
            ValidatorUtils.checkEmptyThrowEx(companyShippingAddressReq.getReceiveUser(), "联系人不能为空");
            ValidatorUtils.checkEmptyThrowEx(companyShippingAddressReq.getReceivePhone(), "联系电话不能为空");
            ValidatorUtils.checkEmptyThrowEx(companyShippingAddressReq.getAddressType(), "地址类型不能为空");
            ValidatorUtils.checkEmptyThrowEx(companyShippingAddressReq.getRegionCode(), "行政区域不能为空");
            ValidatorUtils.checkEmptyThrowEx(companyShippingAddressReq.getReceiveAddr(), "详细地址不能为空");
            ValidatorUtils.checkEmptyThrowEx(companyShippingAddressReq.getIsDefault(), "是否默认不能为空");

            //范围校验
            ValidatorUtils.checkTrueThrowEx(companyShippingAddressReq.getReceiveUser().length() > 50, "联系人不能大于50");
            ValidatorUtils.checkTrueThrowEx(companyShippingAddressReq.getReceivePhone().length() > 100, "联系电话不能大于100");
            ValidatorUtils.checkTrueThrowEx(companyShippingAddressReq.getReceiveAddr().length() > 300, "详细地址不能大于300");
            if (StringUtils.isNotEmpty(companyShippingAddressReq.getAddressDesc())) {
                ValidatorUtils.checkTrueThrowEx(companyShippingAddressReq.getAddressDesc().length() > 100, "地址描述不能大于100");
            }
        }
    }

    @Override
    @Transactional
    public List<Long> editSupplierAddressList(OperationModel operationModel, SupplierAddressListEditReq params) {
        // 参数校验
        editSupplierAddressListValidate(operationModel, params);

        SupplierV2 supplier = supplierV2DAO.selectOne(new LambdaQueryWrapper<SupplierV2>()
                .eq(SupplierV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(SupplierV2::getSupplierCode, params.getSupplierCode())
                .eq(SupplierV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
        if (null == supplier) {
            throw new BusinessException(ErrorCode.param_invalid_msg, "供应商不存在");
        }

        boolean hasBase = supplierBaseDAO.exists(new LambdaQueryWrapper<SupplierBase>()
                .eq(SupplierBase::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(SupplierBase::getSupplierCode, params.getSupplierCode())
                .eq(SupplierBase::getUseOrgNo, params.getUseOrgNo())
                .eq(SupplierBase::getDeleted, DeletedEnum.UN_DELETE.getValue()));
        ValidatorUtils.checkTrueThrowEx(!hasBase, "供应商未分派");


        List<CompanyShippingAddressV2> companyShippingAddressV2s = companyShippingAddressV2DAO.selectList(Wrappers.<CompanyShippingAddressV2>lambdaQuery()
                .eq(CompanyShippingAddressV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CompanyShippingAddressV2::getUseOrgNo, params.getUseOrgNo())
                .eq(CompanyShippingAddressV2::getSourceNo, params.getSupplierCode())
                .eq(CompanyShippingAddressV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
        Map<String, List<?>> diffResultMap = BeanUtil.diffCUD(companyShippingAddressV2s, params.getLinkAddressList());

        List<CompanyShippingAddressReq> addList = (List<CompanyShippingAddressReq>) diffResultMap.get("addList");
        List<CompanyShippingAddressReq> updateList = (List<CompanyShippingAddressReq>) diffResultMap.get("updateList");
        List<CompanyShippingAddressV2> deleteList = (List<CompanyShippingAddressV2>) diffResultMap.get("deleteList");


        List<CompanyShippingAddressV2> saveShippingAddressListList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(addList)) {
            for (CompanyShippingAddressReq r : addList) {
                CompanyShippingAddressV2 shippingAddress = new CompanyShippingAddressV2();
                shippingAddress.setEnterpriseNo(operationModel.getEnterpriseNo());
                shippingAddress.setUseOrgNo(params.getUseOrgNo());
//            shippingAddress.setCompanyNo(supplier.getCompanyNo());
                shippingAddress.setLinkAddressCode(r.getLinkAddressCode());
                shippingAddress.setSourceNo(params.getSupplierCode());
                shippingAddress.setStatus(CommonStatusEnum.EFFECTIVE.getValue());
                shippingAddress.setLinkaddType(LinkmanTypeEnum.SUPPLY.getValue());
                shippingAddress.setReceiveUser(r.getReceiveUser());
                shippingAddress.setReceivePhone(r.getReceivePhone());
                shippingAddress.setRegionCode(r.getRegionCode());
                shippingAddress.setRegionName(r.getRegionName());
                shippingAddress.setReceiveAddr(r.getReceiveAddr());
                shippingAddress.setIsDefault(r.getIsDefault());
                shippingAddress.setAddressDesc(r.getAddressDesc());
                shippingAddress.setAddressType(r.getAddressType());
                saveShippingAddressListList.add(shippingAddress);
            }

            companyShippingAddressV2DAO.addBatch(saveShippingAddressListList);
        }


        if (CollectionUtils.isNotEmpty(updateList)) {
            List<CompanyShippingAddressV2> updateBankList = new ArrayList<>();
            for (CompanyShippingAddressReq r : updateList) {
                CompanyShippingAddressV2 shippingAddress = new CompanyShippingAddressV2();
                shippingAddress.setId(r.getId());
                shippingAddress.setEnterpriseNo(operationModel.getEnterpriseNo());
                shippingAddress.setUseOrgNo(params.getUseOrgNo());
//            shippingAddress.setCompanyNo(supplier.getCompanyNo());
                shippingAddress.setLinkAddressCode(r.getLinkAddressCode());
                shippingAddress.setSourceNo(params.getSupplierCode());
                shippingAddress.setStatus(CommonStatusEnum.EFFECTIVE.getValue());
                shippingAddress.setLinkaddType(LinkmanTypeEnum.SUPPLY.getValue());
                shippingAddress.setReceiveUser(r.getReceiveUser());
                shippingAddress.setReceivePhone(r.getReceivePhone());
                shippingAddress.setRegionCode(r.getRegionCode());
                shippingAddress.setRegionName(r.getRegionName());
                shippingAddress.setReceiveAddr(r.getReceiveAddr());
                shippingAddress.setIsDefault(r.getIsDefault());
                shippingAddress.setAddressDesc(r.getAddressDesc());
                shippingAddress.setAddressType(r.getAddressType());
                updateBankList.add(shippingAddress);
            }
            companyShippingAddressV2DAO.updateByIdBatch(updateBankList);
        }


        if (CollectionUtils.isNotEmpty(deleteList)) {
            CompanyShippingAddressV2 shippingAddress = new CompanyShippingAddressV2();
            shippingAddress.setDeleted(DeletedEnum.DELETED.getValue());
            companyShippingAddressV2DAO.update(shippingAddress, Wrappers.<CompanyShippingAddressV2>lambdaQuery()
                    .eq(CompanyShippingAddressV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                    .in(CompanyShippingAddressV2::getId, deleteList.stream().map(CompanyShippingAddressV2::getId).collect(Collectors.toList())));
        }

        return saveShippingAddressListList.stream().map(CompanyShippingAddressV2::getId).collect(Collectors.toList());
    }

    @Override
    @Transactional
    public List<CompanyShippingAddressV2> overwriteSupplierAddressList(OperationModel operationModel, SupplierAddressListEditReq params) {
        CompanyShippingAddressV2 toDeleteShippingAddress = new CompanyShippingAddressV2();
        toDeleteShippingAddress.setDeleted(DeletedEnum.DELETED.getValue());
        companyShippingAddressV2DAO.update(toDeleteShippingAddress, Wrappers.<CompanyShippingAddressV2>lambdaQuery()
                .eq(CompanyShippingAddressV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CompanyShippingAddressV2::getUseOrgNo, params.getUseOrgNo())
                .eq(CompanyShippingAddressV2::getSourceNo, params.getSupplierCode())
                .eq(CompanyShippingAddressV2::getLinkaddType, LinkmanTypeEnum.SUPPLY.getValue())
                .eq(CompanyShippingAddressV2::getDeleted, DeletedEnum.UN_DELETE.getValue())
        );

        if (CollectionUtils.isNotEmpty(params.getLinkAddressList())) {
            List<CompanyShippingAddressV2> saveShippingAddressListList = new ArrayList<>();

            for (CompanyShippingAddressReq r : params.getLinkAddressList()) {
                CompanyShippingAddressV2 shippingAddress = new CompanyShippingAddressV2();
                shippingAddress.setEnterpriseNo(operationModel.getEnterpriseNo());
                shippingAddress.setUseOrgNo(params.getUseOrgNo());
//            shippingAddress.setCompanyNo(supplier.getCompanyNo());
                shippingAddress.setLinkAddressCode(r.getLinkAddressCode());
                shippingAddress.setSourceNo(params.getSupplierCode());
                shippingAddress.setStatus(CommonStatusEnum.EFFECTIVE.getValue());
                shippingAddress.setLinkaddType(LinkmanTypeEnum.SUPPLY.getValue());
                shippingAddress.setReceiveUser(r.getReceiveUser());
                shippingAddress.setReceivePhone(r.getReceivePhone());
                shippingAddress.setRegionCode(r.getRegionCode());
                shippingAddress.setRegionName(r.getRegionName());
                shippingAddress.setReceiveAddr(r.getReceiveAddr());
                shippingAddress.setIsDefault(r.getIsDefault());
                shippingAddress.setAddressDesc(r.getAddressDesc());
                shippingAddress.setAddressType(r.getAddressType());
                saveShippingAddressListList.add(shippingAddress);
            }

            companyShippingAddressV2DAO.addBatch(saveShippingAddressListList);

            return saveShippingAddressListList;
        }

        return Collections.emptyList();
    }

    @Override
    public void preValidateEditSupplierAddressList(OperationModel operationModel, SupplierAddressListEditReq params) {
        // 参数校验
        editSupplierAddressListValidate(operationModel, params);
    }

    private void editSupplierLinkmanListValidate(OperationModel operationModel, SupplierLinkmanListEditReq params) {
        if (CollectionUtils.isEmpty(params.getLinkmanList())) {
            return;
        }

        for (CompanyLinkmanReq companyLinkmanReq : params.getLinkmanList()) {
            //必填项校验
            ValidatorUtils.checkEmptyThrowEx(companyLinkmanReq.getLinkman(), "联系人不能为空");

            //范围校验
            ValidatorUtils.checkTrueThrowEx(companyLinkmanReq.getLinkman().length() > 50, "联系人不能大于50");

            if (StringUtils.isNotEmpty(companyLinkmanReq.getPosition())) {
                ValidatorUtils.checkTrueThrowEx(companyLinkmanReq.getPosition().length() > 100, "职位不能大于100");
            }

            if (StringUtils.isNotEmpty(companyLinkmanReq.getFixedPhone())) {
                ValidatorUtils.checkTrueThrowEx(companyLinkmanReq.getFixedPhone().length() > 100, "电话不能大于100");
            }

            if (StringUtils.isNotEmpty(companyLinkmanReq.getMobilePhone())) {
                ValidatorUtils.checkTrueThrowEx(companyLinkmanReq.getMobilePhone().length() > 100, "手机不能大于100");
            }

            if (StringUtils.isNotEmpty(companyLinkmanReq.getQq())) {
                ValidatorUtils.checkTrueThrowEx(companyLinkmanReq.getQq().length() > 100, "QQ不能大于100");
            }

            if (StringUtils.isNotEmpty(companyLinkmanReq.getWx())) {
                ValidatorUtils.checkTrueThrowEx(companyLinkmanReq.getWx().length() > 100, "微信不能大于100");
            }

            if (StringUtils.isNotEmpty(companyLinkmanReq.getEmail())) {
                ValidatorUtils.checkTrueThrowEx(companyLinkmanReq.getEmail().length() > 100, "邮箱不能大于100");
            }
        }
    }

    @Override
    @Transactional
    public List<Long> editSupplierLinkmanList(OperationModel operationModel, SupplierLinkmanListEditReq params) {
        // 参数校验
        editSupplierLinkmanListValidate(operationModel, params);

        SupplierV2 supplier = supplierV2DAO.selectOne(new LambdaQueryWrapper<SupplierV2>()
                .eq(SupplierV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(SupplierV2::getSupplierCode, params.getSupplierCode())
                .eq(SupplierV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
        if (null == supplier) {
            throw new BusinessException(ErrorCode.param_invalid_msg, "供应商不存在");
        }

        boolean hasBase = supplierBaseDAO.exists(new LambdaQueryWrapper<SupplierBase>()
                .eq(SupplierBase::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(SupplierBase::getSupplierCode, params.getSupplierCode())
                .eq(SupplierBase::getUseOrgNo, params.getUseOrgNo())
                .eq(SupplierBase::getDeleted, DeletedEnum.UN_DELETE.getValue()));
        ValidatorUtils.checkTrueThrowEx(!hasBase, "供应商未分派");


        List<CompanyLinkmanV2> companyLinkmanV2s = companyLinkmanV2DAO.selectList(Wrappers.<CompanyLinkmanV2>lambdaQuery()
                .eq(CompanyLinkmanV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CompanyLinkmanV2::getUseOrgNo, params.getUseOrgNo())
                .eq(CompanyLinkmanV2::getSourceNo, params.getSupplierCode())
                .eq(CompanyLinkmanV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
        Map<String, List<?>> diffResultMap = BeanUtil.diffCUD(companyLinkmanV2s, params.getLinkmanList());

        List<CompanyLinkmanReq> addList = (List<CompanyLinkmanReq>) diffResultMap.get("addList");
        List<CompanyLinkmanReq> updateList = (List<CompanyLinkmanReq>) diffResultMap.get("updateList");
        List<CompanyLinkmanV2> deleteList = (List<CompanyLinkmanV2>) diffResultMap.get("deleteList");


        List<CompanyLinkmanV2> companyLinkmanList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(addList)) {
            for (CompanyLinkmanReq r : addList) {
                //应该走新增逻辑
                CompanyLinkmanV2 companyLinkman = new CompanyLinkmanV2();
                companyLinkman.setEnterpriseNo(operationModel.getEnterpriseNo());
                companyLinkman.setUseOrgNo(params.getUseOrgNo());
//            companyLinkman.setCompanyNo(supplier.getCompanyNo());
                companyLinkman.setLinkCode(r.getLinkCode());
                companyLinkman.setLinkman(r.getLinkman());
                companyLinkman.setPosition(r.getPosition());
                companyLinkman.setMobilePhone(r.getMobilePhone());
                companyLinkman.setSex(r.getSex());
                companyLinkman.setFixedPhone(r.getFixedPhone());
                companyLinkman.setQq(r.getQq());
                companyLinkman.setWx(r.getWx());
                companyLinkman.setLinkmanType(LinkmanTypeEnum.SUPPLY.getValue());
                companyLinkman.setStatus(CommonStatusEnum.EFFECTIVE.getValue());
                companyLinkman.setSourceNo(params.getSupplierCode());
                companyLinkman.setEmail(r.getEmail());
                companyLinkman.setIsDefault(r.getIsDefault());
                companyLinkman.setStatus(r.getStatus());
                companyLinkmanList.add(companyLinkman);
            }

            companyLinkmanV2DAO.addBatch(companyLinkmanList);
        }

        if (CollectionUtils.isNotEmpty(updateList)) {
            List<CompanyLinkmanV2> companyLinkmanUpdateList = new ArrayList<>();

            for (CompanyLinkmanReq r : updateList) {
                CompanyLinkmanV2 companyLinkman = new CompanyLinkmanV2();
                companyLinkman.setId(r.getId());
                companyLinkman.setEnterpriseNo(operationModel.getEnterpriseNo());
                companyLinkman.setUseOrgNo(params.getUseOrgNo());
//            companyLinkman.setCompanyNo(supplier.getCompanyNo());
                companyLinkman.setLinkCode(r.getLinkCode());
                companyLinkman.setLinkman(r.getLinkman());
                companyLinkman.setPosition(r.getPosition());
                companyLinkman.setMobilePhone(r.getMobilePhone());
                companyLinkman.setSex(r.getSex());
                companyLinkman.setFixedPhone(r.getFixedPhone());
                companyLinkman.setQq(r.getQq());
                companyLinkman.setWx(r.getWx());
                companyLinkman.setLinkmanType(LinkmanTypeEnum.SUPPLY.getValue());
                companyLinkman.setStatus(CommonStatusEnum.EFFECTIVE.getValue());
                companyLinkman.setSourceNo(params.getSupplierCode());
                companyLinkman.setEmail(r.getEmail());
                companyLinkman.setIsDefault(r.getIsDefault());
                companyLinkman.setStatus(r.getStatus());

                companyLinkmanUpdateList.add(companyLinkman);
            }

            companyLinkmanV2DAO.updateByIdBatch(companyLinkmanUpdateList);
        }

        if (CollectionUtils.isNotEmpty(deleteList)) {
            CompanyLinkmanV2 linkman = new CompanyLinkmanV2();
            linkman.setDeleted(DeletedEnum.DELETED.getValue());
            companyLinkmanV2DAO.update(linkman, Wrappers.<CompanyLinkmanV2>lambdaQuery()
                    .eq(CompanyLinkmanV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                    .in(CompanyLinkmanV2::getId, deleteList.stream().map(CompanyLinkmanV2::getId).collect(Collectors.toList())));
        }

        return companyLinkmanList.stream().map(CompanyLinkmanV2::getId).collect(Collectors.toList());
    }

    @Override
    @Transactional
    public List<CompanyLinkmanV2> overwriteSupplierLinkmanList(OperationModel operationModel, SupplierLinkmanListEditReq params) {
        CompanyLinkmanV2 toDeleteLinkman = new CompanyLinkmanV2();
        toDeleteLinkman.setDeleted(DeletedEnum.DELETED.getValue());
        companyLinkmanV2DAO.update(toDeleteLinkman, Wrappers.<CompanyLinkmanV2>lambdaQuery()
                .eq(CompanyLinkmanV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CompanyLinkmanV2::getUseOrgNo, params.getUseOrgNo())
                .eq(CompanyLinkmanV2::getSourceNo, params.getSupplierCode())
                .eq(CompanyLinkmanV2::getLinkmanType, LinkmanTypeEnum.SUPPLY.getValue())
                .eq(CompanyLinkmanV2::getDeleted, DeletedEnum.UN_DELETE.getValue())
        );

        if (CollectionUtils.isNotEmpty(params.getLinkmanList())) {
            List<CompanyLinkmanV2> companyLinkmanList = new ArrayList<>();
            for (CompanyLinkmanReq r : params.getLinkmanList()) {
                //应该走新增逻辑
                CompanyLinkmanV2 companyLinkman = new CompanyLinkmanV2();
                companyLinkman.setEnterpriseNo(operationModel.getEnterpriseNo());
                companyLinkman.setUseOrgNo(params.getUseOrgNo());
//            companyLinkman.setCompanyNo(supplier.getCompanyNo());
                companyLinkman.setLinkCode(r.getLinkCode());
                companyLinkman.setLinkman(r.getLinkman());
                companyLinkman.setPosition(r.getPosition());
                companyLinkman.setMobilePhone(r.getMobilePhone());
                companyLinkman.setSex(r.getSex());
                companyLinkman.setFixedPhone(r.getFixedPhone());
                companyLinkman.setQq(r.getQq());
                companyLinkman.setWx(r.getWx());
                companyLinkman.setLinkmanType(LinkmanTypeEnum.SUPPLY.getValue());
                companyLinkman.setStatus(CommonStatusEnum.EFFECTIVE.getValue());
                companyLinkman.setSourceNo(params.getSupplierCode());
                companyLinkman.setEmail(r.getEmail());
                companyLinkman.setIsDefault(r.getIsDefault());
                companyLinkman.setStatus(r.getStatus());

                companyLinkmanList.add(companyLinkman);
            }

            companyLinkmanV2DAO.addBatch(companyLinkmanList);

            return companyLinkmanList;
        }

        return Collections.emptyList();
    }

    @Override
    public void preValidateEditSupplierLinkmanList(OperationModel operationModel, SupplierLinkmanListEditReq params) {
        // 参数校验
        editSupplierLinkmanListValidate(operationModel, params);
    }

    private void editSupplierOrdermanListValidate(OperationModel operationModel, SupplierOrdermanListEditReq params) {
        if (CollectionUtils.isEmpty(params.getSupplierManList())) {
            return;
        }

        for (SupplierOrderManReq supplierOrderManReq : params.getSupplierManList()) {
            //必填项校验
            ValidatorUtils.checkEmptyThrowEx(supplierOrderManReq.getDeptNo(), "部门不能为空");
            ValidatorUtils.checkEmptyThrowEx(supplierOrderManReq.getOrderManNo(), "员工不能为空");
            ValidatorUtils.checkEmptyThrowEx(supplierOrderManReq.getOrderSpecialist(), "订单专员不能为空");
            ValidatorUtils.checkEmptyThrowEx(supplierOrderManReq.getIsDefault(), "采购员不能为空");

            //范围校验
            if (StringUtils.isNotEmpty(supplierOrderManReq.getOrderManName())) {
                ValidatorUtils.checkTrueThrowEx(supplierOrderManReq.getOrderManName().length() > 255, "员工姓名不能大于255");
            }
            if (StringUtils.isNotEmpty(supplierOrderManReq.getPost())) {
                ValidatorUtils.checkTrueThrowEx(supplierOrderManReq.getPost().length() > 100, "职务不能大于100");
            }
        }
    }

    @Override
    @Transactional
    public List<Long> editSupplierOrdermanList(OperationModel operationModel, SupplierOrdermanListEditReq params) {
        // 参数校验
        editSupplierOrdermanListValidate(operationModel, params);

        SupplierV2 supplier = supplierV2DAO.selectOne(new LambdaQueryWrapper<SupplierV2>()
                .eq(SupplierV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(SupplierV2::getSupplierCode, params.getSupplierCode())
                .eq(SupplierV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
        if (null == supplier) {
            throw new BusinessException(ErrorCode.param_invalid_msg, "供应商不存在");
        }

        boolean hasBase = supplierBaseDAO.exists(new LambdaQueryWrapper<SupplierBase>()
                .eq(SupplierBase::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(SupplierBase::getSupplierCode, params.getSupplierCode())
                .eq(SupplierBase::getUseOrgNo, params.getUseOrgNo())
                .eq(SupplierBase::getDeleted, DeletedEnum.UN_DELETE.getValue()));
        ValidatorUtils.checkTrueThrowEx(!hasBase, "供应商未分派");


        List<SupplierOrderManV2> orderManV2s = supplierOrderManV2DAO.selectList(Wrappers.<SupplierOrderManV2>lambdaQuery()
                .eq(SupplierOrderManV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(SupplierOrderManV2::getUseOrgNo, params.getUseOrgNo())
                .eq(SupplierOrderManV2::getSupplierCode, params.getSupplierCode())
                .eq(SupplierOrderManV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
        Map<String, List<?>> diffResultMap = BeanUtil.diffCUD(orderManV2s, params.getSupplierManList());

        List<SupplierOrderManReq> addList = (List<SupplierOrderManReq>) diffResultMap.get("addList");
        List<SupplierOrderManReq> updateList = (List<SupplierOrderManReq>) diffResultMap.get("updateList");
        List<SupplierOrderManV2> deleteList = (List<SupplierOrderManV2>) diffResultMap.get("deleteList");


        List<SupplierOrderManV2> supplierOrderManList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(addList)) {
            for (SupplierOrderManReq r : addList) {
                SupplierOrderManV2 supplierOrderMan = new SupplierOrderManV2();
                supplierOrderMan.setEnterpriseNo(operationModel.getEnterpriseNo());
                supplierOrderMan.setUseOrgNo(params.getUseOrgNo());
//                supplierOrderMan.setSupplierNo(supplier.getSupplierNo());
                supplierOrderMan.setSupplierCode(params.getSupplierCode());
//                supplierOrderMan.setManCode(UUID.randomUUID().toString());
                supplierOrderMan.setOrderManNo(r.getOrderManNo());
                supplierOrderMan.setOrderManName(r.getOrderManName());
                supplierOrderMan.setDeptNo(r.getDeptNo());
                supplierOrderMan.setDeptName(r.getDeptName());
                supplierOrderMan.setPost(r.getPost());
                supplierOrderMan.setIsDefault(r.getIsDefault());
                supplierOrderMan.setOrderSpecialist(r.getOrderSpecialist());
                supplierOrderMan.setPost(r.getPost());
                supplierOrderManList.add(supplierOrderMan);
            }
            supplierOrderManV2DAO.addBatch(supplierOrderManList);
        }

        if (CollectionUtils.isNotEmpty(updateList)) {
            List<SupplierOrderManV2> supplierOrderManUpdateList = new ArrayList<>();

            for (SupplierOrderManReq r : updateList) {
                SupplierOrderManV2 supplierOrderMan = new SupplierOrderManV2();
                supplierOrderMan.setId(r.getId());
                supplierOrderMan.setEnterpriseNo(operationModel.getEnterpriseNo());
                supplierOrderMan.setUseOrgNo(params.getUseOrgNo());
//                supplierOrderMan.setSupplierNo(supplier.getSupplierNo());
                supplierOrderMan.setSupplierCode(params.getSupplierCode());
//                supplierOrderMan.setManCode(UUID.randomUUID().toString());
                supplierOrderMan.setOrderManNo(r.getOrderManNo());
                supplierOrderMan.setOrderManName(r.getOrderManName());
                supplierOrderMan.setDeptNo(r.getDeptNo());
                supplierOrderMan.setDeptName(r.getDeptName());
                supplierOrderMan.setPost(r.getPost());
                supplierOrderMan.setIsDefault(r.getIsDefault());
                supplierOrderMan.setOrderSpecialist(r.getOrderSpecialist());
                supplierOrderMan.setPost(r.getPost());

                supplierOrderManUpdateList.add(supplierOrderMan);
            }
            supplierOrderManV2DAO.updateByIdBatch(supplierOrderManUpdateList);
        }

        if (CollectionUtils.isNotEmpty(deleteList)) {
            SupplierOrderManV2 orderMan = new SupplierOrderManV2();
            orderMan.setDeleted(DeletedEnum.DELETED.getValue());
            supplierOrderManV2DAO.update(orderMan, Wrappers.<SupplierOrderManV2>lambdaQuery()
                    .eq(SupplierOrderManV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                    .in(SupplierOrderManV2::getId, deleteList.stream().map(SupplierOrderManV2::getId).collect(Collectors.toList())));
        }

        return supplierOrderManList.stream().map(SupplierOrderManV2::getId).collect(Collectors.toList());
    }

    @Override
    @Transactional
    public List<SupplierOrderManV2> overwriteSupplierOrdermanList(OperationModel operationModel, SupplierOrdermanListEditReq params) {
        SupplierOrderManV2 toDeleteOrderMan = new SupplierOrderManV2();
        toDeleteOrderMan.setDeleted(DeletedEnum.DELETED.getValue());
        supplierOrderManV2DAO.update(toDeleteOrderMan, Wrappers.<SupplierOrderManV2>lambdaQuery()
                .eq(SupplierOrderManV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(SupplierOrderManV2::getUseOrgNo, params.getUseOrgNo())
                .eq(SupplierOrderManV2::getSupplierCode, params.getSupplierCode())
                .eq(SupplierOrderManV2::getDeleted, DeletedEnum.UN_DELETE.getValue())
        );

        if (CollectionUtils.isNotEmpty(params.getSupplierManList())) {
            List<SupplierOrderManV2> supplierOrderManList = new ArrayList<>();
            for (SupplierOrderManReq r : params.getSupplierManList()) {
                SupplierOrderManV2 supplierOrderMan = new SupplierOrderManV2();

                supplierOrderMan.setEnterpriseNo(operationModel.getEnterpriseNo());
                supplierOrderMan.setUseOrgNo(params.getUseOrgNo());
//                supplierOrderMan.setSupplierNo(supplier.getSupplierNo());
                supplierOrderMan.setSupplierCode(params.getSupplierCode());
//                supplierOrderMan.setManCode(UUID.randomUUID().toString());
                supplierOrderMan.setOrderManNo(r.getOrderManNo());
                supplierOrderMan.setOrderManName(r.getOrderManName());
                supplierOrderMan.setDeptNo(r.getDeptNo());
                supplierOrderMan.setDeptName(r.getDeptName());
                supplierOrderMan.setPost(r.getPost());
                supplierOrderMan.setIsDefault(r.getIsDefault());
                supplierOrderMan.setOrderSpecialist(r.getOrderSpecialist());
                supplierOrderMan.setPost(r.getPost());

                supplierOrderManList.add(supplierOrderMan);
            }
            supplierOrderManV2DAO.addBatch(supplierOrderManList);

            return supplierOrderManList;
        }

        return Collections.emptyList();
    }

    @Override
    public void preValidateEditSupplierOrdermanList(OperationModel operationModel, SupplierOrdermanListEditReq params) {
        // 参数校验
        editSupplierOrdermanListValidate(operationModel, params);
    }

    private void editSupplierBankListValidate(OperationModel operationModel, SupplierBankListEditReq params) {
        if (CollectionUtils.isEmpty(params.getBankList())) {
            return;
        }

        for (CompanyBankReq companyBankReq : params.getBankList()) {
            //必填项校验
            ValidatorUtils.checkEmptyThrowEx(companyBankReq.getBankType(), "银行类别不能为空");
            ValidatorUtils.checkEmptyThrowEx(companyBankReq.getOpenBank(), "开户银行不能为空");
            ValidatorUtils.checkEmptyThrowEx(companyBankReq.getAccountName(), "户名不能为空");
            ValidatorUtils.checkEmptyThrowEx(companyBankReq.getAccountNo(), "账户不能为空");
            ValidatorUtils.checkEmptyThrowEx(companyBankReq.getAccountType(), "账户性质不能为空");
            ValidatorUtils.checkEmptyThrowEx(companyBankReq.getIsDefault(), "是否默认不能为空");
            ValidatorUtils.checkEmptyThrowEx(companyBankReq.getStatus(), "状态不能为空");

            //范围校验
            ValidatorUtils.checkTrueThrowEx(companyBankReq.getOpenBank().length() > 100, "开户银行不能大于100");
            ValidatorUtils.checkTrueThrowEx(companyBankReq.getAccountName().length() > 100, "户名不能大于100");
            ValidatorUtils.checkTrueThrowEx(companyBankReq.getAccountNo().length() > 100, "账户不能大于100");
            if (StringUtils.isNotEmpty(companyBankReq.getLinkPerson())) {
                ValidatorUtils.checkTrueThrowEx(companyBankReq.getLinkPerson().length() > 100, "联系人不能大于100");
            }
            if (StringUtils.isNotEmpty(companyBankReq.getLinkPhone())) {
                ValidatorUtils.checkTrueThrowEx(companyBankReq.getLinkPhone().length() > 100, "联系电话不能大于100");
            }
        }
    }

    @Override
    @Transactional
    public List<Long> editSupplierBankList(OperationModel operationModel, SupplierBankListEditReq params) {
        // 参数校验
        editSupplierBankListValidate(operationModel, params);

        SupplierV2 supplier = supplierV2DAO.selectOne(new LambdaQueryWrapper<SupplierV2>()
                .eq(SupplierV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(SupplierV2::getManageOrgNo, params.getManageOrgNo())
                .eq(SupplierV2::getSupplierCode, params.getSupplierCode())
                .eq(SupplierV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
        if (null == supplier) {
            throw new BusinessException(ErrorCode.param_invalid_msg, "供应商不存在");
        }

        CompanyV2 company = companyV2Service.findByEnterpriseNoAndCompanyNo(operationModel.getEnterpriseNo(), supplier.getCompanyNo());

        Map<String, List<?>> bankDiffMap = companyV2Service.saveOrUpdateBank(operationModel, params.getBankList(), company.getCompanyNo(), params.getManageOrgNo());
        List<CompanyBankV2> addedList = (List<CompanyBankV2>) bankDiffMap.get("addedList");
        if (CollectionUtils.isEmpty(addedList)) {
            return Collections.emptyList();
        }

        return addedList.stream().map(CompanyBankV2::getId).collect(Collectors.toList());
    }

    @Override
    public void preValidateEditSupplierBankList(OperationModel operationModel, SupplierBankListEditReq params) {
        // 参数校验
        editSupplierBankListValidate(operationModel, params);
    }

    @Override
    public List<SupplierV2> findByInternalOrgNo(OperationModel operationModel, String supplierCode, String internalOrgNo) {
        return supplierV2DAO.selectList(new LambdaQueryWrapper<SupplierV2>()
                .eq(SupplierV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .ne(StringUtils.isNotEmpty(supplierCode), SupplierV2::getSupplierCode, supplierCode)
                .eq(SupplierV2::getIsAssociatedEnterprise, CommonIfEnum.YES.getValue())
                .eq(SupplierV2::getAssociatedOrgNo, internalOrgNo)
                .eq(SupplierV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
    }

    @Override
    public PageVo<OrganizationVo> findOrgListPageBySetting(OperationModel operationModel, SupplierEligibleOrgListQueryReq queryReq, PageDto pageDto) {
        ValidatorUtils.checkEmptyThrowEx(queryReq, "查询条件不能为空");

        List<SupplierV2> supplierV2s = supplierV2DAO.selectList(new LambdaQueryWrapper<SupplierV2>()
                .eq(SupplierV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .ne(StringUtils.isNotEmpty(queryReq.getSupplierCode()), SupplierV2::getSupplierCode, queryReq.getSupplierCode())
                .eq(SupplierV2::getIsAssociatedEnterprise, CommonIfEnum.YES.getValue())
                .eq(SupplierV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
        Set<String> assOrgNos = supplierV2s.stream().map(SupplierV2::getAssociatedOrgNo).filter(Objects::nonNull).collect(Collectors.toSet());
        List<String> excludeOrgNoList = null;
        if (CollectionUtils.isNotEmpty(assOrgNos)) {
            excludeOrgNoList = new ArrayList<>(assOrgNos);
        }

        return organizationService.findListPageBySetting(operationModel, excludeOrgNoList, pageDto);
    }

    @Override
    @Transactional
    public Map<String, Object> manageApprove(OperationModel operationModel, SupplierApply supplierApply, SupplierApplyItem supplierApplyItem, SupplierApplyFormReq supplierApplyFormReq, SupplierApplyApproveReq req) {
        Map<String, Object> result = new HashMap<>();

        String supplierCode = null;
        if (SupplierApplyTypeEnum.ADD_APPLY.getValue().equals(supplierApply.getApplyType())) { // 新增供应商
            SupplierSaveBasicAndBizReq supplierSaveBasicAndBizReq = new SupplierSaveBasicAndBizReq();
            BeanUtils.copyProperties(supplierApplyFormReq, supplierSaveBasicAndBizReq);
            supplierSaveBasicAndBizReq.setEnterpriseNo(operationModel.getEnterpriseNo());
            supplierSaveBasicAndBizReq.setManageOrgNo(supplierApply.getManageOrgNo());
            supplierSaveBasicAndBizReq.setBusinessFlag(SupplierBusinessFlagEnum.FORMAL.getValue());

            SupplierVO supplierVO = null;
            if (StringUtils.isNotEmpty(supplierApply.getSupplierCode())) {
                supplierVO = getDetailSupplierByCode(operationModel, supplierApply.getUseOrgNo(), supplierApply.getSupplierCode());
            }
            if (null == supplierVO) {
                supplierVO = getDetailSupplierByName(operationModel, supplierApply.getUseOrgNo(), supplierApply.getSupplierName());
            }

            result.put("preApproveSupplier", supplierVO);

            // 存在同名供应商则更新，否则新增
            if (null == supplierVO) {
                supplierCode = StringUtils.isNotEmpty(supplierApply.getSupplierCode()) ? supplierApply.getSupplierCode() : numberCenterService.createNumberForBill(BillNameConstant.BDC_SUPPLIER_BILL, operationModel.getEnterpriseNo());
                supplierSaveBasicAndBizReq.setSupplierCode(supplierCode);
                manageSaveSupplierBasicAndBiz(operationModel, supplierSaveBasicAndBizReq);

                //管理组织存业务信息，拷贝一份给使用组织；其他tab页只存使用组织

                // 同步分派，拿到最新的业务信息，存入镜像
                SupplierAssignExeRequest supplierAssignExeRequest = new SupplierAssignExeRequest();
                supplierAssignExeRequest.setEnterpriseNo(operationModel.getEnterpriseNo());
                supplierAssignExeRequest.setManageOrgNo(supplierApply.getManageOrgNo());
                SupplierAssignDocExeRequest supplierAssignDocExeRequest = new SupplierAssignDocExeRequest();
                supplierAssignDocExeRequest.setSupplierCode(supplierCode);
                supplierAssignDocExeRequest.setUseOrgNoList(Collections.singletonList(supplierApply.getUseOrgNo()));
                supplierAssignExeRequest.setDocList(Collections.singletonList(supplierAssignDocExeRequest));
                assignSupplier(supplierAssignExeRequest);
            } else {
                supplierCode = supplierVO.getSupplierCode();

                // 同步分派，拿到最新的业务信息，存入镜像
                SupplierAssignExeRequest supplierAssignExeRequest = new SupplierAssignExeRequest();
                supplierAssignExeRequest.setEnterpriseNo(operationModel.getEnterpriseNo());
                supplierAssignExeRequest.setManageOrgNo(supplierApply.getManageOrgNo());
                SupplierAssignDocExeRequest supplierAssignDocExeRequest = new SupplierAssignDocExeRequest();
                supplierAssignDocExeRequest.setSupplierCode(supplierCode);
                supplierAssignDocExeRequest.setUseOrgNoList(Collections.singletonList(supplierApply.getUseOrgNo()));
                supplierAssignExeRequest.setDocList(Collections.singletonList(supplierAssignDocExeRequest));
                assignSupplier(supplierAssignExeRequest);

                SupplierAssumeManageEditBasicAndBizReq supplierAssumeManageEditBasicAndBizReq = new SupplierAssumeManageEditBasicAndBizReq();
                BeanUtils.copyProperties(supplierApplyFormReq, supplierAssumeManageEditBasicAndBizReq);
                supplierAssumeManageEditBasicAndBizReq.setSupplierCode(supplierCode);
                assumeManageEditSupplierBasicAndBiz(operationModel, supplierAssumeManageEditBasicAndBizReq);
            }


            // 保存联系人
            SupplierLinkmanListEditReq supplierLinkmanListEditReq = new SupplierLinkmanListEditReq();
            supplierLinkmanListEditReq.setEnterpriseNo(operationModel.getEnterpriseNo());
            supplierLinkmanListEditReq.setUseOrgNo(supplierApply.getUseOrgNo());
            supplierLinkmanListEditReq.setSupplierCode(supplierCode);
            supplierLinkmanListEditReq.setLinkmanList(supplierApplyFormReq.getLinkmanList());
            editSupplierLinkmanList(operationModel, supplierLinkmanListEditReq);

            // 保存联系地址
            SupplierAddressListEditReq supplierAddressListEditReq = new SupplierAddressListEditReq();
            supplierAddressListEditReq.setEnterpriseNo(operationModel.getEnterpriseNo());
            supplierAddressListEditReq.setUseOrgNo(supplierApply.getUseOrgNo());
            supplierAddressListEditReq.setSupplierCode(supplierCode);
            supplierAddressListEditReq.setLinkAddressList(supplierApplyFormReq.getLinkAddressList());
            editSupplierAddressList(operationModel, supplierAddressListEditReq);

            // 保存银行信息
            SupplierBankListEditReq supplierBankListEditReq = new SupplierBankListEditReq();
            supplierBankListEditReq.setEnterpriseNo(operationModel.getEnterpriseNo());
            supplierBankListEditReq.setManageOrgNo(supplierApply.getManageOrgNo());
            supplierBankListEditReq.setSupplierCode(supplierCode);
            supplierBankListEditReq.setBankList(supplierApplyFormReq.getBankList());
            editSupplierBankList(operationModel, supplierBankListEditReq);

            // 保存负责人信息
            SupplierOrdermanListEditReq supplierOrdermanListEditReq = new SupplierOrdermanListEditReq();
            supplierOrdermanListEditReq.setEnterpriseNo(operationModel.getEnterpriseNo());
            supplierOrdermanListEditReq.setUseOrgNo(supplierApply.getUseOrgNo());
            supplierOrdermanListEditReq.setSupplierCode(supplierCode);
            supplierOrdermanListEditReq.setSupplierManList(supplierApplyFormReq.getSupplierManList());
            editSupplierOrdermanList(operationModel, supplierOrdermanListEditReq);
        } else { // 变更供应商
            SupplierVO supplierVO = getDetailSupplierByCode(operationModel, supplierApply.getUseOrgNo(), supplierApply.getSupplierCode());
            result.put("preApproveSupplier", supplierVO);

            supplierCode = supplierApply.getSupplierCode();

            SupplierEditBasicReq supplierEditBasicReq = new SupplierEditBasicReq();
            BeanUtils.copyProperties(supplierApplyFormReq, supplierEditBasicReq);
            supplierEditBasicReq.setEnterpriseNo(operationModel.getEnterpriseNo());
            supplierEditBasicReq.setManageOrgNo(supplierApply.getManageOrgNo());

            editSupplierBasic(operationModel, supplierEditBasicReq);
        }

        result.put("supplierCode", supplierCode);
        return result;
    }

    @Override
    public Map<String, String> preValidateManageApprove(OperationModel operationModel, SupplierApply supplierApply, SupplierApplyItem supplierApplyItem, SupplierApplyFormReq supplierApplyFormReq, SupplierApplyApproveReq req) {
        String supplierCode = null;
        String companyNo = null;

        if (SupplierApplyTypeEnum.ADD_APPLY.getValue().equals(supplierApply.getApplyType())) { // 新增供应商
            SupplierSaveBasicAndBizReq supplierSaveBasicAndBizReq = new SupplierSaveBasicAndBizReq();
            BeanUtils.copyProperties(supplierApplyFormReq, supplierSaveBasicAndBizReq);
            supplierSaveBasicAndBizReq.setEnterpriseNo(operationModel.getEnterpriseNo());
            supplierSaveBasicAndBizReq.setManageOrgNo(supplierApply.getManageOrgNo());
            supplierSaveBasicAndBizReq.setBusinessFlag(SupplierBusinessFlagEnum.FORMAL.getValue());

            SupplierVO supplierVO = null;
            if (StringUtils.isNotEmpty(supplierApply.getSupplierCode())) {
                supplierVO = getDetailSupplierByCode(operationModel, supplierApply.getUseOrgNo(), supplierApply.getSupplierCode());
            }
            if (null == supplierVO) {
                supplierVO = getDetailSupplierByName(operationModel, supplierApply.getUseOrgNo(), supplierApply.getSupplierName());
            }


            // 存在同名供应商则更新，否则新增
            if (null == supplierVO) {
                supplierCode = StringUtils.isNotEmpty(supplierApply.getSupplierCode()) ? supplierApply.getSupplierCode() : numberCenterService.createNumberForBill(BillNameConstant.BDC_SUPPLIER_BILL, operationModel.getEnterpriseNo());
                supplierSaveBasicAndBizReq.setSupplierCode(supplierCode);
                companyNo = preValidateManageSaveSupplierBasicAndBiz(operationModel, supplierSaveBasicAndBizReq);
            } else {
                supplierCode = supplierVO.getSupplierCode();


                SupplierAssumeManageEditBasicAndBizReq supplierAssumeManageEditBasicAndBizReq = new SupplierAssumeManageEditBasicAndBizReq();
                BeanUtils.copyProperties(supplierApplyFormReq, supplierAssumeManageEditBasicAndBizReq);
                supplierAssumeManageEditBasicAndBizReq.setSupplierCode(supplierCode);
                companyNo = preValidateAssumeManageEditSupplierBasicAndBiz(operationModel, supplierAssumeManageEditBasicAndBizReq);
            }

            // 保存联系人
            SupplierLinkmanListEditReq supplierLinkmanListEditReq = new SupplierLinkmanListEditReq();
            supplierLinkmanListEditReq.setEnterpriseNo(operationModel.getEnterpriseNo());
            supplierLinkmanListEditReq.setUseOrgNo(supplierApply.getUseOrgNo());
            supplierLinkmanListEditReq.setSupplierCode(supplierCode);
            supplierLinkmanListEditReq.setLinkmanList(supplierApplyFormReq.getLinkmanList());
            preValidateEditSupplierLinkmanList(operationModel, supplierLinkmanListEditReq);

            // 保存联系地址
            SupplierAddressListEditReq supplierAddressListEditReq = new SupplierAddressListEditReq();
            supplierAddressListEditReq.setEnterpriseNo(operationModel.getEnterpriseNo());
            supplierAddressListEditReq.setUseOrgNo(supplierApply.getUseOrgNo());
            supplierAddressListEditReq.setSupplierCode(supplierCode);
            supplierAddressListEditReq.setLinkAddressList(supplierApplyFormReq.getLinkAddressList());
            preValidateEditSupplierAddressList(operationModel, supplierAddressListEditReq);

            // 保存银行信息
            SupplierBankListEditReq supplierBankListEditReq = new SupplierBankListEditReq();
            supplierBankListEditReq.setEnterpriseNo(operationModel.getEnterpriseNo());
            supplierBankListEditReq.setManageOrgNo(supplierApply.getManageOrgNo());
            supplierBankListEditReq.setSupplierCode(supplierCode);
            supplierBankListEditReq.setBankList(supplierApplyFormReq.getBankList());
            preValidateEditSupplierBankList(operationModel, supplierBankListEditReq);

            // 保存负责人信息
            SupplierOrdermanListEditReq supplierOrdermanListEditReq = new SupplierOrdermanListEditReq();
            supplierOrdermanListEditReq.setEnterpriseNo(operationModel.getEnterpriseNo());
            supplierOrdermanListEditReq.setUseOrgNo(supplierApply.getUseOrgNo());
            supplierOrdermanListEditReq.setSupplierCode(supplierCode);
            supplierOrdermanListEditReq.setSupplierManList(supplierApplyFormReq.getSupplierManList());
            preValidateEditSupplierOrdermanList(operationModel, supplierOrdermanListEditReq);

        } else { // 变更供应商
            supplierCode = supplierApply.getSupplierCode();

            SupplierEditBasicReq supplierEditBasicReq = new SupplierEditBasicReq();
            BeanUtils.copyProperties(supplierApplyFormReq, supplierEditBasicReq);
            supplierEditBasicReq.setEnterpriseNo(operationModel.getEnterpriseNo());
            supplierEditBasicReq.setManageOrgNo(supplierApply.getManageOrgNo());

            companyNo = preValidateEditSupplierBasic(operationModel, supplierEditBasicReq);
        }

        Map<String, String> result = new HashMap<>();
        result.put("companyNo", companyNo);
        result.put("supplierCode", supplierCode);

        return result;
    }

    private List<SupplierBasicResponse> convert2SupplierBasicResponse(List<SupplierV2> supplierV2s) {
        if (CollectionUtils.isEmpty(supplierV2s)) {
            return Collections.emptyList();
        }

        String enterpriseNo = supplierV2s.get(0).getEnterpriseNo();

        Map<String, Object> infoMap = collectBasicInfo(enterpriseNo, supplierV2s, Collections.emptyList(), Sets.newHashSet(
                "taxCategoryMap",
                "economicTypeMap",
                "companyMap",
                "transactionTypeMap",
                "orgMap",
                "categoryMap",
                "countryRegionMap"
        ));

        final Map<String, CustomDocResponse> taxCategoryMap = (Map<String, CustomDocResponse>) infoMap.get("taxCategoryMap");

        final Map<String, CustomDocResponse> economicTypeMap = (Map<String, CustomDocResponse>) infoMap.get("economicTypeMap");

        final Map<String, CompanyV2> companyMap = (Map<String, CompanyV2>) infoMap.get("companyMap");

        final Map<String, TransactionTypeResponse> transactionTypeMap = (Map<String, TransactionTypeResponse>) infoMap.get("transactionTypeMap");

        final Map<String, OrganizationVo> orgNo2OrganizationVo = (Map<String, OrganizationVo>) infoMap.get("orgMap");

        final Map<String, SupplierCategoryV2> categoryMap = (Map<String, SupplierCategoryV2>) infoMap.get("categoryMap");

        final Map<String, String> countryRegionMap = (Map<String, String>) infoMap.get("countryRegionMap");


        List<SupplierBasicResponse> supplierBasicResponses = BeanUtil.copyFieldsList(supplierV2s, SupplierBasicResponse.class);

        for (SupplierBasicResponse supplierBasicRespons : supplierBasicResponses) {
            // 补充名称类字段
            if (orgNo2OrganizationVo.containsKey(supplierBasicRespons.getManageOrgNo())) {
                supplierBasicRespons.setManageOrgName(orgNo2OrganizationVo.get(supplierBasicRespons.getManageOrgNo()).getOrgName());
            }

            if (categoryMap.containsKey(supplierBasicRespons.getSupplierCategoryNo())) {
                supplierBasicRespons.setSupplierCategoryName(categoryMap.get(supplierBasicRespons.getSupplierCategoryNo()).getCategoryName());
            }

            if (categoryMap.containsKey(supplierBasicRespons.getTransactionType())) {
                supplierBasicRespons.setTransactionTypeName(transactionTypeMap.get(supplierBasicRespons.getTransactionType()).getTransactionTypeName());
            }

            supplierBasicRespons.setRetailInvestorsName(CommonIfEnum.getNameByValue(supplierBasicRespons.getRetailInvestors()));

            // 补充企业信息
            CompanyV2 companyV2 = companyMap.get(supplierBasicRespons.getCompanyNo());
            supplierBasicRespons.setCompanyCode(companyV2.getCompanyCode());
            supplierBasicRespons.setCompanyName(companyV2.getCompanyName());
            supplierBasicRespons.setFactoryType(companyV2.getFactoryType());
            supplierBasicRespons.setFactoryTypeName(FactoryTypeEnum.getByType(supplierBasicRespons.getFactoryType()) == null ? null : FactoryTypeEnum.getByType(supplierBasicRespons.getFactoryType()).getName());
//            supplierBasicRespons.setCountry(companyV2.getCountry());
            supplierBasicRespons.setCountryRegionId(companyV2.getCountryRegionId());
            if (StringUtils.isNotEmpty(companyV2.getCountryRegionId())) {
                supplierBasicRespons.setCountryRegionName(countryRegionMap.get(companyV2.getCountryRegionId()));
            }
            supplierBasicRespons.setTaxCategory(companyV2.getTaxCategory());
            supplierBasicRespons.setTaxCategoryName(taxCategoryMap.getOrDefault(companyV2.getTaxCategory(), new CustomDocResponse()).getDocItemName());
            supplierBasicRespons.setEconomicType(companyV2.getEconomicType());
            supplierBasicRespons.setEconomicTypeName(economicTypeMap.getOrDefault(companyV2.getEconomicType(), new CustomDocResponse()).getDocItemName());

//            supplierBasicRespons.setIsAssociatedEnterprise(companyV2.getIsAssociatedEnterprise());
//            supplierBasicRespons.setAssociatedOrgNo(companyV2.getAssociatedOrgNo());
//            supplierBasicRespons.setAssociatedOrgCode(companyV2.getAssociatedOrgCode());
//            supplierBasicRespons.setAssociatedOrgName(companyV2.getAssociatedOrgName());
        }

        return supplierBasicResponses;
    }

    private List<SupplierBizResponse> convert2SupplierWithBizInfoResponse(List<SupplierV2WithBiz> supplierV2WithBizs) {
        if (CollectionUtils.isEmpty(supplierV2WithBizs)) {
            return Collections.emptyList();
        }

        String enterpriseNo = supplierV2WithBizs.get(0).getEnterpriseNo();

        Map<String, Object> infoMap = collectBizInfo(enterpriseNo, supplierV2WithBizs, Sets.newHashSet(
                "taxCategoryMap",
                "economicTypeMap",
                "companyMap",
                "transactionTypeMap",
                "orgMap",
                "categoryMap",
                "bankMap",
                "currencyMap",
                "cooperationMap",
                "settlementModesMap",
                "paymentTermMap",
                "linkManMap",
                "addressMap",
                "orderManMap",
                "supplierBizMap",
                "supplierGspMap",
                "countryRegionMap",
                "supplierBaseMap"
        ));

        // 获取税收分类
        final Map<String, CustomDocResponse> taxCategoryMap = (Map<String, CustomDocResponse>) infoMap.get("taxCategoryMap");

        // 获取经济类型
        final Map<String, CustomDocResponse> economicTypeMap = (Map<String, CustomDocResponse>) infoMap.get("economicTypeMap");

        // 获取企业信息
        final Map<String, CompanyV2> companyMap = (Map<String, CompanyV2>) infoMap.get("companyMap");

        // 获取供应商类型
        final Map<String, TransactionTypeResponse> transactionTypeMap = (Map<String, TransactionTypeResponse>) infoMap.get("transactionTypeMap");

        // 获取组织信息
        final Map<String, OrganizationVo> orgNo2OrganizationVo = (Map<String, OrganizationVo>) infoMap.get("orgMap");

        // 获取供应商分类
        final Map<String, SupplierCategoryV2> categoryMap = (Map<String, SupplierCategoryV2>) infoMap.get("categoryMap");

        // 获取银行信息
        final Map<String, List<CompanyBankV2>> bankMap = (Map<String, List<CompanyBankV2>>) infoMap.get("bankMap");

        // 获取币种
        final Map<String, String> currencyMap = (Map<String, String>) infoMap.get("currencyMap");

        // 获取合作性质
        final Map<String, CustomDocResponse> cooperationMap = (Map<String, CustomDocResponse>) infoMap.get("cooperationMap");

        // 获取结算方式
        final Map<String, String> settlementModesMap = (Map<String, String>) infoMap.get("settlementModesMap");

        //付款条件
        final Map<String, CustomDocResponse> paymentTermMap = (Map<String, CustomDocResponse>) infoMap.get("paymentTermMap");

        // 获取联系人
        final Map<String, Map<String, List<CompanyLinkmanV2>>> code2org2LinkManList = (Map<String, Map<String, List<CompanyLinkmanV2>>>) infoMap.get("linkManMap");

        // 获取地址
        final Map<String, Map<String, List<CompanyShippingAddressV2>>> code2org2AddressList = (Map<String, Map<String, List<CompanyShippingAddressV2>>>) infoMap.get("addressMap");

        // 获取负责人
        final Map<String, Map<String, List<SupplierOrderManV2>>> code2org2OrderManList = (Map<String, Map<String, List<SupplierOrderManV2>>>) infoMap.get("orderManMap");

        // 国家地区
        final Map<String, String> countryRegionMap = (Map<String, String>) infoMap.get("countryRegionMap");

        // 业务信息
        final Map<Long, SupplierBiz> supplierBizMap = (Map<Long, SupplierBiz>) infoMap.get("supplierBizMap");

        // 首营信息
        final Map<String, Map<String, SupplierGspAudit>> code2org2GspMap = (Map<String, Map<String, SupplierGspAudit>>) infoMap.get("supplierGspMap");

        // 分派信息
        Map<String, Map<String, SupplierBase>> code2org2BaseMap = (Map<String, Map<String, SupplierBase>>) infoMap.get("supplierBaseMap");

        List<SupplierBizResponse> supplierBizResponses = BeanUtil.copyFieldsList(supplierV2WithBizs, SupplierBizResponse.class);

        for (SupplierBizResponse supplierBizResponse : supplierBizResponses) {
            {
                // 补充名称类字段
                if (orgNo2OrganizationVo.containsKey(supplierBizResponse.getManageOrgNo())) {
                    supplierBizResponse.setManageOrgName(orgNo2OrganizationVo.get(supplierBizResponse.getManageOrgNo()).getOrgName());
                }

                if (categoryMap.containsKey(supplierBizResponse.getSupplierCategoryNo())) {
                    supplierBizResponse.setSupplierCategoryName(categoryMap.get(supplierBizResponse.getSupplierCategoryNo()).getCategoryName());
                }

                if (categoryMap.containsKey(supplierBizResponse.getTransactionType())) {
                    supplierBizResponse.setTransactionTypeName(transactionTypeMap.get(supplierBizResponse.getTransactionType()).getTransactionTypeName());
                }

                supplierBizResponse.setRetailInvestorsName(CommonIfEnum.getNameByValue(supplierBizResponse.getRetailInvestors()));
            }

            {
                // 补充企业信息
                CompanyV2 companyV2 = companyMap.get(supplierBizResponse.getCompanyNo());
                supplierBizResponse.setCompanyCode(companyV2.getCompanyCode());
                supplierBizResponse.setCompanyName(companyV2.getCompanyName());
                supplierBizResponse.setFactoryType(companyV2.getFactoryType());
                supplierBizResponse.setFactoryTypeName(FactoryTypeEnum.getByType(supplierBizResponse.getFactoryType()) == null ? null : FactoryTypeEnum.getByType(supplierBizResponse.getFactoryType()).getName());
//                supplierBizResponse.setCountry(companyV2.getCountry());
                supplierBizResponse.setCountryRegionId(companyV2.getCountryRegionId());
                if (StringUtils.isNotEmpty(companyV2.getCountryRegionId())) {
                    supplierBizResponse.setCountryRegionName(countryRegionMap.get(companyV2.getCountryRegionId()));
                }
                supplierBizResponse.setTaxCategory(companyV2.getTaxCategory());
                supplierBizResponse.setTaxCategoryName(taxCategoryMap.getOrDefault(companyV2.getTaxCategory(), new CustomDocResponse()).getDocItemName());
                supplierBizResponse.setEconomicType(companyV2.getEconomicType());
                supplierBizResponse.setEconomicTypeName(economicTypeMap.getOrDefault(companyV2.getEconomicType(), new CustomDocResponse()).getDocItemName());

//                supplierBizResponse.setIsAssociatedEnterprise(companyV2.getIsAssociatedEnterprise());
//                supplierBizResponse.setAssociatedOrgNo(companyV2.getAssociatedOrgNo());
//                supplierBizResponse.setAssociatedOrgCode(companyV2.getAssociatedOrgCode());
//                supplierBizResponse.setAssociatedOrgName(companyV2.getAssociatedOrgName());

                supplierBizResponse.setCompanyBankList(companyV2Service.convert2BankResponse(currencyMap, bankMap.get(supplierBizResponse.getCompanyNo())));
            }

            {
                // 补充业务信息
                SupplierBiz supplierBiz = supplierBizMap.get(supplierBizResponse.getBizId());
                BeanUtils.copyProperties(supplierBiz, supplierBizResponse);

                supplierBizResponse.setUseOrgName(orgNo2OrganizationVo.get(supplierBizResponse.getUseOrgNo()).getOrgName());
                supplierBizResponse.setCooperationModeName(cooperationMap.getOrDefault(supplierBizResponse.getCooperationMode(), new CustomDocResponse()).getDocItemName());
                supplierBizResponse.setCurrencyName(currencyMap.getOrDefault(supplierBizResponse.getCurrency(), ""));
                supplierBizResponse.setSettlementModesName(settlementModesMap.getOrDefault(supplierBizResponse.getSettlementModes(), ""));
                supplierBizResponse.setPaymentTermName(paymentTermMap.getOrDefault(supplierBizResponse.getPaymentTerm(), new CustomDocResponse()).getDocItemName());

                supplierBizResponse.setCompanyLinkmanList(convert2LinkManResponse(code2org2LinkManList.getOrDefault(supplierBizResponse.getSupplierCode(), Collections.emptyMap()).get(supplierBizResponse.getUseOrgNo())));
                supplierBizResponse.setCompanyShippingAddressList(convert2AddressResponse(code2org2AddressList.getOrDefault(supplierBizResponse.getSupplierCode(), Collections.emptyMap()).get(supplierBizResponse.getUseOrgNo())));
                supplierBizResponse.setSupplierOrderManList(convert2OrderManResponse(code2org2OrderManList.getOrDefault(supplierBizResponse.getSupplierCode(), Collections.emptyMap()).get(supplierBizResponse.getUseOrgNo())));
            }

            {
                // 补充首营信息
                supplierBizResponse.setGspAuditStatus(code2org2GspMap.getOrDefault(supplierBizResponse.getSupplierCode(), Collections.emptyMap()).getOrDefault(supplierBizResponse.getUseOrgNo(), new SupplierGspAudit()).getGspAuditStatus());
            }

            {
                // 补充分派信息
                supplierBizResponse.setControlStatus(code2org2BaseMap.getOrDefault(supplierBizResponse.getSupplierCode(), Collections.emptyMap()).getOrDefault(supplierBizResponse.getUseOrgNo(), new SupplierBase()).getControlStatus());
            }
        }

        return supplierBizResponses;
    }

    private List<SupplierOrderManResponse> convert2OrderManResponse(List<SupplierOrderManV2> supplierOrderManV2s) {
        if (CollectionUtils.isEmpty(supplierOrderManV2s)) {
            return Collections.emptyList();
        }

        String enterpriseNo = supplierOrderManV2s.get(0).getEnterpriseNo();
        List<EmployeeVo> employeeList = employeeService.getEmployeeList(enterpriseNo, supplierOrderManV2s.stream().map(SupplierOrderManV2::getOrderManNo).collect(Collectors.toList()));
        Map<String, EmployeeVo> employeeMap = employeeList.stream().collect(Collectors.toMap(EmployeeVo::getEmployeeNo, Function.identity()));

        List<SupplierOrderManResponse> supplierOrderManResponseList = BeanUtil.copyFieldsList(supplierOrderManV2s, SupplierOrderManResponse.class);
        for (SupplierOrderManResponse supplierOrderManResponse : supplierOrderManResponseList) {
            supplierOrderManResponse.setMobile(employeeMap.getOrDefault(supplierOrderManResponse.getOrderManNo(), new EmployeeVo()).getMobile());
        }
        return supplierOrderManResponseList;
    }

    private List<CompanyShippingAddressResponse> convert2AddressResponse(List<CompanyShippingAddressV2> companyShippingAddressV2s) {
        if (CollectionUtils.isEmpty(companyShippingAddressV2s)) {
            return Collections.emptyList();
        }

        List<String> regionCodeList = companyShippingAddressV2s.stream().map(CompanyShippingAddressV2::getRegionCode).filter(s -> !StringUtils.isEmpty(s)).collect(Collectors.toList());
        List<AreaCodeVo> areaCodeList = dictEnterpriseService.findAreaByCodes(regionCodeList);
        final Map<String, AreaCodeVo> areaMap = areaCodeList.stream().collect(Collectors.toMap(AreaCodeVo::getAreaCode, Function.identity()));

        List<CompanyShippingAddressResponse> companyShippingAddressResponseList = BeanUtil.copyFieldsList(companyShippingAddressV2s, CompanyShippingAddressResponse.class);
        for (CompanyShippingAddressResponse companyShippingAddressResponse : companyShippingAddressResponseList) {
            companyShippingAddressResponse.setRegionFullName(areaMap.getOrDefault(companyShippingAddressResponse.getRegionCode(), new AreaCodeVo()).getAreaFullName());
        }
        return companyShippingAddressResponseList;
    }

    private List<CompanyLinkmanResponse> convert2LinkManResponse(List<CompanyLinkmanV2> companyLinkmanV2s) {
        if (CollectionUtils.isEmpty(companyLinkmanV2s)) {
            return Collections.emptyList();
        }

        List<CompanyLinkmanResponse> companyLinkmanResponseList = BeanUtil.copyFieldsList(companyLinkmanV2s, CompanyLinkmanResponse.class);
//        for (CompanyLinkmanResponse companyLinkmanResponse : companyLinkmanResponseList) {
//        }
        return companyLinkmanResponseList;
    }

    @Override
    public Boolean syncSupplierNameByCompanyName(String enterpriseNo, String supplierCode, SupplierV2 toUpdateSupplierV2) {
        LambdaUpdateWrapper<SupplierV2> updateWrapper = Wrappers.<SupplierV2>lambdaUpdate()
                // 更新字段
                .set(SupplierV2::getSupplierName, toUpdateSupplierV2.getSupplierName())
                .set(SupplierV2::getOperateName, toUpdateSupplierV2.getOperateName())
                .set(SupplierV2::getOperateNo, toUpdateSupplierV2.getOperateNo())
                .set(SupplierV2::getOperateTime, toUpdateSupplierV2.getOperateTime())
                // where条件
                .eq(SupplierV2::getEnterpriseNo, enterpriseNo)
                .eq(SupplierV2::getSupplierCode, supplierCode);
        return supplierV2DAO.update(null, updateWrapper) > 0;
    }

}
