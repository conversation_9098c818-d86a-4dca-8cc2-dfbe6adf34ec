package com.yyigou.dsrp.cdc.service.customer;

import com.yyigou.ddc.common.page.PageDto;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.ddc.services.dsrp.bdc.vo.CustomerSalesManVo;
import com.yyigou.dsrp.cdc.api.customer.dto.CustomerSalesManQueryBySpecifyDTO;
import com.yyigou.dsrp.cdc.api.customer.dto.CustomerSalesManQueryDTO;
import com.yyigou.dsrp.cdc.api.customer.vo.CustomerSalesManVO;
import com.yyigou.dsrp.cdc.common.util.OperationModel;

import java.util.List;

public interface CustomerSaleManService {

    /**
     * 跨组织获取客户销售人列表
     *
     * @param customerCode
     * @param orgNo
     * @return
     */
    List<CustomerSalesManVO> getCustomerSaleManListByOrg(OperationModel operationModel, String customerCode, String orgNo);

    /**
     * 获取客户销售人列表
     *
     * @param params
     * @return
     */
    List<CustomerSalesManVo> findList(OperationModel operationModel, CustomerSalesManQueryDTO params);

    /**
     * 获取客户销售人列表
     *
     * @param params
     * @return
     */
    PageVo<CustomerSalesManVo> findPage(OperationModel operationModel, CustomerSalesManQueryDTO params, PageDto pageDto);


    /**
     * 获取客户销售人列表
     *
     * @param params
     * @return
     */
    List<CustomerSalesManVo> findListBySpecifyOrg(OperationModel operationModel, CustomerSalesManQueryBySpecifyDTO params);

    /**
     * 获取客户销售人列表
     *
     * @param params
     * @return
     */
    PageVo<CustomerSalesManVo> findPageBySpecifyOrg(OperationModel operationModel, CustomerSalesManQueryBySpecifyDTO params, PageDto pageDto);

}
