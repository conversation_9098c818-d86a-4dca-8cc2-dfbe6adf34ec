package com.yyigou.dsrp.cdc.service.company;

import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.ddc.common.util.ValidatorUtils;
import com.yyigou.dsrp.cdc.dao.company.CompanyDAO;
import com.yyigou.dsrp.cdc.dao.company.entity.Company;
import com.yyigou.dsrp.cdc.model.company.req.CompanyNameValidReq;
import com.yyigou.dsrp.cdc.model.company.req.CompanySaveReq;
import com.yyigou.dsrp.cdc.model.company.req.CompanyUnionSocialCodeValidReq;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

/**
 * 企业档案公共校验方法
 *
 * @author:  <PERSON>
 * @date: 2024/7/6 14:29
 * @version: 1.0.0
 */

@Component
public class CompanyCheckService {

    @Autowired
    private CompanyDAO companyDAO;

    /**
     * 校验企业档案必填参数
     *
     * @author:  Moore
     * @date: 2024/7/6 15:03
     * @version: 1.0.0
     */
    public static void validateRequiredField(CompanySaveReq companySaveReq) {
        ValidatorUtils.checkEmptyThrowEx(companySaveReq.getCompanyName(),"企业名称不能为空");
        ValidatorUtils.checkEmptyThrowEx(companySaveReq.getUnifiedSocialCode(),"统一社会信用代码不能为空");
        ValidatorUtils.checkEmptyThrowEx(companySaveReq.getFactoryType(),"企业注册地域不能为空");
        ValidatorUtils.checkEmptyThrowEx(companySaveReq.getTaxCategory(),"纳税类别不能为空");
    }

    /**
     * 校验企业名称唯一
     *
     * @param params
     * @return: {@link CallResult< Boolean>}
     */
    public Boolean validateCompanyNameUnique(CompanyNameValidReq params) {
        ValidatorUtils.checkEmptyThrowEx(params.getEnterpriseNo(), "租户编号不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getCompanyName(), "企业名称不能为空");
        List<Company> companyList = companyDAO.findByEnterpriseNoAndCompanyName(params.getEnterpriseNo(),params.getCompanyName());
        if (CollectionUtils.isEmpty(companyList)){
            return true;
        }
        if (StringUtils.isBlank(params.getCompanyNo())){
            return false;
        }else {
            Optional<Company> companyOptional = companyList.stream().filter(t -> !params.getCompanyNo().equals(t.getCompanyNo())).findAny();
            return !companyOptional.isPresent();
        }
    }

    public Boolean validateCompanyUnionSocialCodeUnique(CompanyUnionSocialCodeValidReq params) {
        ValidatorUtils.checkEmptyThrowEx(params.getEnterpriseNo(), "租户编号不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getUnifiedSocialCode(), "社会统一信用代码不能为空");
        if ("/".equals(params.getUnifiedSocialCode())) {
            return true;
        }
        List<Company> companyList = companyDAO.findByEnterpriseNoAndUnifiedSocialCode(params.getEnterpriseNo(),params.getUnifiedSocialCode());
        if (CollectionUtils.isEmpty(companyList)){
            return true;
        }
        if (StringUtils.isBlank(params.getCompanyNo())){
            return false;
        }else {
            Optional<Company> companyOptional = companyList.stream().filter(t -> !params.getCompanyNo().equals(t.getCompanyNo())).findAny();
            return !companyOptional.isPresent();
        }
    }
}
