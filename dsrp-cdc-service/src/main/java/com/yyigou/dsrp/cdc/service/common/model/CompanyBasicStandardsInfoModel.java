package com.yyigou.dsrp.cdc.service.common.model;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;

/**
 * 企业基本+标准信息
 *
 * @author: <PERSON>
 * @date: 2023/5/30 11:14
 * @version: 1.0.0
 */
@Data
public class CompanyBasicStandardsInfoModel implements Serializable {

    private static final long serialVersionUID = 3458795412078710421L;
    // 企业名称
    private String companyName;

    // 统一社会信用代码
    private String unifiedSocialCode;

    // 法定代表人
    private String legalPerson;

    // 联系人
//    private String linkMan;
//
//    // 联系电话
//    private String linkPhone;
//
//    // 联系人职务
//    private String linkManPosition;

    // 企业类型
//    private String enterpriseType;

    // 企业注册地域：1-境内，2-境外
    private Integer factoryType;

    // 合作关系
//    private String partnership;

    // 经营状态
    private String businessStatus;

    // 成立日期
    private String establishmentDate;

    // 营业期限开始日期
    private String businessStartTime;

    // 营业期限结束日期
    private String businessEndTime;

    // 是否长期
    private Integer businessLongTerm;

    // 注册资本
    private String registedCapital;

    // 实缴资本
    private String paidCapital;

    // 类型
    private String companyBusinessType;

    // 所属行业
    private String industry;

    // 工商注册号
    private String businessRegistNo;

    // 组织机构代码
    private String organizationNo;

    // 纳税人识别号
    private String taxpayerNo;

    // 纳税人资质
    private String taxpayerQualification;

    // 核准日期
    private String approvalDate;

    // 登记机关
    private String registrationAuthority;

    // 所在地区区域编码
    private String regionCode;

    // 所在地区区域名称
    private String regionName;

    // 详细地址
    private String address;

    // 曾用名
    private String lastName;

    // 参保人数
    private String insuredNumber;

    // WEB网站
    private String webSite;

    // 企业邮箱
    private String email;

    // 传真
    private String fax;

    // 经营范围
    private String manageScope;

    // 纳税类别
    private String taxCategory;

    // 纳税类别名称
    private String taxCategoryName;

    // 机构类型
    private String institutionalType;

    // 医院类型
    private String hospitalType;

    // 医院等级
    private Integer hospitalClass;

    // 是否上市
    private Integer isListed;

    // 是否关联企业
    private Integer isAssociatedEnterprise;

    // 企业状态:0-草稿，1-正式（现在只有正式，正式说明企业在供应商,客户,厂商至少存在一个正式业务）
    private Integer status;

    @EntityField(name = "国家/地区")
    private String country;

    @EntityField(name = "经济类型编码")
    private String economicType;

    @EntityField(name = "经济类型名称")
    private String economicTypeName;

    @EntityField(name = "关联组织编号")
    private String associatedOrgNo;

    @EntityField(name = "关联组织编码")
    private String associatedOrgCode;

    @EntityField(name = "关联组织名称")
    private String associatedOrgName;

    @EntityField(name = "是否医疗机构")
    private Integer isMedicalInstitution;
}
