package com.yyigou.dsrp.cdc.service.v2.supplier;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yyigou.ddc.common.page.PageDto;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.dsrp.cdc.api.v2.supplier.vo.SupplierApplyDetailVO;
import com.yyigou.dsrp.cdc.api.v2.supplier.vo.SupplierApplyPageVO;
import com.yyigou.dsrp.cdc.common.util.OperationModel;
import com.yyigou.dsrp.cdc.dao.v2.supplier.entity.SupplierApply;
import com.yyigou.dsrp.cdc.model.v2.supplier.req.*;


public interface SupplierApplyService extends IService<SupplierApply> {
    PageVo<SupplierApplyPageVO> applyFindListPage(OperationModel operationModel, SupplierApplyQueryListPageReq queryReq, PageDto pageDTO);

    PageVo<SupplierApplyPageVO> approveFindListPage(OperationModel operationModel, SupplierApplyApproveQueryListPageReq queryReq, PageDto pageDTO);

    Long getPendingCount(OperationModel operationModel);

    String applySaveOrUpdate(OperationModel operationModel, SupplierApplySaveOrUpdateReq req);

    SupplierApplyDetailVO getDetail(OperationModel operationModel, SupplierApplyGetReq req);

    Boolean deleteApply(OperationModel operationModel, SupplierApplyDeleteReq req);

    Boolean withDrawApply(OperationModel operationModel, SupplierApplyWithdrawReq req);

    Boolean manageApprove(OperationModel operationModel, SupplierApplyApproveReq req);
}
