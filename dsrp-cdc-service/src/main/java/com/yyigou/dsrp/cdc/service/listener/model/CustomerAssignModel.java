package com.yyigou.dsrp.cdc.service.listener.model;

import com.yyigou.dsrp.cdc.common.util.OperationModel;
import com.yyigou.dsrp.gcs.common.enums.executeRecord.ExecuteRecordExcuteTypeEnum;
import lombok.Data;

@Data
public class CustomerAssignModel {
    private String customerCode;
    private OperationModel operationModel;
    private String groupEnterpriseNo;
    private String subEnterpriseNo;
    private ExecuteRecordExcuteTypeEnum executeRecordExecuteTypeEnum;

    //非必填
    private Boolean generateExecutionRecord;
    private Integer recordId;
    private String applySourceId;
    private String applySource;
}
