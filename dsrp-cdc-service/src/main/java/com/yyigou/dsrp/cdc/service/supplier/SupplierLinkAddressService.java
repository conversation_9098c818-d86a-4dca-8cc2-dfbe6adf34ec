package com.yyigou.dsrp.cdc.service.supplier;

import com.yyigou.ddc.common.page.PageDto;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.dsrp.cdc.api.company.vo.CompanyShippingAddressVO;
import com.yyigou.dsrp.cdc.api.supply.dto.SupplierLinkAddressQueryDTO;
import com.yyigou.dsrp.cdc.api.supply.dto.SupplierLinkAddressSaveByOrgDTO;
import com.yyigou.dsrp.cdc.api.supply.dto.SupplierLinkAddressSaveDTO;
import com.yyigou.dsrp.cdc.common.util.OperationModel;

import java.util.List;

public interface SupplierLinkAddressService {


    /**
     * 获取供应商联系地址列表
     *
     * @param orgNo
     * @return
     */
    List<CompanyShippingAddressVO> getSupplierLinkAddressList(OperationModel operationModel, String supplierCode);

    /**
     * 获取供应商联系地址列表(分页)
     *
     * @return
     */
    PageVo<CompanyShippingAddressVO> findSupplierLinkAddressPage(OperationModel operationModel, SupplierLinkAddressQueryDTO params, PageDto pageDto);


    /**
     * 跨组织保存供应商联系地址列表
     *
     * @param params
     * @return
     */
    CompanyShippingAddressVO saveSupplierLinkAddress(OperationModel operationModel, SupplierLinkAddressSaveDTO params);

    CompanyShippingAddressVO editSupplierLinkAddress(OperationModel operationModel, SupplierLinkAddressSaveDTO params);

    /**
     * 跨组织获取供应商联系地址列表
     *
     * @param supplierCode
     * @param orgNo
     * @return
     */
    List<CompanyShippingAddressVO> getSupplierLinkAddressListByOrg(OperationModel operationModel, String supplierCode, String orgNo);

    /**
     * 跨组织保存供应商联系地址列表
     *
     * @param params
     * @return
     */
    CompanyShippingAddressVO saveSupplierLinkAddressByOrg(OperationModel operationModel, SupplierLinkAddressSaveByOrgDTO params);

    /**
     * 跨组织编辑联系地址
     *
     * @param params
     * @param
     * @return
     */
    CompanyShippingAddressVO editSupplierLinkAddressByOrg(OperationModel operationModel, SupplierLinkAddressSaveByOrgDTO params);

    /**
     * 跨组织删除联系地址
     *
     * @param linkAddressId
     * @param
     * @return
     */
    Boolean deleteSupplierLinkAddressByOrg(OperationModel operationModel, Long linkAddressId);
}
