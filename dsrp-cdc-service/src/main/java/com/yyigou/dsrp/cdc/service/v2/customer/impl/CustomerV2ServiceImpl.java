package com.yyigou.dsrp.cdc.service.v2.customer.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Sets;
import com.yyigou.ddc.common.error.ErrorCode;
import com.yyigou.ddc.common.exception.BusinessException;
import com.yyigou.ddc.common.page.PageDto;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.ddc.common.util.DateUtil;
import com.yyigou.ddc.common.util.ValidatorUtils;
import com.yyigou.ddc.services.ddc.dict.vo.AreaCodeVo;
import com.yyigou.ddc.services.ddc.uap.util.UapThreadLocalUtil;
import com.yyigou.ddc.services.ddc.uim.vo.EmployeeVo;
import com.yyigou.ddc.services.ddc.uim.vo.OrganizationVo;
import com.yyigou.ddc.services.dlog.dto.DLogLevel;
import com.yyigou.ddc.services.dsrp.bdc.enums.HospitalTypeEnum;
import com.yyigou.dsrp.cdc.api.v2.company.vo.CompanyBankVO;
import com.yyigou.dsrp.cdc.api.v2.company.vo.CompanyCertVO;
import com.yyigou.dsrp.cdc.api.v2.company.vo.CompanyLinkmanVO;
import com.yyigou.dsrp.cdc.api.v2.company.vo.CompanyShippingAddressVO;
import com.yyigou.dsrp.cdc.api.v2.customer.vo.*;
import com.yyigou.dsrp.cdc.client.v2.company.response.CompanyLinkmanResponse;
import com.yyigou.dsrp.cdc.client.v2.company.response.CompanyShippingAddressResponse;
import com.yyigou.dsrp.cdc.client.v2.customer.request.CustomerAssignDocExeRequest;
import com.yyigou.dsrp.cdc.client.v2.customer.request.CustomerAssignExeRequest;
import com.yyigou.dsrp.cdc.client.v2.customer.request.CustomerAssignMdmExeRequest;
import com.yyigou.dsrp.cdc.client.v2.customer.response.*;
import com.yyigou.dsrp.cdc.common.enums.*;
import com.yyigou.dsrp.cdc.common.enums.company.*;
import com.yyigou.dsrp.cdc.common.enums.customer.CustomerApplyTypeEnum;
import com.yyigou.dsrp.cdc.common.enums.customer.CustomerBusinessFlagEnum;
import com.yyigou.dsrp.cdc.common.enums.customer.CustomerControlStatusEnum;
import com.yyigou.dsrp.cdc.common.util.BeanUtil;
import com.yyigou.dsrp.cdc.common.util.CommonUtil;
import com.yyigou.dsrp.cdc.common.util.FormatNameUtils;
import com.yyigou.dsrp.cdc.common.util.OperationModel;
import com.yyigou.dsrp.cdc.dao.common.entity.BankType;
import com.yyigou.dsrp.cdc.dao.v2.company.CompanyLinkmanV2DAO;
import com.yyigou.dsrp.cdc.dao.v2.company.CompanyShippingAddressV2DAO;
import com.yyigou.dsrp.cdc.dao.v2.company.entity.CompanyBankV2;
import com.yyigou.dsrp.cdc.dao.v2.company.entity.CompanyLinkmanV2;
import com.yyigou.dsrp.cdc.dao.v2.company.entity.CompanyShippingAddressV2;
import com.yyigou.dsrp.cdc.dao.v2.company.entity.CompanyV2;
import com.yyigou.dsrp.cdc.dao.v2.customer.*;
import com.yyigou.dsrp.cdc.dao.v2.customer.entity.*;
import com.yyigou.dsrp.cdc.manager.integration.cert.CertService;
import com.yyigou.dsrp.cdc.manager.integration.cert.req.CompanyCertQueryRequest;
import com.yyigou.dsrp.cdc.manager.integration.cert.res.CompanyCertResponse;
import com.yyigou.dsrp.cdc.manager.integration.dict.DictEnterpriseService;
import com.yyigou.dsrp.cdc.manager.integration.dict.enums.DictNumberEnum;
import com.yyigou.dsrp.cdc.manager.integration.dlog.BusinessLogService;
import com.yyigou.dsrp.cdc.manager.integration.numberCenter.NumberCenterService;
import com.yyigou.dsrp.cdc.manager.integration.org.UimTenantService;
import com.yyigou.dsrp.cdc.manager.integration.uap.GradeControlService;
import com.yyigou.dsrp.cdc.manager.integration.uap.res.GradeCheckMgrOrgRes;
import com.yyigou.dsrp.cdc.manager.integration.uim.CustomDocService;
import com.yyigou.dsrp.cdc.manager.integration.uim.EmployeeService;
import com.yyigou.dsrp.cdc.manager.integration.uim.OrganizationService;
import com.yyigou.dsrp.cdc.manager.integration.uim.TransactionTypeService;
import com.yyigou.dsrp.cdc.manager.integration.uim.response.CustomDocResponse;
import com.yyigou.dsrp.cdc.manager.integration.uim.response.TransactionTypeResponse;
import com.yyigou.dsrp.cdc.manager.integration.ulog.ULogService;
import com.yyigou.dsrp.cdc.model.constant.*;
import com.yyigou.dsrp.cdc.model.enums.FactoryTypeEnum;
import com.yyigou.dsrp.cdc.model.v2.company.req.CompanyBankReq;
import com.yyigou.dsrp.cdc.model.v2.company.req.CompanyLinkmanReq;
import com.yyigou.dsrp.cdc.model.v2.company.req.CompanySaveOrUpdateReq;
import com.yyigou.dsrp.cdc.model.v2.company.req.CompanyShippingAddressReq;
import com.yyigou.dsrp.cdc.model.v2.customer.req.*;
import com.yyigou.dsrp.cdc.service.utils.PageUtils;
import com.yyigou.dsrp.cdc.service.utils.RedisClientUtil;
import com.yyigou.dsrp.cdc.service.v2.company.CompanyV2Service;
import com.yyigou.dsrp.cdc.service.v2.customer.CustomerCategoryV2Service;
import com.yyigou.dsrp.cdc.service.v2.customer.CustomerGspAuditService;
import com.yyigou.dsrp.cdc.service.v2.customer.CustomerV2Service;
import com.yyigou.dsrp.cdc.service.v2.supplier.SupplierV2Service;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service("customerV2Service")
@RequiredArgsConstructor
@Slf4j
public class CustomerV2ServiceImpl extends ServiceImpl<CustomerV2DAO, CustomerV2> implements CustomerV2Service {
    private final NumberCenterService numberCenterService;

    @Resource
    private CustomerV2DAO customerV2DAO;

    @Resource
    private CustomerBizDAO customerBizDAO;

    @Resource
    private CustomerBaseDAO customerBaseDAO;

    @Resource
    private CustomerGspAuditDAO customerGspAuditDAO;

    @Resource
    private CustomerCategoryV2Service customerCategoryV2Service;

    @Resource
    private CompanyV2Service companyV2Service;

    @Resource
    private SupplierV2Service supplierV2Service;

    @Resource
    private CustomDocService customDocService;

    @Resource
    private DictEnterpriseService dictEnterpriseService;

    @Resource
    private TransactionTypeService transactionTypeService;

    @Resource
    private CompanyLinkmanV2DAO companyLinkmanV2DAO;

    @Resource
    private CompanyShippingAddressV2DAO companyShippingAddressV2DAO;

    @Resource
    private CustomerSalesManV2DAO customerSalesManV2DAO;

    @Resource
    private CustomerInvoiceV2DAO customerInvoiceV2DAO;

    @Resource
    private EmployeeService employeeService;

    @Resource
    private BusinessLogService businessLogService;

    @Resource
    private OrganizationService organizationService;

    @Resource
    private GradeControlService gradeControlService;

    @Resource
    private ULogService uLogService;

    @Resource
    private RedisClientUtil redisClientUtil;

    @Resource
    private UimTenantService uimTenantService;

    @Resource
    private CustomerGspAuditService customerGspAuditService;

    @Resource
    private CertService certService;

    @Override
    public PageVo<CustomerPageVO> manageFindListPage(OperationModel operationModel, CustomerManageQueryListPageReq queryReq, PageDto pageDTO) {
        ValidatorUtils.checkEmptyThrowEx(queryReq, "查询条件不能为空");

        Page<CustomerV2WithBiz> page = PageHelper.startPage(pageDTO.getPageIndex(), pageDTO.getPageSize(), StringUtils.isBlank(pageDTO.getOrderBy()) ? "b.create_time desc" : pageDTO.getOrderBy());
        customerV2DAO.selectManageView(queryReq);

        return PageUtils.convertPageVo(page, data -> completeSupplementPageVO(operationModel.getEnterpriseNo(), data));
    }

    @Override
    public PageVo<CustomerPageVO> useFindListPage(OperationModel operationModel, CustomerUseQueryListPageReq queryReq, PageDto pageDTO) {
        ValidatorUtils.checkEmptyThrowEx(queryReq, "查询条件不能为空");

        Page<CustomerV2WithBiz> page = PageHelper.startPage(pageDTO.getPageIndex(), pageDTO.getPageSize(), StringUtils.isBlank(pageDTO.getOrderBy()) ? "b.create_time desc" : pageDTO.getOrderBy());
        customerV2DAO.selectUseView(queryReq);
        return PageUtils.convertPageVo(page, data -> completeSupplementPageVO(operationModel.getEnterpriseNo(), data));

    }

    @Override
    public PageVo<CustomerPageVO> findListPageForTenant(CustomerFindQueryListPageForTenantReq queryReq, PageDto pageDTO) {
        ValidatorUtils.checkEmptyThrowEx(queryReq.getEnterpriseNo(), "租户编号为空");
        try {
            UapThreadLocalUtil.setQueryPlanNextSqlJumpFlag(true);
            Set<String> customerCodeSet = customerV2DAO.selectDistinctCustomerCodeByUseOrgNoList(queryReq);
            if (CollectionUtils.isEmpty(customerCodeSet)) {
                return new PageVo<>(pageDTO.getPageIndex(), pageDTO.getPageSize(), 0L, new ArrayList<>());
            }
            queryReq.setCustomerCodeSet(customerCodeSet);
        } finally {
            UapThreadLocalUtil.setQueryPlanNextSqlJumpFlag(null);
        }
        Page<CustomerV2WithBase> page = PageHelper.startPage(pageDTO.getPageIndex(), pageDTO.getPageSize(), StringUtils.isBlank(pageDTO.getOrderBy()) ? "create_time desc" : pageDTO.getOrderBy());
        List<CustomerV2WithBase> customerWithBaseForTenantList = customerV2DAO.findListPageForTenant(queryReq);
        if (CollectionUtils.isEmpty(customerWithBaseForTenantList)) {
            return new PageVo<>(pageDTO.getPageIndex(), pageDTO.getPageSize(), 0L, new ArrayList<>());
        }

        return PageUtils.convertPageVo(page, data -> completeSupplementForTenantPageVO(queryReq.getEnterpriseNo(), queryReq.getUseOrgNoList(), data));
    }

    @Override
    public PageVo<CustomerReversePageVO> reverseUseFindListPageNoAuthZ(OperationModel operationModel, CustomerReverseUseQueryListPageNoAuthZReq queryReq, PageDto pageDTO) {
        ValidatorUtils.checkEmptyThrowEx(queryReq, "查询条件不能为空");
        ValidatorUtils.checkEmptyThrowEx(queryReq.getUseOrgNo(), "使用组织不能为空");

        Page<CustomerV2WithBase> page = PageHelper.startPage(pageDTO.getPageIndex(), pageDTO.getPageSize(), StringUtils.isBlank(pageDTO.getOrderBy()) ? "a.create_time desc" : pageDTO.getOrderBy());
        customerV2DAO.selectReverseUseViewNoAuthZ(queryReq);
        return PageUtils.convertPageVo(page, data -> completeSupplementReversePageVO(operationModel.getEnterpriseNo(), queryReq.getUseOrgNo(), data));

    }


    private void validateSaveBasicAndBizReq(OperationModel operationModel, CustomerSaveBasicAndBizReq params) {
        //必填项校验
        ValidatorUtils.checkEmptyThrowEx(params.getCustomerCode(), "客户编码不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getCustomerName(), "客户名称不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getTransactionType(), "客户类型不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getCustomerCategoryNo(), "客户分类不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getCompanyName(), "企业名称不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getUnifiedSocialCode(), "统一社会信用代码不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getFactoryType(), "企业注册地域不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getTaxCategory(), "纳税类别不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getRetailInvestors(), "是否散户不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getIsAssociatedEnterprise(), "是否内部组织不能为空");
        if (null != params.getIsAssociatedEnterprise() && CommonIfEnum.YES.getValue().equals(params.getIsAssociatedEnterprise())) {
            ValidatorUtils.checkEmptyThrowEx(params.getAssociatedOrgNo(), "对应内部组织不能为空");
        }
        ValidatorUtils.checkEmptyThrowEx(params.getIsMedicalInstitution(), "是否医疗机构不能为空");
        if (InstitutionalTypeEnum.yy.getType().equals(params.getIsMedicalInstitution().toString())) {
            ValidatorUtils.checkEmptyThrowEx(params.getHospitalType(), "医院性质不能为空");
            ValidatorUtils.checkEmptyThrowEx(params.getHospitalClass(), "医院等级不能为空");
        }

        //范围校验
        if (StringUtils.isNotEmpty(params.getCustomerCode())) {
            ValidatorUtils.checkTrueThrowEx(params.getCustomerCode().length() > 50, "客户编码不能大于50");
        }
        if (StringUtils.isNotEmpty(params.getCustomerName())) {
            ValidatorUtils.checkTrueThrowEx(params.getCustomerName().length() > 128, "客户名称不能大于128");
        }
        if (StringUtils.isNotEmpty(params.getCustomerNameEn())) {
            ValidatorUtils.checkTrueThrowEx(params.getCustomerNameEn().length() > 300, "客户英文名称不能大于300");
        }
        if (StringUtils.isNotEmpty(params.getMnemonicCode())) {
            ValidatorUtils.checkTrueThrowEx(params.getMnemonicCode().length() > 100, "助记码不能大于100");
        }
        if (StringUtils.isNotEmpty(params.getCompanyName())) {
            ValidatorUtils.checkTrueThrowEx(params.getCompanyName().length() > 128, "企业名称不能大于128");
        }
        if (StringUtils.isNotEmpty(params.getUnifiedSocialCode())) {
            ValidatorUtils.checkTrueThrowEx(params.getUnifiedSocialCode().length() > 100, "统一社会信用代码不能大于100");
        }
        if (StringUtils.isNotEmpty(params.getCountry())) {
            ValidatorUtils.checkTrueThrowEx(params.getCountry().length() > 50, "国家/地区不能大于50");
        }
        if (StringUtils.isNotEmpty(params.getRemark())) {
            ValidatorUtils.checkTrueThrowEx(params.getRemark().length() > 300, "备注不能大于300");
        }


        if (StringUtils.isNotEmpty(params.getReceiveAgreement())) {
            ValidatorUtils.checkTrueThrowEx(params.getReceiveAgreement().length() > 500, "收款协议不能大于500");
        }
        if (StringUtils.isNotEmpty(params.getReceiveCondition())) {
            ValidatorUtils.checkTrueThrowEx(params.getReceiveCondition().length() > 500, "收款条件不能大于500");
        }
        if (StringUtils.isNotEmpty(params.getCreditAmount())) {
            ValidatorUtils.checkTrueThrowEx(params.getCreditAmount().length() > 20, "信用额度不能大于20");
        }
        if (StringUtils.isNotEmpty(params.getCreditDates())) {
            ValidatorUtils.checkTrueThrowEx(params.getCreditDates().length() > 20, "信用期限不能大于20");
        }
        if (StringUtils.isNotEmpty(params.getOwnerCompany())) {
            ValidatorUtils.checkTrueThrowEx(params.getOwnerCompany().length() > 100, "业务归属不能大于100");
        }
    }


    @Override
    @Transactional
    public Boolean manageDeleteCustomer(OperationModel operationModel, CustomerDeleteReq params) {
        ValidatorUtils.checkEmptyThrowEx(params.getCustomerCode(), "客户编码不能为空");

        LambdaQueryWrapper<CustomerV2> customerQueryWrapper = Wrappers.<CustomerV2>lambdaQuery()
                .eq(CustomerV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CustomerV2::getManageOrgNo, params.getManageOrgNo())
                .eq(CustomerV2::getCustomerCode, params.getCustomerCode())
                .eq(CustomerV2::getDeleted, DeletedEnum.UN_DELETE.getValue());

        CustomerV2 customerV2 = customerV2DAO.selectOne(customerQueryWrapper);

        ValidatorUtils.checkEmptyThrowEx(customerV2, "客户不存在");

        ValidatorUtils.checkEmptyThrowEx(!CustomerBusinessFlagEnum.DRAFT.getValue().equals(customerV2.getBusinessFlag()), "客户不是草稿态");

        //基础信息
        CustomerV2 newCustomerV2 = new CustomerV2();
        newCustomerV2.setDeleted(DeletedEnum.DELETED.getValue());
        CommonUtil.fillOperateInfo(operationModel, newCustomerV2);
        customerV2DAO.update(newCustomerV2, customerQueryWrapper);

        //业务信息
        LambdaQueryWrapper<CustomerBiz> bizQueryWrapper = Wrappers.<CustomerBiz>lambdaQuery()
                .eq(CustomerBiz::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CustomerBiz::getUseOrgNo, params.getManageOrgNo())
                .eq(CustomerBiz::getCustomerCode, customerV2.getCustomerCode())
                .eq(CustomerBiz::getDeleted, DeletedEnum.UN_DELETE.getValue());

        CustomerBiz customerBiz = customerBizDAO.selectOne(bizQueryWrapper);
        ValidatorUtils.checkEmptyThrowEx(customerBiz, "客户业务信息不存在");

        CustomerBiz newCustomerBiz = new CustomerBiz();
        newCustomerBiz.setDeleted(DeletedEnum.DELETED.getValue());
        CommonUtil.fillOperateInfo(operationModel, newCustomerBiz);
        customerBizDAO.update(newCustomerBiz, bizQueryWrapper);

        // 分派信息
        CustomerBase newCustomerBase = new CustomerBase();
        newCustomerBase.setDeleted(DeletedEnum.DELETED.getValue());
        CommonUtil.fillOperateInfo(operationModel, newCustomerBase);
        customerBaseDAO.update(newCustomerBase, Wrappers.<CustomerBase>lambdaQuery()
                .eq(CustomerBase::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CustomerBase::getUseOrgNo, customerV2.getManageOrgNo())
                .eq(CustomerBase::getCustomerCode, customerV2.getCustomerCode())
                .eq(CustomerBase::getDeleted, DeletedEnum.UN_DELETE.getValue()));

        // 联系人
        CompanyLinkmanV2 newCompanyLinkmanV2 = new CompanyLinkmanV2();
        newCompanyLinkmanV2.setDeleted(DeletedEnum.DELETED.getValue());
        CommonUtil.fillOperateInfo(operationModel, newCompanyLinkmanV2);
        companyLinkmanV2DAO.update(newCompanyLinkmanV2, Wrappers.<CompanyLinkmanV2>lambdaQuery()
                .eq(CompanyLinkmanV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CompanyLinkmanV2::getUseOrgNo, customerV2.getManageOrgNo())
                .eq(CompanyLinkmanV2::getSourceNo, customerV2.getCustomerCode())
                .eq(CompanyLinkmanV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));

        // 联系地址
        CompanyShippingAddressV2 newCompanyShippingAddressV2 = new CompanyShippingAddressV2();
        newCompanyShippingAddressV2.setDeleted(DeletedEnum.DELETED.getValue());
        CommonUtil.fillOperateInfo(operationModel, newCompanyShippingAddressV2);
        companyShippingAddressV2DAO.update(newCompanyShippingAddressV2, Wrappers.<CompanyShippingAddressV2>lambdaQuery()
                .eq(CompanyShippingAddressV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CompanyShippingAddressV2::getUseOrgNo, customerV2.getManageOrgNo())
                .eq(CompanyShippingAddressV2::getSourceNo, customerV2.getCustomerCode())
                .eq(CompanyShippingAddressV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));

        // 负责人
        CustomerSalesManV2 newCustomerSalesManV2 = new CustomerSalesManV2();
        newCustomerSalesManV2.setDeleted(DeletedEnum.DELETED.getValue());
        CommonUtil.fillOperateInfo(operationModel, newCustomerSalesManV2);
        customerSalesManV2DAO.update(newCustomerSalesManV2, Wrappers.<CustomerSalesManV2>lambdaQuery()
                .eq(CustomerSalesManV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CustomerSalesManV2::getUseOrgNo, customerV2.getManageOrgNo())
                .eq(CustomerSalesManV2::getCustomerCode, customerV2.getCustomerCode())
                .eq(CustomerSalesManV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));

        // 发票
        CustomerInvoiceV2 newCustomerInvoiceV2 = new CustomerInvoiceV2();
        newCustomerInvoiceV2.setDeleted(DeletedEnum.DELETED.getValue());
        CommonUtil.fillOperateInfo(operationModel, newCustomerInvoiceV2);
        customerInvoiceV2DAO.update(newCustomerInvoiceV2, Wrappers.<CustomerInvoiceV2>lambdaQuery()
                .eq(CustomerInvoiceV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CustomerInvoiceV2::getUseOrgNo, customerV2.getManageOrgNo())
                .eq(CustomerInvoiceV2::getCustomerCode, customerV2.getCustomerCode())
                .eq(CustomerInvoiceV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));

        Map<String, Object> businessValue = new HashMap<>();
        newCustomerV2.setCustomerCode(customerV2.getCustomerCode());
        businessValue.put("customer", newCustomerV2);
        newCustomerBiz.setCustomerCode(customerBiz.getCustomerCode());
        businessValue.put("customerBiz", newCustomerBiz);
        newCustomerBase.setCustomerCode(customerV2.getCustomerCode());
        businessValue.put("customerBase", newCustomerBase);

        newCompanyLinkmanV2.setSourceNo(customerV2.getCustomerCode());
        businessValue.put("companyLinkman", newCompanyLinkmanV2);

        newCompanyShippingAddressV2.setSourceNo(customerV2.getCustomerCode());
        businessValue.put("companyShippingAddress", newCompanyShippingAddressV2);

        newCustomerSalesManV2.setCustomerCode(customerV2.getCustomerCode());
        businessValue.put("newCustomerSalesMan", newCustomerSalesManV2);

        newCustomerInvoiceV2.setCustomerCode(customerV2.getCustomerCode());
        businessValue.put("customerInvoice", newCustomerInvoiceV2);

        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                try {
                    businessLogService.saveLog(DLogLevel.INFO.name(), ViewNameConstant.BDC_CUSTOMER_VIEW, params.getCustomerCode() + params.getManageOrgNo(), "删除客户档案", "删除客户档案", JSON.toJSONString(businessValue), "");
                } catch (Exception e) {
                    log.error("删除客户档案日志保存失败", e);
                }
            }
        });

        return true;
    }


    private void validateEditBasicAndBizReq(OperationModel operationModel, CustomerEditBasicAndBizReq params) {
        //必填项校验
        ValidatorUtils.checkEmptyThrowEx(params.getCustomerCode(), "客户编码不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getCustomerName(), "客户名称不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getTransactionType(), "客户类型不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getCustomerCategoryNo(), "客户分类不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getCompanyName(), "企业名称不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getUnifiedSocialCode(), "统一社会信用代码不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getFactoryType(), "企业注册地域不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getTaxCategory(), "纳税类别不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getRetailInvestors(), "是否散户不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getIsAssociatedEnterprise(), "是否内部组织不能为空");
        if (null != params.getIsAssociatedEnterprise() && CommonIfEnum.YES.getValue().equals(params.getIsAssociatedEnterprise())) {
            ValidatorUtils.checkEmptyThrowEx(params.getAssociatedOrgNo(), "对应内部组织不能为空");
        }
        ValidatorUtils.checkEmptyThrowEx(params.getIsMedicalInstitution(), "是否医疗机构不能为空");
        if (InstitutionalTypeEnum.yy.getType().equals(params.getIsMedicalInstitution().toString())) {
            ValidatorUtils.checkEmptyThrowEx(params.getHospitalType(), "医院性质不能为空");
            ValidatorUtils.checkEmptyThrowEx(params.getHospitalClass(), "医院等级不能为空");
        }

        //范围校验
        if (StringUtils.isNotEmpty(params.getCustomerCode())) {
            ValidatorUtils.checkTrueThrowEx(params.getCustomerCode().length() > 50, "客户编码不能大于50");
        }
        if (StringUtils.isNotEmpty(params.getCustomerName())) {
            ValidatorUtils.checkTrueThrowEx(params.getCustomerName().length() > 128, "客户名称不能大于128");
        }
        if (StringUtils.isNotEmpty(params.getCustomerNameEn())) {
            ValidatorUtils.checkTrueThrowEx(params.getCustomerNameEn().length() > 300, "客户英文名称不能大于300");
        }
        if (StringUtils.isNotEmpty(params.getMnemonicCode())) {
            ValidatorUtils.checkTrueThrowEx(params.getMnemonicCode().length() > 100, "助记码不能大于100");
        }
        if (StringUtils.isNotEmpty(params.getCompanyName())) {
            ValidatorUtils.checkTrueThrowEx(params.getCompanyName().length() > 128, "企业名称不能大于128");
        }
        if (StringUtils.isNotEmpty(params.getUnifiedSocialCode())) {
            ValidatorUtils.checkTrueThrowEx(params.getUnifiedSocialCode().length() > 100, "统一社会信用代码不能大于100");
        }
        if (StringUtils.isNotEmpty(params.getCountry())) {
            ValidatorUtils.checkTrueThrowEx(params.getCountry().length() > 50, "国家/地区不能大于50");
        }
        if (StringUtils.isNotEmpty(params.getRemark())) {
            ValidatorUtils.checkTrueThrowEx(params.getRemark().length() > 300, "备注不能大于300");
        }


        if (StringUtils.isNotEmpty(params.getReceiveAgreement())) {
            ValidatorUtils.checkTrueThrowEx(params.getReceiveAgreement().length() > 500, "收款协议不能大于500");
        }
        if (StringUtils.isNotEmpty(params.getReceiveCondition())) {
            ValidatorUtils.checkTrueThrowEx(params.getReceiveCondition().length() > 500, "收款条件不能大于500");
        }
        if (StringUtils.isNotEmpty(params.getCreditAmount())) {
            ValidatorUtils.checkTrueThrowEx(params.getCreditAmount().length() > 20, "信用额度不能大于20");
        }
        if (StringUtils.isNotEmpty(params.getCreditDates())) {
            ValidatorUtils.checkTrueThrowEx(params.getCreditDates().length() > 20, "信用期限不能大于20");
        }
        if (StringUtils.isNotEmpty(params.getOwnerCompany())) {
            ValidatorUtils.checkTrueThrowEx(params.getOwnerCompany().length() > 100, "业务归属不能大于100");
        }
    }

    private void validateEditBasicReq(OperationModel operationModel, CustomerEditBasicReq params) {
        //必填项校验
        ValidatorUtils.checkEmptyThrowEx(params.getCustomerCode(), "客户编码不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getCustomerName(), "客户名称不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getTransactionType(), "客户类型不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getCustomerCategoryNo(), "客户分类不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getCompanyName(), "企业名称不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getUnifiedSocialCode(), "统一社会信用代码不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getFactoryType(), "企业注册地域不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getTaxCategory(), "纳税类别不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getRetailInvestors(), "是否散户不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getIsAssociatedEnterprise(), "是否内部组织不能为空");
        if (null != params.getIsAssociatedEnterprise() && CommonIfEnum.YES.getValue().equals(params.getIsAssociatedEnterprise())) {
            ValidatorUtils.checkEmptyThrowEx(params.getAssociatedOrgNo(), "对应内部组织不能为空");
        }
        ValidatorUtils.checkEmptyThrowEx(params.getIsMedicalInstitution(), "是否医疗机构不能为空");
        if (InstitutionalTypeEnum.yy.getType().equals(params.getIsMedicalInstitution().toString())) {
            ValidatorUtils.checkEmptyThrowEx(params.getHospitalType(), "医院性质不能为空");
            ValidatorUtils.checkEmptyThrowEx(params.getHospitalClass(), "医院等级不能为空");
        }

        //范围校验
        if (StringUtils.isNotEmpty(params.getCustomerCode())) {
            ValidatorUtils.checkTrueThrowEx(params.getCustomerCode().length() > 50, "客户编码不能大于50");
        }
        if (StringUtils.isNotEmpty(params.getCustomerName())) {
            ValidatorUtils.checkTrueThrowEx(params.getCustomerName().length() > 128, "客户名称不能大于128");
        }
        if (StringUtils.isNotEmpty(params.getCustomerNameEn())) {
            ValidatorUtils.checkTrueThrowEx(params.getCustomerNameEn().length() > 300, "客户英文名称不能大于300");
        }
        if (StringUtils.isNotEmpty(params.getMnemonicCode())) {
            ValidatorUtils.checkTrueThrowEx(params.getMnemonicCode().length() > 100, "助记码不能大于100");
        }
        if (StringUtils.isNotEmpty(params.getCompanyName())) {
            ValidatorUtils.checkTrueThrowEx(params.getCompanyName().length() > 128, "企业名称不能大于128");
        }
        if (StringUtils.isNotEmpty(params.getUnifiedSocialCode())) {
            ValidatorUtils.checkTrueThrowEx(params.getUnifiedSocialCode().length() > 100, "统一社会信用代码不能大于100");
        }
        if (StringUtils.isNotEmpty(params.getCountry())) {
            ValidatorUtils.checkTrueThrowEx(params.getCountry().length() > 50, "国家/地区不能大于50");
        }
        if (StringUtils.isNotEmpty(params.getRemark())) {
            ValidatorUtils.checkTrueThrowEx(params.getRemark().length() > 300, "备注不能大于300");
        }
    }


    @Override
    public CustomerVO getCustomer(OperationModel operationModel, CustomerGetReq params) {
        CustomerV2 customer = customerV2DAO.selectOne(Wrappers.<CustomerV2>lambdaQuery()
                .eq(CustomerV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CustomerV2::getCustomerCode, params.getCustomerCode())
                .eq(CustomerV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
        ValidatorUtils.checkEmptyThrowEx(customer, "客户不存在");

        return packDetail(operationModel, params, customer);
    }

    private CustomerVO packDetail(OperationModel operationModel, CustomerGetReq params, CustomerV2 customer) {
        //基本信息
        CustomerVO customerVO = new CustomerVO();
        BeanUtils.copyProperties(customer, customerVO);

        //业务信息
        CustomerBiz customerBiz = customerBizDAO.selectOne(Wrappers.<CustomerBiz>lambdaQuery()
                .eq(CustomerBiz::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CustomerBiz::getUseOrgNo, params.getUseOrgNo())
                .eq(CustomerBiz::getCustomerCode, params.getCustomerCode())
                .eq(CustomerBiz::getDeleted, DeletedEnum.UN_DELETE.getValue()));
        if (null != customerBiz) {
            BeanUtils.copyProperties(customerBiz, customerVO);
        }

        CustomerGspAudit customerGspAudit = customerGspAuditDAO.selectOne(Wrappers.<CustomerGspAudit>lambdaQuery()
                .eq(CustomerGspAudit::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CustomerGspAudit::getUseOrgNo, params.getUseOrgNo())
                .eq(CustomerGspAudit::getCustomerCode, params.getCustomerCode())
                .eq(CustomerGspAudit::getDeleted, DeletedEnum.UN_DELETE.getValue()));

        //分派信息
        CustomerBase customerAssign = customerBaseDAO.selectOne(Wrappers.<CustomerBase>lambdaQuery()
                .eq(CustomerBase::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CustomerBase::getUseOrgNo, params.getUseOrgNo())
                .eq(CustomerBase::getCustomerCode, params.getCustomerCode())
                .eq(CustomerBase::getDeleted, DeletedEnum.UN_DELETE.getValue()));
        if (null != customerAssign) {
            customerVO.setControlStatus(customerAssign.getControlStatus());
            customerVO.setControlStatusName(CustomerControlStatusEnum.getByType(customerAssign.getControlStatus()) == null ? null : CustomerControlStatusEnum.getByType(customerAssign.getControlStatus()).getName());
        }

        Map<String, Object> infoMap = collectBasicInfo(operationModel.getEnterpriseNo(), Collections.singletonList(customer), Collections.singletonList(params.getUseOrgNo()), Sets.newHashSet(
                "companyMap",
                "taxCategoryMap",
                "economicTypeMap",
                "orgMap",
                "categoryMap",
                "currencyMap",
                "cooperationMap",
                "settlementModesMap",
                "businessTypeMap",
                "priceCustomerCategoryMap",
                "transactionTypeMap",
                "linkManMap",
                "addressMap",
                "salesManMap",
                "invoiceMap",
                "bankMap",
                "countryRegionMap"
        ));

        //填充企业信息
        final CompanyV2 company = ((Map<String, CompanyV2>) infoMap.get("companyMap")).get(customer.getCompanyNo());
        final Map<String, String> countryRegionMap = (Map<String, String>) infoMap.get("countryRegionMap");

        if (company != null) {
            customerVO.setCompanyName(company.getCompanyName());
            customerVO.setUnifiedSocialCode(company.getUnifiedSocialCode());
//            customerVO.setCountry(company.getCountry());
            customerVO.setCountryRegionId(company.getCountryRegionId());
            if (StringUtils.isNotEmpty(company.getCountryRegionId())) {
                customerVO.setCountryRegionName(countryRegionMap.get(company.getCountryRegionId()));
            }
            customerVO.setFactoryType(company.getFactoryType());
            customerVO.setFactoryTypeName(FactoryTypeEnum.getByType(company.getFactoryType()) == null ? null : FactoryTypeEnum.getByType(company.getFactoryType()).getName());
            customerVO.setEconomicType(company.getEconomicType());
            customerVO.setIsMedicalInstitution(company.getIsMedicalInstitution());
            customerVO.setInstitutionalType(company.getInstitutionalType());
            customerVO.setHospitalType(company.getHospitalType());
            customerVO.setHospitalClass(company.getHospitalClass());
//            customerVO.setIsAssociatedEnterprise(company.getIsAssociatedEnterprise());
//            customerVO.setAssociatedOrgNo(company.getAssociatedOrgNo());
//            customerVO.setAssociatedOrgCode(company.getAssociatedOrgCode());
//            customerVO.setAssociatedOrgName(company.getAssociatedOrgName());

            if (null != company.getRegionCode()) {
                customerVO.setRegionCode(company.getRegionCode());

                List<AreaCodeVo> areaCodeList = dictEnterpriseService.findAreaByCodes(Collections.singletonList(company.getRegionCode()));
                final Map<String, AreaCodeVo> areaMap = areaCodeList.stream().collect(Collectors.toMap(AreaCodeVo::getAreaCode, Function.identity()));
                customerVO.setRegionFullName(areaMap.getOrDefault(company.getRegionCode(), new AreaCodeVo()).getAreaFullName());
            }

            customerVO.setEmail(company.getEmail());
            customerVO.setAddress(company.getAddress());
            customerVO.setIsMedicalInstitution(company.getIsMedicalInstitution());
            customerVO.setIsMedicalInstitutionName(CommonIfEnum.getNameByValue(company.getIsMedicalInstitution()));
            customerVO.setInstitutionalType(company.getInstitutionalType());
            customerVO.setInstitutionalTypeName(InstitutionalTypeEnum.getByType(customerVO.getInstitutionalType()) != null ? InstitutionalTypeEnum.getByType(customerVO.getInstitutionalType()).getName() : "");
            customerVO.setHospitalType(company.getHospitalType());
            customerVO.setHospitalTypeName(com.yyigou.dsrp.cdc.common.enums.company.HospitalTypeEnum.getByType(customerVO.getHospitalType()) != null ? com.yyigou.dsrp.cdc.common.enums.company.HospitalTypeEnum.getByType(customerVO.getHospitalType()).getName() : "");
            customerVO.setHospitalClass(company.getHospitalClass());
            customerVO.setHospitalClassName(HospitalClassEnum.getByType(customerVO.getHospitalClass()) != null ? HospitalClassEnum.getByType(customerVO.getHospitalClass()).getName() : "");

            if (StringUtils.isNotBlank(company.getTaxCategory())) {
                final Map<String, CustomDocResponse> itemByCustomDocCode = (Map<String, CustomDocResponse>) infoMap.get("taxCategoryMap");
                customerVO.setTaxCategory(company.getTaxCategory());
                customerVO.setTaxCategoryName(itemByCustomDocCode.getOrDefault(company.getTaxCategory(), new CustomDocResponse()).getDocItemName());
            }
            if (StringUtils.isNotBlank(company.getEconomicType())) {
                final Map<String, CustomDocResponse> itemByCustomDocCode = (Map<String, CustomDocResponse>) infoMap.get("economicTypeMap");
                customerVO.setEconomicTypeName(itemByCustomDocCode.getOrDefault(company.getEconomicType(), new CustomDocResponse()).getDocItemName());
            }

            // 资料
            CompanyCertQueryRequest companyCertQueryRequest = new CompanyCertQueryRequest();
            companyCertQueryRequest.setEnterpriseNo(operationModel.getEnterpriseNo());
            companyCertQueryRequest.setOrgNo(params.getUseOrgNo());
            companyCertQueryRequest.setCompanyNo(company.getCompanyNo());
            companyCertQueryRequest.setSourceDocType(CertSourceTypeEnum.CUSTOMER.getValue());
            companyCertQueryRequest.setSourceDocCode(customer.getCustomerCode());
            List<CompanyCertResponse> companyCertResponseList = certService.findList(companyCertQueryRequest);
            if (CollectionUtils.isNotEmpty(companyCertResponseList)) {
                Map<Integer, List<CompanyCertResponse>> docType2CertList = companyCertResponseList.stream().collect(Collectors.groupingBy(CompanyCertResponse::getSourceDocType));

                // 企业资料
                if (docType2CertList.containsKey(CertSourceTypeEnum.COMPANY.getValue())) {
                    List<CompanyCertResponse> rows = docType2CertList.get(CertSourceTypeEnum.COMPANY.getValue());
                    if (CollectionUtils.isNotEmpty(rows)) {
                        customerVO.setCompanyCertList(BeanUtil.copyFieldsListForJSON(rows, CompanyCertVO.class));
                    }
                }

                // 使用组织资料
                if (docType2CertList.containsKey(CertSourceTypeEnum.CUSTOMER.getValue())) {
                    List<CompanyCertResponse> rows = docType2CertList.get(CertSourceTypeEnum.CUSTOMER.getValue());
                    if (CollectionUtils.isNotEmpty(rows)) {
                        customerVO.setCustomerCertList(BeanUtil.copyFieldsListForJSON(rows, CompanyCertVO.class));
                    }
                }
            }
        }

        //填充编码字段对应的名称
        customerVO.setRetailInvestorsName(CommonIfEnum.getNameByValue(customer.getRetailInvestors()));

        customerVO.setIsAssociatedEnterpriseName(CommonIfEnum.getNameByValue(customer.getIsAssociatedEnterprise()));

        final Map<String, OrganizationVo> orgNo2OrganizationVo = (Map<String, OrganizationVo>) infoMap.get("orgMap");


        if (orgNo2OrganizationVo.containsKey(customer.getManageOrgNo())) {
            customerVO.setManageOrgName(orgNo2OrganizationVo.get(customer.getManageOrgNo()).getOrgName());
        }

        customerVO.setUseOrgNo(params.getUseOrgNo());

        if (orgNo2OrganizationVo.containsKey(params.getUseOrgNo())) {
            customerVO.setUseOrgName(orgNo2OrganizationVo.get(params.getUseOrgNo()).getOrgName());
        }

        //客户分类
        if (StringUtils.isNotBlank(customer.getCustomerCategoryNo())) {
            final CustomerCategoryV2 customerCategory = ((Map<String, CustomerCategoryV2>) infoMap.get("categoryMap")).get(customer.getCustomerCategoryNo());
            if (customerCategory != null) {
                customerVO.setCustomerCategoryName(customerCategory.getCategoryName());
            }
        }

        if (null != customerBiz) {
            //交易币种
            if (StringUtils.isNotBlank(customerBiz.getCurrencyId())) {
                final Map<String, String> currencyMap = (Map<String, String>) infoMap.get("currencyMap");
                customerVO.setCurrencyName(currencyMap.getOrDefault(customerBiz.getCurrencyId(), ""));
            }

            //合作性质
            if (StringUtils.isNotBlank(customerBiz.getCooperationMode())) {
                final Map<String, CustomDocResponse> cooperationMap = (Map<String, CustomDocResponse>) infoMap.get("cooperationMap");
                customerVO.setCooperationModeName(cooperationMap.getOrDefault(customerBiz.getCooperationMode(), new CustomDocResponse()).getDocItemName());
            }

            //结算方式
            if (StringUtils.isNotBlank(customerBiz.getSettlementModes())) {
                final Map<String, String> settlementModesMap = (Map<String, String>) infoMap.get("settlementModesMap");
                customerVO.setSettlementModesName(settlementModesMap.getOrDefault(customerBiz.getSettlementModes(), ""));
            }

            //客户性质
            if (StringUtils.isNotBlank(customerBiz.getBusinessType())) {
                final Map<String, CustomDocResponse> businessTypeMap = (Map<String, CustomDocResponse>) infoMap.get("businessTypeMap");
                customerVO.setBusinessTypeName(businessTypeMap.getOrDefault(customerBiz.getBusinessType(), new CustomDocResponse()).getDocItemName());
            }

            //价格体系
            if (StringUtils.isNotBlank(customerBiz.getPriceCategoryCode())) {
                final Map<String, String> priceCustomerCategoryMap = (Map<String, String>) infoMap.get("priceCustomerCategoryMap");
                customerVO.setPriceCategoryName(priceCustomerCategoryMap.getOrDefault(customerBiz.getPriceCategoryCode(), ""));
            }
        }

        if (null != customerGspAudit) {
            customerVO.setGspStatus(customerGspAudit.getGspAuditStatus());
            customerVO.setGspStatusName(null != GspAuditStatusEnum.getByType(customerGspAudit.getGspAuditStatus()) ? GspAuditStatusEnum.getByType(customerGspAudit.getGspAuditStatus()).getName() : "");
        }

        //客户类型
        if (StringUtils.isNotBlank(customer.getTransactionType())) {
            final Map<String, TransactionTypeResponse> transactionTypeMap = (Map<String, TransactionTypeResponse>) infoMap.get("transactionTypeMap");
            customerVO.setTransactionTypeName(transactionTypeMap.getOrDefault(customer.getTransactionType(), new TransactionTypeResponse()).getTransactionTypeName());
        }

        // 获取联系人
        final Map<String, Map<String, List<CompanyLinkmanV2>>> code2org2LinkManList = (Map<String, Map<String, List<CompanyLinkmanV2>>>) infoMap.get("linkManMap");

        // 获取地址
        final Map<String, Map<String, List<CompanyShippingAddressV2>>> code2org2AddressList = (Map<String, Map<String, List<CompanyShippingAddressV2>>>) infoMap.get("addressMap");

        // 获取负责人
        final Map<String, Map<String, List<CustomerSalesManV2>>> code2org2SalesManList = (Map<String, Map<String, List<CustomerSalesManV2>>>) infoMap.get("salesManMap");

        // 获取开票
        final Map<String, Map<String, List<CustomerInvoiceV2>>> code2org2InvoiceList = (Map<String, Map<String, List<CustomerInvoiceV2>>>) infoMap.get("invoiceMap");


        //客户联系人
        final List<CompanyLinkmanV2> customerLinkmanList = code2org2LinkManList.getOrDefault(customer.getCustomerCode(), Collections.emptyMap()).get(params.getUseOrgNo());
        if (CollectionUtils.isNotEmpty(customerLinkmanList)) {
            List<CompanyLinkmanVO> customerLinkmanVoList = new ArrayList<>();
            customerLinkmanList.forEach(t -> {
                CompanyLinkmanVO vo = new CompanyLinkmanVO();
                BeanUtils.copyProperties(t, vo);

                vo.setSexName(null != SexEnum.getByType(vo.getSex()) ? SexEnum.getByType(vo.getSex()).getName() : "");
                vo.setStatusName(CommonStatusEnum.getNameByValue(vo.getStatus()));
                vo.setIsDefaultName(CommonIfEnum.getNameByValue(vo.getIsDefault()));

                customerLinkmanVoList.add(vo);
            });
            customerVO.setLinkmanList(customerLinkmanVoList);
        }

        //开票信息
        final List<CustomerInvoiceV2> invoiceList = code2org2InvoiceList.getOrDefault(customer.getCustomerCode(), Collections.emptyMap()).get(params.getUseOrgNo());
        if (CollectionUtils.isNotEmpty(invoiceList)) {
            List<CustomerInvoiceVO> customerInvoiceVoList = new ArrayList<>();
            invoiceList.forEach(t -> {
                CustomerInvoiceVO vo = new CustomerInvoiceVO();
                BeanUtils.copyProperties(t, vo);

                vo.setTypeName(InvoiceTypeEnum.getByType(t.getType()) == null ? null : InvoiceTypeEnum.getByType(t.getType()).getName());
                vo.setIsDefaultName(CommonIfEnum.getNameByValue(vo.getIsDefault()));

                customerInvoiceVoList.add(vo);
            });
            customerVO.setInvoiceList(customerInvoiceVoList);
        }

        //负责人信息
        final List<CustomerSalesManV2> salesManList = code2org2SalesManList.getOrDefault(customer.getCustomerCode(), Collections.emptyMap()).get(params.getUseOrgNo());
        if (CollectionUtils.isNotEmpty(salesManList)) {
            final List<String> salesManNoList = salesManList.stream().map(CustomerSalesManV2::getSalesManNo).collect(Collectors.toList());
            Map<String, EmployeeVo> employeeMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(salesManNoList)) {
                final List<EmployeeVo> employeeList = employeeService.getEmployeeList(operationModel.getEnterpriseNo(), salesManNoList);
                employeeMap = employeeList.stream().collect(Collectors.toMap(EmployeeVo::getEmployeeNo, Function.identity()));
            }
            List<CustomerSalesManVO> salesManVoList = new ArrayList<>();
            Map<String, EmployeeVo> finalEmployeeMap = employeeMap;
            salesManList.forEach(t -> {
                CustomerSalesManVO customerOrderManVO = new CustomerSalesManVO();
                BeanUtils.copyProperties(t, customerOrderManVO);
                customerOrderManVO.setDeptNo(t.getDeptNo());
                customerOrderManVO.setDeptName(t.getDeptName());

                if (finalEmployeeMap.containsKey(t.getSalesManNo())) {
                    customerOrderManVO.setMobile(finalEmployeeMap.get(t.getSalesManNo()).getMobile());
                }
                customerOrderManVO.setOrderSpecialistName(CommonIfEnum.getNameByValue(t.getOrderSpecialist()));
                customerOrderManVO.setIsDefaultName(CommonIfEnum.getNameByValue(t.getIsDefault()));

                salesManVoList.add(customerOrderManVO);
            });
            customerVO.setSalesManList(salesManVoList);
        }

        //银行信息
        final Map<String, List<CompanyBankV2>> bankMap = (Map<String, List<CompanyBankV2>>) infoMap.get("bankMap");
        if (bankMap.containsKey(customer.getCompanyNo())) {
            List<CompanyBankV2> bankList = bankMap.get(customer.getCompanyNo());
            if (CollectionUtils.isNotEmpty(bankList)) {
                final List<BankType> bankTypeList = companyV2Service.getBankTypeList();
                final Map<String, BankType> bankTypeMap = bankTypeList.stream().collect(Collectors.toMap(BankType::getBankCode, Function.identity(), (v1, v2) -> v1));
                final Map<String, String> currencyMap = (Map<String, String>) infoMap.get("currencyMap");

                List<CompanyBankVO> companyBankVoList = new ArrayList<>();
                bankList.forEach(t -> {
                    CompanyBankVO companyBankVO = new CompanyBankVO();
                    BeanUtils.copyProperties(t, companyBankVO);

                    companyBankVO.setBankTypeName(bankTypeMap.getOrDefault(t.getBankType(), new BankType()).getBankName());
                    companyBankVO.setCurrencyName(currencyMap.getOrDefault(t.getCurrencyId(), ""));
                    companyBankVO.setAccountTypeName(BankAccountTypeEnum.getNameByValue(t.getAccountType()));
                    companyBankVO.setStatusName(CommonStatusEnum.getNameByValue(t.getStatus()));
                    companyBankVO.setIsDefaultName(CommonIfEnum.getNameByValue(t.getIsDefault()));

                    companyBankVoList.add(companyBankVO);
                });

                customerVO.setBankList(companyBankVoList);

            }
        }

        //联系地址
        final List<CompanyShippingAddressV2> companyShippingAddressList = code2org2AddressList.getOrDefault(customer.getCustomerCode(), Collections.emptyMap()).get(params.getUseOrgNo());
        if (CollectionUtils.isNotEmpty(companyShippingAddressList)) {
            List<CompanyShippingAddressVO> salesManVoList = new ArrayList<>();
            companyShippingAddressList.forEach(t -> {
                final List<String> regionCodeList = companyShippingAddressList.stream().map(CompanyShippingAddressV2::getRegionCode).filter(s -> !StringUtils.isEmpty(s)).collect(Collectors.toList());
                Map<String, AreaCodeVo> areaMap = new HashMap<>();
                if (CollectionUtils.isNotEmpty(regionCodeList)) {
                    List<AreaCodeVo> areaCodeList = dictEnterpriseService.findAreaByCodes(regionCodeList);
                    areaMap = areaCodeList.stream().collect(Collectors.toMap(AreaCodeVo::getAreaCode, Function.identity()));
                }
                Map<String, AreaCodeVo> finalAreaMap = areaMap;
                CompanyShippingAddressVO companyShippingAddressVO = new CompanyShippingAddressVO();
                BeanUtils.copyProperties(t, companyShippingAddressVO);

                if (finalAreaMap.containsKey(t.getRegionCode())) {
//                    companyShippingAddressVO.setRegionName(finalAreaMap.get(t.getRegionCode()).getAreaName());
                    companyShippingAddressVO.setRegionFullName(finalAreaMap.get(t.getRegionCode()).getAreaFullName());
                }
                companyShippingAddressVO.setAddressTypeName(AddressEnum.getByType(t.getAddressType()) != null ? AddressEnum.getByType(t.getAddressType()).getName() : "");
                companyShippingAddressVO.setIsDefaultName(CommonIfEnum.getNameByValue(t.getIsDefault()));

                salesManVoList.add(companyShippingAddressVO);
            });
            customerVO.setLinkAddressList(salesManVoList);
        }

        //对应供应商
//        final List<SupplierV2> supplierV2List = supplierV2Service.queryByCompanyNo(operationModel, customer.getCompanyNo());
//        if (CollectionUtils.isNotEmpty(supplierV2List)) {
//            customerVO.setSupplierCode(supplierV2List.stream().map(SupplierV2::getSupplierCode).collect(Collectors.joining(",")));
//            customerVO.setSupplierName(supplierV2List.stream().map(SupplierV2::getSupplierName).collect(Collectors.joining(",")));
//        }

        // 不是管理组织不展示
        if (params.getUseOrgNo().equals(customer.getManageOrgNo())) {
            List<CustomerBase> customerBases = customerBaseDAO.selectList(Wrappers.<CustomerBase>lambdaQuery()
                    .eq(CustomerBase::getEnterpriseNo, operationModel.getEnterpriseNo())
                    .eq(CustomerBase::getCustomerCode, params.getCustomerCode())
                    .eq(CustomerBase::getDeleted, DeletedEnum.UN_DELETE.getValue()));

            List<OrganizationVo> assignrganizationVoList = organizationService.findListNoAuth(operationModel.getEnterpriseNo(), new ArrayList<>(customerBases.stream().map(CustomerBase::getUseOrgNo).collect(Collectors.toSet())));
            Map<String, OrganizationVo> assignOrgNo2OrganizationVo = assignrganizationVoList.stream().collect(Collectors.toMap(OrganizationVo::getOrgNo, Function.identity()));

            if (CollectionUtils.isNotEmpty(customerBases)) {
                List<CustomerAssignVO> customerAssignVOList = new ArrayList<>();

                for (CustomerBase customerBase : customerBases) {
                    CustomerAssignVO customerAssignVO = BeanUtil.copyFields(customerBase, CustomerAssignVO.class);

                    if (assignOrgNo2OrganizationVo.containsKey(customerBase.getManageOrgNo())) {
                        customerAssignVO.setManageOrgName(assignOrgNo2OrganizationVo.get(customerBase.getManageOrgNo()).getOrgName());
                    }

                    if (assignOrgNo2OrganizationVo.containsKey(customerBase.getUseOrgNo())) {
                        customerAssignVO.setUseOrgName(assignOrgNo2OrganizationVo.get(customerBase.getUseOrgNo()).getOrgName());
                    }

                    customerAssignVOList.add(customerAssignVO);
                }

                customerVO.setCustomerBaseList(customerAssignVOList);
            }
        }

        return customerVO;
    }

    @Override
    public CustomerV2 getCustomerByName(OperationModel operationModel, String customerName) {
        return customerV2DAO.selectOne(
                Wrappers.<CustomerV2>lambdaQuery()
                        .eq(CustomerV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                        .eq(CustomerV2::getCustomerName, customerName)
                        .eq(CustomerV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
    }

    @Override
    public CustomerV2 getCustomerByCode(OperationModel operationModel, String customerCode) {
        return customerV2DAO.selectOne(
                Wrappers.<CustomerV2>lambdaQuery()
                        .eq(CustomerV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                        .eq(CustomerV2::getCustomerCode, customerCode)
                        .eq(CustomerV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
    }

    private CustomerVO packBasicInfo(String enterprise, CustomerV2 customer) {
        if (null == customer) {
            return null;
        }

        CustomerVO customerVO = new CustomerVO();
        BeanUtils.copyProperties(customer, customerVO);

        Map<String, Object> infoMap = collectBasicInfo(enterprise, Collections.singletonList(customer), null, Sets.newHashSet(
                "companyMap",
                "taxCategoryMap",
                "economicTypeMap",
                "orgMap",
                "categoryMap",
                "transactionTypeMap",
                "countryRegionMap"
        ));

        //填充企业信息
        final CompanyV2 company = ((Map<String, CompanyV2>) infoMap.get("companyMap")).get(customer.getCompanyNo());
        final Map<String, String> countryRegionMap = (Map<String, String>) infoMap.get("countryRegionMap");

        if (company != null) {
            customerVO.setCompanyName(company.getCompanyName());
            customerVO.setUnifiedSocialCode(company.getUnifiedSocialCode());
//            customerVO.setCountry(company.getCountry());
            customerVO.setCountryRegionId(company.getCountryRegionId());
            if (StringUtils.isNotEmpty(company.getCountryRegionId())) {
                customerVO.setCountryRegionName(countryRegionMap.get(company.getCountryRegionId()));
            }
            customerVO.setFactoryType(company.getFactoryType());
            customerVO.setFactoryTypeName(FactoryTypeEnum.getByType(company.getFactoryType()) == null ? null : FactoryTypeEnum.getByType(company.getFactoryType()).getName());
            customerVO.setEconomicType(company.getEconomicType());
            customerVO.setIsMedicalInstitution(company.getIsMedicalInstitution());
            customerVO.setInstitutionalType(company.getInstitutionalType());
            customerVO.setHospitalType(company.getHospitalType());
            customerVO.setHospitalClass(company.getHospitalClass());
//            customerVO.setIsAssociatedEnterprise(company.getIsAssociatedEnterprise());
//            customerVO.setAssociatedOrgNo(company.getAssociatedOrgNo());
//            customerVO.setAssociatedOrgCode(company.getAssociatedOrgCode());
//            customerVO.setAssociatedOrgName(company.getAssociatedOrgName());

            if (null != company.getRegionCode()) {
                customerVO.setRegionCode(company.getRegionCode());

                List<AreaCodeVo> areaCodeList = dictEnterpriseService.findAreaByCodes(Collections.singletonList(company.getRegionCode()));
                final Map<String, AreaCodeVo> areaMap = areaCodeList.stream().collect(Collectors.toMap(AreaCodeVo::getAreaCode, Function.identity()));
                customerVO.setRegionFullName(areaMap.getOrDefault(company.getRegionCode(), new AreaCodeVo()).getAreaFullName());
            }

            customerVO.setEmail(company.getEmail());
            customerVO.setAddress(company.getAddress());
            customerVO.setIsMedicalInstitution(company.getIsMedicalInstitution());
            customerVO.setIsMedicalInstitutionName(CommonIfEnum.getNameByValue(company.getIsMedicalInstitution()));
            customerVO.setInstitutionalType(company.getInstitutionalType());
            customerVO.setInstitutionalTypeName(InstitutionalTypeEnum.getByType(customerVO.getInstitutionalType()) != null ? InstitutionalTypeEnum.getByType(customerVO.getInstitutionalType()).getName() : "");
            customerVO.setHospitalType(company.getHospitalType());
            customerVO.setHospitalTypeName(com.yyigou.dsrp.cdc.common.enums.company.HospitalTypeEnum.getByType(customerVO.getHospitalType()) != null ? com.yyigou.dsrp.cdc.common.enums.company.HospitalTypeEnum.getByType(customerVO.getHospitalType()).getName() : "");
            customerVO.setHospitalClass(company.getHospitalClass());
            customerVO.setHospitalClassName(HospitalClassEnum.getByType(customerVO.getHospitalClass()) != null ? HospitalClassEnum.getByType(customerVO.getHospitalClass()).getName() : "");

            if (StringUtils.isNotBlank(company.getTaxCategory())) {
                final Map<String, CustomDocResponse> itemByCustomDocCode = (Map<String, CustomDocResponse>) infoMap.get("taxCategoryMap");
                customerVO.setTaxCategory(company.getTaxCategory());
                customerVO.setTaxCategoryName(itemByCustomDocCode.getOrDefault(company.getTaxCategory(), new CustomDocResponse()).getDocItemName());
            }
            if (StringUtils.isNotBlank(company.getEconomicType())) {
                final Map<String, CustomDocResponse> itemByCustomDocCode = (Map<String, CustomDocResponse>) infoMap.get("economicTypeMap");
                customerVO.setEconomicTypeName(itemByCustomDocCode.getOrDefault(company.getEconomicType(), new CustomDocResponse()).getDocItemName());
            }
        }

        //填充编码字段对应的名称
        customerVO.setRetailInvestorsName(CommonIfEnum.getNameByValue(customer.getRetailInvestors()));

        customerVO.setIsAssociatedEnterpriseName(CommonIfEnum.getNameByValue(customer.getIsAssociatedEnterprise()));

        final Map<String, OrganizationVo> orgNo2OrganizationVo = (Map<String, OrganizationVo>) infoMap.get("orgMap");

        if (orgNo2OrganizationVo.containsKey(customer.getManageOrgNo())) {
            customerVO.setManageOrgName(orgNo2OrganizationVo.get(customer.getManageOrgNo()).getOrgName());
        }

        //客户分类
        if (StringUtils.isNotBlank(customer.getCustomerCategoryNo())) {
            final CustomerCategoryV2 customerCategory = ((Map<String, CustomerCategoryV2>) infoMap.get("categoryMap")).get(customer.getCustomerCategoryNo());
            if (customerCategory != null) {
                customerVO.setCustomerCategoryName(customerCategory.getCategoryName());
            }
        }

        //客户类型
        if (StringUtils.isNotBlank(customer.getTransactionType())) {
            final Map<String, TransactionTypeResponse> transactionTypeMap = (Map<String, TransactionTypeResponse>) infoMap.get("transactionTypeMap");
            customerVO.setTransactionTypeName(transactionTypeMap.getOrDefault(customer.getTransactionType(), new TransactionTypeResponse()).getTransactionTypeName());
        }

        return customerVO;
    }

    @Override
    public CustomerVO getCustomerByCode(String enterprise, String customerCode) {
        CustomerV2 customer = customerV2DAO.selectOne(
                Wrappers.<CustomerV2>lambdaQuery()
                        .eq(CustomerV2::getEnterpriseNo, enterprise)
                        .eq(CustomerV2::getCustomerCode, customerCode)
                        .eq(CustomerV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));

        return packBasicInfo(enterprise, customer);
    }

    @Override
    public CustomerVO getDetailCustomerByName(OperationModel operationModel, String useOrgNo, String customerName) {
        CustomerV2 customerV2 = customerV2DAO.selectOne(
                Wrappers.<CustomerV2>lambdaQuery()
                        .eq(CustomerV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                        .eq(CustomerV2::getCustomerName, customerName)
                        .eq(CustomerV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
        if (null == customerV2) {
            return null;
        }

        CustomerGetReq params = new CustomerGetReq();
        params.setEnterpriseNo(operationModel.getEnterpriseNo());
        params.setUseOrgNo(useOrgNo);
        params.setCustomerCode(customerV2.getCustomerCode());
        return packDetail(operationModel, params, customerV2);
    }

    @Override
    public CustomerVO getDetailCustomerByCode(OperationModel operationModel, String useOrgNo, String customerCode) {
        CustomerV2 customerV2 = customerV2DAO.selectOne(
                Wrappers.<CustomerV2>lambdaQuery()
                        .eq(CustomerV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                        .eq(CustomerV2::getCustomerCode, customerCode)
                        .eq(CustomerV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
        if (null == customerV2) {
            return null;
        }

        CustomerGetReq params = new CustomerGetReq();
        params.setEnterpriseNo(operationModel.getEnterpriseNo());
        params.setUseOrgNo(useOrgNo);
        params.setCustomerCode(customerV2.getCustomerCode());
        return packDetail(operationModel, params, customerV2);
    }

    @Override
    public Boolean hasAssignCustomer(OperationModel operationModel, CustomerBase customerBase) {
        return customerBaseDAO.exists(Wrappers.<CustomerBase>lambdaQuery()
                .eq(CustomerBase::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CustomerBase::getCustomerCode, customerBase.getCustomerCode())
                .eq(CustomerBase::getManageOrgNo, customerBase.getManageOrgNo())
                .eq(CustomerBase::getUseOrgNo, customerBase.getUseOrgNo())
                .eq(CustomerBase::getDeleted, DeletedEnum.UN_DELETE.getValue()));
    }

    @Override
    public CustomerBase getCustomerBaseByCodeAndUseOrgNo(String enterpriseNo, String customerCode, String useOrgNo) {
        return customerBaseDAO.selectOne(Wrappers.<CustomerBase>lambdaQuery()
                .eq(CustomerBase::getEnterpriseNo, enterpriseNo)
                .eq(CustomerBase::getCustomerCode, customerCode)
                .eq(CustomerBase::getUseOrgNo, useOrgNo)
                .eq(CustomerBase::getDeleted, DeletedEnum.UN_DELETE.getValue()));
    }

    @Override
    @Transactional
    public Boolean changeCustomerStatus(OperationModel operationModel, CustomerChangeStatusReq customerChangeStatusReq) {
        // 校验必填参数
        ValidatorUtils.checkEmptyThrowEx(operationModel.getEnterpriseNo(), "租户编号不能为空");
        ValidatorUtils.checkEmptyThrowEx(customerChangeStatusReq.getUseOrgNo(), "使用组织不能为空");
        ValidatorUtils.checkEmptyThrowEx(customerChangeStatusReq.getCustomerCode(), "客户编码不能为空");
        ValidatorUtils.checkEmptyThrowEx(customerChangeStatusReq.getControlStatus(), "管控状态不能为空");

        CustomerV2 dbCustomerV2 = customerV2DAO.selectOne(new LambdaQueryWrapper<CustomerV2>()
                .eq(CustomerV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CustomerV2::getCustomerCode, customerChangeStatusReq.getCustomerCode())
                .eq(CustomerV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
        ValidatorUtils.checkEmptyThrowEx(dbCustomerV2, "客户不存在");

        CustomerBase customerBase = customerBaseDAO.selectOne(new LambdaQueryWrapper<CustomerBase>()
                .eq(CustomerBase::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CustomerBase::getUseOrgNo, customerChangeStatusReq.getUseOrgNo())
                .eq(CustomerBase::getCustomerCode, dbCustomerV2.getCustomerCode())
                .eq(CustomerBase::getDeleted, DeletedEnum.UN_DELETE.getValue()));
        ValidatorUtils.checkEmptyThrowEx(customerBase, "客户未分派");


        CustomerBase newCustomerBase = new CustomerBase();

        String op = null;

        // 启用
        if (ControlStatusEnum.ENABLE.getValue().toString().equals(customerChangeStatusReq.getControlStatus())) {
            ValidatorUtils.checkTrueThrowEx(!ControlStatusEnum.DISABLE.getValue().toString().equals(customerBase.getControlStatus()), "客户需要为停用状态");

            // 管理组织 != 使用组织，判断管理组织的启停状态
            if (!customerChangeStatusReq.getUseOrgNo().equals(dbCustomerV2.getManageOrgNo())) {
                CustomerBase manageCustomerBase = customerBaseDAO.selectOne(new LambdaQueryWrapper<CustomerBase>()
                        .eq(CustomerBase::getEnterpriseNo, operationModel.getEnterpriseNo())
                        .eq(CustomerBase::getUseOrgNo, dbCustomerV2.getManageOrgNo())
                        .eq(CustomerBase::getCustomerCode, dbCustomerV2.getCustomerCode())
                        .eq(CustomerBase::getDeleted, DeletedEnum.UN_DELETE.getValue()));
                ValidatorUtils.checkEmptyThrowEx(manageCustomerBase, "管理组织的启停信息不存在");

                ValidatorUtils.checkTrueThrowEx(ControlStatusEnum.DISABLE.getValue().toString().equals(manageCustomerBase.getControlStatus()), "客户已经被管理组织停用");
            }

            newCustomerBase.setControlStatus(ControlStatusEnum.ENABLE.getValue().toString());

            op = "启用客户档案";
        } else { // 停用
            ValidatorUtils.checkTrueThrowEx(!ControlStatusEnum.ENABLE.getValue().toString().equals(customerBase.getControlStatus()), "客户需要为启用状态");

            newCustomerBase.setControlStatus(ControlStatusEnum.DISABLE.getValue().toString());

            op = "停用客户档案";
        }

        customerBaseDAO.update(newCustomerBase, Wrappers.<CustomerBase>lambdaUpdate()
                .eq(CustomerBase::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CustomerBase::getUseOrgNo, customerChangeStatusReq.getUseOrgNo())
                .eq(CustomerBase::getCustomerCode, customerChangeStatusReq.getCustomerCode())
                .eq(CustomerBase::getDeleted, DeletedEnum.UN_DELETE.getValue()));


        final String logOp = op;

        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                try {
                    businessLogService.saveLog(DLogLevel.INFO.name(), ViewNameConstant.BDC_CUSTOMER_VIEW, customerChangeStatusReq.getCustomerCode() + customerChangeStatusReq.getUseOrgNo(), logOp, logOp, JSON.toJSONString(newCustomerBase), "");
                } catch (Exception e) {
                    log.error(logOp + "日志保存失败", e);
                }
            }
        });

        return true;
    }

    @Override
    public List<CustomerAssignVO> getAssignCustomer(OperationModel operationModel, CustomerGetAssignReq params) {
        CustomerV2 customerV2 = customerV2DAO.selectOne(Wrappers.<CustomerV2>lambdaQuery()
                .eq(CustomerV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CustomerV2::getManageOrgNo, params.getUseOrgNo())
                .eq(CustomerV2::getCustomerCode, params.getCustomerCode())
                .eq(CustomerV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
        // 不是管理组织不展示
        if (customerV2 == null) {
            return Collections.emptyList();
        }

        List<CustomerBase> customerBases = customerBaseDAO.selectList(Wrappers.<CustomerBase>lambdaQuery()
                .eq(CustomerBase::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CustomerBase::getCustomerCode, params.getCustomerCode())
                .eq(CustomerBase::getDeleted, DeletedEnum.UN_DELETE.getValue()));

        if (CollectionUtils.isEmpty(customerBases)) {
            return Collections.emptyList();
        }

        List<CustomerAssignVO> customerAssignVOList = new ArrayList<>();

        List<OrganizationVo> organizationVoList = organizationService.findListNoAuth(operationModel.getEnterpriseNo(), new ArrayList<>(customerBases.stream().flatMap(customerBase -> Stream.of(customerBase.getManageOrgNo(), customerBase.getUseOrgNo())).collect(Collectors.toSet())));
        final Map<String, OrganizationVo> orgNo2OrganizationVo = organizationVoList.stream().collect(Collectors.toMap(OrganizationVo::getOrgNo, Function.identity()));


        for (CustomerBase customerBase : customerBases) {
            CustomerAssignVO customerAssignVO = BeanUtil.copyFields(customerBase, CustomerAssignVO.class);

            if (orgNo2OrganizationVo.containsKey(customerBase.getManageOrgNo())) {
                customerAssignVO.setManageOrgName(orgNo2OrganizationVo.get(customerBase.getManageOrgNo()).getOrgName());
            }

            if (orgNo2OrganizationVo.containsKey(customerBase.getUseOrgNo())) {
                customerAssignVO.setUseOrgName(orgNo2OrganizationVo.get(customerBase.getUseOrgNo()).getOrgName());
            }

            customerAssignVOList.add(customerAssignVO);
        }

        return customerAssignVOList;
    }

    @Override
    public Long getPendingCount(OperationModel operationModel) {
        return customerV2DAO.getPendingCount(operationModel.getEnterpriseNo(), CustomerBusinessFlagEnum.DRAFT.getValue(), DeletedEnum.UN_DELETE.getValue());
    }

    /**
     * 支持以下infos值：
     * - categoryMap：客户分类信息
     * - companyMap：企业信息
     * - bankMap：银行信息
     * - taxCategoryMap：税收分类信息
     * - economicTypeMap：经济类型信息
     * - currencyMap：币种信息
     * - transactionTypeMap：客户类型信息
     * - settlementModesMap：结算方式信息
     * - orgMap：组织信息
     * - customerBizMap：客户业务信息
     * - customerGspMap：客户首营信息
     * - customerBaseMap：客户分派信息
     * - businessTypeMap：业务类型信息
     * - cooperationMap：合作性质信息
     * - paymentTermMap：付款条件信息
     * - priceCustomerCategoryMap：价格体系信息
     * - linkManMap：联系人信息
     * - addressMap：地址信息
     * - salesManMap：负责人信息
     * - invoiceMap：开票信息
     * - countryRegionMap：国家地区信息
     *
     * @param enterpriseNo 企业编号
     * @param data         客户数据列表
     * @param infos        需要获取的信息类型集合
     * @return 包含请求信息的Map
     */
    private Map<String, Object> collectBizInfo(String enterpriseNo, List<CustomerV2WithBiz> data, Set<String> infos) {
        Map<String, Object> result = new HashMap<>();

        // 客户分类
        if (infos.contains("categoryMap")) {
            List<String> categoryNoList = data.stream().map(CustomerV2WithBiz::getCustomerCategoryNo).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
            final Map<String, CustomerCategoryV2> categoryMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(categoryNoList)) {
                List<CustomerCategoryV2> categoryList = customerCategoryV2Service.getByNoList(enterpriseNo, categoryNoList);
                if (CollectionUtils.isNotEmpty(categoryList)) {
                    categoryMap.putAll(categoryList.stream().collect(Collectors.toMap(CustomerCategoryV2::getNo, Function.identity())));
                }
            }
            result.put("categoryMap", categoryMap);
        }

        // 企业信息
        if (infos.contains("companyMap")) {
            List<String> companyNoList = data.stream().map(CustomerV2WithBiz::getCompanyNo).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
            Map<String, CompanyV2> companyMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(companyNoList)) {
                List<CompanyV2> companyList = companyV2Service.findByEnterpriseNoAndCompanyNoList(enterpriseNo, companyNoList);
                if (CollectionUtils.isNotEmpty(companyList)) {
                    companyMap.putAll(companyList.stream().collect(Collectors.toMap(CompanyV2::getCompanyNo, Function.identity())));
                }
            }
            result.put("companyMap", companyMap);
        }

        // 获取银行信息
        if (infos.contains("bankMap")) {
            List<CompanyBankV2> bankList = companyV2Service.findBankList(enterpriseNo, new ArrayList<>(data.stream().map(CustomerV2WithBiz::getCompanyNo).collect(Collectors.toSet())));
            final Map<String, List<CompanyBankV2>> companyNo2BankList = bankList.stream().collect(Collectors.groupingBy(CompanyBankV2::getCompanyNo));
            result.put("bankMap", companyNo2BankList);
        }

        //税收分类
        if (infos.contains("taxCategoryMap")) {
            final Map<String, CustomDocResponse> taxCategoryMap = customDocService.getItemByCustomDocCode(enterpriseNo, SystemConstant.TAX_CATEGORY);
            result.put("taxCategoryMap", taxCategoryMap);
        }

        //客户经济类型
        if (infos.contains("economicTypeMap")) {
            final Map<String, CustomDocResponse> economicTypeMap = customDocService.getItemByCustomDocCode(enterpriseNo, SystemConstant.ECONOMIC_TYPE);
            result.put("economicTypeMap", economicTypeMap);
        }

        // 获取国家地区
        if (infos.contains("countryRegionMap")) {
            final Map<String, String> countryRegionMap = dictEnterpriseService.findPlatformCountryRegionList();
            result.put("countryRegionMap", countryRegionMap);
        }

        // 获取币种
        if (infos.contains("currencyMap")) {
            final Map<String, String> currencyMap = dictEnterpriseService.findCurrencyList(enterpriseNo);
            result.put("currencyMap", currencyMap);
        }

        // 获取客户类型
        if (infos.contains("transactionTypeMap")) {
            List<TransactionTypeResponse> transactionTypeList = transactionTypeService.getTransactionTypeList(enterpriseNo, ViewNameConstant.BDC_CUSTOMER_VIEW);
            final Map<String, TransactionTypeResponse> transactionTypeMap = transactionTypeList.stream().collect(Collectors.toMap(TransactionTypeResponse::getTransactionTypeCode, Function.identity()));
            result.put("transactionTypeMap", transactionTypeMap);
        }

        // 获取结算方式
        if (infos.contains("settlementModesMap")) {
            final Map<String, String> settlementModesMap = dictEnterpriseService.getValMapByNumber(enterpriseNo, DictNumberEnum.DSRP_CUSTOMER_SETTLEMENT_MODES);
            result.put("settlementModesMap", settlementModesMap);
        }

        // 组织信息
        if (infos.contains("orgMap")) {
            List<OrganizationVo> organizationVoList = organizationService.findListNoAuth(enterpriseNo, new ArrayList<>(data.stream().flatMap(customerV2WithBiz -> Stream.of(customerV2WithBiz.getManageOrgNo(), customerV2WithBiz.getUseOrgNo())).collect(Collectors.toSet())));
            final Map<String, OrganizationVo> orgNo2OrganizationVo = organizationVoList.stream().collect(Collectors.toMap(OrganizationVo::getOrgNo, Function.identity()));
            result.put("orgMap", orgNo2OrganizationVo);
        }

        // 客户业务信息
        if (infos.contains("customerBizMap")) {
            List<CustomerBiz> customerBizList = customerBizDAO.selectList(Wrappers.<CustomerBiz>lambdaQuery()
                    .eq(CustomerBiz::getEnterpriseNo, enterpriseNo)
                    .in(CustomerBiz::getId, data.stream().map(CustomerV2WithBiz::getBizId).collect(Collectors.toList())));
            final Map<Long, CustomerBiz> customerBizMap = customerBizList.stream().collect(Collectors.toMap(CustomerBiz::getId, Function.identity()));
            result.put("customerBizMap", customerBizMap);
        }

        // 客户首营信息
        if (infos.contains("customerGspMap")) {
            List<CustomerGspAudit> customerGspList = customerGspAuditDAO.selectList(Wrappers.<CustomerGspAudit>lambdaQuery()
                    .eq(CustomerGspAudit::getEnterpriseNo, enterpriseNo)
                    .in(CustomerGspAudit::getCustomerCode, data.stream().map(CustomerV2WithBiz::getCustomerCode).collect(Collectors.toList()))
                    .eq(CustomerGspAudit::getDeleted, DeletedEnum.UN_DELETE.getValue()));
            final Map<String, Map<String, CustomerGspAudit>> customerGspMap = customerGspList.stream().collect(Collectors.groupingBy(CustomerGspAudit::getCustomerCode, Collectors.toMap(CustomerGspAudit::getUseOrgNo, Function.identity())));
            result.put("customerGspMap", customerGspMap);
        }

        // 客户分派信息
        if (infos.contains("customerBaseMap")) {
            List<CustomerBase> customerBaseList = customerBaseDAO.selectList(Wrappers.<CustomerBase>lambdaQuery()
                    .eq(CustomerBase::getEnterpriseNo, enterpriseNo)
                    .in(CustomerBase::getCustomerCode, data.stream().map(CustomerV2WithBiz::getCustomerCode).collect(Collectors.toList()))
                    .eq(CustomerBase::getDeleted, DeletedEnum.UN_DELETE.getValue()));
            Map<String, Map<String, CustomerBase>> customerBaseMap = customerBaseList.stream().collect(Collectors.groupingBy(CustomerBase::getCustomerCode, Collectors.toMap(CustomerBase::getUseOrgNo, Function.identity())));
            result.put("customerBaseMap", customerBaseMap);
        }

        // 客户性质
        if (infos.contains("businessTypeMap")) {
            final Map<String, CustomDocResponse> businessTypeMap = customDocService.getItemByCustomDocCode(enterpriseNo, SystemConstant.DSRP_BUSINESS_TYPE);
            result.put("businessTypeMap", businessTypeMap);
        }

        // 合作性质
        if (infos.contains("cooperationMap")) {
            final Map<String, CustomDocResponse> cooperationMap = customDocService.getItemByCustomDocCode(enterpriseNo, SystemConstant.CUSTOMER_COOPERATION_NUMBER);
            result.put("cooperationMap", cooperationMap);
        }

        //付款条件
        if (infos.contains("paymentTermMap")) {
            final Map<String, CustomDocResponse> paymentTermMap = customDocService.getItemByCustomDocCode(enterpriseNo, SystemConstant.DSRP_CUSTOMER_PAYMENT_TERM);
            result.put("paymentTermMap", paymentTermMap);
        }

        //价格体系
        if (infos.contains("priceCustomerCategoryMap")) {
            final Map<String, String> priceCustomerCategoryMap = dictEnterpriseService.getValMapByNumber(enterpriseNo, DictNumberEnum.DSRP_PRICE_CUSTOMER_CATEGORY);
            result.put("priceCustomerCategoryMap", priceCustomerCategoryMap);
        }

        // 获取联系人
        if (infos.contains("linkManMap")) {
            List<CompanyLinkmanV2> customerLinkmanList = companyLinkmanV2DAO.selectList(Wrappers.<CompanyLinkmanV2>lambdaQuery()
                    .eq(CompanyLinkmanV2::getEnterpriseNo, enterpriseNo)
                    .in(CompanyLinkmanV2::getSourceNo, data.stream().map(CustomerV2WithBiz::getCustomerCode).collect(Collectors.toSet()))
                    .in(CompanyLinkmanV2::getUseOrgNo, data.stream().map(CustomerV2WithBiz::getUseOrgNo).collect(Collectors.toSet()))
                    .eq(CompanyLinkmanV2::getLinkmanType, LinkmanTypeEnum.SALE.getValue())
                    .eq(CompanyLinkmanV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
            final Map<String, Map<String, List<CompanyLinkmanV2>>> code2org2LinkManList = customerLinkmanList.stream().collect(Collectors.groupingBy(CompanyLinkmanV2::getSourceNo, Collectors.groupingBy(CompanyLinkmanV2::getUseOrgNo)));
            result.put("linkManMap", code2org2LinkManList);
        }

        // 获取地址
        if (infos.contains("addressMap")) {
            List<CompanyShippingAddressV2> customerAddressList = companyShippingAddressV2DAO.selectList(Wrappers.<CompanyShippingAddressV2>lambdaQuery()
                    .eq(CompanyShippingAddressV2::getEnterpriseNo, enterpriseNo)
                    .in(CompanyShippingAddressV2::getSourceNo, data.stream().map(CustomerV2WithBiz::getCustomerCode).collect(Collectors.toSet()))
                    .in(CompanyShippingAddressV2::getUseOrgNo, data.stream().map(CustomerV2WithBiz::getUseOrgNo).collect(Collectors.toSet()))
                    .eq(CompanyShippingAddressV2::getLinkaddType, LinkmanTypeEnum.SALE.getValue())
                    .eq(CompanyShippingAddressV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
            final Map<String, Map<String, List<CompanyShippingAddressV2>>> code2org2AddressList = customerAddressList.stream().collect(Collectors.groupingBy(CompanyShippingAddressV2::getSourceNo, Collectors.groupingBy(CompanyShippingAddressV2::getUseOrgNo)));
            result.put("addressMap", code2org2AddressList);
        }

        // 获取负责人
        if (infos.contains("salesManMap")) {
            List<CustomerSalesManV2> customerSalesManV2List = customerSalesManV2DAO.selectList(Wrappers.<CustomerSalesManV2>lambdaQuery()
                    .eq(CustomerSalesManV2::getEnterpriseNo, enterpriseNo)
                    .in(CustomerSalesManV2::getCustomerCode, data.stream().map(CustomerV2WithBiz::getCustomerCode).collect(Collectors.toSet()))
                    .in(CustomerSalesManV2::getUseOrgNo, data.stream().map(CustomerV2WithBiz::getUseOrgNo).collect(Collectors.toSet()))
                    .eq(CustomerSalesManV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
            final Map<String, Map<String, List<CustomerSalesManV2>>> code2org2SalesManList = customerSalesManV2List.stream().collect(Collectors.groupingBy(CustomerSalesManV2::getCustomerCode, Collectors.groupingBy(CustomerSalesManV2::getUseOrgNo)));
            result.put("salesManMap", code2org2SalesManList);
        }

        // 获取开票
        if (infos.contains("invoiceMap")) {
            List<CustomerInvoiceV2> customerInvoiceV2List = customerInvoiceV2DAO.selectList(Wrappers.<CustomerInvoiceV2>lambdaQuery()
                    .eq(CustomerInvoiceV2::getEnterpriseNo, enterpriseNo)
                    .in(CustomerInvoiceV2::getCustomerCode, data.stream().map(CustomerV2WithBiz::getCustomerCode).collect(Collectors.toSet()))
                    .in(CustomerInvoiceV2::getUseOrgNo, data.stream().map(CustomerV2WithBiz::getUseOrgNo).collect(Collectors.toSet()))
                    .eq(CustomerInvoiceV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
            final Map<String, Map<String, List<CustomerInvoiceV2>>> code2org2InvoiceList = customerInvoiceV2List.stream().collect(Collectors.groupingBy(CustomerInvoiceV2::getCustomerCode, Collectors.groupingBy(CustomerInvoiceV2::getUseOrgNo)));
            result.put("invoiceMap", code2org2InvoiceList);
        }

        return result;
    }

    /**
     * 支持以下infos值：
     * - categoryMap：客户分类信息
     * - companyMap：企业信息
     * - taxCategoryMap：税收分类信息
     * - economicTypeMap：经济类型信息
     * - currencyMap：币种信息
     * - transactionTypeMap：客户类型信息
     * - orgMap：组织信息
     * - businessTypeMap：业务类型信息
     * - cooperationMap：合作性质信息
     * - priceCustomerCategoryMap：价格体系信息
     * - linkManMap：联系人信息
     * - addressMap：地址信息
     * - salesManMap：负责人信息
     * - invoiceMap：开票信息
     * - settlementModesMap：结算方式信息
     * - paymentTermMap：付款条件信息
     * - countryRegionMap：国家地区信息
     * - bankMap：银行信息
     *
     * @param enterpriseNo          企业编号
     * @param data                  客户数据列表
     * @param potentialUseOrgNoList 使用组织编号列表
     * @param infos                 需要获取的信息类型集合
     * @return 包含请求信息的Map
     */
    private Map<String, Object> collectBasicInfo(String enterpriseNo, List<? extends CustomerV2> data, List<String> potentialUseOrgNoList, Set<String> infos) {
        Map<String, Object> result = new HashMap<>();

        List<String> allOrgNoList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(potentialUseOrgNoList)) {
            allOrgNoList.addAll(potentialUseOrgNoList);
        }
        allOrgNoList.addAll(data.stream().map(CustomerV2::getManageOrgNo).collect(Collectors.toSet()));

        if (infos.contains("categoryMap")) {
            List<String> categoryNoList = data.stream().map(CustomerV2::getCustomerCategoryNo).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
            final Map<String, CustomerCategoryV2> categoryMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(categoryNoList)) {
                List<CustomerCategoryV2> categoryList = customerCategoryV2Service.getByNoList(enterpriseNo, categoryNoList);
                if (CollectionUtils.isNotEmpty(categoryList)) {
                    categoryMap.putAll(categoryList.stream().collect(Collectors.toMap(CustomerCategoryV2::getNo, Function.identity())));
                }
            }
            result.put("categoryMap", categoryMap);
        }

        if (infos.contains("companyMap")) {
            List<String> companyNoList = data.stream().map(CustomerV2::getCompanyNo).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
            Map<String, CompanyV2> companyMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(companyNoList)) {
                List<CompanyV2> companyList = companyV2Service.findByEnterpriseNoAndCompanyNoList(enterpriseNo, companyNoList);
                if (CollectionUtils.isNotEmpty(companyList)) {
                    companyMap.putAll(companyList.stream().collect(Collectors.toMap(CompanyV2::getCompanyNo, Function.identity())));
                }
            }
            result.put("companyMap", companyMap);
        }

        // 获取银行信息
        if (infos.contains("bankMap")) {
            List<CompanyBankV2> bankList = companyV2Service.findBankList(enterpriseNo, new ArrayList<>(data.stream().map(CustomerV2::getCompanyNo).collect(Collectors.toSet())));
            final Map<String, List<CompanyBankV2>> companyNo2BankList = bankList.stream().collect(Collectors.groupingBy(CompanyBankV2::getCompanyNo));
            result.put("bankMap", companyNo2BankList);
        }


        //税收分类
        if (infos.contains("taxCategoryMap")) {
            final Map<String, CustomDocResponse> taxCategoryMap = customDocService.getItemByCustomDocCode(enterpriseNo, SystemConstant.TAX_CATEGORY);
            result.put("taxCategoryMap", taxCategoryMap);
        }

        //客户经济类型
        if (infos.contains("economicTypeMap")) {
            final Map<String, CustomDocResponse> economicTypeMap = customDocService.getItemByCustomDocCode(enterpriseNo, SystemConstant.ECONOMIC_TYPE);
            result.put("economicTypeMap", economicTypeMap);
        }


        // 获取国家地区
        if (infos.contains("countryRegionMap")) {
            final Map<String, String> countryRegionMap = dictEnterpriseService.findPlatformCountryRegionList();
            result.put("countryRegionMap", countryRegionMap);
        }

        // 获取币种
        if (infos.contains("currencyMap")) {
            final Map<String, String> currencyMap = dictEnterpriseService.findCurrencyList(enterpriseNo);
            result.put("currencyMap", currencyMap);
        }

        if (infos.contains("transactionTypeMap")) {
            List<TransactionTypeResponse> transactionTypeList = transactionTypeService.getTransactionTypeList(enterpriseNo, ViewNameConstant.BDC_CUSTOMER_VIEW);
            final Map<String, TransactionTypeResponse> transactionTypeMap = transactionTypeList.stream().collect(Collectors.toMap(TransactionTypeResponse::getTransactionTypeCode, Function.identity()));
            result.put("transactionTypeMap", transactionTypeMap);
        }

        // 获取结算方式
        if (infos.contains("settlementModesMap")) {
            final Map<String, String> settlementModesMap = dictEnterpriseService.getValMapByNumber(enterpriseNo, DictNumberEnum.DSRP_CUSTOMER_SETTLEMENT_MODES);
            result.put("settlementModesMap", settlementModesMap);
        }

        if (infos.contains("orgMap")) {
            List<OrganizationVo> organizationVoList = organizationService.findListNoAuth(enterpriseNo, allOrgNoList);
            final Map<String, OrganizationVo> orgNo2OrganizationVo = organizationVoList.stream().collect(Collectors.toMap(OrganizationVo::getOrgNo, Function.identity()));
            result.put("orgMap", orgNo2OrganizationVo);
        }

        if (infos.contains("businessTypeMap")) {
            final Map<String, CustomDocResponse> businessTypeMap = customDocService.getItemByCustomDocCode(enterpriseNo, SystemConstant.DSRP_BUSINESS_TYPE);
            result.put("businessTypeMap", businessTypeMap);
        }

        if (infos.contains("cooperationMap")) {
            final Map<String, CustomDocResponse> cooperationMap = customDocService.getItemByCustomDocCode(enterpriseNo, SystemConstant.CUSTOMER_COOPERATION_NUMBER);
            result.put("cooperationMap", cooperationMap);
        }

        //价格体系
        if (infos.contains("priceCustomerCategoryMap")) {
            final Map<String, String> priceCustomerCategoryMap = dictEnterpriseService.getValMapByNumber(enterpriseNo, DictNumberEnum.DSRP_PRICE_CUSTOMER_CATEGORY);
            result.put("priceCustomerCategoryMap", priceCustomerCategoryMap);
        }

        // 获取联系人
        if (infos.contains("linkManMap")) {
            List<CompanyLinkmanV2> customerLinkmanList = companyLinkmanV2DAO.selectList(Wrappers.<CompanyLinkmanV2>lambdaQuery()
                    .eq(CompanyLinkmanV2::getEnterpriseNo, enterpriseNo)
                    .in(CompanyLinkmanV2::getSourceNo, data.stream().map(CustomerV2::getCustomerCode).collect(Collectors.toSet()))
                    .in(CompanyLinkmanV2::getUseOrgNo, allOrgNoList)
                    .eq(CompanyLinkmanV2::getLinkmanType, LinkmanTypeEnum.SALE.getValue())
                    .eq(CompanyLinkmanV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
            final Map<String, Map<String, List<CompanyLinkmanV2>>> code2org2LinkManList = customerLinkmanList.stream().collect(Collectors.groupingBy(CompanyLinkmanV2::getSourceNo, Collectors.groupingBy(CompanyLinkmanV2::getUseOrgNo)));
            result.put("linkManMap", code2org2LinkManList);
        }

        // 获取地址
        if (infos.contains("addressMap")) {
            List<CompanyShippingAddressV2> customerAddressList = companyShippingAddressV2DAO.selectList(Wrappers.<CompanyShippingAddressV2>lambdaQuery()
                    .eq(CompanyShippingAddressV2::getEnterpriseNo, enterpriseNo)
                    .in(CompanyShippingAddressV2::getSourceNo, data.stream().map(CustomerV2::getCustomerCode).collect(Collectors.toSet()))
                    .in(CompanyShippingAddressV2::getUseOrgNo, allOrgNoList)
                    .eq(CompanyShippingAddressV2::getLinkaddType, LinkmanTypeEnum.SALE.getValue())
                    .eq(CompanyShippingAddressV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
            final Map<String, Map<String, List<CompanyShippingAddressV2>>> code2org2AddressList = customerAddressList.stream().collect(Collectors.groupingBy(CompanyShippingAddressV2::getSourceNo, Collectors.groupingBy(CompanyShippingAddressV2::getUseOrgNo)));
            result.put("addressMap", code2org2AddressList);
        }

        // 获取负责人
        if (infos.contains("salesManMap")) {
            List<CustomerSalesManV2> customerSalesManV2List = customerSalesManV2DAO.selectList(Wrappers.<CustomerSalesManV2>lambdaQuery()
                    .eq(CustomerSalesManV2::getEnterpriseNo, enterpriseNo)
                    .in(CustomerSalesManV2::getCustomerCode, data.stream().map(CustomerV2::getCustomerCode).collect(Collectors.toSet()))
                    .in(CustomerSalesManV2::getUseOrgNo, allOrgNoList)
                    .eq(CustomerSalesManV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
            final Map<String, Map<String, List<CustomerSalesManV2>>> code2org2SalesManList = customerSalesManV2List.stream().collect(Collectors.groupingBy(CustomerSalesManV2::getCustomerCode, Collectors.groupingBy(CustomerSalesManV2::getUseOrgNo)));
            result.put("salesManMap", code2org2SalesManList);
        }

        // 获取开票
        if (infos.contains("invoiceMap")) {
            List<CustomerInvoiceV2> customerInvoiceV2List = customerInvoiceV2DAO.selectList(Wrappers.<CustomerInvoiceV2>lambdaQuery()
                    .eq(CustomerInvoiceV2::getEnterpriseNo, enterpriseNo)
                    .in(CustomerInvoiceV2::getCustomerCode, data.stream().map(CustomerV2::getCustomerCode).collect(Collectors.toSet()))
                    .in(CustomerInvoiceV2::getUseOrgNo, allOrgNoList)
                    .eq(CustomerInvoiceV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
            final Map<String, Map<String, List<CustomerInvoiceV2>>> code2org2InvoiceList = customerInvoiceV2List.stream().collect(Collectors.groupingBy(CustomerInvoiceV2::getCustomerCode, Collectors.groupingBy(CustomerInvoiceV2::getUseOrgNo)));
            result.put("invoiceMap", code2org2InvoiceList);
        }

        return result;
    }

    private List<com.yyigou.dsrp.cdc.api.v2.customer.vo.CustomerPageVO> completeSupplementForTenantPageVO(String enterpriseNo, List<String> useOrgNoList, List<CustomerV2WithBase> data) {
        List<com.yyigou.dsrp.cdc.api.v2.customer.vo.CustomerPageVO> result = new ArrayList<>();

        Map<String, Object> infoMap = collectBasicInfo(enterpriseNo, data, useOrgNoList,
                Sets.newHashSet(
                        "categoryMap",
                        "companyMap",
                        "taxCategoryMap",
                        "economicTypeMap",
                        "transactionTypeMap",
                        "orgMap",
                        "countryRegionMap"
                ));


        final Map<String, CustomerCategoryV2> categoryMap = (Map<String, CustomerCategoryV2>) infoMap.get("categoryMap");
        final Map<String, CompanyV2> companyMap = (Map<String, CompanyV2>) infoMap.get("companyMap");
        final Map<String, CustomDocResponse> taxCategoryMap = (Map<String, CustomDocResponse>) infoMap.get("taxCategoryMap");
        final Map<String, CustomDocResponse> economicTypeMap = (Map<String, CustomDocResponse>) infoMap.get("economicTypeMap");
        final Map<String, TransactionTypeResponse> transactionTypeMap = (Map<String, TransactionTypeResponse>) infoMap.get("transactionTypeMap");
        final Map<String, OrganizationVo> orgNo2OrganizationVo = (Map<String, OrganizationVo>) infoMap.get("orgMap");
        final Map<String, String> countryRegionMap = (Map<String, String>) infoMap.get("countryRegionMap");

        data.forEach(customerV2WithBiz -> {
            com.yyigou.dsrp.cdc.api.v2.customer.vo.CustomerPageVO customerPageVO = new com.yyigou.dsrp.cdc.api.v2.customer.vo.CustomerPageVO();
            BeanUtils.copyProperties(customerV2WithBiz, customerPageVO);

            customerPageVO.setTransactionTypeName(transactionTypeMap.getOrDefault(customerV2WithBiz.getTransactionType(), new TransactionTypeResponse()).getTransactionTypeName());

            // 补充使用组织名称
            if (orgNo2OrganizationVo.containsKey(customerV2WithBiz.getUseOrgNo())) {
                customerPageVO.setUseOrgName(orgNo2OrganizationVo.get(customerV2WithBiz.getUseOrgNo()).getOrgName());
            }

            // 补充管理组织名称
            if (orgNo2OrganizationVo.containsKey(customerV2WithBiz.getManageOrgNo())) {
                customerPageVO.setManageOrgName(orgNo2OrganizationVo.get(customerV2WithBiz.getManageOrgNo()).getOrgName());
            }

            if (companyMap.containsKey(customerV2WithBiz.getCompanyNo())) {
                final CompanyV2 company = companyMap.get(customerV2WithBiz.getCompanyNo());
                customerPageVO.setTaxCategoryName(taxCategoryMap.getOrDefault(company.getTaxCategory(), new CustomDocResponse()).getDocItemName());
                customerPageVO.setEconomicTypeName(economicTypeMap.getOrDefault(company.getEconomicType(), new CustomDocResponse()).getDocItemName());
                customerPageVO.setCompanyName(company.getCompanyName());
                customerPageVO.setUnifiedSocialCode(company.getUnifiedSocialCode());
//                customerVO.setCountry(company.getCountry());
                customerPageVO.setCountryRegionId(company.getCountryRegionId());
                if (StringUtils.isNotEmpty(company.getCountryRegionId())) {
                    customerPageVO.setCountryRegionName(countryRegionMap.get(company.getCountryRegionId()));
                }
                customerPageVO.setFactoryType(company.getFactoryType());
                customerPageVO.setFactoryTypeName(FactoryTypeEnum.getByType(company.getFactoryType()) == null ? null : FactoryTypeEnum.getByType(company.getFactoryType()).getName());
                customerPageVO.setEconomicType(company.getEconomicType());
//                customerVO.setIsAssociatedEnterprise(company.getIsAssociatedEnterprise());
//                customerVO.setAssociatedOrgNo(company.getAssociatedOrgNo());
//                customerVO.setAssociatedOrgCode(company.getAssociatedOrgCode());
//                customerVO.setAssociatedOrgName(company.getAssociatedOrgName());
                customerPageVO.setInstitutionalType(company.getInstitutionalType());

                InstitutionalTypeEnum institutionalTypeEnum = InstitutionalTypeEnum.getByType(company.getInstitutionalType());
                if (null != institutionalTypeEnum) {
                    customerPageVO.setInstitutionalTypeName(institutionalTypeEnum.getName());
                }
            }

            customerPageVO.setCustomerCategoryName(categoryMap.getOrDefault(customerV2WithBiz.getCustomerCategoryNo(), new CustomerCategoryV2()).getCategoryName());

            result.add(customerPageVO);
        });
        return result;
    }

    private List<com.yyigou.dsrp.cdc.api.v2.customer.vo.CustomerPageVO> completeSupplementPageVO(String enterpriseNo, List<CustomerV2WithBiz> data) {
        List<com.yyigou.dsrp.cdc.api.v2.customer.vo.CustomerPageVO> result = new ArrayList<>();

        Map<String, Object> infoMap = collectBizInfo(enterpriseNo, data,
                Sets.newHashSet(
                        "categoryMap",
                        "companyMap",
                        "taxCategoryMap",
                        "economicTypeMap",
                        "transactionTypeMap",
                        "orgMap",
                        "customerBizMap",
                        "customerGspMap",
                        "customerBaseMap",
                        "businessTypeMap",
                        "cooperationMap",
                        "countryRegionMap"
                ));


        final Map<String, CustomerCategoryV2> categoryMap = (Map<String, CustomerCategoryV2>) infoMap.get("categoryMap");
        final Map<String, CompanyV2> companyMap = (Map<String, CompanyV2>) infoMap.get("companyMap");
        final Map<String, CustomDocResponse> taxCategoryMap = (Map<String, CustomDocResponse>) infoMap.get("taxCategoryMap");
        final Map<String, CustomDocResponse> economicTypeMap = (Map<String, CustomDocResponse>) infoMap.get("economicTypeMap");
        final Map<String, TransactionTypeResponse> transactionTypeMap = (Map<String, TransactionTypeResponse>) infoMap.get("transactionTypeMap");
        final Map<String, OrganizationVo> orgNo2OrganizationVo = (Map<String, OrganizationVo>) infoMap.get("orgMap");
        final Map<Long, CustomerBiz> customerBizMap = (Map<Long, CustomerBiz>) infoMap.get("customerBizMap");
        final Map<String, Map<String, CustomerGspAudit>> customerGspMap = (Map<String, Map<String, CustomerGspAudit>>) infoMap.get("customerGspMap");
        final Map<String, Map<String, CustomerBase>> customerBaseMap = (Map<String, Map<String, CustomerBase>>) infoMap.get("customerBaseMap");
        final Map<String, CustomDocResponse> businessTypeMap = (Map<String, CustomDocResponse>) infoMap.get("businessTypeMap");
        final Map<String, CustomDocResponse> cooperationMap = (Map<String, CustomDocResponse>) infoMap.get("cooperationMap");
        final Map<String, String> countryRegionMap = (Map<String, String>) infoMap.get("countryRegionMap");

        data.forEach(customerV2WithBiz -> {
            com.yyigou.dsrp.cdc.api.v2.customer.vo.CustomerPageVO customerPageVO = new com.yyigou.dsrp.cdc.api.v2.customer.vo.CustomerPageVO();
            BeanUtils.copyProperties(customerV2WithBiz, customerPageVO);

            customerPageVO.setTransactionTypeName(transactionTypeMap.getOrDefault(customerV2WithBiz.getTransactionType(), new TransactionTypeResponse()).getTransactionTypeName());

            // 补充使用组织名称
            if (orgNo2OrganizationVo.containsKey(customerV2WithBiz.getUseOrgNo())) {
                customerPageVO.setUseOrgName(orgNo2OrganizationVo.get(customerV2WithBiz.getUseOrgNo()).getOrgName());
            }

            // 补充管理组织名称
            if (orgNo2OrganizationVo.containsKey(customerV2WithBiz.getManageOrgNo())) {
                customerPageVO.setManageOrgName(orgNo2OrganizationVo.get(customerV2WithBiz.getManageOrgNo()).getOrgName());
            }

            // 补充客户业务信息
            if (customerBizMap.containsKey(customerV2WithBiz.getBizId())) {
                CustomerBiz customerBiz = customerBizMap.get(customerV2WithBiz.getBizId());
                BeanUtils.copyProperties(customerBiz, customerPageVO);

                if (null != customerBiz.getBusinessType()) {
                    customerPageVO.setBusinessTypeName(businessTypeMap.getOrDefault(customerBiz.getBusinessType(), new CustomDocResponse()).getDocItemName());
                }

                customerPageVO.setCooperationMode(customerBiz.getCooperationMode());

                if (null != customerBiz.getCooperationMode()) {
                    customerPageVO.setCooperationModeName(cooperationMap.getOrDefault(customerBiz.getCooperationMode(), new CustomDocResponse()).getDocItemName());
                }

                customerPageVO.setOwnerCompany(customerBiz.getOwnerCompany());
            }

            // 补充业务首营状态
            if (customerGspMap.containsKey(customerV2WithBiz.getCustomerCode())) {
                Map<String, CustomerGspAudit> org2GspMap = customerGspMap.get(customerV2WithBiz.getCustomerCode());
                if (org2GspMap.containsKey(customerV2WithBiz.getUseOrgNo())) {
                    CustomerGspAudit customerGspAudit = org2GspMap.get(customerV2WithBiz.getUseOrgNo());
                    if (null != customerGspAudit.getGspAuditStatus()) {
                        customerPageVO.setGspAuditStatus(customerGspAudit.getGspAuditStatus());
                        customerPageVO.setGspAuditStatusName(GspAuditStatusEnum.getByType(customerGspAudit.getGspAuditStatus()) != null ? GspAuditStatusEnum.getByType(customerGspAudit.getGspAuditStatus()).getName() : "");
                    }
                    customerPageVO.setGspAuditResult(customerGspAudit.getGspAuditResult());
                }
            }

            if (customerBaseMap.containsKey(customerV2WithBiz.getCustomerCode())) {
                Map<String, CustomerBase> org2BaseMap = customerBaseMap.get(customerV2WithBiz.getCustomerCode());
                if (org2BaseMap.containsKey(customerV2WithBiz.getUseOrgNo())) {
                    CustomerBase customerBase = org2BaseMap.get(customerV2WithBiz.getUseOrgNo());
                    customerPageVO.setControlStatus(customerBase.getControlStatus());
                    customerPageVO.setControlStatusName(ControlStatusEnum.getByValue(Integer.valueOf(customerBase.getControlStatus())).getName());
                }
            }

            if (companyMap.containsKey(customerV2WithBiz.getCompanyNo())) {
                final CompanyV2 company = companyMap.get(customerV2WithBiz.getCompanyNo());
                customerPageVO.setTaxCategoryName(taxCategoryMap.getOrDefault(company.getTaxCategory(), new CustomDocResponse()).getDocItemName());
                customerPageVO.setEconomicTypeName(economicTypeMap.getOrDefault(company.getEconomicType(), new CustomDocResponse()).getDocItemName());
                customerPageVO.setCompanyName(company.getCompanyName());
                customerPageVO.setUnifiedSocialCode(company.getUnifiedSocialCode());
//                customerVO.setCountry(company.getCountry());
                customerPageVO.setCountryRegionId(company.getCountryRegionId());
                if (StringUtils.isNotEmpty(company.getCountryRegionId())) {
                    customerPageVO.setCountryRegionName(countryRegionMap.get(company.getCountryRegionId()));
                }
                customerPageVO.setFactoryType(company.getFactoryType());
                customerPageVO.setFactoryTypeName(FactoryTypeEnum.getByType(company.getFactoryType()) == null ? null : FactoryTypeEnum.getByType(company.getFactoryType()).getName());
                customerPageVO.setEconomicType(company.getEconomicType());
//                customerVO.setIsAssociatedEnterprise(company.getIsAssociatedEnterprise());
//                customerVO.setAssociatedOrgNo(company.getAssociatedOrgNo());
//                customerVO.setAssociatedOrgCode(company.getAssociatedOrgCode());
//                customerVO.setAssociatedOrgName(company.getAssociatedOrgName());
                customerPageVO.setInstitutionalType(company.getInstitutionalType());
                customerPageVO.setAddress(company.getAddress());

                InstitutionalTypeEnum institutionalTypeEnum = InstitutionalTypeEnum.getByType(company.getInstitutionalType());
                if (null != institutionalTypeEnum) {
                    customerPageVO.setInstitutionalTypeName(institutionalTypeEnum.getName());
                }
            }

            customerPageVO.setCustomerCategoryName(categoryMap.getOrDefault(customerV2WithBiz.getCustomerCategoryNo(), new CustomerCategoryV2()).getCategoryName());

            result.add(customerPageVO);
        });
        return result;
    }

    private List<com.yyigou.dsrp.cdc.api.v2.customer.vo.CustomerReversePageVO> completeSupplementReversePageVO(String enterpriseNo, String useOrgNo, List<CustomerV2WithBase> data) {
        List<com.yyigou.dsrp.cdc.api.v2.customer.vo.CustomerReversePageVO> result = new ArrayList<>();

        Map<String, Object> infoMap = collectBasicInfo(enterpriseNo, data, Collections.singletonList(useOrgNo),
                Sets.newHashSet(
                        "categoryMap",
                        "companyMap",
                        "taxCategoryMap",
                        "economicTypeMap",
                        "transactionTypeMap",
                        "orgMap",
                        "countryRegionMap"
                ));
        final Map<String, CustomerCategoryV2> categoryMap = (Map<String, CustomerCategoryV2>) infoMap.get("categoryMap");
        final Map<String, CompanyV2> companyMap = (Map<String, CompanyV2>) infoMap.get("companyMap");
        final Map<String, CustomDocResponse> taxCategoryMap = (Map<String, CustomDocResponse>) infoMap.get("taxCategoryMap");
        final Map<String, CustomDocResponse> economicTypeMap = (Map<String, CustomDocResponse>) infoMap.get("economicTypeMap");
        final Map<String, TransactionTypeResponse> transactionTypeMap = (Map<String, TransactionTypeResponse>) infoMap.get("transactionTypeMap");
        final Map<String, OrganizationVo> orgNo2OrganizationVo = (Map<String, OrganizationVo>) infoMap.get("orgMap");
        final Map<String, String> countryRegionMap = (Map<String, String>) infoMap.get("countryRegionMap");

        data.forEach(customer -> {
            com.yyigou.dsrp.cdc.api.v2.customer.vo.CustomerReversePageVO customerReversePageVO = new com.yyigou.dsrp.cdc.api.v2.customer.vo.CustomerReversePageVO();
            BeanUtils.copyProperties(customer, customerReversePageVO);

            customerReversePageVO.setAssigned(null != customer.getBaseId() ? CommonIfEnum.YES.getValue() : CommonIfEnum.NO.getValue());

            customerReversePageVO.setTransactionTypeName(transactionTypeMap.getOrDefault(customer.getTransactionType(), new TransactionTypeResponse()).getTransactionTypeName());

            // 补充使用组织名称
            if (orgNo2OrganizationVo.containsKey(customer.getUseOrgNo())) {
                customerReversePageVO.setUseOrgName(orgNo2OrganizationVo.get(customer.getUseOrgNo()).getOrgName());
            }

            // 补充管理组织名称
            if (orgNo2OrganizationVo.containsKey(customer.getManageOrgNo())) {
                customerReversePageVO.setManageOrgName(orgNo2OrganizationVo.get(customer.getManageOrgNo()).getOrgName());
            }

            if (companyMap.containsKey(customer.getCompanyNo())) {
                final CompanyV2 company = companyMap.get(customer.getCompanyNo());
                customerReversePageVO.setTaxCategory(company.getTaxCategory());
                customerReversePageVO.setTaxCategoryName(taxCategoryMap.getOrDefault(company.getTaxCategory(), new CustomDocResponse()).getDocItemName());
                customerReversePageVO.setEconomicType(company.getEconomicType());
                customerReversePageVO.setEconomicTypeName(economicTypeMap.getOrDefault(company.getEconomicType(), new CustomDocResponse()).getDocItemName());
                customerReversePageVO.setCompanyName(company.getCompanyName());
                customerReversePageVO.setUnifiedSocialCode(company.getUnifiedSocialCode());
//                customerVO.setCountry(company.getCountry());
                customerReversePageVO.setCountryRegionId(company.getCountryRegionId());
                if (StringUtils.isNotEmpty(company.getCountryRegionId())) {
                    customerReversePageVO.setCountryRegionName(countryRegionMap.get(company.getCountryRegionId()));
                }
                customerReversePageVO.setFactoryType(company.getFactoryType());
                customerReversePageVO.setFactoryTypeName(FactoryTypeEnum.getByType(company.getFactoryType()) == null ? null : FactoryTypeEnum.getByType(company.getFactoryType()).getName());
//                customerVO.setIsAssociatedEnterprise(company.getIsAssociatedEnterprise());
//                customerVO.setAssociatedOrgNo(company.getAssociatedOrgNo());
//                customerVO.setAssociatedOrgCode(company.getAssociatedOrgCode());
//                customerVO.setAssociatedOrgName(company.getAssociatedOrgName());

                customerReversePageVO.setIsMedicalInstitution(company.getIsMedicalInstitution());
                customerReversePageVO.setInstitutionalType(company.getInstitutionalType());
                customerReversePageVO.setHospitalType(company.getHospitalType());
                customerReversePageVO.setHospitalClass(company.getHospitalClass());
            }

            customerReversePageVO.setCustomerCategoryName(categoryMap.getOrDefault(customer.getCustomerCategoryNo(), new CustomerCategoryV2()).getCategoryName());

            result.add(customerReversePageVO);
        });
        return result;
    }

    @Override
    public Boolean checkOnlyCode(OperationModel operationModel, String no, String code) {
        String realNo = null;
        if (StringUtils.isNotEmpty(no)) {
            CustomerBase useBase = customerBaseDAO.selectOne(Wrappers.<CustomerBase>lambdaQuery()
                    .eq(CustomerBase::getEnterpriseNo, operationModel.getEnterpriseNo())
                    .eq(CustomerBase::getCustomerNo, no)
                    .eq(CustomerBase::getDeleted, DeletedEnum.UN_DELETE.getValue()));
            if (null != useBase) {
                if (useBase.getManageOrgNo().equals(useBase.getUseOrgNo())) {
                    realNo = useBase.getCustomerNo();
                } else {
                    CustomerBase manageBase = customerBaseDAO.selectOne(Wrappers.<CustomerBase>lambdaQuery()
                            .eq(CustomerBase::getEnterpriseNo, operationModel.getEnterpriseNo())
                            .eq(CustomerBase::getCustomerCode, useBase.getCustomerCode())
                            .apply(" manage_org_no = use_org_no ")
                            .eq(CustomerBase::getDeleted, DeletedEnum.UN_DELETE.getValue()));
                    if (null != manageBase) {
                        realNo = manageBase.getCustomerNo();
                    }
                }
            }
        }

        return customerV2DAO.selectCount(new LambdaQueryWrapper<CustomerV2>()
                .eq(CustomerV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CustomerV2::getCustomerCode, code)
                .eq(CustomerV2::getDeleted, DeletedEnum.UN_DELETE.getValue())
                .ne(StringUtils.isNotEmpty(realNo), CustomerV2::getCustomerNo, realNo)) <= 0;
    }

    @Override
    public Boolean checkOnlyName(OperationModel operationModel, String no, String name) {
        String realNo = null;
        if (StringUtils.isNotEmpty(no)) {
            CustomerBase useBase = customerBaseDAO.selectOne(Wrappers.<CustomerBase>lambdaQuery()
                    .eq(CustomerBase::getEnterpriseNo, operationModel.getEnterpriseNo())
                    .eq(CustomerBase::getCustomerNo, no)
                    .eq(CustomerBase::getDeleted, DeletedEnum.UN_DELETE.getValue()));
            if (null != useBase) {
                if (useBase.getManageOrgNo().equals(useBase.getUseOrgNo())) {
                    realNo = useBase.getCustomerNo();
                } else {
                    CustomerBase manageBase = customerBaseDAO.selectOne(Wrappers.<CustomerBase>lambdaQuery()
                            .eq(CustomerBase::getEnterpriseNo, operationModel.getEnterpriseNo())
                            .eq(CustomerBase::getCustomerCode, useBase.getCustomerCode())
                            .apply(" manage_org_no = use_org_no ")
                            .eq(CustomerBase::getDeleted, DeletedEnum.UN_DELETE.getValue()));
                    if (null != manageBase) {
                        realNo = manageBase.getCustomerNo();
                    }
                }
            }
        }

        return customerV2DAO.selectCount(new LambdaQueryWrapper<CustomerV2>()
                .eq(CustomerV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CustomerV2::getCustomerName, name)
                .eq(CustomerV2::getDeleted, DeletedEnum.UN_DELETE.getValue())
                .ne(StringUtils.isNotEmpty(realNo), CustomerV2::getCustomerNo, realNo)) <= 0;
    }

    @Override
    public Boolean checkQuoteCategory(OperationModel operationModel, String customerCategoryNo) {
        return customerV2DAO.exists(Wrappers.<CustomerV2>lambdaQuery()
                .eq(CustomerV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CustomerV2::getCustomerCategoryNo, customerCategoryNo)
                .eq(CustomerV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
    }

    @Override
    public List<CustomerV2> queryByManageOrgNoList(OperationModel operationModel, String customerCategoryNo, List<String> manageOrgNoList) {
        return customerV2DAO.selectList(Wrappers.<CustomerV2>lambdaQuery()
                .eq(CustomerV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CustomerV2::getCustomerCategoryNo, customerCategoryNo)
                .in(CustomerV2::getManageOrgNo, manageOrgNoList)
                .eq(CustomerV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
    }

    @Override
    public List<CustomerV2> queryByCompanyNo(String enterpriseNo, String companyNo) {
        return customerV2DAO.selectList(Wrappers.lambdaQuery(CustomerV2.class)
                .eq(CustomerV2::getEnterpriseNo, enterpriseNo)
                .eq(CustomerV2::getCompanyNo, companyNo)
                .eq(CustomerV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
    }

    private List<CustomerBasicResponse> convert2CustomerBasicResponse(List<CustomerV2> customerV2s) {
        if (CollectionUtils.isEmpty(customerV2s)) {
            return Collections.emptyList();
        }

        String enterpriseNo = customerV2s.get(0).getEnterpriseNo();

        Map<String, Object> infoMap = collectBasicInfo(enterpriseNo, customerV2s, Collections.emptyList(),
                Sets.newHashSet(
                        "taxCategoryMap",
                        "economicTypeMap",
                        "companyMap",
                        "transactionTypeMap",
                        "orgMap",
                        "categoryMap",
                        "countryRegionMap"
                ));

        final Map<String, CustomDocResponse> taxCategoryMap = (Map<String, CustomDocResponse>) infoMap.get("taxCategoryMap");

        final Map<String, CustomDocResponse> economicTypeMap = (Map<String, CustomDocResponse>) infoMap.get("economicTypeMap");

        final Map<String, CompanyV2> companyMap = (Map<String, CompanyV2>) infoMap.get("companyMap");

        final Map<String, TransactionTypeResponse> transactionTypeMap = (Map<String, TransactionTypeResponse>) infoMap.get("transactionTypeMap");

        final Map<String, OrganizationVo> orgNo2OrganizationVo = (Map<String, OrganizationVo>) infoMap.get("orgMap");

        final Map<String, CustomerCategoryV2> categoryMap = (Map<String, CustomerCategoryV2>) infoMap.get("categoryMap");

        final Map<String, String> countryRegionMap = (Map<String, String>) infoMap.get("countryRegionMap");


        List<CustomerBasicResponse> customerBasicResponses = BeanUtil.copyFieldsList(customerV2s, CustomerBasicResponse.class);

        for (CustomerBasicResponse customerBasicRespons : customerBasicResponses) {
            // 补充名称类字段
            if (orgNo2OrganizationVo.containsKey(customerBasicRespons.getManageOrgNo())) {
                customerBasicRespons.setManageOrgName(orgNo2OrganizationVo.get(customerBasicRespons.getManageOrgNo()).getOrgName());
            }

            if (categoryMap.containsKey(customerBasicRespons.getCustomerCategoryNo())) {
                customerBasicRespons.setCustomerCategoryName(categoryMap.get(customerBasicRespons.getCustomerCategoryNo()).getCategoryName());
            }

            if (categoryMap.containsKey(customerBasicRespons.getTransactionType())) {
                customerBasicRespons.setTransactionTypeName(transactionTypeMap.get(customerBasicRespons.getTransactionType()).getTransactionTypeName());
            }

            customerBasicRespons.setRetailInvestorsName(CommonIfEnum.getNameByValue(customerBasicRespons.getRetailInvestors()));

            // 补充企业信息
            CompanyV2 companyV2 = companyMap.get(customerBasicRespons.getCompanyNo());
            customerBasicRespons.setCompanyCode(companyV2.getCompanyCode());
            customerBasicRespons.setCompanyName(companyV2.getCompanyName());
            customerBasicRespons.setFactoryType(companyV2.getFactoryType());
            customerBasicRespons.setFactoryTypeName(FactoryTypeEnum.getByType(customerBasicRespons.getFactoryType()) == null ? null : FactoryTypeEnum.getByType(customerBasicRespons.getFactoryType()).getName());
//            customerBasicRespons.setCountry(companyV2.getCountry());
            customerBasicRespons.setCountryRegionId(companyV2.getCountryRegionId());
            if (StringUtils.isNotEmpty(companyV2.getCountryRegionId())) {
                customerBasicRespons.setCountryRegionName(countryRegionMap.get(companyV2.getCountryRegionId()));
            }
            customerBasicRespons.setTaxCategory(companyV2.getTaxCategory());
            customerBasicRespons.setTaxCategoryName(taxCategoryMap.getOrDefault(companyV2.getTaxCategory(), new CustomDocResponse()).getDocItemName());
            customerBasicRespons.setEconomicType(companyV2.getEconomicType());
            customerBasicRespons.setEconomicTypeName(economicTypeMap.getOrDefault(companyV2.getEconomicType(), new CustomDocResponse()).getDocItemName());

//            customerBasicRespons.setIsAssociatedEnterprise(companyV2.getIsAssociatedEnterprise());
//            customerBasicRespons.setAssociatedOrgNo(companyV2.getAssociatedOrgNo());
//            customerBasicRespons.setAssociatedOrgCode(companyV2.getAssociatedOrgCode());
//            customerBasicRespons.setAssociatedOrgName(companyV2.getAssociatedOrgName());

            customerBasicRespons.setIsMedicalInstitution(companyV2.getIsMedicalInstitution());
            customerBasicRespons.setIsMedicalInstitutionName(CommonIfEnum.YES.getValue().equals(customerBasicRespons.getIsMedicalInstitution()) ? CommonIfEnum.YES.getName() : CommonIfEnum.NO.getName());
            customerBasicRespons.setInstitutionalType(companyV2.getInstitutionalType());
            customerBasicRespons.setInstitutionalTypeName(InstitutionalTypeEnum.getByType(customerBasicRespons.getInstitutionalType()) != null ? InstitutionalTypeEnum.getByType(customerBasicRespons.getInstitutionalType()).getName() : "");
            customerBasicRespons.setHospitalType(companyV2.getHospitalType());
            customerBasicRespons.setHospitalClass(companyV2.getHospitalClass());
        }

        return customerBasicResponses;
    }

    @Override
    public List<CustomerBasicResponse> queryStandardInfo(CustomerStandardQueryReq customerStandardQueryReq) {
        ValidatorUtils.checkEmptyThrowEx(customerStandardQueryReq, "参数不能为空");
        ValidatorUtils.checkEmptyThrowEx(customerStandardQueryReq.getEnterpriseNo(), "企业编号不能为空");

        return convert2CustomerBasicResponse(customerV2DAO.queryStandardInfo(customerStandardQueryReq));
    }

    private List<CustomerBizResponse> convert2CustomerWithBizInfoResponse(List<CustomerV2WithBiz> customerV2WithBizs) {
        if (CollectionUtils.isEmpty(customerV2WithBizs)) {
            return Collections.emptyList();
        }

        String enterpriseNo = customerV2WithBizs.get(0).getEnterpriseNo();

        Map<String, Object> infoMap = collectBizInfo(enterpriseNo, customerV2WithBizs,
                Sets.newHashSet(
                        "taxCategoryMap",
                        "economicTypeMap",
                        "companyMap",
                        "transactionTypeMap",
                        "orgMap",
                        "categoryMap",
                        "bankMap",
                        "currencyMap",
                        "cooperationMap",
                        "settlementModesMap",
                        "paymentTermMap",
                        "businessTypeMap",
                        "priceCustomerCategoryMap",
                        "linkManMap",
                        "addressMap",
                        "salesManMap",
                        "invoiceMap",
                        "customerBizMap",
                        "customerGspMap",
                        "countryRegionMap",
                        "customerBaseMap"
                ));

        // 税收分类
        final Map<String, CustomDocResponse> taxCategoryMap = (Map<String, CustomDocResponse>) infoMap.get("taxCategoryMap");

        // 经济类型
        final Map<String, CustomDocResponse> economicTypeMap = (Map<String, CustomDocResponse>) infoMap.get("economicTypeMap");

        // 企业信息
        final Map<String, CompanyV2> companyMap = (Map<String, CompanyV2>) infoMap.get("companyMap");

        // 客户类型
        final Map<String, TransactionTypeResponse> transactionTypeMap = (Map<String, TransactionTypeResponse>) infoMap.get("transactionTypeMap");

        // 组织信息
        final Map<String, OrganizationVo> orgNo2OrganizationVo = (Map<String, OrganizationVo>) infoMap.get("orgMap");

        // 分类
        final Map<String, CustomerCategoryV2> categoryMap = (Map<String, CustomerCategoryV2>) infoMap.get("categoryMap");

        // 获取银行信息
        final Map<String, List<CompanyBankV2>> companyNo2BankList = (Map<String, List<CompanyBankV2>>) infoMap.get("bankMap");

        // 获取币种
        final Map<String, String> currencyMap = (Map<String, String>) infoMap.get("currencyMap");

        // 获取合作性质
        final Map<String, CustomDocResponse> cooperationMap = (Map<String, CustomDocResponse>) infoMap.get("cooperationMap");

        // 获取结算方式
        final Map<String, String> settlementModesMap = (Map<String, String>) infoMap.get("settlementModesMap");

        //付款条件
        final Map<String, CustomDocResponse> paymentTermMap = (Map<String, CustomDocResponse>) infoMap.get("paymentTermMap");

        //客户性质
        final Map<String, CustomDocResponse> businessTypeMap = (Map<String, CustomDocResponse>) infoMap.get("businessTypeMap");

        //价格体系
        final Map<String, String> priceCustomerCategoryMap = (Map<String, String>) infoMap.get("priceCustomerCategoryMap");

        // 获取联系人
        final Map<String, Map<String, List<CompanyLinkmanV2>>> code2org2LinkManList = (Map<String, Map<String, List<CompanyLinkmanV2>>>) infoMap.get("linkManMap");

        // 获取地址
        final Map<String, Map<String, List<CompanyShippingAddressV2>>> code2org2AddressList = (Map<String, Map<String, List<CompanyShippingAddressV2>>>) infoMap.get("addressMap");

        // 获取负责人
        final Map<String, Map<String, List<CustomerSalesManV2>>> code2org2SalesManList = (Map<String, Map<String, List<CustomerSalesManV2>>>) infoMap.get("salesManMap");

        // 获取开票
        final Map<String, Map<String, List<CustomerInvoiceV2>>> code2org2InvoiceList = (Map<String, Map<String, List<CustomerInvoiceV2>>>) infoMap.get("invoiceMap");

        // 国家地区
        final Map<String, String> countryRegionMap = (Map<String, String>) infoMap.get("countryRegionMap");

        // 业务信息
        final Map<Long, CustomerBiz> customerBizMap = (Map<Long, CustomerBiz>) infoMap.get("customerBizMap");

        // 首营信息
        final Map<String, Map<String, CustomerGspAudit>> code2org2GspMap = (Map<String, Map<String, CustomerGspAudit>>) infoMap.get("customerGspMap");

        //分派信息
        final Map<String, Map<String, CustomerBase>> code2org2BaseMap = (Map<String, Map<String, CustomerBase>>) infoMap.get("customerBaseMap");

        List<CustomerBizResponse> customerBizResponses = BeanUtil.copyFieldsList(customerV2WithBizs, CustomerBizResponse.class);

        for (CustomerBizResponse customerBizResponse : customerBizResponses) {
            {
                // 补充名称类字段
                if (orgNo2OrganizationVo.containsKey(customerBizResponse.getManageOrgNo())) {
                    customerBizResponse.setManageOrgName(orgNo2OrganizationVo.get(customerBizResponse.getManageOrgNo()).getOrgName());
                }

                if (categoryMap.containsKey(customerBizResponse.getCustomerCategoryNo())) {
                    customerBizResponse.setCustomerCategoryName(categoryMap.get(customerBizResponse.getCustomerCategoryNo()).getCategoryName());
                }

                if (categoryMap.containsKey(customerBizResponse.getTransactionType())) {
                    customerBizResponse.setTransactionTypeName(transactionTypeMap.get(customerBizResponse.getTransactionType()).getTransactionTypeName());
                }

                customerBizResponse.setRetailInvestorsName(CommonIfEnum.getNameByValue(customerBizResponse.getRetailInvestors()));
            }

            {
                // 补充企业信息
                CompanyV2 companyV2 = companyMap.get(customerBizResponse.getCompanyNo());
                customerBizResponse.setCompanyCode(companyV2.getCompanyCode());
                customerBizResponse.setCompanyName(companyV2.getCompanyName());
                customerBizResponse.setFactoryType(companyV2.getFactoryType());
                customerBizResponse.setFactoryTypeName(FactoryTypeEnum.getByType(customerBizResponse.getFactoryType()) == null ? null : FactoryTypeEnum.getByType(customerBizResponse.getFactoryType()).getName());
//                customerBizResponse.setCountry(companyV2.getCountry());
                customerBizResponse.setCountryRegionId(companyV2.getCountryRegionId());
                if (StringUtils.isNotEmpty(companyV2.getCountryRegionId())) {
                    customerBizResponse.setCountryRegionName(countryRegionMap.get(companyV2.getCountryRegionId()));
                }
                customerBizResponse.setTaxCategory(companyV2.getTaxCategory());
                customerBizResponse.setTaxCategoryName(taxCategoryMap.getOrDefault(companyV2.getTaxCategory(), new CustomDocResponse()).getDocItemName());
                customerBizResponse.setEconomicType(companyV2.getEconomicType());
                customerBizResponse.setEconomicTypeName(economicTypeMap.getOrDefault(companyV2.getEconomicType(), new CustomDocResponse()).getDocItemName());

//                customerBizResponse.setIsAssociatedEnterprise(companyV2.getIsAssociatedEnterprise());
//                customerBizResponse.setAssociatedOrgNo(companyV2.getAssociatedOrgNo());
//                customerBizResponse.setAssociatedOrgCode(companyV2.getAssociatedOrgCode());
//                customerBizResponse.setAssociatedOrgName(companyV2.getAssociatedOrgName());

                customerBizResponse.setCompanyBankList(companyV2Service.convert2BankResponse(currencyMap, companyNo2BankList.get(customerBizResponse.getCompanyNo())));
            }

            {
                // 补充业务信息
                CustomerBiz customerBiz = customerBizMap.get(customerBizResponse.getBizId());
                BeanUtils.copyProperties(customerBiz, customerBizResponse);

                customerBizResponse.setUseOrgName(orgNo2OrganizationVo.get(customerBizResponse.getUseOrgNo()).getOrgName());
                customerBizResponse.setCooperationModeName(cooperationMap.getOrDefault(customerBizResponse.getCooperationMode(), new CustomDocResponse()).getDocItemName());
                customerBizResponse.setCurrencyName(currencyMap.getOrDefault(customerBizResponse.getCurrency(), ""));
                customerBizResponse.setSettlementModesName(settlementModesMap.getOrDefault(customerBizResponse.getSettlementModes(), ""));
                customerBizResponse.setBusinessTypeName(businessTypeMap.getOrDefault(customerBizResponse.getBusinessType(), new CustomDocResponse()).getDocItemName());
                customerBizResponse.setPriceCategoryName(priceCustomerCategoryMap.getOrDefault(customerBizResponse.getPriceCategoryCode(), ""));

                customerBizResponse.setCompanyLinkmanList(convert2LinkManResponse(code2org2LinkManList.getOrDefault(customerBizResponse.getCustomerCode(), Collections.emptyMap()).get(customerBizResponse.getUseOrgNo())));
                customerBizResponse.setCompanyShippingAddressList(convert2AddressResponse(code2org2AddressList.getOrDefault(customerBizResponse.getCustomerCode(), Collections.emptyMap()).get(customerBizResponse.getUseOrgNo())));
                customerBizResponse.setCustomerSalesManList(convert2SalesManResponse(code2org2SalesManList.getOrDefault(customerBizResponse.getCustomerCode(), Collections.emptyMap()).get(customerBizResponse.getUseOrgNo())));
                customerBizResponse.setCustomerInvoiceList(convert2InvoiceResponse(code2org2InvoiceList.getOrDefault(customerBizResponse.getCustomerCode(), Collections.emptyMap()).get(customerBizResponse.getUseOrgNo())));
            }

            {
                // 补充首营信息
                customerBizResponse.setGspAuditStatus(code2org2GspMap.getOrDefault(customerBizResponse.getCustomerCode(), Collections.emptyMap()).getOrDefault(customerBizResponse.getUseOrgNo(), new CustomerGspAudit()).getGspAuditStatus());
            }

            {
                // 补充分派信息
                customerBizResponse.setControlStatus(code2org2BaseMap.getOrDefault(customerBizResponse.getCustomerCode(), Collections.emptyMap()).getOrDefault(customerBizResponse.getUseOrgNo(), new CustomerBase()).getControlStatus());
            }
        }

        return customerBizResponses;
    }

    private List<CompanyShippingAddressResponse> convert2AddressResponse(List<CompanyShippingAddressV2> companyShippingAddressV2s) {
        if (CollectionUtils.isEmpty(companyShippingAddressV2s)) {
            return Collections.emptyList();
        }

        List<String> regionCodeList = companyShippingAddressV2s.stream().map(CompanyShippingAddressV2::getRegionCode).filter(s -> !StringUtils.isEmpty(s)).collect(Collectors.toList());
        List<AreaCodeVo> areaCodeList = dictEnterpriseService.findAreaByCodes(regionCodeList);
        final Map<String, AreaCodeVo> areaMap = areaCodeList.stream().collect(Collectors.toMap(AreaCodeVo::getAreaCode, Function.identity()));

        List<CompanyShippingAddressResponse> companyShippingAddressResponseList = BeanUtil.copyFieldsList(companyShippingAddressV2s, CompanyShippingAddressResponse.class);
        for (CompanyShippingAddressResponse companyShippingAddressResponse : companyShippingAddressResponseList) {
            companyShippingAddressResponse.setRegionFullName(areaMap.getOrDefault(companyShippingAddressResponse.getRegionCode(), new AreaCodeVo()).getAreaFullName());
        }
        return companyShippingAddressResponseList;
    }

    private List<CompanyLinkmanResponse> convert2LinkManResponse(List<CompanyLinkmanV2> companyLinkmanV2s) {
        if (CollectionUtils.isEmpty(companyLinkmanV2s)) {
            return Collections.emptyList();
        }

        List<CompanyLinkmanResponse> companyLinkmanResponseList = BeanUtil.copyFieldsList(companyLinkmanV2s, CompanyLinkmanResponse.class);
//        for (CompanyLinkmanResponse companyLinkmanResponse : companyLinkmanResponseList) {
//        }
        return companyLinkmanResponseList;
    }

    private List<CustomerSalesManResponse> convert2SalesManResponse(List<CustomerSalesManV2> customerSalesManV2s) {
        if (CollectionUtils.isEmpty(customerSalesManV2s)) {
            return Collections.emptyList();
        }

        String enterpriseNo = customerSalesManV2s.get(0).getEnterpriseNo();
        List<EmployeeVo> employeeList = employeeService.getEmployeeList(enterpriseNo, customerSalesManV2s.stream().map(CustomerSalesManV2::getSalesManNo).collect(Collectors.toList()));
        Map<String, EmployeeVo> employeeMap = employeeList.stream().collect(Collectors.toMap(EmployeeVo::getEmployeeNo, Function.identity()));

        List<CustomerSalesManResponse> customerSalesManResponseList = BeanUtil.copyFieldsList(customerSalesManV2s, CustomerSalesManResponse.class);
        for (CustomerSalesManResponse customerOrderManResponse : customerSalesManResponseList) {
            customerOrderManResponse.setMobile(employeeMap.getOrDefault(customerOrderManResponse.getSalesManNo(), new EmployeeVo()).getMobile());
        }
        return customerSalesManResponseList;
    }

    private List<CustomerInvoiceResponse> convert2InvoiceResponse(List<CustomerInvoiceV2> customerInvoiceV2List) {
        if (CollectionUtils.isEmpty(customerInvoiceV2List)) {
            return Collections.emptyList();
        }

        List<CustomerInvoiceResponse> customerInvoiceResponseList = BeanUtil.copyFieldsList(customerInvoiceV2List, CustomerInvoiceResponse.class);
        for (CustomerInvoiceResponse customerInvoiceResponse : customerInvoiceResponseList) {
            customerInvoiceResponse.setTypeName(InvoiceTypeEnum.getByType(customerInvoiceResponse.getType()) == null ? null : InvoiceTypeEnum.getByType(customerInvoiceResponse.getType()).getName());

        }
        return customerInvoiceResponseList;
    }

    @Override
    public List<CustomerBizResponse> queryWithBizInfo(CustomerStandardQueryReq customerStandardQueryReq) {
        ValidatorUtils.checkEmptyThrowEx(customerStandardQueryReq, "参数不能为空");
        ValidatorUtils.checkEmptyThrowEx(customerStandardQueryReq.getEnterpriseNo(), "企业编号不能为空");

        return convert2CustomerWithBizInfoResponse(customerV2DAO.queryWithBizInfo(customerStandardQueryReq));
    }

    @Override
    public List<CustomerBizResponse> selectCustomerBizByCodeOrgPair(CustomerCodeOrgPairListReq customerCodeOrgPairListReq) {
        ValidatorUtils.checkEmptyThrowEx(customerCodeOrgPairListReq, "参数不能为空");
        ValidatorUtils.checkEmptyThrowEx(customerCodeOrgPairListReq.getEnterpriseNo(), "企业编号不能为空");

        return convert2CustomerWithBizInfoResponse(customerV2DAO.selectCustomerBizByCodeOrgPair(customerCodeOrgPairListReq));
    }

    @Override
    public PageVo<CustomerBizResponse> queryPageWithBizInfo(CustomerStandardQueryReq customerStandardQueryReq, PageDto pageDTO) {
        Page<CustomerV2WithBiz> page = PageHelper.startPage(pageDTO.getPageIndex(), pageDTO.getPageSize(), StringUtils.isBlank(pageDTO.getOrderBy()) ? "b.create_time desc" : pageDTO.getOrderBy());
        customerV2DAO.queryWithBizInfo(customerStandardQueryReq);

        return PageUtils.convertPageVo(page, this::convert2CustomerWithBizInfoResponse);
    }

    @Override
    @Transactional
    public CustomerCoordinationCreateResponse createCoordinationCustomer(String enterpriseNo, String orgNo, String customerName, String unifiedSocialCode, String omsCustomerNo) {
        ValidatorUtils.checkEmptyThrowEx(enterpriseNo, "企业编号不能为空");
        ValidatorUtils.checkEmptyThrowEx(orgNo, "组织编号不能为空");
        ValidatorUtils.checkEmptyThrowEx(customerName, "客户名称不能为空");
        ValidatorUtils.checkEmptyThrowEx(unifiedSocialCode, "统一社会信用代码不能为空");
        ValidatorUtils.checkTrueThrowEx(SystemConstant.DEFAULT_UNIFIED_SOCIAL_CODE.equals(unifiedSocialCode), "无效的社会信用代码");

        String authedType = null;
        String createOrgNo = null;

        GradeCheckMgrOrgRes gradeCheckMgrOrgRes = gradeControlService.checkOrgNoAndListMgrOrgNos(enterpriseNo, BillNameConstant.BDC_CUSTOMER_BILL, ViewNameConstant.BDC_CUSTOMER_VIEW, orgNo);
        if (gradeCheckMgrOrgRes.getMgrPrevEnabled()) {
            authedType = "manage";
            createOrgNo = orgNo;
        } else if (CollectionUtils.isNotEmpty(gradeCheckMgrOrgRes.getMgrOrgNos())) {
            authedType = "use";
            createOrgNo = gradeCheckMgrOrgRes.getMgrOrgNos().get(0);
        }

        if (null == authedType) {
            throw new BusinessException(com.yyigou.ddc.common.error.ErrorCode.param_invalid_code, "无档案管理权");
        }

        customerName = FormatNameUtils.formatName(customerName, Boolean.TRUE);
        log.warn("创建SCS协同客户名称转换后结果{}", customerName);

        CustomerV2 customer = customerV2DAO.selectOne(new LambdaQueryWrapper<CustomerV2>()
                .eq(CustomerV2::getEnterpriseNo, enterpriseNo)
                .eq(CustomerV2::getCustomerName, customerName)
                .eq(CustomerV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
        if (null != customer) {
            log.warn("当前租户内已存在客户{},不做创建处理", JSON.toJSONString(customer));
            if (!StringUtils.isEmpty(omsCustomerNo)) {
                CustomerV2 newCustomer = new CustomerV2();
                newCustomer.setOmsCustomerNo(omsCustomerNo);
                customerV2DAO.update(newCustomer, new LambdaQueryWrapper<CustomerV2>()
                        .eq(CustomerV2::getCustomerCode, customer.getCustomerCode()));
            }

            if ("use".equals(authedType)) {
                // 需要同步分派，否则拿不到使用组织分派后的no
                CustomerAssignExeRequest req = new CustomerAssignExeRequest();
                req.setEnterpriseNo(enterpriseNo);
                req.setManageOrgNo(createOrgNo);
                CustomerAssignDocExeRequest customerAssignDocExeRequest = new CustomerAssignDocExeRequest();
                customerAssignDocExeRequest.setCustomerCode(customer.getCustomerCode());
                customerAssignDocExeRequest.setUseOrgNoList(Collections.singletonList(orgNo));
                req.setDocList(Collections.singletonList(customerAssignDocExeRequest));
                assignCustomer(req);


                final String finalCreateOrgNo = createOrgNo;
                TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                    @Override
                    public void afterCommit() {
                        try {
                            log.warn("updateDocHandlerReq={},{},{},{},{}", enterpriseNo, BillNameConstant.BDC_CUSTOMER_BILL, customer.getCustomerCode(), finalCreateOrgNo, 0);

                            Boolean updateCallResult = gradeControlService.updateDocHandler(enterpriseNo, BillNameConstant.BDC_CUSTOMER_BILL, customer.getCustomerCode(), finalCreateOrgNo, 0);

                            log.warn("updateDocHandlerResp={}", updateCallResult);
                        } catch (Exception e) {
                            log.error("客户档案分派异常", e);
                        }
                    }
                });
            }

            CustomerCoordinationCreateResponse resp = new CustomerCoordinationCreateResponse();
            if ("use".equals(authedType)) {
                CustomerBase customerBase = customerBaseDAO.selectOne(Wrappers.lambdaQuery(CustomerBase.class)
                        .eq(CustomerBase::getEnterpriseNo, enterpriseNo)
                        .eq(CustomerBase::getCustomerCode, customer.getCustomerCode())
                        .eq(CustomerBase::getUseOrgNo, orgNo)
                        .eq(CustomerBase::getDeleted, DeletedEnum.UN_DELETE.getValue()));
                resp.setCustomerNo(customerBase.getCustomerNo());
            } else {
                resp.setCustomerNo(customer.getCustomerNo());
            }
            resp.setCustomerCode(customer.getCustomerCode());
            resp.setCustomerName(customer.getCustomerName());
            return resp;
        }

        //没有客户 需要新创建 创建客户的前提是定company所以需要判定company是否存在 1.查询企业名称 2.查询统一社会信用代码
        CompanyV2 company = companyV2Service.getByEnterpriseAndCompanyName(enterpriseNo, customerName);
        if (null == company) {
            company = companyV2Service.getByEnterpriseAndUnifiedSocialCodeDomestic(enterpriseNo, unifiedSocialCode);
        }
        //走创建逻辑企业逻辑
        if (null == company) {
            log.warn("根据客户名称:[{}],未在租户[{}]内查询到企业,新增处理", customerName, enterpriseNo);
            company = new CompanyV2();
            company.setEnterpriseNo(enterpriseNo);
            company.setCompanyNo(companyV2Service.generateCompanyNo());
            company.setCompanyName(customerName);
            company.setUnifiedSocialCode(unifiedSocialCode);
            //SCS创建的默认境内
            company.setFactoryType(FactoryTypeEnum.DOMESTIC.getValue());
            companyV2Service.save(company);
        }

        CustomerV2 newCustomer = new CustomerV2();
        newCustomer.setEnterpriseNo(enterpriseNo);
        newCustomer.setManageOrgNo(createOrgNo);
        newCustomer.setCompanyNo(company.getCompanyNo());
        newCustomer.setCustomerNo(numberCenterService.createNumber(NumberCenterConstant.CUSTOMER_NO_KEY));
        newCustomer.setCustomerCode(numberCenterService.createNumberForBill(BillNameConstant.BDC_CUSTOMER_BILL, enterpriseNo));
        newCustomer.setCustomerName(customerName);
        newCustomer.setUnifiedSocialCode(unifiedSocialCode);
        newCustomer.setBusinessFlag(CustomerBusinessFlagEnum.FORMAL.getValue());
        newCustomer.setSourceChannel("SCS");
        newCustomer.setOmsCustomerNo(omsCustomerNo);
        this.save(newCustomer);

        CustomerBiz customerBiz = new CustomerBiz();
        customerBiz.setEnterpriseNo(enterpriseNo);
        customerBiz.setUseOrgNo(createOrgNo);
        customerBiz.setCustomerNo(newCustomer.getCustomerNo());
        customerBiz.setCustomerCode(newCustomer.getCustomerCode());
        customerBiz.setDeleted(DeletedEnum.UN_DELETE.getValue());
        customerBizDAO.insert(customerBiz);

        CustomerBase customerBase = new CustomerBase();
        customerBase.setEnterpriseNo(enterpriseNo);
        customerBase.setCustomerNo(newCustomer.getCustomerNo());
        customerBase.setCustomerCode(newCustomer.getCustomerCode());
        customerBase.setManageOrgNo(newCustomer.getManageOrgNo());
        customerBase.setUseOrgNo(newCustomer.getManageOrgNo());
        customerBase.setDeleted(DeletedEnum.UN_DELETE.getValue());
        customerBaseDAO.insert(customerBase);

        log.warn("创建客户[{}]完成", newCustomer.getCustomerNo());

        if ("use".equals(authedType)) {
            // 需要同步分派，否则拿不到使用组织分派后的no
            CustomerAssignExeRequest req = new CustomerAssignExeRequest();
            req.setEnterpriseNo(enterpriseNo);
            req.setManageOrgNo(createOrgNo);
            CustomerAssignDocExeRequest customerAssignDocExeRequest = new CustomerAssignDocExeRequest();
            customerAssignDocExeRequest.setCustomerCode(newCustomer.getCustomerCode());
            customerAssignDocExeRequest.setUseOrgNoList(Collections.singletonList(orgNo));
            req.setDocList(Collections.singletonList(customerAssignDocExeRequest));
            assignCustomer(req);


            String finalCreateOrgNo = createOrgNo;
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                @Override
                public void afterCommit() {
                    try {
                        log.warn("updateDocHandlerReq={},{},{},{},{}", enterpriseNo, BillNameConstant.BDC_CUSTOMER_BILL, newCustomer.getCustomerCode(), finalCreateOrgNo, 0);

                        Boolean updateCallResult = gradeControlService.updateDocHandler(enterpriseNo, BillNameConstant.BDC_CUSTOMER_BILL, newCustomer.getCustomerCode(), finalCreateOrgNo, 0);

                        log.warn("updateDocHandlerResp={}", updateCallResult);
                    } catch (Exception e) {
                        log.error("客户档案分派异常", e);
                    }
                }
            });
        }

        CustomerCoordinationCreateResponse resp = new CustomerCoordinationCreateResponse();
        if ("use".equals(authedType)) {
            CustomerBase useCustomerBase = customerBaseDAO.selectOne(Wrappers.lambdaQuery(CustomerBase.class)
                    .eq(CustomerBase::getEnterpriseNo, enterpriseNo)
                    .eq(CustomerBase::getCustomerCode, newCustomer.getCustomerCode())
                    .eq(CustomerBase::getUseOrgNo, orgNo)
                    .eq(CustomerBase::getDeleted, DeletedEnum.UN_DELETE.getValue()));
            resp.setCustomerNo(useCustomerBase.getCustomerNo());
        } else {
            resp.setCustomerNo(newCustomer.getCustomerNo());
        }
        resp.setCustomerCode(newCustomer.getCustomerCode());
        resp.setCustomerName(newCustomer.getCustomerName());

        return resp;
    }

    @Override
    public Boolean syncCustomerNameByCompanyName(String enterpriseNo, String customerCode, CustomerV2 toUpdateCustomerV2) {
        LambdaUpdateWrapper<CustomerV2> updateWrapper = Wrappers.<CustomerV2>lambdaUpdate()
                // 更新字段
                .set(CustomerV2::getCustomerName, toUpdateCustomerV2.getCustomerName())
                .set(CustomerV2::getOperateName, toUpdateCustomerV2.getOperateName())
                .set(CustomerV2::getOperateNo, toUpdateCustomerV2.getOperateNo())
                .set(CustomerV2::getOperateTime, toUpdateCustomerV2.getOperateTime())
                // where条件
                .eq(CustomerV2::getEnterpriseNo, enterpriseNo)
                .eq(CustomerV2::getCustomerCode, customerCode);
        return customerV2DAO.update(null, updateWrapper) > 0;
    }

    private CustomerAssignExeResponse packAssignResp(String useOrgNo, String msg, boolean result, String docNo) {
        CustomerAssignExeResponse response = new CustomerAssignExeResponse();
        response.setUseOrgNo(useOrgNo);
        response.setMessage(msg);
        response.setSuccess(result);
        response.setDocNo(docNo);

        return response;
    }

    @Override
    @Transactional
    public List<CustomerAssignExeResponse> assignCustomer(CustomerAssignExeRequest req) {
        if (null == req || CollectionUtils.isEmpty(req.getDocList())) {
            return Collections.emptyList();
        }

        String lockKey = null;
        try {
            lockKey = String.join(DistributedLockConstant.DELIMITER, DistributedLockConstant.CUSTOMER_ASSIGN_LOCK_KEY, req.getEnterpriseNo(), req.getManageOrgNo());
            if (!redisClientUtil.lock(lockKey, DistributedLockConstant.DEFAULT_WAIT_TIME, DistributedLockConstant.DEFAULT_LEASE_TIME)) {
                throw new BusinessException(ErrorCode.param_invalid_code, "当前组织正在分派，稍后重试");
            }

            List<String> customerCodeList = req.getDocList().stream().map(CustomerAssignDocExeRequest::getCustomerCode).collect(Collectors.toList());
            List<CustomerV2> customerList = customerV2DAO.selectList(Wrappers.<CustomerV2>lambdaQuery()
                    .eq(CustomerV2::getEnterpriseNo, req.getEnterpriseNo())
                    .eq(CustomerV2::getManageOrgNo, req.getManageOrgNo())
                    .in(CustomerV2::getCustomerCode, customerCodeList)
                    .eq(CustomerV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
            Map<String, CustomerV2> code2CustomerMap = customerList.stream().collect(Collectors.toMap(CustomerV2::getCustomerCode, Function.identity()));

            List<CustomerBiz> customerBizList = customerBizDAO.selectList(Wrappers.<CustomerBiz>lambdaQuery()
                    .eq(CustomerBiz::getEnterpriseNo, req.getEnterpriseNo())
                    .in(CustomerBiz::getCustomerCode, customerCodeList)
                    .eq(CustomerBiz::getDeleted, DeletedEnum.UN_DELETE.getValue()));
            Map<String, Map<String, CustomerBiz>> code2usrOrgNo2Biz = customerBizList.stream().collect(Collectors.groupingBy(CustomerBiz::getCustomerCode, Collectors.toMap(CustomerBiz::getUseOrgNo, Function.identity())));

            List<CustomerBase> customerBaseList = customerBaseDAO.selectList(Wrappers.<CustomerBase>lambdaQuery()
                    .eq(CustomerBase::getEnterpriseNo, req.getEnterpriseNo())
                    .in(CustomerBase::getCustomerCode, customerCodeList)
                    .eq(CustomerBase::getManageOrgNo, req.getManageOrgNo())
                    .eq(CustomerBase::getDeleted, DeletedEnum.UN_DELETE.getValue()));
            Map<String, Map<String, CustomerBase>> code2usrOrgNo2Base = customerBaseList.stream().collect(Collectors.groupingBy(CustomerBase::getCustomerCode, Collectors.toMap(CustomerBase::getUseOrgNo, Function.identity())));

            List<CustomerGspAudit> customerGspAuditList = customerGspAuditDAO.selectList(Wrappers.<CustomerGspAudit>lambdaQuery()
                    .eq(CustomerGspAudit::getEnterpriseNo, req.getEnterpriseNo())
                    .in(CustomerGspAudit::getCustomerCode, customerCodeList)
                    .eq(CustomerGspAudit::getDeleted, DeletedEnum.UN_DELETE.getValue()));
            Map<String, Map<String, CustomerGspAudit>> code2usrOrgNo2Gsp = customerGspAuditList.stream().collect(Collectors.groupingBy(CustomerGspAudit::getCustomerCode, Collectors.toMap(CustomerGspAudit::getUseOrgNo, Function.identity())));


            // 没有分派记录的新增组织分派记录
            List<CustomerBase> addCustomerBaseList = new ArrayList<>();

            // 全量比较后删除不存在的组织分派记录
//            List<CustomerBase> deleteCustomerBaseList = new ArrayList<>();

            // 兼容逻辑，防止旧数据没有业务信息，如果管理组织有业务数据则拷贝管理组织的业务信息，如果管理组织没有业务信息，则插入空白的业务信息
            List<CustomerBiz> addCustomerBizList = new ArrayList<>();

            // 如果使用组织没有修改过业务信息，则拷贝管理组织的业务信息
//            List<CustomerBiz> updateCustomerBizList = new ArrayList<>();

            // 首营信息
            List<CustomerGspAudit> addCustomerGspAuditList = new ArrayList<>();


            Map<String, String> codeOrg2No = new HashMap<>();

            List<CustomerAssignExeResponse> resultList = new ArrayList<>(req.getDocList().size());
            for (CustomerAssignDocExeRequest customerAssignDocExeReq : req.getDocList()) {
                if (!code2CustomerMap.containsKey(customerAssignDocExeReq.getCustomerCode())) {
                    if (CollectionUtils.isNotEmpty(customerAssignDocExeReq.getUseOrgNoList())) {
                        for (String useOrgNo : customerAssignDocExeReq.getUseOrgNoList()) {
                            resultList.add(packAssignResp(useOrgNo, "客户档案不存在", false, customerAssignDocExeReq.getCustomerCode()));
                        }
                    }

                    continue;
                }

                if (CollectionUtils.isNotEmpty(customerAssignDocExeReq.getUseOrgNoList())) {
//                    if (!MapUtils.isEmpty(code2usrOrgNo2Base.get(customerAssignDocExeReq.getCustomerCode()))) {
//                        Set<String> existsUseOrgNoSet = new HashSet<>(code2usrOrgNo2Base.get(customerAssignDocExeReq.getCustomerCode()).keySet());
//                        existsUseOrgNoSet.remove(req.getManageOrgNo());
//
//                        List<String> toDeleteUserOrgNo = existsUseOrgNoSet.stream().filter(useOrgNo -> !customerAssignDocExeReq.getUseOrgNoList().contains(useOrgNo)).collect(Collectors.toList());
//                        if (CollectionUtils.isNotEmpty(toDeleteUserOrgNo)) {
//                            for (String useOrgNo : toDeleteUserOrgNo) {
//                                deleteCustomerBaseList.add(code2usrOrgNo2Base.get(customerAssignDocExeReq.getCustomerCode()).get(useOrgNo));
//                            }
//                        }
//                    }

                    for (String useOrgNo : customerAssignDocExeReq.getUseOrgNoList()) {
                        String key = customerAssignDocExeReq.getCustomerCode() + "-" + useOrgNo;

                        if (code2usrOrgNo2Base.containsKey(customerAssignDocExeReq.getCustomerCode()) && code2usrOrgNo2Base.get(customerAssignDocExeReq.getCustomerCode()).containsKey(useOrgNo)) {
                            if (!codeOrg2No.containsKey(key)) {
                                CustomerBase customerBase = code2usrOrgNo2Base.get(customerAssignDocExeReq.getCustomerCode()).get(useOrgNo);
                                codeOrg2No.put(key, customerBase.getCustomerNo());
                            }

                            resultList.add(packAssignResp(useOrgNo, "客户档案已分派", true, customerAssignDocExeReq.getCustomerCode()));
                        } else {
                            CustomerBase customerBase = new CustomerBase();
                            customerBase.setEnterpriseNo(req.getEnterpriseNo());

//                            if (code2CustomerMap.containsKey(customerAssignDocExeReq.getCustomerCode())) {
//                                customerBase.setCustomerNo(code2CustomerMap.get(customerAssignDocExeReq.getCustomerCode()).getCustomerNo());
//                            }

                            if (!codeOrg2No.containsKey(key)) {
                                codeOrg2No.put(key, numberCenterService.createNumber(NumberCenterConstant.CUSTOMER_NO_KEY));
                            }

                            customerBase.setCustomerNo(codeOrg2No.get(key));

                            customerBase.setCustomerCode(customerAssignDocExeReq.getCustomerCode());
                            customerBase.setManageOrgNo(req.getManageOrgNo());
                            customerBase.setUseOrgNo(useOrgNo);
                            customerBase.setDeleted(DeletedEnum.UN_DELETE.getValue());
                            addCustomerBaseList.add(customerBase);

                            resultList.add(packAssignResp(useOrgNo, "", true, customerAssignDocExeReq.getCustomerCode()));
                        }

                        if (code2usrOrgNo2Biz.containsKey(customerAssignDocExeReq.getCustomerCode()) && code2usrOrgNo2Biz.get(customerAssignDocExeReq.getCustomerCode()).containsKey(useOrgNo)) {
//                            CustomerBiz oldCustomerBiz = code2usrOrgNo2Biz.get(customerAssignDocExeReq.getCustomerCode()).get(useOrgNo);
//                            if (CommonIfEnum.NO.getValue() == oldCustomerBiz.getUseOrgModifyFlag()) {
//                                CustomerBiz manageCustomerBiz = code2usrOrgNo2Biz.get(customerAssignDocExeReq.getCustomerCode()).get(req.getManageOrgNo());
//
//                                CustomerBiz newCustomerBiz = BeanUtil.copyFields(manageCustomerBiz, CustomerBiz.class);
//                                newCustomerBiz.setId(oldCustomerBiz.getId());
//
//                                if (!codeOrg2No.containsKey(key)) {
//                                    codeOrg2No.put(key, numberCenterService.createNumber(NumberCenterConstant.CUSTOMER_NO_KEY));
//                                }
//
//                                newCustomerBiz.setCustomerNo(codeOrg2No.get(key));
//                                newCustomerBiz.setUseOrgNo(oldCustomerBiz.getUseOrgNo());
//                                newCustomerBiz.setUseOrgModifyFlag(CommonIfEnum.NO.getValue());
//
//                                updateCustomerBizList.add(newCustomerBiz);
//                            }
                        } else {
                            CustomerBiz manageCustomerBiz = code2usrOrgNo2Biz.get(customerAssignDocExeReq.getCustomerCode()).get(req.getManageOrgNo());
                            CustomerBiz customerBiz = BeanUtil.copyFields(manageCustomerBiz, CustomerBiz.class);
                            customerBiz.setId(null);

                            if (!codeOrg2No.containsKey(key)) {
                                codeOrg2No.put(key, numberCenterService.createNumber(NumberCenterConstant.CUSTOMER_NO_KEY));
                            }

                            customerBiz.setCustomerNo(codeOrg2No.get(key));
                            customerBiz.setUseOrgNo(useOrgNo);
                            customerBiz.setUseOrgModifyFlag(CommonIfEnum.NO.getValue());

                            addCustomerBizList.add(customerBiz);
                        }


                        if (code2usrOrgNo2Gsp.containsKey(customerAssignDocExeReq.getCustomerCode()) && !code2usrOrgNo2Gsp.get(customerAssignDocExeReq.getCustomerCode()).containsKey(useOrgNo)) {
                            CustomerGspAudit customerGspAudit = new CustomerGspAudit();
                            customerGspAudit.setEnterpriseNo(req.getEnterpriseNo());
                            customerGspAudit.setUseOrgNo(useOrgNo);
                            customerGspAudit.setCustomerNo(codeOrg2No.get(key));
                            customerGspAudit.setCustomerCode(customerAssignDocExeReq.getCustomerCode());
                            customerGspAudit.setGspAuditStatus(GspAuditStatusEnum.NON_FIRST_BUSINESS.getType());

                            addCustomerGspAuditList.add(customerGspAudit);
                        }
                    }
                }
            }

            if (CollectionUtils.isNotEmpty(addCustomerBaseList)) {
                customerBaseDAO.addBatch(addCustomerBaseList);
            }

//            if (CollectionUtils.isNotEmpty(deleteCustomerBaseList)) {
//                customerBaseDAO.updateByIdBatch(deleteCustomerBaseList);
//            }

            if (CollectionUtils.isNotEmpty(addCustomerBizList)) {
                customerBizDAO.addBatch(addCustomerBizList);
            }

//            if (CollectionUtils.isNotEmpty(updateCustomerBizList)) {
//                customerBizDAO.updateByIdBatch(updateCustomerBizList);
//            }

            if (CollectionUtils.isNotEmpty(addCustomerGspAuditList)) {
                customerGspAuditDAO.addBatch(addCustomerGspAuditList);
            }

            return resultList;
        } finally {
            if (lockKey != null) {
                redisClientUtil.unlock(lockKey);
            }
        }
    }

    @Override
    public List<CustomerAssignExeResponse> assignMdmCustomer(CustomerAssignMdmExeRequest req) {
        if (null == req || CollectionUtils.isEmpty(req.getDocList())) {
            return Collections.emptyList();
        }

        String lockKey = null;
        try {
            lockKey = String.join(DistributedLockConstant.DELIMITER, DistributedLockConstant.CUSTOMER_ASSIGN_LOCK_KEY, req.getEnterpriseNo(), req.getManageOrgNo());
            if (!redisClientUtil.lock(lockKey, DistributedLockConstant.DEFAULT_WAIT_TIME, DistributedLockConstant.DEFAULT_LEASE_TIME)) {
                throw new BusinessException(ErrorCode.param_invalid_code, "当前组织正在分派，稍后重试");
            }

            List<String> customerCodeList = req.getDocList().stream().map(CustomerAssignDocExeRequest::getCustomerCode).collect(Collectors.toList());
            List<CustomerV2> customerList = customerV2DAO.selectList(Wrappers.<CustomerV2>lambdaQuery()
                    .eq(CustomerV2::getEnterpriseNo, req.getEnterpriseNo())
                    .eq(CustomerV2::getManageOrgNo, req.getManageOrgNo())
                    .in(CustomerV2::getCustomerCode, customerCodeList)
                    .eq(CustomerV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
            Map<String, CustomerV2> code2CustomerMap = customerList.stream().collect(Collectors.toMap(CustomerV2::getCustomerCode, Function.identity()));

            List<CustomerBiz> customerBizList = customerBizDAO.selectList(Wrappers.<CustomerBiz>lambdaQuery()
                    .eq(CustomerBiz::getEnterpriseNo, req.getEnterpriseNo())
                    .in(CustomerBiz::getCustomerCode, customerCodeList)
                    .eq(CustomerBiz::getDeleted, DeletedEnum.UN_DELETE.getValue()));
            Map<String, Map<String, CustomerBiz>> code2usrOrgNo2Biz = customerBizList.stream().collect(Collectors.groupingBy(CustomerBiz::getCustomerCode, Collectors.toMap(CustomerBiz::getUseOrgNo, Function.identity())));

            List<CustomerBase> customerBaseList = customerBaseDAO.selectList(Wrappers.<CustomerBase>lambdaQuery()
                    .eq(CustomerBase::getEnterpriseNo, req.getEnterpriseNo())
                    .in(CustomerBase::getCustomerCode, customerCodeList)
                    .eq(CustomerBase::getManageOrgNo, req.getManageOrgNo())
                    .eq(CustomerBase::getDeleted, DeletedEnum.UN_DELETE.getValue()));
            Map<String, Map<String, CustomerBase>> code2usrOrgNo2Base = customerBaseList.stream().collect(Collectors.groupingBy(CustomerBase::getCustomerCode, Collectors.toMap(CustomerBase::getUseOrgNo, Function.identity())));

            List<CustomerGspAudit> customerGspAuditList = customerGspAuditDAO.selectList(Wrappers.<CustomerGspAudit>lambdaQuery()
                    .eq(CustomerGspAudit::getEnterpriseNo, req.getEnterpriseNo())
                    .in(CustomerGspAudit::getCustomerCode, customerCodeList)
                    .eq(CustomerGspAudit::getDeleted, DeletedEnum.UN_DELETE.getValue()));
            Map<String, Map<String, CustomerGspAudit>> code2usrOrgNo2Gsp = customerGspAuditList.stream().collect(Collectors.groupingBy(CustomerGspAudit::getCustomerCode, Collectors.toMap(CustomerGspAudit::getUseOrgNo, Function.identity())));


            // 没有分派记录的新增组织分派记录
            List<CustomerBase> addCustomerBaseList = new ArrayList<>();

            // 全量比较后删除不存在的组织分派记录
//            List<CustomerBase> deleteCustomerBaseList = new ArrayList<>();

            // 兼容逻辑，防止旧数据没有业务信息，如果管理组织有业务数据则拷贝管理组织的业务信息，如果管理组织没有业务信息，则插入空白的业务信息
            List<CustomerBiz> addCustomerBizList = new ArrayList<>();

            // 如果使用组织没有修改过业务信息，则拷贝管理组织的业务信息
//            List<CustomerBiz> updateCustomerBizList = new ArrayList<>();

            // 首营信息
            List<CustomerGspAudit> addCustomerGspAuditList = new ArrayList<>();


            Map<String, String> codeOrg2No = new HashMap<>();

            List<CustomerAssignExeResponse> resultList = new ArrayList<>(req.getDocList().size());
            for (CustomerAssignDocExeRequest customerAssignDocExeReq : req.getDocList()) {
                if (!code2CustomerMap.containsKey(customerAssignDocExeReq.getCustomerCode())) {
                    if (CollectionUtils.isNotEmpty(customerAssignDocExeReq.getUseOrgNoList())) {
                        for (String useOrgNo : customerAssignDocExeReq.getUseOrgNoList()) {
                            resultList.add(packAssignResp(useOrgNo, "客户档案不存在", false, customerAssignDocExeReq.getCustomerCode()));
                        }
                    }

                    continue;
                }

                if (CollectionUtils.isNotEmpty(customerAssignDocExeReq.getUseOrgNoList())) {
//                    if (!MapUtils.isEmpty(code2usrOrgNo2Base.get(customerAssignDocExeReq.getCustomerCode()))) {
//                        Set<String> existsUseOrgNoSet = new HashSet<>(code2usrOrgNo2Base.get(customerAssignDocExeReq.getCustomerCode()).keySet());
//                        existsUseOrgNoSet.remove(req.getManageOrgNo());
//
//                        List<String> toDeleteUserOrgNo = existsUseOrgNoSet.stream().filter(useOrgNo -> !customerAssignDocExeReq.getUseOrgNoList().contains(useOrgNo)).collect(Collectors.toList());
//                        if (CollectionUtils.isNotEmpty(toDeleteUserOrgNo)) {
//                            for (String useOrgNo : toDeleteUserOrgNo) {
//                                deleteCustomerBaseList.add(code2usrOrgNo2Base.get(customerAssignDocExeReq.getCustomerCode()).get(useOrgNo));
//                            }
//                        }
//                    }

                    for (String useOrgNo : customerAssignDocExeReq.getUseOrgNoList()) {
                        String key = customerAssignDocExeReq.getCustomerCode() + "-" + useOrgNo;

                        if (code2usrOrgNo2Base.containsKey(customerAssignDocExeReq.getCustomerCode()) && code2usrOrgNo2Base.get(customerAssignDocExeReq.getCustomerCode()).containsKey(useOrgNo)) {
                            if (!codeOrg2No.containsKey(key)) {
                                CustomerBase customerBase = code2usrOrgNo2Base.get(customerAssignDocExeReq.getCustomerCode()).get(useOrgNo);
                                codeOrg2No.put(key, customerBase.getCustomerNo());
                            }

                            resultList.add(packAssignResp(useOrgNo, "客户档案已分派", true, customerAssignDocExeReq.getCustomerCode()));
                        } else {
                            CustomerBase customerBase = new CustomerBase();
                            customerBase.setEnterpriseNo(req.getEnterpriseNo());

//                            if (code2CustomerMap.containsKey(customerAssignDocExeReq.getCustomerCode())) {
//                                customerBase.setCustomerNo(code2CustomerMap.get(customerAssignDocExeReq.getCustomerCode()).getCustomerNo());
//                            }

                            if (!codeOrg2No.containsKey(key)) {
                                codeOrg2No.put(key, req.getOrgNo2CustomerNoMap().get(useOrgNo));
                            }

                            customerBase.setCustomerNo(codeOrg2No.get(key));

                            customerBase.setCustomerCode(customerAssignDocExeReq.getCustomerCode());
                            customerBase.setManageOrgNo(req.getManageOrgNo());
                            customerBase.setUseOrgNo(useOrgNo);
                            customerBase.setDeleted(DeletedEnum.UN_DELETE.getValue());
                            addCustomerBaseList.add(customerBase);

                            resultList.add(packAssignResp(useOrgNo, "", true, customerAssignDocExeReq.getCustomerCode()));
                        }

                        if (code2usrOrgNo2Biz.containsKey(customerAssignDocExeReq.getCustomerCode()) && code2usrOrgNo2Biz.get(customerAssignDocExeReq.getCustomerCode()).containsKey(useOrgNo)) {
//                            CustomerBiz oldCustomerBiz = code2usrOrgNo2Biz.get(customerAssignDocExeReq.getCustomerCode()).get(useOrgNo);
//                            if (CommonIfEnum.NO.getValue() == oldCustomerBiz.getUseOrgModifyFlag()) {
//                                CustomerBiz manageCustomerBiz = code2usrOrgNo2Biz.get(customerAssignDocExeReq.getCustomerCode()).get(req.getManageOrgNo());
//
//                                CustomerBiz newCustomerBiz = BeanUtil.copyFields(manageCustomerBiz, CustomerBiz.class);
//                                newCustomerBiz.setId(oldCustomerBiz.getId());
//
//                                if (!codeOrg2No.containsKey(key)) {
//                                    codeOrg2No.put(key, numberCenterService.createNumber(NumberCenterConstant.CUSTOMER_NO_KEY));
//                                }
//
//                                newCustomerBiz.setCustomerNo(codeOrg2No.get(key));
//                                newCustomerBiz.setUseOrgNo(oldCustomerBiz.getUseOrgNo());
//                                newCustomerBiz.setUseOrgModifyFlag(CommonIfEnum.NO.getValue());
//
//                                updateCustomerBizList.add(newCustomerBiz);
//                            }
                        } else {
                            CustomerBiz manageCustomerBiz = code2usrOrgNo2Biz.get(customerAssignDocExeReq.getCustomerCode()).get(req.getManageOrgNo());
                            CustomerBiz customerBiz = BeanUtil.copyFields(manageCustomerBiz, CustomerBiz.class);
                            customerBiz.setId(null);

                            if (!codeOrg2No.containsKey(key)) {
                                codeOrg2No.put(key, numberCenterService.createNumber(NumberCenterConstant.CUSTOMER_NO_KEY));
                            }

                            customerBiz.setCustomerNo(codeOrg2No.get(key));
                            customerBiz.setUseOrgNo(useOrgNo);
                            customerBiz.setUseOrgModifyFlag(CommonIfEnum.NO.getValue());

                            addCustomerBizList.add(customerBiz);
                        }


                        if (code2usrOrgNo2Gsp.containsKey(customerAssignDocExeReq.getCustomerCode()) && !code2usrOrgNo2Gsp.get(customerAssignDocExeReq.getCustomerCode()).containsKey(useOrgNo)) {
                            CustomerGspAudit customerGspAudit = new CustomerGspAudit();
                            customerGspAudit.setEnterpriseNo(req.getEnterpriseNo());
                            customerGspAudit.setUseOrgNo(useOrgNo);
                            customerGspAudit.setCustomerNo(codeOrg2No.get(key));
                            customerGspAudit.setCustomerCode(customerAssignDocExeReq.getCustomerCode());
                            customerGspAudit.setGspAuditStatus(GspAuditStatusEnum.NON_FIRST_BUSINESS.getType());

                            addCustomerGspAuditList.add(customerGspAudit);
                        }
                    }
                }
            }

            if (CollectionUtils.isNotEmpty(addCustomerBaseList)) {
                customerBaseDAO.addBatch(addCustomerBaseList);
            }

//            if (CollectionUtils.isNotEmpty(deleteCustomerBaseList)) {
//                customerBaseDAO.updateByIdBatch(deleteCustomerBaseList);
//            }

            if (CollectionUtils.isNotEmpty(addCustomerBizList)) {
                customerBizDAO.addBatch(addCustomerBizList);
            }

//            if (CollectionUtils.isNotEmpty(updateCustomerBizList)) {
//                customerBizDAO.updateByIdBatch(updateCustomerBizList);
//            }

            if (CollectionUtils.isNotEmpty(addCustomerGspAuditList)) {
                customerGspAuditDAO.addBatch(addCustomerGspAuditList);
            }

            return resultList;
        } finally {
            if (lockKey != null) {
                redisClientUtil.unlock(lockKey);
            }
        }
    }

    @Override
    @Transactional
    public List<CustomerAssignExeResponse> deAssignCustomer(CustomerAssignExeRequest req) {
        if (null == req || CollectionUtils.isEmpty(req.getDocList())) {
            return Collections.emptyList();
        }

        String lockKey = null;
        try {
            lockKey = String.join(DistributedLockConstant.DELIMITER, DistributedLockConstant.CUSTOMER_ASSIGN_LOCK_KEY, req.getEnterpriseNo(), req.getManageOrgNo());
            if (!redisClientUtil.lock(lockKey, DistributedLockConstant.DEFAULT_WAIT_TIME, DistributedLockConstant.DEFAULT_LEASE_TIME)) {
                throw new BusinessException(ErrorCode.param_invalid_code, "当前组织正在分派，稍后重试");
            }

            List<String> customerCodeList = req.getDocList().stream().map(CustomerAssignDocExeRequest::getCustomerCode).collect(Collectors.toList());
            List<CustomerV2> customerList = customerV2DAO.selectList(Wrappers.<CustomerV2>lambdaQuery()
                    .eq(CustomerV2::getEnterpriseNo, req.getEnterpriseNo())
                    .eq(CustomerV2::getManageOrgNo, req.getManageOrgNo())
                    .in(CustomerV2::getCustomerCode, customerCodeList)
                    .eq(CustomerV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
            Map<String, CustomerV2> code2CustomerMap = customerList.stream().collect(Collectors.toMap(CustomerV2::getCustomerCode, Function.identity()));

            List<CustomerBase> customerBaseList = customerBaseDAO.selectList(Wrappers.<CustomerBase>lambdaQuery()
                    .eq(CustomerBase::getEnterpriseNo, req.getEnterpriseNo())
                    .in(CustomerBase::getCustomerCode, customerCodeList)
                    .eq(CustomerBase::getManageOrgNo, req.getManageOrgNo())
                    .eq(CustomerBase::getDeleted, DeletedEnum.UN_DELETE.getValue()));
            Map<String, Map<String, CustomerBase>> code2usrOrgNo2Base = customerBaseList.stream().collect(Collectors.groupingBy(CustomerBase::getCustomerCode, Collectors.toMap(CustomerBase::getUseOrgNo, Function.identity())));

            List<CustomerBase> deleteCustomerBaseList = new ArrayList<>();

            List<CustomerAssignExeResponse> resultList = new ArrayList<>(req.getDocList().size());

            for (CustomerAssignDocExeRequest customerAssignDocExeReq : req.getDocList()) {
                if (!code2CustomerMap.containsKey(customerAssignDocExeReq.getCustomerCode())) {
                    if (CollectionUtils.isNotEmpty(customerAssignDocExeReq.getUseOrgNoList())) {
                        for (String useOrgNo : customerAssignDocExeReq.getUseOrgNoList()) {
                            resultList.add(packAssignResp(useOrgNo, "客户档案不存在", false, customerAssignDocExeReq.getCustomerCode()));
                        }
                    }

                    continue;
                }

                if (CollectionUtils.isNotEmpty(customerAssignDocExeReq.getUseOrgNoList())) {
                    if (!MapUtils.isEmpty(code2usrOrgNo2Base.get(customerAssignDocExeReq.getCustomerCode()))) {
                        Set<String> existsUseOrgNoSet = new HashSet<>(code2usrOrgNo2Base.get(customerAssignDocExeReq.getCustomerCode()).keySet());
                        existsUseOrgNoSet.remove(req.getManageOrgNo());

                        for (String useOrgNo : customerAssignDocExeReq.getUseOrgNoList()) {
                            if (!existsUseOrgNoSet.contains(useOrgNo)) {
                                resultList.add(packAssignResp(useOrgNo, "不存在分派关系", false, customerAssignDocExeReq.getCustomerCode()));
                                continue;
                            }

                            CustomerBase customerBase = code2usrOrgNo2Base.get(customerAssignDocExeReq.getCustomerCode()).get(useOrgNo);
                            customerBase.setDeleted(DeletedEnum.DELETED.getValue());

                            deleteCustomerBaseList.add(customerBase);

                            resultList.add(packAssignResp(useOrgNo, "", true, customerAssignDocExeReq.getCustomerCode()));
                        }
                    }
                }
            }

            if (CollectionUtils.isNotEmpty(deleteCustomerBaseList)) {
                customerBaseDAO.updateByIdBatch(deleteCustomerBaseList);
            }

            return resultList;
        } finally {
            if (lockKey != null) {
                redisClientUtil.unlock(lockKey);
            }
        }
    }

    @Override
    public PageVo<CustomerPageVO> queryPageCustomer(OperationModel operationModel, CustomerPageQueryReq queryReq, PageDto pageDTO) {
        ValidatorUtils.checkEmptyThrowEx(queryReq, "查询条件不能为空");

        Page<CustomerV2WithBiz> page = PageHelper.startPage(pageDTO.getPageIndex(), pageDTO.getPageSize(), StringUtils.isBlank(pageDTO.getOrderBy()) ? "b.create_time desc" : pageDTO.getOrderBy());
        if (CollectionUtils.isEmpty(queryReq.getBusinessFlags())) {
            queryReq.setBusinessFlags(Collections.singletonList(CustomerBusinessFlagEnum.FORMAL.getValue()));
        }

        customerV2DAO.queryPageCustomer(queryReq);

        return PageUtils.convertPageVo(page, data -> completeSupplementPageVO(operationModel.getEnterpriseNo(), data));
    }

    @Override
    public PageVo<CustomerPageVO> querySpecifyOrgPageCustomer(OperationModel operationModel, CustomerPageQueryBySpecifyOrgReq params, PageDto pageDto) {
        ValidatorUtils.checkEmptyThrowEx(params.getOrgNo(), "指定组织不能为空");

        //获取有权限的组织列表
        List<OrganizationVo> organizationList = uimTenantService.getAuthEnterpriseList(operationModel.getEnterpriseNo(), operationModel.getUserNo(), operationModel.getEmployerNo());
        final Optional<OrganizationVo> optional = organizationList.stream().filter(t -> params.getOrgNo().equals(t.getOrgNo())).findAny();
        if (!optional.isPresent()) {
            return new PageVo<>();
        }

        Page<CustomerV2WithBiz> page = PageHelper.startPage(pageDto.getPageIndex(), pageDto.getPageSize(), StringUtils.isBlank(pageDto.getOrderBy()) ? "b.create_time desc" : pageDto.getOrderBy());
        params.setEnterpriseNo(operationModel.getEnterpriseNo());
        params.setUseOrgNo(params.getOrgNo());
        customerV2DAO.querySpecifyOrgPageCustomer(params);

        return PageUtils.convertPageVo(page, data -> completeSupplementPageVO(operationModel.getEnterpriseNo(), data));
    }

    @Override
    public PageVo<CustomerFormalPageVO> findListPageByFormal(OperationModel operationModel, CustomerPageByFormalQueryReq queryReq, PageDto pageDTO) {
        ValidatorUtils.checkEmptyThrowEx(queryReq, "查询条件不能为空");

        Page<CustomerV2WithBiz> page = PageHelper.startPage(pageDTO.getPageIndex(), pageDTO.getPageSize(), StringUtils.isBlank(pageDTO.getOrderBy()) ? "b.create_time desc" : pageDTO.getOrderBy());
        if (CollectionUtils.isEmpty(queryReq.getBusinessFlags())) {
            queryReq.setBusinessFlags(Collections.singletonList(CustomerBusinessFlagEnum.FORMAL.getValue()));
        }

        customerV2DAO.findListPageByFormal(queryReq);

        return PageUtils.convertPageVo(page, data -> completeSupplementFormalPageVO(operationModel.getEnterpriseNo(), data));

    }

    @Override
    public PageVo<CustomerPageVO> findNotInPageList(OperationModel operationModel, CustomerNotInPageListQueryReq queryReq, PageDto pageDTO) {
        ValidatorUtils.checkEmptyThrowEx(queryReq, "查询条件不能为空");

        Page<CustomerV2WithBiz> page = PageHelper.startPage(pageDTO.getPageIndex(), pageDTO.getPageSize(), StringUtils.isBlank(pageDTO.getOrderBy()) ? "b.create_time desc" : pageDTO.getOrderBy());
        if (CollectionUtils.isEmpty(queryReq.getBusinessFlags())) {
            queryReq.setBusinessFlags(Collections.singletonList(CustomerBusinessFlagEnum.FORMAL.getValue()));
        }

        customerV2DAO.findNotInPageList(queryReq);

        return PageUtils.convertPageVo(page, data -> completeSupplementPageVO(operationModel.getEnterpriseNo(), data));

    }

    @Override
    @Transactional
    public String manageSaveCustomerBasicAndBiz(OperationModel operationModel, CustomerSaveBasicAndBizReq params) {
        ValidatorUtils.checkEmptyThrowEx(params, "客户信息不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getManageOrgNo(), "管理组织不能为空");

        // 多组织时才需要校验档案管理权，目前集团和子租户都属于多组织架构，否则不需要校验；uap统一处理
        List<String> manageNoList = gradeControlService.listMgrOrgNos(operationModel.getEnterpriseNo(), BillNameConstant.BDC_CUSTOMER_BILL);
        ValidatorUtils.checkEmptyThrowEx(manageNoList, "无档案管理权");
        ValidatorUtils.checkTrueThrowEx(!manageNoList.contains(params.getManageOrgNo()), "无档案管理权");

        // 校验入参必填、范围
        validateSaveBasicAndBizReq(operationModel, params);


        ValidatorUtils.checkTrueThrowEx(!checkOnlyCode(operationModel, null, params.getCustomerCode()), "客户编码重复");
        ValidatorUtils.checkTrueThrowEx(!checkOnlyName(operationModel, null, params.getCustomerName()), "客户名称重复");

        if (CommonIfEnum.YES.getValue().equals(params.getIsAssociatedEnterprise())) {
            List<CustomerV2> byInternalOrgNo = findByInternalOrgNo(operationModel, null, params.getAssociatedOrgNo());
            if (CollectionUtils.isNotEmpty(byInternalOrgNo)) {
                String errorMessage = String.format("对应内部组织已被客户【%s】关联", String.join(",", byInternalOrgNo.stream().map(CustomerV2::getCustomerName).collect(Collectors.toSet())));
                throw new BusinessException(ErrorCode.param_invalid_msg, errorMessage);
            }
        }

        // 如果企业不存在，则创建企业
        CompanySaveOrUpdateReq companySaveOrUpdateReq = new CompanySaveOrUpdateReq();
        BeanUtils.copyProperties(params, companySaveOrUpdateReq);
        CompanyV2 company = companyV2Service.saveOrUpdateCompany(operationModel, params.getCustomerCode(), companySaveOrUpdateReq, CompanyPartnershipEnum.CUSTOMER);

        // 客户基本信息
        CustomerV2 customer = new CustomerV2();
        String customerNo = numberCenterService.createNumber(NumberCenterConstant.CUSTOMER_NO_KEY);
        customer.setEnterpriseNo(operationModel.getEnterpriseNo());
        customer.setManageOrgNo(params.getManageOrgNo());
        customer.setCompanyNo(company.getCompanyNo());
        customer.setUnifiedSocialCode(company.getUnifiedSocialCode());
        customer.setCustomerNo(customerNo);
        customer.setCustomerCode(params.getCustomerCode());
        customer.setCustomerName(params.getCustomerName());
        customer.setCustomerCategoryNo(params.getCustomerCategoryNo());
        customer.setTransactionType(params.getTransactionType());

        customer.setIsGspControl(params.getIsGspControl());

        customer.setIsAssociatedEnterprise(params.getIsAssociatedEnterprise());
        customer.setAssociatedOrgNo(params.getAssociatedOrgNo());
        customer.setAssociatedOrgCode(params.getAssociatedOrgCode());
        customer.setAssociatedOrgName(params.getAssociatedOrgName());

        customer.setMnemonicCode(params.getMnemonicCode());
        customer.setCustomerNameEn(params.getCustomerNameEn());
        customer.setBusinessFlag(CustomerBusinessFlagEnum.DRAFT.getValue().equals(params.getBusinessFlag()) ? CustomerBusinessFlagEnum.DRAFT.getValue() : CustomerBusinessFlagEnum.FORMAL.getValue());
        customer.setDeleted(DeletedEnum.UN_DELETE.getValue());
        customer.setRemark(params.getRemark());
        customer.setRetailInvestors(params.getRetailInvestors());

        CommonUtil.fillCreatInfo(operationModel, customer);

        customerV2DAO.insert(customer);

        CustomerBiz customerBiz = null;
        //业务信息
        customerBiz = new CustomerBiz();
        customerBiz.setEnterpriseNo(operationModel.getEnterpriseNo());
        customerBiz.setCustomerCode(params.getCustomerCode());
        customerBiz.setUseOrgNo(params.getManageOrgNo());
        customerBiz.setCustomerNo(customerNo);
//        customerBiz.setControlId(params.getControlId());
//        customerBiz.setControlTypeName(params.getControlTypeName());
        customerBiz.setCooperationMode(params.getCooperationMode());
        customerBiz.setCurrencyId(params.getCurrencyId());
        customerBiz.setSettlementModes(params.getSettlementModes());
        customerBiz.setCreditAmount(params.getCreditAmount());
        customerBiz.setCreditDates(params.getCreditDates());
        customerBiz.setCoopStartTime(params.getCoopStartTime());
        customerBiz.setCoopEndTime(params.getCoopEndTime());
        customerBiz.setPriceCategoryCode(params.getPriceCategoryCode());
        customerBiz.setBusinessType(params.getBusinessType());
        customerBiz.setReceiveAgreement(params.getReceiveAgreement());
        customerBiz.setReceiveCondition(params.getReceiveCondition());
        customerBiz.setOwnerCompany(params.getOwnerCompany());
        customerBiz.setDeleted(DeletedEnum.UN_DELETE.getValue());

        CommonUtil.fillCreatInfo(operationModel, customerBiz);
        customerBizDAO.insert(customerBiz);

        // 新增管理组织的分派记录
        CustomerBase customerBase = new CustomerBase();
        customerBase.setEnterpriseNo(operationModel.getEnterpriseNo());
        customerBase.setCustomerNo(customerNo);
        customerBase.setCustomerCode(params.getCustomerCode());
        customerBase.setManageOrgNo(params.getManageOrgNo());
        customerBase.setUseOrgNo(params.getManageOrgNo());
        customerBase.setDeleted(DeletedEnum.UN_DELETE.getValue());
        CommonUtil.fillCreatInfo(operationModel, customerBase);
        CommonUtil.fillOperateInfo(operationModel, customerBase);
        customerBaseDAO.insert(customerBase);

        CustomerGspAudit customerGspAudit = new CustomerGspAudit();
        customerGspAudit.setEnterpriseNo(operationModel.getEnterpriseNo());
        customerGspAudit.setUseOrgNo(customer.getManageOrgNo());
        customerGspAudit.setCustomerNo(customer.getCustomerNo());
        customerGspAudit.setCustomerCode(customer.getCustomerCode());
        customerGspAudit.setGspAuditStatus(GspAuditStatusEnum.NON_FIRST_BUSINESS.getType());
        customerGspAuditService.save(customerGspAudit);

        Map<String, Object> businessValue = new HashMap<>();
        businessValue.put("customer", customer);
        businessValue.put("customerBase", customerBase);
        businessValue.put("customerBiz", customerBiz);
        businessValue.put("customerGspAudit", customerGspAudit);

        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                try {
                    businessLogService.saveLog(DLogLevel.INFO.name(), ViewNameConstant.BDC_CUSTOMER_VIEW, params.getCustomerCode() + params.getManageOrgNo(), "新增客户档案", "新增客户档案", JSON.toJSONString(businessValue), "");
                } catch (Exception e) {
                    log.error("新增客户档案日志保存失败", e);
                }
            }
        });

        return params.getCustomerCode();
    }

    @Override
    public String preValidateManageSaveCustomerBasicAndBiz(OperationModel operationModel, CustomerSaveBasicAndBizReq params) {
        ValidatorUtils.checkEmptyThrowEx(params, "客户信息不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getManageOrgNo(), "管理组织不能为空");

        // 多组织时才需要校验档案管理权，目前集团和子租户都属于多组织架构，否则不需要校验；uap统一处理
        List<String> manageNoList = gradeControlService.listMgrOrgNos(operationModel.getEnterpriseNo(), BillNameConstant.BDC_CUSTOMER_BILL);
        ValidatorUtils.checkEmptyThrowEx(manageNoList, "无档案管理权");
        ValidatorUtils.checkTrueThrowEx(!manageNoList.contains(params.getManageOrgNo()), "无档案管理权");

        // 校验入参必填、范围
        validateSaveBasicAndBizReq(operationModel, params);


        ValidatorUtils.checkTrueThrowEx(!checkOnlyCode(operationModel, null, params.getCustomerCode()), "客户编码重复");
        ValidatorUtils.checkTrueThrowEx(!checkOnlyName(operationModel, null, params.getCustomerName()), "客户名称重复");

        if (CommonIfEnum.YES.getValue().equals(params.getIsAssociatedEnterprise())) {
            List<CustomerV2> byInternalOrgNo = findByInternalOrgNo(operationModel, null, params.getAssociatedOrgNo());
            if (CollectionUtils.isNotEmpty(byInternalOrgNo)) {
                String errorMessage = String.format("对应内部组织已被客户【%s】关联", String.join(",", byInternalOrgNo.stream().map(CustomerV2::getCustomerName).collect(Collectors.toSet())));
                throw new BusinessException(ErrorCode.param_invalid_msg, errorMessage);
            }
        }

        // 如果企业不存在，则创建企业
        CompanySaveOrUpdateReq companySaveOrUpdateReq = new CompanySaveOrUpdateReq();
        BeanUtils.copyProperties(params, companySaveOrUpdateReq);
        return companyV2Service.preValidateSaveOrUpdateCompany(operationModel, null, companySaveOrUpdateReq, CompanyPartnershipEnum.CUSTOMER);
    }

    @Override
    @Transactional
    public Boolean editCustomerBasicAndBiz(OperationModel operationModel, CustomerEditBasicAndBizReq params) {
        //校验入参必填、范围
        validateEditBasicAndBizReq(operationModel, params);

        LambdaQueryWrapper<CustomerV2> queryWrapper = Wrappers.<CustomerV2>lambdaQuery()
                .eq(CustomerV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CustomerV2::getCustomerCode, params.getCustomerCode())
                .eq(CustomerV2::getDeleted, DeletedEnum.UN_DELETE.getValue());
        CustomerV2 customerV2 = customerV2DAO.selectOne(queryWrapper);
        ValidatorUtils.checkEmptyThrowEx(customerV2, "客户不存在");

        // 当前为管理组织，档案不为正式态，需要校验管理权开启与否
        if (customerV2.getManageOrgNo().equals(params.getUseOrgNo()) &&
                (!CustomerBusinessFlagEnum.FORMAL.getValue().equals(customerV2.getBusinessFlag()))) {
            List<String> manageNoList = gradeControlService.listMgrOrgNos(operationModel.getEnterpriseNo(), BillNameConstant.BDC_CUSTOMER_BILL);
            ValidatorUtils.checkEmptyThrowEx(manageNoList, "无档案管理权");
            ValidatorUtils.checkTrueThrowEx(!manageNoList.contains(params.getUseOrgNo()), "无档案管理权");
        }

        ValidatorUtils.checkTrueThrowEx(customerV2DAO.selectCount(new LambdaQueryWrapper<CustomerV2>()
                .eq(CustomerV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CustomerV2::getCustomerName, params.getCustomerName())
                .ne(CustomerV2::getCustomerCode, params.getCustomerCode())
                .eq(CustomerV2::getDeleted, DeletedEnum.UN_DELETE.getValue())) > 0, "客户名称重复");

        if (CommonIfEnum.YES.getValue().equals(params.getIsAssociatedEnterprise())) {
            List<CustomerV2> byInternalOrgNo = findByInternalOrgNo(operationModel, params.getCustomerCode(), params.getAssociatedOrgNo());
            if (CollectionUtils.isNotEmpty(byInternalOrgNo)) {
                String errorMessage = String.format("对应内部组织已被客户【%s】关联", String.join(",", byInternalOrgNo.stream().map(CustomerV2::getCustomerName).collect(Collectors.toSet())));
                throw new BusinessException(ErrorCode.param_invalid_msg, errorMessage);
            }
        }

        // 管理组织才能修改基本信息
        CompanyV2 company = null;
//        CustomerV2 newCustomer = null;
        if (customerV2.getManageOrgNo().equals(params.getUseOrgNo())) {
            // 如果企业不存在，则创建企业
            CompanySaveOrUpdateReq companySaveOrUpdateReq = new CompanySaveOrUpdateReq();
            BeanUtils.copyProperties(params, companySaveOrUpdateReq);
            company = companyV2Service.saveOrUpdateCompany(operationModel, params.getCustomerCode(), companySaveOrUpdateReq, CompanyPartnershipEnum.CUSTOMER);

            // 客户基本信息
            LambdaUpdateWrapper<CustomerV2> updateWrapper = Wrappers.lambdaUpdate(CustomerV2.class);
            updateWrapper.set(CustomerV2::getCustomerName, params.getCustomerName());
            updateWrapper.set(CustomerV2::getCustomerNameEn, params.getCustomerNameEn());
            updateWrapper.set(CustomerV2::getMnemonicCode, params.getMnemonicCode());
            updateWrapper.set(CustomerV2::getTransactionType, params.getTransactionType());
            updateWrapper.set(CustomerV2::getCustomerCategoryNo, params.getCustomerCategoryNo());

            updateWrapper.set(CustomerV2::getCompanyNo, company.getCompanyNo());
            updateWrapper.set(CustomerV2::getUnifiedSocialCode, company.getUnifiedSocialCode());

            updateWrapper.set(CustomerV2::getRetailInvestors, params.getRetailInvestors());

            updateWrapper.set(CustomerV2::getIsAssociatedEnterprise, params.getIsAssociatedEnterprise());
            if (CommonIfEnum.YES.getValue().equals(params.getIsAssociatedEnterprise())) {
                updateWrapper.set(CustomerV2::getAssociatedOrgNo, params.getAssociatedOrgNo());
                updateWrapper.set(CustomerV2::getAssociatedOrgCode, params.getAssociatedOrgCode());
                updateWrapper.set(CustomerV2::getAssociatedOrgName, params.getAssociatedOrgName());
            } else {
                updateWrapper.set(CustomerV2::getAssociatedOrgNo, null);
                updateWrapper.set(CustomerV2::getAssociatedOrgCode, null);
                updateWrapper.set(CustomerV2::getAssociatedOrgName, null);
            }

            updateWrapper.set(CustomerV2::getRemark, params.getRemark());

            updateWrapper.set(CustomerV2::getIsGspControl, params.getIsGspControl());
            updateWrapper.set(CustomerV2::getBusinessFlag, CustomerBusinessFlagEnum.DRAFT.getValue().equals(params.getBusinessFlag()) ? CustomerBusinessFlagEnum.DRAFT.getValue() : CustomerBusinessFlagEnum.FORMAL.getValue());

            updateWrapper.set(CustomerV2::getOperateName, operationModel.getUserName());
            updateWrapper.set(CustomerV2::getOperateNo, operationModel.getEmployerNo());
            updateWrapper.set(CustomerV2::getOperateTime, DateUtil.getCurrentDate());

            // where条件
            updateWrapper.eq(CustomerV2::getEnterpriseNo, operationModel.getEnterpriseNo());
            updateWrapper.eq(CustomerV2::getManageOrgNo, customerV2.getManageOrgNo());
            updateWrapper.eq(CustomerV2::getCustomerCode, params.getCustomerCode());
            updateWrapper.eq(CustomerV2::getDeleted, DeletedEnum.UN_DELETE.getValue());

            customerV2DAO.update(null, updateWrapper);

//            newCustomer = updateWrapper.getEntity();
        } else {
            company = companyV2Service.getOne(Wrappers.<CompanyV2>lambdaQuery()
                    .eq(CompanyV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                    .eq(CompanyV2::getCompanyNo, customerV2.getCompanyNo()));
        }

        LambdaQueryWrapper<CustomerBiz> bizQueryWrapper = Wrappers.<CustomerBiz>lambdaQuery()
                .eq(CustomerBiz::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CustomerBiz::getUseOrgNo, params.getUseOrgNo())
                .eq(CustomerBiz::getCustomerCode, params.getCustomerCode())
                .eq(CustomerBiz::getDeleted, DeletedEnum.UN_DELETE.getValue());
        CustomerBiz dbCustomerBiz = customerBizDAO.selectOne(bizQueryWrapper);
        ValidatorUtils.checkEmptyThrowEx(dbCustomerBiz, "客户业务信息不存在");

        //业务信息
        LambdaUpdateWrapper<CustomerBiz> updateBizWrapper = Wrappers.lambdaUpdate(CustomerBiz.class);
        updateBizWrapper.set(CustomerBiz::getCooperationMode, params.getCooperationMode());
        updateBizWrapper.set(CustomerBiz::getCurrencyId, params.getCurrencyId());
        updateBizWrapper.set(CustomerBiz::getSettlementModes, params.getSettlementModes());
        updateBizWrapper.set(CustomerBiz::getCreditAmount, params.getCreditAmount());
        updateBizWrapper.set(CustomerBiz::getCreditDates, params.getCreditDates());
        updateBizWrapper.set(CustomerBiz::getCoopStartTime, params.getCoopStartTime());
        updateBizWrapper.set(CustomerBiz::getCoopEndTime, params.getCoopEndTime());
        updateBizWrapper.set(CustomerBiz::getPriceCategoryCode, params.getPriceCategoryCode());
        updateBizWrapper.set(CustomerBiz::getBusinessType, params.getBusinessType());
        updateBizWrapper.set(CustomerBiz::getReceiveAgreement, params.getReceiveAgreement());
        updateBizWrapper.set(CustomerBiz::getReceiveCondition, params.getReceiveCondition());
        updateBizWrapper.set(CustomerBiz::getOwnerCompany, params.getOwnerCompany());
        if (!customerV2.getManageOrgNo().equals(params.getUseOrgNo())) {
            updateBizWrapper.set(CustomerBiz::getUseOrgModifyFlag, CommonIfEnum.YES.getValue());
        }
        updateBizWrapper.set(CustomerBiz::getOperateName, operationModel.getUserName());
        updateBizWrapper.set(CustomerBiz::getOperateNo, operationModel.getEmployerNo());
        updateBizWrapper.set(CustomerBiz::getOperateTime, DateUtil.getCurrentDate());

        //where条件
        updateBizWrapper.eq(CustomerBiz::getEnterpriseNo, operationModel.getEnterpriseNo());
        updateBizWrapper.eq(CustomerBiz::getUseOrgNo, params.getUseOrgNo());
        updateBizWrapper.eq(CustomerBiz::getCustomerCode, params.getCustomerCode());
        updateBizWrapper.eq(CustomerBiz::getDeleted, DeletedEnum.UN_DELETE.getValue());

        customerBizDAO.update(null, updateBizWrapper);


        Map<String, Object> businessValue = new HashMap<>();
//        if (null != newCustomer) {
//            businessValue.put("customer", newCustomer);
//        }
//        businessValue.put("customerBiz", updateBizWrapper.getEntity());

        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                try {
                    businessLogService.saveLog(DLogLevel.INFO.name(), ViewNameConstant.BDC_CUSTOMER_VIEW, params.getCustomerCode() + params.getUseOrgNo(), "编辑客户档案", "编辑客户档案", JSON.toJSONString(businessValue), "");
                } catch (Exception e) {
                    log.error("编辑客户档案日志保存失败", e);
                }
            }
        });

        // 分派更新
        if (!CustomerBusinessFlagEnum.DRAFT.getValue().equals(params.getBusinessFlag()) && customerV2.getManageOrgNo().equals(params.getUseOrgNo())) {
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                @Override
                public void afterCommit() {
                    try {
                        log.warn("updateDocHandlerReq={},{},{},{},{}", operationModel.getEnterpriseNo(), BillNameConstant.BDC_CUSTOMER_BILL, params.getCustomerCode(), customerV2.getManageOrgNo(), 0);

                        Boolean updateCallResult = gradeControlService.updateDocHandler(operationModel.getEnterpriseNo(), BillNameConstant.BDC_CUSTOMER_BILL, params.getCustomerCode(), customerV2.getManageOrgNo(), 0);

                        log.warn("updateDocHandlerResp={}", updateCallResult);
                    } catch (Exception e) {
                        log.error("客户档案调用分派更新失败", e);
                    }
                }
            });
        }

        return true;
    }

    private void validateAssumeManageEditBasicAndBizReq(OperationModel operationModel, CustomerAssumeManageEditBasicAndBizReq params) {
        //必填项校验
        ValidatorUtils.checkEmptyThrowEx(params.getCustomerCode(), "客户编码不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getCustomerName(), "客户名称不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getTransactionType(), "客户类型不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getCustomerCategoryNo(), "客户分类不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getCompanyName(), "企业名称不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getUnifiedSocialCode(), "统一社会信用代码不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getFactoryType(), "企业注册地域不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getTaxCategory(), "纳税类别不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getRetailInvestors(), "是否散户不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getIsAssociatedEnterprise(), "是否内部组织不能为空");
        if (null != params.getIsAssociatedEnterprise() && CommonIfEnum.YES.getValue().equals(params.getIsAssociatedEnterprise())) {
            ValidatorUtils.checkEmptyThrowEx(params.getAssociatedOrgNo(), "对应内部组织不能为空");
        }
        ValidatorUtils.checkEmptyThrowEx(params.getIsMedicalInstitution(), "是否医疗机构不能为空");
        if (InstitutionalTypeEnum.yy.getType().equals(params.getIsMedicalInstitution().toString())) {
            ValidatorUtils.checkEmptyThrowEx(params.getHospitalType(), "医院性质不能为空");
            ValidatorUtils.checkEmptyThrowEx(params.getHospitalClass(), "医院等级不能为空");
        }

        //范围校验
        if (StringUtils.isNotEmpty(params.getCustomerCode())) {
            ValidatorUtils.checkTrueThrowEx(params.getCustomerCode().length() > 50, "客户编码不能大于50");
        }
        if (StringUtils.isNotEmpty(params.getCustomerName())) {
            ValidatorUtils.checkTrueThrowEx(params.getCustomerName().length() > 128, "客户名称不能大于128");
        }
        if (StringUtils.isNotEmpty(params.getCustomerNameEn())) {
            ValidatorUtils.checkTrueThrowEx(params.getCustomerNameEn().length() > 300, "客户英文名称不能大于300");
        }
        if (StringUtils.isNotEmpty(params.getMnemonicCode())) {
            ValidatorUtils.checkTrueThrowEx(params.getMnemonicCode().length() > 100, "助记码不能大于100");
        }
        if (StringUtils.isNotEmpty(params.getCompanyName())) {
            ValidatorUtils.checkTrueThrowEx(params.getCompanyName().length() > 128, "企业名称不能大于128");
        }
        if (StringUtils.isNotEmpty(params.getUnifiedSocialCode())) {
            ValidatorUtils.checkTrueThrowEx(params.getUnifiedSocialCode().length() > 100, "统一社会信用代码不能大于100");
        }
        if (StringUtils.isNotEmpty(params.getCountry())) {
            ValidatorUtils.checkTrueThrowEx(params.getCountry().length() > 50, "国家/地区不能大于50");
        }
        if (StringUtils.isNotEmpty(params.getRemark())) {
            ValidatorUtils.checkTrueThrowEx(params.getRemark().length() > 300, "备注不能大于300");
        }


        if (StringUtils.isNotEmpty(params.getReceiveAgreement())) {
            ValidatorUtils.checkTrueThrowEx(params.getReceiveAgreement().length() > 500, "收款协议不能大于500");
        }
        if (StringUtils.isNotEmpty(params.getReceiveCondition())) {
            ValidatorUtils.checkTrueThrowEx(params.getReceiveCondition().length() > 500, "收款条件不能大于500");
        }
        if (StringUtils.isNotEmpty(params.getCreditAmount())) {
            ValidatorUtils.checkTrueThrowEx(params.getCreditAmount().length() > 20, "信用额度不能大于20");
        }
        if (StringUtils.isNotEmpty(params.getCreditDates())) {
            ValidatorUtils.checkTrueThrowEx(params.getCreditDates().length() > 20, "信用期限不能大于20");
        }
        if (StringUtils.isNotEmpty(params.getOwnerCompany())) {
            ValidatorUtils.checkTrueThrowEx(params.getOwnerCompany().length() > 100, "业务归属不能大于100");
        }
    }

    @Override
    public Boolean assumeManageEditCustomerBasicAndBiz(OperationModel operationModel, CustomerAssumeManageEditBasicAndBizReq params) {

        //校验入参必填、范围
        validateAssumeManageEditBasicAndBizReq(operationModel, params);

        LambdaQueryWrapper<CustomerV2> queryWrapper = Wrappers.<CustomerV2>lambdaQuery()
                .eq(CustomerV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CustomerV2::getCustomerCode, params.getCustomerCode())
                .eq(CustomerV2::getDeleted, DeletedEnum.UN_DELETE.getValue());
        CustomerV2 customerV2 = customerV2DAO.selectOne(queryWrapper);
        ValidatorUtils.checkEmptyThrowEx(customerV2, "客户不存在");

        ValidatorUtils.checkTrueThrowEx(customerV2DAO.selectCount(new LambdaQueryWrapper<CustomerV2>()
                .eq(CustomerV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CustomerV2::getCustomerName, params.getCustomerName())
                .ne(CustomerV2::getCustomerCode, params.getCustomerCode())
                .eq(CustomerV2::getDeleted, DeletedEnum.UN_DELETE.getValue())) > 0, "客户名称重复");

        if (CommonIfEnum.YES.getValue().equals(params.getIsAssociatedEnterprise())) {
            List<CustomerV2> byInternalOrgNo = findByInternalOrgNo(operationModel, params.getCustomerCode(), params.getAssociatedOrgNo());
            if (CollectionUtils.isNotEmpty(byInternalOrgNo)) {
                String errorMessage = String.format("对应内部组织已被客户【%s】关联", String.join(",", byInternalOrgNo.stream().map(CustomerV2::getCustomerName).collect(Collectors.toSet())));
                throw new BusinessException(ErrorCode.param_invalid_msg, errorMessage);
            }
        }

        // 处理企业
        CompanySaveOrUpdateReq companySaveOrUpdateReq = new CompanySaveOrUpdateReq();
        BeanUtils.copyProperties(params, companySaveOrUpdateReq);
        CompanyV2 company = companyV2Service.saveOrUpdateCompany(operationModel, params.getCustomerCode(), companySaveOrUpdateReq, CompanyPartnershipEnum.CUSTOMER);

        // 处理客户
        LambdaUpdateWrapper<CustomerV2> updateWrapper = Wrappers.lambdaUpdate(CustomerV2.class);
        updateWrapper.set(CustomerV2::getCustomerName, params.getCustomerName());
        updateWrapper.set(CustomerV2::getCustomerNameEn, params.getCustomerNameEn());
        updateWrapper.set(CustomerV2::getMnemonicCode, params.getMnemonicCode());
        updateWrapper.set(CustomerV2::getTransactionType, params.getTransactionType());
        updateWrapper.set(CustomerV2::getCustomerCategoryNo, params.getCustomerCategoryNo());

        updateWrapper.set(CustomerV2::getCompanyNo, company.getCompanyNo());
        updateWrapper.set(CustomerV2::getUnifiedSocialCode, company.getUnifiedSocialCode());

        updateWrapper.set(CustomerV2::getRetailInvestors, params.getRetailInvestors());

        updateWrapper.set(CustomerV2::getIsAssociatedEnterprise, params.getIsAssociatedEnterprise());
        if (CommonIfEnum.YES.getValue().equals(params.getIsAssociatedEnterprise())) {
            updateWrapper.set(CustomerV2::getAssociatedOrgNo, params.getAssociatedOrgNo());
            updateWrapper.set(CustomerV2::getAssociatedOrgCode, params.getAssociatedOrgCode());
            updateWrapper.set(CustomerV2::getAssociatedOrgName, params.getAssociatedOrgName());
        } else {
            updateWrapper.set(CustomerV2::getAssociatedOrgNo, null);
            updateWrapper.set(CustomerV2::getAssociatedOrgCode, null);
            updateWrapper.set(CustomerV2::getAssociatedOrgName, null);
        }

        updateWrapper.set(CustomerV2::getRemark, params.getRemark());

        updateWrapper.set(CustomerV2::getIsGspControl, params.getIsGspControl());
        updateWrapper.set(CustomerV2::getBusinessFlag, CustomerBusinessFlagEnum.DRAFT.getValue().equals(params.getBusinessFlag()) ? CustomerBusinessFlagEnum.DRAFT.getValue() : CustomerBusinessFlagEnum.FORMAL.getValue());

        updateWrapper.set(CustomerV2::getOperateName, operationModel.getUserName());
        updateWrapper.set(CustomerV2::getOperateNo, operationModel.getEmployerNo());
        updateWrapper.set(CustomerV2::getOperateTime, DateUtil.getCurrentDate());

        // where条件
        updateWrapper.eq(CustomerV2::getEnterpriseNo, operationModel.getEnterpriseNo());
        updateWrapper.eq(CustomerV2::getManageOrgNo, customerV2.getManageOrgNo());
        updateWrapper.eq(CustomerV2::getCustomerCode, params.getCustomerCode());
        updateWrapper.eq(CustomerV2::getDeleted, DeletedEnum.UN_DELETE.getValue());

        customerV2DAO.update(null, updateWrapper);

        // 处理使用组织业务信息
        LambdaQueryWrapper<CustomerBiz> bizQueryWrapper = Wrappers.<CustomerBiz>lambdaQuery()
                .eq(CustomerBiz::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CustomerBiz::getUseOrgNo, params.getUseOrgNo())
                .eq(CustomerBiz::getCustomerCode, params.getCustomerCode())
                .eq(CustomerBiz::getDeleted, DeletedEnum.UN_DELETE.getValue());
        CustomerBiz dbCustomerBiz = customerBizDAO.selectOne(bizQueryWrapper);
        ValidatorUtils.checkEmptyThrowEx(dbCustomerBiz, "客户业务信息不存在");

        LambdaUpdateWrapper<CustomerBiz> updateBizWrapper = Wrappers.lambdaUpdate(CustomerBiz.class);
        updateBizWrapper.set(CustomerBiz::getCooperationMode, params.getCooperationMode());
        updateBizWrapper.set(CustomerBiz::getCurrencyId, params.getCurrencyId());
        updateBizWrapper.set(CustomerBiz::getSettlementModes, params.getSettlementModes());
        updateBizWrapper.set(CustomerBiz::getCreditAmount, params.getCreditAmount());
        updateBizWrapper.set(CustomerBiz::getCreditDates, params.getCreditDates());
        updateBizWrapper.set(CustomerBiz::getCoopStartTime, params.getCoopStartTime());
        updateBizWrapper.set(CustomerBiz::getCoopEndTime, params.getCoopEndTime());
        updateBizWrapper.set(CustomerBiz::getPriceCategoryCode, params.getPriceCategoryCode());
        updateBizWrapper.set(CustomerBiz::getBusinessType, params.getBusinessType());
        updateBizWrapper.set(CustomerBiz::getReceiveAgreement, params.getReceiveAgreement());
        updateBizWrapper.set(CustomerBiz::getReceiveCondition, params.getReceiveCondition());
        updateBizWrapper.set(CustomerBiz::getOwnerCompany, params.getOwnerCompany());
        if (!customerV2.getManageOrgNo().equals(params.getUseOrgNo())) {
            updateBizWrapper.set(CustomerBiz::getUseOrgModifyFlag, CommonIfEnum.YES.getValue());
        }
        updateBizWrapper.set(CustomerBiz::getOperateName, operationModel.getUserName());
        updateBizWrapper.set(CustomerBiz::getOperateNo, operationModel.getEmployerNo());
        updateBizWrapper.set(CustomerBiz::getOperateTime, DateUtil.getCurrentDate());

        //where条件
        updateBizWrapper.eq(CustomerBiz::getEnterpriseNo, operationModel.getEnterpriseNo());
        updateBizWrapper.eq(CustomerBiz::getUseOrgNo, params.getUseOrgNo());
        updateBizWrapper.eq(CustomerBiz::getCustomerCode, params.getCustomerCode());
        updateBizWrapper.eq(CustomerBiz::getDeleted, DeletedEnum.UN_DELETE.getValue());

        customerBizDAO.update(null, updateBizWrapper);


        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                try {
                    businessLogService.saveLog(DLogLevel.INFO.name(), ViewNameConstant.BDC_CUSTOMER_VIEW, params.getCustomerCode() + params.getUseOrgNo(), "编辑客户档案", "编辑客户档案", "", "");
                } catch (Exception e) {
                    log.error("编辑客户档案日志保存失败", e);
                }
            }
        });

        return true;
    }

    @Override
    public String preValidateAssumeManageEditCustomerBasicAndBiz(OperationModel operationModel, CustomerAssumeManageEditBasicAndBizReq params) {

        //校验入参必填、范围
        validateAssumeManageEditBasicAndBizReq(operationModel, params);

        LambdaQueryWrapper<CustomerV2> queryWrapper = Wrappers.<CustomerV2>lambdaQuery()
                .eq(CustomerV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CustomerV2::getCustomerCode, params.getCustomerCode())
                .eq(CustomerV2::getDeleted, DeletedEnum.UN_DELETE.getValue());
        CustomerV2 customerV2 = customerV2DAO.selectOne(queryWrapper);
        ValidatorUtils.checkEmptyThrowEx(customerV2, "客户不存在");

        ValidatorUtils.checkTrueThrowEx(customerV2DAO.selectCount(new LambdaQueryWrapper<CustomerV2>()
                .eq(CustomerV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CustomerV2::getCustomerName, params.getCustomerName())
                .ne(CustomerV2::getCustomerCode, params.getCustomerCode())
                .eq(CustomerV2::getDeleted, DeletedEnum.UN_DELETE.getValue())) > 0, "客户名称重复");

        if (CommonIfEnum.YES.getValue().equals(params.getIsAssociatedEnterprise())) {
            List<CustomerV2> byInternalOrgNo = findByInternalOrgNo(operationModel, params.getCustomerCode(), params.getAssociatedOrgNo());
            if (CollectionUtils.isNotEmpty(byInternalOrgNo)) {
                String errorMessage = String.format("对应内部组织已被客户【%s】关联", String.join(",", byInternalOrgNo.stream().map(CustomerV2::getCustomerName).collect(Collectors.toSet())));
                throw new BusinessException(ErrorCode.param_invalid_msg, errorMessage);
            }
        }

        // 处理企业
        CompanySaveOrUpdateReq companySaveOrUpdateReq = new CompanySaveOrUpdateReq();
        BeanUtils.copyProperties(params, companySaveOrUpdateReq);
        return companyV2Service.preValidateSaveOrUpdateCompany(operationModel, params.getCustomerCode(), companySaveOrUpdateReq, CompanyPartnershipEnum.CUSTOMER);
    }

    @Override
    public Boolean editCustomerBasic(OperationModel operationModel, CustomerEditBasicReq params) {
        //校验入参必填、范围
        validateEditBasicReq(operationModel, params);

        CustomerV2 customerV2 = getCustomerByCode(operationModel, params.getCustomerCode());
        ValidatorUtils.checkEmptyThrowEx(customerV2, "客户不存在");

        ValidatorUtils.checkTrueThrowEx(customerV2DAO.selectCount(new LambdaQueryWrapper<CustomerV2>()
                .eq(CustomerV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CustomerV2::getCustomerName, params.getCustomerName())
                .ne(CustomerV2::getCustomerCode, params.getCustomerCode())
                .eq(CustomerV2::getDeleted, DeletedEnum.UN_DELETE.getValue())) > 0, "客户名称重复");

        if (CommonIfEnum.YES.getValue().equals(params.getIsAssociatedEnterprise())) {
            List<CustomerV2> byInternalOrgNo = findByInternalOrgNo(operationModel, params.getCustomerCode(), params.getAssociatedOrgNo());
            if (CollectionUtils.isNotEmpty(byInternalOrgNo)) {
                String errorMessage = String.format("对应内部组织已被客户【%s】关联", String.join(",", byInternalOrgNo.stream().map(CustomerV2::getCustomerName).collect(Collectors.toSet())));
                throw new BusinessException(ErrorCode.param_invalid_msg, errorMessage);
            }
        }

        // 处理企业
        CompanySaveOrUpdateReq companySaveOrUpdateReq = new CompanySaveOrUpdateReq();
        BeanUtils.copyProperties(params, companySaveOrUpdateReq);
        CompanyV2 company = companyV2Service.saveOrUpdateCompany(operationModel, params.getCustomerCode(), companySaveOrUpdateReq, CompanyPartnershipEnum.CUSTOMER);

        // 处理企业银行
        companyV2Service.saveOrUpdateBank(operationModel, params.getBankList(), company.getCompanyNo(), customerV2.getManageOrgNo());

        // 处理客户
        LambdaUpdateWrapper<CustomerV2> updateWrapper = Wrappers.lambdaUpdate(CustomerV2.class);
        updateWrapper.set(CustomerV2::getCustomerName, params.getCustomerName());
        updateWrapper.set(CustomerV2::getCustomerNameEn, params.getCustomerNameEn());
        updateWrapper.set(CustomerV2::getMnemonicCode, params.getMnemonicCode());
        updateWrapper.set(CustomerV2::getTransactionType, params.getTransactionType());
        updateWrapper.set(CustomerV2::getCustomerCategoryNo, params.getCustomerCategoryNo());

        updateWrapper.set(CustomerV2::getCompanyNo, company.getCompanyNo());
        updateWrapper.set(CustomerV2::getUnifiedSocialCode, company.getUnifiedSocialCode());

        updateWrapper.set(CustomerV2::getRetailInvestors, params.getRetailInvestors());

        updateWrapper.set(CustomerV2::getIsAssociatedEnterprise, params.getIsAssociatedEnterprise());
        if (CommonIfEnum.YES.getValue().equals(params.getIsAssociatedEnterprise())) {
            updateWrapper.set(CustomerV2::getAssociatedOrgNo, params.getAssociatedOrgNo());
            updateWrapper.set(CustomerV2::getAssociatedOrgCode, params.getAssociatedOrgCode());
            updateWrapper.set(CustomerV2::getAssociatedOrgName, params.getAssociatedOrgName());
        } else {
            updateWrapper.set(CustomerV2::getAssociatedOrgNo, null);
            updateWrapper.set(CustomerV2::getAssociatedOrgCode, null);
            updateWrapper.set(CustomerV2::getAssociatedOrgName, null);
        }

        updateWrapper.set(CustomerV2::getRemark, params.getRemark());

        updateWrapper.set(CustomerV2::getIsGspControl, params.getIsGspControl());
        updateWrapper.set(CustomerV2::getBusinessFlag, CustomerBusinessFlagEnum.DRAFT.getValue().equals(params.getBusinessFlag()) ? CustomerBusinessFlagEnum.DRAFT.getValue() : CustomerBusinessFlagEnum.FORMAL.getValue());

        updateWrapper.set(CustomerV2::getOperateName, operationModel.getUserName());
        updateWrapper.set(CustomerV2::getOperateNo, operationModel.getEmployerNo());
        updateWrapper.set(CustomerV2::getOperateTime, DateUtil.getCurrentDate());

        // where条件
        updateWrapper.eq(CustomerV2::getEnterpriseNo, operationModel.getEnterpriseNo());
        updateWrapper.eq(CustomerV2::getManageOrgNo, customerV2.getManageOrgNo());
        updateWrapper.eq(CustomerV2::getCustomerCode, params.getCustomerCode());
        updateWrapper.eq(CustomerV2::getDeleted, DeletedEnum.UN_DELETE.getValue());

        int update = customerV2DAO.update(null, updateWrapper);

        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                try {
                    businessLogService.saveLog(DLogLevel.INFO.name(), ViewNameConstant.BDC_CUSTOMER_VIEW, params.getCustomerCode() + params.getManageOrgNo(), "客户档案变更申请编辑", "客户档案变更申请编辑", "", "");
                } catch (Exception e) {
                    log.error("客户档案变更申请编辑日志保存失败", e);
                }
            }
        });

        return update > 0;
    }

    @Override
    public String preValidateEditCustomerBasic(OperationModel operationModel, CustomerEditBasicReq params) {
        //校验入参必填、范围
        validateEditBasicReq(operationModel, params);

        CustomerV2 customerV2 = getCustomerByCode(operationModel, params.getCustomerCode());
        ValidatorUtils.checkEmptyThrowEx(customerV2, "客户不存在");

        ValidatorUtils.checkTrueThrowEx(customerV2DAO.selectCount(new LambdaQueryWrapper<CustomerV2>()
                .eq(CustomerV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CustomerV2::getCustomerName, params.getCustomerName())
                .ne(CustomerV2::getCustomerCode, params.getCustomerCode())
                .eq(CustomerV2::getDeleted, DeletedEnum.UN_DELETE.getValue())) > 0, "客户名称重复");

        if (CommonIfEnum.YES.getValue().equals(params.getIsAssociatedEnterprise())) {
            List<CustomerV2> byInternalOrgNo = findByInternalOrgNo(operationModel, params.getCustomerCode(), params.getAssociatedOrgNo());
            if (CollectionUtils.isNotEmpty(byInternalOrgNo)) {
                String errorMessage = String.format("对应内部组织已被客户【%s】关联", String.join(",", byInternalOrgNo.stream().map(CustomerV2::getCustomerName).collect(Collectors.toSet())));
                throw new BusinessException(ErrorCode.param_invalid_msg, errorMessage);
            }
        }

        // 处理企业
        CompanySaveOrUpdateReq companySaveOrUpdateReq = new CompanySaveOrUpdateReq();
        BeanUtils.copyProperties(params, companySaveOrUpdateReq);
        return companyV2Service.preValidateSaveOrUpdateCompany(operationModel, params.getCustomerCode(), companySaveOrUpdateReq, CompanyPartnershipEnum.CUSTOMER);
    }

    private void editCustomerAddressListValidate(OperationModel operationModel, CustomerAddressListEditReq params) {
        if (CollectionUtils.isEmpty(params.getLinkAddressList())) {
            return;
        }

        for (CompanyShippingAddressReq companyShippingAddressReq : params.getLinkAddressList()) {
            //必填项校验
            ValidatorUtils.checkEmptyThrowEx(companyShippingAddressReq.getReceiveUser(), "联系人不能为空");
            ValidatorUtils.checkEmptyThrowEx(companyShippingAddressReq.getReceivePhone(), "联系电话不能为空");
            ValidatorUtils.checkEmptyThrowEx(companyShippingAddressReq.getAddressType(), "地址类型不能为空");
            ValidatorUtils.checkEmptyThrowEx(companyShippingAddressReq.getRegionCode(), "行政区域不能为空");
            ValidatorUtils.checkEmptyThrowEx(companyShippingAddressReq.getReceiveAddr(), "详细地址不能为空");
            ValidatorUtils.checkEmptyThrowEx(companyShippingAddressReq.getIsDefault(), "是否默认不能为空");

            //范围校验
            ValidatorUtils.checkTrueThrowEx(companyShippingAddressReq.getReceiveUser().length() > 50, "联系人不能大于50");
            ValidatorUtils.checkTrueThrowEx(companyShippingAddressReq.getReceivePhone().length() > 100, "联系电话不能大于100");
            ValidatorUtils.checkTrueThrowEx(companyShippingAddressReq.getReceiveAddr().length() > 300, "详细地址不能大于300");
            if (StringUtils.isNotEmpty(companyShippingAddressReq.getAddressDesc())) {
                ValidatorUtils.checkTrueThrowEx(companyShippingAddressReq.getAddressDesc().length() > 100, "地址描述不能大于100");
            }
        }
    }

    @Override
    @Transactional
    public List<Long> editCustomerAddressList(OperationModel operationModel, CustomerAddressListEditReq params) {
        // 参数校验
        editCustomerAddressListValidate(operationModel, params);

        CustomerV2 customer = customerV2DAO.selectOne(new LambdaQueryWrapper<CustomerV2>()
                .eq(CustomerV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CustomerV2::getCustomerCode, params.getCustomerCode())
                .eq(CustomerV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
        if (null == customer) {
            throw new BusinessException(ErrorCode.param_invalid_msg, "客户不存在");
        }

        boolean hasBase = customerBaseDAO.exists(new LambdaQueryWrapper<CustomerBase>()
                .eq(CustomerBase::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CustomerBase::getCustomerCode, params.getCustomerCode())
                .eq(CustomerBase::getUseOrgNo, params.getUseOrgNo())
                .eq(CustomerBase::getDeleted, DeletedEnum.UN_DELETE.getValue()));
        ValidatorUtils.checkTrueThrowEx(!hasBase, "客户未分派");


        List<CompanyShippingAddressV2> companyShippingAddressV2s = companyShippingAddressV2DAO.selectList(Wrappers.<CompanyShippingAddressV2>lambdaQuery()
                .eq(CompanyShippingAddressV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CompanyShippingAddressV2::getUseOrgNo, params.getUseOrgNo())
                .eq(CompanyShippingAddressV2::getSourceNo, params.getCustomerCode())
                .eq(CompanyShippingAddressV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
        Map<String, List<?>> diffResultMap = BeanUtil.diffCUD(companyShippingAddressV2s, params.getLinkAddressList());

        List<CompanyShippingAddressReq> addList = (List<CompanyShippingAddressReq>) diffResultMap.get("addList");
        List<CompanyShippingAddressReq> updateList = (List<CompanyShippingAddressReq>) diffResultMap.get("updateList");
        List<CompanyShippingAddressV2> deleteList = (List<CompanyShippingAddressV2>) diffResultMap.get("deleteList");


        List<CompanyShippingAddressV2> saveShippingAddressListList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(addList)) {
            for (CompanyShippingAddressReq r : addList) {
                CompanyShippingAddressV2 shippingAddress = new CompanyShippingAddressV2();
                shippingAddress.setEnterpriseNo(operationModel.getEnterpriseNo());
                shippingAddress.setUseOrgNo(params.getUseOrgNo());
//            shippingAddress.setCompanyNo(customer.getCompanyNo());
                shippingAddress.setLinkAddressCode(r.getLinkAddressCode());
                shippingAddress.setSourceNo(params.getCustomerCode());
                shippingAddress.setStatus(CommonStatusEnum.EFFECTIVE.getValue());
                shippingAddress.setLinkaddType(LinkmanTypeEnum.SALE.getValue());
                shippingAddress.setReceiveUser(r.getReceiveUser());
                shippingAddress.setReceivePhone(r.getReceivePhone());
                shippingAddress.setRegionCode(r.getRegionCode());
                shippingAddress.setRegionName(r.getRegionName());
                shippingAddress.setReceiveAddr(r.getReceiveAddr());
                shippingAddress.setIsDefault(r.getIsDefault());
                shippingAddress.setAddressDesc(r.getAddressDesc());
                shippingAddress.setAddressType(r.getAddressType());
                saveShippingAddressListList.add(shippingAddress);
            }

            companyShippingAddressV2DAO.addBatch(saveShippingAddressListList);
        }


        if (CollectionUtils.isNotEmpty(updateList)) {
            List<CompanyShippingAddressV2> updateBankList = new ArrayList<>();
            for (CompanyShippingAddressReq r : updateList) {
                CompanyShippingAddressV2 shippingAddress = new CompanyShippingAddressV2();
                shippingAddress.setId(r.getId());
                shippingAddress.setEnterpriseNo(operationModel.getEnterpriseNo());
                shippingAddress.setUseOrgNo(params.getUseOrgNo());
//            shippingAddress.setCompanyNo(customer.getCompanyNo());
                shippingAddress.setLinkAddressCode(r.getLinkAddressCode());
                shippingAddress.setSourceNo(params.getCustomerCode());
                shippingAddress.setStatus(CommonStatusEnum.EFFECTIVE.getValue());
                shippingAddress.setLinkaddType(LinkmanTypeEnum.SALE.getValue());
                shippingAddress.setReceiveUser(r.getReceiveUser());
                shippingAddress.setReceivePhone(r.getReceivePhone());
                shippingAddress.setRegionCode(r.getRegionCode());
                shippingAddress.setRegionName(r.getRegionName());
                shippingAddress.setReceiveAddr(r.getReceiveAddr());
                shippingAddress.setIsDefault(r.getIsDefault());
                shippingAddress.setAddressDesc(r.getAddressDesc());
                shippingAddress.setAddressType(r.getAddressType());
                updateBankList.add(shippingAddress);
            }
            companyShippingAddressV2DAO.updateByIdBatch(updateBankList);
        }


        if (CollectionUtils.isNotEmpty(deleteList)) {
            CompanyShippingAddressV2 shippingAddress = new CompanyShippingAddressV2();
            shippingAddress.setDeleted(DeletedEnum.DELETED.getValue());
            companyShippingAddressV2DAO.update(shippingAddress, Wrappers.<CompanyShippingAddressV2>lambdaQuery()
                    .eq(CompanyShippingAddressV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                    .in(CompanyShippingAddressV2::getId, deleteList.stream().map(CompanyShippingAddressV2::getId).collect(Collectors.toList())));
        }

        return saveShippingAddressListList.stream().map(CompanyShippingAddressV2::getId).collect(Collectors.toList());
    }

    @Override
    @Transactional
    public List<CompanyShippingAddressV2> overwriteCustomerAddressList(OperationModel operationModel, CustomerAddressListEditReq params) {
        CompanyShippingAddressV2 toDeleteShippingAddress = new CompanyShippingAddressV2();
        toDeleteShippingAddress.setDeleted(DeletedEnum.DELETED.getValue());
        companyShippingAddressV2DAO.update(toDeleteShippingAddress, Wrappers.<CompanyShippingAddressV2>lambdaQuery()
                .eq(CompanyShippingAddressV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CompanyShippingAddressV2::getUseOrgNo, params.getUseOrgNo())
                .eq(CompanyShippingAddressV2::getSourceNo, params.getCustomerCode())
                .eq(CompanyShippingAddressV2::getLinkaddType, LinkmanTypeEnum.SALE.getValue())
                .eq(CompanyShippingAddressV2::getDeleted, DeletedEnum.UN_DELETE.getValue())
        );

        if (CollectionUtils.isNotEmpty(params.getLinkAddressList())) {
            List<CompanyShippingAddressV2> addShippingAddressList = new ArrayList<>();
            for (CompanyShippingAddressReq r : params.getLinkAddressList()) {
                CompanyShippingAddressV2 shippingAddress = new CompanyShippingAddressV2();
                shippingAddress.setEnterpriseNo(operationModel.getEnterpriseNo());
                shippingAddress.setUseOrgNo(params.getUseOrgNo());
//            shippingAddress.setCompanyNo(customer.getCompanyNo());
                shippingAddress.setLinkAddressCode(r.getLinkAddressCode());
                shippingAddress.setSourceNo(params.getCustomerCode());
                shippingAddress.setStatus(CommonStatusEnum.EFFECTIVE.getValue());
                shippingAddress.setLinkaddType(LinkmanTypeEnum.SALE.getValue());
                shippingAddress.setReceiveUser(r.getReceiveUser());
                shippingAddress.setReceivePhone(r.getReceivePhone());
                shippingAddress.setRegionCode(r.getRegionCode());
                shippingAddress.setRegionName(r.getRegionName());
                shippingAddress.setReceiveAddr(r.getReceiveAddr());
                shippingAddress.setIsDefault(r.getIsDefault());
                shippingAddress.setAddressDesc(r.getAddressDesc());
                shippingAddress.setAddressType(r.getAddressType());
                addShippingAddressList.add(shippingAddress);
            }

            companyShippingAddressV2DAO.addBatch(addShippingAddressList);

            return addShippingAddressList;
        }

        return Collections.emptyList();
    }

    private void saveCustomerAddressListValidate(OperationModel operationModel, CustomerAddressListSaveReq params) {
        if (CollectionUtils.isEmpty(params.getLinkAddressList())) {
            return;
        }

        for (CompanyShippingAddressReq companyShippingAddressReq : params.getLinkAddressList()) {
            //必填项校验
            ValidatorUtils.checkEmptyThrowEx(companyShippingAddressReq.getReceiveUser(), "联系人不能为空");
            ValidatorUtils.checkEmptyThrowEx(companyShippingAddressReq.getReceivePhone(), "联系电话不能为空");
            ValidatorUtils.checkEmptyThrowEx(companyShippingAddressReq.getAddressType(), "地址类型不能为空");
            ValidatorUtils.checkEmptyThrowEx(companyShippingAddressReq.getRegionCode(), "行政区域不能为空");
            ValidatorUtils.checkEmptyThrowEx(companyShippingAddressReq.getReceiveAddr(), "详细地址不能为空");
            ValidatorUtils.checkEmptyThrowEx(companyShippingAddressReq.getIsDefault(), "是否默认不能为空");

            //范围校验
            ValidatorUtils.checkTrueThrowEx(companyShippingAddressReq.getReceiveUser().length() > 50, "联系人不能大于50");
            ValidatorUtils.checkTrueThrowEx(companyShippingAddressReq.getReceivePhone().length() > 100, "联系电话不能大于100");
            ValidatorUtils.checkTrueThrowEx(companyShippingAddressReq.getReceiveAddr().length() > 300, "详细地址不能大于300");
            if (StringUtils.isNotEmpty(companyShippingAddressReq.getAddressDesc())) {
                ValidatorUtils.checkTrueThrowEx(companyShippingAddressReq.getAddressDesc().length() > 100, "地址描述不能大于100");
            }
        }
    }

    @Override
    public List<Long> saveCustomerAddressList(OperationModel operationModel, CustomerAddressListSaveReq params) {
        // 参数校验
        saveCustomerAddressListValidate(operationModel, params);

        CustomerV2 customer = customerV2DAO.selectOne(new LambdaQueryWrapper<CustomerV2>()
                .eq(CustomerV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CustomerV2::getCustomerCode, params.getCustomerCode())
                .eq(CustomerV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
        if (null == customer) {
            throw new BusinessException(ErrorCode.param_invalid_msg, "客户不存在");
        }

        boolean hasBase = customerBaseDAO.exists(new LambdaQueryWrapper<CustomerBase>()
                .eq(CustomerBase::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CustomerBase::getCustomerCode, params.getCustomerCode())
                .eq(CustomerBase::getUseOrgNo, params.getUseOrgNo())
                .eq(CustomerBase::getDeleted, DeletedEnum.UN_DELETE.getValue()));
        ValidatorUtils.checkTrueThrowEx(!hasBase, "客户未分派");





        List<CompanyShippingAddressV2> saveShippingAddressListList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(params.getLinkAddressList())) {
            for (CompanyShippingAddressReq r : params.getLinkAddressList()) {
                CompanyShippingAddressV2 shippingAddress = new CompanyShippingAddressV2();
                shippingAddress.setEnterpriseNo(operationModel.getEnterpriseNo());
                shippingAddress.setUseOrgNo(params.getUseOrgNo());
//            shippingAddress.setCompanyNo(customer.getCompanyNo());
                shippingAddress.setLinkAddressCode(r.getLinkAddressCode());
                shippingAddress.setSourceNo(params.getCustomerCode());
                shippingAddress.setStatus(CommonStatusEnum.EFFECTIVE.getValue());
                shippingAddress.setLinkaddType(LinkmanTypeEnum.SALE.getValue());
                shippingAddress.setReceiveUser(r.getReceiveUser());
                shippingAddress.setReceivePhone(r.getReceivePhone());
                shippingAddress.setRegionCode(r.getRegionCode());
                shippingAddress.setRegionName(r.getRegionName());
                shippingAddress.setReceiveAddr(r.getReceiveAddr());
                shippingAddress.setIsDefault(r.getIsDefault());
                shippingAddress.setAddressDesc(r.getAddressDesc());
                shippingAddress.setAddressType(r.getAddressType());
                saveShippingAddressListList.add(shippingAddress);
            }

            companyShippingAddressV2DAO.addBatch(saveShippingAddressListList);
        }




        return saveShippingAddressListList.stream().map(CompanyShippingAddressV2::getId).collect(Collectors.toList());
    }

    private void updateCustomerAddressListValidate(OperationModel operationModel, CustomerAddressListUpdateReq params) {
        if (CollectionUtils.isEmpty(params.getLinkAddressList())) {
            return;
        }

        for (CompanyShippingAddressReq companyShippingAddressReq : params.getLinkAddressList()) {
            //必填项校验
            ValidatorUtils.checkEmptyThrowEx(companyShippingAddressReq.getReceiveUser(), "联系人不能为空");
            ValidatorUtils.checkEmptyThrowEx(companyShippingAddressReq.getReceivePhone(), "联系电话不能为空");
            ValidatorUtils.checkEmptyThrowEx(companyShippingAddressReq.getAddressType(), "地址类型不能为空");
            ValidatorUtils.checkEmptyThrowEx(companyShippingAddressReq.getRegionCode(), "行政区域不能为空");
            ValidatorUtils.checkEmptyThrowEx(companyShippingAddressReq.getReceiveAddr(), "详细地址不能为空");
            ValidatorUtils.checkEmptyThrowEx(companyShippingAddressReq.getIsDefault(), "是否默认不能为空");

            //范围校验
            ValidatorUtils.checkTrueThrowEx(companyShippingAddressReq.getReceiveUser().length() > 50, "联系人不能大于50");
            ValidatorUtils.checkTrueThrowEx(companyShippingAddressReq.getReceivePhone().length() > 100, "联系电话不能大于100");
            ValidatorUtils.checkTrueThrowEx(companyShippingAddressReq.getReceiveAddr().length() > 300, "详细地址不能大于300");
            if (StringUtils.isNotEmpty(companyShippingAddressReq.getAddressDesc())) {
                ValidatorUtils.checkTrueThrowEx(companyShippingAddressReq.getAddressDesc().length() > 100, "地址描述不能大于100");
            }
        }
    }

    @Override
    public Boolean updateCustomerAddressList(OperationModel operationModel, CustomerAddressListUpdateReq params) {
        // 参数校验
        updateCustomerAddressListValidate(operationModel, params);

        CustomerV2 customer = customerV2DAO.selectOne(new LambdaQueryWrapper<CustomerV2>()
                .eq(CustomerV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CustomerV2::getCustomerCode, params.getCustomerCode())
                .eq(CustomerV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
        if (null == customer) {
            throw new BusinessException(ErrorCode.param_invalid_msg, "客户不存在");
        }

        boolean hasBase = customerBaseDAO.exists(new LambdaQueryWrapper<CustomerBase>()
                .eq(CustomerBase::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CustomerBase::getCustomerCode, params.getCustomerCode())
                .eq(CustomerBase::getUseOrgNo, params.getUseOrgNo())
                .eq(CustomerBase::getDeleted, DeletedEnum.UN_DELETE.getValue()));
        ValidatorUtils.checkTrueThrowEx(!hasBase, "客户未分派");





        if (CollectionUtils.isNotEmpty(params.getLinkAddressList())) {
            List<CompanyShippingAddressV2> updateBankList = new ArrayList<>();
            for (CompanyShippingAddressReq r : params.getLinkAddressList()) {
                CompanyShippingAddressV2 shippingAddress = new CompanyShippingAddressV2();
                shippingAddress.setId(r.getId());
                shippingAddress.setEnterpriseNo(operationModel.getEnterpriseNo());
                shippingAddress.setUseOrgNo(params.getUseOrgNo());
//            shippingAddress.setCompanyNo(customer.getCompanyNo());
                shippingAddress.setLinkAddressCode(r.getLinkAddressCode());
                shippingAddress.setSourceNo(params.getCustomerCode());
                shippingAddress.setStatus(CommonStatusEnum.EFFECTIVE.getValue());
                shippingAddress.setLinkaddType(LinkmanTypeEnum.SALE.getValue());
                shippingAddress.setReceiveUser(r.getReceiveUser());
                shippingAddress.setReceivePhone(r.getReceivePhone());
                shippingAddress.setRegionCode(r.getRegionCode());
                shippingAddress.setRegionName(r.getRegionName());
                shippingAddress.setReceiveAddr(r.getReceiveAddr());
                shippingAddress.setIsDefault(r.getIsDefault());
                shippingAddress.setAddressDesc(r.getAddressDesc());
                shippingAddress.setAddressType(r.getAddressType());
                updateBankList.add(shippingAddress);
            }
            companyShippingAddressV2DAO.updateByIdBatch(updateBankList);
        }




        return true;
    }

    @Override
    public Boolean deleteCustomerAddressList(OperationModel operationModel, CustomerAddressListDeleteReq params) {

        CustomerV2 customer = customerV2DAO.selectOne(new LambdaQueryWrapper<CustomerV2>()
                .eq(CustomerV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CustomerV2::getCustomerCode, params.getCustomerCode())
                .eq(CustomerV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
        if (null == customer) {
            throw new BusinessException(ErrorCode.param_invalid_msg, "客户不存在");
        }

        boolean hasBase = customerBaseDAO.exists(new LambdaQueryWrapper<CustomerBase>()
                .eq(CustomerBase::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CustomerBase::getCustomerCode, params.getCustomerCode())
                .eq(CustomerBase::getUseOrgNo, params.getUseOrgNo())
                .eq(CustomerBase::getDeleted, DeletedEnum.UN_DELETE.getValue()));
        ValidatorUtils.checkTrueThrowEx(!hasBase, "客户未分派");





        if (CollectionUtils.isNotEmpty(params.getLinkAddressList())) {
            CompanyShippingAddressV2 shippingAddress = new CompanyShippingAddressV2();
            shippingAddress.setDeleted(DeletedEnum.DELETED.getValue());
            companyShippingAddressV2DAO.update(shippingAddress, Wrappers.<CompanyShippingAddressV2>lambdaQuery()
                    .eq(CompanyShippingAddressV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                    .in(CompanyShippingAddressV2::getId, params.getLinkAddressList()));
        }

        return true;
    }

    @Override
    public void preValidateEditCustomerAddressList(OperationModel operationModel, CustomerAddressListEditReq params) {
        // 参数校验
        editCustomerAddressListValidate(operationModel, params);
    }

    @Override
    public PageVo<CustomerAddressVO> findCustomerAddressPageList(OperationModel operationModel, CustomerAddressQueryReq params, PageDto pageDTO) {
        ValidatorUtils.checkTrueThrowEx(StringUtils.isEmpty(params.getCustomerCode()), "客户编码不能为空");
        ValidatorUtils.checkTrueThrowEx(StringUtils.isEmpty(params.getUseOrgNo()), "使用组织编号不能为空");
        ValidatorUtils.checkTrueThrowEx(CollectionUtils.isEmpty(params.getAddressTypeList()), "地址类型不能为空");

        Page<CompanyShippingAddressV2> page = PageHelper.startPage(pageDTO.getPageIndex(), pageDTO.getPageSize(), StringUtils.isBlank(pageDTO.getOrderBy()) ? "create_time desc" : pageDTO.getOrderBy());

        companyShippingAddressV2DAO.selectList(Wrappers.<CompanyShippingAddressV2>lambdaQuery()
                .eq(CompanyShippingAddressV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CompanyShippingAddressV2::getUseOrgNo, params.getUseOrgNo())
                .eq(CompanyShippingAddressV2::getSourceNo, params.getCustomerCode())
                .eq(CompanyShippingAddressV2::getLinkaddType, LinkmanTypeEnum.SALE.getValue())
                .in(CompanyShippingAddressV2::getAddressType, params.getAddressTypeList())
                .eq(CompanyShippingAddressV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));

        return PageUtils.convertPageVo(page, data -> {
            List<AreaCodeVo> areaCodeList = dictEnterpriseService.findAreaByCodes(data.stream().map(CompanyShippingAddressV2::getRegionCode).collect(Collectors.toList()));
            final Map<String, AreaCodeVo> areaMap = areaCodeList.stream().collect(Collectors.toMap(AreaCodeVo::getAreaCode, Function.identity()));


            List<CustomerAddressVO> result = new ArrayList<>();

            for (CompanyShippingAddressV2 companyShippingAddressV2 : data) {
                CustomerAddressVO customerAddressVO = BeanUtil.copyFields(companyShippingAddressV2, CustomerAddressVO.class);

                // 补充区域全路径
                customerAddressVO.setRegionFullName(areaMap.getOrDefault(companyShippingAddressV2.getRegionCode(), new AreaCodeVo()).getAreaFullName());

                result.add(customerAddressVO);
            }
            return result;
        });
    }

    @Override
    public List<CustomerAddressVO> findCustomerAddressListByIds(String enterpriseNo, List<Long> addressIdList) {
        List<CompanyShippingAddressV2> companyShippingAddressV2List = companyShippingAddressV2DAO.selectList(Wrappers.<CompanyShippingAddressV2>lambdaQuery()
                .eq(CompanyShippingAddressV2::getEnterpriseNo, enterpriseNo)
                .in(CompanyShippingAddressV2::getId, addressIdList)
                .eq(CompanyShippingAddressV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));

        if (CollectionUtils.isEmpty(companyShippingAddressV2List)) {
            return Collections.emptyList();
        }


        List<AreaCodeVo> areaCodeList = dictEnterpriseService.findAreaByCodes(companyShippingAddressV2List.stream().map(CompanyShippingAddressV2::getRegionCode).collect(Collectors.toList()));
        final Map<String, AreaCodeVo> areaMap = areaCodeList.stream().collect(Collectors.toMap(AreaCodeVo::getAreaCode, Function.identity()));

        List<CustomerAddressVO> result = new ArrayList<>();
        for (CompanyShippingAddressV2 companyShippingAddressV2 : companyShippingAddressV2List) {
            CustomerAddressVO customerAddressVO = BeanUtil.copyFields(companyShippingAddressV2, CustomerAddressVO.class);

            // 补充区域全路径
            customerAddressVO.setRegionFullName(areaMap.getOrDefault(companyShippingAddressV2.getRegionCode(), new AreaCodeVo()).getAreaFullName());

            result.add(customerAddressVO);
        }

        return result;
    }

    private void editCustomerLinkmanListValidate(OperationModel operationModel, CustomerLinkmanListEditReq params) {
        if (CollectionUtils.isEmpty(params.getLinkmanList())) {
            return;
        }

        for (CompanyLinkmanReq companyLinkmanReq : params.getLinkmanList()) {
            //必填项校验
            ValidatorUtils.checkEmptyThrowEx(companyLinkmanReq.getLinkman(), "联系人不能为空");

            //范围校验
            ValidatorUtils.checkTrueThrowEx(companyLinkmanReq.getLinkman().length() > 50, "联系人不能大于50");

            if (StringUtils.isNotEmpty(companyLinkmanReq.getPosition())) {
                ValidatorUtils.checkTrueThrowEx(companyLinkmanReq.getPosition().length() > 100, "职位不能大于100");
            }

            if (StringUtils.isNotEmpty(companyLinkmanReq.getFixedPhone())) {
                ValidatorUtils.checkTrueThrowEx(companyLinkmanReq.getFixedPhone().length() > 100, "电话不能大于100");
            }

            if (StringUtils.isNotEmpty(companyLinkmanReq.getMobilePhone())) {
                ValidatorUtils.checkTrueThrowEx(companyLinkmanReq.getMobilePhone().length() > 100, "手机不能大于100");
            }

            if (StringUtils.isNotEmpty(companyLinkmanReq.getQq())) {
                ValidatorUtils.checkTrueThrowEx(companyLinkmanReq.getQq().length() > 100, "QQ不能大于100");
            }

            if (StringUtils.isNotEmpty(companyLinkmanReq.getWx())) {
                ValidatorUtils.checkTrueThrowEx(companyLinkmanReq.getWx().length() > 100, "微信不能大于100");
            }

            if (StringUtils.isNotEmpty(companyLinkmanReq.getEmail())) {
                ValidatorUtils.checkTrueThrowEx(companyLinkmanReq.getEmail().length() > 100, "邮箱不能大于100");
            }
        }
    }

    @Override
    @Transactional
    public List<Long> editCustomerLinkmanList(OperationModel operationModel, CustomerLinkmanListEditReq params) {
        // 参数校验
        editCustomerLinkmanListValidate(operationModel, params);

        CustomerV2 customer = customerV2DAO.selectOne(new LambdaQueryWrapper<CustomerV2>()
                .eq(CustomerV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CustomerV2::getCustomerCode, params.getCustomerCode())
                .eq(CustomerV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
        if (null == customer) {
            throw new BusinessException(ErrorCode.param_invalid_msg, "客户不存在");
        }

        boolean hasBase = customerBaseDAO.exists(new LambdaQueryWrapper<CustomerBase>()
                .eq(CustomerBase::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CustomerBase::getCustomerCode, params.getCustomerCode())
                .eq(CustomerBase::getUseOrgNo, params.getUseOrgNo())
                .eq(CustomerBase::getDeleted, DeletedEnum.UN_DELETE.getValue()));
        ValidatorUtils.checkTrueThrowEx(!hasBase, "客户未分派");


        List<CompanyLinkmanV2> companyLinkmanV2s = companyLinkmanV2DAO.selectList(Wrappers.<CompanyLinkmanV2>lambdaQuery()
                .eq(CompanyLinkmanV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CompanyLinkmanV2::getUseOrgNo, params.getUseOrgNo())
                .eq(CompanyLinkmanV2::getSourceNo, params.getCustomerCode())
                .eq(CompanyLinkmanV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
        Map<String, List<?>> diffResultMap = BeanUtil.diffCUD(companyLinkmanV2s, params.getLinkmanList());

        List<CompanyLinkmanReq> addList = (List<CompanyLinkmanReq>) diffResultMap.get("addList");
        List<CompanyLinkmanReq> updateList = (List<CompanyLinkmanReq>) diffResultMap.get("updateList");
        List<CompanyLinkmanV2> deleteList = (List<CompanyLinkmanV2>) diffResultMap.get("deleteList");


        List<CompanyLinkmanV2> companyLinkmanList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(addList)) {
            for (CompanyLinkmanReq r : addList) {
                //应该走新增逻辑
                CompanyLinkmanV2 companyLinkman = new CompanyLinkmanV2();
                companyLinkman.setEnterpriseNo(operationModel.getEnterpriseNo());
                companyLinkman.setUseOrgNo(params.getUseOrgNo());
//            companyLinkman.setCompanyNo(customer.getCompanyNo());
                companyLinkman.setLinkCode(r.getLinkCode());
                companyLinkman.setLinkman(r.getLinkman());
                companyLinkman.setPosition(r.getPosition());
                companyLinkman.setMobilePhone(r.getMobilePhone());
                companyLinkman.setSex(r.getSex());
                companyLinkman.setFixedPhone(r.getFixedPhone());
                companyLinkman.setQq(r.getQq());
                companyLinkman.setWx(r.getWx());
                companyLinkman.setLinkmanType(LinkmanTypeEnum.SALE.getValue());
                companyLinkman.setStatus(CommonStatusEnum.EFFECTIVE.getValue());
                companyLinkman.setSourceNo(params.getCustomerCode());
                companyLinkman.setEmail(r.getEmail());
                companyLinkman.setIsDefault(r.getIsDefault());
                companyLinkman.setStatus(r.getStatus());
                companyLinkmanList.add(companyLinkman);
            }

            companyLinkmanV2DAO.addBatch(companyLinkmanList);
        }

        if (CollectionUtils.isNotEmpty(updateList)) {
            List<CompanyLinkmanV2> companyLinkmanUpdateList = new ArrayList<>();

            for (CompanyLinkmanReq r : updateList) {
                CompanyLinkmanV2 companyLinkman = new CompanyLinkmanV2();
                companyLinkman.setId(r.getId());
                companyLinkman.setEnterpriseNo(operationModel.getEnterpriseNo());
                companyLinkman.setUseOrgNo(params.getUseOrgNo());
//            companyLinkman.setCompanyNo(customer.getCompanyNo());
                companyLinkman.setLinkCode(r.getLinkCode());
                companyLinkman.setLinkman(r.getLinkman());
                companyLinkman.setPosition(r.getPosition());
                companyLinkman.setMobilePhone(r.getMobilePhone());
                companyLinkman.setSex(r.getSex());
                companyLinkman.setFixedPhone(r.getFixedPhone());
                companyLinkman.setQq(r.getQq());
                companyLinkman.setWx(r.getWx());
                companyLinkman.setLinkmanType(LinkmanTypeEnum.SALE.getValue());
                companyLinkman.setStatus(CommonStatusEnum.EFFECTIVE.getValue());
                companyLinkman.setSourceNo(params.getCustomerCode());
                companyLinkman.setEmail(r.getEmail());
                companyLinkman.setIsDefault(r.getIsDefault());
                companyLinkman.setStatus(r.getStatus());

                companyLinkmanUpdateList.add(companyLinkman);
            }

            companyLinkmanV2DAO.updateByIdBatch(companyLinkmanUpdateList);
        }

        if (CollectionUtils.isNotEmpty(deleteList)) {
            CompanyLinkmanV2 linkman = new CompanyLinkmanV2();
            linkman.setDeleted(DeletedEnum.DELETED.getValue());
            companyLinkmanV2DAO.update(linkman, Wrappers.<CompanyLinkmanV2>lambdaQuery()
                    .eq(CompanyLinkmanV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                    .in(CompanyLinkmanV2::getId, deleteList.stream().map(CompanyLinkmanV2::getId).collect(Collectors.toList())));
        }

        return companyLinkmanList.stream().map(CompanyLinkmanV2::getId).collect(Collectors.toList());
    }

    @Override
    public List<CompanyLinkmanV2> overwriteCustomerLinkmanList(OperationModel operationModel, CustomerLinkmanListEditReq params) {
        CompanyLinkmanV2 toDeleteLinkman = new CompanyLinkmanV2();
        toDeleteLinkman.setDeleted(DeletedEnum.DELETED.getValue());
        companyLinkmanV2DAO.update(toDeleteLinkman, Wrappers.<CompanyLinkmanV2>lambdaQuery()
                .eq(CompanyLinkmanV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CompanyLinkmanV2::getUseOrgNo, params.getUseOrgNo())
                .eq(CompanyLinkmanV2::getSourceNo, params.getCustomerCode())
                .eq(CompanyLinkmanV2::getLinkmanType, LinkmanTypeEnum.SALE.getValue())
                .eq(CompanyLinkmanV2::getDeleted, DeletedEnum.UN_DELETE.getValue())
        );

        if (CollectionUtils.isNotEmpty(params.getLinkmanList())) {
            List<CompanyLinkmanV2> companyLinkmanList = new ArrayList<>();
            for (CompanyLinkmanReq r : params.getLinkmanList()) {
                CompanyLinkmanV2 companyLinkman = new CompanyLinkmanV2();
                companyLinkman.setEnterpriseNo(operationModel.getEnterpriseNo());
                companyLinkman.setUseOrgNo(params.getUseOrgNo());
//            companyLinkman.setCompanyNo(customer.getCompanyNo());
                companyLinkman.setLinkCode(r.getLinkCode());
                companyLinkman.setLinkman(r.getLinkman());
                companyLinkman.setPosition(r.getPosition());
                companyLinkman.setMobilePhone(r.getMobilePhone());
                companyLinkman.setSex(r.getSex());
                companyLinkman.setFixedPhone(r.getFixedPhone());
                companyLinkman.setQq(r.getQq());
                companyLinkman.setWx(r.getWx());
                companyLinkman.setLinkmanType(LinkmanTypeEnum.SALE.getValue());
                companyLinkman.setStatus(CommonStatusEnum.EFFECTIVE.getValue());
                companyLinkman.setSourceNo(params.getCustomerCode());
                companyLinkman.setEmail(r.getEmail());
                companyLinkman.setIsDefault(r.getIsDefault());
                companyLinkman.setStatus(r.getStatus());
                companyLinkmanList.add(companyLinkman);
            }

            companyLinkmanV2DAO.addBatch(companyLinkmanList);

            return companyLinkmanList;
        }

        return Collections.emptyList();
    }

    private void saveCustomerLinkmanListValidate(OperationModel operationModel, CustomerLinkmanListSaveReq params) {
        if (CollectionUtils.isEmpty(params.getLinkmanList())) {
            return;
        }

        for (CompanyLinkmanReq companyLinkmanReq : params.getLinkmanList()) {
            //必填项校验
            ValidatorUtils.checkEmptyThrowEx(companyLinkmanReq.getLinkman(), "联系人不能为空");

            //范围校验
            ValidatorUtils.checkTrueThrowEx(companyLinkmanReq.getLinkman().length() > 50, "联系人不能大于50");

            if (StringUtils.isNotEmpty(companyLinkmanReq.getPosition())) {
                ValidatorUtils.checkTrueThrowEx(companyLinkmanReq.getPosition().length() > 100, "职位不能大于100");
            }

            if (StringUtils.isNotEmpty(companyLinkmanReq.getFixedPhone())) {
                ValidatorUtils.checkTrueThrowEx(companyLinkmanReq.getFixedPhone().length() > 100, "电话不能大于100");
            }

            if (StringUtils.isNotEmpty(companyLinkmanReq.getMobilePhone())) {
                ValidatorUtils.checkTrueThrowEx(companyLinkmanReq.getMobilePhone().length() > 100, "手机不能大于100");
            }

            if (StringUtils.isNotEmpty(companyLinkmanReq.getQq())) {
                ValidatorUtils.checkTrueThrowEx(companyLinkmanReq.getQq().length() > 100, "QQ不能大于100");
            }

            if (StringUtils.isNotEmpty(companyLinkmanReq.getWx())) {
                ValidatorUtils.checkTrueThrowEx(companyLinkmanReq.getWx().length() > 100, "微信不能大于100");
            }

            if (StringUtils.isNotEmpty(companyLinkmanReq.getEmail())) {
                ValidatorUtils.checkTrueThrowEx(companyLinkmanReq.getEmail().length() > 100, "邮箱不能大于100");
            }
        }
    }

    @Override
    public List<Long> saveCustomerLinkmanList(OperationModel operationModel, CustomerLinkmanListSaveReq params) {
        // 参数校验
        saveCustomerLinkmanListValidate(operationModel, params);

        CustomerV2 customer = customerV2DAO.selectOne(new LambdaQueryWrapper<CustomerV2>()
                .eq(CustomerV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CustomerV2::getCustomerCode, params.getCustomerCode())
                .eq(CustomerV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
        if (null == customer) {
            throw new BusinessException(ErrorCode.param_invalid_msg, "客户不存在");
        }

        boolean hasBase = customerBaseDAO.exists(new LambdaQueryWrapper<CustomerBase>()
                .eq(CustomerBase::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CustomerBase::getCustomerCode, params.getCustomerCode())
                .eq(CustomerBase::getUseOrgNo, params.getUseOrgNo())
                .eq(CustomerBase::getDeleted, DeletedEnum.UN_DELETE.getValue()));
        ValidatorUtils.checkTrueThrowEx(!hasBase, "客户未分派");





        List<CompanyLinkmanV2> companyLinkmanList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(params.getLinkmanList())) {
            for (CompanyLinkmanReq r : params.getLinkmanList()) {
                //应该走新增逻辑
                CompanyLinkmanV2 companyLinkman = new CompanyLinkmanV2();
                companyLinkman.setEnterpriseNo(operationModel.getEnterpriseNo());
                companyLinkman.setUseOrgNo(params.getUseOrgNo());
//            companyLinkman.setCompanyNo(customer.getCompanyNo());
                companyLinkman.setLinkCode(r.getLinkCode());
                companyLinkman.setLinkman(r.getLinkman());
                companyLinkman.setPosition(r.getPosition());
                companyLinkman.setMobilePhone(r.getMobilePhone());
                companyLinkman.setSex(r.getSex());
                companyLinkman.setFixedPhone(r.getFixedPhone());
                companyLinkman.setQq(r.getQq());
                companyLinkman.setWx(r.getWx());
                companyLinkman.setLinkmanType(LinkmanTypeEnum.SALE.getValue());
                companyLinkman.setStatus(CommonStatusEnum.EFFECTIVE.getValue());
                companyLinkman.setSourceNo(params.getCustomerCode());
                companyLinkman.setEmail(r.getEmail());
                companyLinkman.setIsDefault(r.getIsDefault());
                companyLinkman.setStatus(r.getStatus());
                companyLinkmanList.add(companyLinkman);
            }

            companyLinkmanV2DAO.addBatch(companyLinkmanList);
        }



        return companyLinkmanList.stream().map(CompanyLinkmanV2::getId).collect(Collectors.toList());
    }

    private void updateCustomerLinkmanListValidate(OperationModel operationModel, CustomerLinkmanListUpdateReq params) {
        if (CollectionUtils.isEmpty(params.getLinkmanList())) {
            return;
        }

        for (CompanyLinkmanReq companyLinkmanReq : params.getLinkmanList()) {
            //必填项校验
            ValidatorUtils.checkEmptyThrowEx(companyLinkmanReq.getLinkman(), "联系人不能为空");

            //范围校验
            ValidatorUtils.checkTrueThrowEx(companyLinkmanReq.getLinkman().length() > 50, "联系人不能大于50");

            if (StringUtils.isNotEmpty(companyLinkmanReq.getPosition())) {
                ValidatorUtils.checkTrueThrowEx(companyLinkmanReq.getPosition().length() > 100, "职位不能大于100");
            }

            if (StringUtils.isNotEmpty(companyLinkmanReq.getFixedPhone())) {
                ValidatorUtils.checkTrueThrowEx(companyLinkmanReq.getFixedPhone().length() > 100, "电话不能大于100");
            }

            if (StringUtils.isNotEmpty(companyLinkmanReq.getMobilePhone())) {
                ValidatorUtils.checkTrueThrowEx(companyLinkmanReq.getMobilePhone().length() > 100, "手机不能大于100");
            }

            if (StringUtils.isNotEmpty(companyLinkmanReq.getQq())) {
                ValidatorUtils.checkTrueThrowEx(companyLinkmanReq.getQq().length() > 100, "QQ不能大于100");
            }

            if (StringUtils.isNotEmpty(companyLinkmanReq.getWx())) {
                ValidatorUtils.checkTrueThrowEx(companyLinkmanReq.getWx().length() > 100, "微信不能大于100");
            }

            if (StringUtils.isNotEmpty(companyLinkmanReq.getEmail())) {
                ValidatorUtils.checkTrueThrowEx(companyLinkmanReq.getEmail().length() > 100, "邮箱不能大于100");
            }
        }
    }

    @Override
    public Boolean updateCustomerLinkmanList(OperationModel operationModel, CustomerLinkmanListUpdateReq params) {
        // 参数校验
        updateCustomerLinkmanListValidate(operationModel, params);

        CustomerV2 customer = customerV2DAO.selectOne(new LambdaQueryWrapper<CustomerV2>()
                .eq(CustomerV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CustomerV2::getCustomerCode, params.getCustomerCode())
                .eq(CustomerV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
        if (null == customer) {
            throw new BusinessException(ErrorCode.param_invalid_msg, "客户不存在");
        }

        boolean hasBase = customerBaseDAO.exists(new LambdaQueryWrapper<CustomerBase>()
                .eq(CustomerBase::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CustomerBase::getCustomerCode, params.getCustomerCode())
                .eq(CustomerBase::getUseOrgNo, params.getUseOrgNo())
                .eq(CustomerBase::getDeleted, DeletedEnum.UN_DELETE.getValue()));
        ValidatorUtils.checkTrueThrowEx(!hasBase, "客户未分派");



        if (CollectionUtils.isNotEmpty(params.getLinkmanList())) {
            List<CompanyLinkmanV2> companyLinkmanUpdateList = new ArrayList<>();

            for (CompanyLinkmanReq r : params.getLinkmanList()) {
                CompanyLinkmanV2 companyLinkman = new CompanyLinkmanV2();
                companyLinkman.setId(r.getId());
                companyLinkman.setEnterpriseNo(operationModel.getEnterpriseNo());
                companyLinkman.setUseOrgNo(params.getUseOrgNo());
//            companyLinkman.setCompanyNo(customer.getCompanyNo());
                companyLinkman.setLinkCode(r.getLinkCode());
                companyLinkman.setLinkman(r.getLinkman());
                companyLinkman.setPosition(r.getPosition());
                companyLinkman.setMobilePhone(r.getMobilePhone());
                companyLinkman.setSex(r.getSex());
                companyLinkman.setFixedPhone(r.getFixedPhone());
                companyLinkman.setQq(r.getQq());
                companyLinkman.setWx(r.getWx());
                companyLinkman.setLinkmanType(LinkmanTypeEnum.SALE.getValue());
                companyLinkman.setStatus(CommonStatusEnum.EFFECTIVE.getValue());
                companyLinkman.setSourceNo(params.getCustomerCode());
                companyLinkman.setEmail(r.getEmail());
                companyLinkman.setIsDefault(r.getIsDefault());
                companyLinkman.setStatus(r.getStatus());

                companyLinkmanUpdateList.add(companyLinkman);
            }

            companyLinkmanV2DAO.updateByIdBatch(companyLinkmanUpdateList);
        }



        return true;
    }

    @Override
    public Boolean deleteCustomerLinkmanList(OperationModel operationModel, CustomerLinkmanListDeleteReq params) {

        CustomerV2 customer = customerV2DAO.selectOne(new LambdaQueryWrapper<CustomerV2>()
                .eq(CustomerV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CustomerV2::getCustomerCode, params.getCustomerCode())
                .eq(CustomerV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
        if (null == customer) {
            throw new BusinessException(ErrorCode.param_invalid_msg, "客户不存在");
        }

        boolean hasBase = customerBaseDAO.exists(new LambdaQueryWrapper<CustomerBase>()
                .eq(CustomerBase::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CustomerBase::getCustomerCode, params.getCustomerCode())
                .eq(CustomerBase::getUseOrgNo, params.getUseOrgNo())
                .eq(CustomerBase::getDeleted, DeletedEnum.UN_DELETE.getValue()));
        ValidatorUtils.checkTrueThrowEx(!hasBase, "客户未分派");




        if (CollectionUtils.isNotEmpty(params.getLinkmanList())) {
            CompanyLinkmanV2 linkman = new CompanyLinkmanV2();
            linkman.setDeleted(DeletedEnum.DELETED.getValue());
            companyLinkmanV2DAO.update(linkman, Wrappers.<CompanyLinkmanV2>lambdaQuery()
                    .eq(CompanyLinkmanV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                    .in(CompanyLinkmanV2::getId, params.getLinkmanList()));
        }

        return true;
    }

    @Override
    public void preValidateEditCustomerLinkmanList(OperationModel operationModel, CustomerLinkmanListEditReq params) {
        // 参数校验
        editCustomerLinkmanListValidate(operationModel, params);
    }

    private void editCustomerSalesmanListValidate(OperationModel operationModel, CustomerSalesmanListEditReq params) {
        if (CollectionUtils.isEmpty(params.getSalesManList())) {
            return;
        }

        for (CustomerSalesManReq customerSalesManReq : params.getSalesManList()) {
            //必填项校验
            ValidatorUtils.checkEmptyThrowEx(customerSalesManReq.getDeptNo(), "部门不能为空");
            ValidatorUtils.checkEmptyThrowEx(customerSalesManReq.getSalesManNo(), "员工不能为空");
            ValidatorUtils.checkEmptyThrowEx(customerSalesManReq.getOrderSpecialist(), "订单专员不能为空");
            ValidatorUtils.checkEmptyThrowEx(customerSalesManReq.getIsDefault(), "采购员不能为空");

            //范围校验
            if (StringUtils.isNotEmpty(customerSalesManReq.getSalesManName())) {
                ValidatorUtils.checkTrueThrowEx(customerSalesManReq.getSalesManName().length() > 255, "员工姓名不能大于255");
            }
            if (StringUtils.isNotEmpty(customerSalesManReq.getPost())) {
                ValidatorUtils.checkTrueThrowEx(customerSalesManReq.getPost().length() > 100, "职务不能大于100");
            }
        }
    }

    @Override
    @Transactional
    public List<Long> editCustomerSalesmanList(OperationModel operationModel, CustomerSalesmanListEditReq params) {
        // 参数校验
        editCustomerSalesmanListValidate(operationModel, params);

        CustomerV2 customer = customerV2DAO.selectOne(new LambdaQueryWrapper<CustomerV2>()
                .eq(CustomerV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CustomerV2::getCustomerCode, params.getCustomerCode())
                .eq(CustomerV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
        if (null == customer) {
            throw new BusinessException(ErrorCode.param_invalid_msg, "客户不存在");
        }

        boolean hasBase = customerBaseDAO.exists(new LambdaQueryWrapper<CustomerBase>()
                .eq(CustomerBase::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CustomerBase::getCustomerCode, params.getCustomerCode())
                .eq(CustomerBase::getUseOrgNo, params.getUseOrgNo())
                .eq(CustomerBase::getDeleted, DeletedEnum.UN_DELETE.getValue()));
        ValidatorUtils.checkTrueThrowEx(!hasBase, "客户未分派");


        List<CustomerSalesManV2> salesManV2s = customerSalesManV2DAO.selectList(Wrappers.<CustomerSalesManV2>lambdaQuery()
                .eq(CustomerSalesManV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CustomerSalesManV2::getUseOrgNo, params.getUseOrgNo())
                .eq(CustomerSalesManV2::getCustomerCode, params.getCustomerCode())
                .eq(CustomerSalesManV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
        Map<String, List<?>> diffResultMap = BeanUtil.diffCUD(salesManV2s, params.getSalesManList());

        List<CustomerSalesManReq> addList = (List<CustomerSalesManReq>) diffResultMap.get("addList");
        List<CustomerSalesManReq> updateList = (List<CustomerSalesManReq>) diffResultMap.get("updateList");
        List<CustomerSalesManV2> deleteList = (List<CustomerSalesManV2>) diffResultMap.get("deleteList");


        List<CustomerSalesManV2> customerSalesManList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(addList)) {
            for (CustomerSalesManReq r : addList) {
                CustomerSalesManV2 customerSalesMan = new CustomerSalesManV2();
                customerSalesMan.setEnterpriseNo(operationModel.getEnterpriseNo());
                customerSalesMan.setUseOrgNo(params.getUseOrgNo());
//                customerSalesMan.setCustomerNo(customer.getCustomerNo());
                customerSalesMan.setCustomerCode(params.getCustomerCode());
//                customerOrderMan.setManCode(UUID.randomUUID().toString());
                customerSalesMan.setSalesManNo(r.getSalesManNo());
                customerSalesMan.setSalesManName(r.getSalesManName());
                customerSalesMan.setDeptNo(r.getDeptNo());
                customerSalesMan.setDeptName(r.getDeptName());
                customerSalesMan.setPost(r.getPost());
                customerSalesMan.setIsDefault(r.getIsDefault());
                customerSalesMan.setOrderSpecialist(r.getOrderSpecialist());
                customerSalesMan.setPost(r.getPost());

                CommonUtil.fillCreatInfo(operationModel, customerSalesMan);

                customerSalesManList.add(customerSalesMan);
            }
            customerSalesManV2DAO.addBatch(customerSalesManList);
        }

        if (CollectionUtils.isNotEmpty(updateList)) {
            List<CustomerSalesManV2> customerSalesManUpdateList = new ArrayList<>();

            for (CustomerSalesManReq r : updateList) {
                CustomerSalesManV2 customerSalesMan = new CustomerSalesManV2();
                customerSalesMan.setId(r.getId());
                customerSalesMan.setEnterpriseNo(operationModel.getEnterpriseNo());
                customerSalesMan.setUseOrgNo(params.getUseOrgNo());
//                customerSalesMan.setCustomerNo(customer.getCustomerNo());
                customerSalesMan.setCustomerCode(params.getCustomerCode());
//                customerSalesMan.setManCode(UUID.randomUUID().toString());
                customerSalesMan.setSalesManNo(r.getSalesManNo());
                customerSalesMan.setSalesManName(r.getSalesManName());
                customerSalesMan.setDeptNo(r.getDeptNo());
                customerSalesMan.setDeptName(r.getDeptName());
                customerSalesMan.setPost(r.getPost());
                customerSalesMan.setIsDefault(r.getIsDefault());
                customerSalesMan.setOrderSpecialist(r.getOrderSpecialist());
                customerSalesMan.setPost(r.getPost());

                customerSalesManUpdateList.add(customerSalesMan);
            }
            customerSalesManV2DAO.updateByIdBatch(customerSalesManUpdateList);
        }

        if (CollectionUtils.isNotEmpty(deleteList)) {
            CustomerSalesManV2 orderMan = new CustomerSalesManV2();
            orderMan.setDeleted(DeletedEnum.DELETED.getValue());
            customerSalesManV2DAO.update(orderMan, Wrappers.<CustomerSalesManV2>lambdaQuery()
                    .eq(CustomerSalesManV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                    .in(CustomerSalesManV2::getId, deleteList.stream().map(CustomerSalesManV2::getId).collect(Collectors.toList())));
        }

        return customerSalesManList.stream().map(CustomerSalesManV2::getId).collect(Collectors.toList());
    }

    @Override
    public List<CustomerSalesManV2> overwriteCustomerSalesmanList(OperationModel operationModel, CustomerSalesmanListEditReq params) {
        CustomerSalesManV2 toDeleteOrderMan = new CustomerSalesManV2();
        toDeleteOrderMan.setDeleted(DeletedEnum.DELETED.getValue());
        customerSalesManV2DAO.update(toDeleteOrderMan, Wrappers.<CustomerSalesManV2>lambdaQuery()
                .eq(CustomerSalesManV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CustomerSalesManV2::getUseOrgNo, params.getUseOrgNo())
                .eq(CustomerSalesManV2::getCustomerCode, params.getCustomerCode())
                .eq(CustomerSalesManV2::getDeleted, DeletedEnum.UN_DELETE.getValue())
        );

        if (CollectionUtils.isNotEmpty(params.getSalesManList())) {
            List<CustomerSalesManV2> customerSalesManList = new ArrayList<>();
            for (CustomerSalesManReq r : params.getSalesManList()) {
                CustomerSalesManV2 customerSalesMan = new CustomerSalesManV2();
                customerSalesMan.setEnterpriseNo(operationModel.getEnterpriseNo());
                customerSalesMan.setUseOrgNo(params.getUseOrgNo());
//                customerSalesMan.setCustomerNo(customer.getCustomerNo());
                customerSalesMan.setCustomerCode(params.getCustomerCode());
//                customerOrderMan.setManCode(UUID.randomUUID().toString());
                customerSalesMan.setSalesManNo(r.getSalesManNo());
                customerSalesMan.setSalesManName(r.getSalesManName());
                customerSalesMan.setDeptNo(r.getDeptNo());
                customerSalesMan.setDeptName(r.getDeptName());
                customerSalesMan.setPost(r.getPost());
                customerSalesMan.setIsDefault(r.getIsDefault());
                customerSalesMan.setOrderSpecialist(r.getOrderSpecialist());
                customerSalesMan.setPost(r.getPost());

                CommonUtil.fillCreatInfo(operationModel, customerSalesMan);

                customerSalesManList.add(customerSalesMan);
            }
            customerSalesManV2DAO.addBatch(customerSalesManList);

            return customerSalesManList;
        }

        return Collections.emptyList();
    }

    @Override
    public void preValidateEditCustomerSalesmanList(OperationModel operationModel, CustomerSalesmanListEditReq params) {
        // 参数校验
        editCustomerSalesmanListValidate(operationModel, params);
    }

    @Override
    public List<CustomerSalesManVO> findCustomerSalesManList(OperationModel operationModel, CustomerSalesManQueryReq params) {
        ValidatorUtils.checkTrueThrowEx(StringUtils.isEmpty(params.getCustomerCode()), "客户编码不能为空");
        ValidatorUtils.checkTrueThrowEx(StringUtils.isEmpty(params.getUseOrgNo()), "组织编码不能为空");

        List<CustomerSalesManV2> customerSalesManV2s = customerSalesManV2DAO.selectList(Wrappers.<CustomerSalesManV2>lambdaQuery()
                .eq(CustomerSalesManV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CustomerSalesManV2::getCustomerCode, params.getCustomerCode())
                .eq(CustomerSalesManV2::getUseOrgNo, params.getUseOrgNo())
                .in(null != params.getIsDefault(), CustomerSalesManV2::getIsDefault, params.getIsDefault())
                .eq(CustomerSalesManV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));

        if (CollectionUtils.isEmpty(customerSalesManV2s)) {
            return Collections.emptyList();
        }

        List<String> salesManNoList = customerSalesManV2s.stream().map(CustomerSalesManV2::getSalesManNo).collect(Collectors.toList());
        List<EmployeeVo> employeeList = employeeService.getEmployeeList(operationModel.getEnterpriseNo(), salesManNoList);
        Map<String, EmployeeVo> employeeMap = employeeList.stream().collect(Collectors.toMap(EmployeeVo::getEmployeeNo, Function.identity()));

        List<CustomerSalesManVO> customerSalesManVOList = new ArrayList<>(customerSalesManV2s.size());
        for (CustomerSalesManV2 customerSalesManV2 : customerSalesManV2s) {
            CustomerSalesManVO customerSalesManVO = new CustomerSalesManVO();
            customerSalesManVO.setId(customerSalesManV2.getId());
            customerSalesManVO.setEnterpriseNo(customerSalesManV2.getEnterpriseNo());
            customerSalesManVO.setUseOrgNo(customerSalesManV2.getUseOrgNo());
            customerSalesManVO.setOrgNo(customerSalesManV2.getOrgNo());
            customerSalesManVO.setOrgName(customerSalesManV2.getOrgName());
            customerSalesManVO.setSalesManNo(customerSalesManV2.getSalesManNo());
            customerSalesManVO.setSalesManName(customerSalesManV2.getSalesManName());
            customerSalesManVO.setPost(customerSalesManV2.getPost());
            customerSalesManVO.setOrderSpecialist(customerSalesManV2.getOrderSpecialist());
            customerSalesManVO.setIsDefault(customerSalesManV2.getIsDefault());
            customerSalesManVO.setDeptNo(customerSalesManV2.getDeptNo());
            customerSalesManVO.setDeptName(customerSalesManV2.getDeptName());

            customerSalesManVO.setMobile(employeeMap.getOrDefault(customerSalesManV2.getSalesManNo(), new EmployeeVo()).getMobile());
            customerSalesManVO.setOrderSpecialistName(CommonIfEnum.getNameByValue(customerSalesManV2.getOrderSpecialist()));
            customerSalesManVO.setIsDefaultName(CommonIfEnum.getNameByValue(customerSalesManV2.getIsDefault()));

            customerSalesManVOList.add(customerSalesManVO);
        }


        return customerSalesManVOList;
    }

    @Override
    public PageVo<CustomerSalesManVO> findCustomerSalesManListPage(OperationModel operationModel, CustomerSalesManQueryReq params, PageDto pageDTO) {
        ValidatorUtils.checkTrueThrowEx(StringUtils.isEmpty(params.getCustomerCode()), "客户编码不能为空");
        ValidatorUtils.checkTrueThrowEx(StringUtils.isEmpty(params.getUseOrgNo()), "组织编码不能为空");

        Page<CustomerSalesManV2> page = PageHelper.startPage(pageDTO.getPageIndex(), pageDTO.getPageSize(), StringUtils.isBlank(pageDTO.getOrderBy()) ? "create_time desc" : pageDTO.getOrderBy());

        customerSalesManV2DAO.selectList(Wrappers.<CustomerSalesManV2>lambdaQuery()
                .eq(CustomerSalesManV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CustomerSalesManV2::getCustomerCode, params.getCustomerCode())
                .eq(CustomerSalesManV2::getUseOrgNo, params.getUseOrgNo())
                .in(null != params.getIsDefault(), CustomerSalesManV2::getIsDefault, params.getIsDefault())
                .eq(CustomerSalesManV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));

        return PageUtils.convertPageVo(page, data -> {
            List<String> salesManNoList = data.stream().map(CustomerSalesManV2::getSalesManNo).collect(Collectors.toList());
            List<EmployeeVo> employeeList = employeeService.getEmployeeList(operationModel.getEnterpriseNo(), salesManNoList);
            Map<String, EmployeeVo> employeeMap = employeeList.stream().collect(Collectors.toMap(EmployeeVo::getEmployeeNo, Function.identity()));

            List<CustomerSalesManVO> customerSalesManVOList = new ArrayList<>(data.size());
            for (CustomerSalesManV2 customerSalesManV2 : data) {
                CustomerSalesManVO customerSalesManVO = new CustomerSalesManVO();
                customerSalesManVO.setId(customerSalesManV2.getId());
                customerSalesManVO.setEnterpriseNo(customerSalesManV2.getEnterpriseNo());
                customerSalesManVO.setUseOrgNo(customerSalesManV2.getUseOrgNo());
                customerSalesManVO.setOrgNo(customerSalesManV2.getOrgNo());
                customerSalesManVO.setOrgName(customerSalesManV2.getOrgName());
                customerSalesManVO.setSalesManNo(customerSalesManV2.getSalesManNo());
                customerSalesManVO.setSalesManName(customerSalesManV2.getSalesManName());
                customerSalesManVO.setPost(customerSalesManV2.getPost());
                customerSalesManVO.setOrderSpecialist(customerSalesManV2.getOrderSpecialist());
                customerSalesManVO.setIsDefault(customerSalesManV2.getIsDefault());
                customerSalesManVO.setDeptNo(customerSalesManV2.getDeptNo());
                customerSalesManVO.setDeptName(customerSalesManV2.getDeptName());

                customerSalesManVO.setMobile(employeeMap.getOrDefault(customerSalesManV2.getSalesManNo(), new EmployeeVo()).getMobile());
                customerSalesManVO.setOrderSpecialistName(CommonIfEnum.getNameByValue(customerSalesManV2.getOrderSpecialist()));
                customerSalesManVO.setIsDefaultName(CommonIfEnum.getNameByValue(customerSalesManV2.getIsDefault()));

                customerSalesManVOList.add(customerSalesManVO);
            }


            return customerSalesManVOList;
        });
    }

    @Override
    public PageVo<CompanyLinkmanVO> findCustomerLinkmanListPage(OperationModel operationModel, CustomerLinkmanQueryReq params, PageDto pageDTO) {
        ValidatorUtils.checkTrueThrowEx(StringUtils.isEmpty(params.getCustomerCode()), "客户编码不能为空");
        ValidatorUtils.checkTrueThrowEx(StringUtils.isEmpty(params.getUseOrgNo()), "组织编码不能为空");

        Page<CompanyLinkmanV2> page = PageHelper.startPage(pageDTO.getPageIndex(), pageDTO.getPageSize(), StringUtils.isBlank(pageDTO.getOrderBy()) ? "create_time desc" : pageDTO.getOrderBy());

        companyLinkmanV2DAO.selectList(Wrappers.<CompanyLinkmanV2>lambdaQuery()
                .eq(CompanyLinkmanV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CompanyLinkmanV2::getSourceNo, params.getCustomerCode())
                .eq(CompanyLinkmanV2::getUseOrgNo, params.getUseOrgNo())
                .eq(CompanyLinkmanV2::getStatus, 1)
                .eq(CompanyLinkmanV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));

        return PageUtils.convertPageVo(page, data -> {

            List<CompanyLinkmanVO> customerLinkmanVOList = new ArrayList<>(data.size());
            for (CompanyLinkmanV2 companyLinkmanV2 : data) {
                CompanyLinkmanVO companyLinkmanVO = new CompanyLinkmanVO();
                companyLinkmanVO.setId(companyLinkmanV2.getId());
                companyLinkmanVO.setLinkman(companyLinkmanV2.getLinkman());
                companyLinkmanVO.setPosition(companyLinkmanV2.getPosition());
                companyLinkmanVO.setMobilePhone(companyLinkmanV2.getMobilePhone());
                companyLinkmanVO.setFixedPhone(companyLinkmanV2.getFixedPhone());
                companyLinkmanVO.setSex(companyLinkmanV2.getSex());
                companyLinkmanVO.setSexName(null != SexEnum.getByType(companyLinkmanV2.getSex()) ? SexEnum.getByType(companyLinkmanV2.getSex()).getName() : "");
                companyLinkmanVO.setEmail(companyLinkmanV2.getEmail());
                companyLinkmanVO.setQq(companyLinkmanV2.getQq());
                companyLinkmanVO.setWx(companyLinkmanV2.getWx());
                companyLinkmanVO.setStatus(companyLinkmanV2.getStatus());
                customerLinkmanVOList.add(companyLinkmanVO);
            }

            return customerLinkmanVOList;
        });
    }

    @Override
    public List<CompanyLinkmanVO> findCustomerLinkmanList(OperationModel operationModel, CustomerLinkmanQueryReq params) {
        ValidatorUtils.checkTrueThrowEx(StringUtils.isEmpty(params.getCustomerCode()), "客户编码不能为空");
        ValidatorUtils.checkTrueThrowEx(StringUtils.isEmpty(params.getUseOrgNo()), "组织编码不能为空");


        List<CompanyLinkmanV2> companyLinkmanV2List = companyLinkmanV2DAO.selectList(Wrappers.<CompanyLinkmanV2>lambdaQuery()
                .eq(CompanyLinkmanV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CompanyLinkmanV2::getSourceNo, params.getCustomerCode())
                .eq(CompanyLinkmanV2::getUseOrgNo, params.getUseOrgNo())
                .eq(CompanyLinkmanV2::getStatus, 1)
                .eq(CompanyLinkmanV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));

        if (CollectionUtils.isEmpty(companyLinkmanV2List)) {
            return Collections.emptyList();
        }

        List<CompanyLinkmanVO> customerLinkmanVOList = new ArrayList<>(companyLinkmanV2List.size());

        for (CompanyLinkmanV2 companyLinkmanV2 : companyLinkmanV2List) {
            CompanyLinkmanVO companyLinkmanVO = new CompanyLinkmanVO();
            companyLinkmanVO.setId(companyLinkmanV2.getId());
            companyLinkmanVO.setLinkman(companyLinkmanV2.getLinkman());
            companyLinkmanVO.setPosition(companyLinkmanV2.getPosition());
            companyLinkmanVO.setMobilePhone(companyLinkmanV2.getMobilePhone());
            companyLinkmanVO.setFixedPhone(companyLinkmanV2.getFixedPhone());
            companyLinkmanVO.setSex(companyLinkmanV2.getSex());
            companyLinkmanVO.setSexName(null != SexEnum.getByType(companyLinkmanV2.getSex()) ? SexEnum.getByType(companyLinkmanV2.getSex()).getName() : "");
            companyLinkmanVO.setEmail(companyLinkmanV2.getEmail());
            companyLinkmanVO.setQq(companyLinkmanV2.getQq());
            companyLinkmanVO.setWx(companyLinkmanV2.getWx());
            companyLinkmanVO.setStatus(companyLinkmanV2.getStatus());
            customerLinkmanVOList.add(companyLinkmanVO);
        }

        return customerLinkmanVOList;
    }

    @Override
    public List<CompanyLinkmanVO> findCustomerLinkmanList(String enterpriseNo, CustomerLinkmanQueryReq params) {
        ValidatorUtils.checkTrueThrowEx(StringUtils.isEmpty(params.getCustomerCode()), "客户编码不能为空");
        ValidatorUtils.checkTrueThrowEx(StringUtils.isEmpty(params.getUseOrgNo()), "组织编码不能为空");


        List<CompanyLinkmanV2> companyLinkmanV2List = companyLinkmanV2DAO.selectList(Wrappers.<CompanyLinkmanV2>lambdaQuery()
                .eq(CompanyLinkmanV2::getEnterpriseNo, enterpriseNo)
                .eq(CompanyLinkmanV2::getSourceNo, params.getCustomerCode())
                .eq(CompanyLinkmanV2::getUseOrgNo, params.getUseOrgNo())
                .eq(CompanyLinkmanV2::getStatus, 1)
                .eq(CompanyLinkmanV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));

        if (CollectionUtils.isEmpty(companyLinkmanV2List)) {
            return Collections.emptyList();
        }

        List<CompanyLinkmanVO> customerLinkmanVOList = new ArrayList<>(companyLinkmanV2List.size());

        for (CompanyLinkmanV2 companyLinkmanV2 : companyLinkmanV2List) {
            CompanyLinkmanVO companyLinkmanVO = new CompanyLinkmanVO();
            companyLinkmanVO.setId(companyLinkmanV2.getId());
            companyLinkmanVO.setLinkman(companyLinkmanV2.getLinkman());
            companyLinkmanVO.setPosition(companyLinkmanV2.getPosition());
            companyLinkmanVO.setMobilePhone(companyLinkmanV2.getMobilePhone());
            companyLinkmanVO.setFixedPhone(companyLinkmanV2.getFixedPhone());
            companyLinkmanVO.setSex(companyLinkmanV2.getSex());
            companyLinkmanVO.setSexName(null != SexEnum.getByType(companyLinkmanV2.getSex()) ? SexEnum.getByType(companyLinkmanV2.getSex()).getName() : "");
            companyLinkmanVO.setEmail(companyLinkmanV2.getEmail());
            companyLinkmanVO.setQq(companyLinkmanV2.getQq());
            companyLinkmanVO.setWx(companyLinkmanV2.getWx());
            companyLinkmanVO.setStatus(companyLinkmanV2.getStatus());
            customerLinkmanVOList.add(companyLinkmanVO);
        }

        return customerLinkmanVOList;
    }

    @Override
    public List<CompanyLinkmanVO> findCustomerLinkmanListByIds(String enterpriseNo, List<Long> linkmanIdList) {
        List<CompanyLinkmanV2> companyLinkmanV2List = companyLinkmanV2DAO.selectList(Wrappers.<CompanyLinkmanV2>lambdaQuery()
                .eq(CompanyLinkmanV2::getEnterpriseNo, enterpriseNo)
                .in(CompanyLinkmanV2::getId, linkmanIdList)
                .eq(CompanyLinkmanV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));

        if (CollectionUtils.isEmpty(companyLinkmanV2List)) {
            return Collections.emptyList();
        }

        List<CompanyLinkmanVO> result = new ArrayList<>();
        for (CompanyLinkmanV2 companyLinkmanV2 : companyLinkmanV2List) {
            CompanyLinkmanVO companyLinkmanVO = new CompanyLinkmanVO();
            companyLinkmanVO.setId(companyLinkmanV2.getId());
            companyLinkmanVO.setLinkman(companyLinkmanV2.getLinkman());
            companyLinkmanVO.setPosition(companyLinkmanV2.getPosition());
            companyLinkmanVO.setMobilePhone(companyLinkmanV2.getMobilePhone());
            companyLinkmanVO.setFixedPhone(companyLinkmanV2.getFixedPhone());
            companyLinkmanVO.setSex(companyLinkmanV2.getSex());
            companyLinkmanVO.setSexName(null != SexEnum.getByType(companyLinkmanV2.getSex()) ? SexEnum.getByType(companyLinkmanV2.getSex()).getName() : "");
            companyLinkmanVO.setEmail(companyLinkmanV2.getEmail());
            companyLinkmanVO.setQq(companyLinkmanV2.getQq());
            companyLinkmanVO.setWx(companyLinkmanV2.getWx());
            companyLinkmanVO.setStatus(companyLinkmanV2.getStatus());

            result.add(companyLinkmanVO);
        }

        return result;
    }

    @Override
    public List<CustomerSalesManVO> findCustomerSalesManByIds(String enterpriseNo, List<Long> salesManIdList) {
        List<CustomerSalesManV2> customerSalesManV2List = customerSalesManV2DAO.selectList(Wrappers.<CustomerSalesManV2>lambdaQuery()
                .eq(CustomerSalesManV2::getEnterpriseNo, enterpriseNo)
                .in(CustomerSalesManV2::getId, salesManIdList)
                .eq(CustomerSalesManV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
        if (CollectionUtils.isEmpty(customerSalesManV2List)) {
            return Collections.emptyList();
        }

        List<EmployeeVo> employeeList = employeeService.getEmployeeList(enterpriseNo, customerSalesManV2List.stream().map(CustomerSalesManV2::getSalesManNo).collect(Collectors.toList()));
        Map<String, EmployeeVo> employeeMap = employeeList.stream().collect(Collectors.toMap(EmployeeVo::getEmployeeNo, Function.identity()));


        List<CustomerSalesManVO> result = new ArrayList<>(customerSalesManV2List.size());
        for (CustomerSalesManV2 customerSalesManV2 : customerSalesManV2List) {
            CustomerSalesManVO customerSalesManVO = BeanUtil.copyFields(customerSalesManV2, CustomerSalesManVO.class);

            // 填充名字信息
            customerSalesManVO.setMobile(employeeMap.getOrDefault(customerSalesManV2.getSalesManNo(), new EmployeeVo()).getMobile());
            customerSalesManVO.setOrderSpecialistName(CommonIfEnum.getNameByValue(customerSalesManV2.getOrderSpecialist()));
            customerSalesManVO.setIsDefaultName(CommonIfEnum.getNameByValue(customerSalesManV2.getIsDefault()));

            result.add(customerSalesManVO);
        }

        return result;
    }

    @Override
    public List<CustomerInvoiceVO> findCustomerInvoiceList(String enterpriseNo, CustomerInvoiceQueryReq params) {
        ValidatorUtils.checkTrueThrowEx(StringUtils.isEmpty(params.getCustomerCode()), "客户编码不能为空");
        ValidatorUtils.checkTrueThrowEx(StringUtils.isEmpty(params.getUseOrgNo()), "组织编码不能为空");

        List<CustomerInvoiceV2> customerInvoiceV2List = customerInvoiceV2DAO.selectList(Wrappers.<CustomerInvoiceV2>lambdaQuery()
                .eq(CustomerInvoiceV2::getEnterpriseNo, enterpriseNo)
                .eq(CustomerInvoiceV2::getCustomerCode, params.getCustomerCode())
                .eq(CustomerInvoiceV2::getUseOrgNo, params.getUseOrgNo())
                .eq(CustomerInvoiceV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));

        if (CollectionUtils.isEmpty(customerInvoiceV2List)) {
            return Collections.emptyList();
        }

        Map<String, OrganizationVo> orgNo2OrganizationVoMap = new HashMap<>();
        List<String> useOrgNoList = customerInvoiceV2List.stream().map(CustomerInvoiceV2::getUseOrgNo).collect(Collectors.toList());
        List<OrganizationVo> organizationVoList = organizationService.findListNoAuth(enterpriseNo, useOrgNoList);
        if (CollectionUtils.isNotEmpty(organizationVoList)) {
            orgNo2OrganizationVoMap = organizationVoList.stream().collect(Collectors.toMap(OrganizationVo::getOrgNo, Function.identity()));
        }

        List<CustomerInvoiceVO> customerInvoiceVOList = new ArrayList<>(customerInvoiceV2List.size());
        for (CustomerInvoiceV2 customerInvoiceV2 : customerInvoiceV2List) {
            CustomerInvoiceVO customerInvoiceVO = BeanUtil.copyFields(customerInvoiceV2, CustomerInvoiceVO.class);

            if (orgNo2OrganizationVoMap.containsKey(customerInvoiceV2.getUseOrgNo())) {
                customerInvoiceVO.setUseOrgName(orgNo2OrganizationVoMap.get(customerInvoiceV2.getUseOrgNo()).getOrgName());
            }

            if (StringUtils.isNotBlank(customerInvoiceV2.getStatus())) {
                customerInvoiceVO.setStatus(Integer.valueOf(customerInvoiceV2.getStatus()));
            }
            customerInvoiceVO.setTypeName(InvoiceTypeEnum.getByType(customerInvoiceV2.getType()) == null ? null : InvoiceTypeEnum.getByType(customerInvoiceV2.getType()).getName());
            customerInvoiceVO.setIsDefaultName(CommonIfEnum.getByValue(customerInvoiceV2.getIsDefault()) == null ? null : CommonIfEnum.getByValue(customerInvoiceV2.getIsDefault()).getName());

            customerInvoiceVOList.add(customerInvoiceVO);
        }


        return customerInvoiceVOList;
    }


    private void editCustomerInvoiceListValidate(OperationModel operationModel, CustomerInvoiceListEditReq params) {
        if (CollectionUtils.isEmpty(params.getInvoiceList())) {
            return;
        }

        for (CustomerInvoiceReq customerInvoiceReq : params.getInvoiceList()) {
            //必填项校验
            ValidatorUtils.checkEmptyThrowEx(customerInvoiceReq.getType(), "发票类型不能为空");
            ValidatorUtils.checkEmptyThrowEx(customerInvoiceReq.getInvoiceTitle(), "开票名称不能为空");
            ValidatorUtils.checkEmptyThrowEx(customerInvoiceReq.getIsDefault(), "是否默认不能为空");

            //范围校验
            if (StringUtils.isNotEmpty(customerInvoiceReq.getInvoiceTitle())) {
                ValidatorUtils.checkTrueThrowEx(customerInvoiceReq.getInvoiceTitle().length() > 128, "开票名称不能大于128");
            }
            if (StringUtils.isNotEmpty(customerInvoiceReq.getTaxNo())) {
                ValidatorUtils.checkTrueThrowEx(customerInvoiceReq.getTaxNo().length() > 100, "纳税人识别号不能大于100");
            }
            if (StringUtils.isNotEmpty(customerInvoiceReq.getAddress())) {
                ValidatorUtils.checkTrueThrowEx(customerInvoiceReq.getAddress().length() > 300, "地址不能大于300");
            }
            if (StringUtils.isNotEmpty(customerInvoiceReq.getPhone())) {
                ValidatorUtils.checkTrueThrowEx(customerInvoiceReq.getPhone().length() > 100, "电话不能大于100");
            }
            if (StringUtils.isNotEmpty(customerInvoiceReq.getBankDeposit())) {
                ValidatorUtils.checkTrueThrowEx(customerInvoiceReq.getBankDeposit().length() > 255, "开户行不能大于255");
            }
            if (StringUtils.isNotEmpty(customerInvoiceReq.getBankAccount())) {
                ValidatorUtils.checkTrueThrowEx(customerInvoiceReq.getBankAccount().length() > 100, "开户账号不能大于100");
            }
            if (StringUtils.isNotEmpty(customerInvoiceReq.getInvoicePhone())) {
                ValidatorUtils.checkTrueThrowEx(customerInvoiceReq.getInvoicePhone().length() > 100, "收票手机号不能大于100");
            }
            if (StringUtils.isNotEmpty(customerInvoiceReq.getEmail())) {
                ValidatorUtils.checkTrueThrowEx(customerInvoiceReq.getEmail().length() > 100, "收票邮箱不能大于100");
            }
            if (StringUtils.isNotEmpty(customerInvoiceReq.getRequirement())) {
                ValidatorUtils.checkTrueThrowEx(customerInvoiceReq.getRequirement().length() > 500, "客户开票要求不能大于500");
            }
        }
    }

    @Override
    @Transactional
    public List<Long> editCustomerInvoiceList(OperationModel operationModel, CustomerInvoiceListEditReq params) {
        // 参数校验
        editCustomerInvoiceListValidate(operationModel, params);

        CustomerV2 customer = customerV2DAO.selectOne(new LambdaQueryWrapper<CustomerV2>()
                .eq(CustomerV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CustomerV2::getCustomerCode, params.getCustomerCode())
                .eq(CustomerV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
        if (null == customer) {
            throw new BusinessException(ErrorCode.param_invalid_msg, "客户不存在");
        }

        boolean hasBase = customerBaseDAO.exists(new LambdaQueryWrapper<CustomerBase>()
                .eq(CustomerBase::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CustomerBase::getCustomerCode, params.getCustomerCode())
                .eq(CustomerBase::getUseOrgNo, params.getUseOrgNo())
                .eq(CustomerBase::getDeleted, DeletedEnum.UN_DELETE.getValue()));
        ValidatorUtils.checkTrueThrowEx(!hasBase, "客户未分派");


        List<CustomerInvoiceV2> salesInvoiceV2s = customerInvoiceV2DAO.selectList(Wrappers.<CustomerInvoiceV2>lambdaQuery()
                .eq(CustomerInvoiceV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CustomerInvoiceV2::getUseOrgNo, params.getUseOrgNo())
                .eq(CustomerInvoiceV2::getCustomerCode, params.getCustomerCode())
                .eq(CustomerInvoiceV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
        Map<String, List<?>> diffResultMap = BeanUtil.diffCUD(salesInvoiceV2s, params.getInvoiceList());

        List<CustomerInvoiceReq> addList = (List<CustomerInvoiceReq>) diffResultMap.get("addList");
        List<CustomerInvoiceReq> updateList = (List<CustomerInvoiceReq>) diffResultMap.get("updateList");
        List<CustomerInvoiceV2> deleteList = (List<CustomerInvoiceV2>) diffResultMap.get("deleteList");


        List<CustomerInvoiceV2> customerInvoiceList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(addList)) {
            for (CustomerInvoiceReq r : addList) {
                CustomerInvoiceV2 customerInvoiceV2 = new CustomerInvoiceV2();
                customerInvoiceV2.setEnterpriseNo(operationModel.getEnterpriseNo());
                customerInvoiceV2.setUseOrgNo(params.getUseOrgNo());
//                customerInvoiceV2.setCustomerNo(customer.getCustomerNo());
                customerInvoiceV2.setCustomerCode(params.getCustomerCode());
                customerInvoiceV2.setInvoiceCode(r.getInvoiceCode());
                customerInvoiceV2.setType(r.getType());
                customerInvoiceV2.setTaxNo(r.getTaxNo());
                customerInvoiceV2.setInvoiceTitle(r.getInvoiceTitle());
                customerInvoiceV2.setPhone(r.getPhone());
                customerInvoiceV2.setInvoicePhone(r.getInvoicePhone());
                customerInvoiceV2.setEmail(r.getEmail());
//                customerInvoiceV2.setRegionCode();
//                customerInvoiceV2.setRegionName();
                customerInvoiceV2.setAddress(r.getAddress());
                customerInvoiceV2.setBankDeposit(r.getBankDeposit());
                customerInvoiceV2.setBankAccount(r.getBankAccount());
                customerInvoiceV2.setIsDefault(r.getIsDefault());
//                customerInvoiceV2.setOpType();
//                customerInvoiceV2.setAddregionCode();
                customerInvoiceV2.setRequirement(r.getRequirement());
//                customerInvoiceV2.setOutSystemId();

                CommonUtil.fillCreatInfo(operationModel, customerInvoiceV2);

                customerInvoiceList.add(customerInvoiceV2);
            }
            customerInvoiceV2DAO.addBatch(customerInvoiceList);
        }

        if (CollectionUtils.isNotEmpty(updateList)) {
            List<CustomerInvoiceV2> customerInvoiceUpdateList = new ArrayList<>();

            for (CustomerInvoiceReq r : updateList) {
                CustomerInvoiceV2 customerInvoiceV2 = new CustomerInvoiceV2();
                customerInvoiceV2.setId(r.getId());
                customerInvoiceV2.setEnterpriseNo(operationModel.getEnterpriseNo());
                customerInvoiceV2.setUseOrgNo(params.getUseOrgNo());
//                customerInvoiceV2.setCustomerNo(customer.getCustomerNo());
                customerInvoiceV2.setCustomerCode(params.getCustomerCode());
                customerInvoiceV2.setInvoiceCode(r.getInvoiceCode());
                customerInvoiceV2.setType(r.getType());
                customerInvoiceV2.setTaxNo(r.getTaxNo());
                customerInvoiceV2.setInvoiceTitle(r.getInvoiceTitle());
                customerInvoiceV2.setPhone(r.getPhone());
                customerInvoiceV2.setInvoicePhone(r.getInvoicePhone());
                customerInvoiceV2.setEmail(r.getEmail());
//                customerInvoiceV2.setRegionCode();
//                customerInvoiceV2.setRegionName();
                customerInvoiceV2.setAddress(r.getAddress());
                customerInvoiceV2.setBankDeposit(r.getBankDeposit());
                customerInvoiceV2.setBankAccount(r.getBankAccount());
                customerInvoiceV2.setIsDefault(r.getIsDefault());
//                customerInvoiceV2.setOpType();
//                customerInvoiceV2.setAddregionCode();
                customerInvoiceV2.setRequirement(r.getRequirement());
//                customerInvoiceV2.setOutSystemId();

                customerInvoiceUpdateList.add(customerInvoiceV2);
            }
            customerInvoiceV2DAO.updateByIdBatch(customerInvoiceUpdateList);
        }

        if (CollectionUtils.isNotEmpty(deleteList)) {
            CustomerInvoiceV2 customerInvoiceV2 = new CustomerInvoiceV2();
            customerInvoiceV2.setDeleted(DeletedEnum.DELETED.getValue());
            customerInvoiceV2DAO.update(customerInvoiceV2, Wrappers.<CustomerInvoiceV2>lambdaQuery()
                    .eq(CustomerInvoiceV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                    .in(CustomerInvoiceV2::getId, deleteList.stream().map(CustomerInvoiceV2::getId).collect(Collectors.toList())));
        }

        return customerInvoiceList.stream().map(CustomerInvoiceV2::getId).collect(Collectors.toList());
    }

    @Override
    public List<CustomerInvoiceV2> overwriteCustomerInvoiceList(OperationModel operationModel, CustomerInvoiceListEditReq params) {
        CustomerInvoiceV2 toDeleteCustomerInvoiceV2 = new CustomerInvoiceV2();
        toDeleteCustomerInvoiceV2.setDeleted(DeletedEnum.DELETED.getValue());
        customerInvoiceV2DAO.update(toDeleteCustomerInvoiceV2, Wrappers.<CustomerInvoiceV2>lambdaQuery()
                .eq(CustomerInvoiceV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CustomerInvoiceV2::getUseOrgNo, params.getUseOrgNo())
                .eq(CustomerInvoiceV2::getCustomerCode, params.getCustomerCode())
                .eq(CustomerInvoiceV2::getDeleted, DeletedEnum.UN_DELETE.getValue())
        );

        if (CollectionUtils.isNotEmpty(params.getInvoiceList())) {
            List<CustomerInvoiceV2> customerInvoiceList = new ArrayList<>();
            for (CustomerInvoiceReq r : params.getInvoiceList()) {
                CustomerInvoiceV2 customerInvoiceV2 = new CustomerInvoiceV2();
                customerInvoiceV2.setEnterpriseNo(operationModel.getEnterpriseNo());
                customerInvoiceV2.setUseOrgNo(params.getUseOrgNo());
//                customerInvoiceV2.setCustomerNo(customer.getCustomerNo());
                customerInvoiceV2.setCustomerCode(params.getCustomerCode());
                customerInvoiceV2.setInvoiceCode(r.getInvoiceCode());
                customerInvoiceV2.setType(r.getType());
                customerInvoiceV2.setTaxNo(r.getTaxNo());
                customerInvoiceV2.setInvoiceTitle(r.getInvoiceTitle());
                customerInvoiceV2.setPhone(r.getPhone());
                customerInvoiceV2.setInvoicePhone(r.getInvoicePhone());
                customerInvoiceV2.setEmail(r.getEmail());
//                customerInvoiceV2.setRegionCode();
//                customerInvoiceV2.setRegionName();
                customerInvoiceV2.setAddress(r.getAddress());
                customerInvoiceV2.setBankDeposit(r.getBankDeposit());
                customerInvoiceV2.setBankAccount(r.getBankAccount());
                customerInvoiceV2.setIsDefault(r.getIsDefault());
//                customerInvoiceV2.setOpType();
//                customerInvoiceV2.setAddregionCode();
                customerInvoiceV2.setRequirement(r.getRequirement());
//                customerInvoiceV2.setOutSystemId();

                CommonUtil.fillCreatInfo(operationModel, customerInvoiceV2);

                customerInvoiceList.add(customerInvoiceV2);
            }
            customerInvoiceV2DAO.addBatch(customerInvoiceList);

            return customerInvoiceList;
        }

        return Collections.emptyList();
    }

    @Override
    public void preValidateEditCustomerInvoiceList(OperationModel operationModel, CustomerInvoiceListEditReq params) {
        // 参数校验
        editCustomerInvoiceListValidate(operationModel, params);
    }

    private void editCustomerBankListValidate(OperationModel operationModel, CustomerBankListEditReq params) {
        if (CollectionUtils.isEmpty(params.getBankList())) {
            return;
        }

        for (CompanyBankReq companyBankReq : params.getBankList()) {
            //必填项校验
            ValidatorUtils.checkEmptyThrowEx(companyBankReq.getBankType(), "银行类别不能为空");
            ValidatorUtils.checkEmptyThrowEx(companyBankReq.getOpenBank(), "开户银行不能为空");
            ValidatorUtils.checkEmptyThrowEx(companyBankReq.getAccountName(), "户名不能为空");
            ValidatorUtils.checkEmptyThrowEx(companyBankReq.getAccountNo(), "账户不能为空");
            ValidatorUtils.checkEmptyThrowEx(companyBankReq.getAccountType(), "账户性质不能为空");
            ValidatorUtils.checkEmptyThrowEx(companyBankReq.getIsDefault(), "是否默认不能为空");
            ValidatorUtils.checkEmptyThrowEx(companyBankReq.getStatus(), "状态不能为空");

            //范围校验
            ValidatorUtils.checkTrueThrowEx(companyBankReq.getOpenBank().length() > 100, "开户银行不能大于100");
            ValidatorUtils.checkTrueThrowEx(companyBankReq.getAccountName().length() > 100, "户名不能大于100");
            ValidatorUtils.checkTrueThrowEx(companyBankReq.getAccountNo().length() > 100, "账户不能大于100");
            if (StringUtils.isNotEmpty(companyBankReq.getLinkPerson())) {
                ValidatorUtils.checkTrueThrowEx(companyBankReq.getLinkPerson().length() > 100, "联系人不能大于100");
            }
            if (StringUtils.isNotEmpty(companyBankReq.getLinkPhone())) {
                ValidatorUtils.checkTrueThrowEx(companyBankReq.getLinkPhone().length() > 100, "联系电话不能大于100");
            }
        }
    }

    @Override
    @Transactional
    public List<Long> editCustomerBankList(OperationModel operationModel, CustomerBankListEditReq params) {
        // 参数校验
        editCustomerBankListValidate(operationModel, params);

        CustomerV2 customer = customerV2DAO.selectOne(new LambdaQueryWrapper<CustomerV2>()
                .eq(CustomerV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CustomerV2::getManageOrgNo, params.getManageOrgNo())
                .eq(CustomerV2::getCustomerCode, params.getCustomerCode())
                .eq(CustomerV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
        if (null == customer) {
            throw new BusinessException(ErrorCode.param_invalid_msg, "客户不存在");
        }

        CompanyV2 company = companyV2Service.findByEnterpriseNoAndCompanyNo(operationModel.getEnterpriseNo(), customer.getCompanyNo());

        Map<String, List<?>> bankDiffMap = companyV2Service.saveOrUpdateBank(operationModel, params.getBankList(), company.getCompanyNo(), params.getManageOrgNo());
        List<CompanyBankV2> addedList = (List<CompanyBankV2>) bankDiffMap.get("addedList");
        if (CollectionUtils.isEmpty(addedList)) {
            return Collections.emptyList();
        }

        return addedList.stream().map(CompanyBankV2::getId).collect(Collectors.toList());
    }

    @Override
    public void preValidateEditCustomerBankList(OperationModel operationModel, CustomerBankListEditReq params) {
        // 参数校验
        editCustomerBankListValidate(operationModel, params);
    }

    @Override
    public List<CustomerV2> findByInternalOrgNo(OperationModel operationModel, String customerCode, String internalOrgNo) {
        return customerV2DAO.selectList(new LambdaQueryWrapper<CustomerV2>()
                .eq(CustomerV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .ne(StringUtils.isNotEmpty(customerCode), CustomerV2::getCustomerCode, customerCode)
                .eq(CustomerV2::getIsAssociatedEnterprise, CommonIfEnum.YES.getValue())
                .eq(CustomerV2::getAssociatedOrgNo, internalOrgNo)
                .eq(CustomerV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
    }

    @Override
    public PageVo<OrganizationVo> findOrgListPageBySetting(OperationModel operationModel, CustomerEligibleOrgListQueryReq queryReq, PageDto pageDto) {
        ValidatorUtils.checkEmptyThrowEx(queryReq, "查询条件不能为空");

        List<CustomerV2> customerV2s = customerV2DAO.selectList(new LambdaQueryWrapper<CustomerV2>()
                .eq(CustomerV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .ne(StringUtils.isNotEmpty(queryReq.getCustomerCode()), CustomerV2::getCustomerCode, queryReq.getCustomerCode())
                .eq(CustomerV2::getIsAssociatedEnterprise, CommonIfEnum.YES.getValue())
                .eq(CustomerV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
        Set<String> assOrgNos = customerV2s.stream().map(CustomerV2::getAssociatedOrgNo).filter(Objects::nonNull).collect(Collectors.toSet());
        List<String> excludeOrgNoList = null;
        if (CollectionUtils.isNotEmpty(assOrgNos)) {
            excludeOrgNoList = new ArrayList<>(assOrgNos);
        }

        return organizationService.findListPageBySetting(operationModel, excludeOrgNoList, pageDto);
    }

    @Override
    @Transactional
    public Map<String,Object> manageApprove(OperationModel operationModel, CustomerApply customerApply, CustomerApplyItem customerApplyItem, CustomerApplyFormReq customerApplyFormReq, CustomerApplyApproveReq req) {
        Map<String,Object> result = new HashMap<>();

        String customerCode = null;
        if (CustomerApplyTypeEnum.ADD_APPLY.getValue().equals(customerApply.getApplyType())) { // 新增客户
            CustomerSaveBasicAndBizReq customerSaveBasicAndBizReq = new CustomerSaveBasicAndBizReq();
            BeanUtils.copyProperties(customerApplyFormReq, customerSaveBasicAndBizReq);
            customerSaveBasicAndBizReq.setEnterpriseNo(operationModel.getEnterpriseNo());
            customerSaveBasicAndBizReq.setManageOrgNo(customerApply.getManageOrgNo());
            customerSaveBasicAndBizReq.setBusinessFlag(CustomerBusinessFlagEnum.FORMAL.getValue());

            CustomerVO customerVO = null;
            if (StringUtils.isNotEmpty(customerApply.getCustomerCode())) {
                customerVO = getDetailCustomerByCode(operationModel, customerApply.getUseOrgNo(), customerApply.getCustomerCode());
            }
            if (null == customerVO) {
                customerVO = getDetailCustomerByName(operationModel, customerApply.getUseOrgNo(), customerApply.getCustomerName());
            }

            result.put("preApproveCustomer", customerVO);

            // 存在同名客户则更新，否则新增
            if (null == customerVO) {
                customerCode = StringUtils.isNotEmpty(customerApply.getCustomerCode()) ? customerApply.getCustomerCode() : numberCenterService.createNumberForBill(BillNameConstant.BDC_CUSTOMER_BILL, operationModel.getEnterpriseNo());
                customerSaveBasicAndBizReq.setCustomerCode(customerCode);
                manageSaveCustomerBasicAndBiz(operationModel, customerSaveBasicAndBizReq);

                //管理组织存业务信息，拷贝一份给使用组织；其他tab页只存使用组织

                // 同步分派，拿到最新的业务信息，存入镜像
                CustomerAssignExeRequest customerAssignExeRequest = new CustomerAssignExeRequest();
                customerAssignExeRequest.setEnterpriseNo(operationModel.getEnterpriseNo());
                customerAssignExeRequest.setManageOrgNo(customerApply.getManageOrgNo());
                CustomerAssignDocExeRequest customerAssignDocExeRequest = new CustomerAssignDocExeRequest();
                customerAssignDocExeRequest.setCustomerCode(customerCode);
                customerAssignDocExeRequest.setUseOrgNoList(Collections.singletonList(customerApply.getUseOrgNo()));
                customerAssignExeRequest.setDocList(Collections.singletonList(customerAssignDocExeRequest));
                assignCustomer(customerAssignExeRequest);
            } else {
                customerCode = customerVO.getCustomerCode();

                // 同步分派，拿到最新的业务信息，存入镜像
                CustomerAssignExeRequest customerAssignExeRequest = new CustomerAssignExeRequest();
                customerAssignExeRequest.setEnterpriseNo(operationModel.getEnterpriseNo());
                customerAssignExeRequest.setManageOrgNo(customerApply.getManageOrgNo());
                CustomerAssignDocExeRequest customerAssignDocExeRequest = new CustomerAssignDocExeRequest();
                customerAssignDocExeRequest.setCustomerCode(customerCode);
                customerAssignDocExeRequest.setUseOrgNoList(Collections.singletonList(customerApply.getUseOrgNo()));
                customerAssignExeRequest.setDocList(Collections.singletonList(customerAssignDocExeRequest));
                assignCustomer(customerAssignExeRequest);

                CustomerAssumeManageEditBasicAndBizReq customerAssumeManageEditBasicAndBizReq = new CustomerAssumeManageEditBasicAndBizReq();
                BeanUtils.copyProperties(customerApplyFormReq, customerAssumeManageEditBasicAndBizReq);
                customerAssumeManageEditBasicAndBizReq.setCustomerCode(customerCode);
                assumeManageEditCustomerBasicAndBiz(operationModel, customerAssumeManageEditBasicAndBizReq);
            }


            // 保存联系人
            CustomerLinkmanListEditReq customerLinkmanListEditReq = new CustomerLinkmanListEditReq();
            customerLinkmanListEditReq.setEnterpriseNo(operationModel.getEnterpriseNo());
            customerLinkmanListEditReq.setUseOrgNo(customerApply.getUseOrgNo());
            customerLinkmanListEditReq.setCustomerCode(customerCode);
            customerLinkmanListEditReq.setLinkmanList(customerApplyFormReq.getLinkmanList());
            editCustomerLinkmanList(operationModel, customerLinkmanListEditReq);

            // 保存联系地址
            CustomerAddressListEditReq customerAddressListEditReq = new CustomerAddressListEditReq();
            customerAddressListEditReq.setEnterpriseNo(operationModel.getEnterpriseNo());
            customerAddressListEditReq.setUseOrgNo(customerApply.getUseOrgNo());
            customerAddressListEditReq.setCustomerCode(customerCode);
            customerAddressListEditReq.setLinkAddressList(customerApplyFormReq.getLinkAddressList());
            editCustomerAddressList(operationModel, customerAddressListEditReq);

            // 保存银行信息
            CustomerBankListEditReq customerBankListEditReq = new CustomerBankListEditReq();
            customerBankListEditReq.setEnterpriseNo(operationModel.getEnterpriseNo());
            customerBankListEditReq.setManageOrgNo(customerApply.getManageOrgNo());
            customerBankListEditReq.setCustomerCode(customerCode);
            customerBankListEditReq.setBankList(customerApplyFormReq.getBankList());
            editCustomerBankList(operationModel, customerBankListEditReq);

            // 保存负责人信息
            CustomerSalesmanListEditReq customerSalesmanListEditReq = new CustomerSalesmanListEditReq();
            customerSalesmanListEditReq.setEnterpriseNo(operationModel.getEnterpriseNo());
            customerSalesmanListEditReq.setUseOrgNo(customerApply.getUseOrgNo());
            customerSalesmanListEditReq.setCustomerCode(customerCode);
            customerSalesmanListEditReq.setSalesManList(customerApplyFormReq.getCustomerManList());
            editCustomerSalesmanList(operationModel, customerSalesmanListEditReq);

            // 保存开票信息
            CustomerInvoiceListEditReq customerInvoiceListEditReq = new CustomerInvoiceListEditReq();
            customerInvoiceListEditReq.setEnterpriseNo(operationModel.getEnterpriseNo());
            customerInvoiceListEditReq.setUseOrgNo(customerApply.getUseOrgNo());
            customerInvoiceListEditReq.setCustomerCode(customerCode);
            customerInvoiceListEditReq.setInvoiceList(customerApplyFormReq.getInvoiceList());
            editCustomerInvoiceList(operationModel, customerInvoiceListEditReq);
        } else { // 变更客户
            CustomerVO customerVO = getDetailCustomerByCode(operationModel, customerApply.getUseOrgNo(), customerApply.getCustomerCode());
            result.put("preApproveCustomer", customerVO);

            customerCode = customerApply.getCustomerCode();

            CustomerEditBasicReq customerEditBasicReq = new CustomerEditBasicReq();
            BeanUtils.copyProperties(customerApplyFormReq, customerEditBasicReq);
            customerEditBasicReq.setEnterpriseNo(operationModel.getEnterpriseNo());
            customerEditBasicReq.setManageOrgNo(customerApply.getManageOrgNo());

            editCustomerBasic(operationModel, customerEditBasicReq);
        }

        result.put("customerCode", customerCode);
        return result;
    }

    @Override
    public Map<String, String> preValidateManageApprove(OperationModel operationModel, CustomerApply customerApply, CustomerApplyItem customerApplyItem, CustomerApplyFormReq customerApplyFormReq, CustomerApplyApproveReq req) {
        String companyNo = null;
        String customerCode = null;
        if (CustomerApplyTypeEnum.ADD_APPLY.getValue().equals(customerApply.getApplyType())) { // 新增客户
            CustomerSaveBasicAndBizReq customerSaveBasicAndBizReq = new CustomerSaveBasicAndBizReq();
            BeanUtils.copyProperties(customerApplyFormReq, customerSaveBasicAndBizReq);
            customerSaveBasicAndBizReq.setEnterpriseNo(operationModel.getEnterpriseNo());
            customerSaveBasicAndBizReq.setManageOrgNo(customerApply.getManageOrgNo());
            customerSaveBasicAndBizReq.setBusinessFlag(CustomerBusinessFlagEnum.FORMAL.getValue());

            CustomerVO customerVO = null;
            if (StringUtils.isNotEmpty(customerApply.getCustomerCode())) {
                customerVO = getDetailCustomerByCode(operationModel, customerApply.getUseOrgNo(), customerApply.getCustomerCode());
            }
            if (null == customerVO) {
                customerVO = getDetailCustomerByName(operationModel, customerApply.getUseOrgNo(), customerApply.getCustomerName());
            }

            // 存在同名客户则更新，否则新增
            if (null == customerVO) {
                customerCode = StringUtils.isNotEmpty(customerApply.getCustomerCode()) ? customerApply.getCustomerCode() : numberCenterService.createNumberForBill(BillNameConstant.BDC_CUSTOMER_BILL, operationModel.getEnterpriseNo());
                customerSaveBasicAndBizReq.setCustomerCode(customerCode);
                companyNo = preValidateManageSaveCustomerBasicAndBiz(operationModel, customerSaveBasicAndBizReq);
            } else {
                customerCode = customerVO.getCustomerCode();

                CustomerAssumeManageEditBasicAndBizReq customerAssumeManageEditBasicAndBizReq = new CustomerAssumeManageEditBasicAndBizReq();
                BeanUtils.copyProperties(customerApplyFormReq, customerAssumeManageEditBasicAndBizReq);
                customerAssumeManageEditBasicAndBizReq.setCustomerCode(customerCode);
                companyNo = preValidateAssumeManageEditCustomerBasicAndBiz(operationModel, customerAssumeManageEditBasicAndBizReq);
            }


            // 保存联系人
            CustomerLinkmanListEditReq customerLinkmanListEditReq = new CustomerLinkmanListEditReq();
            customerLinkmanListEditReq.setEnterpriseNo(operationModel.getEnterpriseNo());
            customerLinkmanListEditReq.setUseOrgNo(customerApply.getUseOrgNo());
            customerLinkmanListEditReq.setCustomerCode(customerCode);
            customerLinkmanListEditReq.setLinkmanList(customerApplyFormReq.getLinkmanList());
            preValidateEditCustomerLinkmanList(operationModel, customerLinkmanListEditReq);

            // 保存联系地址
            CustomerAddressListEditReq customerAddressListEditReq = new CustomerAddressListEditReq();
            customerAddressListEditReq.setEnterpriseNo(operationModel.getEnterpriseNo());
            customerAddressListEditReq.setUseOrgNo(customerApply.getUseOrgNo());
            customerAddressListEditReq.setCustomerCode(customerCode);
            customerAddressListEditReq.setLinkAddressList(customerApplyFormReq.getLinkAddressList());
            preValidateEditCustomerAddressList(operationModel, customerAddressListEditReq);

            // 保存银行信息
            CustomerBankListEditReq customerBankListEditReq = new CustomerBankListEditReq();
            customerBankListEditReq.setEnterpriseNo(operationModel.getEnterpriseNo());
            customerBankListEditReq.setManageOrgNo(customerApply.getManageOrgNo());
            customerBankListEditReq.setCustomerCode(customerCode);
            customerBankListEditReq.setBankList(customerApplyFormReq.getBankList());
            preValidateEditCustomerBankList(operationModel, customerBankListEditReq);

            // 保存负责人信息
            CustomerSalesmanListEditReq customerSalesmanListEditReq = new CustomerSalesmanListEditReq();
            customerSalesmanListEditReq.setEnterpriseNo(operationModel.getEnterpriseNo());
            customerSalesmanListEditReq.setUseOrgNo(customerApply.getUseOrgNo());
            customerSalesmanListEditReq.setCustomerCode(customerCode);
            customerSalesmanListEditReq.setSalesManList(customerApplyFormReq.getCustomerManList());
            preValidateEditCustomerSalesmanList(operationModel, customerSalesmanListEditReq);

            // 保存开票信息
            CustomerInvoiceListEditReq customerInvoiceListEditReq = new CustomerInvoiceListEditReq();
            customerInvoiceListEditReq.setEnterpriseNo(operationModel.getEnterpriseNo());
            customerInvoiceListEditReq.setUseOrgNo(customerApply.getUseOrgNo());
            customerInvoiceListEditReq.setCustomerCode(customerCode);
            customerInvoiceListEditReq.setInvoiceList(customerApplyFormReq.getInvoiceList());
            preValidateEditCustomerInvoiceList(operationModel, customerInvoiceListEditReq);

        } else { // 变更客户
            customerCode = customerApply.getCustomerCode();

            CustomerEditBasicReq customerEditBasicReq = new CustomerEditBasicReq();
            BeanUtils.copyProperties(customerApplyFormReq, customerEditBasicReq);
            customerEditBasicReq.setEnterpriseNo(operationModel.getEnterpriseNo());
            customerEditBasicReq.setManageOrgNo(customerApply.getManageOrgNo());

            companyNo = preValidateEditCustomerBasic(operationModel, customerEditBasicReq);
        }

        Map<String, String> result = new HashMap<>();
        result.put("companyNo", companyNo);
        result.put("customerCode", customerCode);

        return result;
    }

    @Override
    public void validateSaveBasicAndInfoReqByApply(OperationModel operationModel, String applyContent) {
        CustomerApplyFormReq params = JSON.parseObject(applyContent, CustomerApplyFormReq.class);

        //必填项校验
//        ValidatorUtils.checkEmptyThrowEx(params.getCustomerCode(), "客户编码不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getCustomerName(), "客户名称不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getTransactionType(), "客户类型不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getCustomerCategoryNo(), "客户分类不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getCompanyName(), "企业名称不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getUnifiedSocialCode(), "统一社会信用代码不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getFactoryType(), "企业注册地域不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getTaxCategory(), "纳税类别不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getRetailInvestors(), "是否散户不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getIsAssociatedEnterprise(), "是否内部组织不能为空");
        if (null != params.getIsAssociatedEnterprise() && CommonIfEnum.YES.getValue().equals(params.getIsAssociatedEnterprise())) {
            ValidatorUtils.checkEmptyThrowEx(params.getAssociatedOrgNo(), "对应内部组织不能为空");
        }
        ValidatorUtils.checkEmptyThrowEx(params.getIsMedicalInstitution(), "是否医疗机构不能为空");
        if (InstitutionalTypeEnum.yy.getType().equals(params.getIsMedicalInstitution().toString())) {
            ValidatorUtils.checkEmptyThrowEx(params.getHospitalType(), "医院性质不能为空");
            ValidatorUtils.checkEmptyThrowEx(params.getHospitalClass(), "医院等级不能为空");
        }

        //范围校验
        if (StringUtils.isNotEmpty(params.getCustomerCode())) {
            ValidatorUtils.checkTrueThrowEx(params.getCustomerCode().length() > 50, "客户编码不能大于50");
        }
        if (StringUtils.isNotEmpty(params.getCustomerName())) {
            ValidatorUtils.checkTrueThrowEx(params.getCustomerName().length() > 128, "客户名称不能大于128");
        }
        if (StringUtils.isNotEmpty(params.getCustomerNameEn())) {
            ValidatorUtils.checkTrueThrowEx(params.getCustomerNameEn().length() > 300, "客户英文名称不能大于300");
        }
        if (StringUtils.isNotEmpty(params.getMnemonicCode())) {
            ValidatorUtils.checkTrueThrowEx(params.getMnemonicCode().length() > 100, "助记码不能大于100");
        }
        if (StringUtils.isNotEmpty(params.getCompanyName())) {
            ValidatorUtils.checkTrueThrowEx(params.getCompanyName().length() > 128, "企业名称不能大于128");
        }
        if (StringUtils.isNotEmpty(params.getUnifiedSocialCode())) {
            ValidatorUtils.checkTrueThrowEx(params.getUnifiedSocialCode().length() > 100, "统一社会信用代码不能大于100");
        }
//        if (StringUtils.isNotEmpty(params.getCountry())) {
//            ValidatorUtils.checkTrueThrowEx(params.getCountry().length() > 50, "国家/地区不能大于50");
//        }
        if (StringUtils.isNotEmpty(params.getRemark())) {
            ValidatorUtils.checkTrueThrowEx(params.getRemark().length() > 300, "备注不能大于300");
        }


        if (StringUtils.isNotEmpty(params.getReceiveAgreement())) {
            ValidatorUtils.checkTrueThrowEx(params.getReceiveAgreement().length() > 500, "收款协议不能大于500");
        }
        if (StringUtils.isNotEmpty(params.getReceiveCondition())) {
            ValidatorUtils.checkTrueThrowEx(params.getReceiveCondition().length() > 500, "收款条件不能大于500");
        }
        if (StringUtils.isNotEmpty(params.getCreditAmount())) {
            ValidatorUtils.checkTrueThrowEx(params.getCreditAmount().length() > 20, "信用额度不能大于20");
        }
        if (StringUtils.isNotEmpty(params.getCreditDates())) {
            ValidatorUtils.checkTrueThrowEx(params.getCreditDates().length() > 20, "信用期限不能大于20");
        }
        if (StringUtils.isNotEmpty(params.getOwnerCompany())) {
            ValidatorUtils.checkTrueThrowEx(params.getOwnerCompany().length() > 100, "业务归属不能大于100");
        }
    }

    @Override
    public void validateEditBasicReqByApply(OperationModel operationModel, String applyContent) {
        CustomerApplyFormReq params = JSON.parseObject(applyContent, CustomerApplyFormReq.class);

        //必填项校验
//        ValidatorUtils.checkEmptyThrowEx(params.getCustomerCode(), "客户编码不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getCustomerName(), "客户名称不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getTransactionType(), "客户类型不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getCustomerCategoryNo(), "客户分类不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getCompanyName(), "企业名称不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getUnifiedSocialCode(), "统一社会信用代码不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getFactoryType(), "企业注册地域不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getTaxCategory(), "纳税类别不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getRetailInvestors(), "是否散户不能为空");
        ValidatorUtils.checkEmptyThrowEx(params.getIsAssociatedEnterprise(), "是否内部组织不能为空");
        if (null != params.getIsAssociatedEnterprise() && CommonIfEnum.YES.getValue().equals(params.getIsAssociatedEnterprise())) {
            ValidatorUtils.checkEmptyThrowEx(params.getAssociatedOrgNo(), "对应内部组织不能为空");
        }
        ValidatorUtils.checkEmptyThrowEx(params.getIsMedicalInstitution(), "是否医疗机构不能为空");
        if (InstitutionalTypeEnum.yy.getType().equals(params.getIsMedicalInstitution().toString())) {
            ValidatorUtils.checkEmptyThrowEx(params.getHospitalType(), "医院性质不能为空");
            ValidatorUtils.checkEmptyThrowEx(params.getHospitalClass(), "医院等级不能为空");
        }

        //范围校验
        if (StringUtils.isNotEmpty(params.getCustomerCode())) {
            ValidatorUtils.checkTrueThrowEx(params.getCustomerCode().length() > 50, "客户编码不能大于50");
        }
        if (StringUtils.isNotEmpty(params.getCustomerName())) {
            ValidatorUtils.checkTrueThrowEx(params.getCustomerName().length() > 128, "客户名称不能大于128");
        }
        if (StringUtils.isNotEmpty(params.getCustomerNameEn())) {
            ValidatorUtils.checkTrueThrowEx(params.getCustomerNameEn().length() > 300, "客户英文名称不能大于300");
        }
        if (StringUtils.isNotEmpty(params.getMnemonicCode())) {
            ValidatorUtils.checkTrueThrowEx(params.getMnemonicCode().length() > 100, "助记码不能大于100");
        }
        if (StringUtils.isNotEmpty(params.getCompanyName())) {
            ValidatorUtils.checkTrueThrowEx(params.getCompanyName().length() > 128, "企业名称不能大于128");
        }
        if (StringUtils.isNotEmpty(params.getUnifiedSocialCode())) {
            ValidatorUtils.checkTrueThrowEx(params.getUnifiedSocialCode().length() > 100, "统一社会信用代码不能大于100");
        }
//        if (StringUtils.isNotEmpty(params.getCountry())) {
//            ValidatorUtils.checkTrueThrowEx(params.getCountry().length() > 50, "国家/地区不能大于50");
//        }
        if (StringUtils.isNotEmpty(params.getRemark())) {
            ValidatorUtils.checkTrueThrowEx(params.getRemark().length() > 300, "备注不能大于300");
        }
    }

    @Override
    public Boolean changeCustomerGspStatus(CustomerGspStatusReq params) {
        CustomerGspAudit newCustomerGspAudit = new CustomerGspAudit();
        newCustomerGspAudit.setGspAuditStatus(GspAuditStatusEnum.HAS_FIRST_BUSINESS.getType());
        newCustomerGspAudit.setGspAuditResult(GspAuditStatusEnum.HAS_FIRST_BUSINESS.getName());

        int update = customerGspAuditDAO.update(newCustomerGspAudit, Wrappers.<CustomerGspAudit>lambdaUpdate()
                .eq(CustomerGspAudit::getEnterpriseNo, params.getEnterpriseNo())
                .eq(CustomerGspAudit::getUseOrgNo, params.getUseOrgNo())
                .eq(CustomerGspAudit::getCustomerCode, params.getCustomerCode())
                .eq(CustomerGspAudit::getDeleted, DeletedEnum.UN_DELETE.getValue()));
        return update == 0;
    }

    @Override
    public Boolean checkConvertToSupplier(OperationModel operationModel, String customerCode) {
        ValidatorUtils.checkEmptyThrowEx(customerCode, "客户编码不能为空");

        CustomerV2 customer = getCustomerByCode(operationModel, customerCode);
        ValidatorUtils.checkEmptyThrowEx(customer, "客户不存在");

        // 管理组织列表
        List<String> manageNoList = gradeControlService.listMgrOrgNos(operationModel.getEnterpriseNo(), BillNameConstant.BDC_SUPPLIER_BILL);
        if (CollectionUtils.isEmpty(manageNoList) || !manageNoList.contains(customer.getManageOrgNo())) {
            return false;
        }

        return true;
    }

    private void fillCompanyToCustomer(CustomerFormalPageVO customerVo, CompanyV2 company, Map<String, CustomDocResponse> taxCategoryMap) {
//        customerVo.setCompanyName(company.getCompanyName());
//        customerVo.setUnifiedSocialCode(company.getUnifiedSocialCode());
//        customerVo.setRegistedCapital(company.getRegistedCapital());
//        customerVo.setEstablishmentDate(company.getEstablishmentDate());
//        customerVo.setRegionCode(company.getRegionCode());
//        customerVo.setRegionName(company.getRegionName());
//        customerVo.setAddress(company.getAddress());
//        customerVo.setLegalPerson(company.getLegalPerson());
//        customerVo.setEmail(company.getEmail());
//        customerVo.setWebSite(company.getWebSite());
//        customerVo.setFax(company.getFax());
////        customerVo.setLinkMan(company.getLinkMan());
////        customerVo.setLinkPhone(company.getLinkPhone());
////        customerVo.setLinkManPosition(company.getLinkManPosition());
//        customerVo.setTaxCategoryName(taxCategoryMap.containsKey(company.getTaxCategory()) ? taxCategoryMap.get(company.getTaxCategory()).getDocItemName() : company.getTaxCategoryName());
////        customerVo.setTaxRate(company.getTaxRate());
//        customerVo.setTaxCategory(company.getTaxCategory());
////        customerVo.setOwnerShip(company.getOwnerShip());
////        customerVo.setOwnerShipName(company.getOwnerShipName());
//        customerVo.setIsListed(company.getIsListed());
//        customerVo.setBusinessStartTime(company.getBusinessStartTime());
//        customerVo.setBusinessEndTime(company.getBusinessEndTime());
//        customerVo.setBusinessLongTerm(company.getBusinessLongTerm());
//        customerVo.setPaidCapital(company.getPaidCapital());
//        customerVo.setIndustry(company.getIndustry());
//        customerVo.setBusinessRegistNo(company.getBusinessRegistNo());
//        customerVo.setOrganizationNo(company.getOrganizationNo());
//        customerVo.setTaxpayerNo(company.getTaxpayerNo());
//        customerVo.setTaxpayerQualification(company.getTaxpayerQualification());
//        customerVo.setRegistrationAuthority(company.getRegistrationAuthority());
//        customerVo.setApprovalDate(company.getApprovalDate());
//        customerVo.setLastName(company.getLastName());
//        customerVo.setInsuredNumber(company.getInsuredNumber());
//        customerVo.setCompanyBusinessType(company.getCompanyBusinessType());
//        customerVo.setBusinessStatus(company.getBusinessStatus());
//        // 覆盖经营范围
//        customerVo.setManageScope(company.getManageScope());
//        customerVo.setFactoryType(company.getFactoryType());
//        customerVo.setIsAssociatedEnterprise(company.getIsAssociatedEnterprise());
//        customerVo.setIsAssociatedEnterpriseName(null != company.getIsAssociatedEnterprise() && company.getIsAssociatedEnterprise() == 1 ? "是" : "否");
//        if (!org.apache.axis.utils.StringUtils.isEmpty(company.getPartnership())) {
//            try {
//                List<CompanyPartnershipEnum> partnershipEnumList = new ArrayList<>();
//                List<String> partnershipList = JSON.parseArray(company.getPartnership(), String.class);
//                for (String partnership : partnershipList) {
//                    partnershipEnumList.add(CompanyPartnershipEnum.getByType(partnership));
//                }
//                customerVo.setPartnershipText(String.join(",", partnershipEnumList.stream().map(n -> n.getName()).collect(Collectors.toList())));
//            } catch (Exception e) {
//                log.error("合作关系解析类型失败,失败原因:{}", JSON.toJSONString(e));
//            }
//        }


        customerVo.setUnifiedSocialCode(company.getUnifiedSocialCode());
        customerVo.setTaxpayerNo(company.getTaxpayerNo());
        customerVo.setInstitutionalType(company.getInstitutionalType());
        if (customerVo.getInstitutionalType() != null) {
            customerVo.setInstitutionalTypeName(InstitutionalTypeEnum.getByType(customerVo.getInstitutionalType()) == null ? null : InstitutionalTypeEnum.getByType(customerVo.getInstitutionalType()).getName());
        }
        customerVo.setHospitalType(company.getHospitalType());
        if (customerVo.getHospitalType() != null) {
            customerVo.setHospitalTypeName(HospitalTypeEnum.getByType(customerVo.getHospitalType()) == null ? null : HospitalTypeEnum.getByType(customerVo.getHospitalType()).getName());
        }
        customerVo.setHospitalClass(company.getHospitalClass());
        customerVo.setTaxCategory(company.getTaxCategory());
        customerVo.setAddress(company.getAddress());
        if (!StringUtils.isEmpty(customerVo.getTaxCategory()) && taxCategoryMap.containsKey(customerVo.getTaxCategory())) {
            CustomDocResponse customDocResponse = taxCategoryMap.get(customerVo.getTaxCategory());
            if (null != customDocResponse) {
                customerVo.setTaxCategoryName(customDocResponse.getDocItemName());
            }
        }
    }


    private List<CustomerFormalPageVO> completeSupplementFormalPageVO(String enterpriseNo, List<CustomerV2WithBiz> data) {
        List<CustomerFormalPageVO> result = new ArrayList<>();

        Map<String, Object> infoMap = collectBizInfo(enterpriseNo, data,
                Sets.newHashSet(
                        "categoryMap",
                        "companyMap",
                        "taxCategoryMap",
//                        "economicTypeMap",
                        "cooperationMap",
//                        "transactionTypeMap",
//                        "orgMap",
                        "customerBizMap",
//                        "addressMap",
                        "linkManMap",
                        "businessTypeMap",
                        "currencyMap",
                        "priceCustomerCategoryMap"
                ));
        final Map<String, CustomerCategoryV2> categoryMap = (Map<String, CustomerCategoryV2>) infoMap.get("categoryMap");
        final Map<String, CompanyV2> companyMap = (Map<String, CompanyV2>) infoMap.get("companyMap");
        final Map<String, CustomDocResponse> taxCategoryMap = (Map<String, CustomDocResponse>) infoMap.get("taxCategoryMap");
//        final Map<String, CustomDocResponse> economicTypeMap = (Map<String, CustomDocResponse>) infoMap.get("economicTypeMap");
        final Map<String, CustomDocResponse> cooperationMap = (Map<String, CustomDocResponse>) infoMap.get("cooperationMap");
//        final Map<String, TransactionTypeResponse> transactionTypeMap = (Map<String, TransactionTypeResponse>) infoMap.get("transactionTypeMap");
//        final Map<String, OrganizationVo> orgNo2OrganizationVo = (Map<String, OrganizationVo>) infoMap.get("orgMap");
        final Map<Long, CustomerBiz> customerBizMap = (Map<Long, CustomerBiz>) infoMap.get("customerBizMap");
//        Map<String, List<CompanyShippingAddressV2>> addressMap = (Map<String, List<CompanyShippingAddressV2>>) infoMap.get("addressMap");
        Map<String, List<CompanyLinkmanV2>> linkManMap = (Map<String, List<CompanyLinkmanV2>>) infoMap.get("linkManMap");
        final Map<String, CustomDocResponse> businessTypeMap = (Map<String, CustomDocResponse>) infoMap.get("businessTypeMap");
        final Map<String, String> currencyMap = (Map<String, String>) infoMap.get("currencyMap");
        final Map<String, String> priceCustomerCategoryMap = (Map<String, String>) infoMap.get("priceCustomerCategoryMap");


        data.forEach(customer -> {
            CustomerFormalPageVO customerVO = new CustomerFormalPageVO();
            BeanUtils.copyProperties(customer, customerVO);

            customerVO.setCustomerCategoryName(categoryMap.getOrDefault(customer.getCustomerCategoryNo(), new CustomerCategoryV2()).getCategoryName());


            // 补充供应商业务信息
            if (customerBizMap.containsKey(customer.getBizId())) {
                CustomerBiz customerBiz = customerBizMap.get(customer.getBizId());
                BeanUtils.copyProperties(customerBiz, customerVO);

                customerVO.setCooperationModeName(cooperationMap.getOrDefault(customerBiz.getCooperationMode(), new CustomDocResponse()).getDocItemName());

                if (StringUtils.isNotEmpty(customerBiz.getBusinessType()) && StringUtils.isEmpty(customerBiz.getBusinessTypeName())) {
                    CustomDocResponse customDocResponse = businessTypeMap.get(customerBiz.getBusinessType());
                    if (null != customDocResponse) {
                        customerVO.setBusinessTypeName(customDocResponse.getDocItemName());
                    }
                }

                if (StringUtils.isNotEmpty(customerBiz.getCurrencyId())) {
                    customerVO.setCurrencyName(currencyMap.get(customerBiz.getCurrencyId()));
                }

                if (StringUtils.isNotEmpty(customerBiz.getPriceCategoryCode())) {
                    customerVO.setPriceCategoryCodeName(priceCustomerCategoryMap.get(customerBiz.getPriceCategoryCode()));
                }

            }

            if (companyMap.containsKey(customer.getCompanyNo())) {
                fillCompanyToCustomer(customerVO, companyMap.get(customer.getCompanyNo()), taxCategoryMap);
            }

            customerVO.setIsAssociatedEnterpriseName(null != customer.getIsAssociatedEnterprise() && customer.getIsAssociatedEnterprise().equals(CommonIfEnum.YES.getValue()) ? "是" : "否");

//            if (addressMap.containsKey(customer.getCustomerCode())) {
//                fillAddressToCustomer(customerVO, addressMap.get(customer.getCustomerCode()));
//            }

            if (linkManMap.containsKey(customer.getCustomerCode())) {
                linkManMap.get(customer.getCustomerCode()).stream().filter(companyLinkmanV2 -> companyLinkmanV2.getIsDefault().equals(CommonIfEnum.YES.getValue())).findFirst().ifPresent(companyLinkmanV2 -> {
                    customerVO.setLinkMan(companyLinkmanV2.getLinkman());
                    customerVO.setLinkPhone(companyLinkmanV2.getMobilePhone());
                    customerVO.setLinkManPosition(companyLinkmanV2.getPosition());
                });
            } else {
                customerVO.setLinkMan(null);
                customerVO.setLinkPhone(null);
            }


            //不兼容老接口
//            customerVo.setIsCustomerVersion("true");
//            customerVo.setGspAuditStatus("");
//            customerVo.setApplyformBillType("");
//            customerVo.setCustomerNo("");
//            customerVo.setIsSQAuditing(0);
//            customerVO.setIsAdmittance("1");
//            customerVo.setHasHistory("0");
//            customerVo.setIsAccountManagerName(customerVo.getIsAccountManager() == 1 ? "是" : "否");

            result.add(customerVO);
        });
        return result;
    }

}
