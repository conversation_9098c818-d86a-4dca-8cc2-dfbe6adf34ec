package com.yyigou.dsrp.cdc.service.v2.customer.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.yyigou.ddc.common.error.ErrorCode;
import com.yyigou.ddc.common.exception.BusinessException;
import com.yyigou.ddc.common.page.PageDto;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.ddc.common.util.DateUtil;
import com.yyigou.ddc.common.util.ValidatorUtils;
import com.yyigou.ddc.services.ddc.uap.domain.vo.GradeAutoReviewConfigVO;
import com.yyigou.ddc.services.ddc.uim.vo.EmployeeVo;
import com.yyigou.ddc.services.ddc.uim.vo.OrganizationVo;
import com.yyigou.ddc.services.dlog.dto.DLogLevel;
import com.yyigou.dsrp.cdc.api.v2.customer.vo.CustomerApplyDetailVO;
import com.yyigou.dsrp.cdc.api.v2.customer.vo.CustomerApplyPageVO;
import com.yyigou.dsrp.cdc.api.v2.customer.vo.CustomerVO;
import com.yyigou.dsrp.cdc.common.enums.ApplyResultEnum;
import com.yyigou.dsrp.cdc.common.enums.CertSourceTypeEnum;
import com.yyigou.dsrp.cdc.common.enums.CommonIfEnum;
import com.yyigou.dsrp.cdc.common.enums.DeletedEnum;
import com.yyigou.dsrp.cdc.common.enums.customer.CustomerApplyAuditStatusEnum;
import com.yyigou.dsrp.cdc.common.enums.customer.CustomerApplyTypeEnum;
import com.yyigou.dsrp.cdc.common.enums.supplier.SupplierApplyAuditStatusEnum;
import com.yyigou.dsrp.cdc.common.util.BeanUtil;
import com.yyigou.dsrp.cdc.common.util.CommonUtil;
import com.yyigou.dsrp.cdc.common.util.OperationModel;
import com.yyigou.dsrp.cdc.dao.v2.company.entity.CompanyV2;
import com.yyigou.dsrp.cdc.dao.v2.customer.CustomerApplyDAO;
import com.yyigou.dsrp.cdc.dao.v2.customer.CustomerApplyItemDAO;
import com.yyigou.dsrp.cdc.dao.v2.customer.entity.CustomerApply;
import com.yyigou.dsrp.cdc.dao.v2.customer.entity.CustomerApplyItem;
import com.yyigou.dsrp.cdc.dao.v2.customer.entity.CustomerBase;
import com.yyigou.dsrp.cdc.dao.v2.customer.entity.CustomerV2;
import com.yyigou.dsrp.cdc.manager.integration.cert.CertService;
import com.yyigou.dsrp.cdc.manager.integration.cert.req.CompanyCertBasicRequest;
import com.yyigou.dsrp.cdc.manager.integration.cert.req.CompanyCertUpsertRequest;
import com.yyigou.dsrp.cdc.manager.integration.dict.DictEnterpriseService;
import com.yyigou.dsrp.cdc.manager.integration.dlog.BusinessLogService;
import com.yyigou.dsrp.cdc.manager.integration.uap.GradeControlService;
import com.yyigou.dsrp.cdc.manager.integration.uim.CustomDocService;
import com.yyigou.dsrp.cdc.manager.integration.uim.EmployeeService;
import com.yyigou.dsrp.cdc.manager.integration.uim.OrganizationService;
import com.yyigou.dsrp.cdc.manager.integration.uim.TransactionTypeService;
import com.yyigou.dsrp.cdc.manager.integration.uim.response.CustomDocResponse;
import com.yyigou.dsrp.cdc.model.constant.BillNameConstant;
import com.yyigou.dsrp.cdc.model.constant.SystemConstant;
import com.yyigou.dsrp.cdc.model.constant.ViewNameConstant;
import com.yyigou.dsrp.cdc.model.v2.company.req.CompanyBankReq;
import com.yyigou.dsrp.cdc.model.v2.company.req.CompanyCertReq;
import com.yyigou.dsrp.cdc.model.v2.company.req.CompanyLinkmanReq;
import com.yyigou.dsrp.cdc.model.v2.company.req.CompanyShippingAddressReq;
import com.yyigou.dsrp.cdc.model.v2.customer.req.*;
import com.yyigou.dsrp.cdc.service.utils.PageUtils;
import com.yyigou.dsrp.cdc.service.v2.company.CompanyV2Service;
import com.yyigou.dsrp.cdc.service.v2.customer.CustomerApplyService;
import com.yyigou.dsrp.cdc.service.v2.customer.CustomerCategoryV2Service;
import com.yyigou.dsrp.cdc.service.v2.customer.CustomerV2Service;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionCallbackWithoutResult;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.yyigou.dsrp.cdc.manager.integration.uap.GradeControlService.AUTO_REVIEW;

@Service("customerApplyService")
@RequiredArgsConstructor
@Slf4j
public class CustomerApplyServiceImpl extends ServiceImpl<CustomerApplyDAO, CustomerApply> implements CustomerApplyService {
    @Resource
    private CustomerV2Service customerV2Service;

    @Resource
    private CustomerApplyDAO customerApplyDAO;

    @Resource
    private CustomerApplyItemDAO customerApplyItemDAO;

    @Resource
    private CustomerCategoryV2Service customerCategoryV2Service;

    @Resource
    private CompanyV2Service companyV2Service;

    @Resource
    private CustomDocService customDocService;

    @Resource
    private DictEnterpriseService dictEnterpriseService;

    @Resource
    private TransactionTypeService transactionTypeService;

    @Resource
    private OrganizationService organizationService;

    @Resource
    private EmployeeService employeeService;

    @Resource
    private BusinessLogService businessLogService;

    @Resource
    private GradeControlService gradeControlService;

    @Resource
    private TransactionTemplate transactionTemplate;

    @Resource
    private CertService certService;

    @Override
    public PageVo<CustomerApplyPageVO> applyFindListPage(OperationModel operationModel, CustomerApplyQueryListPageReq queryReq, PageDto pageDTO) {
        ValidatorUtils.checkEmptyThrowEx(queryReq, "查询条件不能为空");

        Page<CustomerApply> page = PageHelper.startPage(pageDTO.getPageIndex(), pageDTO.getPageSize(), StringUtils.isBlank(pageDTO.getOrderBy()) ? "create_time desc" : pageDTO.getOrderBy());
        customerApplyDAO.selectList(Wrappers.<CustomerApply>lambdaQuery()
                .eq(CustomerApply::getEnterpriseNo, operationModel.getEnterpriseNo())
                .in(CollectionUtils.isNotEmpty(queryReq.getIdList()), CustomerApply::getApplyInstanceNo, queryReq.getIdList())
                .eq(CustomerApply::getDeleted, DeletedEnum.UN_DELETE.getValue()));

        return PageUtils.convertPageVo(page, data -> completeSupplementPageVO(operationModel.getEnterpriseNo(), data));
    }

    @Override
    public PageVo<CustomerApplyPageVO> approveFindListPage(OperationModel operationModel, CustomerApplyApproveQueryListPageReq queryReq, PageDto pageDTO) {
        ValidatorUtils.checkEmptyThrowEx(queryReq, "查询条件不能为空");

        Page<CustomerApply> page = PageHelper.startPage(pageDTO.getPageIndex(), pageDTO.getPageSize(), StringUtils.isBlank(pageDTO.getOrderBy()) ? "create_time desc" : pageDTO.getOrderBy());
        customerApplyDAO.selectList(Wrappers.<CustomerApply>lambdaQuery()
                .eq(CustomerApply::getEnterpriseNo, operationModel.getEnterpriseNo())
                .in(CollectionUtils.isNotEmpty(queryReq.getIdList()), CustomerApply::getApplyInstanceNo, queryReq.getIdList())
                .eq(CustomerApply::getDeleted, DeletedEnum.UN_DELETE.getValue()));

        return PageUtils.convertPageVo(page, data -> completeSupplementPageVO(operationModel.getEnterpriseNo(), data));
    }

    @Override
    public Long getPendingCount(OperationModel operationModel) {
        return customerApplyDAO.selectCount(Wrappers.<CustomerApply>lambdaQuery()
                .eq(CustomerApply::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CustomerApply::getAuditStatus, CustomerApplyAuditStatusEnum.TO_BE_AUDITED.getValue())
                .eq(CustomerApply::getDeleted, DeletedEnum.UN_DELETE.getValue()));
    }

    private void saveOrUpdateValidate(OperationModel operationModel, CustomerApplySaveOrUpdateReq req) {
        // 必填校验
        ValidatorUtils.checkEmptyThrowEx(req, "申请信息不能为空");
        ValidatorUtils.checkEmptyThrowEx(req.getApplyInstanceNo(), "申请编号为空");
        ValidatorUtils.checkEmptyThrowEx(req.getAuditStatus(), "审核状态为空");

        // 范围校验
        ValidatorUtils.checkTrueThrowEx(req.getApplyInstanceNo().length() > 32, "申请编号长度不能超过32");
        if (StringUtils.isNotEmpty(req.getApplyDesc())) {
            ValidatorUtils.checkTrueThrowEx(req.getApplyDesc().length() > 500, "申请描述长度不能超过500");
        }

        // 合法性校验
        ValidatorUtils.checkTrueThrowEx(null == CustomerApplyTypeEnum.getNameByValue(req.getApplyType()), "申请类型不正确");
    }

    @Override
    @Transactional
    public String applySaveOrUpdate(OperationModel operationModel, CustomerApplySaveOrUpdateReq req) {
        // 参数校验
        saveOrUpdateValidate(operationModel, req);

        // 业务校验
        List<CustomerApply> customerAppliesInDB = customerApplyDAO.selectList(Wrappers.<CustomerApply>lambdaQuery()
                .eq(CustomerApply::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CustomerApply::getCustomerName, req.getCustomerName())
                .ne(CustomerApply::getApplyInstanceNo, req.getApplyInstanceNo())
                .eq(CustomerApply::getAuditStatus, CustomerApplyAuditStatusEnum.TO_BE_AUDITED.getValue())
                .eq(CustomerApply::getDeleted, DeletedEnum.UN_DELETE.getValue()));
        if (CustomerApplyTypeEnum.ADD_APPLY.getValue().equals(req.getApplyType())) { // 新增申请
            /*
            申请组织视角下存在同名客户的“待审核”申请单：客户【客户名称】已存在待审核的申请单【申请单号】，不能重复创建。
            客户档案中已存在同名客户，未分派，但是管理组织不同：名称【客户名称】的客户档案已存在，管理组织是【组织名称】，与申请单中的管理组织不一致，请更换管理组织！
            客户档案中已存在同名客户，已分派：客户档案中已存在名称【客户名称】的客户，可以直接使用。
             */
            if (CollectionUtils.isNotEmpty(customerAppliesInDB)) {
                String errorMessage = String.format("客户【%s】已存在待审核的申请单【%s】，不能重复创建", req.getCustomerName(), customerAppliesInDB.stream().map(CustomerApply::getApplyInstanceNo).collect(Collectors.joining(",")));
                throw new BusinessException(ErrorCode.param_invalid_msg, errorMessage);
            }

            CustomerV2 customerV2 = null;
            if (StringUtils.isNotEmpty(req.getCustomerCode())) {
                customerV2 = customerV2Service.getCustomerByCode(operationModel, req.getCustomerName());
            }
            if (null == customerV2) {
                customerV2 = customerV2Service.getCustomerByName(operationModel, req.getCustomerName());
            }
            if (null != customerV2) {
                if (!customerV2.getManageOrgNo().equals(req.getManageOrgNo())) {
                    List<OrganizationVo> organizationVoList = organizationService.findListNoAuth(operationModel.getEnterpriseNo(), Collections.singletonList(customerV2.getManageOrgNo()));
                    final Map<String, OrganizationVo> orgNo2OrganizationVo = organizationVoList.stream().collect(Collectors.toMap(OrganizationVo::getOrgNo, Function.identity()));

                    String errorMessage = String.format("名称【%s】的客户档案已存在，管理组织是【%s】，与申请单中的管理组织不一致，请更换管理组织！", req.getCustomerName(), orgNo2OrganizationVo.containsKey(customerV2.getManageOrgNo()) ? orgNo2OrganizationVo.get(customerV2.getManageOrgNo()).getOrgName() : "");
                    throw new BusinessException(ErrorCode.param_invalid_msg, errorMessage);
                }


                CustomerBase customerBaseQuery = new CustomerBase();
                customerBaseQuery.setCustomerCode(customerV2.getCustomerCode());
                customerBaseQuery.setManageOrgNo(customerV2.getManageOrgNo());
                customerBaseQuery.setUseOrgNo(req.getUseOrgNo());
                boolean hasAssigned = customerV2Service.hasAssignCustomer(operationModel, customerBaseQuery);
                ValidatorUtils.checkTrueThrowEx(hasAssigned, String.format("客户档案中已存在名称【%s】的客户，可以直接使用", req.getCustomerName()));
            }
        } else if (CustomerApplyTypeEnum.MODIFY_APPLY.getValue().equals(req.getApplyType())) { // 变更申请
            /*
            申请组织视角下存在同名客户的“待审核”申请单：客户【客户名称】已存在待审核的申请单【申请单号】，不能重复创建。
             */
            if (CollectionUtils.isNotEmpty(customerAppliesInDB)) {
                String errorMessage = String.format("客户【%s】已存在待审核的申请单【%s】，不能重复创建", req.getCustomerName(), customerAppliesInDB.stream().map(CustomerApply::getApplyInstanceNo).collect(Collectors.joining(",")));
                throw new BusinessException(ErrorCode.param_invalid_msg, errorMessage);
            }
        }


        CustomerApply customerApply = customerApplyDAO.selectOne(Wrappers.<CustomerApply>lambdaQuery()
                .eq(CustomerApply::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CustomerApply::getApplyInstanceNo, req.getApplyInstanceNo())
                .eq(CustomerApply::getDeleted, DeletedEnum.UN_DELETE.getValue()));
        if (null == customerApply) { // 新增
            // 当前状态：初始态
            // 可执行动作
            ValidatorUtils.checkTrueThrowEx(!(CustomerApplyAuditStatusEnum.DRAFT.getValue().equals(req.getAuditStatus()) || CustomerApplyAuditStatusEnum.TO_BE_AUDITED.getValue().equals(req.getAuditStatus())), "新增申请单需要保存为草稿态或待审核态");

            // 校验json内容
            customerV2Service.validateSaveBasicAndInfoReqByApply(operationModel, req.getApplyContent());

            CustomerApply newCustomerApply = BeanUtil.copyFields(req, CustomerApply.class);
            CommonUtil.fillCreatInfo(operationModel, newCustomerApply);
            customerApplyDAO.insert(newCustomerApply);

            CustomerApplyItem customerApplyItem = new CustomerApplyItem();
            customerApplyItem.setEnterpriseNo(operationModel.getEnterpriseNo());
            customerApplyItem.setApplyInstanceId(newCustomerApply.getId());
            customerApplyItem.setApplyInstanceNo(newCustomerApply.getApplyInstanceNo());
            customerApplyItem.setApplyContent(req.getApplyContent());
            customerApplyItemDAO.insert(customerApplyItem);

            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                @Override
                public void afterCommit() {
                    try {
                        businessLogService.saveLog(DLogLevel.INFO.name(), ViewNameConstant.BDC_CUSTOMER_APPLY_VIEW, req.getApplyInstanceNo(), "新增客户申请", "新增客户申请", "", "");
                    } catch (Exception e) {
                        log.error("新增客户申请日志保存失败", e);
                    }
                }
            });
        } else {
            // 当前状态
            ValidatorUtils.checkTrueThrowEx(!(CustomerApplyAuditStatusEnum.DRAFT.getValue().equals(customerApply.getAuditStatus()) || CustomerApplyAuditStatusEnum.REJECTED.getValue().equals(customerApply.getAuditStatus())), "非草稿态和审批失败态的申请单不允许修改");
            // 可执行动作
//            if (CustomerApplyAuditStatusEnum.REJECTED.getValue().equals(customerApply.getAuditStatus())) {
//                ValidatorUtils.checkTrueThrowEx(!(CustomerApplyAuditStatusEnum.DRAFT.getValue().equals(req.getAuditStatus())), "审批失败的申请单需要保存为草稿态");
//            } else {
            ValidatorUtils.checkTrueThrowEx(!(req.getAuditStatus().equals(CustomerApplyAuditStatusEnum.DRAFT.getValue()) || req.getAuditStatus().equals(CustomerApplyAuditStatusEnum.TO_BE_AUDITED.getValue())), "申请单状态不正确");
//            }

            //校验json内容
            customerV2Service.validateEditBasicReqByApply(operationModel, req.getApplyContent());

            LambdaUpdateWrapper<CustomerApply> customerApplyLambdaUpdateWrapper = Wrappers.lambdaUpdate(CustomerApply.class);
            // 表头
            customerApplyLambdaUpdateWrapper.set(CustomerApply::getApplyNo, req.getApplyNo());
            customerApplyLambdaUpdateWrapper.set(CustomerApply::getApplyTime, req.getApplyTime());
            customerApplyLambdaUpdateWrapper.set(CustomerApply::getApplyReason, req.getApplyReason());
            customerApplyLambdaUpdateWrapper.set(CustomerApply::getApplyDesc, req.getApplyDesc());
            customerApplyLambdaUpdateWrapper.set(CustomerApply::getAuditStatus, req.getAuditStatus());
            customerApplyLambdaUpdateWrapper.set(CustomerApply::getApplyNo, req.getApplyNo());

            // 所属关系
            customerApplyLambdaUpdateWrapper.set(CustomerApply::getUseOrgNo, req.getUseOrgNo());
            customerApplyLambdaUpdateWrapper.set(CustomerApply::getManageOrgNo, req.getManageOrgNo());

            // 客户基本信息
            customerApplyLambdaUpdateWrapper.set(CustomerApply::getCustomerCode, req.getCustomerCode());
            customerApplyLambdaUpdateWrapper.set(CustomerApply::getCustomerName, req.getCustomerName());

            // 企业信息
            customerApplyLambdaUpdateWrapper.set(CustomerApply::getCompanyNo, req.getCompanyNo());
            customerApplyLambdaUpdateWrapper.set(CustomerApply::getCompanyName, req.getCompanyName());

            // 变更人信息
            customerApplyLambdaUpdateWrapper.set(CustomerApply::getModifyName, operationModel.getUserName());
            customerApplyLambdaUpdateWrapper.set(CustomerApply::getModifyNo, operationModel.getEmployerNo());
            customerApplyLambdaUpdateWrapper.set(CustomerApply::getModifyTime, DateUtil.getCurrentDate());

            // where条件
            customerApplyLambdaUpdateWrapper.eq(CustomerApply::getEnterpriseNo, operationModel.getEnterpriseNo());
            customerApplyLambdaUpdateWrapper.eq(CustomerApply::getId, customerApply.getId());

            customerApplyDAO.update(null, customerApplyLambdaUpdateWrapper);


            LambdaUpdateWrapper<CustomerApplyItem> customerApplyItemLambdaUpdateWrapper = Wrappers.lambdaUpdate(CustomerApplyItem.class);
            // 申请单明细
            customerApplyItemLambdaUpdateWrapper.set(CustomerApplyItem::getApplyContent, req.getApplyContent());

            // where条件
            customerApplyItemLambdaUpdateWrapper.eq(CustomerApplyItem::getEnterpriseNo, operationModel.getEnterpriseNo());
            customerApplyItemLambdaUpdateWrapper.eq(CustomerApplyItem::getApplyInstanceId, customerApply.getId());

            customerApplyItemDAO.update(null, customerApplyItemLambdaUpdateWrapper);


            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                @Override
                public void afterCommit() {
                    try {
                        businessLogService.saveLog(DLogLevel.INFO.name(), ViewNameConstant.BDC_CUSTOMER_APPLY_VIEW, req.getApplyInstanceNo(), "编辑客户申请", "编辑客户申请", "", "");
                    } catch (Exception e) {
                        log.error("新增客户申请日志保存失败", e);
                    }
                }
            });
        }

        if (CustomerApplyAuditStatusEnum.TO_BE_AUDITED.getValue().equals(req.getAuditStatus())) {
            //获取自动审批
            boolean autoAudit = false;
            List<GradeAutoReviewConfigVO> gradeAutoReviewConfigVOS = gradeControlService.listApplyAutoReviewConfig(operationModel.getEnterpriseNo(), ViewNameConstant.BDC_CUSTOMER_VIEW, req.getManageOrgNo(), Collections.singletonList(req.getUseOrgNo()), CustomerApplyTypeEnum.ADD_APPLY.getValue().equals(req.getApplyType()));
            if (CollectionUtils.isNotEmpty(gradeAutoReviewConfigVOS)) {
                for (GradeAutoReviewConfigVO gradeAutoReviewConfigVO : gradeAutoReviewConfigVOS) {
                    if (req.getUseOrgNo().equals(gradeAutoReviewConfigVO.getUseOrgNo())) {
                        if (CustomerApplyTypeEnum.ADD_APPLY.getValue().equals(req.getApplyType())) {
                            autoAudit = AUTO_REVIEW.equals(gradeAutoReviewConfigVO.getAddAutoReview());
                        } else {
                            autoAudit = AUTO_REVIEW.equals(gradeAutoReviewConfigVO.getUpdateAutoReview());
                        }
                    }
                }
            }
            if (autoAudit) {
                CustomerApplyApproveReq customerApplyApproveReq = new CustomerApplyApproveReq();
                customerApplyApproveReq.setEnterpriseNo(operationModel.getEnterpriseNo());
                customerApplyApproveReq.setApplyInstanceNo(req.getApplyInstanceNo());
                customerApplyApproveReq.setAuditStatus(CustomerApplyAuditStatusEnum.APPROVED.getValue());
                customerApplyApproveReq.setAuditRemark("自动审核通过");
                customerApplyApproveReq.setAutoAudit(true);
                manageApprove(operationModel, customerApplyApproveReq);
            }
        }


        return req.getApplyNo();
    }

    @Override
    public CustomerApplyDetailVO getDetail(OperationModel operationModel, CustomerApplyGetReq req) {
        CustomerApply customerApply = customerApplyDAO.selectOne(Wrappers.<CustomerApply>lambdaQuery()
                .eq(CustomerApply::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CustomerApply::getApplyInstanceNo, req.getApplyInstanceNo())
                .eq(CustomerApply::getDeleted, DeletedEnum.UN_DELETE.getValue()));
        ValidatorUtils.checkEmptyThrowEx(customerApply, "申请单不存在");

        CustomerApplyItem customerApplyItem = customerApplyItemDAO.selectOne(Wrappers.<CustomerApplyItem>lambdaQuery()
                .eq(CustomerApplyItem::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CustomerApplyItem::getApplyInstanceId, customerApply.getId()));
        ValidatorUtils.checkEmptyThrowEx(customerApplyItem, "申请单不存在");

        return fillDetailsToApplyDetail(operationModel, customerApply, customerApplyItem);
    }

    @Override
    @Transactional
    public Boolean deleteApply(OperationModel operationModel, CustomerApplyDeleteReq req) {
        CustomerApply customerApply = customerApplyDAO.selectOne(Wrappers.<CustomerApply>lambdaQuery()
                .eq(CustomerApply::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CustomerApply::getApplyInstanceNo, req.getApplyInstanceNo())
                .eq(CustomerApply::getDeleted, DeletedEnum.UN_DELETE.getValue()));
        ValidatorUtils.checkEmptyThrowEx(customerApply, "申请单不存在");

        // 当前状态
        ValidatorUtils.checkTrueThrowEx(!(CustomerApplyAuditStatusEnum.DRAFT.getValue().equals(customerApply.getAuditStatus()) || CustomerApplyAuditStatusEnum.REJECTED.getValue().equals(customerApply.getAuditStatus())), "非草稿态和审批失败态的申请单不允许删除");
        // 可执行动作：删除

        CustomerApply newCustomerApply = new CustomerApply();
        newCustomerApply.setDeleted(DeletedEnum.DELETED.getValue());
        CommonUtil.fillModifyInfo(operationModel, newCustomerApply);

        int update = customerApplyDAO.update(newCustomerApply, Wrappers.<CustomerApply>lambdaUpdate()
                .eq(CustomerApply::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CustomerApply::getId, customerApply.getId()));


        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                try {
                    businessLogService.saveLog(DLogLevel.INFO.name(), ViewNameConstant.BDC_CUSTOMER_APPLY_VIEW, req.getApplyInstanceNo(), "删除客户申请", "删除客户申请", "", "");
                } catch (Exception e) {
                    log.error("删除客户申请日志保存失败", e);
                }
            }
        });

        return update > 0;
    }

    @Override
    @Transactional
    public Boolean withDrawApply(OperationModel operationModel, CustomerApplyWithdrawReq req) {
        CustomerApply customerApply = customerApplyDAO.selectOne(Wrappers.<CustomerApply>lambdaQuery()
                .eq(CustomerApply::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CustomerApply::getApplyInstanceNo, req.getApplyInstanceNo())
                .eq(CustomerApply::getDeleted, DeletedEnum.UN_DELETE.getValue()));
        ValidatorUtils.checkEmptyThrowEx(customerApply, "申请单不存在");

        // 当前状态
        ValidatorUtils.checkTrueThrowEx(!CustomerApplyAuditStatusEnum.TO_BE_AUDITED.getValue().equals(customerApply.getAuditStatus()), "非待审核的申请单不允许撤回");
        // 可执行动作：撤回

        CustomerApply newCustomerApply = new CustomerApply();
        newCustomerApply.setAuditStatus(CustomerApplyAuditStatusEnum.DRAFT.getValue());
        CommonUtil.fillModifyInfo(operationModel, newCustomerApply);

        int update = customerApplyDAO.update(newCustomerApply, Wrappers.<CustomerApply>lambdaUpdate()
                .eq(CustomerApply::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CustomerApply::getId, customerApply.getId()));


        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                try {
                    businessLogService.saveLog(DLogLevel.INFO.name(), ViewNameConstant.BDC_CUSTOMER_APPLY_VIEW, req.getApplyInstanceNo(), "撤回客户申请", "撤回客户申请", "", "");
                } catch (Exception e) {
                    log.error("撤回客户申请日志保存失败", e);
                }
            }
        });

        return update > 0;
    }

    private Map<String, Object> manageApproveValidate(OperationModel operationModel, CustomerApplyApproveReq req) {
        // 必填校验
        ValidatorUtils.checkEmptyThrowEx(req, "申请信息不能为空");
        ValidatorUtils.checkEmptyThrowEx(req.getApplyInstanceNo(), "申请编号为空");
        ValidatorUtils.checkEmptyThrowEx(req.getAuditStatus(), "审核状态为空");
        if (CustomerApplyAuditStatusEnum.REJECTED.getValue().equals(req.getAuditStatus())) {
            ValidatorUtils.checkEmptyThrowEx(req.getAuditRemark(), "审核拒绝时，意见不能为空");
        }

        // 范围校验
        if (StringUtils.isNotEmpty(req.getAuditRemark())) {
            ValidatorUtils.checkTrueThrowEx(req.getAuditRemark().length() > 500, "审核原因长度不能超过500");
        }

        // 合法校验
        // 业务校验
        CustomerApply customerApply = customerApplyDAO.selectOne(Wrappers.<CustomerApply>lambdaQuery()
                .eq(CustomerApply::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CustomerApply::getApplyInstanceNo, req.getApplyInstanceNo())
                .eq(CustomerApply::getDeleted, DeletedEnum.UN_DELETE.getValue()));
        ValidatorUtils.checkEmptyThrowEx(customerApply, "申请单不存在");

        CustomerApplyItem customerApplyItem = customerApplyItemDAO.selectOne(Wrappers.<CustomerApplyItem>lambdaQuery()
                .eq(CustomerApplyItem::getEnterpriseNo, operationModel.getEnterpriseNo())
                .eq(CustomerApplyItem::getApplyInstanceId, customerApply.getId()));
        ValidatorUtils.checkEmptyThrowEx(customerApplyItem, "申请单不存在");



        // 当前状态
        ValidatorUtils.checkTrueThrowEx(!CustomerApplyAuditStatusEnum.TO_BE_AUDITED.getValue().equals(customerApply.getAuditStatus()), "非待审核的申请单不允许审核");
        // 可执行动作
        ValidatorUtils.checkTrueThrowEx(!(req.getAuditStatus().equals(CustomerApplyAuditStatusEnum.APPROVED.getValue()) || req.getAuditStatus().equals(CustomerApplyAuditStatusEnum.REJECTED.getValue())), "申请单状态不正确");

        Map<String, Object> result = new HashMap<>();
        result.put("customerApply", customerApply);
        result.put("customerApplyItem", customerApplyItem);

        return result;
    }

    @Override
    @Transactional
    public Boolean manageApprove(OperationModel operationModel, CustomerApplyApproveReq req) {
        // 参数校验
        Map<String, Object> validateResult = manageApproveValidate(operationModel, req);

        CustomerApply customerApply = (CustomerApply) validateResult.get("customerApply");
        CustomerApplyItem customerApplyItem = (CustomerApplyItem) validateResult.get("customerApplyItem");

        if (CustomerApplyAuditStatusEnum.REJECTED.getValue().equals(req.getAuditStatus())) {
            // 更新成“审核失败”
            CustomerApply newCustomerApply = new CustomerApply();
            newCustomerApply.setAuditStatus(CustomerApplyAuditStatusEnum.REJECTED.getValue());
            newCustomerApply.setAuditRemark(req.getAuditRemark());
            newCustomerApply.setAuditNo(operationModel.getEmployerNo());
            newCustomerApply.setAuditName(operationModel.getUserName());
            newCustomerApply.setAuditTime(DateUtil.getCurrentDate());
            CommonUtil.fillModifyInfo(operationModel, newCustomerApply);
            customerApplyDAO.update(newCustomerApply, Wrappers.<CustomerApply>lambdaUpdate()
                    .eq(CustomerApply::getEnterpriseNo, operationModel.getEnterpriseNo())
                    .eq(CustomerApply::getId, customerApply.getId()));
        } else {
            CustomerApplyFormReq customerApplyFormReq = JSON.parseObject(customerApplyItem.getApplyContent(), CustomerApplyFormReq.class);

            // 预校验客户档案
            validateApprove(operationModel, customerApply, customerApplyItem, customerApplyFormReq, req);

            // 更新成“审核通过”
            CustomerApply newCustomerApply = new CustomerApply();
            newCustomerApply.setAuditStatus(CustomerApplyAuditStatusEnum.APPROVED.getValue());
//            newCustomerApply.setApplyResult(ApplyResultEnum.YES.getValue());
            newCustomerApply.setAuditRemark(req.getAuditRemark());
            newCustomerApply.setAuditNo(operationModel.getEmployerNo());
            newCustomerApply.setAuditName(operationModel.getUserName());
            newCustomerApply.setAuditTime(DateUtil.getCurrentDate());
            CommonUtil.fillModifyInfo(operationModel, newCustomerApply);
            customerApplyDAO.update(newCustomerApply, Wrappers.<CustomerApply>lambdaUpdate()
                    .eq(CustomerApply::getEnterpriseNo, operationModel.getEnterpriseNo())
                    .eq(CustomerApply::getId, customerApply.getId()));

            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                @Override
                public void afterCommit() {
                    int errorType = 0;
                    Exception errorException = null;

                    Map<String, Object> approveResult = null;

                    try {
                        approveResult = transactionTemplate.execute(status ->
                                customerV2Service.manageApprove(operationModel, customerApply, customerApplyItem, customerApplyFormReq, req)
                        );
                    } catch (Exception e) {
                        errorType = 1;
                        log.error("客户审核通过后处理客户档案异常", e);

                        errorException = e;
                    }

                    CustomerVO customerVO = null;

                    if (0 == errorType) {
                        CustomerGetReq customerGetReq = new CustomerGetReq();
                        customerGetReq.setEnterpriseNo(operationModel.getEnterpriseNo());
                        customerGetReq.setCustomerCode((String) approveResult.get("customerCode"));
                        customerGetReq.setUseOrgNo(customerApply.getUseOrgNo());
                        customerVO = customerV2Service.getCustomer(operationModel, customerGetReq);

                        try {
                            CompanyCertUpsertRequest companyCertUpsertRequest = new CompanyCertUpsertRequest();
                            companyCertUpsertRequest.setEnterpriseNo(operationModel.getEnterpriseNo());
                            companyCertUpsertRequest.setOrgNo(customerApply.getUseOrgNo());
                            companyCertUpsertRequest.setCompanyNo(customerVO.getCompanyNo());
                            companyCertUpsertRequest.setSourceDocType(CertSourceTypeEnum.CUSTOMER.getValue());
                            companyCertUpsertRequest.setSourceDocCode(customerVO.getCustomerCode());
                            List<CompanyCertBasicRequest> companyCertList = new ArrayList<>();
                            companyCertList.addAll(BeanUtil.copyFieldsListForJSON(customerApplyFormReq.getCompanyCertList(), CompanyCertBasicRequest.class));
                            companyCertList.addAll(BeanUtil.copyFieldsListForJSON(customerApplyFormReq.getCustomerCertList(), CompanyCertBasicRequest.class));
                            companyCertUpsertRequest.setCompanyCertList(companyCertList);
                            Boolean upsert = certService.upsert(companyCertUpsertRequest);
                            if (!upsert) {
                                throw new BusinessException(ErrorCode.param_invalid_msg, "证照保存失败");
                            }

                            //TODO shenbin 发送事件（企业证照修改）

                        } catch (Exception e) {
                            errorType = 2;
                            log.error("客户审核通过后处理证照异常", e);

                            errorException = e;
                        }
                    }

                    final CustomerVO finalCustomerVO = customerVO;
                    final Map<String, Object> finalApproveResult = approveResult;
                    final int finalErrorType = errorType;
                    final Exception finalErrorException = errorException;

                    transactionTemplate.execute(new TransactionCallbackWithoutResult() {
                        protected void doInTransactionWithoutResult(TransactionStatus status) {
                            CommonUtil.fillModifyInfo(operationModel, newCustomerApply);
                            newCustomerApply.setApplyResult(ApplyResultEnum.SUCC.getValue());

                            if (1 != finalErrorType) {
                                CustomerApplyItem newCustomerApplyItem = new CustomerApplyItem();

                                // 回写新生成的编码
                                if (StringUtils.isEmpty(customerApplyFormReq.getCustomerCode())) {
                                    newCustomerApply.setCustomerCode((String) finalApproveResult.get("customerCode"));

                                    customerApplyFormReq.setCustomerCode((String) finalApproveResult.get("customerCode"));
                                    newCustomerApplyItem.setApplyContent(JSON.toJSONString(customerApplyFormReq));
                                }

                                // 审核通过时的镜像
                                if (finalApproveResult.containsKey("preApproveCustomer") && null != finalApproveResult.get("preApproveCustomer")) {
                                    newCustomerApplyItem.setPreApproveContent(JSON.toJSONString(packPreCustomer2ApproveForm(operationModel, customerApply, (CustomerVO) finalApproveResult.get("preApproveCustomer"))));
                                }
                                newCustomerApplyItem.setApproveContent(JSON.toJSONString(packCustomer2ApproveForm(operationModel, customerApply, finalCustomerVO)));
                                customerApplyItemDAO.update(newCustomerApplyItem, Wrappers.<CustomerApplyItem>lambdaUpdate()
                                        .eq(CustomerApplyItem::getEnterpriseNo, operationModel.getEnterpriseNo())
                                        .eq(CustomerApplyItem::getApplyInstanceId, customerApply.getId()));
                            }


                            // 更新审批后执行结果
                            if (0 != finalErrorType) {
                                newCustomerApply.setApplyResult(ApplyResultEnum.FAIL.getValue());

                                String failReason = null;
                                if (1 == finalErrorType) {
//                                    failReason = "客户审核通过后处理供应商档案异常：" + (finalErrorException instanceof BusinessException ? finalErrorException.getMessage() : "系统异常");
                                    failReason = "客户档案创建失败";
                                } else {
//                                    failReason = "客户审核通过后处理证照异常：" + (finalErrorException instanceof BusinessException ? finalErrorException.getMessage() : "系统异常");
                                    failReason = "客户资质创建失败";
                                }

                                newCustomerApply.setFailReason(failReason);
                            }
                            customerApplyDAO.update(newCustomerApply, Wrappers.<CustomerApply>lambdaUpdate()
                                    .eq(CustomerApply::getEnterpriseNo, operationModel.getEnterpriseNo())
                                    .eq(CustomerApply::getId, customerApply.getId()));
                        }
                    });
                }
            });
        }

        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                String op = req.isAutoAudit() ? "自动" : "手动";
                try {
                    businessLogService.saveLog(DLogLevel.INFO.name(), ViewNameConstant.BDC_CUSTOMER_APPLY_VIEW, req.getApplyInstanceNo(), op + "审核客户申请" + CustomerApplyAuditStatusEnum.getNameByValue(req.getAuditStatus()), op + "审核客户申请" + CustomerApplyAuditStatusEnum.getNameByValue(req.getAuditStatus()), "", req.getAuditRemark());
                } catch (Exception e) {
                    log.error(op + "审核客户申请日志保存失败", e);
                }
            }
        });

        return true;
    }

    private void validateApprove(OperationModel operationModel, CustomerApply customerApply, CustomerApplyItem customerApplyItem, CustomerApplyFormReq customerApplyFormReq, CustomerApplyApproveReq req) {
        // 校验管理权
        if (!req.isAutoAudit()) {
            List<String> manageNoList = gradeControlService.listMgrOrgNos(operationModel.getEnterpriseNo(), BillNameConstant.BDC_CUSTOMER_BILL);
            if (CollectionUtils.isEmpty(manageNoList) || !manageNoList.contains(customerApply.getManageOrgNo())) {
                List<OrganizationVo> manageOrgList = organizationService.findListNoAuth(operationModel.getEnterpriseNo(), Collections.singletonList(customerApply.getManageOrgNo()));
                if (CollectionUtils.isNotEmpty(manageOrgList)) {
                    for (OrganizationVo manageOrg : manageOrgList) {
                        if (customerApply.getManageOrgNo().equals(manageOrg.getOrgNo())) {
                            throw new BusinessException(ErrorCode.param_invalid_msg, String.format("管理组织【%s】没有档案管理权，无法审核通过", manageOrg.getOrgName()));
                        }
                    }
                }
                throw new BusinessException(ErrorCode.param_invalid_msg, "管理组织没有档案管理权，无法审核通过");
            }
        }

        /*
            客户档案中已存在同名客户，未分派，但是管理组织不同：名称【客户名称】的客户档案已存在，管理组织是【组织名称】，与申请单中的管理组织不一致，无法审核通过。
            客户档案中已存在同名客户，已分派：【使用组织名称】的客户档案中已存在名称【客户名称】的客户，无法审核通过。
             */
        if (CustomerApplyTypeEnum.ADD_APPLY.getValue().equals(customerApply.getApplyType())) {
            CustomerV2 customerV2 = null;
            if (StringUtils.isNotEmpty(customerApply.getCustomerCode())) {
                customerV2 = customerV2Service.getCustomerByCode(operationModel, customerApply.getCustomerCode());
            }
            if (null == customerV2) {
                customerV2 = customerV2Service.getCustomerByName(operationModel, customerApply.getCustomerName());
            }
            if (null != customerV2) {
                if (!customerV2.getManageOrgNo().equals(customerApply.getManageOrgNo())) {
                    List<OrganizationVo> organizationVoList = organizationService.findListNoAuth(operationModel.getEnterpriseNo(), Collections.singletonList(customerV2.getManageOrgNo()));
                    final Map<String, OrganizationVo> orgNo2OrganizationVo = organizationVoList.stream().collect(Collectors.toMap(OrganizationVo::getOrgNo, Function.identity()));

                    String errorMessage = String.format("名称【%s】的客户档案已存在，管理组织是【%s】，与申请单中的管理组织不一致，无法审核通过。", customerApply.getCustomerName(), orgNo2OrganizationVo.containsKey(customerV2.getManageOrgNo()) ? orgNo2OrganizationVo.get(customerV2.getManageOrgNo()).getOrgName() : "");
                    throw new BusinessException(ErrorCode.param_invalid_msg, errorMessage);
                }

                CustomerBase customerBaseQuery = new CustomerBase();
                customerBaseQuery.setCustomerCode(customerV2.getCustomerCode());
                customerBaseQuery.setManageOrgNo(customerV2.getManageOrgNo());
                customerBaseQuery.setUseOrgNo(customerApply.getUseOrgNo());
                boolean hasAssigned = customerV2Service.hasAssignCustomer(operationModel, customerBaseQuery);
                ValidatorUtils.checkTrueThrowEx(hasAssigned, String.format("【%s】的客户档案中已存在名称【%s】的客户，无法审核通过。", customerApply.getUseOrgName(), customerApply.getCustomerName()));

                CompanyV2 companyV2 = companyV2Service.findByEnterpriseNoAndCompanyNo(operationModel.getEnterpriseNo(), customerV2.getCompanyNo());
                if (!(customerApplyFormReq.getCompanyName().equals(companyV2.getCompanyName()) && customerApplyFormReq.getUnifiedSocialCode().equals(companyV2.getUnifiedSocialCode()))) {
                    String errorMessage = String.format("名称【%s】的客户档案的企业名称或统一社会信用代码与申请单中的不一致，无法审核通过。", customerApply.getCustomerName());
                    throw new BusinessException(ErrorCode.param_invalid_msg, errorMessage);
                }
            }
        }

        try {
            Map<String, String> preValidateResult = customerV2Service.preValidateManageApprove(operationModel, customerApply, customerApplyItem, customerApplyFormReq, req);
            String companyNo = preValidateResult.get("companyNo");
            if (StringUtils.isEmpty(companyNo)) {
                throw new Exception("companyNo is empty");
            }

            String customerCode = preValidateResult.get("customerCode");
            if (StringUtils.isEmpty(customerCode)) {
                throw new Exception("customerCode is empty");
            }

            CompanyCertUpsertRequest companyCertUpsertRequest = new CompanyCertUpsertRequest();
            companyCertUpsertRequest.setEnterpriseNo(operationModel.getEnterpriseNo());
            companyCertUpsertRequest.setOrgNo(customerApply.getUseOrgNo());
            companyCertUpsertRequest.setCompanyNo(companyNo);
            companyCertUpsertRequest.setSourceDocType(CertSourceTypeEnum.CUSTOMER.getValue());
            companyCertUpsertRequest.setSourceDocCode(customerCode);
            List<CompanyCertBasicRequest> companyCertList = new ArrayList<>();
            companyCertList.addAll(BeanUtil.copyFieldsListForJSON(customerApplyFormReq.getCompanyCertList(), CompanyCertBasicRequest.class));
            companyCertList.addAll(BeanUtil.copyFieldsListForJSON(customerApplyFormReq.getCustomerCertList(), CompanyCertBasicRequest.class));
            companyCertUpsertRequest.setCompanyCertList(companyCertList);
            certService.preValidateUpsert(companyCertUpsertRequest);
        } catch (Exception e) {
            if (e instanceof BusinessException) {
                throw (BusinessException) e;
            }

            log.error(req.getApplyInstanceNo() + "审核预校验异常", e);

            throw new BusinessException(ErrorCode.param_invalid_msg, "审核预校验异常");
        }
    }

    private CustomerApplyFormReq packPreCustomer2ApproveForm(OperationModel operationModel, CustomerApply customerApply, CustomerVO customerVO) {
        CustomerApplyFormReq customerApplyFormReq = new CustomerApplyFormReq();

        // --------新增和变更共有的信息--------
        // 客户信息
        customerApplyFormReq.setCustomerNo(customerVO.getCustomerNo());
        customerApplyFormReq.setCustomerCode(customerVO.getCustomerCode());
        customerApplyFormReq.setCustomerName(customerVO.getCustomerName());
        customerApplyFormReq.setCustomerNameEn(customerVO.getCustomerNameEn());
        customerApplyFormReq.setMnemonicCode(customerVO.getMnemonicCode());
        customerApplyFormReq.setTransactionType(customerVO.getTransactionType());
        customerApplyFormReq.setTransactionTypeName(customerVO.getTransactionTypeName());
        customerApplyFormReq.setCustomerCategoryNo(customerVO.getCustomerCategoryNo());
        customerApplyFormReq.setCustomerCategoryName(customerVO.getCustomerCategoryName());
        customerApplyFormReq.setRetailInvestors(customerVO.getRetailInvestors());
        customerApplyFormReq.setRetailInvestorsName(customerVO.getRetailInvestorsName());
        customerApplyFormReq.setIsAssociatedEnterprise(customerVO.getIsAssociatedEnterprise());
        customerApplyFormReq.setIsAssociatedEnterpriseName(customerVO.getIsAssociatedEnterpriseName());
        customerApplyFormReq.setAssociatedOrgNo(customerVO.getAssociatedOrgNo());
        customerApplyFormReq.setAssociatedOrgCode(customerVO.getAssociatedOrgCode());
        customerApplyFormReq.setAssociatedOrgName(customerVO.getAssociatedOrgName());
        customerApplyFormReq.setIsGspControl(customerVO.getIsGspControl());
        customerApplyFormReq.setIsGspControlName(customerVO.getIsGspControlName());
        customerApplyFormReq.setRemark(customerVO.getRemark());

        // 企业信息
        customerApplyFormReq.setCompanyNo(customerVO.getCompanyNo());
        customerApplyFormReq.setCompanyName(customerVO.getCompanyName());
        customerApplyFormReq.setUnifiedSocialCode(customerVO.getUnifiedSocialCode());
        customerApplyFormReq.setFactoryType(customerVO.getFactoryType());
        customerApplyFormReq.setFactoryTypeName(customerVO.getFactoryTypeName());
        customerApplyFormReq.setCountryRegionId(customerVO.getCountryRegionId());
        customerApplyFormReq.setCountryRegionName(customerVO.getCountryRegionName());
        customerApplyFormReq.setTaxCategory(customerVO.getTaxCategory());
        customerApplyFormReq.setTaxCategoryName(customerVO.getTaxCategoryName());
        customerApplyFormReq.setEconomicType(customerVO.getEconomicType());
        customerApplyFormReq.setEconomicTypeName(customerVO.getEconomicTypeName());
        customerApplyFormReq.setIsMedicalInstitution(customerVO.getIsMedicalInstitution());
        customerApplyFormReq.setIsMedicalInstitutionName(customerVO.getIsMedicalInstitutionName());
        customerApplyFormReq.setInstitutionalType(customerVO.getInstitutionalType());
        customerApplyFormReq.setInstitutionalTypeName(customerVO.getInstitutionalTypeName());
        customerApplyFormReq.setHospitalType(customerVO.getHospitalType());
        customerApplyFormReq.setHospitalTypeName(customerVO.getHospitalTypeName());
        customerApplyFormReq.setHospitalClass(customerVO.getHospitalClass());
        customerApplyFormReq.setHospitalClassName(customerVO.getHospitalClassName());

        // 企业银行信息
        customerApplyFormReq.setBankList(BeanUtil.copyFieldsList(customerVO.getBankList(), CompanyBankReq.class));

        //企业资料信息
        customerApplyFormReq.setCompanyCertList(BeanUtil.copyFieldsList(customerVO.getCompanyCertList(), CompanyCertReq.class));

        return customerApplyFormReq;
    }

    private CustomerApplyFormReq packCustomer2ApproveForm(OperationModel operationModel, CustomerApply customerApply, CustomerVO customerVO) {
        CustomerApplyFormReq customerApplyFormReq = new CustomerApplyFormReq();

        // --------新增和变更共有的信息--------
        // 客户信息
        customerApplyFormReq.setCustomerNo(customerVO.getCustomerNo());
        customerApplyFormReq.setCustomerCode(customerVO.getCustomerCode());
        customerApplyFormReq.setCustomerName(customerVO.getCustomerName());
        customerApplyFormReq.setCustomerNameEn(customerVO.getCustomerNameEn());
        customerApplyFormReq.setMnemonicCode(customerVO.getMnemonicCode());
        customerApplyFormReq.setTransactionType(customerVO.getTransactionType());
        customerApplyFormReq.setTransactionTypeName(customerVO.getTransactionTypeName());
        customerApplyFormReq.setCustomerCategoryNo(customerVO.getCustomerCategoryNo());
        customerApplyFormReq.setCustomerCategoryName(customerVO.getCustomerCategoryName());
        customerApplyFormReq.setRetailInvestors(customerVO.getRetailInvestors());
        customerApplyFormReq.setRetailInvestorsName(customerVO.getRetailInvestorsName());
        customerApplyFormReq.setIsAssociatedEnterprise(customerVO.getIsAssociatedEnterprise());
        customerApplyFormReq.setIsAssociatedEnterpriseName(customerVO.getIsAssociatedEnterpriseName());
        customerApplyFormReq.setAssociatedOrgNo(customerVO.getAssociatedOrgNo());
        customerApplyFormReq.setAssociatedOrgCode(customerVO.getAssociatedOrgCode());
        customerApplyFormReq.setAssociatedOrgName(customerVO.getAssociatedOrgName());
        customerApplyFormReq.setIsGspControl(customerVO.getIsGspControl());
        customerApplyFormReq.setIsGspControlName(customerVO.getIsGspControlName());
        customerApplyFormReq.setRemark(customerVO.getRemark());

        // 企业信息
        customerApplyFormReq.setCompanyNo(customerVO.getCompanyNo());
        customerApplyFormReq.setCompanyName(customerVO.getCompanyName());
        customerApplyFormReq.setUnifiedSocialCode(customerVO.getUnifiedSocialCode());
        customerApplyFormReq.setFactoryType(customerVO.getFactoryType());
        customerApplyFormReq.setFactoryTypeName(customerVO.getFactoryTypeName());
        customerApplyFormReq.setCountryRegionId(customerVO.getCountryRegionId());
        customerApplyFormReq.setCountryRegionName(customerVO.getCountryRegionName());
        customerApplyFormReq.setTaxCategory(customerVO.getTaxCategory());
        customerApplyFormReq.setTaxCategoryName(customerVO.getTaxCategoryName());
        customerApplyFormReq.setEconomicType(customerVO.getEconomicType());
        customerApplyFormReq.setEconomicTypeName(customerVO.getEconomicTypeName());
        customerApplyFormReq.setIsMedicalInstitution(customerVO.getIsMedicalInstitution());
        customerApplyFormReq.setIsMedicalInstitutionName(customerVO.getIsMedicalInstitutionName());
        customerApplyFormReq.setInstitutionalType(customerVO.getInstitutionalType());
        customerApplyFormReq.setInstitutionalTypeName(customerVO.getInstitutionalTypeName());
        customerApplyFormReq.setHospitalType(customerVO.getHospitalType());
        customerApplyFormReq.setHospitalTypeName(customerVO.getHospitalTypeName());
        customerApplyFormReq.setHospitalClass(customerVO.getHospitalClass());
        customerApplyFormReq.setHospitalClassName(customerVO.getHospitalClassName());

        // 企业银行信息
        customerApplyFormReq.setBankList(BeanUtil.copyFieldsList(customerVO.getBankList(), CompanyBankReq.class));

        //企业资料信息
        customerApplyFormReq.setCompanyCertList(BeanUtil.copyFieldsList(customerVO.getCompanyCertList(), CompanyCertReq.class));

        // --------新增申请独有的业务信息--------
        if (CustomerApplyTypeEnum.ADD_APPLY.getValue().equals(customerApply.getApplyType())) {
            customerApplyFormReq.setCooperationMode(customerVO.getCooperationMode());
            customerApplyFormReq.setCooperationModeName(customerVO.getCooperationModeName());
            customerApplyFormReq.setBusinessType(customerVO.getBusinessType());
            customerApplyFormReq.setBusinessTypeName(customerVO.getBusinessTypeName());
            customerApplyFormReq.setPriceCategoryCode(customerVO.getPriceCategoryCode());
            customerApplyFormReq.setPriceCategoryName(customerVO.getPriceCategoryName());
            customerApplyFormReq.setCurrencyId(customerVO.getCurrencyId());
            customerApplyFormReq.setCurrencyName(customerVO.getCurrencyName());
            customerApplyFormReq.setSettlementModes(customerVO.getSettlementModes());
            customerApplyFormReq.setSettlementModesName(customerVO.getSettlementModesName());
            customerApplyFormReq.setReceiveAgreement(customerVO.getReceiveAgreement());
            customerApplyFormReq.setReceiveCondition(customerVO.getReceiveCondition());
            customerApplyFormReq.setCreditAmount(customerVO.getCreditAmount());
            customerApplyFormReq.setCreditDates(customerVO.getCreditDates());
            customerApplyFormReq.setCoopStartTime(customerVO.getCoopStartTime());
            customerApplyFormReq.setCoopEndTime(customerVO.getCoopEndTime());
            customerApplyFormReq.setOwnerCompany(customerVO.getOwnerCompany());


            //使用组织资料信息
            customerApplyFormReq.setCustomerCertList(BeanUtil.copyFieldsList(customerVO.getCustomerCertList(), CompanyCertReq.class));
            customerApplyFormReq.setLinkmanList(BeanUtil.copyFieldsList(customerVO.getLinkmanList(), CompanyLinkmanReq.class));
            customerApplyFormReq.setLinkAddressList(BeanUtil.copyFieldsList(customerVO.getLinkAddressList(), CompanyShippingAddressReq.class));
            customerApplyFormReq.setCustomerManList(BeanUtil.copyFieldsList(customerVO.getSalesManList(), CustomerSalesManReq.class));
            customerApplyFormReq.setInvoiceList(BeanUtil.copyFieldsList(customerVO.getInvoiceList(), CustomerInvoiceReq.class));
        }

        return customerApplyFormReq;
    }

    private CustomerApplyFormReq packLatestDiff(CustomerVO customerVO) {
        CustomerApplyFormReq customerApplyFormReq = new CustomerApplyFormReq();

        // 客户信息
        customerApplyFormReq.setCustomerNo(customerVO.getCustomerNo());
        customerApplyFormReq.setCustomerCode(customerVO.getCustomerCode());
        customerApplyFormReq.setCustomerName(customerVO.getCustomerName());
        customerApplyFormReq.setCustomerNameEn(customerVO.getCustomerNameEn());
        customerApplyFormReq.setMnemonicCode(customerVO.getMnemonicCode());
        customerApplyFormReq.setTransactionType(customerVO.getTransactionType());
        customerApplyFormReq.setTransactionTypeName(customerVO.getTransactionTypeName());
        customerApplyFormReq.setCustomerCategoryNo(customerVO.getCustomerCategoryNo());
        customerApplyFormReq.setCustomerCategoryName(customerVO.getCustomerCategoryName());
        customerApplyFormReq.setRetailInvestors(customerVO.getRetailInvestors());
        customerApplyFormReq.setRetailInvestorsName(customerVO.getRetailInvestorsName());
        customerApplyFormReq.setIsAssociatedEnterprise(customerVO.getIsAssociatedEnterprise());
        customerApplyFormReq.setIsAssociatedEnterpriseName(customerVO.getIsAssociatedEnterpriseName());
        customerApplyFormReq.setAssociatedOrgNo(customerVO.getAssociatedOrgNo());
        customerApplyFormReq.setAssociatedOrgCode(customerVO.getAssociatedOrgCode());
        customerApplyFormReq.setAssociatedOrgName(customerVO.getAssociatedOrgName());
        customerApplyFormReq.setIsGspControl(customerVO.getIsGspControl());
        customerApplyFormReq.setIsGspControlName(customerVO.getIsGspControlName());
        customerApplyFormReq.setRemark(customerVO.getRemark());

        // 企业信息
        customerApplyFormReq.setCompanyNo(customerVO.getCompanyNo());
        customerApplyFormReq.setCompanyName(customerVO.getCompanyName());
        customerApplyFormReq.setUnifiedSocialCode(customerVO.getUnifiedSocialCode());
        customerApplyFormReq.setFactoryType(customerVO.getFactoryType());
        customerApplyFormReq.setFactoryTypeName(customerVO.getFactoryTypeName());
        customerApplyFormReq.setCountryRegionId(customerVO.getCountryRegionId());
        customerApplyFormReq.setCountryRegionName(customerVO.getCountryRegionName());
        customerApplyFormReq.setAddress(customerVO.getAddress());
        customerApplyFormReq.setTaxCategory(customerVO.getTaxCategory());
        customerApplyFormReq.setTaxCategoryName(customerVO.getTaxCategoryName());
        customerApplyFormReq.setEconomicType(customerVO.getEconomicType());
        customerApplyFormReq.setEconomicTypeName(customerVO.getEconomicTypeName());
        customerApplyFormReq.setIsMedicalInstitution(customerVO.getIsMedicalInstitution());
        customerApplyFormReq.setIsMedicalInstitutionName(customerVO.getIsMedicalInstitutionName());
        customerApplyFormReq.setInstitutionalType(customerVO.getInstitutionalType());
        customerApplyFormReq.setInstitutionalTypeName(customerVO.getInstitutionalTypeName());
        customerApplyFormReq.setHospitalType(customerVO.getHospitalType());
        customerApplyFormReq.setHospitalTypeName(customerVO.getHospitalTypeName());
        customerApplyFormReq.setHospitalClass(customerVO.getHospitalClass());
        customerApplyFormReq.setHospitalClassName(customerVO.getHospitalClassName());

        // 企业银行信息
        customerApplyFormReq.setBankList(BeanUtil.copyFieldsList(customerVO.getBankList(), CompanyBankReq.class));

        //企业资料信息
        customerApplyFormReq.setCompanyCertList(BeanUtil.copyFieldsList(customerVO.getCompanyCertList(), CompanyCertReq.class));

        return customerApplyFormReq;
    }

    private CustomerApplyDetailVO fillDetailsToApplyDetail(OperationModel operationModel, CustomerApply customerApply, CustomerApplyItem customerApplyItem) {
        List<OrganizationVo> organizationVoList = organizationService.findListNoAuth(operationModel.getEnterpriseNo(),
                Lists.newArrayList(customerApply.getManageOrgNo(), customerApply.getUseOrgNo(), customerApply.getApplyOrgNo()));
        final Map<String, OrganizationVo> orgNo2OrganizationVo = organizationVoList.stream().collect(Collectors.toMap(OrganizationVo::getOrgNo, Function.identity()));


        List<EmployeeVo> employeeList = employeeService.getEmployeeList(operationModel.getEnterpriseNo(),
                Lists.newArrayList(customerApply.getApplyNo(), customerApply.getAuditNo()));
        final Map<String, EmployeeVo> employeeNo2EmployeeVo = employeeList.stream().collect(Collectors.toMap(EmployeeVo::getEmployeeNo, Function.identity()));

        final Map<String, CustomDocResponse> applyRasonMap = customDocService.getItemByCustomDocCode(operationModel.getEnterpriseNo(), SystemConstant.DSRP_APPLY_REASON);


        CustomerApplyDetailVO result = BeanUtil.copyFields(customerApply, CustomerApplyDetailVO.class);

        // 补充申请时填写的详情JSON
        result.setApplyContent(customerApplyItem.getApplyContent());
        result.setPreApproveContent(customerApplyItem.getPreApproveContent());
        result.setApproveContent(customerApplyItem.getApproveContent());

        // 补充审核状态名称
        result.setAuditStatusName(CustomerApplyAuditStatusEnum.getNameByValue(customerApply.getAuditStatus()));

        // 补充申请原因名称
        if (StringUtils.isNotEmpty(customerApply.getApplyReason())) {
            result.setApplyReasonName(applyRasonMap.getOrDefault(customerApply.getApplyReason(), new CustomDocResponse()).getDocItemName());
        }

        // 补充申请类型名称
        result.setApplyTypeName(CustomerApplyTypeEnum.getNameByValue(customerApply.getApplyType()));

        // 补充申请组织名称
        if (orgNo2OrganizationVo.containsKey(customerApply.getApplyOrgNo())) {
            result.setApplyOrgName(orgNo2OrganizationVo.get(customerApply.getApplyOrgNo()).getOrgName());
        }

        // 补充使用组织名称
        if (orgNo2OrganizationVo.containsKey(customerApply.getUseOrgNo())) {
            result.setUseOrgName(orgNo2OrganizationVo.get(customerApply.getUseOrgNo()).getOrgName());
        }

        // 补充管理组织名称
        if (orgNo2OrganizationVo.containsKey(customerApply.getManageOrgNo())) {
            result.setManageOrgName(orgNo2OrganizationVo.get(customerApply.getManageOrgNo()).getOrgName());
        }

        // 补充申请人姓名
        if (employeeNo2EmployeeVo.containsKey(customerApply.getApplyNo())) {
            result.setApplyName(employeeNo2EmployeeVo.get(customerApply.getApplyNo()).getUserName());
        }

        // 补充审核人姓名
        if (employeeNo2EmployeeVo.containsKey(customerApply.getAuditNo())) {
            result.setAuditName(employeeNo2EmployeeVo.get(customerApply.getAuditNo()).getUserName());
        }

        // 补充执行结果
        result.setApplyResultName(null != customerApply.getApplyResult() ? CommonIfEnum.getNameByValue(customerApply.getApplyResult()) : "");


        // 获取客户和企业信息，用于前端进行diff
        if (CustomerApplyAuditStatusEnum.APPROVED.getValue().equals(customerApply.getAuditStatus())) {
            result.setLatestContent(customerApplyItem.getPreApproveContent());
        } else {
            CustomerVO customerVO = null;

            if (StringUtils.isNotEmpty(customerApply.getCustomerCode())) {
                customerVO = customerV2Service.getDetailCustomerByCode(operationModel, customerApply.getUseOrgNo(), customerApply.getCustomerCode());
            }
            if (null == customerVO) {
                customerVO = customerV2Service.getDetailCustomerByName(operationModel, customerApply.getUseOrgNo(), customerApply.getCustomerName());
            }

            if (null != customerVO) {
                CustomerApplyFormReq customerApplyDiffVO = packLatestDiff(customerVO);

                result.setLatestContent(JSON.toJSONString(customerApplyDiffVO));
            }
        }

        return result;
    }

    private List<CustomerApplyPageVO> completeSupplementPageVO(String enterpriseNo, List<CustomerApply> data) {
        List<CustomerApplyPageVO> result = new ArrayList<>();

        List<OrganizationVo> organizationVoList = organizationService.findListNoAuth(enterpriseNo, new ArrayList<>(data.stream().flatMap(customerApply -> Stream.of(customerApply.getManageOrgNo(), customerApply.getUseOrgNo(), customerApply.getApplyOrgNo())).collect(Collectors.toSet())));
        final Map<String, OrganizationVo> orgNo2OrganizationVo = organizationVoList.stream().collect(Collectors.toMap(OrganizationVo::getOrgNo, Function.identity()));

        List<EmployeeVo> employeeList = employeeService.getEmployeeList(enterpriseNo, new ArrayList<>(data.stream().flatMap(customerApply -> Stream.of(customerApply.getApplyNo(), customerApply.getAuditNo())).collect(Collectors.toSet())));
        final Map<String, EmployeeVo> employeeNo2EmployeeVo = employeeList.stream().collect(Collectors.toMap(EmployeeVo::getEmployeeNo, Function.identity()));

        final Map<String, CustomDocResponse> applyRasonMap = customDocService.getItemByCustomDocCode(enterpriseNo, SystemConstant.DSRP_APPLY_REASON);

        data.forEach(customerapply -> {
            CustomerApplyPageVO customerApplyPageVO = new CustomerApplyPageVO();
            org.springframework.beans.BeanUtils.copyProperties(customerapply, customerApplyPageVO);

            // 补充审核状态名称
            customerApplyPageVO.setAuditStatusName(CustomerApplyAuditStatusEnum.getNameByValue(customerapply.getAuditStatus()));

            // 补充申请类型名称
            customerApplyPageVO.setApplyTypeName(CustomerApplyTypeEnum.getNameByValue(customerapply.getApplyType()));

            // 补充申请原因名称
            if (StringUtils.isNotEmpty(customerapply.getApplyReason())) {
                customerApplyPageVO.setApplyReasonName(applyRasonMap.getOrDefault(customerapply.getApplyReason(), new CustomDocResponse()).getDocItemName());
            }

            // 补充申请组织名称
            if (orgNo2OrganizationVo.containsKey(customerapply.getApplyOrgNo())) {
                customerApplyPageVO.setApplyOrgName(orgNo2OrganizationVo.get(customerapply.getApplyOrgNo()).getOrgName());
            }

            // 补充使用组织名称
            if (orgNo2OrganizationVo.containsKey(customerapply.getUseOrgNo())) {
                customerApplyPageVO.setUseOrgName(orgNo2OrganizationVo.get(customerapply.getUseOrgNo()).getOrgName());
            }

            // 补充管理组织名称
            if (orgNo2OrganizationVo.containsKey(customerapply.getManageOrgNo())) {
                customerApplyPageVO.setManageOrgName(orgNo2OrganizationVo.get(customerapply.getManageOrgNo()).getOrgName());
            }

            // 补充申请人姓名
            if (employeeNo2EmployeeVo.containsKey(customerapply.getApplyNo())) {
                customerApplyPageVO.setApplyName(employeeNo2EmployeeVo.get(customerapply.getApplyNo()).getUserName());
            }

            // 补充审核人姓名
            if (employeeNo2EmployeeVo.containsKey(customerapply.getAuditNo())) {
                customerApplyPageVO.setAuditName(employeeNo2EmployeeVo.get(customerapply.getAuditNo()).getUserName());
            }

            // 补充执行结果
            customerApplyPageVO.setApplyResultName(null != customerapply.getApplyResult() ? ApplyResultEnum.getNameByValue(customerapply.getApplyResult()) : "");

            result.add(customerApplyPageVO);
        });
        return result;
    }
}
