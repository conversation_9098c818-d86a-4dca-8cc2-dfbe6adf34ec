package com.yyigou.dsrp.cdc.service.customer.impl;

import com.yyigou.ddc.common.util.ValidatorUtils;
import com.yyigou.ddc.services.ddc.uim.vo.OrganizationExtandVo;
import com.yyigou.ddc.services.ddc.uim.vo.OrganizationVo;
import com.yyigou.dsrp.cdc.api.customer.dto.CustomerInvoiceQueryDTO;
import com.yyigou.dsrp.cdc.api.customer.vo.CustomerInvoiceVO;
import com.yyigou.dsrp.cdc.client.customer.response.response.CustomerInvoiceResponse;
import com.yyigou.dsrp.cdc.common.enums.InvoiceTypeEnum;
import com.yyigou.dsrp.cdc.common.util.OperationModel;
import com.yyigou.dsrp.cdc.dao.customer.CustomerDAO;
import com.yyigou.dsrp.cdc.dao.customer.CustomerInvoiceDAO;
import com.yyigou.dsrp.cdc.dao.customer.entity.Customer;
import com.yyigou.dsrp.cdc.dao.customer.entity.CustomerInvoice;
import com.yyigou.dsrp.cdc.manager.integration.org.UimTenantService;
import com.yyigou.dsrp.cdc.service.customer.CustomerInvoiceService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 *
 **/

@Service
@RequiredArgsConstructor
@Slf4j
public class CustomerInvoiceServiceImpl implements CustomerInvoiceService {

    private final UimTenantService uimTenantService;

    private final CustomerDAO customerDAO;

    private final CustomerInvoiceDAO customerInvoiceDAO;

    /**
     * 根据组织编号 + 客户编码查询客户税务信息，需要先校验当前用户是否有对应组织的权限
     * @param operationModel
     * @param params
     * @return
     */
    @Override
    public List<CustomerInvoiceVO> getInvoiceListByCustomerCode(OperationModel operationModel, CustomerInvoiceQueryDTO params) {
        ValidatorUtils.checkTrueThrowEx(StringUtils.isEmpty(params.getCustomerCode()), "客户编码不存在");
        ValidatorUtils.checkTrueThrowEx(StringUtils.isEmpty(params.getOrgNo()), "组织编号不存在");
        //获取有权限的组织列表
        List<OrganizationVo> organizationList = uimTenantService.getAuthEnterpriseList(operationModel.getEnterpriseNo(), operationModel.getUserNo(), operationModel.getEmployerNo());
        final Optional<OrganizationVo> optional = organizationList.stream().filter(t -> params.getOrgNo().equals(t.getOrgNo())).findAny();
        if (!optional.isPresent()) {
            //无权限查询
            return new ArrayList<>();
        }

        OrganizationVo organizationVo = optional.get();
        Customer customer = customerDAO.getCustomerByCustomerCode(organizationVo.getBindingEnterpriseNo(), params.getCustomerCode());
        if(customer == null) {
            return new ArrayList<>();
        }
        List<CustomerInvoice> customerInvoiceList = customerInvoiceDAO.getCustomerInvoiceByCustomerNo(customer.getEnterpriseNo(),
                customer.getCustomerNo());
        List<CustomerInvoiceVO> invoiceVoList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(customerInvoiceList)) {
            customerInvoiceList.forEach(t -> {
                CustomerInvoiceVO vo = new CustomerInvoiceVO();
                BeanUtils.copyProperties(t, vo);
                vo.setTypeName(InvoiceTypeEnum.getByType(t.getType()) == null ? null : InvoiceTypeEnum.getByType(t.getType()).getName());
                invoiceVoList.add(vo);
            });
        }

        return invoiceVoList;
    }


    /**
     * 根据组织编号 + 客户编码查询客户税务信息
     * @param orgNo
     * @param customerCode
     * @return
     */
    @Override
    public List<CustomerInvoiceResponse> getInvoiceListByCustomerCodeNoSession(String orgNo, String customerCode) {
        ValidatorUtils.checkTrueThrowEx(StringUtils.isEmpty(customerCode), "客户编码不存在");
        ValidatorUtils.checkTrueThrowEx(StringUtils.isEmpty(orgNo), "组织编号不存在");

        // 根据组织编号查询出对应的企业编号
        OrganizationExtandVo organizationTByOrgNo = uimTenantService.getOrganizationByOrgNo(orgNo);
        if(organizationTByOrgNo == null) {
            return new ArrayList<>();
        }

        // 得到组织对应的租户号，注意：集团租户对应的业务单元需要通过 bindingGroupEnterpriseNo 获取租户编号，子租户通过 BindingEnterpriseNo 获取
        String orgEnterpriseNo = StringUtils.isNotEmpty(organizationTByOrgNo.getBindingEnterpriseNo()) ? organizationTByOrgNo.getBindingEnterpriseNo() : organizationTByOrgNo.getBindingGroupEnterpriseNo();
        if(StringUtils.isEmpty(orgEnterpriseNo)) {
            return new ArrayList<>();
        }
        Customer customer = customerDAO.getCustomerByCustomerCode(orgEnterpriseNo, customerCode);
        if(customer == null) {
            return new ArrayList<>();
        }
        List<CustomerInvoice> customerInvoiceList = customerInvoiceDAO.getCustomerInvoiceByCustomerNo(orgEnterpriseNo,
                customer.getCustomerNo());
        List<CustomerInvoiceResponse> responseList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(customerInvoiceList)) {
            customerInvoiceList.forEach(t -> {
                CustomerInvoiceResponse response = new CustomerInvoiceResponse();
                BeanUtils.copyProperties(t, response);
                response.setTypeName(InvoiceTypeEnum.getByType(t.getType()) == null ? null : InvoiceTypeEnum.getByType(t.getType()).getName());
                responseList.add(response);
            });
        }
        return responseList;
    }
}
