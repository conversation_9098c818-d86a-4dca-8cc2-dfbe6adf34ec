package com.yyigou.dsrp.cdc.service.customer.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yyigou.ddc.common.util.ValidatorUtils;
import com.yyigou.ddc.services.mq.dto.JmsHeadersDto;
import com.yyigou.ddc.services.mq.manager.MessageQueueManager;
import com.yyigou.dsrp.cdc.client.customer.request.CoordinationCustomerRequest;
import com.yyigou.dsrp.cdc.client.customer.request.CustomerAreaRequest;
import com.yyigou.dsrp.cdc.client.customer.response.AreaResponse;
import com.yyigou.dsrp.cdc.client.customer.response.CoordinationCustomerResponse;
import com.yyigou.dsrp.cdc.common.enums.DeletedEnum;
import com.yyigou.dsrp.cdc.common.util.OperationModel;
import com.yyigou.dsrp.cdc.dao.customer.CustomerAreaDAO;
import com.yyigou.dsrp.cdc.dao.customer.CustomerDAO;
import com.yyigou.dsrp.cdc.dao.customer.entity.Customer;
import com.yyigou.dsrp.cdc.dao.customer.entity.CustomerArea;
import com.yyigou.dsrp.cdc.service.customer.CustomerExtendService;
import com.yyigou.dsrp.cdc.service.listener.model.CustomerAssignModel;
import com.yyigou.dsrp.gcs.client.executeRecord.response.ExecuteRecordResponse;
import com.yyigou.dsrp.gcs.common.enums.executeRecord.ExecuteRecordExcuteTypeEnum;
import com.yyigou.dsrp.gcs.model.api.constant.MqConstant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.activemq.ScheduledMessage;
import org.apache.activemq.command.ActiveMQTopic;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Service
@RequiredArgsConstructor
@Slf4j
public class CustomerExtendServiceImpl implements CustomerExtendService {
    private final CustomerDAO customerDAO;
    private final CustomerAreaDAO customerAreaDAO;
    private final MessageQueueManager messageQueueManager;

    @Override
    public void startCustomerAssignTask(ExecuteRecordResponse executeRecord){
        OperationModel operationModel = new OperationModel();
        operationModel.setEnterpriseNo(executeRecord.getApplyEnterpriseNo());
        operationModel.setGroupTenantNo(executeRecord.getGroupEnterpriseNo());
        operationModel.setEmployerNo(executeRecord.getCreateNo());
        operationModel.setUserName(executeRecord.getCreateName());
        CustomerAssignModel model = new CustomerAssignModel();
        model.setCustomerCode(executeRecord.getObjCode());
        model.setOperationModel(operationModel);
        model.setGroupEnterpriseNo(executeRecord.getGroupEnterpriseNo());
        model.setSubEnterpriseNo(executeRecord.getApplyEnterpriseNo());
        model.setExecuteRecordExecuteTypeEnum(ExecuteRecordExcuteTypeEnum.getByValue(executeRecord.getExecuteType()));
        model.setRecordId(executeRecord.getId());
        log.warn("客户{}开始分派", JSON.toJSONString(model));
        try {
            JmsHeadersDto jmsHeadersDto = new JmsHeadersDto();
            jmsHeadersDto.setSessionTransacted(true);
            Map<String, Object> customPropMap = new HashMap<>();
            customPropMap.put(ScheduledMessage.AMQ_SCHEDULED_DELAY, 5000L);
            messageQueueManager.produceMessage(new ActiveMQTopic(MqConstant.CDC_CUSTOMER_ASSIGN_TOPIC), JSON.toJSONString(model), customPropMap, jmsHeadersDto);
        } catch (Exception e) {
            log.error("分派客户失败", e);
        }
    }

    @Override
    public List<CoordinationCustomerResponse> queryCoordinationCustomer(CoordinationCustomerRequest params) {
        LambdaQueryWrapper<Customer> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(CollectionUtils.isNotEmpty(params.getEnterpriseNoList()), Customer::getEnterpriseNo, params.getEnterpriseNoList())
                .in(CollectionUtils.isNotEmpty(params.getCustomerNameList()), Customer::getCustomerName, params.getCustomerNameList())
                .like(StringUtils.isNotEmpty(params.getCustomerNameKeywords()), Customer::getCustomerName, params.getCustomerNameKeywords())
                .in(CollectionUtils.isNotEmpty(params.getCustomerNoList()), Customer::getCustomerNo, params.getCustomerNoList());
        final List<Customer> customerList = customerDAO.selectList(lambdaQueryWrapper);

        List<CoordinationCustomerResponse> result = new ArrayList<>();
        customerList.forEach(t -> {
            CoordinationCustomerResponse response = new CoordinationCustomerResponse();
            BeanUtils.copyProperties(t, response);
            result.add(response);
        });

        return result;
    }

    @Override
    public List<AreaResponse> queryCustomerArea(CustomerAreaRequest params) {
        ValidatorUtils.checkTrueThrowEx(StringUtils.isEmpty(params.getEnterpriseNo()), "租户不能为空");
        ValidatorUtils.checkTrueThrowEx(CollectionUtils.isEmpty(params.getCustomerNoList()), "客户不能为空");
        LambdaQueryWrapper<CustomerArea> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(CustomerArea::getEnterpriseNo, params.getEnterpriseNo())
                .in(CustomerArea::getCustomerNo, params.getCustomerNoList())
                .in(CollectionUtils.isNotEmpty(params.getAreaNoList()), CustomerArea::getAreaNo, params.getAreaNoList())
                .in(CollectionUtils.isNotEmpty(params.getAreaCodeList()), CustomerArea::getAreaCode, params.getAreaCodeList())
                .eq(params.getIsDefault() != null, CustomerArea::getIsDefault, params.getIsDefault())
                .eq(CustomerArea::getDeleted, DeletedEnum.UN_DELETE.getValue());
        final List<CustomerArea> customerAreas = customerAreaDAO.selectList(wrapper);
        List<AreaResponse> result = new ArrayList<>();
        customerAreas.forEach(t -> {
            AreaResponse response = new AreaResponse();
            BeanUtils.copyProperties(t, response);
            result.add(response);
        });
        return result;
    }
}


