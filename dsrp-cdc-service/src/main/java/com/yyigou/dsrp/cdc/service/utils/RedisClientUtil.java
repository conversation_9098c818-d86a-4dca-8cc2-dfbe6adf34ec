package com.yyigou.dsrp.cdc.service.utils;

import com.yyigou.ddc.common.cache.redisson3.RedisUtils;
import com.yyigou.ddc.common.exception.BusinessException;
import com.yyigou.dsrp.cdc.common.util.CommonUtil;
import com.yyigou.dsrp.gcs.model.api.constant.ErrorCode;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class RedisClientUtil {
    @Resource
    private RedisUtils redisUtils;


    /**
     * 获取值
     * @param key
     * @return
     */
    public String get(String key){
        RedissonClient redissonClient = redisUtils.getRedissonClient();
        RBucket<String> bucket = redissonClient.getBucket(key);
        return bucket.get();
    }

    /**
     * 设置值
     * @param key
     * @param value
     */
    public void set(String key,String value){
        RedissonClient redissonClient = redisUtils.getRedissonClient();
        RBucket<String> bucket = redissonClient.getBucket(key);
        bucket.set(value);
    }



    /**
     * 设置值同时设置超期时间
     * @param key
     * @param value
     * @param seconds
     */
    public void set(String key,String value,long seconds){
        RedissonClient redissonClient = redisUtils.getRedissonClient();
        RBucket<String> bucket = redissonClient.getBucket(key);
        bucket.set(value,seconds, TimeUnit.SECONDS);
    }

    /**
     * 失效
     * @param key
     * @param seconds
     */
    public void expir(String key,Long seconds){
        RedissonClient redissonClient = redisUtils.getRedissonClient();
        RBucket<String> bucket = redissonClient.getBucket(key);
        bucket.expire(seconds,TimeUnit.SECONDS);
    }


    /**
     * 设置值 如果存在则返回false
     * @param key
     * @param value
     * @param seconds
     * @return
     */
    public boolean trySet(String key,String value,long seconds){
        RedissonClient redissonClient = redisUtils.getRedissonClient();
        RBucket<String> bucket = redissonClient.getBucket(key);


        return bucket.trySet(value,seconds, TimeUnit.SECONDS);
    }

    public boolean  trySetAsync(String key,String value,long seconds){
        RedissonClient redissonClient = redisUtils.getRedissonClient();
        RBucket<String> bucket = redissonClient.getBucket(key);

        try {
            return bucket.trySetAsync(value,seconds, TimeUnit.SECONDS).get();
        } catch (Exception e) {
           throw new BusinessException(ErrorCode.BUSINESS_ERROR_CODE,"trySetAsync 异常:"+e.getMessage());
        }
    }


    @SneakyThrows
    public boolean lock(String lockName, long waitTime, long leaseTime) {
        RedissonClient redisson = redisUtils.getRedissonClient();
        RLock lock = redisson.getLock(lockName);
        if(lock.isLocked()){
            return false;
        }
        return lock.tryLock(waitTime, leaseTime, TimeUnit.MILLISECONDS);
    }


    @SneakyThrows
    public void unlock(String lockName) {
        try {
            CommonUtil.checkEmptyThrowEx(lockName,"lock is null");
            RedissonClient redisson = redisUtils.getRedissonClient();
            RLock lock = redisson.getLock(lockName);
            // 如果锁已释放, 则会抛出 Runtime 异常, 发生在leaseTime过短, 锁被自动释放
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }catch (Exception e){
            log.error("释放所失败",e);
        }

    }
}
