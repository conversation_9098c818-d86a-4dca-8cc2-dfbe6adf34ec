package com.yyigou.dsrp.cdc.service.company.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yyigou.ddc.common.util.StringUtil;
import com.yyigou.ddc.common.util.ValidatorUtils;
import com.yyigou.dsrp.cdc.client.company.request.CompanyForOpenFindRequest;
import com.yyigou.dsrp.cdc.client.company.response.CompanyInfoResponse;
import com.yyigou.dsrp.cdc.common.enums.CommonStatusEnum;
import com.yyigou.dsrp.cdc.common.enums.company.CompanyPartnershipEnum;
import com.yyigou.dsrp.cdc.dao.company.CompanyDAO;
import com.yyigou.dsrp.cdc.dao.company.entity.Company;
import com.yyigou.dsrp.cdc.dao.supplier.entity.Supplier;
import com.yyigou.dsrp.cdc.service.company.CompanyForOpenService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CompanyForOpenServiceImpl extends ServiceImpl<CompanyDAO, Company> implements CompanyForOpenService {
    @Resource
    private CompanyDAO companyDao;

    @Override
    public List<CompanyInfoResponse> findList(CompanyForOpenFindRequest params) {
        ValidatorUtils.checkEmptyThrowEx(params.getCompanyNoList(), "企业编号不能为空");
        LambdaQueryWrapper<Company> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(Company::getCompanyNo, params.getCompanyNoList())
                .eq(Company::getStatus, CommonStatusEnum.EFFECTIVE.getValue());
        if (StringUtils.isNotEmpty(params.getLastSyncTime())) {
            lambdaQueryWrapper = lambdaQueryWrapper.gt(Company::getOpTimestamp, params.getLastSyncTime());
        }
        List<Company> customerList = companyDao.selectList(lambdaQueryWrapper);
        return convert(customerList);
    }

    private List<CompanyInfoResponse> convert(List<Company> companyList) {
        List<CompanyInfoResponse> result = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(companyList)) {
            companyList.forEach(t -> {
                CompanyInfoResponse companyInfoResponse = new CompanyInfoResponse();
                BeanUtils.copyProperties(t, companyInfoResponse);
                result.add(companyInfoResponse);
                if (!StringUtil.isEmpty(t.getPartnership())) {
                    try {
                        List<CompanyPartnershipEnum> partnershipEnumList = new ArrayList<>();
                        List<String> partnershipList = JSON.parseArray(t.getPartnership(), String.class);
                        for (String s : partnershipList) {
                            partnershipEnumList.add(CompanyPartnershipEnum.getByType(s));
                        }
                        companyInfoResponse.setPartnershipText(String.join(",", partnershipEnumList.stream().map(CompanyPartnershipEnum::getName).collect(Collectors.toList())));
                    } catch (Exception e) {
                        log.error("合作关系解析类型失败" + JSON.toJSONString(e));
                    }

                }
            });
        }
        return result;
    }
}
