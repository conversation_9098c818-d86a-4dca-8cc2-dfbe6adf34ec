package com.yyigou.dsrp.cdc.service.company;

import com.yyigou.ddc.common.service.SessionUser;
import com.yyigou.ddc.services.ddc.task.export.base.AbstractUapExcelCheckAndImport;
import com.yyigou.ddc.services.ddc.task.state.ExcelRowCheckResultEnum;
import com.yyigou.dsrp.cdc.api.company.dto.CompanyImportDTO;
import com.yyigou.dsrp.cdc.common.enums.CommonIfEnum;
import com.yyigou.dsrp.cdc.dao.company.entity.Company;
import com.yyigou.dsrp.cdc.service.common.CdcTenantService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CompanyImportService extends AbstractUapExcelCheckAndImport<CompanyImportDTO> {
    @Autowired
    private CompanyService companyService;

    @Override
    public List<CompanyImportDTO> checkBatchImportData(List<CompanyImportDTO> importDataList) {
        return checkCompanyImportData(importDataList);
    }

    private List<CompanyImportDTO> checkCompanyImportData(List<CompanyImportDTO> importDataList) {
        SessionUser sessionUser = CdcTenantService.getSessionUser();
        String enterpriseNo = sessionUser.getEnterpriseNo();
        // 获取统一社会信用代码，查询本地企业档案
        List<String> unifiedSocialCodeList = importDataList.stream().map(CompanyImportDTO::getUnifiedSocialCode).collect(Collectors.toList());
        List<Company> localUnifiedSocialCompanyList = companyService.findByUnifiedSocialCodeList(enterpriseNo, unifiedSocialCodeList);
        Map<String, Company> localUnifiedSocialCompanyMap = localUnifiedSocialCompanyList.stream().collect(Collectors.toMap(Company::getUnifiedSocialCode, Function.identity(), (v1, v2) -> v1));
        // 获取企业名称，查询本地企业档案
        List<String> companyNameList = importDataList.stream().map(CompanyImportDTO::getCompanyName).collect(Collectors.toList());
        List<Company> localCompanyByNameList = companyService.findByCompanyNameList(enterpriseNo, companyNameList);
        Map<String, Company> localCompanyByNameMap = localCompanyByNameList.stream().collect(Collectors.toMap(Company::getCompanyName, Function.identity(), (v1, v2) -> v1));
        importDataList.forEach(importData -> {
            Company localCompanyByCode = localUnifiedSocialCompanyMap.get(importData.getUnifiedSocialCode());
            if (localCompanyByCode != null) {
                importData.setExcelRowCheckResultEnumCode(ExcelRowCheckResultEnum.FAIL.getCheckCode());
                importData.setExcelRowCheckResultEnumMessage("统一社会信用代码【" + importData.getUnifiedSocialCode() + "】已存在对应企业");
                return;
            }
            Company localCompanyByName = localCompanyByNameMap.get(importData.getCompanyName());
            if (localCompanyByName != null) {
                importData.setExcelRowCheckResultEnumCode(ExcelRowCheckResultEnum.FAIL.getCheckCode());
                importData.setExcelRowCheckResultEnumMessage("企业名称【" + importData.getCompanyName() + "】已存在");
                return;
            }
            // 是否医疗机构
//            setDefaultInstitutionFields(importData);
            // 关联企业
            if (CommonIfEnum.NO.getValue().equals(importData.getIsAssociatedEnterprise())) {
                importData.setAssociatedOrgNo(null);
                importData.setAssociatedOrgNoName(null);
                importData.setAssociatedOrgCode(null);
                importData.setAssociatedOrgName(null);
            } else {
                // 关联组织名称
                importData.setAssociatedOrgName(StringUtils.isNotBlank(importData.getAssociatedOrgNoName()) ? importData.getAssociatedOrgNoName() : null);
            }

            // 营业期限止和长期同时有值，取营业期限止
            if (StringUtils.isNotBlank(importData.getBusinessEndTime()) && Objects.nonNull(importData.getBusinessLongTerm()) && CommonIfEnum.YES.getValue().equals(importData.getBusinessLongTerm())) {
                importData.setBusinessLongTerm(CommonIfEnum.NO.getValue());
                importData.setBusinessLongTermName(CommonIfEnum.NO.getName());
            }

        });
        return importDataList;
    }

    /**
     * 是否医疗机构默认值处理
     *
     * @param importData
     * @return:
     */
    private void setDefaultInstitutionFields(CompanyImportDTO importData) {
        // 非医疗机构直接设置默认值
        if (CommonIfEnum.NO.getValue().equals(importData.getIsMedicalInstitution())) {
            importData.setInstitutionalType(null);
            importData.setHospitalType(null);
            importData.setHospitalClass(null);
            importData.setInstitutionalTypeName(null);
            importData.setHospitalTypeName(null);
            importData.setHospitalClassName(null);
            return;
        }

        // 是医疗机构但机构类型为空时设置默认值
        if (StringUtils.isBlank(importData.getInstitutionalType())) {
            importData.setInstitutionalType("yy");
            importData.setInstitutionalTypeName("医院");
        }

        // 设置医院类型和等级的默认值
        importData.setHospitalTypeName(StringUtils.isBlank(importData.getHospitalType()) ? "公立" : importData.getHospitalTypeName());
        importData.setHospitalClassName(importData.getHospitalClass() != null ? importData.getHospitalClassName() : "无等级");
        importData.setHospitalType(StringUtils.isBlank(importData.getHospitalType()) ? "yy" : importData.getHospitalType());
        importData.setHospitalClass(importData.getHospitalClass() != null ? importData.getHospitalClass() : 0);

        // 如果机构类型不是 "yy"，则重置医院类型和等级
        if (!"yy".equals(importData.getInstitutionalType())) {
            importData.setHospitalType(null);
            importData.setHospitalClass(null);
            importData.setHospitalTypeName(null);
            importData.setHospitalClassName(null);
        }
    }

    @Override
    public List<CompanyImportDTO> handleImportSliceData(List<CompanyImportDTO> importDataList) {
        SessionUser sessionUser = CdcTenantService.getSessionUser();
        // 校验导入参数
        List<CompanyImportDTO> companyImportDTOList = checkCompanyImportData(importDataList);
        companyService.saveImportCompanyBatch(sessionUser, companyImportDTOList);
        return companyImportDTOList;
    }
}
