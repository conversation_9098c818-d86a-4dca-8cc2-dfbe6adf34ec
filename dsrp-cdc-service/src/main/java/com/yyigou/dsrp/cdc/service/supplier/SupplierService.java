package com.yyigou.dsrp.cdc.service.supplier;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yyigou.ddc.common.page.PageDto;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.dsrp.cdc.api.common.vo.OrganizationVO;
import com.yyigou.dsrp.cdc.api.supply.dto.*;
import com.yyigou.dsrp.cdc.api.supply.vo.SupplierOperationPlatformPageVO;
import com.yyigou.dsrp.cdc.api.supply.vo.SupplierPageVO;
import com.yyigou.dsrp.cdc.api.supply.vo.SupplierSyncCountVO;
import com.yyigou.dsrp.cdc.api.supply.vo.SupplierSyncQueryVO;
import com.yyigou.dsrp.cdc.api.supply.vo.SupplierVO;
import com.yyigou.dsrp.cdc.client.common.response.QueryUseInfoResponse;
import com.yyigou.dsrp.cdc.client.supplier.request.SupplierComponentRequest;
import com.yyigou.dsrp.cdc.client.supplier.request.SupplierFindRequest;
import com.yyigou.dsrp.cdc.client.supplier.request.SupplierNameRequest;
import com.yyigou.dsrp.cdc.client.supplier.request.SupplierNoRequest;
import com.yyigou.dsrp.cdc.client.supplier.response.SupplierComponentResponse;
import com.yyigou.dsrp.cdc.client.supplier.response.SupplierInfoResponse;
import com.yyigou.dsrp.cdc.client.supplier.response.SupplierResponse;
import com.yyigou.dsrp.cdc.client.supplier.response.SupplierSimpleComponentResponse;
import com.yyigou.dsrp.cdc.common.util.OperationModel;
import com.yyigou.dsrp.cdc.dao.supplier.entity.Supplier;

import java.util.List;

public interface SupplierService extends IService<Supplier> {
    List<SupplierInfoResponse> findSupplierByName(SupplierNameRequest request);

    List<SupplierInfoResponse> findSupplierByNo(SupplierNoRequest request);

    /**
     * 按条件查询供应商信息
     *
     * @param enterpriseNo
     * @param request
     * @return
     */
    List<SupplierResponse> findSupplier(String enterpriseNo, SupplierFindRequest request);

    List<String> findSupplierUserList(String enterpriseNo, String supplierCode);

    /**
     * 根据企业编号查询供应商档案
     *
     * @param enterpriseNo
     * @param companyNo
     * @return: {@link List<  Supplier >}
     */
    List<Supplier> findSupplierByCompanyNo(String enterpriseNo, String companyNo);

    int updateSupplier(Supplier supplier);


    List<SupplierSimpleComponentResponse> querySupplierList(List<String> enterpriseNoList, String supplierCode);

    /**
     * 根据供应商编码查询供应商信息
     *
     * @param enterpriseNo
     * @param supplierCode
     * @return: {@link Supplier}
     */
    Supplier getSupplierBySupplierCode(String enterpriseNo, String supplierCode);

    /**
     * 根据供应商编码查询供应商档案分配组织租户编号
     *
     * @param groupEnterpriseNo
     * @param supplierCode
     * @return: {@link List< String>}
     */
    List<String> findSupplierAssignOrgEnterpriseNo(String groupEnterpriseNo, String supplierCode);


    String saveSupplier(OperationModel operationModel, SaveSupplierDTO params, String logRecord);

    Boolean updateSupplier(OperationModel operationModel, SaveSupplierDTO params);

    SupplierVO getSupplier(String enterpriseNo, String supplierNo);

    SupplierVO getSupplierByOrg(OperationModel operationModel, String supplierNo);


    Boolean checkOnlyCode(OperationModel operationModel, String no, String code);

    Boolean checkOnlyName(OperationModel operationModel, String no, String name);


    Boolean deleteSupplier(OperationModel operationModel, List<String> supplierNoList);

    PageVo<SupplierSyncQueryVO> findListPageForSync(OperationModel operationModel, SupplierSyncQueryDTO params, PageDto pageDto);

    SupplierSyncCountVO findSupplierSyncCount(OperationModel operationModel);

    PageVo<SupplierSyncQueryVO> findListPageForPendingAdmission(OperationModel operationModel, SupplierSyncQueryDTO params, PageDto pageDto);

    PageVo<SupplierPageVO> queryPageSupplier(OperationModel operationModel, QueryPageSupplierDTO params, PageDto pageDto);

    Long findExamCount(OperationModel operationModel);

    PageVo<SupplierPageVO> queryPageSupplierByOrg(OperationModel operationModel, QueryPageSupplierByOrgDTO params, PageDto pageDto);

    PageVo<SupplierPageVO> queryPageSupplierBySpecifyOrg(OperationModel operationModel, QueryPageSupplierBySpecifyOrgDTO params, PageDto pageDto);


    PageVo<SupplierOperationPlatformPageVO> queryOperationPlatformPageSupplier(OperationModel operationModel, QueryPageOperationPlatformSupplierDTO params, PageDto pageDto);


    PageVo<SupplierPageVO> queryPageSupplierByGroup(OperationModel operationModel, QueryPageSupplierDTO params, PageDto pageDto);

    PageVo<SupplierComponentResponse> selectSupplierPageForCommonComponent(SupplierComponentRequest params, PageDto pageDto);

    List<SupplierComponentResponse> selectSupplierListForCommonComponent(SupplierComponentRequest params);

    SupplierInfoResponse selectSupplierInfo(String enterpriseNo, String supplierNo);


    List<QueryUseInfoResponse> queryUseInfo(String enterpriseNo, String supplierCode);

    List<OrganizationVO> findOrgInfoBySupplier(OperationModel operationModel, String groupEnterpriseNo, OrgInfoBySupplierDTO params);
}
