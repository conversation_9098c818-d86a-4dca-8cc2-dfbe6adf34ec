package com.yyigou.dsrp.cdc.service.v2.customer;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yyigou.ddc.common.page.PageDto;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.dsrp.cdc.api.v2.customer.vo.CustomerApplyDetailVO;
import com.yyigou.dsrp.cdc.api.v2.customer.vo.CustomerApplyPageVO;
import com.yyigou.dsrp.cdc.common.util.OperationModel;
import com.yyigou.dsrp.cdc.dao.v2.customer.entity.CustomerApply;
import com.yyigou.dsrp.cdc.model.v2.customer.req.*;


public interface CustomerApplyService extends IService<CustomerApply> {
    PageVo<CustomerApplyPageVO> applyFindListPage(OperationModel operationModel, CustomerApplyQueryListPageReq queryReq, PageDto pageDTO);

    PageVo<CustomerApplyPageVO> approveFindListPage(OperationModel operationModel, CustomerApplyApproveQueryListPageReq queryReq, PageDto pageDTO);

    Long getPendingCount(OperationModel operationModel);

    String applySaveOrUpdate(OperationModel operationModel, CustomerApplySaveOrUpdateReq req);

    CustomerApplyDetailVO getDetail(OperationModel operationModel, CustomerApplyGetReq req);

    Boolean deleteApply(OperationModel operationModel, CustomerApplyDeleteReq req);

    Boolean withDrawApply(OperationModel operationModel, CustomerApplyWithdrawReq req);

    Boolean manageApprove(OperationModel operationModel, CustomerApplyApproveReq req);
}
