package com.yyigou.dsrp.cdc.service.v2.supplier.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yyigou.ddc.common.exception.BusinessException;
import com.yyigou.ddc.common.util.ValidatorUtils;
import com.yyigou.ddc.services.ddc.task.export.base.AbstractUapExcelCheckAndImport;
import com.yyigou.ddc.services.ddc.task.state.ExcelRowCheckResultEnum;
import com.yyigou.ddc.services.ddc.uim.vo.EmployeeVo;
import com.yyigou.ddc.services.ddc.uim.vo.OrganizationVo;
import com.yyigou.ddc.services.dsrp.bdc.enums.FactoryTypeEnum;
import com.yyigou.ddc.services.dsrp.bdc.vo.PaymentAgreementVo;
import com.yyigou.dsrp.cdc.api.v2.supplier.dto.*;
import com.yyigou.dsrp.cdc.common.enums.CommonIfEnum;
import com.yyigou.dsrp.cdc.common.enums.CommonStatusEnum;
import com.yyigou.dsrp.cdc.common.enums.DeletedEnum;
import com.yyigou.dsrp.cdc.common.util.OperationModel;
import com.yyigou.dsrp.cdc.dao.v2.supplier.SupplierCategoryV2DAO;
import com.yyigou.dsrp.cdc.dao.v2.supplier.SupplierV2DAO;
import com.yyigou.dsrp.cdc.dao.v2.supplier.entity.SupplierCategoryV2;
import com.yyigou.dsrp.cdc.dao.v2.supplier.entity.SupplierV2;
import com.yyigou.dsrp.cdc.manager.integration.paymentAgreement.PaymentAgreementService;
import com.yyigou.dsrp.cdc.manager.integration.uim.EmployeeService;
import com.yyigou.dsrp.cdc.manager.integration.uim.OrganizationService;
import com.yyigou.dsrp.cdc.model.v2.company.req.CompanyBankReq;
import com.yyigou.dsrp.cdc.model.v2.company.req.CompanyLinkmanReq;
import com.yyigou.dsrp.cdc.model.v2.company.req.CompanyShippingAddressReq;
import com.yyigou.dsrp.cdc.model.v2.supplier.req.*;
import com.yyigou.dsrp.cdc.service.utils.UserHandleUtils;
import com.yyigou.dsrp.cdc.service.v2.supplier.SupplierV2Service;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service("supplierV2ImportService")
@RequiredArgsConstructor
@Slf4j
public class SupplierV2ImportServiceImpl extends AbstractUapExcelCheckAndImport<SupplierImportDTO> {

    @Resource
    private SupplierV2DAO supplierV2DAO;

    @Resource
    private SupplierCategoryV2DAO supplierCategoryV2DAO;

    @Resource
    private SupplierV2Service supplierV2Service;

    @Resource
    private TransactionTemplate transactionTemplate;


    @Resource
    private OrganizationService organizationService;


    @Resource
    private EmployeeService employeeService;


    @Resource
    private PaymentAgreementService paymentAgreementService;

    @Override
    public List<SupplierImportDTO> checkBatchImportData(List<SupplierImportDTO> pojoList) {
        log.warn("checkBatchImportDataReq={}", pojoList);

        OperationModel operationModel = UserHandleUtils.getOperationModel();

        validateImportSupplier(operationModel, pojoList);

        log.warn("checkBatchImportDataResp={}", pojoList);

        return pojoList;
    }

    @Override
    public List<SupplierImportDTO> handleImportSliceData(List<SupplierImportDTO> pojoList) {
        log.warn("handleImportSliceDataReq={}", pojoList);

        OperationModel operationModel = UserHandleUtils.getOperationModel();

        adjustValue(operationModel, pojoList);

        for (SupplierImportDTO supplierImportDTO : pojoList) {
            transactionTemplate.execute(status -> {
                // 新增供应商
                SupplierSaveBasicAndBizReq supplierSaveBasicAndBizReq = packSupplierSaveBasicAndBizReq(operationModel, supplierImportDTO);
                supplierV2Service.manageSaveSupplierBasicAndBiz(operationModel, supplierSaveBasicAndBizReq);

                // 新增联系人信息
                SupplierLinkmanListEditReq supplierLinkmanListEditReq = packSupplierSaveLinkmanListReq(operationModel, supplierImportDTO);
                supplierV2Service.editSupplierLinkmanList(operationModel, supplierLinkmanListEditReq);

                // 新增地址信息
                SupplierAddressListEditReq supplierAddressListEditReq = packSupplierSaveAddressListReq(operationModel, supplierImportDTO);
                supplierV2Service.editSupplierAddressList(operationModel, supplierAddressListEditReq);

                // 新增银行信息
                SupplierBankListEditReq supplierBankListEditReq = packSupplierSaveBankListReq(operationModel, supplierImportDTO);
                supplierV2Service.editSupplierBankList(operationModel, supplierBankListEditReq);

                // 新增负责人信息
                SupplierOrdermanListEditReq supplierOrdermanListEditReq = packSupplierSaveOrdermanListReq(operationModel, supplierImportDTO);
                supplierV2Service.editSupplierOrdermanList(operationModel, supplierOrdermanListEditReq);


                return null;
            });
        }

        log.warn("handleImportSliceDataResp={}", pojoList);

        return pojoList;
    }

    private SupplierAddressListEditReq packSupplierSaveAddressListReq(OperationModel operationModel, SupplierImportDTO supplierImportDTO) {
        SupplierAddressListEditReq req = new SupplierAddressListEditReq();

        // 设置基本信息
        req.setEnterpriseNo(operationModel.getEnterpriseNo());
        req.setUseOrgNo(supplierImportDTO.getManageOrgNo()); // 使用组织编号使用管理组织编号
        req.setSupplierCode(supplierImportDTO.getSupplierCode());

        // 处理地址列表
        if (CollectionUtils.isNotEmpty(supplierImportDTO.getBdcCompanyShippingAddressDetailList())) {
            List<CompanyShippingAddressReq> addressList = new ArrayList<>(supplierImportDTO.getBdcCompanyShippingAddressDetailList().size());

            for (SupplierAddressImportDTO addressImportDTO : supplierImportDTO.getBdcCompanyShippingAddressDetailList()) {
                CompanyShippingAddressReq addressReq = new CompanyShippingAddressReq();

                // 设置地址信息
                addressReq.setReceiveUser(addressImportDTO.getReceiveUser());
                addressReq.setReceivePhone(addressImportDTO.getReceivePhone());
                addressReq.setAddressType(addressImportDTO.getAddressType());
                addressReq.setRegionCode(addressImportDTO.getRegionCode());
                addressReq.setRegionName(addressImportDTO.getRegionCodeName());
                addressReq.setReceiveAddr(addressImportDTO.getReceiveAddr());
                addressReq.setAddressDesc(addressImportDTO.getAddressDesc());
                addressReq.setIsDefault(addressImportDTO.getIsDefault());

                // 添加到列表
                addressList.add(addressReq);
            }

            req.setLinkAddressList(addressList);
        }

        return req;
    }

    private SupplierBankListEditReq packSupplierSaveBankListReq(OperationModel operationModel, SupplierImportDTO supplierImportDTO) {
        SupplierBankListEditReq req = new SupplierBankListEditReq();

        // 设置基本信息
        req.setEnterpriseNo(operationModel.getEnterpriseNo());
        req.setManageOrgNo(supplierImportDTO.getManageOrgNo()); // 银行信息使用管理组织编号
        req.setSupplierCode(supplierImportDTO.getSupplierCode());

        // 处理银行列表
        if (CollectionUtils.isNotEmpty(supplierImportDTO.getBdcSupplierBankDetailList())) {
            List<CompanyBankReq> bankList = new ArrayList<>(supplierImportDTO.getBdcSupplierBankDetailList().size());

            for (SupplierBankImportDTO bankImportDTO : supplierImportDTO.getBdcSupplierBankDetailList()) {
                CompanyBankReq bankReq = new CompanyBankReq();

                // 设置银行信息
                bankReq.setOpenBank(bankImportDTO.getOpenBank());
                bankReq.setAccountNo(bankImportDTO.getAccountNo());
                bankReq.setAccountName(bankImportDTO.getAccountName());
                bankReq.setBankType(bankImportDTO.getBankType());
                bankReq.setCurrencyId(bankImportDTO.getCurrencyId());
                bankReq.setAccountType(bankImportDTO.getAccountType());
                bankReq.setLinkPerson(bankImportDTO.getLinkPerson());
                bankReq.setLinkPhone(bankImportDTO.getLinkPhone());
                bankReq.setIsDefault(bankImportDTO.getIsDefault());
                bankReq.setStatus(bankImportDTO.getStatus());

                // 添加到列表
                bankList.add(bankReq);
            }

            req.setBankList(bankList);
        }

        return req;
    }

    private SupplierOrdermanListEditReq packSupplierSaveOrdermanListReq(OperationModel operationModel, SupplierImportDTO supplierImportDTO) {
        SupplierOrdermanListEditReq req = new SupplierOrdermanListEditReq();

        // 设置基本信息
        req.setEnterpriseNo(operationModel.getEnterpriseNo());
        req.setUseOrgNo(supplierImportDTO.getManageOrgNo()); // 使用组织编号使用管理组织编号
        req.setSupplierCode(supplierImportDTO.getSupplierCode());

        // 处理负责人列表
        if (CollectionUtils.isNotEmpty(supplierImportDTO.getBdcSupplierOrderManDetailList())) {
            List<SupplierOrderManReq> orderManList = new ArrayList<>(supplierImportDTO.getBdcSupplierOrderManDetailList().size());

            for (SupplierOrderManImportDTO orderManImportDTO : supplierImportDTO.getBdcSupplierOrderManDetailList()) {
                SupplierOrderManReq orderManReq = new SupplierOrderManReq();

                // 设置负责人信息
                orderManReq.setDeptNo(orderManImportDTO.getDeptNo());
                orderManReq.setOrderManName(orderManImportDTO.getOrderManName());
                orderManReq.setPost(orderManImportDTO.getPost());
                orderManReq.setOrderSpecialist(orderManImportDTO.getOrderSpecialist());
                orderManReq.setIsDefault(orderManImportDTO.getIsDefault());

                // 添加到列表
                orderManList.add(orderManReq);
            }

            req.setSupplierManList(orderManList);
        }

        return req;
    }

    private SupplierLinkmanListEditReq packSupplierSaveLinkmanListReq(OperationModel operationModel, SupplierImportDTO supplierImportDTO) {
        SupplierLinkmanListEditReq req = new SupplierLinkmanListEditReq();

        // 设置基本信息
        req.setEnterpriseNo(operationModel.getEnterpriseNo());
        req.setUseOrgNo(supplierImportDTO.getManageOrgNo()); // 使用组织编号使用管理组织编号
        req.setSupplierCode(supplierImportDTO.getSupplierCode());

        // 处理联系人列表
        if (CollectionUtils.isNotEmpty(supplierImportDTO.getBdcCompanyLinkmanDetailList())) {
            List<CompanyLinkmanReq> linkmanList = new ArrayList<>(supplierImportDTO.getBdcCompanyLinkmanDetailList().size());

            for (SupplierLinkmanImportDTO linkmanImportDTO : supplierImportDTO.getBdcCompanyLinkmanDetailList()) {
                CompanyLinkmanReq linkmanReq = new CompanyLinkmanReq();

                // 设置联系人信息
                linkmanReq.setLinkman(linkmanImportDTO.getLinkman());
                linkmanReq.setSex(linkmanImportDTO.getSex());
                linkmanReq.setPosition(linkmanImportDTO.getPosition());
                linkmanReq.setFixedPhone(linkmanImportDTO.getFixedPhone());
                linkmanReq.setMobilePhone(linkmanImportDTO.getMobilePhone());
                linkmanReq.setQq(linkmanImportDTO.getQq());
                linkmanReq.setWx(linkmanImportDTO.getWx());
                linkmanReq.setEmail(linkmanImportDTO.getEmail());
                linkmanReq.setIsDefault(linkmanImportDTO.getIsDefault());
                linkmanReq.setStatus(linkmanImportDTO.getStatus());

                // 添加到列表
                linkmanList.add(linkmanReq);
            }

            req.setLinkmanList(linkmanList);
        }

        return req;
    }

    private SupplierSaveBasicAndBizReq packSupplierSaveBasicAndBizReq(OperationModel operationModel, SupplierImportDTO supplierImportDTO) {
        SupplierSaveBasicAndBizReq req = new SupplierSaveBasicAndBizReq();

        req.setEnterpriseNo(operationModel.getEnterpriseNo());

        // 设置基本信息
        req.setManageOrgNo(supplierImportDTO.getManageOrgNo());
        req.setSupplierCode(supplierImportDTO.getSupplierCode());
        req.setSupplierName(supplierImportDTO.getSupplierName());
        req.setSupplierNameEn(supplierImportDTO.getSupplierNameEn());
        req.setMnemonicCode(supplierImportDTO.getMnemonicCode());
        req.setTransactionType(supplierImportDTO.getTransactionType());
        req.setSupplierCategoryNo(supplierImportDTO.getSupplierCategoryNo());
        req.setCompanyName(supplierImportDTO.getCompanyName());
        req.setUnifiedSocialCode(supplierImportDTO.getUnifiedSocialCode());
        req.setFactoryType(supplierImportDTO.getFactoryType());
        req.setCountryRegionId(supplierImportDTO.getCountryRegionId());
        req.setTaxCategory(supplierImportDTO.getTaxCategory());
        req.setEconomicType(supplierImportDTO.getEconomicType());
        req.setRetailInvestors(supplierImportDTO.getRetailInvestors());
        req.setIsAssociatedEnterprise(supplierImportDTO.getIsAssociatedEnterprise());
        req.setAssociatedOrgNo(supplierImportDTO.getAssociatedOrgNo());
        req.setAssociatedOrgCode(supplierImportDTO.getAssociatedOrgCode());
        req.setAssociatedOrgName(supplierImportDTO.getAssociatedOrgNoName());
        req.setRemark(supplierImportDTO.getRemark());

        // 设置业务信息
        req.setCooperationMode(supplierImportDTO.getCooperationMode());
        req.setCooperationModeName(supplierImportDTO.getCooperationModeName());
        req.setCurrencyId(supplierImportDTO.getCurrencyId());
        req.setSettlementModes(supplierImportDTO.getSettlementModes());
        req.setSettlementModesName(supplierImportDTO.getSettlementModesName());
        req.setPaymentAgreementId(supplierImportDTO.getPaymentAgreementId());
        req.setPaymentAgreementCode(supplierImportDTO.getPaymentAgreementCode());
        req.setPaymentAgreementName(supplierImportDTO.getPaymentAgreementIdName());
        req.setPaymentTerm(supplierImportDTO.getPaymentTerm());
        req.setCreditAmount(supplierImportDTO.getCreditAmount());
        req.setPeriodDays(supplierImportDTO.getPeriodDays());
        req.setCoopStartTime(supplierImportDTO.getCoopStartTime());
        req.setCoopEndTime(supplierImportDTO.getCoopEndTime());
        req.setOwnerCompany(supplierImportDTO.getOwnerCompany());

        return req;
    }

    private void adjustValue(OperationModel operationModel, List<SupplierImportDTO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        Map<String, List<SupplierCategoryV2>> categoryNameMap = new HashMap<>();
        List<String> supplierCategoryNameList = list.stream().map(SupplierImportDTO::getSupplierCategoryName).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(supplierCategoryNameList)) {
            List<SupplierCategoryV2> supplierCategoryV2s = supplierCategoryV2DAO.selectList(Wrappers.<SupplierCategoryV2>lambdaQuery()
                    .eq(SupplierCategoryV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                    .in(SupplierCategoryV2::getCategoryName, supplierCategoryNameList)
                    .eq(SupplierCategoryV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
            categoryNameMap = supplierCategoryV2s.stream().collect(Collectors.groupingBy(SupplierCategoryV2::getCategoryName));
        }

        Map<String, OrganizationVo> organizationVoMap = new HashMap<>();
        List<String> associatedOrgNoList = list.stream().map(SupplierImportDTO::getAssociatedOrgNo).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(associatedOrgNoList)) {
            List<OrganizationVo> organizationVos = organizationService.findListNoAuth(operationModel.getEnterpriseNo(), associatedOrgNoList);
            organizationVoMap = organizationVos.stream().collect(Collectors.toMap(OrganizationVo::getOrgNo, Function.identity()));
        }

        Map<String, List<EmployeeVo>> employeeNameMap = new HashMap<>();
        List<String> orderManNameList = list.stream().filter(supp -> null != supp.getBdcSupplierOrderManDetailList()).flatMap(supp -> supp.getBdcSupplierOrderManDetailList().stream()).map(SupplierOrderManImportDTO::getOrderManName).collect(Collectors.toList());
        List<EmployeeVo> employeeByNames = employeeService.getEmployeeByNames(operationModel.getEnterpriseNo(), orderManNameList);
        if (CollectionUtils.isNotEmpty(employeeByNames)) {
            employeeNameMap = employeeByNames.stream().collect(Collectors.groupingBy(EmployeeVo::getUserName));
        }

        Map<Long, PaymentAgreementVo> paymentAgreementMap = new HashMap<>();
        List<Long> paymentAgreementIdList = list.stream().map(SupplierImportDTO::getPaymentAgreementId).filter(Objects::nonNull).collect(Collectors.toList());
        List<PaymentAgreementVo> paymentAgreementVoList = paymentAgreementService.findByIdList(operationModel.getEnterpriseNo(), paymentAgreementIdList);
        if (CollectionUtils.isNotEmpty(paymentAgreementVoList)) {
            paymentAgreementMap = paymentAgreementVoList.stream().collect(Collectors.toMap(PaymentAgreementVo::getId, Function.identity()));
        }


        for (SupplierImportDTO supplierImportDTO : list) {
            // 分类
            if (!categoryNameMap.containsKey(supplierImportDTO.getSupplierCategoryName())) {
                throw new BusinessException(supplierImportDTO.getSupplierCategoryName() + "不存在");
            }
            List<SupplierCategoryV2> maybeMultiCategoryList = categoryNameMap.get(supplierImportDTO.getSupplierCategoryName());
            if (CollectionUtils.isNotEmpty(maybeMultiCategoryList) && maybeMultiCategoryList.size() > 1) {
                throw new BusinessException(supplierImportDTO.getSupplierCategoryName() + "对应多个分类");
            }
            supplierImportDTO.setSupplierCategoryNo(maybeMultiCategoryList.get(0).getNo());

            // 企业名称
            if (StringUtils.isEmpty(supplierImportDTO.getCompanyName())) {
                supplierImportDTO.setCompanyName(supplierImportDTO.getSupplierName());
            }

            // 企业注册地域
            if (null == supplierImportDTO.getFactoryType()) {
                supplierImportDTO.setFactoryType(FactoryTypeEnum.domestic.getValue());
            }

            // 散户
            if (null == supplierImportDTO.getRetailInvestors()) {
                supplierImportDTO.setRetailInvestors(CommonIfEnum.NO.getValue());
            }

            // 内部组织
            if (null == supplierImportDTO.getIsAssociatedEnterprise()) {
                supplierImportDTO.setIsAssociatedEnterprise(CommonIfEnum.NO.getValue());
            }

            if (CommonIfEnum.NO.getValue().equals(supplierImportDTO.getIsAssociatedEnterprise())) {
                supplierImportDTO.setAssociatedOrgNo(null);
            }

            // setAssociatedOrgCode
            if (CommonIfEnum.YES.getValue().equals(supplierImportDTO.getIsAssociatedEnterprise())
                    && StringUtils.isNotEmpty(supplierImportDTO.getAssociatedOrgNo())) {
                OrganizationVo organizationVo = organizationVoMap.get(supplierImportDTO.getAssociatedOrgNo());
                if (null != organizationVo) {
                    supplierImportDTO.setAssociatedOrgCode(organizationVo.getOrgCode());
                }
            }

            if (null == supplierImportDTO.getPaymentAgreementId()) {
                PaymentAgreementVo paymentAgreementVo = paymentAgreementMap.get(supplierImportDTO.getPaymentAgreementId());
                if (null != paymentAgreementVo) {
                    supplierImportDTO.setPaymentAgreementCode(paymentAgreementVo.getCode());
                }
            }

            // 处理联系人
            if (CollectionUtils.isNotEmpty(supplierImportDTO.getBdcCompanyLinkmanDetailList())) {
                for (SupplierLinkmanImportDTO supplierLinkmanImportDTO : supplierImportDTO.getBdcCompanyLinkmanDetailList()) {
                    // 设置默认值
                    if (null == supplierLinkmanImportDTO.getIsDefault()) {
                        supplierLinkmanImportDTO.setIsDefault(CommonIfEnum.NO.getValue());
                    }

                    // 设置状态
                    if (null == supplierLinkmanImportDTO.getStatus()) {
                        supplierLinkmanImportDTO.setStatus(CommonStatusEnum.EFFECTIVE.getValue());
                    }
                }
            }

            // 处理地址
            if (CollectionUtils.isNotEmpty(supplierImportDTO.getBdcCompanyShippingAddressDetailList())) {
                for (SupplierAddressImportDTO supplierAddressImportDTO : supplierImportDTO.getBdcCompanyShippingAddressDetailList()) {
                    // 设置默认值
                    if (null == supplierAddressImportDTO.getIsDefault()) {
                        supplierAddressImportDTO.setIsDefault(CommonIfEnum.NO.getValue());
                    }
                }
            }

            // 处理银行
            if (CollectionUtils.isNotEmpty(supplierImportDTO.getBdcSupplierBankDetailList())) {
                for (SupplierBankImportDTO supplierBankImportDTO : supplierImportDTO.getBdcSupplierBankDetailList()) {
                    // 设置默认值
                    if (null == supplierBankImportDTO.getIsDefault()) {
                        supplierBankImportDTO.setIsDefault(CommonIfEnum.NO.getValue());
                    }

                    // 设置状态
                    if (null == supplierBankImportDTO.getStatus()) {
                        supplierBankImportDTO.setStatus(CommonStatusEnum.EFFECTIVE.getValue());
                    }
                }
            }

            // 处理负责人
            if (CollectionUtils.isNotEmpty(supplierImportDTO.getBdcSupplierOrderManDetailList())) {
                for (SupplierOrderManImportDTO supplierOrderManImportDTO : supplierImportDTO.getBdcSupplierOrderManDetailList()) {
                    // 设置员工编号
                    if (!employeeNameMap.containsKey(supplierOrderManImportDTO.getOrderManName())) {
                        throw new BusinessException(supplierOrderManImportDTO.getOrderManName() + "不存在");
                    }
                    List<EmployeeVo> maybeMultiEmployeeList = employeeNameMap.get(supplierOrderManImportDTO.getOrderManName());

                    if (CollectionUtils.isNotEmpty(maybeMultiEmployeeList) && maybeMultiEmployeeList.size() > 1) {
                        throw new BusinessException(supplierOrderManImportDTO.getOrderManName() + "对应多个员工");
                    }
                    supplierOrderManImportDTO.setOrderManNo(maybeMultiEmployeeList.get(0).getEmployeeNo());
                }
            }
        }
    }

    public static final String UNIFIED_SOCIAL_CODE_SLASH = "/";

    private void validateImportSupplier(OperationModel operationModel, List<SupplierImportDTO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        List<String> supplierCodeList = list.stream().map(SupplierImportDTO::getSupplierCode).collect(Collectors.toList());
        List<SupplierV2> supplierV2s = supplierV2DAO.selectList(Wrappers.<SupplierV2>lambdaQuery()
                .eq(SupplierV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                .in(SupplierV2::getSupplierCode, supplierCodeList)
                .eq(SupplierV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
        Map<String, SupplierV2> dbSupplierMap = supplierV2s.stream().collect(Collectors.toMap(SupplierV2::getSupplierCode, Function.identity()));


        Map<String, List<SupplierCategoryV2>> categoryNameMap = new HashMap<>();
        List<String> supplierCategoryNameList = list.stream().map(SupplierImportDTO::getSupplierCategoryName).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(supplierCategoryNameList)) {
            List<SupplierCategoryV2> supplierCategoryV2s = supplierCategoryV2DAO.selectList(Wrappers.<SupplierCategoryV2>lambdaQuery()
                    .eq(SupplierCategoryV2::getEnterpriseNo, operationModel.getEnterpriseNo())
                    .in(SupplierCategoryV2::getCategoryName, supplierCategoryNameList)
                    .eq(SupplierCategoryV2::getDeleted, DeletedEnum.UN_DELETE.getValue()));
            categoryNameMap = supplierCategoryV2s.stream().collect(Collectors.groupingBy(SupplierCategoryV2::getCategoryName));
        }


        Map<String, List<EmployeeVo>> employeeNameMap = new HashMap<>();
        List<String> orderManNameList = list.stream().filter(supp -> null != supp.getBdcSupplierOrderManDetailList()).flatMap(supp -> supp.getBdcSupplierOrderManDetailList().stream()).map(SupplierOrderManImportDTO::getOrderManName).collect(Collectors.toList());
        List<EmployeeVo> employeeByNames = employeeService.getEmployeeByNames(operationModel.getEnterpriseNo(), orderManNameList);
        if (CollectionUtils.isNotEmpty(employeeByNames)) {
            employeeNameMap = employeeByNames.stream().collect(Collectors.groupingBy(EmployeeVo::getUserName));
        }


        for (SupplierImportDTO supplierImportDTO : list) {
            if (dbSupplierMap.containsKey(supplierImportDTO.getSupplierCode())) {
                supplierImportDTO.setExcelRowCheckResultEnumCode(ExcelRowCheckResultEnum.FAIL.getCheckCode());
                supplierImportDTO.setExcelRowCheckResultEnumMessage(supplierImportDTO.getSupplierCode() + "已存在");

                continue;
            }

            try {
                //必填项校验
                ValidatorUtils.checkEmptyThrowEx(supplierImportDTO.getManageOrgNo(), "管理组织不能为空");
                ValidatorUtils.checkEmptyThrowEx(supplierImportDTO.getSupplierCode(), "供应商编码不能为空");
                ValidatorUtils.checkEmptyThrowEx(supplierImportDTO.getSupplierName(), "供应商名称不能为空");
                ValidatorUtils.checkEmptyThrowEx(supplierImportDTO.getTransactionType(), "供应商类型不能为空");
                ValidatorUtils.checkEmptyThrowEx(supplierImportDTO.getSupplierCategoryName(), "供应商分类不能为空");
                ValidatorUtils.checkEmptyThrowEx(supplierImportDTO.getUnifiedSocialCode(), "统一社会信用代码不能为空");
                ValidatorUtils.checkEmptyThrowEx(supplierImportDTO.getTaxCategory(), "纳税类别不能为空");

                //合法值校验
                if (!FactoryTypeEnum.abroad.getValue().equals(supplierImportDTO.getFactoryType())) {
                    ValidatorUtils.checkTrueThrowEx(UNIFIED_SOCIAL_CODE_SLASH.equals(supplierImportDTO.getUnifiedSocialCode()), "境外企业统一社会信用代码不能为\"/\"");
                }
                if (null != supplierImportDTO.getIsAssociatedEnterprise() && CommonIfEnum.YES.getValue().equals(supplierImportDTO.getIsAssociatedEnterprise())) {
                    ValidatorUtils.checkEmptyThrowEx(supplierImportDTO.getAssociatedOrgNo(), "对应内部组织不能为空");
                }

                //范围校验
                if (StringUtils.isNotEmpty(supplierImportDTO.getSupplierCode())) {
                    ValidatorUtils.checkTrueThrowEx(supplierImportDTO.getSupplierCode().length() > 50, "供应商编码不能大于50");
                }
                if (StringUtils.isNotEmpty(supplierImportDTO.getSupplierName())) {
                    ValidatorUtils.checkTrueThrowEx(supplierImportDTO.getSupplierName().length() > 128, "供应商名称不能大于128");
                }
                if (StringUtils.isNotEmpty(supplierImportDTO.getSupplierNameEn())) {
                    ValidatorUtils.checkTrueThrowEx(supplierImportDTO.getSupplierNameEn().length() > 300, "供应商英文名称不能大于300");
                }
                if (StringUtils.isNotEmpty(supplierImportDTO.getMnemonicCode())) {
                    ValidatorUtils.checkTrueThrowEx(supplierImportDTO.getMnemonicCode().length() > 100, "助记码不能大于100");
                }

                if (!categoryNameMap.containsKey(supplierImportDTO.getSupplierCategoryName())) {
                    throw new BusinessException(supplierImportDTO.getSupplierCategoryName() + "不存在");
                } else {
                    List<SupplierCategoryV2> maybeMultiCategoryList = categoryNameMap.get(supplierImportDTO.getSupplierCategoryName());
                    if (CollectionUtils.isNotEmpty(maybeMultiCategoryList) && maybeMultiCategoryList.size() > 1) {
                        throw new BusinessException(supplierImportDTO.getSupplierCategoryName() + "对应多个分类");
                    }
                }

                if (StringUtils.isNotEmpty(supplierImportDTO.getCompanyName())) {
                    ValidatorUtils.checkTrueThrowEx(supplierImportDTO.getCompanyName().length() > 128, "企业名称不能大于128");
                }
                if (StringUtils.isNotEmpty(supplierImportDTO.getUnifiedSocialCode())) {
                    ValidatorUtils.checkTrueThrowEx(supplierImportDTO.getUnifiedSocialCode().length() > 100, "统一社会信用代码不能大于100");
                }
                if (StringUtils.isNotEmpty(supplierImportDTO.getRemark())) {
                    ValidatorUtils.checkTrueThrowEx(supplierImportDTO.getRemark().length() > 300, "备注不能大于300");
                }


                if (StringUtils.isNotEmpty(supplierImportDTO.getCreditAmount())) {
                    ValidatorUtils.checkTrueThrowEx(supplierImportDTO.getCreditAmount().length() > 20, "信用额度不能大于20");
                }
                if (StringUtils.isNotEmpty(supplierImportDTO.getOwnerCompany())) {
                    ValidatorUtils.checkTrueThrowEx(supplierImportDTO.getOwnerCompany().length() > 100, "业务归属不能大于20");
                }
            } catch (Exception e) {
                supplierImportDTO.setExcelRowCheckResultEnumCode(ExcelRowCheckResultEnum.FAIL.getCheckCode());
                supplierImportDTO.setExcelRowCheckResultEnumMessage(e.getMessage());
            }

            //校验关联信息
            validateSupplierLinkmanList(operationModel, supplierImportDTO.getBdcCompanyLinkmanDetailList());
            validateSupplierAddressList(operationModel, supplierImportDTO.getBdcCompanyShippingAddressDetailList());
            validateSupplierBankList(operationModel, supplierImportDTO.getBdcSupplierBankDetailList());
            validateSupplierOrderManList(operationModel, supplierImportDTO.getBdcSupplierOrderManDetailList(), employeeNameMap);
        }
    }

    private void validateSupplierLinkmanList(OperationModel operationModel, List<SupplierLinkmanImportDTO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        for (SupplierLinkmanImportDTO supplierLinkmanImportDTO : list) {
            try {
                //必填项校验
                ValidatorUtils.checkEmptyThrowEx(supplierLinkmanImportDTO.getLinkman(), "联系人不能为空");

                //范围校验
                ValidatorUtils.checkTrueThrowEx(supplierLinkmanImportDTO.getLinkman().length() > 50, "联系人不能大于50");

                if (StringUtils.isNotEmpty(supplierLinkmanImportDTO.getPosition())) {
                    ValidatorUtils.checkTrueThrowEx(supplierLinkmanImportDTO.getPosition().length() > 100, "职位不能大于100");
                }

                if (StringUtils.isNotEmpty(supplierLinkmanImportDTO.getFixedPhone())) {
                    ValidatorUtils.checkTrueThrowEx(supplierLinkmanImportDTO.getFixedPhone().length() > 100, "电话不能大于100");
                }

                if (StringUtils.isNotEmpty(supplierLinkmanImportDTO.getMobilePhone())) {
                    ValidatorUtils.checkTrueThrowEx(supplierLinkmanImportDTO.getMobilePhone().length() > 100, "手机不能大于100");
                }

                if (StringUtils.isNotEmpty(supplierLinkmanImportDTO.getQq())) {
                    ValidatorUtils.checkTrueThrowEx(supplierLinkmanImportDTO.getQq().length() > 100, "QQ不能大于100");
                }

                if (StringUtils.isNotEmpty(supplierLinkmanImportDTO.getWx())) {
                    ValidatorUtils.checkTrueThrowEx(supplierLinkmanImportDTO.getWx().length() > 100, "微信不能大于100");
                }

                if (StringUtils.isNotEmpty(supplierLinkmanImportDTO.getEmail())) {
                    ValidatorUtils.checkTrueThrowEx(supplierLinkmanImportDTO.getEmail().length() > 100, "邮箱不能大于100");
                }
            } catch (Exception e) {
                supplierLinkmanImportDTO.setExcelRowCheckResultEnumCode(ExcelRowCheckResultEnum.FAIL.getCheckCode());
                supplierLinkmanImportDTO.setExcelRowCheckResultEnumMessage(e.getMessage());
            }
        }
    }

    private void validateSupplierAddressList(OperationModel operationModel, List<SupplierAddressImportDTO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        for (SupplierAddressImportDTO supplierAddressImportDTO : list) {
            try {
                //必填项校验
                ValidatorUtils.checkEmptyThrowEx(supplierAddressImportDTO.getReceiveUser(), "联系人不能为空");
                ValidatorUtils.checkEmptyThrowEx(supplierAddressImportDTO.getReceivePhone(), "联系电话不能为空");
                ValidatorUtils.checkEmptyThrowEx(supplierAddressImportDTO.getAddressType(), "地址类型不能为空");
                ValidatorUtils.checkEmptyThrowEx(supplierAddressImportDTO.getRegionCode(), "行政区域不能为空");
                ValidatorUtils.checkEmptyThrowEx(supplierAddressImportDTO.getReceiveAddr(), "详细地址不能为空");

                //范围校验
                ValidatorUtils.checkTrueThrowEx(supplierAddressImportDTO.getReceiveUser().length() > 50, "联系人不能大于50");
                ValidatorUtils.checkTrueThrowEx(supplierAddressImportDTO.getReceivePhone().length() > 100, "联系电话不能大于100");
                ValidatorUtils.checkTrueThrowEx(supplierAddressImportDTO.getReceiveAddr().length() > 300, "详细地址不能大于300");
                if (StringUtils.isNotEmpty(supplierAddressImportDTO.getAddressDesc())) {
                    ValidatorUtils.checkTrueThrowEx(supplierAddressImportDTO.getAddressDesc().length() > 100, "地址描述不能大于100");
                }
            } catch (Exception e) {
                supplierAddressImportDTO.setExcelRowCheckResultEnumCode(ExcelRowCheckResultEnum.FAIL.getCheckCode());
                supplierAddressImportDTO.setExcelRowCheckResultEnumMessage(e.getMessage());
            }
        }
    }

    private void validateSupplierBankList(OperationModel operationModel, List<SupplierBankImportDTO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        for (SupplierBankImportDTO supplierBankImportDTO : list) {
            try {
                //必填项校验
                ValidatorUtils.checkEmptyThrowEx(supplierBankImportDTO.getOpenBank(), "开户银行不能为空");
                ValidatorUtils.checkEmptyThrowEx(supplierBankImportDTO.getAccountNo(), "银行账号不能为空");
                ValidatorUtils.checkEmptyThrowEx(supplierBankImportDTO.getAccountName(), "账户名称不能为空");
                ValidatorUtils.checkEmptyThrowEx(supplierBankImportDTO.getBankType(), "银行类别不能为空");
                ValidatorUtils.checkEmptyThrowEx(supplierBankImportDTO.getCurrencyId(), "币种不能为空");
                ValidatorUtils.checkEmptyThrowEx(supplierBankImportDTO.getAccountType(), "账户性质不能为空");

                //范围校验
                ValidatorUtils.checkTrueThrowEx(supplierBankImportDTO.getOpenBank().length() > 100, "开户银行不能大于100");
                ValidatorUtils.checkTrueThrowEx(supplierBankImportDTO.getAccountName().length() > 100, "账户名称不能大于100");
                ValidatorUtils.checkTrueThrowEx(supplierBankImportDTO.getAccountNo().length() > 100, "银行账号不能大于100");
                if (StringUtils.isNotEmpty(supplierBankImportDTO.getLinkPerson())) {
                    ValidatorUtils.checkTrueThrowEx(supplierBankImportDTO.getLinkPerson().length() > 100, "联系人不能大于100");
                }
                if (StringUtils.isNotEmpty(supplierBankImportDTO.getLinkPhone())) {
                    ValidatorUtils.checkTrueThrowEx(supplierBankImportDTO.getLinkPhone().length() > 100, "联系电话不能大于100");
                }
            } catch (Exception e) {
                supplierBankImportDTO.setExcelRowCheckResultEnumCode(ExcelRowCheckResultEnum.FAIL.getCheckCode());
                supplierBankImportDTO.setExcelRowCheckResultEnumMessage(e.getMessage());
            }
        }
    }

    private void validateSupplierOrderManList(OperationModel operationModel, List<SupplierOrderManImportDTO> list, Map<String, List<EmployeeVo>> employeeNameMap) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        for (SupplierOrderManImportDTO supplierOrderManImportDTO : list) {
            try {
                //必填项校验
                ValidatorUtils.checkEmptyThrowEx(supplierOrderManImportDTO.getDeptNo(), "部门不能为空");
                ValidatorUtils.checkEmptyThrowEx(supplierOrderManImportDTO.getOrderManName(), "员工不能为空");
                ValidatorUtils.checkEmptyThrowEx(supplierOrderManImportDTO.getOrderSpecialist(), "订单专员不能为空");
                ValidatorUtils.checkEmptyThrowEx(supplierOrderManImportDTO.getIsDefault(), "采购员不能为空");

                //范围校验
                if (StringUtils.isNotEmpty(supplierOrderManImportDTO.getOrderManName())) {
                    ValidatorUtils.checkTrueThrowEx(supplierOrderManImportDTO.getOrderManName().length() > 255, "员工姓名不能大于255");
                }
                if (StringUtils.isNotEmpty(supplierOrderManImportDTO.getPost())) {
                    ValidatorUtils.checkTrueThrowEx(supplierOrderManImportDTO.getPost().length() > 100, "职务不能大于100");
                }

                if (!employeeNameMap.containsKey(supplierOrderManImportDTO.getOrderManName())) {
                    throw new BusinessException(supplierOrderManImportDTO.getOrderManName() + "不存在");
                } else {
                    List<EmployeeVo> maybeMultiEmployeeList = employeeNameMap.get(supplierOrderManImportDTO.getOrderManName());
                    if (CollectionUtils.isNotEmpty(maybeMultiEmployeeList) && maybeMultiEmployeeList.size() > 1) {
                        throw new BusinessException(supplierOrderManImportDTO.getOrderManName() + "对应多个员工");
                    }
                }
            } catch (Exception e) {
                supplierOrderManImportDTO.setExcelRowCheckResultEnumCode(ExcelRowCheckResultEnum.FAIL.getCheckCode());
                supplierOrderManImportDTO.setExcelRowCheckResultEnumMessage(e.getMessage());
            }
        }
    }
}
