package com.yyigou.dsrp.cdc.service.supplier;

import com.yyigou.ddc.common.page.PageDto;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.ddc.services.dsrp.bdc.vo.SupplierOrderManVo;
import com.yyigou.dsrp.cdc.api.supply.dto.SupplierSalesManByOrgDTO;
import com.yyigou.dsrp.cdc.api.supply.dto.SupplierSalesManQueryBySpecifyOrgDTO;
import com.yyigou.dsrp.cdc.api.supply.dto.SupplierSalesManQueryDTO;
import com.yyigou.dsrp.cdc.common.util.OperationModel;

import java.util.List;

public interface SupplierSalesManService {
    /**
     * 跨组织获取供应商负责人列表
     *
     * @param supplierCode
     * @param orgNo
     * @return
     */
    List<SupplierOrderManVo> getSupplierSalesManListByOrg(OperationModel operationModel, String supplierCode, String orgNo);

    /**
     * 跨组织保存供应商负责人列表
     *
     * @param params
     * @return
     */
    SupplierOrderManVo saveSupplierSalesManByOrg(OperationModel operationModel, SupplierSalesManByOrgDTO params);

    /**
     * 跨组织删除负责人
     *
     * @param params
     * @param
     * @return
     */
    SupplierOrderManVo editSupplierSalesManByOrg(OperationModel operationModel, SupplierSalesManByOrgDTO params);

    /**
     * 跨组织删除负责人
     *
     * @param salesManId
     * @param
     * @return
     */
    Boolean deleteSupplierSalesManOrg(OperationModel operationModel, Long salesManId);


    /**
     * 获取供应商负责人列表
     *
     * @param params
     * @return
     */
    List<SupplierOrderManVo> findList(OperationModel operationModel, SupplierSalesManQueryDTO params);

    /**
     * 获取供应商负责人列表
     *
     * @param params
     * @return
     */
    PageVo<SupplierOrderManVo> findPage(OperationModel operationModel, SupplierSalesManQueryDTO params, PageDto pageDto);

    /**
     * 获取供应商负责人列表
     *
     * @param params
     * @return
     */
    List<SupplierOrderManVo> findListBySpecifyOrg(OperationModel operationModel, SupplierSalesManQueryBySpecifyOrgDTO params);

    /**
     * 获取供应商负责人列表
     *
     * @param params
     * @return
     */
    PageVo<SupplierOrderManVo> findPageBySpecifyOrg(OperationModel operationModel, SupplierSalesManQueryBySpecifyOrgDTO params, PageDto pageDto);
}
