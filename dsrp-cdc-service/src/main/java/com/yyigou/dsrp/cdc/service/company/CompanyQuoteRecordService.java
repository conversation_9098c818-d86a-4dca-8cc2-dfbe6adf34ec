package com.yyigou.dsrp.cdc.service.company;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yyigou.dsrp.cdc.dao.company.entity.CompanyQuoteRecord;

import java.util.List;

public interface CompanyQuoteRecordService extends IService<CompanyQuoteRecord> {

    /**
     * 查询剧团企业档案分派子租户信息
     *
     * @param groupEnterpriseNo
     * @param manageEnterpriseNo
     * @param companyNoList
     * @return: {@link List< CompanyQuoteRecord>}
     */
    List<CompanyQuoteRecord> findCompanyAssignOrgList(String groupEnterpriseNo, String manageEnterpriseNo, List<String> companyNoList);


    List<CompanyQuoteRecord> findCompanyByUseEnterprise(String manageEnterpriseNo, String useEnterpriseNo, List<String> companyNoList);

}
