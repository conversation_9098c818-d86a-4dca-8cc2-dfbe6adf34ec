<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.yyigou.dsrp.cdc</groupId>
        <artifactId>service-dsrp-cdc</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>dsrp-cdc-api</artifactId>
    <packaging>jar</packaging>

    <name>dsrp-cdc-api</name>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.yyigou.ddc</groupId>
            <artifactId>common-base-lite</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yyigou.ddc.services</groupId>
            <artifactId>ddc-task-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yyigou.dsrp.cert</groupId>
            <artifactId>dsrp-cert-client-bean</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yyigou.ddc.services</groupId>
            <artifactId>openlink-api</artifactId>
        </dependency>
    </dependencies>
</project>
