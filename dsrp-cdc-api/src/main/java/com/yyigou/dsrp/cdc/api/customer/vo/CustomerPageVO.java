package com.yyigou.dsrp.cdc.api.customer.vo;

import com.yyigou.ddc.common.service.annotation.EntityField;
import com.yyigou.ddc.common.service.annotation.MethodType;
import lombok.Data;

import java.util.List;

@Data
public class CustomerPageVO {

    /**
     * 客户编码
     */
    @EntityField(name = "客户编码", stringValueTypeLength = 50, requiredMetTypes = {MethodType.UPDATE})
    private String customerNo;
    /**
     * 客户编码
     */
    @EntityField(name = "客户编码", stringValueTypeLength = 50, requiredMetTypes = {MethodType.UPDATE})
    private String customerCode;
    /**
     * 客户名称
     */
    @EntityField(name = "客户名称", stringValueTypeLength = 128, requiredMetTypes = {MethodType.UPDATE})
    private String customerName;
    /**
     * 客户基本分类
     */
    @EntityField(name = "客户基本分类", stringValueTypeLength = 32, requiredMetTypes = {MethodType.UPDATE})
    private String customerCategoryNo;
    private String customerCategoryName;

    /**
     * 助记码
     */
    @EntityField(name = "助记码", stringValueTypeLength = 100, requiredMetTypes = {MethodType.UPDATE})
    private String mnemonicCode;

    /**
     * 客户外语名称
     */
    @EntityField(name = "客户外语名称", stringValueTypeLength = 300, requiredMetTypes = {MethodType.UPDATE})
    private String customerNameEn;


    @EntityField(name = "归属公司")
    private String ownerCompany;

    /**
     * Gsp首营状态
     */
    @EntityField(name = "Gsp首营状态 0:无需首营，1：未首营 2：已首营")
    private Integer gspStatus;


    /**
     * 公司纳税类别
     */
    @EntityField(name = "纳税类别")
    private String taxCategory;
    private String taxCategoryName;


    @EntityField(name = "纳税类别")
    private List<String> enterpriseTypes;

    /**
     * 备注
     */
    @EntityField(name = "备注", stringValueTypeLength = 500, requiredMetTypes = {MethodType.UPDATE})
    private String remark;


    /**
     * 业务信息
     */

    @EntityField(name = "管控类型id")
    private Long controlId;
    @EntityField(name = "管控类型名称")
    private String controlTypeName;


    @EntityField(name = "管控状态")
    private String controlStatus;
    @EntityField(name = "管控状态名称")
    private String controlStatusName;

    /**
     * 合作性质
     */
    @EntityField(name = "合作性质", stringValueTypeLength = 1, requiredMetTypes = {MethodType.UPDATE})
    private String cooperationMode;

    /**
     * 合作性质
     */
    @EntityField(name = "合作性质名称", stringValueTypeLength = 1, requiredMetTypes = {MethodType.UPDATE})
    private String cooperationModeName;

    @EntityField(name = "价格体系", stringValueTypeLength = 19)
    private String priceCategoryCode;
    @EntityField(name = "价格体系", stringValueTypeLength = 19)
    private String priceCategoryName;

    @EntityField(name = "客户性质")
    private String customerType;

    @EntityField(name = "客户性质名称")
    private String customerTypeName;

    @EntityField(name = "结算方式")
    private String settlementModes;

    @EntityField(name = "结算方式名称")
    private String settlementModesName;

    @EntityField(name = "信用额度")
    private String creditAmount;

    @EntityField(name = "信用期限")
    private String creditDates;

    @EntityField(name = "合作起始时间")
    private String coopStartTime;

    @EntityField(name = "合作结束时间")
    private String coopEndTime;

    /**
     * 机构类型：yy:医院,dsfsys:第三方实验室,dsftjzx:第三方体检中心,jkzx:疾控中心,xz:血站,zs:诊所,ylhly:养老护理院,jgqt:其他
     */
    @EntityField(name = "机构类型")
    private String institutionalType;
    private String institutionalTypeName;

    /**
     * 医院类型：yy：公立医院，mbyy:民营医院
     */
    @EntityField(name = "医院类型")
    private String hospitalType;

    /**
     * 医院等级：0:无等级,1:一级甲等,2:一级乙等,3:一级丙等,4:二级甲等,5:二级乙等,6:二级丙等,7:三级特等,8:三级甲等,9:三级乙等,10:三级丙等
     */
    @EntityField(name = "医院等级")
    private Integer hospitalClass;

    @EntityField(name = "收款协议")
    private String paymentAgreement;
    @EntityField(name = "收款条件")
    private String paymentCondition;
    @EntityField(name = "交易币种")
    private String currency;
    @EntityField(name = "交易币种")
    private String currencyName;
    @EntityField(name = "国家/地区")
    private String country;
    @EntityField(name = "企业注册地域：1-境内，2-境外")
    private Integer factoryType;
    @EntityField(name = "经济类型")
    private String economicType;
    private String economicTypeName;

    /**
     * 业务性质
     */
    private String businessType;

    /**
     * 业务性质名称
     */
    private String businessTypeName;

    /**
     * 统一社会信用代码。公立医院的事业单位法人证号
     */
    @EntityField(name = "统一社会信用代码")
    private String unifiedSocialCode;
    @EntityField(name = "企业编号")
    private String companyNo;
    @EntityField(name = "企业名称")
    private String companyName;
    @EntityField(name = "是否关联企业：1-是，0-否")
    private Integer isAssociatedEnterprise;
    @EntityField(name = "关联组织编号,关联企业时选择")
    private String associatedOrgNo;
    @EntityField(name = "关联组织编码,关联企业时选择")
    private String associatedOrgCode;
    @EntityField(name = "关联组织名称,关联企业时选择")
    private String associatedOrgName;

    @EntityField(name = "是否医疗机构")
    private Integer isMedicalInstitution;

    @EntityField(name = "是否医疗机构")
    private String isMedicalInstitutionName;

    @EntityField(name = "是否散户")
    private Integer retailInvestors;
    @EntityField(name = "是否散户名称")
    private String retailInvestorsName;

    @EntityField(name = "交易类型(客户类型)")
    private String transactionType;
    @EntityField(name = "交易类型名称(客户类型)")
    private String transactionTypeName;

    @EntityField(name = "需GSP首营")
    private Integer isGspControl;
    @EntityField(name = "需GSP首营名称")
    private String isGspControlName;

    /**
     * GSP的审核状态 0:未创建，1：待审批 2：审批通过 3：审核失败;	9-审批撤回
     */
    private String gspAuditStatus;
    private String gspAuditStatusName;

    /**
     * 最后申请流程：ZRSQ准入申请、BGSQ档案变更、TTSQ淘汰申请、HMDSQ黑名单申请、GSP首营
     */
    private String applyformBillType;
    /**
     * 是否发起准入
     */
    @EntityField(name = "申请单状态")
    private String applyformStatus;

    /**
     * 是否同步wms
     */
    @EntityField(name = " 是否同步wms", stringValueTypeLength = 0, requiredMetTypes = {MethodType.UPDATE})
    private Integer isSyncWms;
    @EntityField(name = " 是否同步wms", stringValueTypeLength = 0, requiredMetTypes = {MethodType.UPDATE})
    private String isSyncWmsName;
    @EntityField(name = "wms推送结果", stringValueTypeLength = 0, requiredMetTypes = {MethodType.UPDATE})
    private String wmsPushResult;

    /**
     * ys推送结果
     * ys同步标识 0:未同步 1:同步成功 2:同步失败
     */
    private String ysSyncFlag;
    private String ysSyncFlagName;
    private String ysPushResult;

    /**
     * scs同步状态
     * 同步scs状态：0：未同步 1：同步成功 2：同步失败 3：已协同
     */
    private Integer isSyncScs;
    private String isSyncScsName;
    private String scsPushResult;

    @EntityField(name = "联系人")
    private String linkMan;
    @EntityField(name = "联系电话")
    private String linkPhone;

    @EntityField(name = "版本")
    private Integer version;

}
