package com.yyigou.dsrp.cdc.api.company;

import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.ddc.services.ddc.task.message.incoming.DataImportDataSpiceMessage;
import com.yyigou.ddc.services.ddc.task.message.incoming.UapExcelImportDataSpiceMessage;
import com.yyigou.ddc.services.ddc.task.vo.incoming.ImportTaskDataSpiceVO;

public interface CdcCompanyImportAPI {

    /**
     * 分片校验逻辑
     *
     * @param uapExcelImportDataSpiceMessage
     * @return
     */
    CallResult<ImportTaskDataSpiceVO> checkExcelSliceData(UapExcelImportDataSpiceMessage uapExcelImportDataSpiceMessage);


    /**
     * 分片导入逻辑
     *
     * @param dataImportDataSpiceMessage
     * @return
     */
    CallResult<ImportTaskDataSpiceVO> handleImportSliceData(DataImportDataSpiceMessage dataImportDataSpiceMessage);
}
