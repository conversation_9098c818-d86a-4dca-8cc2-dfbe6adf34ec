package com.yyigou.dsrp.cdc.api.customer.dto;

import com.yyigou.ddc.common.service.annotation.EntityField;
import com.yyigou.ddc.common.service.annotation.MethodType;
import lombok.Data;

import java.io.Serializable;

@Data
public class CustomerLinkAddressByOrgQueryDTO extends CustomerLinkAddressQueryDTO implements Serializable {

    @EntityField(name = "组织no", stringValueTypeLength = 50, requiredMetTypes = {MethodType.UPDATE})
    private String orgNo;


}
