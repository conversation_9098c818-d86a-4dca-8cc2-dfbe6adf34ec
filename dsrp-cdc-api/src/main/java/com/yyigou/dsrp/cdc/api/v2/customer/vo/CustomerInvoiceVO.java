package com.yyigou.dsrp.cdc.api.v2.customer.vo;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;

@Data
public class CustomerInvoiceVO implements Serializable {
    @EntityField(name = "id")
    private Long id;

    private String enterpriseNo;

    private String useOrgNo;

    @EntityField(name = "发票类型: 1增值税专用发票 2增值税普通发票 3.增值税电子专用发票 4.增值税电子普通发票")
    private Integer type;

    /**
     * 纳税人识别号
     */
    @EntityField(name = "纳税人识别号")
    private String taxNo;
    /**
     * 开票抬头(企业名称)
     */
    @EntityField(name = "开票抬头(企业名称)")
    private String invoiceTitle;
    /**
     * 开户银行
     */
    @EntityField(name = "开户银行")
    private String bankDeposit;
    /**
     * 银行账号
     */
    @EntityField(name = "银行账号")
    private String bankAccount;
    /**
     * 注册地址
     */
    @EntityField(name = "注册地址")
    private String address;


    /**
     * 注册电话
     */
    @EntityField(name = "注册电话")
    private String phone;

    /**
     * 电子邮箱
     */
    @EntityField(name = "收票邮箱")
    private String email;


    @EntityField(name = "收票手机号")
    private String invoicePhone;
    /**
     * 默认发票 1默认 0非默认
     */
    @EntityField(name = "默认发票 1默认 0非默认")
    private Integer isDefault;

    /**
     * 要求
     */
    @EntityField(name = "客户开票要求")
    private String requirement;


    @EntityField(name = "客户开票要求")
    private Integer status;


    // -----------名字信息-----------
    @EntityField(name = "使用组织名称")
    private String useOrgName;

    @EntityField(name = "发票类型: 1增值税专用发票 2增值税普通发票 3.增值税电子专用发票 4.增值税电子普通发票")
    private String typeName;

    @EntityField(name = "默认发票 1默认 0非默认")
    private String isDefaultName;

}
