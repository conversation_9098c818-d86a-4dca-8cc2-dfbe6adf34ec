package com.yyigou.dsrp.cdc.api.customer.dto;

import com.yyigou.ddc.common.service.annotation.EntityField;
import com.yyigou.ddc.common.service.annotation.MethodType;
import com.yyigou.dsrp.cdc.api.company.dto.CompanyLinkmanDTO;
import lombok.Data;

import java.io.Serializable;

@Data
public class CustomerLinkmanByOrgDTO extends CompanyLinkmanDTO implements Serializable {
    @EntityField(name = "客户商编号", stringValueTypeLength = 50, requiredMetTypes = {MethodType.UPDATE})
    private String customerCode;
    @EntityField(name = "组织no", stringValueTypeLength = 50, requiredMetTypes = {MethodType.UPDATE})
    private String orgNo;
}
