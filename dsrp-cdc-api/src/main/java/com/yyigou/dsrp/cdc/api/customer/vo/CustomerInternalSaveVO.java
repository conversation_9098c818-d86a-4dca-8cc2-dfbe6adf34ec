package com.yyigou.dsrp.cdc.api.customer.vo;

import com.yyigou.ddc.services.dlog.enums.integration.IntegrationError;
import com.yyigou.dsrp.cdc.common.util.OperationModel;
import lombok.Getter;
import lombok.Setter;

/**
 * 客户MDM对接内部类
 */
@Getter
@Setter
public class CustomerInternalSaveVO extends CustomerExternalSaveVO {

    public CustomerInternalSaveVO(String customerCode,
                                  String customerName,
                                  Boolean success,
                                  String message,
                                  OperationModel operationModel,
                                  IntegrationError integrationError) {
        super(customerCode, success, message);
        this.operationModel = operationModel;
        this.integrationError = integrationError;
        this.customerName = customerName;
    }

    public CustomerInternalSaveVO(String customerCode,
                                  String customerName,
                                  Boolean success,
                                  String message,
                                  OperationModel operationModel) {
        super(customerCode, success, message);
        this.operationModel = operationModel;
        this.customerName = customerName;
    }

    private OperationModel operationModel;

    private IntegrationError integrationError;

    private String customerName;

}
