package com.yyigou.dsrp.cdc.api.supply.vo;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;

@Data
public class SupplierBankPageVO implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     *
     */
    @EntityField(name = "",stringValueTypeLength = 20)
    private Long id;
    /**
     * 所属企业编号（由scs平台分配）
     */
    @EntityField(name = "所属企业编号（由scs平台分配）",stringValueTypeLength = 32)
    private String enterpriseNo;
    /**
     * 所属供应商表
     */
    @EntityField(name = "所属供应商表",stringValueTypeLength = 32)
    private String supplierNo;
    /**
     * 所属供应商表
     */
    @EntityField(name = "企业编号",stringValueTypeLength = 32)
    private String companyNo;
    /**
     * 银行类别,取值g_bank_type表
     */
    @EntityField(name = "银行类别,取值g_bank_type表",stringValueTypeLength = 50)
    private String bankType;
    /**
     * 银行名称
     */
    @EntityField(name = "银行名称",stringValueTypeLength = 100)
    private String bankTypeName;
    /**
     * 开户行名称
     */
    @EntityField(name = "开户行名称",stringValueTypeLength = 100)
    private String openBank;
    /**
     * 账户号
     */
    @EntityField(name = "账户号",stringValueTypeLength = 100)
    private String accountNo;
    /**
     * 账号名称
     */
    @EntityField(name = "账号名称",stringValueTypeLength = 100)
    private String accountName;
    /**
     * 账号性质 1：公司 2：个人
     */
    @EntityField(name = "账号性质 1：公司 2：个人",stringValueTypeLength = 1)
    private Integer accountType;
    /**
     * 联系人
     */
    @EntityField(name = "联系人",stringValueTypeLength = 100)
    private String linkPerson;
    /**
     * 联系电话
     */
    @EntityField(name = "联系电话",stringValueTypeLength = 100)
    private String linkPhone;
    /**
     * 状态：1正常 0无效
     */
    @EntityField(name = "状态：1正常 0无效 ",stringValueTypeLength = 0)
    private Integer status;
    /**
     * 删除标志 0：未删除 1：已删除
     */
    @EntityField(name = "删除标志 0：未删除 1：已删除",stringValueTypeLength = 1)
    private Integer deleted;

    /**
     * 是否默认:1默认 0非默认
     */
    @EntityField(name = "是否默认:1默认 0非默认",stringValueTypeLength = 1)
    private Integer isDefault;



}
