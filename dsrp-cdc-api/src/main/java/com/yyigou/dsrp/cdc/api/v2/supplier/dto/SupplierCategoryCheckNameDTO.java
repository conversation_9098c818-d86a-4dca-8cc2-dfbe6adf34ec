package com.yyigou.dsrp.cdc.api.v2.supplier.dto;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;

@Data
public class SupplierCategoryCheckNameDTO implements Serializable {
    @EntityField(name = "分类名称")
    private String name;

    @EntityField(name = "分类编号")
    private String no;

    @EntityField(name = "上级分类编号")
    private String parentNo;
}