package com.yyigou.dsrp.cdc.api.v2.supplier.dto;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class SupplierCategoryUpdateDTO implements Serializable {
    @EntityField(name = "分类编号")
    private String no;

    @EntityField(name = "上级分类编号")
    private String parentNo;

    @EntityField(name = "助记码")
    private String mnemonicCode;

//    @EntityField(name = "分类编码")
//    private String categoryCode;

    @EntityField(name = "分类名称")
    private String categoryName;

    @EntityField(name = "描述")
    private String remark;

    @EntityField(name = "状态")
    private String status;

    @EntityField(name = "排序")
    private Integer sortNum;

    @EntityField(name = "管理组织编号")
    private String manageOrgNo;

    @EntityField(name = "使用组织编号集合")
    private List<String> useOrgNoList;

    @EntityField(name = "使用组织")
    private List<SupplierCategoryUseOrgDTO> useOrgList;
}