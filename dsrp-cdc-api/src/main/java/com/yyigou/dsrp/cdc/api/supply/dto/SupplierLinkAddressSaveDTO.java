package com.yyigou.dsrp.cdc.api.supply.dto;

import com.yyigou.ddc.common.service.annotation.EntityField;
import com.yyigou.ddc.common.service.annotation.MethodType;
import com.yyigou.dsrp.cdc.api.company.dto.CompanyShippingAddressDTO;
import lombok.Data;

@Data
public class SupplierLinkAddressSaveDTO extends CompanyShippingAddressDTO {
    @EntityField(name = "供应商编号", stringValueTypeLength = 50, requiredMetTypes = {MethodType.UPDATE})
    private String supplierCode;
}
