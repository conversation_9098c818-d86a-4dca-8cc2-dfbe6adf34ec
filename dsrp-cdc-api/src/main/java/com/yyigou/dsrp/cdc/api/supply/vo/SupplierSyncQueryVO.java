package com.yyigou.dsrp.cdc.api.supply.vo;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;

@Data
public class SupplierSyncQueryVO implements Serializable {
    @EntityField(name = "供应商对照的ID")
    private Long id;
    @EntityField(name = "供应商编号")
    private String supplierNo;
    @EntityField(name = "供应商编码")
    private String supplierCode;
    @EntityField(name = "供应商名称")
    private String supplierName;
    @EntityField(name = "统一社会信用代码")
    private String unifiedSocialCode;
    @EntityField(name = "协同供应商编码")
    private String supplierEnterpriseNo;
    @EntityField(name = "协同供应商名称")
    private String supplierEnterpriseName;
    @EntityField(name = "统一社会信用代码（协同）")
    private String supplierUnifiedSocialCode;
    @EntityField(name = "绑定状态：0-未绑定，1-已绑定")
    private String bindStatusName;
    @EntityField(name = "绑定状态：0-未绑定，1-已绑定")
    private String bindStatus;
}
