package com.yyigou.dsrp.cdc.api.supply;

import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.ddc.common.service.ServiceBase;
import com.yyigou.dsrp.cdc.api.supply.dto.SupplierExternalAssignDTO;
import com.yyigou.dsrp.cdc.api.supply.dto.SupplierExternalSaveDTO;
import com.yyigou.dsrp.cdc.api.supply.vo.SupplierExternalSaveVO;

import java.util.List;


public interface SupplyExternalApi extends ServiceBase {
    /**
     * 保存客户档案
     *
     * @param params
     * @return
     */
    CallResult<List<SupplierExternalSaveVO>> batchSave(List<SupplierExternalSaveDTO> params);

    CallResult<Boolean> supplyAssign(SupplierExternalAssignDTO params);
}
