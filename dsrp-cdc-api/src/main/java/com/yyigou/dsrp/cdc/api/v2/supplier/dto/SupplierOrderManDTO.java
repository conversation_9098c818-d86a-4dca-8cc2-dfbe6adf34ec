package com.yyigou.dsrp.cdc.api.v2.supplier.dto;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

@Data
public class SupplierOrderManDTO {
    @EntityField(name = "ID")
    private Long id;
    private String manCode;
    /**
     * 部门编号
     */
    @EntityField(name = "部门编号")
    private String deptNo;
    /**
     * 部门名称
     */
    @EntityField(name = "部门名称")
    private String deptName;
    /**
     * 业务员编号
     */
    @EntityField(name = "业务员编号")
    private String orderManNo;
    /**
     *
     */
    @EntityField(name = "")
    private String orderManName;

    /**
     * 职位
     */
    @EntityField(name = "职位")
    private String post;

    /**
     * 默认订单专员  2:是 1：默认  0：否
     */
    @EntityField(name = "默认订单专员  2:是 1：默认  0：否 ")
    private Integer orderSpecialist;

    /**
     * 默认业务员  2:是 1：默认  0：否
     */
    @EntityField(name = "默认业务员  2:是 1：默认  0：否 ")
    private Integer isDefault;

}
