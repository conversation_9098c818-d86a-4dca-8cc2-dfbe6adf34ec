package com.yyigou.dsrp.cdc.api.customer;

import com.yyigou.ddc.common.page.PageDto;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.ddc.common.service.ServiceBase;
import com.yyigou.dsrp.cdc.api.customer.dto.QueryPageCustomerByOrgDTO;
import com.yyigou.dsrp.cdc.api.customer.dto.QueryPageCustomerBySpecifyOrgDTO;
import com.yyigou.dsrp.cdc.api.customer.dto.QueryPageCustomerDTO;
import com.yyigou.dsrp.cdc.api.customer.dto.SaveCustomerDTO;
import com.yyigou.dsrp.cdc.api.customer.vo.CustomerPageVO;
import com.yyigou.dsrp.cdc.api.customer.vo.CustomerVO;

import java.util.List;


/**
 * 保存客户档案
 */
public interface CustomerApi extends ServiceBase {

    CallResult<Boolean> checkOnlyCode(String no, String code);

    CallResult<Boolean> checkOnlyName(String no, String name);

    /**
     * 保存客户档案
     *
     * @param params
     * @return
     */
    CallResult<String> saveCustomer(SaveCustomerDTO params);

    /**
     * 保存客户档案
     *
     * @param params
     * @return
     */
    CallResult<Boolean> updateCustomer(SaveCustomerDTO params);


    /**
     * 删除客户档案
     *
     * @param customerNoList
     * @return
     */
    CallResult<Boolean> deleteCustomer(List<String> customerNoList);

    /**
     * 获取客户档案详情
     *
     * @param customerNo
     * @return
     */
    CallResult<CustomerVO> getCustomer(String customerNo);


    /**
     * 分页获取客户档案
     *
     * @param params
     * @return
     */
    CallResult<PageVo<CustomerPageVO>> queryPageCustomer(QueryPageCustomerDTO params, PageDto pageDto);

    /**
     * 查询待处理客户数量
     * @return
     */
    CallResult<Long> findExamCount();

    /**
     * 分页获取客户档案
     *
     * @param params
     * @return
     */
    CallResult<PageVo<CustomerPageVO>> queryPageCustomerByGroup(QueryPageCustomerDTO params, PageDto pageDto);

    /**
     * 分页获取客户档案
     *
     * @param params
     * @return
     */
    CallResult<PageVo<CustomerPageVO>> queryPageCustomerByOrg(QueryPageCustomerByOrgDTO params, PageDto pageDto);

    /**
     * 分页获取指定组织客户档案
     *
     * @param params
     * @return
     */
    CallResult<PageVo<CustomerPageVO>> queryPageCustomerBySpecifyOrg(QueryPageCustomerBySpecifyOrgDTO params, PageDto pageDto);

    CallResult<PageVo<CustomerPageVO>> queryPageCustomerByCompatibleOrg(QueryPageCustomerBySpecifyOrgDTO params, PageDto pageDto);
}
