package com.yyigou.dsrp.cdc.api.company;

import com.yyigou.ddc.common.page.PageDto;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.ddc.common.service.ServiceBase;
import com.yyigou.ddc.services.dsrp.bdc.dto.CompanyCertDto;
import com.yyigou.ddc.services.dsrp.bdc.vo.CertControlVo;
import com.yyigou.dsrp.cdc.api.common.dto.CompanyBasicDTO;
import com.yyigou.dsrp.cdc.api.company.dto.CompanyQueryListPageDTO;
import com.yyigou.dsrp.cdc.api.company.dto.CompanySaveDTO;
import com.yyigou.dsrp.cdc.api.company.vo.CompanyBasicVO;
import com.yyigou.dsrp.cdc.api.company.vo.CompanyDetailVO;

import java.util.List;

public interface CompanyAPI extends ServiceBase {

    /**
     * 保存企业档案
     *
     * @param params
     * @return: {@link CallResult< CompanyBasicVO>}
     */
    CallResult<CompanyBasicVO> saveCompany(CompanySaveDTO params);


    /**
     * 获取本企业证照
     *
     * @return
     */
    CallResult<CompanyDetailVO> getEnterpriseDetail();

    /**
     * 获取企业详情
     *
     * @param params
     * @return: {@link CallResult< CompanyDetailVO>}
     */
    CallResult<CompanyDetailVO> getCompanyDetail(CompanyBasicDTO params);


    CallResult<CompanyDetailVO> getCompanyByName(String companyName);

    /**
     * 修改企业档案信息
     *
     * @param params
     * @return: {@link CallResult< Boolean>}
     */
    CallResult<Boolean> updateCompany(CompanySaveDTO params);

    CallResult<PageVo<CompanyDetailVO>> findCompanyListPage(CompanyQueryListPageDTO params, PageDto pageParams);

    CallResult<PageVo<CompanyDetailVO>> findCompanyListPageForDing(CompanyQueryListPageDTO params, PageDto pageParams);


    CallResult<List<CertControlVo>> checkQualification(CompanyCertDto params);
}
