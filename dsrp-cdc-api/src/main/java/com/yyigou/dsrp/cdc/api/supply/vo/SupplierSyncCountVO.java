package com.yyigou.dsrp.cdc.api.supply.vo;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;

@Data
public class SupplierSyncCountVO implements Serializable {
    @EntityField(name = "正式供应商数量")
    private Integer formalNum;
    @EntityField(name = "已对照供应商数量")
    private Integer bindNum = 0;
    @EntityField(name = "未对照供应商数量")
    private Integer unBindNum = 0;
}
