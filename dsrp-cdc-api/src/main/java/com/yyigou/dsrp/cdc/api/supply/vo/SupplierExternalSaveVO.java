package com.yyigou.dsrp.cdc.api.supply.vo;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.AllArgsConstructor;
import lombok.Data;

@Data
public class SupplierExternalSaveVO {

    public SupplierExternalSaveVO() {
    }

    public SupplierExternalSaveVO(String supplyCode, Boolean success, String message) {
        this.supplyCode = supplyCode;
        this.success = success;
        this.message = message;
    }

    @EntityField(name = "供应商编码", stringValueTypeLength = 400)
    private String supplyCode;
    @EntityField(name = "注册证编码", stringValueTypeLength = 400)
    private Boolean success;
    @EntityField(name = "消息", stringValueTypeLength = 400)
    private String message;
}
