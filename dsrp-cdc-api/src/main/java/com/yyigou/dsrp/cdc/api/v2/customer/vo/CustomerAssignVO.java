package com.yyigou.dsrp.cdc.api.v2.customer.vo;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;

@Data
public class CustomerAssignVO implements Serializable {
    private Long id;

    @EntityField(name = "租户编号")
    private String enterpriseNo;

    @EntityField(name = "客户档案编号")
    private String customerNo;

    @EntityField(name = "客户档案编码")
    private String customerCode;

    @EntityField(name = "管理组织编号")
    private String manageOrgNo;

    @EntityField(name = "管理组织名称")
    private String manageOrgName;

    @EntityField(name = "使用组织编号")
    private String useOrgNo;

    @EntityField(name = "使用组织名称")
    private String useOrgName;

    @EntityField(name = "管控状态")
    private String controlStatus;

    @EntityField(name = "管控状态名称")
    private String controlStatusName;
}
