package com.yyigou.dsrp.cdc.api.v2.customer.vo;

import com.yyigou.ddc.common.service.annotation.EntityField;
import com.yyigou.dsrp.cdc.api.v2.company.vo.CompanyFormalPageVO;
import com.yyigou.dsrp.cdc.api.v2.company.vo.CompanyShippingAddressFormalPageVO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class CustomerFormalPageVO extends CompanyFormalPageVO implements Serializable {
    @EntityField(name = "管理组织编号")
    private String manageOrgNo;

    @EntityField(name = "管理组织名称")
    private String manageOrgName;

    @EntityField(name = "使用组织编号")
    private String useOrgNo;

    @EntityField(name = "使用组织名称")
    private String useOrgName;

    // -------------基本信息-------------
    @EntityField(name = "客户编码")
    private String customerNo;

    @EntityField(name = "客户编码")
    private String customerCode;

    @EntityField(name = "客户名称")
    private String customerName;

    @EntityField(name = "客户基本分类")
    private String customerCategoryNo;

    @EntityField(name = "客户基本分类名称")
    private String customerCategoryName;

    @EntityField(name = "助记码")
    private String mnemonicCode;

    @EntityField(name = "是否Gsp管控：1-是，0-否")
    private Integer isGspControl;

    @EntityField(name = "是否关联企业：1-是，0-否")
    private Integer isAssociatedEnterprise;

    @EntityField(name = "是否关联企业：1-是，0-否")
    private String isAssociatedEnterpriseName;

    @EntityField(name = "关联组织编号")
    private String associatedOrgNo;

    @EntityField(name = "关联组织编码")
    private String associatedOrgCode;

    @EntityField(name = "关联组织名称")
    private String associatedOrgName;

    @EntityField(name = "客户外语名称")
    private String customerNameEn;

    @EntityField(name = "备注")
    private String remark;

    @EntityField(name = "是否正式")
    private Integer businessFlag;

    @EntityField(name = "是否散户")
    private Integer retailInvestors;

    @EntityField(name = "是否散户名称")
    private String retailInvestorsName;

    @EntityField(name = "交易类型(客户类型)")
    private String transactionType;

    @EntityField(name = "交易类型名称(客户类型)")
    private String transactionTypeName;

    @EntityField(name = "客户性质")
    private String customerType;

    @EntityField(name = "客户性质名称")
    private String customerTypeName;


    // -------------企业信息------------- @see com.yyigou.dsrp.cdc.api.v2.company.vo.CompanyFormalPageVO


    // -------------业务信息-------------
    @EntityField(name = "归属公司")
    private String ownerCompany;

    @EntityField(name = "Gsp首营状态 0:无需首营，1：未首营 2：已首营")
    private Integer gspStatus;

//    @EntityField(name = "管控类型id")
//    private Long controlId;
//
//    @EntityField(name = "管控类型名称")
//    private String controlTypeName;

    @EntityField(name = "管控状态")
    private String controlStatus;

    @EntityField(name = "管控状态名称")
    private String controlStatusName;

    @EntityField(name = "合作性质")
    private String cooperationMode;

    @EntityField(name = "合作性质名称")
    private String cooperationModeName;

    @EntityField(name = "价格体系")
    private String priceCategoryCode;

    @EntityField(name = "价格体系")
    private String priceCategoryCodeName;

    @EntityField(name = "结算方式")
    private String settlementModes;

    @EntityField(name = "结算方式名称")
    private String settlementModesName;

    @EntityField(name = "信用额度")
    private String creditAmount;

    @EntityField(name = "合作起始时间")
    private String coopStartTime;

    @EntityField(name = "合作结束时间")
    private String coopEndTime;

    @EntityField(name = "交易币种")
    private String currencyId;

    @EntityField(name = "交易币种")
    private String currencyName;

    @EntityField(name = "收款协议")
    private String paymentAgreement;

    @EntityField(name = "收款条件")
    private String paymentCondition;

    @EntityField(name = "付款条件")
    private String paymentTerm;

    @EntityField(name = "付款条件名称")
    private String paymentTermName;

    @EntityField(name = "付款协议id")
    private Long paymentAgreementId;

    @EntityField(name = "付款协议id")
    private String paymentAgreementYsId;

    @EntityField(name = "付款协议编码")
    private String paymentAgreementCode;

    @EntityField(name = "付款协议名称")
    private String paymentAgreementName;

    @EntityField(name = "账期天数")
    private Integer periodDays;

    @EntityField(name = "客户性质")
    private String businessType;

    @EntityField(name = "客户性质名称")
    private String businessTypeName;

    /**
     * 最后申请流程：ZRSQ准入申请、BGSQ档案变更、TTSQ淘汰申请、HMDSQ黑名单申请、GSP首营
     */
    @EntityField(name = "最后申请流程类型")
    private String applyformBillType;

    @EntityField(name = "申请单状态")
    private String applyformStatus;

    @EntityField(name = "联系人")
    private String linkMan;

    @EntityField(name = "联系电话")
    private String linkPhone;

    @EntityField(name = "联系人职务")
    private String LinkManPosition;

    @EntityField(name = "客户地址信息")
    private List<CompanyShippingAddressFormalPageVO> companyShippingAddressVoList;
}
