package com.yyigou.dsrp.cdc.api.v2.customer.dto;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;

@Data
public class CustomerChangeStatusDTO implements Serializable {
    @EntityField(name = "使用组织编号")
    private String useOrgNo;

    @EntityField(name = "客户编码")
    private String customerCode;

    /**
     * 管控状态 1：启用 2：停用
     */
    @EntityField(name = "管控状态")
    private String controlStatus;
}
