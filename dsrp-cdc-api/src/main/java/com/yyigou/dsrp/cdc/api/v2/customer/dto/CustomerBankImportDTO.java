package com.yyigou.dsrp.cdc.api.v2.customer.dto;

import com.yyigou.ddc.common.service.annotation.EntityField;
import com.yyigou.ddc.services.ddc.task.vo.excel.UapBaseImportDTO;
import lombok.Data;

import java.io.Serializable;

@Data
public class CustomerBankImportDTO extends UapBaseImportDTO implements Serializable {
    @EntityField(name = "客户编号")
    private String customerCode;

    @EntityField(name = "开户银行")
    private String openBank;

    @EntityField(name = "银行账号")
    private String accountNo;

    @EntityField(name = "账号名称")
    private String accountName;

    @EntityField(name = "银行类别")
    private String bankType;

    @EntityField(name = "银行类别")
    private String bankTypeName;

    @EntityField(name = "币种")
    private String currencyId;

    @EntityField(name = "币种")
    private String currencyIdName;

    @EntityField(name = "账户性质")
    private Integer accountType;

    @EntityField(name = "账户性质")
    private String accountTypeName;

    @EntityField(name = "联系人")
    private String linkPerson;

    @EntityField(name = "联系电话")
    private String linkPhone;

    @EntityField(name = "是否默认银行账号")
    private Integer isDefault;

    @EntityField(name = "是否默认银行账号")
    private String isDefaultName;

    @EntityField(name = "状态")
    private Integer status;

    @EntityField(name = "状态")
    private String statusName;

}
