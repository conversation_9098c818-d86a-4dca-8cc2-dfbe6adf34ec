package com.yyigou.dsrp.cdc.api.v2.customer.dto;

import com.yyigou.ddc.common.service.annotation.EntityField;
import com.yyigou.ddc.services.ddc.task.vo.excel.UapBaseImportDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class CustomerImportDTO extends UapBaseImportDTO implements Serializable {
    @EntityField(name = "管理组织编号")
    private String manageOrgNo;

    @EntityField(name = "管理组织名称")
    private String manageOrgNoName;

    @EntityField(name = "客户编号")
    private String customerCode;

    @EntityField(name = "客户名称")
    private String customerName;

    @EntityField(name = "客户英文名称")
    private String customerNameEn;

    @EntityField(name = "助记码")
    private String mnemonicCode;

    @EntityField(name = "客户类型")
    private String transactionType;

    @EntityField(name = "客户类型名称")
    private String transactionTypeName;

    @EntityField(name = "客户分类")
    private String customerCategoryNo;

    @EntityField(name = "客户分类名称")
    private String customerCategoryName;

    @EntityField(name = "企业编号")
    private String companyNo;

    @EntityField(name = "企业名称")
    private String companyName;

    @EntityField(name = "统一社会信用代码")
    private String unifiedSocialCode;

    @EntityField(name = "企业注册地域：1-境内，2-境外")
    private Integer factoryType;

    @EntityField(name = "企业注册地域：1-境内，2-境外")
    private String factoryTypeName;

    @EntityField(name = "国家/地区")
    private String countryRegionId;

    @EntityField(name = "国家/地区")
    private String countryRegionIdName;

    @EntityField(name = "纳税类别编码")
    private String taxCategory;

    @EntityField(name = "纳税类别名称")
    private String taxCategoryName;

    @EntityField(name = "经济类型编码")
    private String economicType;

    @EntityField(name = "经济类型名称")
    private String economicTypeName;

    @EntityField(name = "是否散户")
    private Integer retailInvestors;

    @EntityField(name = "是否散户")
    private String retailInvestorsName;

    @EntityField(name = "是否关联企业：1-是，0-否")
    private Integer isAssociatedEnterprise;

    @EntityField(name = "是否关联企业：1-是，0-否")
    private String isAssociatedEnterpriseName;

    @EntityField(name = "关联组织编号")
    private String associatedOrgNo;

    @EntityField(name = "关联组织编码")
    private String associatedOrgCode;

    @EntityField(name = "关联组织名称")
    private String associatedOrgNoName;

    @EntityField(name = "是否医疗机构")
    private Integer isMedicalInstitution;

    @EntityField(name = "是否医疗机构")
    private String isMedicalInstitutionName;

    @EntityField(name = "机构类型：yy:医院,dsfsys:第三方实验室,dsftjzx:第三方体检中心,jkzx:疾控中心,xz:血站,zs:诊所,ylhly:养老护理院,jgqt:其他")
    private String institutionalType;

    @EntityField(name = "医院类型:yy：公立医院，mbyy:民营医院")
    private String hospitalType;

    @EntityField(name = "医院等级：0:无等级,1:一级甲等,2:一级乙等,3:一级丙等,4:二级甲等,5:二级乙等,6:二级丙等,7:三级特等,8:三级甲等,9:三级乙等,10:三级丙等")
    private Integer hospitalClass;

    @EntityField(name = "备注")
    private String remark;


    @EntityField(name = "合作性质")
    private String cooperationMode;

    @EntityField(name = "合作性质")
    private String cooperationModeName;

    @EntityField(name = "客户性质")
    private String businessType;

    @EntityField(name = "客户性质")
    private String businessTypeName;

    @EntityField(name = "价格体系")
    private String priceCategoryCode;

    @EntityField(name = "价格体系")
    private String priceCategoryCodeName;

    @EntityField(name = "币种id")
    private String currencyId;

    @EntityField(name = "币种id")
    private String currencyIdName;

    @EntityField(name = "结算方式")
    private String settlementModes;

    @EntityField(name = "结算方式")
    private String settlementModesName;

    @EntityField(name = "收款协议")
    private String  receiveAgreement;

    @EntityField(name = "收款条件")
    private String receiveCondition;

    @EntityField(name = "信用额度")
    private String creditAmount;

    @EntityField(name = "信用期限")
    private String creditDates;

    @EntityField(name = "合作起始时间")
    private String coopStartTime;

    @EntityField(name = "合作结束时间")
    private String coopEndTime;

    @EntityField(name = "归属公司")
    private String ownerCompany;


    @EntityField(name = "联系人信息")
    private List<CustomerLinkmanImportDTO> bdcCompanyLinkmanDetailList;

    @EntityField(name = "地址信息")
    private List<CustomerAddressImportDTO> bdcCompanyShippingAddressDetailList;

    @EntityField(name = "银行信息")
    private List<CustomerBankImportDTO> bdcSupplierBankDetailList;

    @EntityField(name = "开票信息")
    private List<CustomerInvoiceImportDTO> bdcCustomerInvoiceDetailList;

    @EntityField(name = "负责人信息")
    private List<CustomerSalesManImportDTO> bdcCustomerSalesManDetailList;

}
