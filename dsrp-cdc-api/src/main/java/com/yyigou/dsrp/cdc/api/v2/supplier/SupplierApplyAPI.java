package com.yyigou.dsrp.cdc.api.v2.supplier;

import com.yyigou.ddc.common.page.PageDto;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.ddc.common.service.ServiceBase;
import com.yyigou.dsrp.cdc.api.v2.supplier.dto.*;
import com.yyigou.dsrp.cdc.api.v2.supplier.vo.SupplierApplyDetailVO;
import com.yyigou.dsrp.cdc.api.v2.supplier.vo.SupplierApplyPageVO;

public interface SupplierApplyAPI extends ServiceBase {
    /**
     * 申请组织视角：分页查询供应商申请列表
     *
     * @param params     查询条件
     * @param pageParams 分页条件
     * @return
     */
    CallResult<PageVo<SupplierApplyPageVO>> applyFindListPage(SupplierApplyQueryListPageDTO params, PageDto pageParams);

    /**
     * 管理组织视角：分页查询供应商待审核列表
     *
     * @param params     查询条件
     * @param pageParams 分页条件
     * @return
     */
    CallResult<PageVo<SupplierApplyPageVO>> approveFindListPage(SupplierApplyApproveQueryListPageDTO params, PageDto pageParams);

    /**
     * 有权限待审核数量
     *
     * @return
     */
    CallResult<Long> getPendingCount();


    /**
     * 申请单详情
     *
     * @param params
     * @return
     */
    CallResult<SupplierApplyDetailVO> getDetail(SupplierApplyGetDTO params);


    /**
     * 暂存、提交、编辑
     *
     * @param params
     * @return
     */
    CallResult<String> applySaveOrUpdate(SupplierApplySaveOrUpdateDTO params);


    /**
     * 审核
     *
     * @param params
     * @return
     */
    CallResult<Boolean> manageApprove(SupplierApplyApproveDTO params);


    /**
     * 撤回申请
     *
     * @param params
     * @return
     */
    CallResult<Boolean> withDrawApply(SupplierApplyWithdrawDTO params);

    /**
     * 删除申请
     *
     * @param params
     * @return
     */
    CallResult<Boolean> deleteApply(SupplierApplyDeleteDTO params);
}
