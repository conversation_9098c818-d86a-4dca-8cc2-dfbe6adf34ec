package com.yyigou.dsrp.cdc.api.v2.company.dto;

import com.yyigou.ddc.common.service.annotation.EntityField;
import com.yyigou.ddc.common.service.annotation.MethodType;
import lombok.Data;

import java.io.Serializable;

@Data
public class CompanyLinkmanDTO implements Serializable {
    private Long id;
    /**
     * 联系人
     */
    @EntityField(name = "联系人")
    private String linkman;
    /**
     * 岗位/职务
     */
    @EntityField(name = "岗位/职务")
    private String position;
    /**
     * 联系电话
     */
    @EntityField(name = "联系电话")
    private String mobilePhone;
    /**
     * 固定电话
     */
    @EntityField(name = "固定电话")
    private String fixedPhone;
    /**
     * 性别
     */
    @EntityField(name = "性别('male','secrecy','female')")
    private String sex;
    /**
     * 邮箱
     */
    @EntityField(name = "邮箱")
    private String email;
    /**
     *
     */
    @EntityField(name = "")
    private String qq;
    /**
     *
     */
    @EntityField(name = "")
    private String wx;

    /**
     * 状态：1-正常、0-作废
     */
    @EntityField(name = "状态：1-正常、0-作废")
    private Integer status;
    /**
     * 是否默认
     */
    @EntityField(name = "是否默认 1：是 0：否")
    private Integer isDefault;

    @EntityField(name = "联系编码code 外部对接主键")
    private String linkCode;
}
