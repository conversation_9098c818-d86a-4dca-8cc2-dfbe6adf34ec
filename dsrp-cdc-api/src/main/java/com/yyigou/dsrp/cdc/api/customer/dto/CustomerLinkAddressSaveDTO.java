package com.yyigou.dsrp.cdc.api.customer.dto;

import com.yyigou.ddc.common.service.annotation.EntityField;
import com.yyigou.ddc.common.service.annotation.MethodType;
import com.yyigou.dsrp.cdc.api.company.dto.CompanyShippingAddressDTO;
import lombok.Data;

import java.io.Serializable;

@Data
public class CustomerLinkAddressSaveDTO extends CompanyShippingAddressDTO implements Serializable {
    @EntityField(name = "客户编号", stringValueTypeLength = 50, requiredMetTypes = {MethodType.UPDATE})
    private String customerCode;
}
