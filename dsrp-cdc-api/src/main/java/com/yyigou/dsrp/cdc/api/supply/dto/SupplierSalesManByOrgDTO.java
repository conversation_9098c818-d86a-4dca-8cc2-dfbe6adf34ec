package com.yyigou.dsrp.cdc.api.supply.dto;

import com.yyigou.ddc.common.service.annotation.EntityField;
import com.yyigou.ddc.common.service.annotation.MethodType;
import lombok.Data;

@Data
public class SupplierSalesManByOrgDTO extends SupplierSalesManDTO {
    @EntityField(name = "供应商编号", stringValueTypeLength = 50, requiredMetTypes = {MethodType.UPDATE})
    private String supplierCode;
    @EntityField(name = "组织no", stringValueTypeLength = 50, requiredMetTypes = {MethodType.UPDATE})
    private String orgNo;
}
