package com.yyigou.dsrp.cdc.api.supply.vo;

import com.yyigou.ddc.common.service.annotation.EntityField;
import com.yyigou.ddc.common.service.annotation.MethodType;
import com.yyigou.dsrp.cdc.api.company.vo.BankVO;
import com.yyigou.dsrp.cdc.api.company.vo.CompanyLinkmanVO;
import com.yyigou.dsrp.cdc.api.company.vo.CompanyShippingAddressVO;
import lombok.Data;

import java.util.List;

@Data
public class SupplierVO {

    /**
     * 客户编码
     */
    @EntityField(name = "客户编码", stringValueTypeLength = 50, requiredMetTypes = {MethodType.UPDATE})
    private String supplierNo;
    /**
     * 客户编码
     */
    @EntityField(name = "客户编码", stringValueTypeLength = 50, requiredMetTypes = {MethodType.UPDATE})
    private String supplierCode;
    /**
     * 客户名称
     */
    @EntityField(name = "客户名称", stringValueTypeLength = 128, requiredMetTypes = {MethodType.UPDATE})
    private String supplierName;
    /**
     * 客户基本分类
     */
    @EntityField(name = "客户基本分类", stringValueTypeLength = 32, requiredMetTypes = {MethodType.UPDATE})
    private String supplierCategoryNo;
    private String supplierCategoryName;
    /**
     * 助记码
     */
    @EntityField(name = "助记码", stringValueTypeLength = 100, requiredMetTypes = {MethodType.UPDATE})
    private String mnemonicCode;

    /**
     * 客户外语名称
     */
    @EntityField(name = "客户外语名称", stringValueTypeLength = 300, requiredMetTypes = {MethodType.UPDATE})
    private String supplierNameEn;


    @EntityField(name = "归属公司")
    private String ownerCompany;

    /**
     * Gsp首营状态
     */
    @EntityField(name = "Gsp首营状态 0:无需首营，1：未首营 2：已首营")
    private Integer gspStatus;


    /**
     * 公司纳税类别
     */
    @EntityField(name = "纳税类别")
    private String companyTaxCategory;


    @EntityField(name = "纳税类别")
    private List<String> enterpriseTypes;

    /**
     * 备注
     */
    @EntityField(name = "备注", stringValueTypeLength = 500, requiredMetTypes = {MethodType.UPDATE})
    private String remark;


    /**
     * 业务信息
     */

    @EntityField(name = "管控类型id")
    private Long controlId;
    @EntityField(name = "管控类型名称")
    private String controlTypeName;

    @EntityField(name = "管控状态")
    private String controlStatus;
    @EntityField(name = "管控状态名称")
    private String controlStatusName;

    /**
     * 合作性质
     */
    @EntityField(name = "合作性质", stringValueTypeLength = 1, requiredMetTypes = {MethodType.UPDATE})
    private String cooperationMode;

    /**
     * 合作性质
     */
    @EntityField(name = "合作性质名称", stringValueTypeLength = 1, requiredMetTypes = {MethodType.UPDATE})
    private String cooperationModeName;

    @EntityField(name = "价格体系", stringValueTypeLength = 19)
    private String priceCategoryCode;

    @EntityField(name = "客户性质")
    private String supplierType;

    @EntityField(name = "客户性质名称")
    private String supplierTypeName;

    @EntityField(name = "结算方式")
    private String settlementModes;

    @EntityField(name = "结算方式名称")
    private String settlementModesName;

    @EntityField(name = "信用额度")
    private String creditAmount;

    @EntityField(name = "信用期限")
    private String creditDates;
    @EntityField(name = "信用天数")
    private Integer periodDays;

    @EntityField(name = "合作起始时间")
    private String coopStartTime;

    @EntityField(name = "合作结束时间")
    private String coopEndTime;


    /**
     * 公司纳税类别
     */
    @EntityField(name = "纳税类别")
    private String taxCategory;
    private String taxCategoryName;

    @EntityField(name = "收款协议")
    private String paymentAgreement;
    @EntityField(name = "收款条件")
    private String paymentCondition;
    @EntityField(name = "交易币种")
    private String currency;
    @EntityField(name = "交易币种")
    private String currencyName;
    @EntityField(name = "国家/地区")
    private String country;
    @EntityField(name = "企业注册地域：1-境内，2-境外")
    private Integer factoryType;
    @EntityField(name = "经济类型")
    private String economicType;
    private String economicTypeName;
    /**
     * 统一社会信用代码。公立医院的事业单位法人证号
     */
    @EntityField(name = "统一社会信用代码")
    private String unifiedSocialCode;
    @EntityField(name = "企业编号")
    private String companyNo;
    @EntityField(name = "企业名称")
    private String companyName;
    @EntityField(name = "是否关联企业：1-是，0-否")
    private Integer isAssociatedEnterprise;
    @EntityField(name = "关联组织编号,关联企业时选择")
    private String associatedOrgNo;
    @EntityField(name = "关联组织编码,关联企业时选择")
    private String associatedOrgCode;
    @EntityField(name = "关联组织名称,关联企业时选择")
    private String associatedOrgName;

    @EntityField(name = "是否散户")
    private Integer retailInvestors;
    @EntityField(name = "是否散户名称")
    private String retailInvestorsName;

    @EntityField(name = "交易类型(客户类型)")
    private String transactionType;
    @EntityField(name = "交易类型名称(客户类型)")
    private String transactionTypeName;


    /**
     * 付款条件
     */
    private String paymentTerm;

    /**
     * 付款条件名称
     */
    private String paymentTermName;
    /**
     * 付款协议id(本地的id)
     */
    @EntityField(name = "付款协议id")
    private Long paymentAgreementId;

    /**
     * 付款协议id(ys的id)
     */
    @EntityField(name = "付款协议id")
    private String paymentAgreementYsId;

    /**
     * 付款协议编码
     */
    @EntityField(name = "付款协议编码")
    private String paymentAgreementCode;

    /**
     * 付款协议名称
     */
    @EntityField(name = "付款协议名称")
    private String paymentAgreementName;
    @EntityField(name = "客户销售员信息")
    private List<SupplierSalesManVO> supplierManList;
    @EntityField(name = "客户联系人信息")
    private List<CompanyLinkmanVO> linkmanList;
    @EntityField(name = "客户联系地址")
    private List<CompanyShippingAddressVO> linkAddressList;
    @EntityField(name = "银行信息")
    private List<BankVO> bankList;

    /**
     * 客户编码
     */
    @EntityField(name = "客户编码", stringValueTypeLength = 50, requiredMetTypes = {MethodType.UPDATE})
    private String customerCode;
    /**
     * 客户名称
     */
    @EntityField(name = "客户名称", stringValueTypeLength = 128, requiredMetTypes = {MethodType.UPDATE})
    private String customerName;
    /**
     * 业务状态：0:潜在 1:合格 2：淘汰 3：黑名单
     */
    private Integer businessFlag;

    @EntityField(name = "企业注册地址")
    private String address;
}
