package com.yyigou.dsrp.cdc.api.company.dto;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 企业档案分页查询参数
 *
 * @author: Moore
 * @date: 2024/8/6 10:59
 * @version: 1.0.0
 */
@Data
public class CompanyQueryListPageDTO implements Serializable {
    private static final long serialVersionUID = 1999169811512860482L;

    @EntityField(name = "合作关系：customer-客户，supplier-供应商，factory-生产厂家")
    private String partnership;

    @EntityField(name = "企业编号集合，目前勾选导出使用")
    private List<String> companyNoList;

    @EntityField(name = "模糊搜索")
    private String keywords;
}
