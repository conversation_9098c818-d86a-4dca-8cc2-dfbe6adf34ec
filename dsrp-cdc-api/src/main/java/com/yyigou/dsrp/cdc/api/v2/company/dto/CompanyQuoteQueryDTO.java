package com.yyigou.dsrp.cdc.api.v2.company.dto;


import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;


@Data
public class CompanyQuoteQueryDTO implements Serializable {
    /**
     * 公司/企业名称（外部）
     */
    @EntityField(name = "公司/企业名称（外部）")
    private String companyNameKeywords;
    /**
     * 统一社会信用代码。公立医院的事业单位法人证号
     */
    @EntityField(name = "统一社会信用代码。公立医院的事业单位法人证号")
    private String unifiedSocialCodeKeywords;

    @EntityField(name = "合作关系")
    private String partnership;
}