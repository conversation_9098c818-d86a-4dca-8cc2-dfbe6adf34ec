package com.yyigou.dsrp.cdc.api.supply.vo;

import com.yyigou.ddc.services.dlog.enums.integration.IntegrationError;
import com.yyigou.dsrp.cdc.common.util.OperationModel;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class SupplierInternalSaveVO extends SupplierExternalSaveVO{

    public SupplierInternalSaveVO(String supplyCode,
                                  String supplyName,
                                  Boolean success,
                                  String message,
                                  OperationModel operationModel) {
        super(supplyCode, success, message);
        this.operationModel = operationModel;
        this.supplyName = supplyName;
    }

    public SupplierInternalSaveVO(String supplyCode,
                                  String supplyName,
                                  Boolean success,
                                  String message,
                                  OperationModel operationModel,
                                  IntegrationError integrationError) {
        super(supplyCode, success, message);
        this.operationModel = operationModel;
        this.integrationError = integrationError;
        this.supplyName = supplyName;
    }

    private OperationModel operationModel;

    private IntegrationError integrationError;

    private String supplyName;
}
