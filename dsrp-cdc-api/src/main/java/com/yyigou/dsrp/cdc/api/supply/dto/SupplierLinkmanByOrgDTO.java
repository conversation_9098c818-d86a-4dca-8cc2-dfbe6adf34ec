package com.yyigou.dsrp.cdc.api.supply.dto;

import com.yyigou.ddc.common.service.annotation.EntityField;
import com.yyigou.ddc.common.service.annotation.MethodType;
import com.yyigou.dsrp.cdc.api.company.dto.CompanyLinkmanDTO;
import lombok.Data;

import java.io.Serializable;

@Data
public class SupplierLinkmanByOrgDTO extends CompanyLinkmanDTO implements Serializable {

    @EntityField(name = "供应商编号", stringValueTypeLength = 50, requiredMetTypes = {MethodType.UPDATE})
    private String supplierCode;
    @EntityField(name = "组织no", stringValueTypeLength = 50, requiredMetTypes = {MethodType.UPDATE})
    private String orgNo;
}
