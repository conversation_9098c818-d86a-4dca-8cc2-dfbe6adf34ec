package com.yyigou.dsrp.cdc.api.v2.supplier;

import com.yyigou.ddc.common.page.PageDto;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.ddc.common.service.ServiceBase;
import com.yyigou.ddc.services.ddc.task.message.incoming.DataImportDataSpiceMessage;
import com.yyigou.ddc.services.ddc.task.message.incoming.UapExcelImportDataSpiceMessage;
import com.yyigou.ddc.services.ddc.task.vo.incoming.ImportTaskDataSpiceVO;
import com.yyigou.ddc.services.ddc.uim.vo.OrganizationVo;
import com.yyigou.dsrp.cdc.api.v2.supplier.dto.*;
import com.yyigou.dsrp.cdc.api.v2.supplier.vo.*;

import java.util.List;

public interface SupplierV2API extends ServiceBase {

    /**
     * 管理视角：分页查询供应商档案列表
     *
     * @param params     查询条件
     * @param pageParams 分页条件
     * @return
     */
    CallResult<PageVo<SupplierPageVO>> manageFindListPage(SupplierManageQueryListPageDTO params, PageDto pageParams);

    /**
     * 使用视角：分页查询供应商档案列表
     *
     * @param params     查询条件
     * @param pageParams 分页条件
     * @return
     */
    CallResult<PageVo<SupplierPageVO>> useFindListPage(SupplierUseQueryListPageDTO params, PageDto pageParams);

    /**
     * 使用视角：分页查询供应商档案列表
     *
     * @param params     查询条件
     * @param pageParams 分页条件
     * @return
     */
    CallResult<PageVo<SupplierPageVO>> useFindListPageNoAuthZ(SupplierUseQueryListPageNoAuthZDTO params, PageDto pageParams);

    /**
     * 使用视角：分页查询供应商档案列表
     *
     * @param params     查询条件
     * @param pageParams 分页条件
     * @return
     */
    CallResult<PageVo<SupplierReversePageVO>> reverseUseFindListPageNoAuthZ(SupplierUseQueryListPageNoAuthZDTO params, PageDto pageParams);

    /**
     * 管理视角：保存供应商档案
     *
     * @param params
     * @return: {@link CallResult < Boolean>}
     */
//    CallResult<String> manageSaveSupplier(SupplierSaveDTO params);

    /**
     * 管理视角：删除供应商档案
     *
     * @param params
     * @return: {@link CallResult < Boolean>}
     */
    CallResult<Boolean> manageDeleteSupplier(SupplierDeleteDTO params);



    /**
     * 共同视角：更新供应商档案
     *
     * @param params
     * @return
     */
//    CallResult<Boolean> updateSupplier(SupplierUpdateDTO params);

    /**
     * 共同视角：获取供应商档案详情
     *
     * @param params
     * @return
     */
    CallResult<SupplierVO> getSupplier(SupplierGetDTO params);

    /**
     * 共同视角：启停供应商档案
     *
     * @param params
     * @return
     */
    CallResult<Boolean> changeSupplierStatus(SupplierChangeStatusDTO params);


    /**
     * 共同视角：查询供应商档案分派
     *
     * @param params
     * @return
     */
    CallResult<List<SupplierAssignVO>> getAssignSupplier(SupplierGetAssignDTO params);

    /**
     * @description: 查询管理组织待处理供应商数量
     * @author: baoww
     * @date: 17:29
     * @return: com.yyigou.ddc.common.service.CallResult<java.lang.Long>
     */
    CallResult<Long> getPendingCount();

    /**
     * 校验编码唯一性
     * @param no
     * @param code
     * @return
     */
    CallResult<Boolean> checkOnlyCode(String no, String code);

    /**
     * 校验名称唯一性
     * @param no
     * @param name
     * @return
     */
    CallResult<Boolean> checkOnlyName(String no, String name);

    CallResult<PageVo<SupplierFormalPageVO>> findListPageByFormal(SupplierPageByFormalQueryDTO params, PageDto pageDto);

    CallResult<PageVo<SupplierPageVO>> querySpecifyOrgPageSupplier(SupplierPageQueryBySpecifyOrgDTO params, PageDto pageDto);

    CallResult<PageVo<SupplierPageVO>> queryPageSupplier(SupplierQueryPageDTO params, PageDto pageDto);


    CallResult<String> manageSaveSupplierBasicAndBiz(SupplierSaveBasicAndBizDTO params);

    CallResult<Boolean> editSupplierBasicAndBiz(SupplierEditBasicAndBizDTO params);

    CallResult<List<Long>> editSupplierAddressList(SupplierAddressListEditDTO params);

    CallResult<List<Long>> editSupplierLinkmanList(SupplierLinkmanListEditDTO params);

    CallResult<List<Long>> editSupplierOrdermanList(SupplierOrdermanListEditDTO params);

    CallResult<List<Long>> editSupplierBankList(SupplierBankListEditDTO params);


    CallResult<PageVo<OrganizationVo>> findOrgListPageBySetting(SupplierEligibleOrgListQueryDTO params, PageDto pageDto);



    /**
     * 分片校验导入的供应商
     *
     * @param uapexcelImportDataSpiceMessage
     * @return
     */
    CallResult<ImportTaskDataSpiceVO> checkExcelSliceData(UapExcelImportDataSpiceMessage uapexcelImportDataSpiceMessage);


    /**
     * 分片导入供应商
     *
     * @param dataImportDataSpiceMessage
     * @return
     */
    CallResult<ImportTaskDataSpiceVO> handleImportSliceData(DataImportDataSpiceMessage dataImportDataSpiceMessage);


    /**
     * 检测管理组织是否可转客户
     *
     * @param params
     * @return
     */
    CallResult<Boolean> checkConvertToCustomer(SupplierConvertToCustomerDTO params);

}
