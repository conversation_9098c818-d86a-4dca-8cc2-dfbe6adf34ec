package com.yyigou.dsrp.cdc.api.supply.dto;

import com.yyigou.ddc.common.service.annotation.EntityField;
import com.yyigou.dsrp.cdc.api.company.dto.BankDTO;
import com.yyigou.dsrp.cdc.api.company.dto.CompanyFileDTO;
import com.yyigou.dsrp.cdc.api.company.dto.CompanyLinkmanDTO;
import com.yyigou.dsrp.cdc.api.company.dto.CompanyShippingAddressDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class SupplierExternalSaveDTO implements Serializable {
    @EntityField(name = "统一社会信用代码")
    private String unifiedSocialCode;
    @EntityField(name = "企业名称")
    private String companyName;
    @EntityField(name = "是否医疗机构")
    private Integer isMedicalInstitution;
    @EntityField(name = "医疗机构性质")
    private String institutionalType;
    @EntityField(name = "医疗机构类型")
    private String hospitalType;
    @EntityField(name = "医疗机构分级")
    private Integer hospitalClass;
    @EntityField(name = "法人")
    private String legalPerson;
    @EntityField(name = "企业注册地域")
    private Integer factoryType;
    @EntityField(name = "所在地区")
    private String regionCode;
    @EntityField(name = "供应商税类名称")
    private String taxCategoryCode;
    @EntityField(name = "供应商税类名称")
    private String taxCategoryName;
    @EntityField(name = "是否关联企业")
    private Integer isAssociatedEnterprise;
    @EntityField(name = "关联企业编码")
    private String associatedOrgCode;
    @EntityField(name = "供应商编码")
    private String supplierCode;
    @EntityField(name = "供应商名称")
    private String supplierName;
    @EntityField(name = "供应商分类编码")
    private String supplierCategoryNo;
    @EntityField(name = "供应商分类编码")
    private String supplierCategoryCode_1;
    @EntityField(name = "供应商分类名称")
    private String supplierCategoryName_1;
    @EntityField(name = "供应商分类编码")
    private String supplierCategoryCode_2;
    @EntityField(name = "供应商分类名称")
    private String supplierCategoryName_2;
    @EntityField(name = "供应商分类编码")
    private String supplierCategoryCode_3;
    @EntityField(name = "供应商分类名称")
    private String supplierCategoryName_3;
    @EntityField(name = "供应商分类编码")
    private String supplierCategoryCode_4;
    @EntityField(name = "供应商分类名称")
    private String supplierCategoryName_4;
    @EntityField(name = "供应商分类编码")
    private String supplierCategoryCode_5;
    @EntityField(name = "供应商分类名称")
    private String supplierCategoryName_5;
    @EntityField(name = "是否直连供应商")
    private String directSupplier;
    @EntityField(name = "助记码")
    private String mnemonicCode;
    @EntityField(name = "供应商英文名称")
    private String supplierNameEn;
    @EntityField(name = "归宿企业")
    private String ownerCompany;
    @EntityField(name = "备注")
    private String remark;
    @EntityField(name = "生效状态：1-生效，2-失效")
    private String controlStatus;
    /**
     * 对应dsrp供应商合作性质字段
     */
    @EntityField(name = "供应商性质编码")
    private String cooperationMode;
    @EntityField(name = "供应商性质名称")
    private String cooperationModeName;
    @EntityField(name = "付款协议编码")
    private String paymentAgreementCode;
    @EntityField(name = "付款协议名称")
    private String paymentAgreementName;
    @EntityField(name = "付款条件编码")
    private String paymentTermCode;
    @EntityField(name = "结算方式编码")
    private String settlementModesCode;
    @EntityField(name = "结算方式名称")
    private String settlementModesName;
    @EntityField(name = "信用额度")
    private String creditAmount;
    @EntityField(name = "信用天数")
    private Integer periodDays;
    @EntityField(name = "合作开始时间")
    private String coopStartTime;
    @EntityField(name = "合作终止时间")
    private String coopEndTime;

    @EntityField(name = "银行信息")
    private List<BankDTO> bankList;
    @EntityField(name = "联系人")
    private List<CompanyLinkmanDTO> linkmanList;
    @EntityField(name = "负责人")
    private List<SupplierSalesManDTO> responsibleManList;
    @EntityField(name = "联系地址")
    private List<CompanyShippingAddressDTO> linkAddressList;
    @EntityField(name = "证照")
    private List<CompanyFileDTO> certList;
    @EntityField(name = "分配组织", stringValueTypeLength = 32)
    private List<SupplierExternalAssignItemDTO> assignOrgList;
}
