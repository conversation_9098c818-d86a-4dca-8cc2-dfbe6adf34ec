package com.yyigou.dsrp.cdc.api.v2.customer.vo;

import com.yyigou.ddc.common.service.annotation.EntityField;
import com.yyigou.dsrp.cdc.api.v2.company.vo.CompanyBankVO;
import com.yyigou.dsrp.cdc.api.v2.company.vo.CompanyLinkmanVO;
import com.yyigou.dsrp.cdc.api.v2.company.vo.CompanyShippingAddressVO;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class CustomerPageVO implements Serializable {
    /**
     * 供应商主键
     */
    private String customerNo;

    /**
     * 本企业编号（由scs平台分配）
     */
    private String enterpriseNo;

    /**
     * 管理组织编号
     */
    private String manageOrgNo;

    /**
     * 公司/企业编码（basic_company表)
     */
    private String companyNo;

    /**
     * 来源渠道：local-自行创建 scs-云端
     */
    private String sourceChannel;

    /**
     * 客户编码
     */
    private String customerCode;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 客户简称
     */
    private String shortName;

    /**
     * 客户外语名称
     */
    private String customerNameEn;

    /**
     * 统一社会信用代码。公立医院的事业单位法人证号
     */
    private String unifiedSocialCode;

    /**
     * 客户基本分类
     */
    private String customerCategoryNo;

    /**
     * 助记码
     */
    private String mnemonicCode;

    @EntityField(name = "是否Gsp管控：1-是，0-否")
    private Integer isGspControl;

    @EntityField(name = "是否关联企业：1-是，0-否")
    private Integer isAssociatedEnterprise;

    @EntityField(name = "关联组织编号")
    private String associatedOrgNo;

    @EntityField(name = "关联组织编码")
    private String associatedOrgCode;

    @EntityField(name = "关联组织名称")
    private String associatedOrgName;

    /**
     * 删除标志 0：未删除 1：已删除
     */
    private Integer deleted;

    /**
     * 企业类型 cs:生产企业，jxs：经营企业，yy：医疗机构
     */
    private String enterpriseType;

    /**
     * 备注
     */
    private String remark;

    /**
     * 业务状态：1:合格 2：草稿
     */
    private Integer businessFlag;

    /**
     * 最后申请流程：ZRSQ准入申请、BGSQ档案变更、TTSQ淘汰申请、HMDSQ黑名单申请、GSP首营
     */
    private String applyformBillType;

    /**
     * 最后申请流程状态：auditing-流转中、aborted-已作废、approved-审核通过、rejected-审核驳回
     */
    private String applyformStatus;

    /**
     * 创建人编号
     */
    private String createNo;

    /**
     * 制单人
     */
    private String createName;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 修改时间
     */
    private String operateNo;

    /**
     * 修改人名称
     */
    private String operateName;

    /**
     * 修改时间
     */
    private String operateTime;

    /**
     * 最后操作时间戳
     */
    private Date opTimestamp;

    private Integer retailInvestors;

    private String transactionType;

    /**
     * 使用组织编号
     */
    private String useOrgNo;

    /**
     * 使用组织修改标志 0：未修改 1：已修改
     */
    private int useOrgModifyFlag;

    /**
     * 合作性质
     */
    private String cooperationMode;

    /**
     * 管控状态 1：启用 2：停用
     */
    private String controlStatus;

    /**
     * 价格分类编码
     */
    private String priceCategoryCode;

    /**
     * 业务归属
     */
    private String ownerCompany;

    /**
     * 信用额度
     */
    private String creditAmount;

    /**
     * 信用期限
     */
    private String creditDates;

    /**
     * 结算方式
     */
    private String settlementModes;

    /**
     * 结算方式名称
     */
    private String settlementModesName;

    /*
     * 收款协议
     */
    private String  receiveAgreement;

    /**
     * 收款条件
     */
    private String receiveCondition;

    /**
     * 业务性质
     */
    private String businessType;

    /**
     * 业务性质名称
     */
    private String businessTypeName;

    /**
     * 合作起始时间
     */
    private String coopStartTime;

    /**
     * 合作结束时间
     */
    private String coopEndTime;

    /**
     * 管控类型id
     */
//    private Long controlId;

    /**
     * 管控类型名称
     */
//    private String controlTypeName;

    /**
     * 交易币种
     */
    private String currencyId;

    /**
     * oms客户编号
     */
    private String omsCustomerNo;


    @EntityField(name = "管理组织名称")
    private String manageOrgName;

    @EntityField(name = "使用组织名称")
    private String useOrgName;

    private String customerCategoryName;

    /**
     * Gsp首营状态
     */
    @EntityField(name = "Gsp首营状态 0:无需首营，1：未首营 2：已首营")
    private Integer gspStatus;


    @EntityField(name = "首营状态")
    private Integer gspAuditStatus;


    @EntityField(name = "首营状态")
    private String gspAuditStatusName;


    @EntityField(name = "首营结果")
    private String gspAuditResult;


    /**
     * 公司纳税类别
     */
    @EntityField(name = "纳税类别")
    private String taxCategory;
    private String taxCategoryName;


    @EntityField(name = "纳税类别")
    private List<String> enterpriseTypes;

    @EntityField(name = "管控状态名称")
    private String controlStatusName;

    /**
     * 合作性质
     */
    @EntityField(name = "合作性质名称")
    private String cooperationModeName;

    @EntityField(name = "价格体系")
    private String priceCategoryName;

    /**
     * 机构类型：yy:医院,dsfsys:第三方实验室,dsftjzx:第三方体检中心,jkzx:疾控中心,xz:血站,zs:诊所,ylhly:养老护理院,jgqt:其他
     */
    @EntityField(name = "机构类型")
    private String institutionalType;

    @EntityField(name = "机构类型")
    private String institutionalTypeName;

    /**
     * 医院类型：yy：公立医院，mbyy:民营医院
     */
    @EntityField(name = "医院类型")
    private String hospitalType;

    /**
     * 医院等级：0:无等级,1:一级甲等,2:一级乙等,3:一级丙等,4:二级甲等,5:二级乙等,6:二级丙等,7:三级特等,8:三级甲等,9:三级乙等,10:三级丙等
     */
    @EntityField(name = "医院等级")
    private Integer hospitalClass;

    @EntityField(name = "交易币种")
    private String currencyName;
    @EntityField(name = "国家/地区")
    private String country;

    @EntityField(name = "国家/地区")
    private String countryRegionId;

    @EntityField(name = "国家/地区")
    private String countryRegionName;

    @EntityField(name = "企业注册地域：1-境内，2-境外")
    private Integer factoryType;
    private String factoryTypeName;

    @EntityField(name = "经济类型")
    private String economicType;
    private String economicTypeName;

    @EntityField(name = "企业名称")
    private String companyName;

    @EntityField(name = "是否医疗机构")
    private Integer isMedicalInstitution;

    @EntityField(name = "是否医疗机构")
    private String isMedicalInstitutionName;

    @EntityField(name = "是否散户名称")
    private String retailInvestorsName;

    @EntityField(name = "交易类型名称(客户类型)")
    private String transactionTypeName;




    /**
     * 业务区域集合
     */
    @EntityField(name = "业务区域集合")
//    private List<AreaVo> areaList;
    private String supplierCode;
    private String supplierName;


    /**
     * 注册地址
     */
    private String address;

    private String receiveTermName;

}
