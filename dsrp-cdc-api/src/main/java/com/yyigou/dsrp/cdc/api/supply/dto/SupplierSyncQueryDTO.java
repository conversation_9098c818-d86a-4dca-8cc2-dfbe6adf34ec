package com.yyigou.dsrp.cdc.api.supply.dto;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class SupplierSyncQueryDTO implements Serializable {
    @EntityField(name = "绑定状态：0-未绑定，1-已绑定")
    private Integer bindStatus;
    @EntityField(name = "模糊搜索：供应商编码/名称")
    private String keywords;
    @EntityField(name = "供应商编码列表")
    private List<String> supplierNoList;
}
