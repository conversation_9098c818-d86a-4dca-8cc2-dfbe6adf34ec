package com.yyigou.dsrp.cdc.api.customer;


import com.yyigou.ddc.common.page.PageDto;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.ddc.common.service.ServiceBase;
import com.yyigou.dsrp.cdc.api.customer.dto.CustomerInvoiceQueryDTO;
import com.yyigou.dsrp.cdc.api.customer.vo.CustomerInvoiceVO;

import java.util.List;

/**
 * 客户税务信息
 */
public interface CustomerInvoiceApi extends ServiceBase  {

    CallResult<List<CustomerInvoiceVO>> getInvoiceListByCustomerCode(CustomerInvoiceQueryDTO params);

}
