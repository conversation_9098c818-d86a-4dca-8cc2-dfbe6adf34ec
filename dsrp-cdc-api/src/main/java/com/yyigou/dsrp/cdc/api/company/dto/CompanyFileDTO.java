package com.yyigou.dsrp.cdc.api.company.dto;

import com.yyigou.ddc.common.service.annotation.Entity;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 收货地址表
 */
@Data
@Entity(name = "企业证照文件")
public class CompanyFileDTO implements Serializable {

    private String certTypeCode;

    private String certTypeName;

    private String certCode;

    private String certName;

    private String startTime;

    private String endTime;

    private Integer longTerm;

    private String approvalDate;
    private String remark;

    private List<String> baseFileList;
}