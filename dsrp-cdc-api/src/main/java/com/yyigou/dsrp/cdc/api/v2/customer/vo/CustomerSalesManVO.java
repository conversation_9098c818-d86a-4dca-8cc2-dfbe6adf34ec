package com.yyigou.dsrp.cdc.api.v2.customer.vo;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;

@Data
public class CustomerSalesManVO implements Serializable {
    @EntityField(name = "id")
    private Long id;

    private String enterpriseNo;

    private String useOrgNo;

    /**
     * 部门编号
     */
    @EntityField(name = "部门编号")
    private String orgNo;
    /**
     * 部门名称
     */
    @EntityField(name = "部门名称")
    private String orgName;
    /**
     * 业务员编号
     */
    @EntityField(name = "业务员编号")
    private String salesManNo;
    /**
     *
     */
    @EntityField(name = "")
    private String salesManName;

    /**
     * 职位
     */
    @EntityField(name = "职位")
    private String post;


    /**
     * 默认订单专员  2:是 1：默认  0：否
     */
    @EntityField(name = "默认订单专员  2:是 1：默认  0：否 ")
    private Integer orderSpecialist;

    /**
     * 默认业务员  2:是 1：默认  0：否
     */
    @EntityField(name = "默认业务员  2:是 1：默认  0：否 ")
    private Integer isDefault;

    /**
     * 部门编号
     */
    @EntityField(name = "部门编号")
    private String deptNo;
    /**
     * 部门名称
     */
    @EntityField(name = "部门名称")
    private String deptName;

    // -----------名字信息------------
    /**
     * 手机号
     */
    @EntityField(name = "手机号")
    private String mobile;

    @EntityField(name = "默认订单专员  2:是 1：默认  0：否 ")
    private String orderSpecialistName;

    @EntityField(name = "默认业务员  2:是 1：默认  0：否 ")
    private String isDefaultName;
}
