package com.yyigou.dsrp.cdc.api.supply;

import com.yyigou.ddc.common.page.PageDto;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.ddc.common.service.ServiceBase;
import com.yyigou.dsrp.cdc.api.company.vo.CompanyShippingAddressVO;
import com.yyigou.dsrp.cdc.api.supply.dto.SupplierLinkAddressQueryDTO;
import com.yyigou.dsrp.cdc.api.supply.dto.SupplierLinkAddressSaveByOrgDTO;
import com.yyigou.dsrp.cdc.api.supply.dto.SupplierLinkAddressSaveDTO;

import java.util.List;

/**
 * 供应商联系地址接口
 */
public interface SupplierLinkAddressApi extends ServiceBase {
    /**
     * 获取客户联系地址列表
     *
     * @param supplierCode
     * @return
     */
    CallResult<List<CompanyShippingAddressVO>> getSupplierLinkAddressList(String supplierCode);


    /**
     * 获取客户联系地址列表(分页)
     */
    CallResult<PageVo<CompanyShippingAddressVO>> findSupplierLinkAddressPage(SupplierLinkAddressQueryDTO params, PageDto pageDto);


    CallResult<CompanyShippingAddressVO> saveSupplierLinkAddress(SupplierLinkAddressSaveDTO params);

    CallResult<CompanyShippingAddressVO> editSupplierLinkAddress(SupplierLinkAddressSaveDTO params);

    /**
     * 跨组织获取供应商联系地址列表
     *
     * @param supplierCode
     * @param orgNo
     * @return
     */
    CallResult<List<CompanyShippingAddressVO>> getSupplierLinkAddressListByOrg(String supplierCode, String orgNo);

    /**
     * 跨组织保存供应商联系地址列表
     *
     * @param params
     * @return
     */
    CallResult<CompanyShippingAddressVO> saveSupplierLinkAddressByOrg(SupplierLinkAddressSaveByOrgDTO params);

    /**
     * 跨组织编辑联系地址
     *
     * @param params
     * @param
     * @return
     */
    CallResult<CompanyShippingAddressVO> editSupplierLinkAddressByOrg(SupplierLinkAddressSaveByOrgDTO params);

    /**
     * 跨组织删除联系地址
     *
     * @param linkAddressId
     * @param
     * @return
     */
    CallResult<Boolean> deleteSupplierLinkAddressByOrg(Long linkAddressId);
}
