package com.yyigou.dsrp.cdc.api.supply;

import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.ddc.common.service.ServiceBase;
import com.yyigou.dsrp.cdc.api.supply.vo.SupplierCategoryTree;

import java.util.List;


/**
 * 客户分类
 */
public interface SupplierCategoryApi extends ServiceBase {


    CallResult<List<SupplierCategoryTree>> queryTree();


    CallResult<List<SupplierCategoryTree>> queryGroupTree();

}
