package com.yyigou.dsrp.cdc.api.v2.company.vo;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;

/**
 * 企业档案基础字段
 */
@Data
public class CompanyBasicVO implements Serializable {
    private static final long serialVersionUID = -1788749759482607503L;

    @EntityField(name = "租户编号")
    private String enterpriseNo;

    @EntityField(name = "企业编号")
    private String companyNo;

    @EntityField(name = "企业编码，集团化统一编码")
    private String companyCode;

    @EntityField(name = "企业名称")
    private String companyName;

    @EntityField(name = "统一社会信用代码")
    private String unifiedSocialCode;
}
