package com.yyigou.dsrp.cdc.api.customer;

import com.yyigou.ddc.common.page.PageDto;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.ddc.common.service.ServiceBase;
import com.yyigou.dsrp.cdc.api.company.vo.CompanyShippingAddressVO;
import com.yyigou.dsrp.cdc.api.customer.dto.CustomerLinkAddressByOrgQueryDTO;
import com.yyigou.dsrp.cdc.api.customer.dto.CustomerLinkAddressQueryDTO;
import com.yyigou.dsrp.cdc.api.customer.dto.CustomerLinkAddressSaveByOrgDTO;
import com.yyigou.dsrp.cdc.api.customer.dto.CustomerLinkAddressSaveDTO;

import java.util.List;

/**
 * 客户联系地址接口
 */
public interface CustomerLinkAddressApi extends ServiceBase {

    /**
     * 获取客户联系地址列表
     *
     * @param customerCode
     * @return
     */
    CallResult<List<CompanyShippingAddressVO>> getCustomerLinkAddressList(String customerCode);

    /**
     * 获取客户联系地址列表(分页)
     */
    CallResult<PageVo<CompanyShippingAddressVO>> findCustomerLinkAddressPage(CustomerLinkAddressQueryDTO params, PageDto pageDto);


    /**
     * 保存客户联系地址列表
     *
     * @param params
     * @return
     */
    CallResult<CompanyShippingAddressVO> saveCustomerLinkAddress(CustomerLinkAddressSaveDTO params);

    /**
     * 编辑联系地址
     *
     * @param params
     * @param
     * @return
     */
    CallResult<CompanyShippingAddressVO> editCustomerLinkAddress(CustomerLinkAddressSaveDTO params);

    /**
     * 跨组织获取客户联系地址列表
     *
     * @param customerCode
     * @param orgNo
     * @return
     */
    CallResult<List<CompanyShippingAddressVO>> getCustomerLinkAddressListByOrg(String customerCode, String orgNo);

    /**
     * 跨组织获取客户联系地址列表(分页)
     */
    CallResult<PageVo<CompanyShippingAddressVO>> findCustomerLinkAddressListByOrgPage(CustomerLinkAddressByOrgQueryDTO params, PageDto pageDto);

    /**
     * 跨组织保存客户联系地址列表
     *
     * @param params
     * @return
     */
    CallResult<CompanyShippingAddressVO> saveCustomerLinkAddressByOrg(CustomerLinkAddressSaveByOrgDTO params);

    /**
     * 跨组织编辑联系地址
     *
     * @param params
     * @param
     * @return
     */
    CallResult<CompanyShippingAddressVO> editCustomerLinkAddressByOrg(CustomerLinkAddressSaveByOrgDTO params);

    /**
     * 跨组织删除联系地址
     *
     * @param linkAddressId
     * @param
     * @return
     */
    CallResult<Boolean> deleteCustomerLinkAddressByOrg(Long linkAddressId);
}
