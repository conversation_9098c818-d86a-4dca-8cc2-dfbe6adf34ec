package com.yyigou.dsrp.cdc.api.v2.customer.dto;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;

@Data
public class CustomerSalesManQueryDTO implements Serializable {
    private static final long serialVersionUID = 1999169811512860482L;

    @EntityField(name = "客户编号")
    private String customerCode;

    @EntityField(name = "组织编号")
    private String useOrgNo;

    @EntityField(name = "默认业务员  2:是 1：默认  0：否 ")
    private Integer isDefault;

}
