package com.yyigou.dsrp.cdc.api.v2.supplier;

import com.yyigou.ddc.common.page.PageDto;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.ddc.common.service.ServiceBase;
import com.yyigou.dsrp.cdc.api.v2.supplier.dto.*;
import com.yyigou.dsrp.cdc.api.v2.supplier.vo.SupplierCategoryTreeVO;
import com.yyigou.dsrp.cdc.api.v2.supplier.vo.SupplierCategoryVO;

import java.util.List;


/**
 * 供应商分类
 */
public interface SupplierCategoryV2API extends ServiceBase {
    CallResult<List<SupplierCategoryTreeVO>> queryTree(SupplierCategoryQueryTreeDTO params);

    CallResult<List<SupplierCategoryTreeVO>> queryCategoryTree(SupplierCategoryQueryCategoryTreeDTO params);

    CallResult<List<SupplierCategoryTreeVO>> queryUseCategoryTree(SupplierCategoryQueryUseCategoryTreeDTO params);

    CallResult<PageVo<SupplierCategoryVO>> queryListPage(SupplierCategoryQueryListPageDTO params, PageDto pageDto);

    CallResult<Boolean> checkUniqueName(SupplierCategoryCheckNameDTO params);

    CallResult<Boolean> checkUniqueCode(SupplierCategoryCheckCodeDTO params);

    CallResult<Boolean> checkUseOrgRemoval(SupplierCategoryCheckUseOrgRemovalDTO params);

    CallResult<SupplierCategoryVO> save(SupplierCategorySaveDTO params);

    CallResult<SupplierCategoryVO> get(SupplierCategoryGetDTO params);

    CallResult<SupplierCategoryVO> update(SupplierCategoryUpdateDTO params);

    CallResult<Boolean> delete(SupplierCategoryDeleteDTO params);

    CallResult<Boolean> changeStatus(SupplierCategoryChangeStatusDTO params);

    CallResult<PageVo<SupplierCategoryVO>> queryFlatListPage(SupplierCategoryQueryFlagListPageDTO params, PageDto pageDto);
}
