package com.yyigou.dsrp.cdc.api.common.dto;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;

@Data
public class CompanyBasicDTO implements Serializable {
    private static final long serialVersionUID = 76475985963785482L;

    @EntityField(name = "企业编号")
    private String companyNo;

    @EntityField(name = "企业名称")
    private String companyName;

    @EntityField(name = "企业编码")
    private String companyCode;
}
