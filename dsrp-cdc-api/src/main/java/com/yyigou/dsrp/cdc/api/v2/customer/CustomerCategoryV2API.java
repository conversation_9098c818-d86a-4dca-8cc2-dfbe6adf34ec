package com.yyigou.dsrp.cdc.api.v2.customer;

import com.yyigou.ddc.common.page.PageDto;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.ddc.common.service.ServiceBase;
import com.yyigou.dsrp.cdc.api.v2.customer.dto.*;
import com.yyigou.dsrp.cdc.api.v2.customer.vo.CustomerCategoryTreeVO;
import com.yyigou.dsrp.cdc.api.v2.customer.vo.CustomerCategoryVO;
import com.yyigou.dsrp.cdc.api.v2.supplier.dto.SupplierCategoryCheckUseOrgRemovalDTO;

import java.util.List;


/**
 * 客户分类
 */
public interface CustomerCategoryV2API extends ServiceBase {
    CallResult<List<CustomerCategoryTreeVO>> queryTree(CustomerCategoryQueryTreeDTO params);

    CallResult<List<CustomerCategoryTreeVO>> queryCategoryTree(CustomerCategoryQueryCategoryTreeDTO params);

    CallResult<List<CustomerCategoryTreeVO>> queryUseCategoryTree(CustomerCategoryQueryUseCategoryTreeDTO params);

    CallResult<PageVo<CustomerCategoryVO>> queryListPage(CustomerCategoryQueryListPageDTO params, PageDto pageDto);

    CallResult<Boolean> checkUniqueName(CustomerCategoryCheckNameDTO params);

    CallResult<Boolean> checkUniqueCode(CustomerCategoryCheckCodeDTO params);

    CallResult<Boolean> checkUseOrgRemoval(CustomerCategoryCheckUseOrgRemovalDTO params);

    CallResult<CustomerCategoryVO> save(CustomerCategorySaveDTO params);

    CallResult<CustomerCategoryVO> get(CustomerCategoryGetDTO params);

    CallResult<CustomerCategoryVO> update(CustomerCategoryUpdateDTO params);

    CallResult<Boolean> delete(CustomerCategoryDeleteDTO params);

    CallResult<Boolean> changeStatus(CustomerCategoryChangeStatusDTO params);
}
