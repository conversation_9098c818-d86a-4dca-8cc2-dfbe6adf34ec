package com.yyigou.dsrp.cdc.api.v2.supplier.dto;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 供应商档案分页查询参数
 */
@Data
public class SupplierUseQueryListPageNoAuthZDTO implements Serializable {
    private static final long serialVersionUID = 1999169811512860482L;

    @EntityField(name = "使用组织编号")
    private String useOrgNo;

    @EntityField(name = "供应商名称模糊搜索")
    private String supplierNameKeywords;

    @EntityField(name = "供应商模糊搜索（名称、编码）")
    private String supplierKeywords;

    @EntityField(name = "控制状态")
    private List<String> controlStatusList;
}
