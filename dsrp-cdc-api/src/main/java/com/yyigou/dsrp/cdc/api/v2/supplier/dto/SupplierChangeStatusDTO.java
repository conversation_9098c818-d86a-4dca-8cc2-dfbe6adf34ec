package com.yyigou.dsrp.cdc.api.v2.supplier.dto;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;

@Data
public class SupplierChangeStatusDTO implements Serializable {

    @EntityField(name = "使用组织编号")
    private String useOrgNo;

    @EntityField(name = "供应商编码")
    private String supplierCode;

    /**
     * 管控状态 1：启用 2：停用 3：冻结
     */
    @EntityField(name = "管控状态")
    private String controlStatus;
}
