package com.yyigou.dsrp.cdc.api.supply;

import com.yyigou.ddc.common.page.PageDto;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.ddc.common.service.ServiceBase;
import com.yyigou.ddc.services.dsrp.bdc.vo.SupplierOrderManVo;
import com.yyigou.dsrp.cdc.api.supply.dto.SupplierSalesManByOrgDTO;
import com.yyigou.dsrp.cdc.api.supply.dto.SupplierSalesManQueryBySpecifyOrgDTO;
import com.yyigou.dsrp.cdc.api.supply.dto.SupplierSalesManQueryDTO;

import java.util.List;

/**
 * 供应商负责人接口
 */
public interface SupplierSalesManApi extends ServiceBase {
    /**
     * 跨组织获取供应商负责人列表
     *
     * @param supplierCode
     * @param orgNo
     * @return
     */
    CallResult<List<SupplierOrderManVo>> getSupplierSalesManListByOrg(String supplierCode, String orgNo);

    /**
     * 跨组织保存供应商负责人列表
     *
     * @param params
     * @return
     */
    CallResult<SupplierOrderManVo> saveSupplierSalesManByOrg(SupplierSalesManByOrgDTO params);

    /**
     * 跨组织删除负责人
     *
     * @param params
     * @param
     * @return
     */
    CallResult<SupplierOrderManVo> editSupplierSalesManByOrg(SupplierSalesManByOrgDTO params);

    /**
     * 跨组织删除负责人
     *
     * @param salesManId
     * @param
     * @return
     */
    CallResult<Boolean> deleteSupplierSalesManOrg(Long salesManId);

    /**
     * 获取供应商负责人列表
     *
     * @param params
     * @return
     */
    CallResult<List<SupplierOrderManVo>> findList(SupplierSalesManQueryDTO params);

    /**
     * 分页获取供应商负责人列表
     *
     * @param params
     * @return
     */
    CallResult<PageVo<SupplierOrderManVo>> findPage(SupplierSalesManQueryDTO params, PageDto pageDto);

    /**
     * 跨组织获取供应商负责人列表
     *
     * @param params
     * @return
     */
    CallResult<List<SupplierOrderManVo>> findListBySpecifyOrg(SupplierSalesManQueryBySpecifyOrgDTO params);

    /**
     * 跨组织分页获取供应商负责人列表
     *
     * @param params
     * @return
     */
    CallResult<PageVo<SupplierOrderManVo>> findPageBySpecifyOrg(SupplierSalesManQueryBySpecifyOrgDTO params, PageDto pageDto);
}
