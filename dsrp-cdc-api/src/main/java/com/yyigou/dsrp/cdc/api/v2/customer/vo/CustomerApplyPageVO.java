package com.yyigou.dsrp.cdc.api.v2.customer.vo;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;

@Data
public class CustomerApplyPageVO implements Serializable {
    @EntityField(name = "主键ID")
    private Long id;

    @EntityField(name = "申请单号")
    private String applyInstanceNo;

    @EntityField(name = "申请组织编码")
    private String applyOrgNo;

    @EntityField(name = "申请组织名称")
    private String applyOrgName;

    @EntityField(name = "使用组织编号")
    private String useOrgNo;

    @EntityField(name = "使用组织名称")
    private String useOrgName;

    @EntityField(name = "管理组织编号")
    private String manageOrgNo;

    @EntityField(name = "管理组织名称")
    private String manageOrgName;

    @EntityField(name = "企业编码")
    private String companyCode;

    @EntityField(name = "企业名称")
    private String companyName;

    @EntityField(name = "客户编码")
    private String customerCode;

    @EntityField(name = "客户名称")
    private String customerName;

    @EntityField(name = "申请类型 2-新增申请 3-变更申请")
    private Integer applyType;

    @EntityField(name = "申请类型 2-新增申请 3-变更申请")
    private String applyTypeName;

    @EntityField(name = "申请原因")
    private String applyReason;

    @EntityField(name = "申请原因")
    private String applyReasonName;

    @EntityField(name = "申请说明")
    private String applyDesc;

    @EntityField(name = "审核状态：0-草稿 1-待审核 2-审核通过 3-审核拒绝")
    private Integer auditStatus;

    @EntityField(name = "审核状态：0-草稿 1-待审核 2-审核通过 3-审核拒绝")
    private String auditStatusName;

    @EntityField(name = "申请结果 1-成功 2-失败")
    private Integer applyResult;

    @EntityField(name = "申请结果 1-成功 2-失败")
    private String applyResultName;

    @EntityField(name = "申请人编号")
    private String applyNo;

    @EntityField(name = "申请人名称")
    private String applyName;

    @EntityField(name = "申请时间")
    private String applyTime;

    @EntityField(name = "审核人编号")
    private String auditNo;

    @EntityField(name = "审核人名称")
    private String auditName;

    @EntityField(name = "审核时间")
    private String auditTime;

    @EntityField(name = "审核意见")
    private String auditRemark;

    @EntityField(name = "失败原因")
    private String failReason;

    @EntityField(name = "是否删除 1是 0否")
    private Integer deleted;

    @EntityField(name = "是否删除 1是 0否")
    private String deletedName;

    @EntityField(name = "是否有效  0无效 1有效")
    private Integer status;

    @EntityField(name = "是否有效  0无效 1有效")
    private String statusName;

    @EntityField(name = "创建人编号")
    private String createNo;

    @EntityField(name = "创建人")
    private String createName;

    @EntityField(name = "创建时间")
    private String createTime;

    @EntityField(name = "更新人编号")
    private String modifyNo;

    @EntityField(name = "更新人")
    private String modifyName;

    @EntityField(name = "更新时间")
    private String modifyTime;
}
