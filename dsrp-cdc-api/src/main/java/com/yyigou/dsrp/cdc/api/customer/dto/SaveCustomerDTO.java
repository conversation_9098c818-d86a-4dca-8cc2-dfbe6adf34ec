package com.yyigou.dsrp.cdc.api.customer.dto;

import com.yyigou.ddc.common.service.annotation.EntityField;
import com.yyigou.ddc.common.service.annotation.MethodType;
import com.yyigou.dsrp.cdc.api.company.dto.BankDTO;
import com.yyigou.dsrp.cdc.api.company.dto.CompanyLinkmanDTO;
import com.yyigou.dsrp.cdc.api.company.dto.CompanyShippingAddressDTO;
import com.yyigou.dsrp.cert.client.certbase.req.CatalogInfoBigClientReq;
import com.yyigou.dsrp.cert.client.certbase.req.CatalogInfoClientReq;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class SaveCustomerDTO implements Serializable {
    @EntityField(name = "申请id", stringValueTypeLength = 50, requiredMetTypes = {MethodType.UPDATE})
    private String id;
    /**
     * 客户编码
     */
    @EntityField(name = "客户NO", stringValueTypeLength = 50, requiredMetTypes = {MethodType.UPDATE})
    private String customerNo;
    /**
     * 客户编码
     */
    @EntityField(name = "客户编码", stringValueTypeLength = 50, requiredMetTypes = {MethodType.UPDATE})
    private String customerCode;
    /**
     * 客户名称
     */
    @EntityField(name = "客户名称", stringValueTypeLength = 128, requiredMetTypes = {MethodType.UPDATE})
    private String customerName;

    /**
     * 客户英文名称
     */
    @EntityField(name = "客户英文名称", stringValueTypeLength = 300, requiredMetTypes = {MethodType.UPDATE})
    private String customerNameEn;

    /**
     * 助记码
     */
    @EntityField(name = "助记码", stringValueTypeLength = 100, requiredMetTypes = {MethodType.UPDATE})
    private String mnemonicCode;


    @EntityField(name = "客户交易类型", stringValueTypeLength = 1, requiredMetTypes = {MethodType.UPDATE})
    private String transactionType;

    /**
     * 客户分类
     */
    @EntityField(name = "客户分类", stringValueTypeLength = 32, requiredMetTypes = {MethodType.UPDATE})
    private String customerCategoryNo;


    @EntityField(name = "企业名称")
    private String companyName;

    @EntityField(name = "统一社会信用代码。公立医院的事业单位法人证号")
    private String unifiedSocialCode;

    @EntityField(name = "企业注册地域：1-境内，2-境外")
    private Integer factoryType;

    @EntityField(name = "国家/地区")
    private String country;
    /**
     * Gsp首营状态
     */
    @EntityField(name = "Gsp首营状态 0:无需首营，1：未首营 2：已首营")
    private Integer gspStatus;
    /**
     * 公司纳税类别
     */
    @EntityField(name = "纳税类别")
    private String taxCategory;


    @EntityField(name = "经济类型")
    private String economicType;

    @EntityField(name = "归属公司")
    private String ownerCompany;

    @EntityField(name = "散户 1.散户 0非散户")
    private Integer retailInvestors;

    @EntityField(name = "是否关联企业：1-是，0-否")
    private Integer isAssociatedEnterprise;

    @EntityField(name = "关联组织编号,关联企业时选择")
    private String associatedOrgNo;

    @EntityField(name = "关联组织编码,关联企业时选择")
    private String associatedOrgCode;

    @EntityField(name = "关联组织名称,关联企业时选择")
    private String associatedOrgName;

    @EntityField(name = "是否医疗机构")
    private Integer isMedicalInstitution;

    @EntityField(name = "机构类型：yy:医院,dsfsys:第三方实验室,dsftjzx:第三方体检中心,jkzx:疾控中心,xz:血站,zs:诊所,ylhly:养老护理院,jgqt:其他")
    private String institutionalType;

    @EntityField(name = "医院类型:yy：公立医院，mbyy:民营医院")
    private String hospitalType;

    @EntityField(name = "医院等级：0:无等级,1:一级甲等,2:一级乙等,3:一级丙等,4:二级甲等,5:二级乙等,6:二级丙等,7:三级特等,8:三级甲等,9:三级乙等,10:三级丙等")
    private Integer hospitalClass;
    /**
     * 备注
     */
    @EntityField(name = "备注", stringValueTypeLength = 500, requiredMetTypes = {MethodType.UPDATE})
    private String remark;


    /**
     * 业务信息
     */

    @EntityField(name = "管控类型id")
    private Long controlId;
    @EntityField(name = "管控类型名称")
    private String controlTypeName;


    /**
     * 合作性质
     */
    @EntityField(name = "合作性质", stringValueTypeLength = 1, requiredMetTypes = {MethodType.UPDATE})
    private String cooperationMode;

    /**
     * 合作性质
     */
    @EntityField(name = "合作性质名称", stringValueTypeLength = 1, requiredMetTypes = {MethodType.UPDATE})
    private String cooperationModeName;

    @EntityField(name = "价格体系", stringValueTypeLength = 19)
    private String priceCategoryCode;

    @EntityField(name = "客户性质")
    private String customerType;

    @EntityField(name = "客户性质名称")
    private String customerTypeName;

    @EntityField(name = "结算方式")
    private String settlementModes;

    @EntityField(name = "结算方式名称")
    private String settlementModesName;

    @EntityField(name = "信用额度")
    private String creditAmount;

    @EntityField(name = "信用期限")
    private String creditDates;

    @EntityField(name = "合作起始时间")
    private String coopStartTime;

    @EntityField(name = "合作结束时间")
    private String coopEndTime;

    @EntityField(name = "交易币种")
    private String currency;
    @EntityField(name = "收款协议")
    private String paymentAgreement;
    @EntityField(name = "收款条件")
    private String paymentCondition;
    @EntityField(name = "客户性质")
    private String businessType;

    /**
     * 业务状态：0:潜在 1:合格 2：草稿
     */
    private Integer businessFlag;

    @EntityField(name = "银行信息", stringValueTypeLength = 0, requiredMetTypes = {MethodType.UPDATE})
    private List<BankDTO> bankList;
    //业务区域
    @EntityField(name = "区域集合")
    private List<SaveCustomerAreaDTO> areaList;
    //客户联系人
    @EntityField(name = "客户联系人")
    private List<CompanyLinkmanDTO> linkmanList;
    //开票信息
    @EntityField(name = "开票信息", stringValueTypeLength = 0, requiredMetTypes = {MethodType.UPDATE})
    private List<CustomerInvoiceDTO> invoiceList;
    //负责人信息
    @EntityField(name = "负责人信息")
    private List<CustomerSalesManDTO> salesManList;
    //联系人地址
    @EntityField(name = "联系地址")
    private List<CompanyShippingAddressDTO> linkAddressList;

    @EntityField(name = "企业资料信息")
    private CatalogInfoBigClientReq catalogInfoBigClientReq;
    private List<CatalogInfoClientReq> catalogInfoClientReqList;
}
