package com.yyigou.dsrp.cdc.api.supply;

import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.ddc.common.service.ServiceBase;
import com.yyigou.dsrp.cdc.api.company.vo.CompanyLinkmanVO;
import com.yyigou.dsrp.cdc.api.supply.dto.SupplierLinkmanByOrgDTO;

import java.util.List;

/**
 * 供应商联系人接口
 */
public interface SupplierLinkManApi extends ServiceBase {


    /**
     * 跨组织获取供应商联系人列表
     *
     * @param supplierCode
     * @param orgNo
     * @return
     */
    CallResult<List<CompanyLinkmanVO>> getSupplierLinkmanListByOrg(String supplierCode, String orgNo);

    /**
     * 跨组织保存供应商联系人列表
     *
     * @param params
     * @return
     */
    CallResult<CompanyLinkmanVO> saveSupplierLinkmanByOrg(SupplierLinkmanByOrgDTO params);

    /**
     * 跨组织删除联系人
     *
     * @param params
     * @param
     * @return
     */
    CallResult<CompanyLinkmanVO> editSupplierLinkmanByOrg(SupplierLinkmanByOrgDTO params);

    /**
     * 跨组织删除联系人
     *
     * @param linkmanId
     * @param
     * @return
     */
    CallResult<Boolean> deleteSupplierLinkmanByOrg(Long linkmanId);
}
