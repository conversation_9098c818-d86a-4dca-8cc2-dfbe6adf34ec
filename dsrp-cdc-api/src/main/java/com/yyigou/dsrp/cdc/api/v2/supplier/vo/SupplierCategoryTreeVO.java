package com.yyigou.dsrp.cdc.api.v2.supplier.vo;

import com.yyigou.ddc.common.service.annotation.EntityField;
import com.yyigou.ddc.common.treegrid.TreeGrid;
import lombok.Data;

import java.util.List;

@Data
public class SupplierCategoryTreeVO extends TreeGrid {
//    @EntityField(name = "分类编码") treegrid已有字段
//    private String no;

    @EntityField(name = "所属企业编号")
    private String enterpriseNo;

//    @EntityField(name = "上级分类编号") treegrid已有字段
//    private String parentNo;

    @EntityField(name = "上级节点名称")
    private String parentName;

    @EntityField(name = "助记码")
    private String mnemonicCode;

    @EntityField(name = "分类编码")
    private String categoryCode;

    @EntityField(name = "分类名称")
    private String categoryName;

    @EntityField(name = "描述")
    private String remark;

    @EntityField(name = "状态：1正常 0无效 ")
    private String status;

    @EntityField(name = "排序")
    private Integer sortNum;

    @EntityField(name = "0:未删除，1已删除")
    private Integer deleted;

    @EntityField(name = "创建人编号")
    private String createNo;

    @EntityField(name = "制单人")
    private String createName;

    @EntityField(name = "创建时间")
    private String createTime;

    @EntityField(name = "修改时间")
    private String operateNo;

    @EntityField(name = "修改人名称")
    private String operateName;

    @EntityField(name = "修改时间")
    private String operateTime;

    @EntityField(name = "ys同步标识 0:未同步 1:同步成功 2:同步失败")
    private String ysSyncFlag;

    @EntityField(name = "管理组织编号")
    private String manageOrgNo;

    @EntityField(name = "管理组织编码")
    private String manageOrgCode;

    @EntityField(name = "管理组织名称")
    private String manageOrgName;

    @EntityField(name = "层级")
    private Integer level;

    @EntityField(name = "顶级编号")
    private String topCategoryNo;

    @EntityField(name = "使用组织名称集合")
    private String useOrgNames;

    @EntityField(name = "使用组织集合")
    private List<SupplierCategoryUseOrgVO> useOrgList;
}
