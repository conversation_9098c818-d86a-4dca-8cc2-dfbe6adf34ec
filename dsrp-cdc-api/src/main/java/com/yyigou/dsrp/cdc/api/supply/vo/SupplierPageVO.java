package com.yyigou.dsrp.cdc.api.supply.vo;

import com.yyigou.ddc.common.service.annotation.EntityField;
import com.yyigou.ddc.common.service.annotation.MethodType;
import lombok.Data;

import java.util.List;

@Data
public class SupplierPageVO {

    /**
     * 客户编码
     */
    @EntityField(name = "客户编码", stringValueTypeLength = 50, requiredMetTypes = {MethodType.UPDATE})
    private String supplierNo;
    /**
     * 客户编码
     */
    @EntityField(name = "客户编码", stringValueTypeLength = 50, requiredMetTypes = {MethodType.UPDATE})
    private String supplierCode;
    /**
     * 客户名称
     */
    @EntityField(name = "客户名称", stringValueTypeLength = 128, requiredMetTypes = {MethodType.UPDATE})
    private String supplierName;
    /**
     * 客户基本分类
     */
    @EntityField(name = "客户基本分类", stringValueTypeLength = 32, requiredMetTypes = {MethodType.UPDATE})
    private String supplierCategoryNo;
    private String supplierCategoryName;
    /**
     * 助记码
     */
    @EntityField(name = "助记码", stringValueTypeLength = 100, requiredMetTypes = {MethodType.UPDATE})
    private String mnemonicCode;

    /**
     * 客户外语名称
     */
    @EntityField(name = "客户外语名称", stringValueTypeLength = 300, requiredMetTypes = {MethodType.UPDATE})
    private String supplierNameEn;


    @EntityField(name = "归属公司")
    private String ownerCompany;

    /**
     * Gsp首营状态
     */
    @EntityField(name = "Gsp首营状态 0:无需首营，1：未首营 2：已首营")
    private Integer gspStatus;


    /**
     * 公司纳税类别
     */
    @EntityField(name = "纳税类别")
    private String companyTaxCategory;


    @EntityField(name = "纳税类别")
    private List<String> enterpriseTypes;

    /**
     * 备注
     */
    @EntityField(name = "备注", stringValueTypeLength = 500, requiredMetTypes = {MethodType.UPDATE})
    private String remark;


    /**
     * 业务信息
     */

    @EntityField(name = "管控类型id")
    private Long controlId;
    @EntityField(name = "管控类型名称")
    private String controlTypeName;

    @EntityField(name = "管控状态")
    private String controlStatus;
    @EntityField(name = "管控状态名称")
    private String controlStatusName;

    /**
     * 合作性质
     */
    @EntityField(name = "合作性质", stringValueTypeLength = 1, requiredMetTypes = {MethodType.UPDATE})
    private String cooperationMode;

    /**
     * 合作性质
     */
    @EntityField(name = "合作性质名称", stringValueTypeLength = 1, requiredMetTypes = {MethodType.UPDATE})
    private String cooperationModeName;

    @EntityField(name = "价格体系", stringValueTypeLength = 19)
    private String priceCategoryCode;

    @EntityField(name = "客户性质")
    private String supplierType;

    @EntityField(name = "客户性质名称")
    private String supplierTypeName;

    @EntityField(name = "结算方式")
    private String settlementModes;

    @EntityField(name = "结算方式名称")
    private String settlementModesName;

    @EntityField(name = "信用额度")
    private String creditAmount;

    @EntityField(name = "合作起始时间")
    private String coopStartTime;

    @EntityField(name = "合作结束时间")
    private String coopEndTime;


    /**
     * 公司纳税类别
     */
    @EntityField(name = "纳税类别")
    private String taxCategory;
    private String taxCategoryName;

    @EntityField(name = "收款协议")
    private String paymentAgreement;
    @EntityField(name = "收款条件")
    private String paymentCondition;
    @EntityField(name = "交易币种")
    private String currency;
    @EntityField(name = "交易币种")
    private String currencyName;
    @EntityField(name = "国家/地区")
    private String country;
    @EntityField(name = "企业注册地域：1-境内，2-境外")
    private Integer factoryType;
    @EntityField(name = "企业注册地域：1-境内，2-境外")
    private String factoryTypeName;
    @EntityField(name = "经济类型")
    private String economicType;
    private String economicTypeName;
    /**
     * 统一社会信用代码。公立医院的事业单位法人证号
     */
    @EntityField(name = "统一社会信用代码")
    private String unifiedSocialCode;
    @EntityField(name = "企业编号")
    private String companyNo;
    @EntityField(name = "企业名称")
    private String companyName;
    @EntityField(name = "是否关联企业：1-是，0-否")
    private Integer isAssociatedEnterprise;
    @EntityField(name = "关联组织编号,关联企业时选择")
    private String associatedOrgNo;
    @EntityField(name = "关联组织编码,关联企业时选择")
    private String associatedOrgCode;
    @EntityField(name = "关联组织名称,关联企业时选择")
    private String associatedOrgName;

    @EntityField(name = "是否散户")
    private Integer retailInvestors;
    @EntityField(name = "是否散户名称")
    private String retailInvestorsName;

    @EntityField(name = "交易类型(客户类型)")
    private String transactionType;
    @EntityField(name = "交易类型名称(客户类型)")
    private String transactionTypeName;


    /**
     * 付款条件
     */
    private String paymentTerm;

    /**
     * 付款条件名称
     */
    private String paymentTermName;
    /**
     * 付款协议id(本地的id)
     */
    @EntityField(name = "付款协议id")
    private Long paymentAgreementId;

    /**
     * 付款协议id(ys的id)
     */
    @EntityField(name = "付款协议id")
    private String paymentAgreementYsId;

    /**
     * 付款协议编码
     */
    @EntityField(name = "付款协议编码")
    private String paymentAgreementCode;

    /**
     * 付款协议名称
     */
    private String paymentAgreementName;

    /**
     * 账期天数
     */
    private Integer periodDays;
    private Integer isGspControl;
    private String isGspControlName;


    /**
     * GSP的审核状态 0:未创建，1：待审批 2：审批通过
     * 3：审核失败;9-审批撤回
     */
    private String gspAuditStatus;


    /**
     * GSP的审核状态 0:未创建，1：待审批 2：审批通过
     * 3：审核失败;9-审批撤回
     */
    private String gspAuditStatusName;

    /**
     * 最后申请流程：ZRSQ准入申请、BGSQ档案变更、TTSQ淘汰申请、HMDSQ黑名单申请、GSP首营
     */
    private String applyformBillType;
    /**
     * 是否发起准入
     */
    @EntityField(name = "申请单状态")
    private String applyformStatus;

    @EntityField(name = "联系人")
    private String linkMan;
    @EntityField(name = "联系电话")
    private String linkPhone;
    /**
     * 是否同步wms
     */
    @EntityField(name = " 是否同步wms", stringValueTypeLength = 0, requiredMetTypes = {MethodType.UPDATE})
    private Integer isSyncWms;
    @EntityField(name = " 是否同步wms", stringValueTypeLength = 0, requiredMetTypes = {MethodType.UPDATE})
    private String isSyncWmsName;
    @EntityField(name = "wms推送结果", stringValueTypeLength = 0, requiredMetTypes = {MethodType.UPDATE})
    private String wmsPushResult;

    /**
     * ys推送结果
     * ys同步标识 0:未同步 1:同步成功 2:同步失败
     */
    private String ysSyncFlag;
    private String ysSyncFlagName;
    private String ysPushResult;

    /**
     * scs同步状态
     * 同步scs状态：0：未同步 1：同步成功 2：同步失败 3：已协同
     */
    private Integer isSyncScs;
    private String isSyncScsName;
    private String scsPushResult;

    @EntityField(name = "版本")
    private Integer version;
}
