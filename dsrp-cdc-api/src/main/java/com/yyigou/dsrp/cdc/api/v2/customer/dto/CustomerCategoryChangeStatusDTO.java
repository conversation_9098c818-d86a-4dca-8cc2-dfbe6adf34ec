package com.yyigou.dsrp.cdc.api.v2.customer.dto;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;


@Data
public class CustomerCategoryChangeStatusDTO implements Serializable {
    @EntityField(name = "主键")
    private String no;

    @EntityField(name = "管理组织编号")
    private String manageOrgNo;

    @EntityField(name = "状态")
    private String status;
}