package com.yyigou.dsrp.cdc.api.supply.dto;

import com.yyigou.ddc.common.service.annotation.EntityField;
import com.yyigou.ddc.common.service.annotation.MethodType;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class SupplierSalesManQueryDTO implements Serializable {
    /**
     * 供应商编号
     */
    @EntityField(name = "供应商编号", stringValueTypeLength = 32, requiredMetTypes = {MethodType.UPDATE})
    private List<String> supplierNoList;
    /**
     * 部门编号
     */
    @EntityField(name = "部门编号", stringValueTypeLength = 32, requiredMetTypes = {MethodType.UPDATE})
    private List<String> deptNoList;


    /**
     * 默认业务员  2:是 1：默认  0：否
     */
    @EntityField(name = "默认业务员  2:是 1：默认  0：否 ", stringValueTypeLength = 255, requiredMetTypes = {MethodType.UPDATE})
    private List<Integer> isDefaultList;
    /**
     * 默认订单专员  2:是 1：默认  0：否
     */
    @EntityField(name = "默认订单专员  2:是 1：默认  0：否 ", stringValueTypeLength = 255, requiredMetTypes = {MethodType.UPDATE})
    private List<Integer> orderSpecialistList;
}
