package com.yyigou.dsrp.cdc.api.v2.customer.openapi;

import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.ddc.common.service.ServiceBase;
import com.yyigou.dsrp.cdc.api.customer.dto.CustomerExternalSaveDTO;
import com.yyigou.dsrp.cdc.api.customer.vo.CustomerExternalSaveVO;

import java.util.List;

/**
 * @Classname CustomerForDAOpenAPI
 * @description: 迪安客户相关OPEN-API接口
 * @author: baoww
 * @date: 2025/3/18 21:59
 */
public interface CustomerForDAOpenAPI extends ServiceBase {

    /**
     * 迪安客户批量保存接口
     *
     * @param params
     * @return
     */
    CallResult<List<CustomerExternalSaveVO>> batchSave(List<CustomerExternalSaveDTO> params);
    
}
