package com.yyigou.dsrp.cdc.api.v2.company.vo;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;

@Data
public class CompanyFormalPageVO implements Serializable {
    // -------------企业信息-------------
    @EntityField(name = "企业编号")
    private String companyNo;

    @EntityField(name = "企业名称")
    private String companyName;

    @EntityField(name = "统一社会信用代码。公立医院的事业单位法人证号")
    private String unifiedSocialCode;

    @EntityField(name = "企业注册地域：1-境内，2-境外")
    private Integer factoryType;

    @EntityField(name = "企业注册地域：1-境内，2-境外")
    private String factoryTypeName;

    @EntityField(name = "国家/地区")
    private String country;

    @EntityField(name = "纳税类别")
    private String taxCategory;

    @EntityField(name = "纳税类别名称")
    private String taxCategoryName;

    @EntityField(name = "经济类型")
    private String economicType;

    @EntityField(name = "经济类型名称")
    private String economicTypeName;

//    @EntityField(name = "是否关联企业：1-是，0-否")
//    private Integer isAssociatedEnterprise;
//
//    private String isAssociatedEnterpriseName;
//
//    @EntityField(name = "关联组织编号,关联企业时选择")
//    private String associatedOrgNo;
//
//    @EntityField(name = "关联组织编码,关联企业时选择")
//    private String associatedOrgCode;
//
//    @EntityField(name = "关联组织名称,关联企业时选择")
//    private String associatedOrgName;

    private String institutionalType;

    private String institutionalTypeName;

    private String hospitalType;

    private String hospitalTypeName;

    private Integer hospitalClass;

    private String registedCapital;

    private String establishmentDate;

    private String regionCode;

    private String regionName;

    private String address;

    private String legalPerson;

    private String email;

    private String webSite;

    private String fax;

    private Integer isListed;

    private String businessStartTime;

    private String businessEndTime;

    private Integer businessLongTerm;

    private String paidCapital;

    private String industry;

    private String businessRegistNo;

    private String organizationNo;

    private String taxpayerNo;

    private String taxpayerQualification;

    private String registrationAuthority;

    private String approvalDate;

    private String lastName;

    private String insuredNumber;

    private String companyBusinessType;

    private String businessStatus;

    private String manageScope;

    private String partnershipText;
}
