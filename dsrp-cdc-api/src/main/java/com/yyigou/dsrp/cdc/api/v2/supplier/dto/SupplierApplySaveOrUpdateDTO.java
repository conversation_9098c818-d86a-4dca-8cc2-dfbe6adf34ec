package com.yyigou.dsrp.cdc.api.v2.supplier.dto;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;

@Data
public class SupplierApplySaveOrUpdateDTO implements Serializable {
    @EntityField(name = "申请单号")
    private String applyInstanceNo;

    @EntityField(name = "申请组织编码")
    private String applyOrgNo;

    @EntityField(name = "申请组织名称")
    private String applyOrgName;

    @EntityField(name = "使用组织编号")
    private String useOrgNo;

    @EntityField(name = "使用组织名称")
    private String useOrgName;

    @EntityField(name = "管理组织编号")
    private String manageOrgNo;

    @EntityField(name = "管理组织名称")
    private String manageOrgName;

    @EntityField(name = "企业编号")
    private String companyNo;

    @EntityField(name = "企业编码")
    private String companyCode;

    @EntityField(name = "企业名称")
    private String companyName;

    @EntityField(name = "供应商编码")
    private String supplierCode;

    @EntityField(name = "供应商名称")
    private String supplierName;

    @EntityField(name = "申请类型 2-新增申请 3-变更申请")
    private Integer applyType;

    @EntityField(name = "申请类型 2-新增申请 3-变更申请")
    private String applyTypeName;

    @EntityField(name = "申请原因")
    private String applyReason;

    @EntityField(name = "申请说明")
    private String applyDesc;

    @EntityField(name = "申请人编号")
    private String applyNo;

    @EntityField(name = "申请人名称")
    private String applyName;

    @EntityField(name = "申请时间")
    private String applyTime;

    @EntityField(name = "申请时填写的详情JSON")
    private String applyContent;

    @EntityField(name = "审核状态：0-草稿 1-待审核")
    private Integer auditStatus;
}
