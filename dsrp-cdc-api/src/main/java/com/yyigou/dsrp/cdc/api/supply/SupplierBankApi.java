package com.yyigou.dsrp.cdc.api.supply;

import com.yyigou.ddc.common.page.PageDto;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.ddc.common.service.ServiceBase;
import com.yyigou.dsrp.cdc.api.supply.dto.SupplierBankQueryDTO;
import com.yyigou.dsrp.cdc.api.supply.vo.SupplierBankPageVO;


/**
 * 供应商银行信息
 */
public interface SupplierBankApi extends ServiceBase {


    /**
     * 分页查询供应商的银行信息
     * @param params
     * @param pageDto
     * @return
     */
    CallResult<PageVo<SupplierBankPageVO>> findBankPage(SupplierBankQueryDTO params, PageDto pageDto);

}
