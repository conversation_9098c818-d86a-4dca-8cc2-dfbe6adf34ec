package com.yyigou.dsrp.cdc.api.customer.vo;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.AllArgsConstructor;
import lombok.Data;

@Data
public class CustomerExternalSaveVO {
    public CustomerExternalSaveVO() {
    }

    public CustomerExternalSaveVO(String customerCode, Boolean success, String message) {
        this.customerCode = customerCode;
        this.success = success;
        this.message = message;
    }

    @EntityField(name = "客户编码", stringValueTypeLength = 400)
    private String customerCode;
    @EntityField(name = "是否成功", stringValueTypeLength = 400)
    private Boolean success;
    @EntityField(name = "消息", stringValueTypeLength = 400)
    private String message;
}
