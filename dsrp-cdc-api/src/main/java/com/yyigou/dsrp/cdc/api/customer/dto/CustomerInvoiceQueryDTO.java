package com.yyigou.dsrp.cdc.api.customer.dto;

import com.yyigou.ddc.common.service.annotation.EntityField;
import com.yyigou.ddc.common.service.annotation.MethodType;
import lombok.Data;

import java.io.Serializable;

@Data
public class CustomerInvoiceQueryDTO implements Serializable {

    @EntityField(name = "组织编号", stringValueTypeLength = 50 ,requiredMetTypes = {MethodType.QUERY})
    private String orgNo;


    @EntityField(name = "客户编码", stringValueTypeLength = 50 ,requiredMetTypes = {MethodType.QUERY})
    private String customerCode;

}
