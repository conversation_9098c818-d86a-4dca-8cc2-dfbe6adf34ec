package com.yyigou.dsrp.cdc.api.v2.customer.dto;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;

@Data
public class CustomerApplyApproveDTO implements Serializable {
    @EntityField(name = "申请单号")
    private String applyInstanceNo;

    @EntityField(name = "审核状态：0-草稿 1-待审核 2-审核通过 3-审核拒绝")
    private Integer auditStatus;

    @EntityField(name = "审核意见")
    private String auditRemark;
}
