package com.yyigou.dsrp.cdc.api.v2.supplier.dto;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;


@Data
public class SupplierCategoryQueryFlagListPageDTO implements Serializable {
    @EntityField(name = "客户分类编码名称模糊搜索")
    private String keywords;

    @EntityField(name = "状态")
    private String status;

    @EntityField(name = "分类编号列表")
    private List<String> supplierCategoryNoList;
}