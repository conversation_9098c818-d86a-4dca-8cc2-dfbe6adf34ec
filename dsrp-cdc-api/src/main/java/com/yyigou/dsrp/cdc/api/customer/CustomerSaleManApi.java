package com.yyigou.dsrp.cdc.api.customer;

import com.yyigou.ddc.common.page.PageDto;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.ddc.common.service.ServiceBase;
import com.yyigou.ddc.services.dsrp.bdc.vo.CustomerSalesManVo;
import com.yyigou.dsrp.cdc.api.customer.dto.CustomerSalesManQueryBySpecifyDTO;
import com.yyigou.dsrp.cdc.api.customer.dto.CustomerSalesManQueryDTO;
import com.yyigou.dsrp.cdc.api.customer.vo.CustomerSalesManVO;

import java.util.List;

/**
 * 客户销售人接口
 */
public interface CustomerSaleManApi extends ServiceBase {


    /**
     * 跨组织获取客户销售人列表
     *
     * @param customerCode
     * @param orgNo
     * @return
     */
    CallResult<List<CustomerSalesManVO>> getCustomerSaleManListByOrg(String customerCode, String orgNo);

    /**
     * 获取客户销售人列表
     *
     * @param params
     * @return
     */
    CallResult<List<CustomerSalesManVo>> findList(CustomerSalesManQueryDTO params);

    /**
     * 获取客户销售人列表
     *
     * @param params
     * @return
     */
    CallResult<PageVo<CustomerSalesManVo>> findPage(CustomerSalesManQueryDTO params, PageDto pageDto);


    /**
     * 获取客户销售人列表
     *
     * @param params
     * @return
     */
    CallResult<List<CustomerSalesManVo>> findListBySpecifyOrg(CustomerSalesManQueryBySpecifyDTO params);

    /**
     * 获取客户销售人列表
     *
     * @param params
     * @return
     */
    CallResult<PageVo<CustomerSalesManVo>> findPageBySpecifyOrg(CustomerSalesManQueryBySpecifyDTO params, PageDto pageDto);
}
