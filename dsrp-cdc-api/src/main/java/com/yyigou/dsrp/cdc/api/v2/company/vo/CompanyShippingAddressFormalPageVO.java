package com.yyigou.dsrp.cdc.api.v2.company.vo;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class CompanyShippingAddressFormalPageVO implements Serializable {
    /**
     * 主键
     */
    @EntityField(name = "主键", stringValueTypeLength = 20)
    private Long id;
    /**
     * 企业编号
     */
    @EntityField(name = "企业编号", stringValueTypeLength = 32)
    private String enterpriseNo;

    /**
     * 企业编号
     */
    @EntityField(name = "客户主/供应商编号", stringValueTypeLength = 128)
    private String companyNo;
    /**
     * 企业编号
     */
    @EntityField(name = "客户编号")
    private String customerCode;
    /**
     * 企业编号
     */
    @EntityField(name = "客户主/供应商名称", stringValueTypeLength = 128)
    private String customerName;

    /**
     * 收货人
     */
    @EntityField(name = "收货人", stringValueTypeLength = 50)
    private String receiveUser;
    /**
     * 联系电话
     */
    @EntityField(name = "联系电话", stringValueTypeLength = 100)
    private String receivePhone;
    /**
     * 行政地区编码
     */
    @EntityField(name = "行政地区编码", stringValueTypeLength = 20)
    private String regionCode;
    /**
     * 行政地区名称
     */
    @EntityField(name = "行政地区名称", stringValueTypeLength = 100)
    private String regionName;
    /**
     * 收货详细地址
     */
    @EntityField(name = "收货详细地址", stringValueTypeLength = 300)
    private String receiveAddr;
    /**
     * 电子邮箱
     */
    @EntityField(name = "电子邮箱", stringValueTypeLength = 100)
    private String email;
    /**
     * 收货地址状态:1默认 0非默认
     */
    @EntityField(name = "收货地址状态:1默认 0非默认", stringValueTypeLength = 4)
    private Byte isDefault;
    /**
     * 状态:1正常 0删除
     */
    @EntityField(name = "状态:1正常 0删除", stringValueTypeLength = 1)
    private Integer status;
    /**
     * 地址类型：1联系地址 2：收票地址 3:仓库地址
     */
    @EntityField(name = "地址类型：1联系地址 2：收票地址 3:仓库地址", stringValueTypeLength = 1)
    private String addressType;
    /**
     * 创建人编号
     */
    @EntityField(name = "创建人编号", stringValueTypeLength = 32)
    private String createNo;
    /**
     * 创建时间(系统时间)
     */
    @EntityField(name = "创建时间(系统时间)", stringValueTypeLength = 19)
    private String createTime;
    /**
     * 操作人编号
     */
    @EntityField(name = "操作人编号", stringValueTypeLength = 32)
    private String operateNo;
    /**
     * 操作人名称
     */
    @EntityField(name = "操作人名称", stringValueTypeLength = 100)
    private String operateName;
    /**
     * 最后更新时间
     */
    @EntityField(name = "最后更新时间", stringValueTypeLength = 19)
    private String operateTime;
    /**
     * 最后操作时间戳
     */
    @EntityField(name = "最后操作时间戳", stringValueTypeLength = 0)
    private Date opTimestamp;
    /**
     * 数据记录版本
     */
    @EntityField(name = "数据记录版本", stringValueTypeLength = 11)
    private Integer opRevsion;
    /**
     * 数据操作类型 I:新增 U：更新 D:删除
     */
    @EntityField(name = "数据操作类型 I:新增 U：更新 D:删除", stringValueTypeLength = 1)
    private String opType;

    @EntityField(name = "联系地址类型:is_sale-销售  is_supply-采购")
    private String linkaddType;

    /**
     * 通昶同步状态 3.未同步(暂时不能同步) 0.待同步(客商以同步成功)  1.同步成功  2.同步失败
     */
    @EntityField(name = "通昶同步状态 3.未同步(暂时不能同步) 0.待同步(客商以同步成功)  1.同步成功  2.同步失败")
    private Integer tcSyncFlag;
    /**
     * 通昶推送结果(失败原因)
     */
    @EntityField(name = "通昶推送结果(失败原因)")
    private String tcSyncResult;

    @EntityField(name = "省份编码")
    private String province;
    @EntityField(name = "城市/州/直辖市编码")
    private String city;
    @EntityField(name = "区/县编码")
    private String area;
}