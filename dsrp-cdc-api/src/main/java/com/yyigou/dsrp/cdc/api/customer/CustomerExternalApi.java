package com.yyigou.dsrp.cdc.api.customer;

import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.ddc.common.service.ServiceBase;
import com.yyigou.dsrp.cdc.api.customer.dto.CustomerExternalAssignDTO;
import com.yyigou.dsrp.cdc.api.customer.dto.CustomerExternalSaveDTO;
import com.yyigou.dsrp.cdc.api.customer.vo.CustomerExternalSaveVO;

import java.util.List;

/**
 * 外部客户调用接口 保存客户档案
 */
public interface CustomerExternalApi extends ServiceBase {


    /**
     * 保存客户档案(迪安)
     *
     * @param params
     * @return
     */
    CallResult<List<CustomerExternalSaveVO>> saveCustomer(List<CustomerExternalSaveDTO> params);


    CallResult<Boolean> customerAssign(CustomerExternalAssignDTO params);

}
