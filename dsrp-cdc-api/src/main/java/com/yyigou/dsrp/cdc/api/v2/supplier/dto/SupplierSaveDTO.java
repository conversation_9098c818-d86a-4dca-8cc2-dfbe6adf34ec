package com.yyigou.dsrp.cdc.api.v2.supplier.dto;

import com.yyigou.ddc.common.service.annotation.EntityField;
import com.yyigou.dsrp.cdc.api.v2.company.dto.CompanyBankDTO;
import com.yyigou.dsrp.cdc.api.v2.company.dto.CompanyLinkmanDTO;
import com.yyigou.dsrp.cdc.api.v2.company.dto.CompanyShippingAddressDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class SupplierSaveDTO implements Serializable {
    @EntityField(name = "管理组织编号")
    private String manageOrgNo;

    @EntityField(name = "供应商编号")
    private String supplierNo;

    @EntityField(name = "供应商编码")
    private String supplierCode;

    @EntityField(name = "供应商名称")
    private String supplierName;

    @EntityField(name = "供应商英文名称")
    private String supplierNameEn;

    @EntityField(name = "助记码")
    private String mnemonicCode;

    @EntityField(name = "供应商类型")
    private String transactionType;

    @EntityField(name = "供应商分类")
    private String supplierCategoryNo;

    @EntityField(name = "企业编号")
    private String companyNo;

    @EntityField(name = "企业名称")
    private String companyName;

    @EntityField(name = "统一社会信用代码。公立医院的事业单位法人证号")
    private String unifiedSocialCode;

    @EntityField(name = "企业注册地域：1-境内，2-境外")
    private Integer factoryType;

    @EntityField(name = "国家/地区")
    private String country;

    @EntityField(name = "纳税类别")
    private String taxCategory;

    @EntityField(name = "经济类型")
    private String economicType;

    @EntityField(name = "是否散户")
    private Integer retailInvestors;

    @EntityField(name = "是否关联企业：1-是，0-否")
    private Integer isAssociatedEnterprise;

    @EntityField(name = "关联组织编号,关联企业时选择")
    private String associatedOrgNo;

    @EntityField(name = "关联组织编码,关联企业时选择")
    private String associatedOrgCode;

    @EntityField(name = "关联组织名称,关联企业时选择")
    private String associatedOrgName;

    @EntityField(name = "备注")
    private String remark;

    @EntityField(name = "供应商状态：1-正式，2-草稿")
    private Integer businessFlag;

    //----------------以下为业务信息----------------

    @EntityField(name = "合作性质")
    private String cooperationMode;

    @EntityField(name = "合作性质名称")
    private String cooperationModeName;

    @EntityField(name = "交易币种")
    private String currencyId;

    @EntityField(name = "结算方式")
    private String settlementModes;

    @EntityField(name = "结算方式名称")
    private String settlementModesName;

    @EntityField(name = "付款协议id")
    private Long paymentAgreementId;

    @EntityField(name = "付款协议id(ys的id)")
    private String paymentAgreementYsId;

    @EntityField(name = "付款协议编码")
    private String paymentAgreementCode;

    @EntityField(name = "付款协议名称")
    private String paymentAgreementName;

    @EntityField(name = "付款条件")
    private String paymentTerm;

    @EntityField(name = "信用额度")
    private String creditAmount;

    @EntityField(name = "信用天数")
    private Integer periodDays;

    @EntityField(name = "合作起始时间")
    private String coopStartTime;

    @EntityField(name = "合作结束时间")
    private String coopEndTime;

    @EntityField(name = "业务归属")
    private String ownerCompany;

    //----------------以下为关联信息----------------

    @EntityField(name = "供应商联系人")
    private List<CompanyLinkmanDTO> linkmanList;

    @EntityField(name = "联系地址")
    private List<CompanyShippingAddressDTO> linkAddressList;

    @EntityField(name = "银行信息")
    private List<CompanyBankDTO> bankList;

    @EntityField(name = "负责人信息")
    private List<SupplierOrderManDTO> supplierManList;
}
