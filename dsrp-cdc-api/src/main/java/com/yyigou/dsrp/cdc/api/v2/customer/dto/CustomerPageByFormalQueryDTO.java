package com.yyigou.dsrp.cdc.api.v2.customer.dto;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class CustomerPageByFormalQueryDTO implements Serializable {
    private String keywords;

    @EntityField(name = "客户档案关键字")
    private String customerKeywords;

    @EntityField(name = "")
    private String customerExactKeywords;

    @EntityField(name = "客户档案关键字")
    private String customerCodeKeywords;

    @EntityField(name = "")
    private String customerNameKeywords;

    @EntityField(name = "")
    private String customerCategoryNo;

    @EntityField(name = "")
    private String enterpriseType;

    @EntityField(name = "")
    private String controlStatus;

    @EntityField(name = "")
    private List<Integer> controlStatusList;

    @EntityField(name = "")
    private List<String> noCustomerNos;

    @EntityField(name = "")
    private List<Integer> businessFlags;
}
