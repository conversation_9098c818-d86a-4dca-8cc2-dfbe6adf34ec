package com.yyigou.dsrp.cdc.api.v2.customer.dto;

import com.yyigou.ddc.common.service.annotation.EntityField;
import com.yyigou.ddc.services.ddc.task.vo.excel.UapBaseImportDTO;
import lombok.Data;

import java.io.Serializable;

@Data
public class CustomerInvoiceImportDTO extends UapBaseImportDTO implements Serializable {
    @EntityField(name = "客户编码")
    private String customerCode;

    @EntityField(name = "开票类型")
    private Integer type;

    @EntityField(name = "开票类型")
    private String typeName;

    @EntityField(name = "开票名称")
    private String invoiceTitle;

    @EntityField(name = "纳税人识别号")
    private String taxNo;

    @EntityField(name = "地址")
    private String address;

    @EntityField(name = "电话")
    private String phone;

    @EntityField(name = "开户行")
    private String bankDeposit;

    @EntityField(name = "开户账号")
    private String bankAccount;

    @EntityField(name = "收票手机号")
    private String invoicePhone;

    @EntityField(name = "收票邮箱")
    private String email;

    @EntityField(name = "客户开票要求")
    private String requirement;

    @EntityField(name = "是否默认发票信息")
    private Integer isDefault;

    @EntityField(name = "是否默认发票信息")
    private String isDefaultName;

}
