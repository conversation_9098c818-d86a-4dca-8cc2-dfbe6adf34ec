package com.yyigou.dsrp.cdc.api.customer.dto;

import com.yyigou.ddc.common.service.annotation.EntityField;
import com.yyigou.ddc.common.service.annotation.MethodType;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class QueryPageCustomerDTO implements Serializable {
    /**
     * 管控状态 1：启用 2：停用
     */
    @EntityField(name = "管控状态 1：启用", stringValueTypeLength = 0, requiredMetTypes = {MethodType.UPDATE})
    private String controlStatus;
    /**
     * 管控状态 1：启用 2：停用 3：冻结
     */
    @EntityField(name = "管控状态 1：启用 2：停用", stringValueTypeLength = 0, requiredMetTypes = {MethodType.UPDATE})
    private List<String> controlStatusList;

    /**
     * 业务状态：0:潜在 1:合格 2：草稿
     */
    @EntityField(name = "业务状态：0:潜在 1:合格 2：草稿", stringValueTypeLength = 1, requiredMetTypes = {MethodType.UPDATE})
    private Integer businessFlag;

    @EntityField(name = "业务状态合集", stringValueTypeLength = 32, requiredMetTypes = {MethodType.UPDATE})
    private List<Integer> businessFlags;
    /**
     * 供应商主键List
     */
    @EntityField(name = "客户主键List", stringValueTypeLength = 32, requiredMetTypes = {MethodType.UPDATE})
    private List<String> customerNos;
    /**
     * 供应商主键List
     */
    @EntityField(name = "客户主键List", stringValueTypeLength = 32, requiredMetTypes = {MethodType.UPDATE})
    private List<String> noCustomerNos;

    @EntityField(name = "关键字查询 客户no 编码 名称 助记码", stringValueTypeLength = 32, requiredMetTypes = {MethodType.UPDATE})
    private String customerExactKeywords;

    @EntityField(name = "客户编码/客户名称/英文名/助记码查询")
    private String customerKeywords;

    /**
     * 合作性质
     */
    @EntityField(name = "多个合作性质", stringValueTypeLength = 1, requiredMetTypes = {MethodType.UPDATE})
    private List<String> cooperationModes;

    @EntityField(name = "价格分类编码集合")
    private List<String> priceCategoryCodeList;

    /**
     * 客户基本分类
     */
    @EntityField(name = "多个客户基本分类", stringValueTypeLength = 32, requiredMetTypes = {MethodType.UPDATE})
    private List<String> customerCategoryNos;

    @EntityField(name = "归属公司模糊查询")
    private String ownerCompanyKeywords;

    private List<Long> controlIdList;

    private String unifiedSocialCodeKeywords;

    @EntityField(name = "业务类别精确查询")
    private List<String> businessTypeList;

    private String linkManAndPhoneKeywords;
    /**
     * 客户编码
     */
    @EntityField(name = "客户编码", stringValueTypeLength = 50, requiredMetTypes = {MethodType.UPDATE})
    private String customerCode;
    /**
     * 是否可以发起Gsp首营
     */
    @EntityField(name = "是否可以发起Gsp首营", stringValueTypeLength = 0, requiredMetTypes = {MethodType.UPDATE})
    private Integer canStartGsp;

    @EntityField(name = "前端组件临时模糊查询")
    private String keywords;

    /**
     * 目前是子租户模式，等多组织的时候，需要改成assignOrgNoList
     */
    @EntityField(name = "分派组织租户编号")
    private List<String> assignEnterprsieNoList;

    @EntityField(name = "客户编码List")
    private List<String> excludeCustomerCodeList;

    @EntityField(name = "客户编码List")
    private List<String> customerCodeList;

    /**
     * 客户分组
     * */
    private String priceCategoryCode;

}
