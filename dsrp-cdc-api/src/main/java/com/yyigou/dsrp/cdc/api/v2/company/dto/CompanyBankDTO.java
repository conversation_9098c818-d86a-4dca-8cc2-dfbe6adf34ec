package com.yyigou.dsrp.cdc.api.v2.company.dto;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

@Data
public class CompanyBankDTO {
    @EntityField(name = "id")
    private Long id;
    @EntityField(name = "银行编码")
    private String bankCode;
    @EntityField(name = "银行类型")
    private String bankType;
    @EntityField(name = "开户银行")
    private String openBank;
    @EntityField(name = "户名")
    private String accountName;
    @EntityField(name = "账号")
    private String accountNo;
    @EntityField(name = "账号类型")
    private Integer accountType;
    @EntityField(name = "联系人")
    private String linkPerson;
    @EntityField(name = "联系电话")
    private String linkPhone;
    @EntityField(name = "状态")
    private Integer status;
    @EntityField(name = "是否默认")
    private Integer isDefault;
    @EntityField(name = "币种")
    private String currencyId;

}
