package com.yyigou.dsrp.cdc.api.customer;

import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.ddc.common.service.ServiceBase;
import com.yyigou.dsrp.cdc.api.customer.vo.CustomerCategoryTree;

import java.util.List;


/**
 * 客户分类
 */
public interface CustomerCategoryApi extends ServiceBase {


    CallResult<List<CustomerCategoryTree>> queryTree();


    CallResult<List<CustomerCategoryTree>> queryGroupTree();


    CallResult<List<CustomerCategoryTree>> queryOrgTree(String orgNo);

}
