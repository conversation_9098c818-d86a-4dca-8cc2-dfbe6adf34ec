package com.yyigou.dsrp.cdc.api.supply.dto;

import com.yyigou.ddc.common.service.annotation.EntityField;
import com.yyigou.ddc.common.service.annotation.MethodType;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class SupplierLinkAddressQueryDTO implements Serializable {
    @EntityField(name = "供应商编码", stringValueTypeLength = 50, requiredMetTypes = {MethodType.UPDATE})
    private String supplierCode;

    @EntityField(name = "地址类型：1联系地址 2：收票地址 3:仓库地址 4：收货地址")
    private List<String> addressTypeList;

    private String addressType;
}
