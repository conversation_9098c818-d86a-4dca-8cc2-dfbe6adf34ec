package com.yyigou.dsrp.cdc.api.v2.supplier.vo;

import com.yyigou.ddc.common.service.annotation.EntityField;
import com.yyigou.dsrp.cdc.api.v2.company.vo.*;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class SupplierVO implements Serializable {
    @EntityField(name = "管理组织编号")
    private String manageOrgNo;

    @EntityField(name = "管理组织名称")
    private String manageOrgName;

    @EntityField(name = "供应商编号")
    private String supplierNo;

    @EntityField(name = "供应商编码")
    private String supplierCode;

    @EntityField(name = "供应商名称")
    private String supplierName;

    @EntityField(name = "供应商英文名称")
    private String supplierNameEn;

    @EntityField(name = "助记码")
    private String mnemonicCode;

    @EntityField(name = "是否关联企业：1-是，0-否")
    private Integer isAssociatedEnterprise;

    @EntityField(name = "是否关联企业：1-是，0-否")
    private String isAssociatedEnterpriseName;

    @EntityField(name = "关联组织编号")
    private String associatedOrgNo;

    @EntityField(name = "关联组织编码")
    private String associatedOrgCode;

    @EntityField(name = "关联组织名称")
    private String associatedOrgName;

    @EntityField(name = "供应商交易类型")
    private String transactionType;

    @EntityField(name = "供应商类型")
    private String transactionTypeName;

    @EntityField(name = "供应商分类")
    private String supplierCategoryNo;

    @EntityField(name = "供应商分类名称")
    private String supplierCategoryName;

    @EntityField(name = "散户")
    private Integer retailInvestors;

    @EntityField(name = "是否散户名称")
    private String retailInvestorsName;

    @EntityField(name = "业务状态：1:合格 2：草稿")
    private Integer businessFlag;

    @EntityField(name = "业务状态：1:合格 2：草稿")
    private String businessFlagName;

    @EntityField(name = "备注")
    private String remark;

    // ----------------企业信息----------------
    @EntityField(name = "企业编号")
    private String companyNo;

    @EntityField(name = "企业名称")
    private String companyName;

    @EntityField(name = "统一社会信用代码。公立医院的事业单位法人证号")
    private String unifiedSocialCode;

    @EntityField(name = "企业注册地域：1-境内，2-境外")
    private Integer factoryType;

    @EntityField(name = "企业注册地域：1-境内，2-境外")
    private String factoryTypeName;

    @EntityField(name = "国家/地区")
    private String countryRegionId;

    @EntityField(name = "国家/地区")
    private String countryRegionName;

    @EntityField(name = "纳税类别")
    private String taxCategory;

    @EntityField(name = "税收分类")
    private String taxCategoryName;

    @EntityField(name = "经济类型")
    private String economicType;

    @EntityField(name = "经济类型")
    private String economicTypeName;

    @EntityField(name = "企业注册区域")
    private String regionFullName;

    @EntityField(name = "企业注册地址")
    private String address;

    @EntityField(name = "是否医疗机构")
    private Integer isMedicalInstitution;

    @EntityField(name = "是否医疗机构名称")
    private String isMedicalInstitutionName;

    @EntityField(name = "医疗机构类型")
    private String institutionalType;

    @EntityField(name = "医疗机构类型名称")
    private String institutionalTypeName;

    @EntityField(name = "医院性质")
    private String hospitalType;

    @EntityField(name = "医院性质名称")
    private String hospitalTypeName;

    @EntityField(name = "医院等级")
    private Integer hospitalClass;

    @EntityField(name = "医院等级名称")
    private String hospitalClassName;

    @EntityField(name = "银行信息")
    private List<CompanyBankVO> bankList;

    @EntityField(name = "企业证书信息")
    private List<CompanyCertVO> companyCertList;

    // ----------------业务信息----------------
    @EntityField(name = "使用组织编号")
    private String useOrgNo;

    @EntityField(name = "使用组织名称")
    private String useOrgName;

    @EntityField(name = "合作性质")
    private String cooperationMode;

    @EntityField(name = "合作性质名称")
    private String cooperationModeName;

    @EntityField(name = "币种id")
    private String currencyId;

    @EntityField(name = "币种名称")
    private String currencyName;

    @EntityField(name = "结算方式")
    private String settlementModes;

    @EntityField(name = "结算方式名称")
    private String settlementModesName;

    @EntityField(name = "付款协议id")
    private Long paymentAgreementId;

    @EntityField(name = "付款协议ys的id")
    private String paymentAgreementYsId;

    @EntityField(name = "付款协议编码")
    private String paymentAgreementCode;

    @EntityField(name = "付款协议名称")
    private String paymentAgreementName;

    @EntityField(name = "付款条件")
    private String paymentTerm;

    @EntityField(name = "付款条件名称")
    private String paymentTermName;

    @EntityField(name = "信用额度")
    private String creditAmount;

    @EntityField(name = "信用期限")
    private Integer periodDays;

    @EntityField(name = "合作起始时间")
    private String coopStartTime;

    @EntityField(name = "合作结束时间")
    private String coopEndTime;

    @EntityField(name = "业务归属")
    private String ownerCompany;

//    @EntityField(name = "管控类型id")
//    private Long controlId;
//
//    @EntityField(name = "管控类型名称")
//    private String controlTypeName;

    @EntityField(name = "oms供应商编号")
    private String omsSupplierNo;

    @EntityField(name = "供应商联系人")
    private List<CompanyLinkmanVO> linkmanList;

    @EntityField(name = "负责人信息")
    private List<SupplierOrderManVO> supplierManList;

    @EntityField(name = "联系地址")
    private List<CompanyShippingAddressVO> linkAddressList;

    @EntityField(name = "供应商证书信息")
    private List<CompanyCertVO> supplierCertList;


    // ----------------供应商档案分派信息----------------
    @EntityField(name = "供应商分配信息")
    private List<SupplierAssignVO> supplierBaseList;


    // ----------------使用组织的管控信息----------------
    @EntityField(name = "管控状态")
    private String controlStatus;

    @EntityField(name = "管控状态名称")
    private String controlStatusName;

    // ----------------使用组织的gsp信息----------------
    @EntityField(name = "Gsp首营状态 0:无需首营，1：未首营 2：已首营")
    private Integer gspStatus;

    @EntityField(name = "Gsp首营状态 0:无需首营，1：未首营 2：已首营")
    private String gspStatusName;





//    @EntityField(name = "客户编码")
//    private String customerCode;
//
//    @EntityField(name = "客户名称")
//    private String customerName;

}
