package com.yyigou.dsrp.cdc.api.v2.customer.dto;

import com.yyigou.ddc.common.service.annotation.EntityField;
import com.yyigou.ddc.services.ddc.task.vo.excel.UapBaseImportDTO;
import lombok.Data;

import java.io.Serializable;

@Data
public class CustomerLinkmanImportDTO extends UapBaseImportDTO implements Serializable {
    @EntityField(name = "客户编码")
    private String sourceNo;

    @EntityField(name = "联系人")
    private String linkman;

    @EntityField(name = "性别")
    private String sex;

    @EntityField(name = "职位")
    private String position;

    @EntityField(name = "电话")
    private String fixedPhone;

    @EntityField(name = "手机")
    private String mobilePhone;

    @EntityField(name = "QQ")
    private String qq;

    @EntityField(name = "微信")
    private String wx;

    @EntityField(name = "邮箱")
    private String email;

    @EntityField(name = "是否默认联系人")
    private Integer isDefault;

    @EntityField(name = "是否默认联系人")
    private String isDefaultName;

    @EntityField(name = "状态")
    private Integer status;

    @EntityField(name = "状态")
    private String statusName;
}
