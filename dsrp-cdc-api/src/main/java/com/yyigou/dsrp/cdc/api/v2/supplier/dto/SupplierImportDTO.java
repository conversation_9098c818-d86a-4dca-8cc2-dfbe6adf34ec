package com.yyigou.dsrp.cdc.api.v2.supplier.dto;

import com.yyigou.ddc.common.service.annotation.EntityField;
import com.yyigou.ddc.services.ddc.task.vo.excel.UapBaseImportDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class SupplierImportDTO extends UapBaseImportDTO implements Serializable {
    @EntityField(name = "管理组织编号")
    private String manageOrgNo;

    @EntityField(name = "管理组织名称")
    private String manageOrgNoName;

    @EntityField(name = "供应商编号")
    private String supplierCode;

    @EntityField(name = "供应商名称")
    private String supplierName;

    @EntityField(name = "供应商英文名称")
    private String supplierNameEn;

    @EntityField(name = "助记码")
    private String mnemonicCode;

    @EntityField(name = "供应商类型")
    private String transactionType;

    @EntityField(name = "供应商类型名称")
    private String transactionTypeName;

    @EntityField(name = "供应商分类")
    private String supplierCategoryNo;

    @EntityField(name = "供应商分类名称")
    private String supplierCategoryName;

    @EntityField(name = "企业编号")
    private String companyNo;

    @EntityField(name = "企业名称")
    private String companyName;

    @EntityField(name = "统一社会信用代码")
    private String unifiedSocialCode;

    @EntityField(name = "企业注册地域：1-境内，2-境外")
    private Integer factoryType;

    @EntityField(name = "企业注册地域：1-境内，2-境外")
    private String factoryTypeName;

    @EntityField(name = "国家/地区")
    private String countryRegionId;

    @EntityField(name = "国家/地区")
    private String countryRegionIdName;

    @EntityField(name = "纳税类别编码")
    private String taxCategory;

    @EntityField(name = "纳税类别名称")
    private String taxCategoryName;

    @EntityField(name = "经济类型编码")
    private String economicType;

    @EntityField(name = "经济类型名称")
    private String economicTypeName;

    @EntityField(name = "是否散户")
    private Integer retailInvestors;

    @EntityField(name = "是否散户")
    private String retailInvestorsName;

    @EntityField(name = "是否关联企业：1-是，0-否")
    private Integer isAssociatedEnterprise;

    @EntityField(name = "是否关联企业：1-是，0-否")
    private String isAssociatedEnterpriseName;

    @EntityField(name = "关联组织编号")
    private String associatedOrgNo;

    @EntityField(name = "关联组织编码")
    private String associatedOrgCode;

    @EntityField(name = "关联组织名称")
    private String associatedOrgNoName;

    @EntityField(name = "备注")
    private String remark;


    @EntityField(name = "合作性质")
    private String cooperationMode;

    @EntityField(name = "合作性质")
    private String cooperationModeName;

    @EntityField(name = "币种id")
    private String currencyId;

    @EntityField(name = "币种id")
    private String currencyIdName;

    @EntityField(name = "结算方式")
    private String settlementModes;

    @EntityField(name = "结算方式")
    private String settlementModesName;

    @EntityField(name = "付款协议id")
    private Long paymentAgreementId;

    @EntityField(name = "付款协议Code")
    private String paymentAgreementCode;

    @EntityField(name = "付款协议名称")
    private String paymentAgreementIdName;

    @EntityField(name = "付款条件")
    private String paymentTerm;

    @EntityField(name = "付款条件名称")
    private String paymentTermName;

    @EntityField(name = "信用额度")
    private String creditAmount;

    @EntityField(name = "信用期限")
    private Integer periodDays;

    @EntityField(name = "合作起始时间")
    private String coopStartTime;

    @EntityField(name = "合作结束时间")
    private String coopEndTime;

    @EntityField(name = "归属公司")
    private String ownerCompany;


    @EntityField(name = "联系人信息")
    private List<SupplierLinkmanImportDTO> bdcCompanyLinkmanDetailList;

    @EntityField(name = "地址信息")
    private List<SupplierAddressImportDTO> bdcCompanyShippingAddressDetailList;

    @EntityField(name = "银行信息")
    private List<SupplierBankImportDTO> bdcSupplierBankDetailList;

    @EntityField(name = "负责人信息")
    private List<SupplierOrderManImportDTO> bdcSupplierOrderManDetailList;

}
