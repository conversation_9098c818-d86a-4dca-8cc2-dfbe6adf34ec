package com.yyigou.dsrp.cdc.api.v2.supplier.dto;

import com.yyigou.ddc.common.service.annotation.EntityField;
import com.yyigou.ddc.services.ddc.task.vo.excel.UapBaseImportDTO;
import lombok.Data;

import java.io.Serializable;

@Data
public class SupplierOrderManImportDTO extends UapBaseImportDTO implements Serializable {
    @EntityField(name = "供应商编码")
    private String supplierCode;

    @EntityField(name = "部门")
    private String deptNo;

    @EntityField(name = "部门")
    private String deptNoName;

    @EntityField(name = "员工编号")
    private String orderManNo;

    @EntityField(name = "员工姓名")
    private String orderManName;

    @EntityField(name = "职务")
    private String post;

    @EntityField(name = "订单专员")
    private Integer orderSpecialist;

    @EntityField(name = "订单专员")
    private String orderSpecialistName;

    @EntityField(name = "采购员")
    private Integer isDefault;

    @EntityField(name = "采购员")
    private String isDefaultName;

}
