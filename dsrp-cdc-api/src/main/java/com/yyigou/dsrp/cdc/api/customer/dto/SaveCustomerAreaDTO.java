package com.yyigou.dsrp.cdc.api.customer.dto;

import com.yyigou.ddc.common.service.annotation.EntityField;
import com.yyigou.ddc.common.service.annotation.MethodType;
import lombok.Data;

import java.io.Serializable;

@Data
public class SaveCustomerAreaDTO implements Serializable {
    private String parentAreaNo;
    /**
     * 区域编号
     */
    @EntityField(name = "区域编号", requiredMetTypes = MethodType.ADD, stringValueTypeLength = 32)
    private String areaNo;
    /**
     * 区域编码
     */
    @EntityField(name = "区域编码", requiredMetTypes = MethodType.ADD, stringValueTypeLength = 32)
    private String areaName;
    /**
     * 区域编码
     */
    @EntityField(name = "区域编码", requiredMetTypes = MethodType.ADD, stringValueTypeLength = 255)
    private String areaCode;
    /**
     * 是否默认 1：是 0：否
     */
    @EntityField(name = "否默认 1：是 0：否", stringValueTypeLength = 255)
    private Integer isDefault;
}
