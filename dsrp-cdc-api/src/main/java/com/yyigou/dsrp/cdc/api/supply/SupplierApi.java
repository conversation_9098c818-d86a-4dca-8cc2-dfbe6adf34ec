package com.yyigou.dsrp.cdc.api.supply;

import com.yyigou.ddc.common.page.PageDto;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.ddc.common.service.ServiceBase;
import com.yyigou.dsrp.cdc.api.common.vo.OrganizationVO;
import com.yyigou.dsrp.cdc.api.company.vo.CompanyDetailVO;
import com.yyigou.dsrp.cdc.api.supply.dto.*;
import com.yyigou.dsrp.cdc.api.supply.vo.SupplierOperationPlatformPageVO;
import com.yyigou.dsrp.cdc.api.supply.vo.SupplierPageVO;
import com.yyigou.dsrp.cdc.api.supply.vo.SupplierSyncCountVO;
import com.yyigou.dsrp.cdc.api.supply.vo.SupplierSyncQueryVO;
import com.yyigou.dsrp.cdc.api.supply.vo.SupplierVO;

import java.util.List;

public interface SupplierApi extends ServiceBase {

    CallResult<Boolean> checkOnlyCode(String no, String code);

    CallResult<Boolean> checkOnlyName(String no, String name);


    /**
     * 保存供应商档案
     *
     * @param params
     * @return: {@link CallResult < Boolean>}
     */
    CallResult<String> saveSupplier(SaveSupplierDTO params);

    /**
     * 修改供应商档案信息
     *
     * @param params
     * @return: {@link CallResult< Boolean>}
     */
    CallResult<Boolean> updateSupplier(SaveSupplierDTO params);


    /**
     * 获取供应商详情
     *
     * @param supplierNo
     * @return: {@link CallResult<  CompanyDetailVO >}
     */
    CallResult<SupplierVO> getSupplier(String supplierNo);

    /**
     * 获取供应商详情 多组织获取详情
     *
     * @param supplierNo
     * @return: {@link CallResult<  CompanyDetailVO >}
     */
    CallResult<SupplierVO> getSupplierByOrg(String supplierNo);

    /**
     * 删除供应商档案
     *
     * @param supplierNoList
     * @return
     */
    CallResult<Boolean> deleteSupplier(List<String> supplierNoList);

    CallResult<PageVo<SupplierSyncQueryVO>> findListPageForSync(SupplierSyncQueryDTO params, PageDto pageParams);

    CallResult<SupplierSyncCountVO> findSupplierSyncCount();

    CallResult<PageVo<SupplierSyncQueryVO>> findListPageForPendingAdmission(SupplierSyncQueryDTO params, PageDto pageParams);

    /**
     * 分页获取供应商档案
     *
     * @param params
     * @return
     */
    CallResult<PageVo<SupplierPageVO>> queryPageSupplier(QueryPageSupplierDTO params, PageDto pageDto);

    /**
     * 查询待处理供应商数量
     * @return
     */
    CallResult<Long> findExamCount();

    /**
     * 分页获取供应商档案
     *
     * @param params
     * @return
     */
    CallResult<PageVo<SupplierPageVO>> queryPageSupplierByGroup(QueryPageSupplierDTO params, PageDto pageDto);


    /**
     * 分页获取供应商档案
     *
     * @param params
     * @return
     */
    CallResult<PageVo<SupplierPageVO>> queryPageSupplierByOrg(QueryPageSupplierByOrgDTO params, PageDto pageDto);


    /**
     * 分页获取指定组织供应商档案
     *
     * @param params
     * @return
     */
    CallResult<PageVo<SupplierPageVO>> queryPageSupplierBySpecifyOrg(QueryPageSupplierBySpecifyOrgDTO params, PageDto pageDto);


    /**
     * 分页获取供应商档案运营平台版本
     *
     * @param params
     * @return
     */
    CallResult<PageVo<SupplierOperationPlatformPageVO>> queryOperationPlatformPageSupplier(QueryPageOperationPlatformSupplierDTO params, PageDto pageDto);


    /**
     * 根据供应商编号查询供应商的所属组织
     * @param params
     * @return
     */
    CallResult<List<OrganizationVO>> findOrgInfoBySupplier(OrgInfoBySupplierDTO params);
}
