package com.yyigou.dsrp.cdc.api.company.dto;

import com.yyigou.ddc.common.service.annotation.EntityField;
import com.yyigou.ddc.common.service.annotation.MethodType;

public class CompanyDTO {

    /**
     * 主键码
     */
    @EntityField(name = "主键码", stringValueTypeLength = 32, requiredMetTypes = {MethodType.UPDATE})
    private String companyNo;
    /**
     * 公司/企业名称（外部）
     */
    @EntityField(name = "公司/企业名称（外部）", stringValueTypeLength = 128, requiredMetTypes = {MethodType.UPDATE})
    private String companyName;

    /**
     * 统一社会信用代码。公立医院的事业单位法人证号
     */
    @EntityField(name = "统一社会信用代码。公立医院的事业单位法人证号", stringValueTypeLength = 100, requiredMetTypes = {MethodType.UPDATE})
    private String unifiedSocialCode;

    /**
     * 企业注册地域:1:境内  2:境外
     */
    @EntityField(name = "企业注册地域:1:境内  2:境外")
    private Integer factoryType;


    @EntityField(name = "是否关联企业: 1是 0否")
    private Integer isAssociatedEnterprise;


    /**
     * 法定代表人
     */
    @EntityField(name = "法定代表人", stringValueTypeLength = 100, requiredMetTypes = {MethodType.UPDATE})
    private String legalPerson;

    @EntityField(name = "经营状态")
    private String businessStatus;

    /**
     * 成立日期
     */
    @EntityField(name = "成立日期", stringValueTypeLength = 19, requiredMetTypes = {MethodType.UPDATE})
    private String establishmentDate;
    /**
     * 是否上市
     */
    @EntityField(name = "是否上市 1：是 0 ：否")
    private Integer isListed;


    @EntityField(name = "营业期限开始时间")
    private String businessStartTime;
    @EntityField(name = "营业期限结束时间")
    private String businessEndTime;
    @EntityField(name = "是否长期 1长期 0无")
    private Integer businessLongTerm;


    /**
     * 注册资金
     */
    @EntityField(name = "注册资金", stringValueTypeLength = 100, requiredMetTypes = {MethodType.UPDATE})
    private String registedCapital;
    @EntityField(name = "实缴资本")
    private String paidCapital;

    @EntityField(name = "类型")
    private String companyBusinessType;

    @EntityField(name = "所属行业")
    private String industry;
    @EntityField(name = "工商注册号")
    private String businessRegistNo;
    @EntityField(name = "组织机构代码")
    private String organizationNo;
    @EntityField(name = "纳税人识别号")
    private String taxpayerNo;
    @EntityField(name = "纳税人资质")
    private String taxpayerQualification;
    @EntityField(name = "纳税种类")
    private String taxCategory;

    @EntityField(name = "核准日期")
    private String approvalDate;


    @EntityField(name = "登记机关")
    private String registrationAuthority;


    /**
     * 区域编码
     */
    @EntityField(name = "区域编码", stringValueTypeLength = 10, requiredMetTypes = {MethodType.UPDATE})
    private String regionCode;
    /**
     * 区域名称 （地址前缀）
     */
    @EntityField(name = "区域名称 （地址前缀）", stringValueTypeLength = 100, requiredMetTypes = {MethodType.UPDATE})
    private String regionName;
    /**
     * 企业地址(后缀)
     */
    @EntityField(name = "企业地址(后缀)", stringValueTypeLength = 300, requiredMetTypes = {MethodType.UPDATE})
    private String address;
    @EntityField(name = "曾用名")
    private String lastName;
    @EntityField(name = "参保人数")
    private String insuredNumber;
    /**
     * 网址
     */
    @EntityField(name = "网址", stringValueTypeLength = 200, requiredMetTypes = {MethodType.UPDATE})
    private String webSite;
    /**
     * email
     */
    @EntityField(name = "email", stringValueTypeLength = 200, requiredMetTypes = {MethodType.UPDATE})
    private String email;
    /**
     * 传真
     */
    @EntityField(name = "传真", stringValueTypeLength = 100, requiredMetTypes = {MethodType.UPDATE})
    private String fax;
    @EntityField(name = "经营范围")
    private String manageScope;
}

