package com.yyigou.dsrp.cdc.api.v2.company.dto;


import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;


/**
 * 统一社会信用代码唯一性校验
 */
@Data
public class CompanyUnifiedSocialCodeCheckDTO implements Serializable {
    @EntityField(name = "企业编号")
    private String companyNo;

    @EntityField(name = "统一社会信用代码")
    private String unifiedSocialCode;

    @EntityField(name = "企业所属地域")
    private Integer factoryType;
}