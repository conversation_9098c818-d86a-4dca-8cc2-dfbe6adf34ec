package com.yyigou.dsrp.cdc.api.v2.customer.vo;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;

@Data
public class CustomerCategoryUseOrgVO implements Serializable {
    @EntityField(name = "分派记录id")
    private Long id;

    @EntityField(name = "使用组织编号")
    private String useOrgNo;

    @EntityField(name = "使用组织编码")
    private String useOrgCode;

    @EntityField(name = "使用组织名称")
    private String useOrgName;
}
