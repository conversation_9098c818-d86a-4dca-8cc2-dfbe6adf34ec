package com.yyigou.dsrp.cdc.api.supply.dto;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.util.List;

@Data
public class QueryPageOperationPlatformSupplierDTO {
    @EntityField(name = "组织noList")
    private List<String> orgNoList;

    @EntityField(name = "供应商编码模糊查询")
    private String supplierCodeKeywords;
    @EntityField(name = "供应商名称模糊查询")
    private String supplierNameKeywords;


    @EntityField(name = "统一社会信用代码。公立医院的事业单位法人证号")
    private String unifiedSocialCode;
    @EntityField(name = "统一社会信用代码。公立医院的事业单位法人证号模糊查询")
    private String unifiedSocialCodeKeywords;
}
