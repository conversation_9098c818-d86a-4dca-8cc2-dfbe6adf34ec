package com.yyigou.dsrp.cdc.api.company.dto;

import com.yyigou.ddc.common.service.annotation.Entity;
import com.yyigou.ddc.common.service.annotation.EntityField;
import com.yyigou.ddc.common.service.annotation.MethodType;
import lombok.Data;

import java.io.Serializable;

/**
 * CompanyShippingAddress
 * <p>
 * 收货地址表
 */
@Data
@Entity(name = "类型地址")
public class CompanyShippingAddressDTO implements Serializable {
    /**
     * 主键
     */
    @EntityField(name = "主键", requiredMetTypes = MethodType.ADD, stringValueTypeLength = 20)
    private Long id;

    @EntityField(name = "联系人编码", requiredMetTypes = MethodType.ADD, stringValueTypeLength = 50)
    private String linkAddressCode;
    /**
     * 收货人
     */
    @EntityField(name = "收货人", requiredMetTypes = MethodType.ADD, stringValueTypeLength = 50)
    private String receiveUser;
    /**
     * 联系电话
     */
    @EntityField(name = "联系电话", requiredMetTypes = MethodType.ADD, stringValueTypeLength = 100)
    private String receivePhone;
    /**
     * 行政地区编码
     */
    @EntityField(name = "行政地区编码", requiredMetTypes = MethodType.ADD, stringValueTypeLength = 20)
    private String regionCode;
    /**
     * 行政地区名称
     */
    @EntityField(name = "行政地区名称", requiredMetTypes = MethodType.ADD, stringValueTypeLength = 100)
    private String regionName;
    /**
     * 收货详细地址
     */
    @EntityField(name = "收货详细地址", requiredMetTypes = MethodType.ADD, stringValueTypeLength = 300)
    private String receiveAddr;
    /**
     * 收货地址状态:1默认 0非默认
     */
    @EntityField(name = "收货地址状态:1默认 0非默认", requiredMetTypes = MethodType.ADD, stringValueTypeLength = 4)
    private Integer isDefault;
    /**
     * 地址类型：1联系地址 2：收票地址 3:收货地址
     */
    @EntityField(name = "地址类型：1联系地址 2：收票地址 3:仓库地址 4：收货地址", requiredMetTypes = MethodType.ADD, stringValueTypeLength = 1)
    private String addressType;
    private String addressDesc;
}