package com.yyigou.dsrp.cdc.api.customer.vo;

import com.yyigou.ddc.common.service.annotation.EntityField;
import com.yyigou.ddc.common.treegrid.TreeGrid;

public class CustomerCategoryTree extends TreeGrid {
    @EntityField(
            name = "分类编码"
    )
    private String categoryCode;
    @EntityField(
            name = "分类名称"
    )
    private String categoryName;
    @EntityField(
            name = "层级"
    )
    private Integer level;
    @EntityField(
            name = "状态"
    )
    private String status;
}
