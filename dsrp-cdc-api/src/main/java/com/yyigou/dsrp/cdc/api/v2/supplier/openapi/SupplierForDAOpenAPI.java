package com.yyigou.dsrp.cdc.api.v2.supplier.openapi;

import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.ddc.common.service.ServiceBase;
import com.yyigou.dsrp.cdc.api.supply.dto.SupplierExternalSaveDTO;
import com.yyigou.dsrp.cdc.api.supply.vo.SupplierExternalSaveVO;

import java.util.List;

/**
 * @Classname SupplierForDAOpenAPI
 * @description: 迪安供应商相关OPEN-API接口
 * @author: baoww
 * @date: 2025/3/18 21:56
 */
public interface SupplierForDAOpenAPI extends ServiceBase {
    /**
     * 迪安供应商批量保存接口
     *
     * @param params
     * @return
     */
    CallResult<List<SupplierExternalSaveVO>> batchSave(List<SupplierExternalSaveDTO> params);

}
