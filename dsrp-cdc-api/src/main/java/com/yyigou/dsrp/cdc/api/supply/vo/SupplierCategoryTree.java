package com.yyigou.dsrp.cdc.api.supply.vo;

import com.yyigou.ddc.common.service.annotation.EntityField;
import com.yyigou.ddc.common.treegrid.TreeGrid;
import lombok.Data;

@Data
public class SupplierCategoryTree extends TreeGrid {
    /**
     * 分类编码
     */
    @EntityField(name = "分类编码")
    private String categoryCode;

    /**
     * 分类名称
     */
    @EntityField(name = "分类名称")
    private String categoryName;

    /**
     * 层级
     */
    @EntityField(name = "层级")
    private Integer level;

    @EntityField(name = "状态")
    private String status;
}
