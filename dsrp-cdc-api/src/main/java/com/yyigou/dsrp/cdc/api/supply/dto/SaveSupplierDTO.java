package com.yyigou.dsrp.cdc.api.supply.dto;

import com.yyigou.ddc.common.service.annotation.EntityField;
import com.yyigou.ddc.common.service.annotation.MethodType;
import com.yyigou.dsrp.cdc.api.company.dto.BankDTO;
import com.yyigou.dsrp.cdc.api.company.dto.CompanyLinkmanDTO;
import com.yyigou.dsrp.cdc.api.company.dto.CompanyShippingAddressDTO;
import com.yyigou.dsrp.cdc.api.customer.dto.CustomerInvoiceDTO;
import com.yyigou.dsrp.cdc.api.customer.dto.SaveCustomerAreaDTO;
import com.yyigou.dsrp.cert.client.certbase.req.CatalogInfoBigClientReq;
import com.yyigou.dsrp.cert.client.certbase.req.CatalogInfoClientReq;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class SaveSupplierDTO implements Serializable {
    @EntityField(name = "申请id", stringValueTypeLength = 50, requiredMetTypes = {MethodType.UPDATE})
    private String id;
    /**
     * 供应商编码
     */
    @EntityField(name = "供应商NO", stringValueTypeLength = 50, requiredMetTypes = {MethodType.UPDATE})
    private String supplierNo;
    /**
     * 供应商编码
     */
    @EntityField(name = "供应商编码", stringValueTypeLength = 50, requiredMetTypes = {MethodType.UPDATE})
    private String supplierCode;
    /**
     * 供应商名称
     */
    @EntityField(name = "供应商名称", stringValueTypeLength = 128, requiredMetTypes = {MethodType.UPDATE})
    private String supplierName;

    /**
     * 供应商英文名称
     */
    @EntityField(name = "供应商英文名称", stringValueTypeLength = 300, requiredMetTypes = {MethodType.UPDATE})
    private String supplierNameEn;

    /**
     * 助记码
     */
    @EntityField(name = "助记码", stringValueTypeLength = 100, requiredMetTypes = {MethodType.UPDATE})
    private String mnemonicCode;


    @EntityField(name = "供应商交易类型", stringValueTypeLength = 1, requiredMetTypes = {MethodType.UPDATE})
    private String transactionType;

    /**
     * 供应商分类
     */
    @EntityField(name = "供应商分类", stringValueTypeLength = 32, requiredMetTypes = {MethodType.UPDATE})
    private String supplierCategoryNo;


    @EntityField(name = "企业名称")
    private String companyName;

    @EntityField(name = "统一社会信用代码。公立医院的事业单位法人证号")
    private String unifiedSocialCode;

    @EntityField(name = "企业注册地域：1-境内，2-境外")
    private Integer factoryType;

    @EntityField(name = "国家/地区")
    private String country;
    /**
     * Gsp首营状态
     */
    @EntityField(name = "Gsp首营状态 0:无需首营，1：未首营 2：已首营")
    private Integer gspStatus;
    /**
     * 公司纳税类别
     */
    @EntityField(name = "纳税类别")
    private String taxCategory;


    @EntityField(name = "经济类型")
    private String economicType;

    @EntityField(name = "归属公司")
    private String ownerCompany;

    @EntityField(name = "散户")
    private Integer retailInvestors;

    @EntityField(name = "是否关联企业：1-是，0-否")
    private Integer isAssociatedEnterprise;

    @EntityField(name = "关联组织编号,关联企业时选择")
    private String associatedOrgNo;

    @EntityField(name = "关联组织编码,关联企业时选择")
    private String associatedOrgCode;

    @EntityField(name = "关联组织名称,关联企业时选择")
    private String associatedOrgName;

    /**
     * 备注
     */
    @EntityField(name = "备注", stringValueTypeLength = 500, requiredMetTypes = {MethodType.UPDATE})
    private String remark;


    /**
     * 业务信息
     */

    @EntityField(name = "管控类型id")
    private Long controlId;
    @EntityField(name = "管控类型名称")
    private String controlTypeName;

    /**
     * 合作性质
     */
    @EntityField(name = "合作性质", stringValueTypeLength = 1, requiredMetTypes = {MethodType.UPDATE})
    private String cooperationMode;

    /**
     * 合作性质
     */
    @EntityField(name = "合作性质名称", stringValueTypeLength = 1, requiredMetTypes = {MethodType.UPDATE})
    private String cooperationModeName;

    @EntityField(name = "价格体系", stringValueTypeLength = 19)
    private String priceCategoryCode;

    @EntityField(name = "结算方式")
    private String settlementModes;

    @EntityField(name = "结算方式名称")
    private String settlementModesName;

    @EntityField(name = "信用额度")
    private String creditAmount;

    @EntityField(name = "信用天数")
    private Integer periodDays;

    @EntityField(name = "合作起始时间")
    private String coopStartTime;

    @EntityField(name = "合作结束时间")
    private String coopEndTime;

    @EntityField(name = "交易币种")
    private String currency;


    /**
     * 付款条件
     */
    @EntityField(name = "付款条件")
    private String paymentTerm;
    /**
     * 付款协议id(本地的id)
     */
    private Long paymentAgreementId;

    /**
     * 付款协议id(ys的id)
     */
    private String paymentAgreementYsId;

    /**
     * 付款协议编码
     */
    private String paymentAgreementCode;

    /**
     * 付款协议名称
     */
    private String paymentAgreementName;


    /**
     * 业务状态：0:潜在 1:合格 2：草稿
     */
    private Integer businessFlag;

    @EntityField(name = "银行信息", stringValueTypeLength = 0, requiredMetTypes = {MethodType.UPDATE})
    private List<BankDTO> bankList;
    //业务区域
    @EntityField(name = "区域集合")
    private List<SaveCustomerAreaDTO> areaList;
    //供应商联系人
    @EntityField(name = "供应商联系人")
    private List<CompanyLinkmanDTO> linkmanList;
    //开票信息
    @EntityField(name = "开票信息", stringValueTypeLength = 0, requiredMetTypes = {MethodType.UPDATE})
    private List<CustomerInvoiceDTO> invoiceList;
    //负责人信息
    @EntityField(name = "负责人信息")
    private List<SupplierSalesManDTO> supplierManList;
    //资质证照
//    @EntityField(name = "证照文件")
//    private List<CompanyCertDTO> companyCertList;
    //联系人地址
    @EntityField(name = "联系地址")
    private List<CompanyShippingAddressDTO> linkAddressList;


    @EntityField(name = "企业资料信息")
    private CatalogInfoBigClientReq catalogInfoBigClientReq;
    private List<CatalogInfoClientReq> catalogInfoClientReqList;
}
