package com.yyigou.dsrp.cdc.api.v2.supplier.dto;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;

@Data
public class SupplierPageQueryBySpecifyOrgDTO implements Serializable {
    @EntityField(name = "租户编号")
    private String enterpriseNo;

    @EntityField(name = "组织编号")
    private String orgNo;

    @EntityField(name = "启停状态")
    private String controlStatus;
}
