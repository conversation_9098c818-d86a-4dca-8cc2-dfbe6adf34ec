package com.yyigou.dsrp.cdc.api.v2.customer;

import com.yyigou.ddc.common.page.PageDto;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.ddc.common.service.ServiceBase;
import com.yyigou.dsrp.cdc.api.v2.customer.dto.*;
import com.yyigou.dsrp.cdc.api.v2.customer.vo.CustomerApplyDetailVO;
import com.yyigou.dsrp.cdc.api.v2.customer.vo.CustomerApplyPageVO;

public interface CustomerApplyV2API extends ServiceBase {
    /**
     * 申请组织视角：分页查询客户申请列表
     *
     * @param params     查询条件
     * @param pageParams 分页条件
     * @return
     */
    CallResult<PageVo<CustomerApplyPageVO>> applyFindListPage(CustomerApplyQueryListPageDTO params, PageDto pageParams);

    /**
     * 管理组织视角：分页查询客户待审核列表
     *
     * @param params     查询条件
     * @param pageParams 分页条件
     * @return
     */
    CallResult<PageVo<CustomerApplyPageVO>> approveFindListPage(CustomerApplyApproveQueryListPageDTO params, PageDto pageParams);

    /**
     * 有权限待审核数量
     *
     * @return
     */
    CallResult<Long> getPendingCount();


    /**
     * 申请单详情
     *
     * @param params
     * @return
     */
    CallResult<CustomerApplyDetailVO> getDetail(CustomerApplyGetDTO params);


    /**
     * 暂存、提交、编辑
     *
     * @param params
     * @return
     */
    CallResult<String> applySaveOrUpdate(CustomerApplySaveOrUpdateDTO params);


    /**
     * 审核
     *
     * @param params
     * @return
     */
    CallResult<Boolean> manageApprove(CustomerApplyApproveDTO params);


    /**
     * 撤回申请
     *
     * @param params
     * @return
     */
    CallResult<Boolean> withDrawApply(CustomerApplyWithdrawDTO params);

    /**
     * 删除申请
     *
     * @param params
     * @return
     */
    CallResult<Boolean> deleteApply(CustomerApplyDeleteDTO params);
}
