package com.yyigou.dsrp.cdc.api.v2.customer.dto;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class CustomerPageQueryBySpecifyOrgDTO implements Serializable {
    private String keywords;

    @EntityField(name = "客户档案关键字")
    private String customerKeywords;

    @EntityField(name = "")
    private String customerExactKeywords;

    @EntityField(name = "")
    private String customerCode;

    @EntityField(name = "")
    private String customerCategoryNo;

    @EntityField(name = "")
    private String orgNo;

    @EntityField(name = "")
    private String enterpriseNo;

    @EntityField(name = "")
    private Integer controlStatus;

    @EntityField(name = "")
    private List<Integer> controlStatusList;

    @EntityField(name = "")
    private List<String> noCustomerNos;

    @EntityField(name = "")
    private Boolean isEnterpriseGroup;

}
