package com.yyigou.dsrp.cdc.api.company.dto;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;

/**
 * 医疗器械监管分类
 *
 * @author:  Moore
 * @date: 2024/7/4 15:52
 * @version: 1.0.0
 */
@Data
public class CompanyBusinessScopeSaveDTO implements Serializable {

    private static final long serialVersionUID = 1710715925820701133L;
    @EntityField(name = "主键")
    private Long id;

    @EntityField(name = "企业编号")
    private String companyNo;

    @EntityField(name = "企业证件主键")
    private Long companyCertId;

    @EntityField(name = "经营编号")
    private Long categoryId;

    @EntityField(name = "经营分类编码")
    private String categoryCode;

    @EntityField(name = "经验分类名称")
    private String categoryName;

    @EntityField(name = "经营分类类型")
    private String categoryType;

    @EntityField(name = "父节点ID")
    private Long parentCategoryId;

    @EntityField(name = "是否为叶子：1：是 0：非")
    private Integer isLeaf;

}
