package com.yyigou.dsrp.cdc.api.customer;

import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.ddc.common.service.ServiceBase;
import com.yyigou.dsrp.cdc.api.company.vo.CompanyLinkmanVO;
import com.yyigou.dsrp.cdc.api.customer.dto.CustomerLinkmanByOrgDTO;

import java.util.List;

/**
 * 客户联系人接口
 */
public interface CustomerLinkManApi extends ServiceBase {


    /**
     * 跨组织获取客户联系人列表
     *
     * @param customerCode
     * @param orgNo
     * @return
     */
    CallResult<List<CompanyLinkmanVO>> getCustomerLinkmanListByOrg(String customerCode, String orgNo);

    /**
     * 跨组织保存客户联系人列表
     *
     * @param params
     * @return
     */
    CallResult<CompanyLinkmanVO> saveCustomerLinkmanByOrg(CustomerLinkmanByOrgDTO params);

    /**
     * 跨组织删除联系人
     *
     * @param params
     * @param
     * @return
     */
    CallResult<CompanyLinkmanVO> editCustomerLinkmanByOrg(CustomerLinkmanByOrgDTO params);

    /**
     * 跨组织删除联系人
     *
     * @param linkmanId
     * @param
     * @return
     */
    CallResult<Boolean> deleteCustomerLinkmanByOrg(Long linkmanId);
}
