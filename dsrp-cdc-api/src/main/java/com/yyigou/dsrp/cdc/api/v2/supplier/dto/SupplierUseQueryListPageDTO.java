package com.yyigou.dsrp.cdc.api.v2.supplier.dto;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 供应商档案分页查询参数
 */
@Data
public class SupplierUseQueryListPageDTO implements Serializable {
    private static final long serialVersionUID = 1999169811512860482L;

    @EntityField(name = "使用组织编号")
    private List<String> useOrgNoList;

    @EntityField(name = "控制状态")
    private List<String> controlStatusList;

    @EntityField(name = "供应商名称模糊搜索")
    private String supplierNameKeywords;

    @EntityField(name = "供应商编码模糊搜索")
    private String supplierCodeKeywords;

    @EntityField(name = "供应商分类")
    private List<String> supplierCategoryNoList;

    @EntityField(name = "统一社会信用代码模糊搜索")
    private String unifiedSocialCodeKeywords;

    @EntityField(name = "管理组织编号")
    private List<String> manageOrgNoList;

//    @EntityField(name = "首营状态")
//    private List<Integer> xxxStatus;

    @EntityField(name = "隐含条件")
    private Integer businessFlag;

    @EntityField(name = "wms同步状态")
    private List<Integer> wmsSyncStatus;

    @EntityField(name = "erp同步状态")
    private List<Integer> erpSyncStatus;

    @EntityField(name = "scs同步状态")
    private List<Integer> scsSyncStatus;

    @EntityField(name = "勾选导出选的no")
    private List<String> idList;
}
