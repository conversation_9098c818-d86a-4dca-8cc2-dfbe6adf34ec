package com.yyigou.dsrp.cdc.api.v2.customer.dto;

import com.yyigou.ddc.common.service.annotation.EntityField;
import com.yyigou.ddc.services.ddc.task.vo.excel.UapBaseImportDTO;
import lombok.Data;

import java.io.Serializable;

@Data
public class CustomerAddressImportDTO extends UapBaseImportDTO implements Serializable {
    @EntityField(name = "客户编码")
    private String sourceNo;

    @EntityField(name = "联系人")
    private String receiveUser;

    @EntityField(name = "联系电话")
    private String receivePhone;

    @EntityField(name = "地址类型")
    private String addressType;

    @EntityField(name = "地址类型")
    private String addressTypeName;

    @EntityField(name = "行政区划")
    private String regionCode;

    @EntityField(name = "行政区划")
    private String regionCodeName;

    @EntityField(name = "详细地址")
    private String receiveAddr;

    @EntityField(name = "地址描述")
    private String addressDesc;

    @EntityField(name = "是否默认地址")
    private Integer isDefault;

    @EntityField(name = "是否默认地址")
    private String isDefaultName;

}
