package com.yyigou.dsrp.cdc.api.v2.company.dto;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;

/**
 * 企业档案分页查询参数
 */
@Data
public class CompanyQueryListPageForGoodsDTO implements Serializable {
    private static final long serialVersionUID = 1999169811512860482L;

    @EntityField(name = "名称/编码/信用代码模糊搜索")
    private String unionKeyWord;


}
