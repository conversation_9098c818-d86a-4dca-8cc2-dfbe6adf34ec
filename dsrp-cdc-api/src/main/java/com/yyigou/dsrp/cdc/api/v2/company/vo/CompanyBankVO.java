package com.yyigou.dsrp.cdc.api.v2.company.vo;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;

@Data
public class CompanyBankVO implements Serializable {
    @EntityField(name = "id")
    private Long id;

    private String bankCode;

    private String bankType;

    private String openBank;

    private String accountNo;

    private String accountName;

    private Integer accountType;

    private String linkPerson;

    private String linkPhone;

    private Integer status;

    private Integer isDefault;

    private String linkaddType;

    private String sourceNo;

    private String manageOrgNo;

    private String currencyId;


    // -----------名字信息-----------
    @EntityField(name = "银行类型")
    private String bankTypeName;

    @EntityField(name = "币种")
    private String currencyName;

    @EntityField(name = "账号类型")
    private String accountTypeName;

    @EntityField(name = "状态")
    private String statusName;

    @EntityField(name = "是否默认")
    private String isDefaultName;
}
