package com.yyigou.dsrp.cdc.api.v2.company.vo;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class CompanyCertVO implements Serializable {
    private Long id;

    @EntityField(name = "所属企业编号(租户)", stringValueTypeLength = 50)
    private String enterpriseNo;

    @EntityField(name = "所属企业名称(租户)", stringValueTypeLength = 128)
    private String enterpriseName;

    @EntityField(name = "组织编号", stringValueTypeLength = 50)
    private String orgNo;

    @EntityField(name = "组织名称", stringValueTypeLength = 50)
    private String orgName;

    @EntityField(name = "资料编号(系统自动生成)", stringValueTypeLength = 100)
    private String certNo;

    @EntityField(name = "资料类型：yyzz：营业执照，ylqxscqyxkz 医疗器械生产企业,ylqxscbaz 医疗器械生产备案证，ylqxjyqyxkz医疗器械经营企业许可证,ylqxjybaz医疗器械经营备案证", stringValueTypeLength = 255)
    private String certTypeCode;

    @EntityField(name = "资料类型名称")
    private String certTypeName;

    @EntityField(name = "证件编号（手动输入）", stringValueTypeLength = 100)
    private String certCode;

    @EntityField(name = "证书名称", stringValueTypeLength = 128)
    private String certName;

    @EntityField(name = "", stringValueTypeLength = 19)
    private String startTime;

    @EntityField(name = "", stringValueTypeLength = 19)
    private String endTime;

    @EntityField(name = "是否长期 1长期 0无")
    private Integer longTerm;

    @EntityField(name = "发证机关", stringValueTypeLength = 200)
    private String issuingAuthority;

    @EntityField(name = "备注", stringValueTypeLength = 300)
    private String remark;

    @EntityField(name = "资料来源档案类型：1-企业档案，2-供应商档案，3-客商档案")
    private Integer sourceDocType;

    @EntityField(name = "资料来源档案编码")
    private String sourceDocCode;

    @EntityField(name = "状态：1正常 0无效 ")
    private Integer status;

    @EntityField(name = "1:删除 0：未删除 -1:回收站")
    private Integer deleted;

    // -----------------资料信息-----------------
    @EntityField(name = "是否经营范围管控, 1是/0否, 默认否")
    private Integer businessScopeControl;

    @EntityField(name = "是否企业证照, 1是/0否, 默认否")
    private Integer enterpriseCert;

    @EntityField(name = "是否效期管控, 1是/0否, 默认否")
    private Integer termControl;

    @EntityField(name = "资料性质", stringValueTypeLength = 50)
    private String certNature;

    @EntityField(name = "是否过期 1过期 0未过期")
    private Integer expired;


    // -----------------附件和经营范围-----------------
    @EntityField(name = "企业资料附件列表")
    private List<CompanyCertFileVO> fileList;

    @EntityField(name = "企业资料经营范围列表")
    private List<CompanyCertBusinessScopeVO> businessScopeList;
}
