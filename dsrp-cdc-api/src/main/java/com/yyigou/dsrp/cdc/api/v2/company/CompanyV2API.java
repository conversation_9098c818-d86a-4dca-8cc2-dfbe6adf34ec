package com.yyigou.dsrp.cdc.api.v2.company;

import com.yyigou.ddc.common.page.PageDto;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.ddc.common.service.ServiceBase;
import com.yyigou.dsrp.cdc.api.v2.company.dto.*;
import com.yyigou.dsrp.cdc.api.v2.company.vo.CompanyBankVO;
import com.yyigou.dsrp.cdc.api.v2.company.vo.CompanyBasicVO;
import com.yyigou.dsrp.cdc.api.v2.company.vo.CompanyDetailVO;

import java.util.List;

public interface CompanyV2API extends ServiceBase {

    /**
     * 分页查询企业档案列表
     *
     * @param params     查询条件
     * @param pageParams 分页条件
     * @return
     */
    CallResult<PageVo<CompanyDetailVO>> findCompanyListPage(CompanyQueryListPageDTO params, PageDto pageParams);

    /**
     * 分页查询企业档案列表（添加产品）
     *
     * @param params     查询条件
     * @param pageParams 分页条件
     * @return
     */
    CallResult<PageVo<CompanyDetailVO>> findCompanyListPageForGoods(CompanyQueryListPageForGoodsDTO params, PageDto pageParams);

    /**
     * 新增企业档案，校验企业名称的唯一性
     *
     * @param params
     * @return
     */
    CallResult<Boolean> checkUniqueName(CompanyNameCheckDTO params);

    /**
     * 新增企业档案，校验统一社会信用代码的唯一性
     *
     * @param params
     * @return
     */
    CallResult<Boolean> checkUniqueUnifiedSocialCode(CompanyUnifiedSocialCodeCheckDTO params);

    /**
     * 企业档案新增保存
     *
     * @param params 待保存企业信息
     * @return
     */
    CallResult<CompanyBasicVO> saveCompany(CompanySaveDTO params);

    /**
     * 企业明细查询
     *
     * @param params 企业编号
     * @return
     */
    CallResult<CompanyDetailVO> getCompanyDetail(CompanyDetailGetDTO params);

    /**
     * 企业档案编辑保存
     *
     * @param params 待保存企业信息
     * @return
     */
    CallResult<Boolean> updateCompany(CompanyUpdateDTO params);

    /**
     * 引用企业档案进行分页查询
     *
     * @param params     查询条件
     * @param pageParams 分页条件
     * @return
     */
    CallResult<PageVo<CompanyDetailVO>> findCompanyListPageForQuote(CompanyQuoteQueryDTO params, PageDto pageParams);

    /**
     * 根据企业名称查询企业详情
     *
     * @param params 企业名称
     * @return
     */
    CallResult<CompanyDetailVO> getCompanyByName(CompanyQueryDTO params);

    /**
     * 根据企业名称模糊搜索
     *
     * @param params     查询条件
     * @param pageParams 分页条件
     * @return
     */
    CallResult<PageVo<CompanyDetailVO>> queryCompanyByName(CompanyQueryDTO params, PageDto pageParams);

    /**
     * 查询银行列表
     *
     * @param params
     * @return
     */
    CallResult<List<CompanyBankVO>> findBankList(CompanyBankQueryDTO params);

    CallResult<CompanyDetailVO> getTenantCompany(TenantCompanyDetailGetDTO params);

    CallResult<CompanyBasicVO> saveTenantCompany(CompanyUpdateDTO params);
}
